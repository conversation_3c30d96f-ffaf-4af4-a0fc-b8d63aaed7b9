<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-205" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-556 -1229 2051 1211">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape15">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="50" x2="47" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <rect height="24" stroke-width="0.398039" width="12" x="41" y="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <polyline arcFlag="1" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape194">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="10" y1="8" y2="8"/>
    <polyline DF8003:Layer="PUBLIC" points="22,1 22,16 10,8 22,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="36" x2="22" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape85_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="14" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="14" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="30" y2="30"/>
   </symbol>
   <symbol id="transformer2:shape85_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ea9490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3161d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_353d010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_353dde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_354f640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_352d830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3529280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3529b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2e825e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2e825e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3145350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3145350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3572570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3572570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_357f890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31c0240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31c0db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31bc5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31bcd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_319eac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_319dba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3191c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3192400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b5920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b62a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_35366b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3537070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_351c310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_351cd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3183c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3144060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_352a4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_352ae10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31bd6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31bec00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1221" width="2061" x="-561" y="-1234"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.443073" x1="1385" x2="1385" y1="-264" y2="-246"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.58427" x1="1359" x2="1385" y1="-246" y2="-246"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.573662" x1="1356" x2="1407" y1="-280" y2="-280"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-135735">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 374.941176 -726.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24569" ObjectName="SW-MD_YD.MD_YD_3011SW"/>
     <cge:Meas_Ref ObjectId="135735"/>
    <cge:TPSR_Ref TObjectID="24569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135739">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 374.941176 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24572" ObjectName="SW-MD_YD.MD_YD_0011SW"/>
     <cge:Meas_Ref ObjectId="135739"/>
    <cge:TPSR_Ref TObjectID="24572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135722">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.695652 374.988466 -1009.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24562" ObjectName="SW-MD_YD.MD_YD_3816SW"/>
     <cge:Meas_Ref ObjectId="135722"/>
    <cge:TPSR_Ref TObjectID="24562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135723">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 374.988466 -847.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24563" ObjectName="SW-MD_YD.MD_YD_3811SW"/>
     <cge:Meas_Ref ObjectId="135723"/>
    <cge:TPSR_Ref TObjectID="24563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135733">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 308.988466 -1084.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24567" ObjectName="SW-MD_YD.MD_YD_3819SW"/>
     <cge:Meas_Ref ObjectId="135733"/>
    <cge:TPSR_Ref TObjectID="24567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135861">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.000000 -496.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24574" ObjectName="SW-MD_YD.MD_YD_0902SW"/>
     <cge:Meas_Ref ObjectId="135861"/>
    <cge:TPSR_Ref TObjectID="24574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135860">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 155.000000 -481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24573" ObjectName="SW-MD_YD.MD_YD_0901SW"/>
     <cge:Meas_Ref ObjectId="135860"/>
    <cge:TPSR_Ref TObjectID="24573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136012">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 672.000000 -475.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24596" ObjectName="SW-MD_YD.MD_YD_0122SW"/>
     <cge:Meas_Ref ObjectId="136012"/>
    <cge:TPSR_Ref TObjectID="24596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136011">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 468.000000 -472.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24595" ObjectName="SW-MD_YD.MD_YD_0121SW"/>
     <cge:Meas_Ref ObjectId="136011"/>
    <cge:TPSR_Ref TObjectID="24595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135725">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 313.988466 -900.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24565" ObjectName="SW-MD_YD.MD_YD_38117SW"/>
     <cge:Meas_Ref ObjectId="135725"/>
    <cge:TPSR_Ref TObjectID="24565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 349.988466 -1064.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24564" ObjectName="SW-MD_YD.MD_YD_38167SW"/>
     <cge:Meas_Ref ObjectId="135724"/>
    <cge:TPSR_Ref TObjectID="24564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135726">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 312.988466 -986.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24566" ObjectName="SW-MD_YD.MD_YD_38160SW"/>
     <cge:Meas_Ref ObjectId="135726"/>
    <cge:TPSR_Ref TObjectID="24566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135734">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 286.000000 -1136.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24568" ObjectName="SW-MD_YD.MD_YD_38197SW"/>
     <cge:Meas_Ref ObjectId="135734"/>
    <cge:TPSR_Ref TObjectID="24568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135892">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 211.400000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24579" ObjectName="SW-MD_YD.MD_YD_0831SW"/>
     <cge:Meas_Ref ObjectId="135892"/>
    <cge:TPSR_Ref TObjectID="24579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135893">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 211.400000 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24580" ObjectName="SW-MD_YD.MD_YD_0836SW"/>
     <cge:Meas_Ref ObjectId="135893"/>
    <cge:TPSR_Ref TObjectID="24580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135868">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -10.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24576" ObjectName="SW-MD_YD.MD_YD_0821SW"/>
     <cge:Meas_Ref ObjectId="135868"/>
    <cge:TPSR_Ref TObjectID="24576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135869">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -10.000000 -255.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24577" ObjectName="SW-MD_YD.MD_YD_0826SW"/>
     <cge:Meas_Ref ObjectId="135869"/>
    <cge:TPSR_Ref TObjectID="24577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135917">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 448.800000 -255.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24583" ObjectName="SW-MD_YD.MD_YD_0846SW"/>
     <cge:Meas_Ref ObjectId="135917"/>
    <cge:TPSR_Ref TObjectID="24583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 448.800000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24582" ObjectName="SW-MD_YD.MD_YD_0841SW"/>
     <cge:Meas_Ref ObjectId="135916"/>
    <cge:TPSR_Ref TObjectID="24582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135736">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 341.941176 -711.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24570" ObjectName="SW-MD_YD.MD_YD_30117SW"/>
     <cge:Meas_Ref ObjectId="135736"/>
    <cge:TPSR_Ref TObjectID="24570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188718">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1015.988466 -1010.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28764" ObjectName="SW-MD_YD.MD_YD_3826SW"/>
     <cge:Meas_Ref ObjectId="188718"/>
    <cge:TPSR_Ref TObjectID="28764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1016.988466 -795.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28765" ObjectName="SW-MD_YD.MD_YD_3822SW"/>
     <cge:Meas_Ref ObjectId="188719"/>
    <cge:TPSR_Ref TObjectID="28765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188721">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 953.988466 -899.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28767" ObjectName="SW-MD_YD.MD_YD_38227SW"/>
     <cge:Meas_Ref ObjectId="188721"/>
    <cge:TPSR_Ref TObjectID="28767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 989.988466 -1063.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28766" ObjectName="SW-MD_YD.MD_YD_38267SW"/>
     <cge:Meas_Ref ObjectId="188720"/>
    <cge:TPSR_Ref TObjectID="28766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188722">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 952.988466 -994.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28768" ObjectName="SW-MD_YD.MD_YD_38260SW"/>
     <cge:Meas_Ref ObjectId="188722"/>
    <cge:TPSR_Ref TObjectID="28768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188729">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1015.941176 -496.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28770" ObjectName="SW-MD_YD.MD_YD_0022SW"/>
     <cge:Meas_Ref ObjectId="188729"/>
    <cge:TPSR_Ref TObjectID="28770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188844">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1011.800000 -399.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28772" ObjectName="SW-MD_YD.MD_YD_0932SW"/>
     <cge:Meas_Ref ObjectId="188844"/>
    <cge:TPSR_Ref TObjectID="28772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188845">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1011.800000 -260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28773" ObjectName="SW-MD_YD.MD_YD_0936SW"/>
     <cge:Meas_Ref ObjectId="188845"/>
    <cge:TPSR_Ref TObjectID="28773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135964">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.600000 -397.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24588" ObjectName="SW-MD_YD.MD_YD_0922SW"/>
     <cge:Meas_Ref ObjectId="135964"/>
    <cge:TPSR_Ref TObjectID="24588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135965">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.600000 -260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24589" ObjectName="SW-MD_YD.MD_YD_0926SW"/>
     <cge:Meas_Ref ObjectId="135965"/>
    <cge:TPSR_Ref TObjectID="24589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135940">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 713.600000 -397.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24585" ObjectName="SW-MD_YD.MD_YD_0912SW"/>
     <cge:Meas_Ref ObjectId="135940"/>
    <cge:TPSR_Ref TObjectID="24585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135941">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 713.600000 -260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24586" ObjectName="SW-MD_YD.MD_YD_0916SW"/>
     <cge:Meas_Ref ObjectId="135941"/>
    <cge:TPSR_Ref TObjectID="24586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188863">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1183.800000 -397.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28775" ObjectName="SW-MD_YD.MD_YD_0952SW"/>
     <cge:Meas_Ref ObjectId="188863"/>
    <cge:TPSR_Ref TObjectID="28775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188864">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1183.800000 -260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28776" ObjectName="SW-MD_YD.MD_YD_0956SW"/>
     <cge:Meas_Ref ObjectId="188864"/>
    <cge:TPSR_Ref TObjectID="28776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135988">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1350.000000 -397.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24591" ObjectName="SW-MD_YD.MD_YD_0942SW"/>
     <cge:Meas_Ref ObjectId="135988"/>
    <cge:TPSR_Ref TObjectID="24591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135989">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1350.000000 -260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24592" ObjectName="SW-MD_YD.MD_YD_0946SW"/>
     <cge:Meas_Ref ObjectId="135989"/>
    <cge:TPSR_Ref TObjectID="24592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135990">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1401.000000 -260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24593" ObjectName="SW-MD_YD.MD_YD_09467SW"/>
     <cge:Meas_Ref ObjectId="135990"/>
    <cge:TPSR_Ref TObjectID="24593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 -393.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 -256.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1376.000000 -260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 356.941176 -817.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 294.988466 -819.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-MD_YD.MD_YD_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-240,-465 491,-465 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24558" ObjectName="BS-MD_YD.MD_YD_9IM"/>
    <cge:TPSR_Ref TObjectID="24558"/></metadata>
   <polyline fill="none" opacity="0" points="-240,-465 491,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_YD.MD_YD_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-465 1445,-465 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24559" ObjectName="BS-MD_YD.MD_YD_9IIM"/>
    <cge:TPSR_Ref TObjectID="24559"/></metadata>
   <polyline fill="none" opacity="0" points="655,-465 1445,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_YD.MD_YD_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-826 389,-826 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24557" ObjectName="BS-MD_YD.MD_YD_3IM"/>
    <cge:TPSR_Ref TObjectID="24557"/></metadata>
   <polyline fill="none" opacity="0" points="378,-826 389,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_YD.MD_YD_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1019,-865 1031,-865 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28762" ObjectName="BS-MD_YD.MD_YD_3IIM"/>
    <cge:TPSR_Ref TObjectID="28762"/></metadata>
   <polyline fill="none" opacity="0" points="1019,-865 1031,-865 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-MD_YD.MD_YD_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1331.000000 -128.000000)" xlink:href="#capacitor:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41824" ObjectName="CB-MD_YD.MD_YD_Cb1"/>
    <cge:TPSR_Ref TObjectID="41824"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 -704.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 -704.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-MD_YD.MD_YD_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34719"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.941176 -614.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.941176 -614.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24597" ObjectName="TF-MD_YD.MD_YD_1T"/>
    <cge:TPSR_Ref TObjectID="24597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-MD_YD.MD_YD_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="41003"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 993.941176 -615.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 993.941176 -615.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28777" ObjectName="TF-MD_YD.MD_YD_2T"/>
    <cge:TPSR_Ref TObjectID="28777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 608.000000 -101.000000)" xlink:href="#transformer2:shape85_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 608.000000 -101.000000)" xlink:href="#transformer2:shape85_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2ec14f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 105.000000 -534.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e40290">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1185.000000 -610.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ecaa30">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 280.400000 -225.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ed1cc0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 59.000000 -226.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f0df20">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 517.800000 -226.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fbe400">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1080.800000 -231.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fa7f90">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 915.600000 -231.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f9a190">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 782.600000 -231.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f11c10">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1252.800000 -231.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e3f7c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 530.988466 -1100.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e542d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 976.988466 -1113.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f67170">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 584.000000 -1070.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f67960">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 828.000000 -1088.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f68600">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 348.988466 -1140.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ef9f90">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 981.400000 -675.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2efb1a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 155.000000 -541.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ef0e60">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 -107.000000 -227.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31f3520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 624.000000 -198.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31f48d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.000000 -553.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f23320">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 465.000000 -763.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -452.000000 -1089.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226304" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -420.000000 -1006.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226304" ObjectName="MD_YD:CX_YD_sumP1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226304" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -420.000000 -967.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226304" ObjectName="MD_YD:CX_YD_sumP1"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-135606" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 204.000000 -918.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24557"/>
     <cge:Term_Ref ObjectID="34640"/>
    <cge:TPSR_Ref TObjectID="24557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-135607" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 204.000000 -918.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135607" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24557"/>
     <cge:Term_Ref ObjectID="34640"/>
    <cge:TPSR_Ref TObjectID="24557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-135608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 204.000000 -918.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24557"/>
     <cge:Term_Ref ObjectID="34640"/>
    <cge:TPSR_Ref TObjectID="24557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-135612" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 204.000000 -918.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24557"/>
     <cge:Term_Ref ObjectID="34640"/>
    <cge:TPSR_Ref TObjectID="24557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-135609" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 204.000000 -918.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24557"/>
     <cge:Term_Ref ObjectID="34640"/>
    <cge:TPSR_Ref TObjectID="24557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-135613" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 204.000000 -918.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24557"/>
     <cge:Term_Ref ObjectID="34640"/>
    <cge:TPSR_Ref TObjectID="24557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-135614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -18.000000 -573.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24558"/>
     <cge:Term_Ref ObjectID="34641"/>
    <cge:TPSR_Ref TObjectID="24558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-135615" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -18.000000 -573.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24558"/>
     <cge:Term_Ref ObjectID="34641"/>
    <cge:TPSR_Ref TObjectID="24558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-135616" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -18.000000 -573.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135616" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24558"/>
     <cge:Term_Ref ObjectID="34641"/>
    <cge:TPSR_Ref TObjectID="24558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-135620" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -18.000000 -573.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24558"/>
     <cge:Term_Ref ObjectID="34641"/>
    <cge:TPSR_Ref TObjectID="24558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-135617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -18.000000 -573.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24558"/>
     <cge:Term_Ref ObjectID="34641"/>
    <cge:TPSR_Ref TObjectID="24558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-135621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -18.000000 -573.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24558"/>
     <cge:Term_Ref ObjectID="34641"/>
    <cge:TPSR_Ref TObjectID="24558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-135636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 488.941176 -652.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24597"/>
     <cge:Term_Ref ObjectID="34720"/>
    <cge:TPSR_Ref TObjectID="24597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-135637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 488.941176 -652.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24597"/>
     <cge:Term_Ref ObjectID="34720"/>
    <cge:TPSR_Ref TObjectID="24597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 524.988466 -961.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24561"/>
     <cge:Term_Ref ObjectID="34645"/>
    <cge:TPSR_Ref TObjectID="24561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135604" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 524.988466 -961.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24561"/>
     <cge:Term_Ref ObjectID="34645"/>
    <cge:TPSR_Ref TObjectID="24561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135600" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 524.988466 -961.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24561"/>
     <cge:Term_Ref ObjectID="34645"/>
    <cge:TPSR_Ref TObjectID="24561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-135605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 524.988466 -961.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24561"/>
     <cge:Term_Ref ObjectID="34645"/>
    <cge:TPSR_Ref TObjectID="24561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135633" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.941176 -604.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24571"/>
     <cge:Term_Ref ObjectID="34665"/>
    <cge:TPSR_Ref TObjectID="24571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.941176 -604.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24571"/>
     <cge:Term_Ref ObjectID="34665"/>
    <cge:TPSR_Ref TObjectID="24571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135630" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.941176 -604.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24571"/>
     <cge:Term_Ref ObjectID="34665"/>
    <cge:TPSR_Ref TObjectID="24571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-135635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.941176 -604.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24571"/>
     <cge:Term_Ref ObjectID="34665"/>
    <cge:TPSR_Ref TObjectID="24571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.000000 -118.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24575"/>
     <cge:Term_Ref ObjectID="34673"/>
    <cge:TPSR_Ref TObjectID="24575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135642" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.000000 -118.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24575"/>
     <cge:Term_Ref ObjectID="34673"/>
    <cge:TPSR_Ref TObjectID="24575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.000000 -118.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24575"/>
     <cge:Term_Ref ObjectID="34673"/>
    <cge:TPSR_Ref TObjectID="24575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.000000 -118.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24575"/>
     <cge:Term_Ref ObjectID="34673"/>
    <cge:TPSR_Ref TObjectID="24575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.000000 -118.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24575"/>
     <cge:Term_Ref ObjectID="34673"/>
    <cge:TPSR_Ref TObjectID="24575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135647" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 246.000000 -118.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135647" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24578"/>
     <cge:Term_Ref ObjectID="34679"/>
    <cge:TPSR_Ref TObjectID="24578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135648" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 246.000000 -118.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24578"/>
     <cge:Term_Ref ObjectID="34679"/>
    <cge:TPSR_Ref TObjectID="24578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135644" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 246.000000 -118.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135644" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24578"/>
     <cge:Term_Ref ObjectID="34679"/>
    <cge:TPSR_Ref TObjectID="24578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135645" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 246.000000 -118.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24578"/>
     <cge:Term_Ref ObjectID="34679"/>
    <cge:TPSR_Ref TObjectID="24578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135646" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 246.000000 -118.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24578"/>
     <cge:Term_Ref ObjectID="34679"/>
    <cge:TPSR_Ref TObjectID="24578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135653" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 483.000000 -118.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135653" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24581"/>
     <cge:Term_Ref ObjectID="34685"/>
    <cge:TPSR_Ref TObjectID="24581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135654" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 483.000000 -118.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24581"/>
     <cge:Term_Ref ObjectID="34685"/>
    <cge:TPSR_Ref TObjectID="24581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135650" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 483.000000 -118.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135650" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24581"/>
     <cge:Term_Ref ObjectID="34685"/>
    <cge:TPSR_Ref TObjectID="24581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135651" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 483.000000 -118.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135651" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24581"/>
     <cge:Term_Ref ObjectID="34685"/>
    <cge:TPSR_Ref TObjectID="24581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135652" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 483.000000 -118.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135652" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24581"/>
     <cge:Term_Ref ObjectID="34685"/>
    <cge:TPSR_Ref TObjectID="24581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135665" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 897.000000 -125.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135665" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24587"/>
     <cge:Term_Ref ObjectID="34697"/>
    <cge:TPSR_Ref TObjectID="24587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135666" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 897.000000 -125.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135666" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24587"/>
     <cge:Term_Ref ObjectID="34697"/>
    <cge:TPSR_Ref TObjectID="24587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135662" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 897.000000 -125.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135662" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24587"/>
     <cge:Term_Ref ObjectID="34697"/>
    <cge:TPSR_Ref TObjectID="24587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135663" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 897.000000 -125.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135663" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24587"/>
     <cge:Term_Ref ObjectID="34697"/>
    <cge:TPSR_Ref TObjectID="24587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135664" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 897.000000 -125.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24587"/>
     <cge:Term_Ref ObjectID="34697"/>
    <cge:TPSR_Ref TObjectID="24587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135671" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.000000 -93.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24590"/>
     <cge:Term_Ref ObjectID="34703"/>
    <cge:TPSR_Ref TObjectID="24590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135672" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.000000 -93.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135672" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24590"/>
     <cge:Term_Ref ObjectID="34703"/>
    <cge:TPSR_Ref TObjectID="24590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135668" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.000000 -93.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135668" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24590"/>
     <cge:Term_Ref ObjectID="34703"/>
    <cge:TPSR_Ref TObjectID="24590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135669" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.000000 -93.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24590"/>
     <cge:Term_Ref ObjectID="34703"/>
    <cge:TPSR_Ref TObjectID="24590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135670" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.000000 -93.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135670" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24590"/>
     <cge:Term_Ref ObjectID="34703"/>
    <cge:TPSR_Ref TObjectID="24590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188686" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1159.000000 -983.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28763"/>
     <cge:Term_Ref ObjectID="40973"/>
    <cge:TPSR_Ref TObjectID="28763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188687" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1159.000000 -983.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28763"/>
     <cge:Term_Ref ObjectID="40973"/>
    <cge:TPSR_Ref TObjectID="28763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188683" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1159.000000 -983.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188683" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28763"/>
     <cge:Term_Ref ObjectID="40973"/>
    <cge:TPSR_Ref TObjectID="28763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-188688" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1159.000000 -983.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188688" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28763"/>
     <cge:Term_Ref ObjectID="40973"/>
    <cge:TPSR_Ref TObjectID="28763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-188703" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1137.000000 -685.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188703" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28777"/>
     <cge:Term_Ref ObjectID="41001"/>
    <cge:TPSR_Ref TObjectID="28777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-188704" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1137.000000 -685.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188704" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28777"/>
     <cge:Term_Ref ObjectID="41001"/>
    <cge:TPSR_Ref TObjectID="28777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188700" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1113.000000 -595.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28769"/>
     <cge:Term_Ref ObjectID="40985"/>
    <cge:TPSR_Ref TObjectID="28769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188701" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1113.000000 -595.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28769"/>
     <cge:Term_Ref ObjectID="40985"/>
    <cge:TPSR_Ref TObjectID="28769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188697" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1113.000000 -595.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188697" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28769"/>
     <cge:Term_Ref ObjectID="40985"/>
    <cge:TPSR_Ref TObjectID="28769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-188702" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1113.000000 -595.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28769"/>
     <cge:Term_Ref ObjectID="40985"/>
    <cge:TPSR_Ref TObjectID="28769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-135622" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -583.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135622" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24559"/>
     <cge:Term_Ref ObjectID="34642"/>
    <cge:TPSR_Ref TObjectID="24559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-135623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -583.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24559"/>
     <cge:Term_Ref ObjectID="34642"/>
    <cge:TPSR_Ref TObjectID="24559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-135624" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -583.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24559"/>
     <cge:Term_Ref ObjectID="34642"/>
    <cge:TPSR_Ref TObjectID="24559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-135628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -583.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24559"/>
     <cge:Term_Ref ObjectID="34642"/>
    <cge:TPSR_Ref TObjectID="24559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-135625" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -583.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135625" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24559"/>
     <cge:Term_Ref ObjectID="34642"/>
    <cge:TPSR_Ref TObjectID="24559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-135629" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -583.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135629" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24559"/>
     <cge:Term_Ref ObjectID="34642"/>
    <cge:TPSR_Ref TObjectID="24559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-188689" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1268.000000 -914.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188689" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28762"/>
     <cge:Term_Ref ObjectID="40972"/>
    <cge:TPSR_Ref TObjectID="28762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-188690" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1268.000000 -914.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188690" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28762"/>
     <cge:Term_Ref ObjectID="40972"/>
    <cge:TPSR_Ref TObjectID="28762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-188691" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1268.000000 -914.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188691" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28762"/>
     <cge:Term_Ref ObjectID="40972"/>
    <cge:TPSR_Ref TObjectID="28762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-188695" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1268.000000 -914.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28762"/>
     <cge:Term_Ref ObjectID="40972"/>
    <cge:TPSR_Ref TObjectID="28762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-188692" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1268.000000 -914.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28762"/>
     <cge:Term_Ref ObjectID="40972"/>
    <cge:TPSR_Ref TObjectID="28762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-188696" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1268.000000 -914.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28762"/>
     <cge:Term_Ref ObjectID="40972"/>
    <cge:TPSR_Ref TObjectID="28762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188708" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1057.000000 -125.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188708" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28771"/>
     <cge:Term_Ref ObjectID="40989"/>
    <cge:TPSR_Ref TObjectID="28771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1057.000000 -125.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28771"/>
     <cge:Term_Ref ObjectID="40989"/>
    <cge:TPSR_Ref TObjectID="28771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188705" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1057.000000 -125.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188705" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28771"/>
     <cge:Term_Ref ObjectID="40989"/>
    <cge:TPSR_Ref TObjectID="28771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-188706" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1057.000000 -125.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188706" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28771"/>
     <cge:Term_Ref ObjectID="40989"/>
    <cge:TPSR_Ref TObjectID="28771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-188707" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1057.000000 -125.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188707" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28771"/>
     <cge:Term_Ref ObjectID="40989"/>
    <cge:TPSR_Ref TObjectID="28771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188714" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1225.000000 -125.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188714" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28774"/>
     <cge:Term_Ref ObjectID="40995"/>
    <cge:TPSR_Ref TObjectID="28774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188715" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1225.000000 -125.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188715" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28774"/>
     <cge:Term_Ref ObjectID="40995"/>
    <cge:TPSR_Ref TObjectID="28774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188711" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1225.000000 -125.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188711" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28774"/>
     <cge:Term_Ref ObjectID="40995"/>
    <cge:TPSR_Ref TObjectID="28774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-188712" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1225.000000 -125.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188712" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28774"/>
     <cge:Term_Ref ObjectID="40995"/>
    <cge:TPSR_Ref TObjectID="28774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-188713" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1225.000000 -125.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188713" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28774"/>
     <cge:Term_Ref ObjectID="40995"/>
    <cge:TPSR_Ref TObjectID="28774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135659" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -125.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24584"/>
     <cge:Term_Ref ObjectID="34691"/>
    <cge:TPSR_Ref TObjectID="24584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135660" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -125.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135660" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24584"/>
     <cge:Term_Ref ObjectID="34691"/>
    <cge:TPSR_Ref TObjectID="24584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135656" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -125.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135656" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24584"/>
     <cge:Term_Ref ObjectID="34691"/>
    <cge:TPSR_Ref TObjectID="24584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135657" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -125.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24584"/>
     <cge:Term_Ref ObjectID="34691"/>
    <cge:TPSR_Ref TObjectID="24584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135658" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -125.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24584"/>
     <cge:Term_Ref ObjectID="34691"/>
    <cge:TPSR_Ref TObjectID="24584"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="-441" y="-1164"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="-441" y="-1164"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-489" y="-1181"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-489" y="-1181"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="7" y="-352"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="7" y="-352"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="230" y="-351"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="230" y="-351"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="467" y="-352"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="467" y="-352"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="865" y="-357"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="865" y="-357"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="730" y="-358"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="730" y="-358"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1368" y="-357"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1368" y="-357"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1044" y="-360"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1044" y="-360"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1212" y="-362"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1212" y="-362"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="397" y="-946"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="397" y="-946"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="415" y="-676"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="415" y="-676"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1037" y="-945"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1037" y="-945"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1066" y="-708"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1066" y="-708"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="563" y="-553"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="563" y="-553"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-165" y="-1144"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-165" y="-1144"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-165" y="-1179"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-165" y="-1179"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="82" x="-537" y="-754"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="82" x="-537" y="-754"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-257,-1173 -260,-1176 -260,-1122 -257,-1125 -257,-1173" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-257,-1173 -260,-1176 -206,-1176 -209,-1173 -257,-1173" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="-257,-1125 -260,-1122 -206,-1122 -209,-1125 -257,-1125" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="-209,-1173 -206,-1176 -206,-1122 -209,-1125 -209,-1173" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="48" stroke="rgb(255,255,255)" width="48" x="-257" y="-1173"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(0,0,0)" width="48" x="-257" y="-1173"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="-441" y="-1164"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-489" y="-1181"/></g>
   <g href="35kV余丁变10kV中屯线082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="7" y="-352"/></g>
   <g href="35kV余丁变10kV蜡湾线083间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="230" y="-351"/></g>
   <g href="35kV余丁变10kV庆丰线084间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="467" y="-352"/></g>
   <g href="35kV余丁变10kV河梁线092间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="865" y="-357"/></g>
   <g href="35kV余丁变10kV天山线091间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="730" y="-358"/></g>
   <g href="35kV余丁变10kV1号电容器094间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1368" y="-357"/></g>
   <g href="35kV余丁变10kV中屯Ⅱ回线093间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1044" y="-360"/></g>
   <g href="35kV余丁变10kV许家桥线095间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1212" y="-362"/></g>
   <g href="35kV余丁变35kV新余古线381间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="397" y="-946"/></g>
   <g href="35kV余丁变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="415" y="-676"/></g>
   <g href="35kV余丁变35kV新余古线Ⅱ回T线382间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1037" y="-945"/></g>
   <g href="35kV余丁变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1066" y="-708"/></g>
   <g href="35kV余丁变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="563" y="-553"/></g>
   <g href="cx_配调_配网接线图35_牟定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-165" y="-1144"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-165" y="-1179"/></g>
   <g href="35kV余丁变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="82" x="-537" y="-754"/></g>
   <g href="AVC余丁站.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(0,0,0)" width="48" x="-257" y="-1173"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-135737">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 374.941176 -550.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24571" ObjectName="SW-MD_YD.MD_YD_001BK"/>
     <cge:Meas_Ref ObjectId="135737"/>
    <cge:TPSR_Ref TObjectID="24571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136009">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 597.000000 -520.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24594" ObjectName="SW-MD_YD.MD_YD_012BK"/>
     <cge:Meas_Ref ObjectId="136009"/>
    <cge:TPSR_Ref TObjectID="24594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 374.988466 -916.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24561" ObjectName="SW-MD_YD.MD_YD_381BK"/>
     <cge:Meas_Ref ObjectId="135720"/>
    <cge:TPSR_Ref TObjectID="24561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135890">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 211.400000 -322.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24578" ObjectName="SW-MD_YD.MD_YD_083BK"/>
     <cge:Meas_Ref ObjectId="135890"/>
    <cge:TPSR_Ref TObjectID="24578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135866">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -11.000000 -323.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24575" ObjectName="SW-MD_YD.MD_YD_082BK"/>
     <cge:Meas_Ref ObjectId="135866"/>
    <cge:TPSR_Ref TObjectID="24575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135914">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 448.800000 -323.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24581" ObjectName="SW-MD_YD.MD_YD_084BK"/>
     <cge:Meas_Ref ObjectId="135914"/>
    <cge:TPSR_Ref TObjectID="24581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188717">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1015.988466 -915.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28763" ObjectName="SW-MD_YD.MD_YD_382BK"/>
     <cge:Meas_Ref ObjectId="188717"/>
    <cge:TPSR_Ref TObjectID="28763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188728">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1015.941176 -553.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28769" ObjectName="SW-MD_YD.MD_YD_002BK"/>
     <cge:Meas_Ref ObjectId="188728"/>
    <cge:TPSR_Ref TObjectID="28769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188843">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1011.800000 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28771" ObjectName="SW-MD_YD.MD_YD_093BK"/>
     <cge:Meas_Ref ObjectId="188843"/>
    <cge:TPSR_Ref TObjectID="28771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135962">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.600000 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24587" ObjectName="SW-MD_YD.MD_YD_092BK"/>
     <cge:Meas_Ref ObjectId="135962"/>
    <cge:TPSR_Ref TObjectID="24587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135938">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 713.600000 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24584" ObjectName="SW-MD_YD.MD_YD_091BK"/>
     <cge:Meas_Ref ObjectId="135938"/>
    <cge:TPSR_Ref TObjectID="24584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188862">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1183.800000 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28774" ObjectName="SW-MD_YD.MD_YD_095BK"/>
     <cge:Meas_Ref ObjectId="188862"/>
    <cge:TPSR_Ref TObjectID="28774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135986">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1349.611111 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24590" ObjectName="SW-MD_YD.MD_YD_094BK"/>
     <cge:Meas_Ref ObjectId="135986"/>
    <cge:TPSR_Ref TObjectID="24590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -177.000000 -324.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="MD_YD" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xinyuguTyd" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="384,-1144 384,-1182 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34585" ObjectName="AC-35kV.LN_xinyuguTyd"/>
    <cge:TPSR_Ref TObjectID="34585_SS-205"/></metadata>
   <polyline fill="none" opacity="0" points="384,-1144 384,-1182 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2ec18e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 298.988466 -834.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f10ef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 334.988466 -993.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc0690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 297.988466 -918.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f3a1e0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 301.000000 -1206.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ed29c0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 263.941176 -726.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fe16a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 938.988466 -833.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ed53f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 974.988466 -992.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f9cb40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 937.988466 -926.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e89990" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1416.000000 -333.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31f5740" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1391.000000 -333.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f23cf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 467.988466 -676.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f26ee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 279.988466 -753.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24559" cx="1025" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24559" cx="1234" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24559" cx="723" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24559" cx="856" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24559" cx="1021" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24559" cx="1193" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24559" cx="1359" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24559" cx="681" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24558" cx="-1" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24558" cx="220" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24558" cx="458" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24558" cx="384" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24558" cx="164" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24558" cx="477" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24558" cx="-167" cy="-465" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24557" cx="384" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24557" cx="384" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28762" cx="1026" cy="-865" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28762" cx="1026" cy="-865" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2f8cc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -402.000000 -1150.500000) translate(0,16)">余丁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eeb980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -1012.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eeb980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -1012.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eeb980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -1012.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eeb980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -1012.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eeb980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -1012.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eeb980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -1012.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eeb980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -1012.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eeb980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -1012.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2eeb980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -1012.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2fda8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -543.000000 -574.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef4df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 10.000000 -425.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef52e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 10.000000 -281.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef5530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 230.000000 -425.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef5740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 230.000000 -281.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef5950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -425.000000) translate(0,12)">0841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3d9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -281.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3dbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 693.000000 -520.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3ddf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 491.000000 -522.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3e030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1250.000000 -522.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f3e3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 333.000000 -518.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f14400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -579.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f14640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 404.000000 -756.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_308d510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 -742.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_308db00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 175.000000 -508.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_308df10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 397.000000 -878.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_308e150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 397.000000 -1045.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_308e390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 252.000000 -966.000000) translate(0,12)">38160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e40ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 257.000000 -881.000000) translate(0,12)">38117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e40ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 285.000000 -1046.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e41120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 266.000000 -1109.000000) translate(0,12)">3819</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e41360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -1167.000000) translate(0,12)">38197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e415a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 54.000000 -657.000000) translate(0,12)">10kV  I母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb45a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 501.000000 -742.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec0fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1168.000000 -705.000000) translate(0,12)">10kV II母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe3f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41.000000 -490.000000) translate(0,12)">10kV  I母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe4130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1420.000000 -488.000000) translate(0,12)">10kV  II母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe4380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -1229.000000) translate(0,12)">35kV新余古线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f608d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 96.000000 -1155.000000) translate(0,12)">35kV进线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f60f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 240.000000 -636.000000) translate(0,12)">SZ11-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f60f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 240.000000 -636.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f60f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 240.000000 -636.000000) translate(0,42)">yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f60f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 240.000000 -636.000000) translate(0,57)">Ud%=7.06</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f70610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 415.941176 -676.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f70c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7.000000 -352.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f70e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 230.000000 -351.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f710a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 467.000000 -352.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f712e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 397.988466 -946.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f71520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 563.000000 -553.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef3c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1037.000000 -877.000000) translate(0,12)">3822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef4210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1039.000000 -1038.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef4450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 880.000000 -966.000000) translate(0,12)">38260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef4690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -881.000000) translate(0,12)">38227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef48d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 930.000000 -1046.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef4b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 -1101.000000) translate(0,12)">35kV新余古线II回T线电缆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebe1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1037.988466 -945.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb1710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 961.000000 -580.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb20f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 -527.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb2f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1076.000000 -1076.000000) translate(0,12)">35kV新余古线II回T线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3156b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1031.000000 -430.000000) translate(0,12)">0932</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3157000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1031.000000 -286.000000) translate(0,12)">0936</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fa8d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -430.000000) translate(0,12)">0922</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fa9270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 867.000000 -286.000000) translate(0,12)">0926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2faa540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 865.000000 -357.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_301e230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.000000 -103.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f9af40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 734.000000 -430.000000) translate(0,12)">0912</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f9b430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 734.000000 -286.000000) translate(0,12)">0916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f4a280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -358.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f39d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1203.000000 -430.000000) translate(0,12)">0952</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ead690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1203.000000 -286.000000) translate(0,12)">0956</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ffd110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1370.000000 -430.000000) translate(0,12)">0942</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ffd600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1309.000000 -292.000000) translate(0,12)">0946</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ffd840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1420.000000 -291.000000) translate(0,12)">09467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ffda80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1321.000000 -120.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fc1150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1368.000000 -357.000000) translate(0,12)">094</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3de70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1044.000000 -358.000000) translate(0,12)">093</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3e3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1212.000000 -359.000000) translate(0,12)">095</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f88af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -1183.000000) translate(0,12)">35kV新余古线避雷器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f898e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 949.000000 -1197.000000) translate(0,12)">35kV新余古线II回T线避雷器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e8e5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -708.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2e50cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -1136.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2e532b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -1171.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f65840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 796.000000 -694.000000) translate(0,12)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f65840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 796.000000 -694.000000) translate(0,27)">35000±3×2.5%/10500V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f65840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 796.000000 -694.000000) translate(0,42)">yNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f65840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 796.000000 -694.000000) translate(0,57)">Ud%=7.33</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f66100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -533.000000 -751.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef99a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 534.000000 -1055.000000) translate(0,12)">35kV新余古线II回T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f1e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -156.000000 -426.000000) translate(0,12)">0851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f1e8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -156.000000 -282.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f1eb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -194.000000 -146.000000) translate(0,12)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f20590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -159.000000 -353.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 584.000000 -84.000000) translate(0,12)">S11-50/10+5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee52c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 321.000000 -818.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee57b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 240.000000 -800.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee59f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 493.000000 -721.000000) translate(0,12)">S11-50/35+5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee6490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -30.000000 -144.000000) translate(0,12)">10kV中屯线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee6c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 186.000000 -146.000000) translate(0,12)">10kV蜡湾线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee74f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 417.000000 -146.000000) translate(0,12)">10kV庆丰线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee7d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 -148.000000) translate(0,12)">10kV天山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee85f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 -148.000000) translate(0,12)">10kV河梁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee8e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 984.000000 -145.000000) translate(0,12)">10kV中屯Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ee93e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1159.000000 -149.000000) translate(0,12)">10kV许家桥线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_31feeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -556.000000 -169.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_31feeb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -556.000000 -169.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb3c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -424.000000 -178.000000) translate(0,16)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb3c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -424.000000 -178.000000) translate(0,36)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb3c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -424.000000 -178.000000) translate(0,56)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb5cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -424.000000 -226.000000) translate(0,16)">5383116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2eb5cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -424.000000 -226.000000) translate(0,36)">2260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2eb60a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -251.000000 -1158.000000) translate(0,16)">AVC</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eeae80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1354.000000 48.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_308d130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1354.000000 63.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b5f310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 93.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d69010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.000000 77.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f08850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1354.000000 33.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd25a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1184.000000 80.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6e510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1184.000000 95.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c73d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1170.000000 125.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eca240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.000000 109.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ecec10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1184.000000 65.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f71850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 149.000000 887.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd8ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 149.000000 902.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd8d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 149.000000 918.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd8f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 155.000000 872.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd9180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 157.000000 840.000000) translate(0,12)">F(HZ)</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd9930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 141.000000 857.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd9ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -73.000000 545.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd9f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -73.000000 560.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6e780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -73.000000 576.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6e9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -67.000000 530.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6ec10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -65.000000 498.000000) translate(0,12)">F(HZ)</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6ee50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -81.000000 515.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6f180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1326.000000 554.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6f400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1326.000000 569.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6f640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1326.000000 585.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6f880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1332.000000 539.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6fac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1334.000000 507.000000) translate(0,12)">F(HZ)</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f4a850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1318.000000 524.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f4b900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 415.000000 638.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f19ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 415.000000 653.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebe860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1212.000000 884.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebeae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1212.000000 899.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ebed20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1212.000000 915.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f04720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1218.000000 869.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f04910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1220.000000 837.000000) translate(0,12)">F(HZ)</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f04b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1204.000000 854.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb29f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1066.000000 669.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb2d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1066.000000 684.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f89cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 430.000000 557.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f8a930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 426.000000 575.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f8abb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 412.000000 605.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f8adf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 401.000000 590.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f8b120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1075.000000 549.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f8b390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 567.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e8acb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1057.000000 597.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e8aed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 582.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f90e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 915.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f9390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 476.000000 933.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f95d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.000000 963.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f9810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.000000 948.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f9b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1116.000000 937.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f9db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1112.000000 955.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f9ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 985.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fa230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1087.000000 970.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fa560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -22.000000 73.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fa7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -22.000000 88.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31faa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -36.000000 118.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fac50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -47.000000 102.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fae90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -22.000000 58.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fb1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 203.000000 73.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fb430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 203.000000 88.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fb670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 189.000000 118.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fb8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 178.000000 102.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fbaf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 203.000000 58.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fbe20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 443.000000 73.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fc090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 443.000000 88.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fc2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 429.000000 118.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fc510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 418.000000 102.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fc750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 443.000000 58.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fca80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 80.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fccf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 95.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fcf30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 703.000000 125.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fd170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 692.000000 109.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fd3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 65.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fd6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 854.000000 80.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fd950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 854.000000 95.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fdb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 840.000000 125.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fddd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 829.000000 109.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fe010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 854.000000 65.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fe340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1014.000000 80.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fe5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1014.000000 95.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fe7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1000.000000 125.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fea30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 989.000000 109.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fec70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1014.000000 65.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-MD_YD.082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -10.000000 -159.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33993" ObjectName="EC-MD_YD.082Ld"/>
    <cge:TPSR_Ref TObjectID="33993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_YD.083Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 211.000000 -159.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33994" ObjectName="EC-MD_YD.083Ld"/>
    <cge:TPSR_Ref TObjectID="33994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_YD.084Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 449.000000 -154.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33995" ObjectName="EC-MD_YD.084Ld"/>
    <cge:TPSR_Ref TObjectID="33995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_YD.093Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1012.000000 -159.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33998" ObjectName="EC-MD_YD.093Ld"/>
    <cge:TPSR_Ref TObjectID="33998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_YD.092Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 847.000000 -159.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33997" ObjectName="EC-MD_YD.092Ld"/>
    <cge:TPSR_Ref TObjectID="33997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_YD.091Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -159.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33996" ObjectName="EC-MD_YD.091Ld"/>
    <cge:TPSR_Ref TObjectID="33996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_YD.095Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1184.000000 -159.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33999" ObjectName="EC-MD_YD.095Ld"/>
    <cge:TPSR_Ref TObjectID="33999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 -160.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-135582" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -247.000000 -1059.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24540" ObjectName="DYN-MD_YD"/>
     <cge:Meas_Ref ObjectId="135582"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f52100">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 145.000000 -587.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ee25c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.000000 -630.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f3e7a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 298.988466 -1154.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eff310">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1100.988466 -1103.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_2fa5ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1,-433 -1,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24576@1" ObjectIDZND0="24558@0" Pin0InfoVect0LinkObjId="g_2fc9870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135868_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1,-433 -1,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fa5cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1,-397 -1,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24576@0" ObjectIDZND0="24575@1" Pin0InfoVect0LinkObjId="SW-135866_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135868_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1,-397 -1,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fa5eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1,-331 -1,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24575@0" ObjectIDZND0="24577@1" Pin0InfoVect0LinkObjId="SW-135869_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135866_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1,-331 -1,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fa60e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1,-231 52,-231 52,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="24577@x" ObjectIDND1="33993@x" ObjectIDZND0="g_2ed1cc0@0" Pin0InfoVect0LinkObjId="g_2ed1cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135869_0" Pin1InfoVect1LinkObjId="EC-MD_YD.082Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1,-231 52,-231 52,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc9410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1,-260 -1,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24577@0" ObjectIDZND0="g_2ed1cc0@0" ObjectIDZND1="33993@x" Pin0InfoVect0LinkObjId="g_2ed1cc0_0" Pin0InfoVect1LinkObjId="EC-MD_YD.082Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135869_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-1,-260 -1,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc9640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1,-231 -1,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2ed1cc0@0" ObjectIDND1="24577@x" ObjectIDZND0="33993@0" Pin0InfoVect0LinkObjId="EC-MD_YD.082Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ed1cc0_0" Pin1InfoVect1LinkObjId="SW-135869_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1,-231 -1,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc9870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="220,-432 220,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24579@1" ObjectIDZND0="24558@0" Pin0InfoVect0LinkObjId="g_2fa5ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135892_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="220,-432 220,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc9aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="220,-396 220,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24579@0" ObjectIDZND0="24578@1" Pin0InfoVect0LinkObjId="SW-135890_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135892_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="220,-396 220,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc9cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="220,-330 220,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24578@0" ObjectIDZND0="24580@1" Pin0InfoVect0LinkObjId="SW-135893_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="220,-330 220,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc9f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-433 458,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24582@1" ObjectIDZND0="24558@0" Pin0InfoVect0LinkObjId="g_2fa5ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-433 458,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fca130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-397 458,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24582@0" ObjectIDZND0="24581@1" Pin0InfoVect0LinkObjId="SW-135914_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-397 458,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fca390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-331 458,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24581@0" ObjectIDZND0="24583@1" Pin0InfoVect0LinkObjId="SW-135917_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135914_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-331 458,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc80b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-488 384,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24572@0" ObjectIDZND0="24558@0" Pin0InfoVect0LinkObjId="g_2fa5ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-488 384,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc8310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-524 384,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24572@1" ObjectIDZND0="24571@0" Pin0InfoVect0LinkObjId="SW-135737_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135739_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-524 384,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc8570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-585 384,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24571@1" ObjectIDZND0="24597@0" Pin0InfoVect0LinkObjId="g_2fc8a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135737_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-585 384,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc87d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-768 474,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2f23320@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f23320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="474,-768 474,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc8a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="337,-720 384,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="24570@0" ObjectIDZND0="24597@x" ObjectIDZND1="24569@x" Pin0InfoVect0LinkObjId="g_2fc8570_0" Pin0InfoVect1LinkObjId="SW-135735_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="337,-720 384,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc8c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-699 384,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24597@1" ObjectIDZND0="24570@x" ObjectIDZND1="24569@x" Pin0InfoVect0LinkObjId="SW-135736_0" Pin0InfoVect1LinkObjId="SW-135735_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fc8570_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="384,-699 384,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc8ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-720 384,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="24570@x" ObjectIDND1="24597@x" ObjectIDZND0="24569@0" Pin0InfoVect0LinkObjId="SW-135735_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135736_0" Pin1InfoVect1LinkObjId="g_2fc8570_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-720 384,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc9150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="301,-720 282,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24570@1" ObjectIDZND0="g_2ed29c0@0" Pin0InfoVect0LinkObjId="g_2ed29c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="301,-720 282,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff5ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="164,-486 164,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24573@0" ObjectIDZND0="24558@0" Pin0InfoVect0LinkObjId="g_2fa5ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="164,-486 164,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff6250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="477,-477 477,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24595@0" ObjectIDZND0="24558@0" Pin0InfoVect0LinkObjId="g_2fa5ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136011_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="477,-477 477,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff64b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="477,-513 477,-529 561,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24595@1" ObjectIDZND0="24594@1" Pin0InfoVect0LinkObjId="SW-136009_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136011_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="477,-513 477,-529 561,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff6710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="588,-529 681,-529 681,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24594@0" ObjectIDZND0="24596@1" Pin0InfoVect0LinkObjId="SW-136012_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136009_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="588,-529 681,-529 681,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff6970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="681,-480 681,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24596@0" ObjectIDZND0="24559@0" Pin0InfoVect0LinkObjId="g_2ff6bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136012_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="681,-480 681,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff6bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1234,-501 1234,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24574@0" ObjectIDZND0="24559@0" Pin0InfoVect0LinkObjId="g_2ff6970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1234,-501 1234,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff6e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-614 1192,-605 1235,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="g_2e40290@0" ObjectIDZND0="g_2ee25c0@0" ObjectIDZND1="g_31f48d0@0" Pin0InfoVect0LinkObjId="g_2ee25c0_0" Pin0InfoVect1LinkObjId="g_31f48d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e40290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-614 1192,-605 1235,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff7090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1235,-605 1235,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2e40290@0" ObjectIDND1="g_31f48d0@0" ObjectIDZND0="g_2ee25c0@0" Pin0InfoVect0LinkObjId="g_2ee25c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e40290_0" Pin1InfoVect1LinkObjId="g_31f48d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1235,-605 1235,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fbc830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-1067 341,-1067 341,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24567@x" ObjectIDND1="g_2e3f7c0@0" ObjectIDND2="g_2f67170@0" ObjectIDZND0="24564@0" Pin0InfoVect0LinkObjId="SW-135724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135733_0" Pin1InfoVect1LinkObjId="g_2e3f7c0_0" Pin1InfoVect2LinkObjId="g_2f67170_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-1067 341,-1067 341,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fbca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="341,-1023 341,-1011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24564@1" ObjectIDZND0="g_2f10ef0@0" Pin0InfoVect0LinkObjId="g_2f10ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="341,-1023 341,-1011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fbd4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="295,-1188 295,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2f3a1e0@0" ObjectIDZND0="24568@1" Pin0InfoVect0LinkObjId="SW-135734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f3a1e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="295,-1188 295,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fbd710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-945 304,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24566@1" ObjectIDZND0="g_2fc0690@0" Pin0InfoVect0LinkObjId="g_2fc0690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135726_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-945 304,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fbd970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="305,-859 305,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24565@1" ObjectIDZND0="g_2ec18e0@0" Pin0InfoVect0LinkObjId="g_2ec18e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="305,-859 305,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff98b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-233 458,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24583@x" ObjectIDND1="g_2f0df20@0" ObjectIDZND0="33995@0" Pin0InfoVect0LinkObjId="EC-MD_YD.084Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135917_0" Pin1InfoVect1LinkObjId="g_2f0df20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-233 458,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff9b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="511,-221 511,-233 458,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2f0df20@0" ObjectIDZND0="24583@x" ObjectIDZND1="33995@x" Pin0InfoVect0LinkObjId="SW-135917_0" Pin0InfoVect1LinkObjId="EC-MD_YD.084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f0df20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="511,-221 511,-233 458,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff9d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-233 458,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2f0df20@0" ObjectIDND1="33995@x" ObjectIDZND0="24583@0" Pin0InfoVect0LinkObjId="SW-135917_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f0df20_0" Pin1InfoVect1LinkObjId="EC-MD_YD.084Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-233 458,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ff9fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-694 474,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="transformer2" ObjectIDND0="g_2f23cf0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f23cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="474,-694 474,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ffa230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="220,-231 220,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24580@x" ObjectIDND1="g_2ecaa30@0" ObjectIDZND0="33994@0" Pin0InfoVect0LinkObjId="EC-MD_YD.083Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135893_0" Pin1InfoVect1LinkObjId="g_2ecaa30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="220,-231 220,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ffa490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,-220 273,-231 220,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2ecaa30@0" ObjectIDZND0="24580@x" ObjectIDZND1="33994@x" Pin0InfoVect0LinkObjId="SW-135893_0" Pin0InfoVect1LinkObjId="EC-MD_YD.083Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ecaa30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="273,-220 273,-231 220,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ffa6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="220,-231 220,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2ecaa30@0" ObjectIDND1="33994@x" ObjectIDZND0="24580@0" Pin0InfoVect0LinkObjId="SW-135893_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ecaa30_0" Pin1InfoVect1LinkObjId="EC-MD_YD.083Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="220,-231 220,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f1a870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-981 304,-991 384,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="24566@0" ObjectIDZND0="24561@x" ObjectIDZND1="24562@x" Pin0InfoVect0LinkObjId="SW-135720_0" Pin0InfoVect1LinkObjId="SW-135722_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="304,-981 304,-991 384,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f1aa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-951 384,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24561@1" ObjectIDZND0="24566@x" ObjectIDZND1="24562@x" Pin0InfoVect0LinkObjId="SW-135726_0" Pin0InfoVect1LinkObjId="SW-135722_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="384,-951 384,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f1ac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="305,-895 305,-905 384,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24565@0" ObjectIDZND0="24563@x" ObjectIDZND1="24561@x" Pin0InfoVect0LinkObjId="SW-135723_0" Pin0InfoVect1LinkObjId="SW-135720_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135725_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="305,-895 305,-905 384,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f1ae40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-888 384,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24563@1" ObjectIDZND0="24565@x" ObjectIDZND1="24561@x" Pin0InfoVect0LinkObjId="SW-135725_0" Pin0InfoVect1LinkObjId="SW-135720_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135723_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="384,-888 384,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f1b070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-905 384,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="24563@x" ObjectIDND1="24565@x" ObjectIDZND0="24561@0" Pin0InfoVect0LinkObjId="SW-135720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135723_0" Pin1InfoVect1LinkObjId="SW-135725_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-905 384,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebd710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-1066 981,-1066 981,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2eff310@0" ObjectIDND1="g_2e542d0@0" ObjectIDND2="g_2f67960@0" ObjectIDZND0="28766@0" Pin0InfoVect0LinkObjId="SW-188720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2eff310_0" Pin1InfoVect1LinkObjId="g_2e542d0_0" Pin1InfoVect2LinkObjId="g_2f67960_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-1066 981,-1066 981,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebd900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-1051 1025,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="28762@0" ObjectIDZND0="28766@x" ObjectIDZND1="g_2eff310@0" ObjectIDZND2="g_2e542d0@0" Pin0InfoVect0LinkObjId="SW-188720_0" Pin0InfoVect1LinkObjId="g_2eff310_0" Pin0InfoVect2LinkObjId="g_2e542d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38e3ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-1051 1025,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebdb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1022 981,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28766@1" ObjectIDZND0="g_2ed53f0@0" Pin0InfoVect0LinkObjId="g_2ed53f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1022 981,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebdd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-953 944,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28768@1" ObjectIDZND0="g_2f9cb40@0" Pin0InfoVect0LinkObjId="g_2f9cb40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188722_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="944,-953 944,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ebdf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="945,-858 945,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28767@1" ObjectIDZND0="g_2fe16a0@0" Pin0InfoVect0LinkObjId="g_2fe16a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188721_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="945,-858 945,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f04d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="944,-989 944,-999 1025,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28768@0" ObjectIDZND0="28763@x" ObjectIDZND1="28764@x" Pin0InfoVect0LinkObjId="SW-188717_0" Pin0InfoVect1LinkObjId="SW-188718_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188722_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="944,-989 944,-999 1025,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f04f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-950 1025,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28763@1" ObjectIDZND0="28768@x" ObjectIDZND1="28764@x" Pin0InfoVect0LinkObjId="SW-188722_0" Pin0InfoVect1LinkObjId="SW-188718_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188717_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-950 1025,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f05170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-999 1025,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="28768@x" ObjectIDND1="28763@x" ObjectIDZND0="28764@0" Pin0InfoVect0LinkObjId="SW-188718_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188722_0" Pin1InfoVect1LinkObjId="SW-188717_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-999 1025,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef7920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1026,-800 1025,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="28765@0" ObjectIDZND0="28777@1" Pin0InfoVect0LinkObjId="g_2efaab0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188719_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-800 1025,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fb0ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-620 1025,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="28777@0" ObjectIDZND0="28769@1" Pin0InfoVect0LinkObjId="SW-188728_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ef7920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-620 1025,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fb1250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-561 1025,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28769@0" ObjectIDZND0="28770@1" Pin0InfoVect0LinkObjId="SW-188729_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-561 1025,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fb14b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-501 1025,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28770@0" ObjectIDZND0="24559@0" Pin0InfoVect0LinkObjId="g_2ff6970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188729_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-501 1025,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3157240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1021,-404 1021,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28772@0" ObjectIDZND0="28771@1" Pin0InfoVect0LinkObjId="SW-188843_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188844_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1021,-404 1021,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3157430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1021,-336 1021,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28771@0" ObjectIDZND0="28773@1" Pin0InfoVect0LinkObjId="SW-188845_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188843_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1021,-336 1021,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3157620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1021,-238 1021,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2fbe400@0" ObjectIDND1="28773@x" ObjectIDZND0="33998@0" Pin0InfoVect0LinkObjId="EC-MD_YD.093Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fbe400_0" Pin1InfoVect1LinkObjId="SW-188845_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1021,-238 1021,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3157810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,-226 1074,-238 1021,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2fbe400@0" ObjectIDZND0="33998@x" ObjectIDZND1="28773@x" Pin0InfoVect0LinkObjId="EC-MD_YD.093Ld_0" Pin0InfoVect1LinkObjId="SW-188845_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fbe400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-226 1074,-238 1021,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f8d620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1021,-238 1021,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2fbe400@0" ObjectIDND1="33998@x" ObjectIDZND0="28773@0" Pin0InfoVect0LinkObjId="SW-188845_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fbe400_0" Pin1InfoVect1LinkObjId="EC-MD_YD.093Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1021,-238 1021,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fa94b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="856,-402 856,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24588@0" ObjectIDZND0="24587@1" Pin0InfoVect0LinkObjId="SW-135962_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135964_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="856,-402 856,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fa96a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="856,-336 856,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24587@0" ObjectIDZND0="24589@1" Pin0InfoVect0LinkObjId="SW-135965_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135962_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="856,-336 856,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fa9890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="856,-240 911,-240 909,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="24589@x" ObjectIDND1="33997@x" ObjectIDZND0="g_2fa7f90@0" Pin0InfoVect0LinkObjId="g_2fa7f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135965_0" Pin1InfoVect1LinkObjId="EC-MD_YD.092Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="856,-240 911,-240 909,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fa9a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="856,-265 856,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24589@0" ObjectIDZND0="g_2fa7f90@0" ObjectIDZND1="33997@x" Pin0InfoVect0LinkObjId="g_2fa7f90_0" Pin0InfoVect1LinkObjId="EC-MD_YD.092Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135965_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="856,-265 856,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fa9cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="856,-240 856,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24589@x" ObjectIDND1="g_2fa7f90@0" ObjectIDZND0="33997@0" Pin0InfoVect0LinkObjId="EC-MD_YD.092Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135965_0" Pin1InfoVect1LinkObjId="g_2fa7f90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="856,-240 856,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_301dfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="723,-252 633,-252 633,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24586@x" ObjectIDND1="33996@x" ObjectIDND2="g_2f9a190@0" ObjectIDZND0="g_31f3520@0" Pin0InfoVect0LinkObjId="g_31f3520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135941_0" Pin1InfoVect1LinkObjId="EC-MD_YD.091Ld_0" Pin1InfoVect2LinkObjId="g_2f9a190_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="723,-252 633,-252 633,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f9b670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="723,-402 723,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24585@0" ObjectIDZND0="24584@1" Pin0InfoVect0LinkObjId="SW-135938_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="723,-402 723,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f9b860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="723,-336 723,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24584@0" ObjectIDZND0="24586@1" Pin0InfoVect0LinkObjId="SW-135941_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135938_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="723,-336 723,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f47880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="723,-240 778,-240 776,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="33996@x" ObjectIDND1="24586@x" ObjectIDND2="g_31f3520@0" ObjectIDZND0="g_2f9a190@0" Pin0InfoVect0LinkObjId="g_2f9a190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-MD_YD.091Ld_0" Pin1InfoVect1LinkObjId="SW-135941_0" Pin1InfoVect2LinkObjId="g_31f3520_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="723,-240 778,-240 776,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f47a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="723,-240 723,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="g_2f9a190@0" ObjectIDND1="24586@x" ObjectIDND2="g_31f3520@0" ObjectIDZND0="33996@0" Pin0InfoVect0LinkObjId="EC-MD_YD.091Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f9a190_0" Pin1InfoVect1LinkObjId="SW-135941_0" Pin1InfoVect2LinkObjId="g_31f3520_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="723,-240 723,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ead8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-402 1193,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28775@0" ObjectIDZND0="28774@1" Pin0InfoVect0LinkObjId="SW-188862_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188863_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-402 1193,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eadac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-336 1193,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28774@0" ObjectIDZND0="28776@1" Pin0InfoVect0LinkObjId="SW-188864_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-336 1193,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eadcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-238 1193,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2f11c10@0" ObjectIDND1="28776@x" ObjectIDZND0="33999@0" Pin0InfoVect0LinkObjId="EC-MD_YD.095Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f11c10_0" Pin1InfoVect1LinkObjId="SW-188864_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-238 1193,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eadea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1246,-226 1246,-238 1193,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2f11c10@0" ObjectIDZND0="33999@x" ObjectIDZND1="28776@x" Pin0InfoVect0LinkObjId="EC-MD_YD.095Ld_0" Pin0InfoVect1LinkObjId="SW-188864_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f11c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1246,-226 1246,-238 1193,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eae0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-238 1193,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2f11c10@0" ObjectIDND1="33999@x" ObjectIDZND0="28776@0" Pin0InfoVect0LinkObjId="SW-188864_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f11c10_0" Pin1InfoVect1LinkObjId="EC-MD_YD.095Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-238 1193,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ffe1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-402 1359,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24591@0" ObjectIDZND0="24590@1" Pin0InfoVect0LinkObjId="SW-135986_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135988_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-402 1359,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc0ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-336 1359,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24590@0" ObjectIDZND0="24592@1" Pin0InfoVect0LinkObjId="SW-135989_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135986_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-336 1359,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc0cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1410,-261 1410,-130 1376,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="24593@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1410,-261 1410,-130 1376,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc0f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1410,-301 1410,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24593@1" ObjectIDZND0="g_2e89990@0" Pin0InfoVect0LinkObjId="g_2e89990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1410,-301 1410,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc35e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="723,-438 723,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24585@1" ObjectIDZND0="24559@0" Pin0InfoVect0LinkObjId="g_2ff6970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135940_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="723,-438 723,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc3e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="856,-438 856,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24588@1" ObjectIDZND0="24559@0" Pin0InfoVect0LinkObjId="g_2ff6970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135964_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="856,-438 856,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3c750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1021,-440 1021,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28772@1" ObjectIDZND0="24559@0" Pin0InfoVect0LinkObjId="g_2ff6970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188844_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1021,-440 1021,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3ce10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-438 1193,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28775@1" ObjectIDZND0="24559@0" Pin0InfoVect0LinkObjId="g_2ff6970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188863_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-438 1193,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3d640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-438 1359,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24591@1" ObjectIDZND0="24559@0" Pin0InfoVect0LinkObjId="g_2ff6970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135988_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-438 1359,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3f3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="823,-1080 1025,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="busSection" EndDevType2="voltageTransformer" ObjectIDND0="g_2f67960@1" ObjectIDZND0="28766@x" ObjectIDZND1="28762@0" ObjectIDZND2="g_2eff310@0" Pin0InfoVect0LinkObjId="SW-188720_0" Pin0InfoVect1LinkObjId="g_38e3ad0_0" Pin0InfoVect2LinkObjId="g_2eff310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f67960_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="823,-1080 1025,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3f590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-1066 1025,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28766@x" ObjectIDND1="28762@0" ObjectIDZND0="g_2eff310@0" ObjectIDZND1="g_2e542d0@0" ObjectIDZND2="g_2f67960@0" Pin0InfoVect0LinkObjId="g_2eff310_0" Pin0InfoVect1LinkObjId="g_2e542d0_0" Pin0InfoVect2LinkObjId="g_2f67960_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188720_0" Pin1InfoVect1LinkObjId="g_38e3ad0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-1066 1025,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f878e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="318,-1089 318,-1078 384,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" EndDevType2="lightningRod" ObjectIDND0="24567@0" ObjectIDZND0="24564@x" ObjectIDZND1="24557@0" ObjectIDZND2="g_2e3f7c0@0" Pin0InfoVect0LinkObjId="SW-135724_0" Pin0InfoVect1LinkObjId="g_3840e80_0" Pin0InfoVect2LinkObjId="g_2e3f7c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="318,-1089 318,-1078 384,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f87b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="538,-1104 538,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="busSection" ObjectIDND0="g_2e3f7c0@0" ObjectIDZND0="g_2f67170@0" ObjectIDZND1="24564@x" ObjectIDZND2="24557@0" Pin0InfoVect0LinkObjId="g_2f67170_0" Pin0InfoVect1LinkObjId="SW-135724_0" Pin0InfoVect2LinkObjId="g_3840e80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e3f7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="538,-1104 538,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f88630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-1078 538,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="24564@x" ObjectIDND1="24557@0" ObjectIDND2="24567@x" ObjectIDZND0="g_2e3f7c0@0" ObjectIDZND1="g_2f67170@0" Pin0InfoVect0LinkObjId="g_2e3f7c0_0" Pin0InfoVect1LinkObjId="g_2f67170_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135724_0" Pin1InfoVect1LinkObjId="g_3840e80_0" Pin1InfoVect2LinkObjId="SW-135733_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="384,-1078 538,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f88890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="538,-1078 589,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="busSection" EndDevType0="lightningRod" ObjectIDND0="g_2e3f7c0@0" ObjectIDND1="24564@x" ObjectIDND2="24557@0" ObjectIDZND0="g_2f67170@1" Pin0InfoVect0LinkObjId="g_2f67170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e3f7c0_0" Pin1InfoVect1LinkObjId="SW-135724_0" Pin1InfoVect2LinkObjId="g_3840e80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="538,-1078 589,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f896f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-1080 1025,-1086 1120,-1086 1120,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="28766@x" ObjectIDND1="28762@0" ObjectIDND2="g_2e542d0@0" ObjectIDZND0="g_2eff310@0" Pin0InfoVect0LinkObjId="g_2eff310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-188720_0" Pin1InfoVect1LinkObjId="g_38e3ad0_0" Pin1InfoVect2LinkObjId="g_2e542d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-1080 1025,-1086 1120,-1086 1120,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e8e1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-1078 384,-1144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="24564@x" ObjectIDND1="24557@0" ObjectIDND2="24567@x" ObjectIDZND0="34585@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135724_0" Pin1InfoVect1LinkObjId="g_3840e80_0" Pin1InfoVect2LinkObjId="SW-135733_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-1078 384,-1144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e506c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="723,-265 723,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24586@0" ObjectIDZND0="33996@x" ObjectIDZND1="g_2f9a190@0" ObjectIDZND2="g_31f3520@0" Pin0InfoVect0LinkObjId="EC-MD_YD.091Ld_0" Pin0InfoVect1LinkObjId="g_2f9a190_0" Pin0InfoVect2LinkObjId="g_31f3520_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135941_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="723,-265 723,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e508e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="723,-252 723,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="24586@x" ObjectIDND1="g_31f3520@0" ObjectIDZND0="33996@x" ObjectIDZND1="g_2f9a190@0" Pin0InfoVect0LinkObjId="EC-MD_YD.091Ld_0" Pin0InfoVect1LinkObjId="g_2f9a190_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135941_0" Pin1InfoVect1LinkObjId="g_31f3520_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="723,-252 723,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f64890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,-1117 1025,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="busSection" EndDevType2="voltageTransformer" ObjectIDND0="g_2e542d0@0" ObjectIDZND0="28766@x" ObjectIDZND1="28762@0" ObjectIDZND2="g_2eff310@0" Pin0InfoVect0LinkObjId="SW-188720_0" Pin0InfoVect1LinkObjId="g_38e3ad0_0" Pin0InfoVect2LinkObjId="g_2eff310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e542d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="984,-1117 1025,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f65380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-1152 1025,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="busSection" ObjectIDZND0="g_2e542d0@0" ObjectIDZND1="28766@x" ObjectIDZND2="28762@0" Pin0InfoVect0LinkObjId="g_2e542d0_0" Pin0InfoVect1LinkObjId="SW-188720_0" Pin0InfoVect2LinkObjId="g_38e3ad0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-1152 1025,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f655e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-1117 1025,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="busSection" EndDevType2="voltageTransformer" ObjectIDND0="g_2e542d0@0" ObjectIDZND0="28766@x" ObjectIDZND1="28762@0" ObjectIDZND2="g_2eff310@0" Pin0InfoVect0LinkObjId="SW-188720_0" Pin0InfoVect1LinkObjId="g_38e3ad0_0" Pin0InfoVect2LinkObjId="g_2eff310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e542d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-1117 1025,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f683a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="794,-1079 618,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2f67960@0" ObjectIDZND0="g_2f67170@0" Pin0InfoVect0LinkObjId="g_2f67170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f67960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="794,-1079 618,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef94d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="318,-1159 318,-1131 318,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2f3e7a0@0" ObjectIDZND0="24567@1" Pin0InfoVect0LinkObjId="SW-135733_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f3e7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="318,-1159 318,-1131 318,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef9730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="295,-1141 295,-1128 356,-1128 356,-1144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24568@0" ObjectIDZND0="g_2f68600@0" Pin0InfoVect0LinkObjId="g_2f68600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135734_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="295,-1141 295,-1128 356,-1128 356,-1144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2efaab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="974,-670 974,-681 1021,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2ef9f90@0" ObjectIDZND0="28777@x" Pin0InfoVect0LinkObjId="g_2ef7920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ef9f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="974,-670 974,-681 1021,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eface0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="112,-538 114,-534 164,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2ec14f0@0" ObjectIDZND0="24573@x" ObjectIDZND1="g_2efb1a0@0" Pin0InfoVect0LinkObjId="SW-135860_0" Pin0InfoVect1LinkObjId="g_2efb1a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ec14f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="112,-538 114,-534 164,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2efaf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="164,-534 164,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2ec14f0@0" ObjectIDND1="g_2efb1a0@0" ObjectIDZND0="24573@1" Pin0InfoVect0LinkObjId="SW-135860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ec14f0_0" Pin1InfoVect1LinkObjId="g_2efb1a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="164,-534 164,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2efbb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="164,-534 164,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2ec14f0@0" ObjectIDND1="24573@x" ObjectIDZND0="g_2efb1a0@1" Pin0InfoVect0LinkObjId="g_2efb1a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ec14f0_0" Pin1InfoVect1LinkObjId="SW-135860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="164,-534 164,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2efbdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="164,-578 164,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2efb1a0@0" ObjectIDZND0="g_2f52100@0" Pin0InfoVect0LinkObjId="g_2f52100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2efb1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="164,-578 164,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f1f280">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="-167,-434 -167,-465 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="24558@0" Pin0InfoVect0LinkObjId="g_2fa5ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-167,-434 -167,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f1f470">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="-167,-398 -167,-359 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-167,-398 -167,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f1f660">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="-167,-332 -167,-297 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-167,-332 -167,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f1f890">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="-167,-232 -114,-232 -114,-222 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2ef0e60@0" Pin0InfoVect0LinkObjId="g_2ef0e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-167,-232 -114,-232 -114,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f1fac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="-167,-261 -167,-232 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="0@0" ObjectIDZND0="g_2ef0e60@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2ef0e60_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-167,-261 -167,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f1fcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="-167,-232 -167,-187 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2ef0e60@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ef0e60_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-167,-232 -167,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31f3e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="633,-186 633,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_31f3520@1" Pin0InfoVect0LinkObjId="g_31f3520_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="633,-186 633,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31f5020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1235,-537 1235,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24574@1" ObjectIDZND0="g_31f48d0@1" Pin0InfoVect0LinkObjId="g_31f48d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135861_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1235,-537 1235,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31f5280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1235,-590 1235,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="g_31f48d0@0" ObjectIDZND0="g_2ee25c0@0" ObjectIDZND1="g_2e40290@0" Pin0InfoVect0LinkObjId="g_2ee25c0_0" Pin0InfoVect1LinkObjId="g_2e40290_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31f48d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1235,-590 1235,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31f54e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-265 1359,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="24592@0" ObjectIDZND0="41824@0" Pin0InfoVect0LinkObjId="CB-MD_YD.MD_YD_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135989_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-265 1359,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f22c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-301 1385,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_31f5740@0" Pin0InfoVect0LinkObjId="g_31f5740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1385,-301 1385,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ee40b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="286,-778 286,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2f26ee0@0" Pin0InfoVect0LinkObjId="g_2f26ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="286,-778 286,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ee4310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="286,-814 286,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="286,-814 286,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ee4e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="257,-826 286,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="257,-826 286,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ee5060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="286,-826 316,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="286,-826 316,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb7250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-991 384,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24561@x" ObjectIDND1="24566@x" ObjectIDZND0="24562@0" Pin0InfoVect0LinkObjId="SW-135722_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135720_0" Pin1InfoVect1LinkObjId="SW-135726_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-991 384,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eba020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,-826 373,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="24569@2" ObjectIDZND1="24563@2" ObjectIDZND2="24569@2" Pin0InfoVect0LinkObjId="SW-135735_2" Pin0InfoVect1LinkObjId="SW-135723_2" Pin0InfoVect2LinkObjId="SW-135735_2" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="352,-826 373,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eba4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-800 474,-826 397,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2f23320@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f23320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="474,-800 474,-826 397,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_373afb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-1078 384,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24567@x" ObjectIDND1="g_2e3f7c0@0" ObjectIDND2="g_2f67170@0" ObjectIDZND0="24564@x" ObjectIDZND1="24562@x" Pin0InfoVect0LinkObjId="SW-135724_0" Pin0InfoVect1LinkObjId="SW-135722_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135733_0" Pin1InfoVect1LinkObjId="g_2e3f7c0_0" Pin1InfoVect2LinkObjId="g_2f67170_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="384,-1078 384,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb76b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-1067 384,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24564@x" ObjectIDND1="24567@x" ObjectIDND2="g_2e3f7c0@0" ObjectIDZND0="24562@1" Pin0InfoVect0LinkObjId="SW-135722_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135724_0" Pin1InfoVect1LinkObjId="SW-135733_0" Pin1InfoVect2LinkObjId="g_2e3f7c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-1067 384,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3840e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-767 384,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24569@1" ObjectIDZND0="24557@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135735_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-767 384,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3841ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-826 384,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24557@0" ObjectIDZND0="24563@0" Pin0InfoVect0LinkObjId="SW-135723_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3840e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="384,-826 384,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38e3ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1026,-836 1026,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28765@1" ObjectIDZND0="28762@0" Pin0InfoVect0LinkObjId="g_3876e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188719_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-836 1026,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3876e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="945,-894 1026,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="breaker" ObjectIDND0="28767@0" ObjectIDZND0="28762@0" ObjectIDZND1="28763@x" Pin0InfoVect0LinkObjId="g_38e3ad0_0" Pin0InfoVect1LinkObjId="SW-188717_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188721_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="945,-894 1026,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b7070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1026,-865 1026,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28762@0" ObjectIDZND0="28767@x" ObjectIDZND1="28763@x" Pin0InfoVect0LinkObjId="SW-188721_0" Pin0InfoVect1LinkObjId="SW-188717_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38e3ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-865 1026,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3539ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1026,-894 1025,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="breaker" ObjectIDND0="28767@x" ObjectIDND1="28762@0" ObjectIDZND0="28763@0" Pin0InfoVect0LinkObjId="SW-188717_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188721_0" Pin1InfoVect1LinkObjId="g_38e3ad0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-894 1025,-923 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="MD_YD"/>
</svg>