<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-29" aopId="2884350" id="thSvg" product="E8000V2" version="1.0" viewBox="3110 -1223 2396 1285">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.416609" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="91" y2="82"/>
    <polyline points="10,25 10,39 " stroke-width="1"/>
    <polyline points="10,82 10,66 " stroke-width="1"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="99" y2="90"/>
    <rect height="27" stroke-width="0.416609" width="14" x="3" y="40"/>
    <polyline points="10,82 10,66 " stroke-width="1"/>
    <polyline points="11,25 11,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="9" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="98" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <polyline points="10,82 10,66 " stroke-width="1"/>
    <rect height="27" stroke-width="0.416609" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="91" y2="82"/>
    <polyline points="10,25 10,39 " stroke-width="1"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="98" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="9" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="17" y2="8"/>
    <polyline points="11,25 11,40 " stroke-width="1"/>
    <polyline points="10,82 10,66 " stroke-width="1"/>
    <rect height="27" stroke-width="0.416609" width="14" x="3" y="40"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="0" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="9" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="7" y2="5"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="0" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.267029" x1="17" x2="9" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.349915" x1="20" x2="5" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="13" y1="14" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape81">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="40" y2="27"/>
    <ellipse cx="9" cy="18" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="12" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape12">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="6" x2="6" y1="25" y2="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,8 6,2 11,8 " stroke-width="1.14286"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape40_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape62_0">
    <circle cx="16" cy="58" fillStyle="0" r="16" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,58 41,58 41,29 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="53" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="16,15 22,28 10,28 16,15 16,16 16,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="58" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="58" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="58" y2="53"/>
   </symbol>
   <symbol id="transformer2:shape62_1">
    <circle cx="16" cy="82" fillStyle="0" r="16" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="20" y1="78" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="20" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="12" y1="86" y2="86"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.09677"/>
    <polyline points="58,100 64,100 " stroke-width="1.09677"/>
    <polyline points="64,100 64,93 " stroke-width="1.09677"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="voltageTransformer:shape111">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="62" y2="62"/>
    <circle cx="37" cy="55" fillStyle="0" r="7.5" stroke-width="1"/>
    <circle cx="42" cy="64" fillStyle="0" r="7.5" stroke-width="1"/>
    <circle cx="32" cy="64" fillStyle="0" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="60" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="59" y2="59"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="15" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="37" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="15" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="5" y2="15"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1b07e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b08fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b099d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b0a670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b0b660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b0bf60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b0c6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1b0d1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1752160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1752160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b103d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b103d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b12160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b12160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1b13180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b14d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1b15970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1b16730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b17070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b18730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b19330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b19bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b1a390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b1b470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b1bdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b1c8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1b1d2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1b1e6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1b1f210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1b201f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b20eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b2f0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b27170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b28990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b23260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1295" width="2406" x="3105" y="-1228"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3695,-909 3695,-910 3695,-910 3695,-911 3695,-912 3696,-912 3696,-913 3696,-914 3696,-914 3697,-914 3697,-915 3698,-915 3698,-915 3698,-915 3699,-915 3699,-915 3700,-914 3700,-914 3700,-914 3701,-913 3701,-912 3701,-912 3701,-911 3701,-910 3702,-910 3702,-909 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3702,-909 3702,-910 3702,-910 3702,-911 3703,-912 3703,-912 3703,-913 3703,-914 3704,-914 3704,-914 3705,-915 3705,-915 3705,-915 3706,-915 3706,-915 3707,-915 3707,-914 3707,-914 3708,-914 3708,-913 3708,-912 3709,-912 3709,-911 3709,-910 3709,-910 3709,-909 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3788,-896 3789,-896 3789,-896 3790,-896 3791,-896 3791,-896 3792,-895 3793,-895 3793,-895 3793,-894 3794,-894 3794,-893 3794,-893 3794,-893 3794,-892 3794,-892 3793,-891 3793,-891 3793,-891 3792,-890 3791,-890 3791,-890 3790,-890 3789,-890 3789,-890 3788,-890 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3722,-909 3722,-910 3722,-910 3722,-911 3722,-912 3723,-912 3723,-913 3723,-914 3723,-914 3724,-914 3724,-915 3725,-915 3725,-915 3725,-915 3726,-915 3726,-915 3727,-914 3727,-914 3727,-914 3728,-913 3728,-912 3728,-912 3728,-911 3728,-910 3729,-910 3729,-909 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3729,-909 3729,-910 3729,-910 3729,-911 3730,-912 3730,-912 3730,-913 3730,-914 3731,-914 3731,-914 3732,-915 3732,-915 3732,-915 3733,-915 3733,-915 3734,-915 3734,-914 3734,-914 3735,-914 3735,-913 3735,-912 3736,-912 3736,-911 3736,-910 3736,-910 3736,-909 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3722,-929 3722,-930 3722,-930 3722,-931 3722,-932 3723,-932 3723,-933 3723,-934 3723,-934 3724,-934 3724,-935 3725,-935 3725,-935 3725,-935 3726,-935 3726,-935 3727,-934 3727,-934 3727,-934 3728,-933 3728,-932 3728,-932 3728,-931 3728,-930 3729,-930 3729,-929 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="3729,-929 3729,-930 3729,-930 3729,-931 3730,-932 3730,-932 3730,-933 3730,-934 3731,-934 3731,-934 3732,-935 3732,-935 3732,-935 3733,-935 3733,-935 3734,-935 3734,-934 3734,-934 3735,-934 3735,-933 3735,-932 3736,-932 3736,-931 3736,-930 3736,-930 3736,-929 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="4996,-905 4996,-906 4996,-906 4996,-907 4996,-908 4997,-908 4997,-909 4997,-910 4997,-910 4998,-910 4998,-911 4999,-911 4999,-911 4999,-911 5000,-911 5000,-911 5001,-910 5001,-910 5001,-910 5002,-909 5002,-908 5002,-908 5002,-907 5002,-906 5003,-906 5003,-905 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5003,-905 5003,-906 5003,-906 5003,-907 5004,-908 5004,-908 5004,-909 5004,-910 5005,-910 5005,-910 5006,-911 5006,-911 5006,-911 5007,-911 5007,-911 5008,-911 5008,-910 5008,-910 5009,-910 5009,-909 5009,-908 5010,-908 5010,-907 5010,-906 5010,-906 5010,-905 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5089,-892 5090,-892 5090,-892 5091,-892 5092,-892 5092,-892 5093,-891 5094,-891 5094,-891 5094,-890 5095,-890 5095,-889 5095,-889 5095,-889 5095,-888 5095,-888 5094,-887 5094,-887 5094,-887 5093,-886 5092,-886 5092,-886 5091,-886 5090,-886 5090,-886 5089,-886 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5023,-905 5023,-906 5023,-906 5023,-907 5023,-908 5024,-908 5024,-909 5024,-910 5024,-910 5025,-910 5025,-911 5026,-911 5026,-911 5026,-911 5027,-911 5027,-911 5028,-910 5028,-910 5028,-910 5029,-909 5029,-908 5029,-908 5029,-907 5029,-906 5030,-906 5030,-905 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5030,-905 5030,-906 5030,-906 5030,-907 5031,-908 5031,-908 5031,-909 5031,-910 5032,-910 5032,-910 5033,-911 5033,-911 5033,-911 5034,-911 5034,-911 5035,-911 5035,-910 5035,-910 5036,-910 5036,-909 5036,-908 5037,-908 5037,-907 5037,-906 5037,-906 5037,-905 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5023,-925 5023,-926 5023,-926 5023,-927 5023,-928 5024,-928 5024,-929 5024,-930 5024,-930 5025,-930 5025,-931 5026,-931 5026,-931 5026,-931 5027,-931 5027,-931 5028,-930 5028,-930 5028,-930 5029,-929 5029,-928 5029,-928 5029,-927 5029,-926 5030,-926 5030,-925 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="5030,-925 5030,-926 5030,-926 5030,-927 5031,-928 5031,-928 5031,-929 5031,-930 5032,-930 5032,-930 5033,-931 5033,-931 5033,-931 5034,-931 5034,-931 5035,-931 5035,-930 5035,-930 5036,-930 5036,-929 5036,-928 5037,-928 5037,-927 5037,-926 5037,-926 5037,-925 " stroke="rgb(50,205,50)" stroke-width="0.471449"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3996" x2="4057" y1="-994" y2="-1020"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3996" x2="4059" y1="-983" y2="-952"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3653" x2="3740" y1="-741" y2="-739"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3740" x2="3740" y1="-739" y2="-753"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3740" x2="3740" y1="-789" y2="-805"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3707" x2="3723" y1="-895" y2="-895"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3703" x2="3719" y1="-891" y2="-891"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3719" x2="3711" y1="-891" y2="-899"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3716" x2="3723" y1="-888" y2="-895"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3703" x2="3711" y1="-891" y2="-899"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3716" x2="3708" y1="-888" y2="-896"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3740" x2="3740" y1="-857" y2="-885"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3713" x2="3740" y1="-880" y2="-880"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3713" x2="3713" y1="-880" y2="-891"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3713" x2="3713" y1="-897" y2="-920"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3740" x2="3740" y1="-897" y2="-944"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3713" x2="3740" y1="-920" y2="-920"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3740" x2="3791" y1="-797" y2="-797"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3791" x2="3791" y1="-797" y2="-810"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3791" x2="3791" y1="-847" y2="-860"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3791" x2="3791" y1="-872" y2="-919"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.49157" x1="3748" x2="3748" y1="-805" y2="-849"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.99998" x1="3755" x2="3745" y1="-861" y2="-859"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.28736" x1="3725" x2="3754" y1="-832" y2="-860"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="2" x1="3740" x2="3740" y1="-850" y2="-857"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.99998" x1="3755" x2="3755" y1="-861" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4954" x2="5041" y1="-735" y2="-735"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5041" x2="5041" y1="-735" y2="-749"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5041" x2="5041" y1="-785" y2="-801"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5008" x2="5024" y1="-891" y2="-891"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5004" x2="5020" y1="-887" y2="-887"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5020" x2="5012" y1="-887" y2="-895"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5017" x2="5024" y1="-884" y2="-891"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5004" x2="5012" y1="-887" y2="-895"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5017" x2="5009" y1="-884" y2="-892"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5041" x2="5041" y1="-853" y2="-881"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5014" x2="5041" y1="-876" y2="-876"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5014" x2="5014" y1="-876" y2="-887"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5014" x2="5014" y1="-893" y2="-916"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5041" x2="5041" y1="-893" y2="-940"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5014" x2="5041" y1="-916" y2="-916"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5041" x2="5092" y1="-793" y2="-793"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5092" x2="5092" y1="-793" y2="-806"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5092" x2="5092" y1="-843" y2="-856"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5092" x2="5092" y1="-868" y2="-915"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.49157" x1="5049" x2="5049" y1="-801" y2="-845"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.99998" x1="5056" x2="5046" y1="-857" y2="-855"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.28736" x1="5026" x2="5055" y1="-828" y2="-856"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="2" x1="5041" x2="5041" y1="-846" y2="-853"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1.99998" x1="5056" x2="5056" y1="-857" y2="-848"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="3649" x2="3656" y1="-647" y2="-647"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5501" x2="5500" y1="-524" y2="-524"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5504" x2="5504" y1="-524" y2="-503"/>
  </g><g id="Spline_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-805 3734,-808 3732,-809 3732,-810 3732,-810 3734,-812 3737,-813 3741,-815 3742,-816 3742,-816 3742,-817 3741,-818 3737,-819 3734,-821 3733,-821 3733,-822 3733,-823 3734,-824 3737,-825 3741,-827 3742,-827 3742,-828 3742,-829 3741,-829 3737,-831 3734,-832 3733,-833 3733,-834 3733,-835 3734,-835 3737,-837 3741,-838 3742,-839 3742,-840 3742,-840 3741,-841 3737,-843 3734,-844 3732,-846 3732,-846 3732,-847 3734,-848 3740,-851 " stroke="rgb(60,120,255)" stroke-width="2.00006"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-801 5035,-804 5033,-805 5033,-806 5033,-806 5035,-808 5038,-809 5042,-811 5043,-812 5043,-812 5043,-813 5042,-814 5038,-815 5035,-817 5034,-817 5034,-818 5034,-819 5035,-820 5038,-821 5042,-823 5043,-823 5043,-824 5043,-825 5042,-825 5038,-827 5035,-828 5034,-829 5034,-830 5034,-831 5035,-831 5038,-833 5042,-834 5043,-835 5043,-836 5043,-836 5042,-837 5038,-839 5035,-840 5033,-842 5033,-842 5033,-843 5035,-844 5041,-847 " stroke="rgb(60,120,255)" stroke-width="2.00006"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-35428">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3579.000000 -411.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5616" ObjectName="SW-CX_KFQ.CX_KFQ_4511SW"/>
     <cge:Meas_Ref ObjectId="35428"/>
    <cge:TPSR_Ref TObjectID="5616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35432">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3529.000000 -397.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5620" ObjectName="SW-CX_KFQ.CX_KFQ_7511SW"/>
     <cge:Meas_Ref ObjectId="35432"/>
    <cge:TPSR_Ref TObjectID="5620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35433">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3529.000000 -339.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5621" ObjectName="SW-CX_KFQ.CX_KFQ_7512SW"/>
     <cge:Meas_Ref ObjectId="35433"/>
    <cge:TPSR_Ref TObjectID="5621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35429">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3579.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5617" ObjectName="SW-CX_KFQ.CX_KFQ_4512SW"/>
     <cge:Meas_Ref ObjectId="35429"/>
    <cge:TPSR_Ref TObjectID="5617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3545.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5619" ObjectName="SW-CX_KFQ.CX_KFQ_4514SW"/>
     <cge:Meas_Ref ObjectId="35431"/>
    <cge:TPSR_Ref TObjectID="5619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35430">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3579.000000 -104.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5618" ObjectName="SW-CX_KFQ.CX_KFQ_4513SW"/>
     <cge:Meas_Ref ObjectId="35430"/>
    <cge:TPSR_Ref TObjectID="5618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35449">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -411.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5623" ObjectName="SW-CX_KFQ.CX_KFQ_4521SW"/>
     <cge:Meas_Ref ObjectId="35449"/>
    <cge:TPSR_Ref TObjectID="5623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35453">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3670.000000 -397.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5627" ObjectName="SW-CX_KFQ.CX_KFQ_7521SW"/>
     <cge:Meas_Ref ObjectId="35453"/>
    <cge:TPSR_Ref TObjectID="5627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35454">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3670.000000 -339.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5628" ObjectName="SW-CX_KFQ.CX_KFQ_7522SW"/>
     <cge:Meas_Ref ObjectId="35454"/>
    <cge:TPSR_Ref TObjectID="5628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35450">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5624" ObjectName="SW-CX_KFQ.CX_KFQ_4522SW"/>
     <cge:Meas_Ref ObjectId="35450"/>
    <cge:TPSR_Ref TObjectID="5624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3683.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5626" ObjectName="SW-CX_KFQ.CX_KFQ_4524SW"/>
     <cge:Meas_Ref ObjectId="35452"/>
    <cge:TPSR_Ref TObjectID="5626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5625" ObjectName="SW-CX_KFQ.CX_KFQ_4523SW"/>
     <cge:Meas_Ref ObjectId="35451"/>
    <cge:TPSR_Ref TObjectID="5625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35470">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -411.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5630" ObjectName="SW-CX_KFQ.CX_KFQ_4531SW"/>
     <cge:Meas_Ref ObjectId="35470"/>
    <cge:TPSR_Ref TObjectID="5630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35474">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -397.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5634" ObjectName="SW-CX_KFQ.CX_KFQ_7531SW"/>
     <cge:Meas_Ref ObjectId="35474"/>
    <cge:TPSR_Ref TObjectID="5634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35475">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -339.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5635" ObjectName="SW-CX_KFQ.CX_KFQ_7532SW"/>
     <cge:Meas_Ref ObjectId="35475"/>
    <cge:TPSR_Ref TObjectID="5635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35471">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5631" ObjectName="SW-CX_KFQ.CX_KFQ_4532SW"/>
     <cge:Meas_Ref ObjectId="35471"/>
    <cge:TPSR_Ref TObjectID="5631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35473">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3820.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5633" ObjectName="SW-CX_KFQ.CX_KFQ_4534SW"/>
     <cge:Meas_Ref ObjectId="35473"/>
    <cge:TPSR_Ref TObjectID="5633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -105.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5632" ObjectName="SW-CX_KFQ.CX_KFQ_4533SW"/>
     <cge:Meas_Ref ObjectId="35472"/>
    <cge:TPSR_Ref TObjectID="5632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 -411.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5637" ObjectName="SW-CX_KFQ.CX_KFQ_4541SW"/>
     <cge:Meas_Ref ObjectId="35491"/>
    <cge:TPSR_Ref TObjectID="5637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35495">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.000000 -397.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5641" ObjectName="SW-CX_KFQ.CX_KFQ_7541SW"/>
     <cge:Meas_Ref ObjectId="35495"/>
    <cge:TPSR_Ref TObjectID="5641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35496">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.000000 -339.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5642" ObjectName="SW-CX_KFQ.CX_KFQ_7542SW"/>
     <cge:Meas_Ref ObjectId="35496"/>
    <cge:TPSR_Ref TObjectID="5642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35492">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5638" ObjectName="SW-CX_KFQ.CX_KFQ_4542SW"/>
     <cge:Meas_Ref ObjectId="35492"/>
    <cge:TPSR_Ref TObjectID="5638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35494">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5640" ObjectName="SW-CX_KFQ.CX_KFQ_4544SW"/>
     <cge:Meas_Ref ObjectId="35494"/>
    <cge:TPSR_Ref TObjectID="5640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35493">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 -101.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5639" ObjectName="SW-CX_KFQ.CX_KFQ_4543SW"/>
     <cge:Meas_Ref ObjectId="35493"/>
    <cge:TPSR_Ref TObjectID="5639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35512">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -411.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5644" ObjectName="SW-CX_KFQ.CX_KFQ_4561SW"/>
     <cge:Meas_Ref ObjectId="35512"/>
    <cge:TPSR_Ref TObjectID="5644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35516">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4070.000000 -397.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5648" ObjectName="SW-CX_KFQ.CX_KFQ_7561SW"/>
     <cge:Meas_Ref ObjectId="35516"/>
    <cge:TPSR_Ref TObjectID="5648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35517">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4070.000000 -339.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5649" ObjectName="SW-CX_KFQ.CX_KFQ_7562SW"/>
     <cge:Meas_Ref ObjectId="35517"/>
    <cge:TPSR_Ref TObjectID="5649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35513">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5645" ObjectName="SW-CX_KFQ.CX_KFQ_4562SW"/>
     <cge:Meas_Ref ObjectId="35513"/>
    <cge:TPSR_Ref TObjectID="5645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35515">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4083.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5647" ObjectName="SW-CX_KFQ.CX_KFQ_4564SW"/>
     <cge:Meas_Ref ObjectId="35515"/>
    <cge:TPSR_Ref TObjectID="5647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35533">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4248.000000 -411.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5651" ObjectName="SW-CX_KFQ.CX_KFQ_4571SW"/>
     <cge:Meas_Ref ObjectId="35533"/>
    <cge:TPSR_Ref TObjectID="5651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35537">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -397.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5655" ObjectName="SW-CX_KFQ.CX_KFQ_7571SW"/>
     <cge:Meas_Ref ObjectId="35537"/>
    <cge:TPSR_Ref TObjectID="5655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35538">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -339.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5656" ObjectName="SW-CX_KFQ.CX_KFQ_7572SW"/>
     <cge:Meas_Ref ObjectId="35538"/>
    <cge:TPSR_Ref TObjectID="5656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35534">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4248.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5652" ObjectName="SW-CX_KFQ.CX_KFQ_4572SW"/>
     <cge:Meas_Ref ObjectId="35534"/>
    <cge:TPSR_Ref TObjectID="5652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35536">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5654" ObjectName="SW-CX_KFQ.CX_KFQ_4574SW"/>
     <cge:Meas_Ref ObjectId="35536"/>
    <cge:TPSR_Ref TObjectID="5654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35362">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3926.000000 -516.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5607" ObjectName="SW-CX_KFQ.CX_KFQ_4601SW"/>
     <cge:Meas_Ref ObjectId="35362"/>
    <cge:TPSR_Ref TObjectID="5607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35363">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3874.000000 -570.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5608" ObjectName="SW-CX_KFQ.CX_KFQ_7601SW"/>
     <cge:Meas_Ref ObjectId="35363"/>
    <cge:TPSR_Ref TObjectID="5608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35338">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4287.000000 -568.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5603" ObjectName="SW-CX_KFQ.CX_KFQ_4901SW"/>
     <cge:Meas_Ref ObjectId="35338"/>
    <cge:TPSR_Ref TObjectID="5603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35339">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.000000 -568.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5604" ObjectName="SW-CX_KFQ.CX_KFQ_4902SW"/>
     <cge:Meas_Ref ObjectId="35339"/>
    <cge:TPSR_Ref TObjectID="5604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35340">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -516.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5605" ObjectName="SW-CX_KFQ.CX_KFQ_7901SW"/>
     <cge:Meas_Ref ObjectId="35340"/>
    <cge:TPSR_Ref TObjectID="5605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35341">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -515.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5606" ObjectName="SW-CX_KFQ.CX_KFQ_7902SW"/>
     <cge:Meas_Ref ObjectId="35341"/>
    <cge:TPSR_Ref TObjectID="5606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35317">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5158.000000 -409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5596" ObjectName="SW-CX_KFQ.CX_KFQ_4961SW"/>
     <cge:Meas_Ref ObjectId="35317"/>
    <cge:TPSR_Ref TObjectID="5596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35321">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5108.000000 -395.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5600" ObjectName="SW-CX_KFQ.CX_KFQ_7961SW"/>
     <cge:Meas_Ref ObjectId="35321"/>
    <cge:TPSR_Ref TObjectID="5600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35322">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5108.000000 -337.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5601" ObjectName="SW-CX_KFQ.CX_KFQ_7962SW"/>
     <cge:Meas_Ref ObjectId="35322"/>
    <cge:TPSR_Ref TObjectID="5601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35318">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5158.000000 -282.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5597" ObjectName="SW-CX_KFQ.CX_KFQ_4962SW"/>
     <cge:Meas_Ref ObjectId="35318"/>
    <cge:TPSR_Ref TObjectID="5597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35320">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5121.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5599" ObjectName="SW-CX_KFQ.CX_KFQ_4964SW"/>
     <cge:Meas_Ref ObjectId="35320"/>
    <cge:TPSR_Ref TObjectID="5599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35277">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5030.000000 -409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5584" ObjectName="SW-CX_KFQ.CX_KFQ_4941SW"/>
     <cge:Meas_Ref ObjectId="35277"/>
    <cge:TPSR_Ref TObjectID="5584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35281">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4980.000000 -395.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5588" ObjectName="SW-CX_KFQ.CX_KFQ_7941SW"/>
     <cge:Meas_Ref ObjectId="35281"/>
    <cge:TPSR_Ref TObjectID="5588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35282">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4980.000000 -337.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5589" ObjectName="SW-CX_KFQ.CX_KFQ_7942SW"/>
     <cge:Meas_Ref ObjectId="35282"/>
    <cge:TPSR_Ref TObjectID="5589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35278">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5030.000000 -282.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5585" ObjectName="SW-CX_KFQ.CX_KFQ_4942SW"/>
     <cge:Meas_Ref ObjectId="35278"/>
    <cge:TPSR_Ref TObjectID="5585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35280">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4993.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5587" ObjectName="SW-CX_KFQ.CX_KFQ_4944SW"/>
     <cge:Meas_Ref ObjectId="35280"/>
    <cge:TPSR_Ref TObjectID="5587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35257">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4900.000000 -409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5578" ObjectName="SW-CX_KFQ.CX_KFQ_4931SW"/>
     <cge:Meas_Ref ObjectId="35257"/>
    <cge:TPSR_Ref TObjectID="5578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35260">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4850.000000 -395.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5581" ObjectName="SW-CX_KFQ.CX_KFQ_7931SW"/>
     <cge:Meas_Ref ObjectId="35260"/>
    <cge:TPSR_Ref TObjectID="5581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35261">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4850.000000 -337.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5582" ObjectName="SW-CX_KFQ.CX_KFQ_7932SW"/>
     <cge:Meas_Ref ObjectId="35261"/>
    <cge:TPSR_Ref TObjectID="5582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35258">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4900.000000 -282.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5579" ObjectName="SW-CX_KFQ.CX_KFQ_4932SW"/>
     <cge:Meas_Ref ObjectId="35258"/>
    <cge:TPSR_Ref TObjectID="5579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35259">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5580" ObjectName="SW-CX_KFQ.CX_KFQ_4934SW"/>
     <cge:Meas_Ref ObjectId="35259"/>
    <cge:TPSR_Ref TObjectID="5580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35236">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5571" ObjectName="SW-CX_KFQ.CX_KFQ_4921SW"/>
     <cge:Meas_Ref ObjectId="35236"/>
    <cge:TPSR_Ref TObjectID="5571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35240">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4717.000000 -395.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5575" ObjectName="SW-CX_KFQ.CX_KFQ_7921SW"/>
     <cge:Meas_Ref ObjectId="35240"/>
    <cge:TPSR_Ref TObjectID="5575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35241">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4717.000000 -337.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5576" ObjectName="SW-CX_KFQ.CX_KFQ_7922SW"/>
     <cge:Meas_Ref ObjectId="35241"/>
    <cge:TPSR_Ref TObjectID="5576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35237">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -282.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5572" ObjectName="SW-CX_KFQ.CX_KFQ_4922SW"/>
     <cge:Meas_Ref ObjectId="35237"/>
    <cge:TPSR_Ref TObjectID="5572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35239">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4730.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5574" ObjectName="SW-CX_KFQ.CX_KFQ_4924SW"/>
     <cge:Meas_Ref ObjectId="35239"/>
    <cge:TPSR_Ref TObjectID="5574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35238">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -92.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5573" ObjectName="SW-CX_KFQ.CX_KFQ_4923SW"/>
     <cge:Meas_Ref ObjectId="35238"/>
    <cge:TPSR_Ref TObjectID="5573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35215">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 -409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5564" ObjectName="SW-CX_KFQ.CX_KFQ_4911SW"/>
     <cge:Meas_Ref ObjectId="35215"/>
    <cge:TPSR_Ref TObjectID="5564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35219">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 -395.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5568" ObjectName="SW-CX_KFQ.CX_KFQ_7911SW"/>
     <cge:Meas_Ref ObjectId="35219"/>
    <cge:TPSR_Ref TObjectID="5568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35220">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 -337.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5569" ObjectName="SW-CX_KFQ.CX_KFQ_7912SW"/>
     <cge:Meas_Ref ObjectId="35220"/>
    <cge:TPSR_Ref TObjectID="5569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35216">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 -282.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5565" ObjectName="SW-CX_KFQ.CX_KFQ_4912SW"/>
     <cge:Meas_Ref ObjectId="35216"/>
    <cge:TPSR_Ref TObjectID="5565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35218">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5567" ObjectName="SW-CX_KFQ.CX_KFQ_4914SW"/>
     <cge:Meas_Ref ObjectId="35218"/>
    <cge:TPSR_Ref TObjectID="5567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35217">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 -106.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5566" ObjectName="SW-CX_KFQ.CX_KFQ_4913SW"/>
     <cge:Meas_Ref ObjectId="35217"/>
    <cge:TPSR_Ref TObjectID="5566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35298">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.000000 -409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5591" ObjectName="SW-CX_KFQ.CX_KFQ_4951SW"/>
     <cge:Meas_Ref ObjectId="35298"/>
    <cge:TPSR_Ref TObjectID="5591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35300">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4447.000000 -395.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5593" ObjectName="SW-CX_KFQ.CX_KFQ_7951SW"/>
     <cge:Meas_Ref ObjectId="35300"/>
    <cge:TPSR_Ref TObjectID="5593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4447.000000 -337.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5594" ObjectName="SW-CX_KFQ.CX_KFQ_7952SW"/>
     <cge:Meas_Ref ObjectId="35301"/>
    <cge:TPSR_Ref TObjectID="5594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35299">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.000000 -282.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5592" ObjectName="SW-CX_KFQ.CX_KFQ_4952SW"/>
     <cge:Meas_Ref ObjectId="35299"/>
    <cge:TPSR_Ref TObjectID="5592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35556">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4092.000000 -1135.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5658" ObjectName="SW-CX_KFQ.CX_KFQ_35167SW"/>
     <cge:Meas_Ref ObjectId="35556"/>
    <cge:TPSR_Ref TObjectID="5658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -841.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11730" ObjectName="SW-CX_KFQ.CX_KFQ_TVGLSW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35582">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4442.000000 -809.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5661" ObjectName="SW-CX_KFQ.CX_KFQ_3121SW"/>
     <cge:Meas_Ref ObjectId="35582"/>
    <cge:TPSR_Ref TObjectID="5661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4618.000000 -1136.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5660" ObjectName="SW-CX_KFQ.CX_KFQ_35267SW"/>
     <cge:Meas_Ref ObjectId="35579"/>
    <cge:TPSR_Ref TObjectID="5660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.000000 -517.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5561" ObjectName="SW-CX_KFQ.CX_KFQ_4222SW"/>
     <cge:Meas_Ref ObjectId="35192"/>
    <cge:TPSR_Ref TObjectID="5561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35193">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 -569.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5562" ObjectName="SW-CX_KFQ.CX_KFQ_7222SW"/>
     <cge:Meas_Ref ObjectId="35193"/>
    <cge:TPSR_Ref TObjectID="5562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35405">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 -516.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5613" ObjectName="SW-CX_KFQ.CX_KFQ_4212SW"/>
     <cge:Meas_Ref ObjectId="35405"/>
    <cge:TPSR_Ref TObjectID="5613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35406">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4119.000000 -568.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5614" ObjectName="SW-CX_KFQ.CX_KFQ_7212SW"/>
     <cge:Meas_Ref ObjectId="35406"/>
    <cge:TPSR_Ref TObjectID="5614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35364">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5125.000000 -517.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5609" ObjectName="SW-CX_KFQ.CX_KFQ_4602SW"/>
     <cge:Meas_Ref ObjectId="35364"/>
    <cge:TPSR_Ref TObjectID="5609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35365">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5073.000000 -571.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5610" ObjectName="SW-CX_KFQ.CX_KFQ_7602SW"/>
     <cge:Meas_Ref ObjectId="35365"/>
    <cge:TPSR_Ref TObjectID="5610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35555">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 -1012.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10572" ObjectName="SW-CX_KFQ.CX_KFQ_351XC"/>
     <cge:Meas_Ref ObjectId="35555"/>
    <cge:TPSR_Ref TObjectID="10572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35555">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 -932.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18162" ObjectName="SW-CX_KFQ.CX_KFQ_351XC1"/>
     <cge:Meas_Ref ObjectId="35555"/>
    <cge:TPSR_Ref TObjectID="18162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 -1013.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10573" ObjectName="SW-CX_KFQ.CX_KFQ_352XC"/>
     <cge:Meas_Ref ObjectId="35578"/>
    <cge:TPSR_Ref TObjectID="10573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 -933.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18161" ObjectName="SW-CX_KFQ.CX_KFQ_352XC1"/>
     <cge:Meas_Ref ObjectId="35578"/>
    <cge:TPSR_Ref TObjectID="18161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213670">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.000000 -504.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31760" ObjectName="SW-CX_KFQ.CX_KFQ_0581SW"/>
     <cge:Meas_Ref ObjectId="213670"/>
    <cge:TPSR_Ref TObjectID="31760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213671">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.000000 -599.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31761" ObjectName="SW-CX_KFQ.CX_KFQ_0586SW"/>
     <cge:Meas_Ref ObjectId="213671"/>
    <cge:TPSR_Ref TObjectID="31761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213672">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3599.000000 -642.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31762" ObjectName="SW-CX_KFQ.CX_KFQ_05867SW"/>
     <cge:Meas_Ref ObjectId="213672"/>
    <cge:TPSR_Ref TObjectID="31762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213677">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3731.000000 -749.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31768" ObjectName="SW-CX_KFQ.CX_KFQ_0010SW"/>
     <cge:Meas_Ref ObjectId="213677"/>
    <cge:TPSR_Ref TObjectID="31768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3782.000000 -806.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.000000 -595.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31766" ObjectName="SW-CX_KFQ.CX_KFQ_0976SW"/>
     <cge:Meas_Ref ObjectId="213675"/>
    <cge:TPSR_Ref TObjectID="31766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213676">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4900.000000 -638.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31767" ObjectName="SW-CX_KFQ.CX_KFQ_09767SW"/>
     <cge:Meas_Ref ObjectId="213676"/>
    <cge:TPSR_Ref TObjectID="31767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.000000 -745.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31769" ObjectName="SW-CX_KFQ.CX_KFQ_0020SW"/>
     <cge:Meas_Ref ObjectId="213678"/>
    <cge:TPSR_Ref TObjectID="31769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5083.000000 -802.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213674">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.000000 -500.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31765" ObjectName="SW-CX_KFQ.CX_KFQ_0972SW"/>
     <cge:Meas_Ref ObjectId="213674"/>
    <cge:TPSR_Ref TObjectID="31765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35171">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.375000 -1.359578 0.000000 4729.136364 -766.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5559" ObjectName="SW-CX_KFQ.CX_KFQ_3712SW"/>
     <cge:Meas_Ref ObjectId="35171"/>
    <cge:TPSR_Ref TObjectID="5559"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_KFQ.CX_KFQ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3524,-471 4333,-471 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5551" ObjectName="BS-CX_KFQ.CX_KFQ_9IM"/>
    <cge:TPSR_Ref TObjectID="5551"/></metadata>
   <polyline fill="none" opacity="0" points="3524,-471 4333,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_KFQ.CX_KFQ_9PM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3518,-218 5192,-218 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10437" ObjectName="BS-CX_KFQ.CX_KFQ_9PM"/>
    <cge:TPSR_Ref TObjectID="10437"/></metadata>
   <polyline fill="none" opacity="0" points="3518,-218 5192,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_KFQ.CX_KFQ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4459,-471 5196,-471 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5552" ObjectName="BS-CX_KFQ.CX_KFQ_9IIM"/>
    <cge:TPSR_Ref TObjectID="5552"/></metadata>
   <polyline fill="none" opacity="0" points="4459,-471 5196,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_KFQ.CX_KFQ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-895 4340,-895 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5550" ObjectName="BS-CX_KFQ.CX_KFQ_3IM"/>
    <cge:TPSR_Ref TObjectID="5550"/></metadata>
   <polyline fill="none" opacity="0" points="4007,-895 4340,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_KFQ.CX_KFQ_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4423,-895 4740,-895 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10436" ObjectName="BS-CX_KFQ.CX_KFQ_3IIM"/>
    <cge:TPSR_Ref TObjectID="10436"/></metadata>
   <polyline fill="none" opacity="0" points="4423,-895 4740,-895 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_KFQ.451Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3582.000000 -13.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34380" ObjectName="EC-CX_KFQ.451Ld"/>
    <cge:TPSR_Ref TObjectID="34380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_KFQ.452Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3723.000000 -16.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34381" ObjectName="EC-CX_KFQ.452Ld"/>
    <cge:TPSR_Ref TObjectID="34381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_KFQ.456Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 -11.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34384" ObjectName="EC-CX_KFQ.456Ld"/>
    <cge:TPSR_Ref TObjectID="34384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_KFQ.453Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3860.000000 -16.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34382" ObjectName="EC-CX_KFQ.453Ld"/>
    <cge:TPSR_Ref TObjectID="34382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_KFQ.454Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.000000 -17.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34383" ObjectName="EC-CX_KFQ.454Ld"/>
    <cge:TPSR_Ref TObjectID="34383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_KFQ.491Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4633.000000 -11.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34385" ObjectName="EC-CX_KFQ.491Ld"/>
    <cge:TPSR_Ref TObjectID="34385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_KFQ.492Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4770.000000 -15.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34387" ObjectName="EC-CX_KFQ.492Ld"/>
    <cge:TPSR_Ref TObjectID="34387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_KFQ.493Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4903.000000 -14.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34388" ObjectName="EC-CX_KFQ.493Ld"/>
    <cge:TPSR_Ref TObjectID="34388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_KFQ.494Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5033.000000 -15.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34389" ObjectName="EC-CX_KFQ.494Ld"/>
    <cge:TPSR_Ref TObjectID="34389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_KFQ.496Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5161.000000 -15.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34386" ObjectName="EC-CX_KFQ.496Ld"/>
    <cge:TPSR_Ref TObjectID="34386"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_11b8800" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3511.000000 -375.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11f5bd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3511.000000 -317.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11760c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 -375.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10d0300" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 -317.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11ddcd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3789.000000 -375.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11e0090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3789.000000 -317.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11b15e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -375.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12046a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -317.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11e7790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 -375.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11837c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 -317.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1186660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4180.000000 -375.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_114db60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4180.000000 -317.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_113ad20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3847.000000 -569.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11ba5d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4347.000000 -489.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11bdb20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4422.000000 -488.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11420e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5090.000000 -373.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1178870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5090.000000 -315.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1125110" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4962.000000 -373.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_116c460" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4962.000000 -315.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11042d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 -373.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1107710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 -315.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10f8bb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4699.000000 -373.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10fbff0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4699.000000 -315.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10a3e00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.000000 -373.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10a7240" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.000000 -315.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11a5150" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4429.000000 -373.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10961e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4429.000000 -315.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_109d050" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4141.000000 -1134.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10eeb90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4667.000000 -1135.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_108dac0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4721.000000 -568.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10b1780" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4168.000000 -567.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10b8300" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -570.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13a8840" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.000000 -939.000000)" xlink:href="#earth:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13ac210" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3778.000000 -914.000000)" xlink:href="#earth:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13bfb90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5028.000000 -935.000000)" xlink:href="#earth:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c3560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5079.000000 -910.000000)" xlink:href="#earth:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_1155f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-471 3588,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5551@0" ObjectIDZND0="5616@1" Pin0InfoVect0LinkObjId="SW-35428_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1093b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-471 3588,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_120e200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-416 3588,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5616@0" ObjectIDZND0="5615@x" ObjectIDZND1="5620@x" Pin0InfoVect0LinkObjId="SW-35427_0" Pin0InfoVect1LinkObjId="SW-35432_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-416 3588,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_120e3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-402 3588,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5616@x" ObjectIDND1="5620@x" ObjectIDZND0="5615@1" Pin0InfoVect0LinkObjId="SW-35427_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35428_0" Pin1InfoVect1LinkObjId="SW-35432_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-402 3588,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11b8420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-402 3570,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5615@x" ObjectIDND1="5616@x" ObjectIDZND0="5620@1" Pin0InfoVect0LinkObjId="SW-35432_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35427_0" Pin1InfoVect1LinkObjId="SW-35428_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-402 3570,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11b8610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-392 3517,-402 3534,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11b8800@0" ObjectIDZND0="5620@0" Pin0InfoVect0LinkObjId="SW-35432_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b8800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3517,-392 3517,-402 3534,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f57f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-344 3570,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5615@x" ObjectIDND1="5617@x" ObjectIDZND0="5621@1" Pin0InfoVect0LinkObjId="SW-35433_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35427_0" Pin1InfoVect1LinkObjId="SW-35429_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-344 3570,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f59e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-334 3517,-344 3534,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11f5bd0@0" ObjectIDZND0="5621@0" Pin0InfoVect0LinkObjId="SW-35433_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11f5bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3517,-334 3517,-344 3534,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f6200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-360 3588,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5615@0" ObjectIDZND0="5621@x" ObjectIDZND1="5617@x" Pin0InfoVect0LinkObjId="SW-35433_0" Pin0InfoVect1LinkObjId="SW-35429_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-360 3588,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f2330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-344 3588,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5615@x" ObjectIDND1="5621@x" ObjectIDZND0="5617@1" Pin0InfoVect0LinkObjId="SW-35429_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35427_0" Pin1InfoVect1LinkObjId="SW-35433_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-344 3588,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121ec50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-471 3729,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5551@0" ObjectIDZND0="5623@1" Pin0InfoVect0LinkObjId="SW-35449_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1093b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-471 3729,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1174250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-416 3729,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5623@0" ObjectIDZND0="5622@x" ObjectIDZND1="5627@x" Pin0InfoVect0LinkObjId="SW-35448_0" Pin0InfoVect1LinkObjId="SW-35453_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35449_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-416 3729,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1174440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-402 3729,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5623@x" ObjectIDND1="5627@x" ObjectIDZND0="5622@1" Pin0InfoVect0LinkObjId="SW-35448_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35449_0" Pin1InfoVect1LinkObjId="SW-35453_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-402 3729,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1175ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-402 3711,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5622@x" ObjectIDND1="5623@x" ObjectIDZND0="5627@1" Pin0InfoVect0LinkObjId="SW-35453_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35448_0" Pin1InfoVect1LinkObjId="SW-35449_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-402 3711,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1175ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3658,-392 3658,-402 3675,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11760c0@0" ObjectIDZND0="5627@0" Pin0InfoVect0LinkObjId="SW-35453_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11760c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3658,-392 3658,-402 3675,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10cff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-344 3711,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5622@x" ObjectIDND1="5624@x" ObjectIDZND0="5628@1" Pin0InfoVect0LinkObjId="SW-35454_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35448_0" Pin1InfoVect1LinkObjId="SW-35450_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-344 3711,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10d0110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3658,-334 3658,-344 3675,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_10d0300@0" ObjectIDZND0="5628@0" Pin0InfoVect0LinkObjId="SW-35454_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10d0300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3658,-334 3658,-344 3675,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10d0930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-360 3729,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5622@0" ObjectIDZND0="5628@x" ObjectIDZND1="5624@x" Pin0InfoVect0LinkObjId="SW-35454_0" Pin0InfoVect1LinkObjId="SW-35450_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35448_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-360 3729,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10d22b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-344 3729,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5622@x" ObjectIDND1="5628@x" ObjectIDZND0="5624@1" Pin0InfoVect0LinkObjId="SW-35450_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35448_0" Pin1InfoVect1LinkObjId="SW-35454_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-344 3729,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11cf5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-471 3866,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5551@0" ObjectIDZND0="5630@1" Pin0InfoVect0LinkObjId="SW-35470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1093b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-471 3866,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11d0ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-416 3866,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5630@0" ObjectIDZND0="5629@x" ObjectIDZND1="5634@x" Pin0InfoVect0LinkObjId="SW-35469_0" Pin0InfoVect1LinkObjId="SW-35474_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-416 3866,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11d10c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-402 3866,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5630@x" ObjectIDND1="5634@x" ObjectIDZND0="5629@1" Pin0InfoVect0LinkObjId="SW-35469_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35470_0" Pin1InfoVect1LinkObjId="SW-35474_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-402 3866,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11dd8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-402 3848,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5629@x" ObjectIDND1="5630@x" ObjectIDZND0="5634@1" Pin0InfoVect0LinkObjId="SW-35474_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35469_0" Pin1InfoVect1LinkObjId="SW-35470_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-402 3848,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11ddae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3795,-392 3795,-402 3812,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11ddcd0@0" ObjectIDZND0="5634@0" Pin0InfoVect0LinkObjId="SW-35474_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11ddcd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3795,-392 3795,-402 3812,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11dfcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-344 3848,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5629@x" ObjectIDND1="5631@x" ObjectIDZND0="5635@1" Pin0InfoVect0LinkObjId="SW-35475_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35469_0" Pin1InfoVect1LinkObjId="SW-35471_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-344 3848,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11dfea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3795,-334 3795,-344 3812,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11e0090@0" ObjectIDZND0="5635@0" Pin0InfoVect0LinkObjId="SW-35475_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11e0090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3795,-334 3795,-344 3812,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e0800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-360 3866,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5629@0" ObjectIDZND0="5635@x" ObjectIDZND1="5631@x" Pin0InfoVect0LinkObjId="SW-35475_0" Pin0InfoVect1LinkObjId="SW-35471_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35469_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-360 3866,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_111c120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-344 3866,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5629@x" ObjectIDND1="5635@x" ObjectIDZND0="5631@1" Pin0InfoVect0LinkObjId="SW-35471_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35469_0" Pin1InfoVect1LinkObjId="SW-35475_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-344 3866,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11db9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-471 3999,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5551@0" ObjectIDZND0="5637@1" Pin0InfoVect0LinkObjId="SW-35491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1093b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-471 3999,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11aee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-416 3999,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5637@0" ObjectIDZND0="5636@x" ObjectIDZND1="5641@x" Pin0InfoVect0LinkObjId="SW-35490_0" Pin0InfoVect1LinkObjId="SW-35495_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-416 3999,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11aeff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-402 3999,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5637@x" ObjectIDND1="5641@x" ObjectIDZND0="5636@1" Pin0InfoVect0LinkObjId="SW-35490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35491_0" Pin1InfoVect1LinkObjId="SW-35495_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-402 3999,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11b11a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-402 3981,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5636@x" ObjectIDND1="5637@x" ObjectIDZND0="5641@1" Pin0InfoVect0LinkObjId="SW-35495_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35490_0" Pin1InfoVect1LinkObjId="SW-35491_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-402 3981,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11b13c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-392 3928,-402 3945,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11b15e0@0" ObjectIDZND0="5641@0" Pin0InfoVect0LinkObjId="SW-35495_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b15e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-392 3928,-402 3945,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1204260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-344 3981,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5636@x" ObjectIDND1="5638@x" ObjectIDZND0="5642@1" Pin0InfoVect0LinkObjId="SW-35496_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35490_0" Pin1InfoVect1LinkObjId="SW-35492_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-344 3981,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1204480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-334 3928,-344 3945,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_12046a0@0" ObjectIDZND0="5642@0" Pin0InfoVect0LinkObjId="SW-35496_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12046a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-334 3928,-344 3945,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1204f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-360 3999,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5636@0" ObjectIDZND0="5642@x" ObjectIDZND1="5638@x" Pin0InfoVect0LinkObjId="SW-35496_0" Pin0InfoVect1LinkObjId="SW-35492_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-360 3999,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12073d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-344 3999,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5636@x" ObjectIDND1="5642@x" ObjectIDZND0="5638@1" Pin0InfoVect0LinkObjId="SW-35492_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35490_0" Pin1InfoVect1LinkObjId="SW-35496_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-344 3999,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e3a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-471 4129,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5551@0" ObjectIDZND0="5644@1" Pin0InfoVect0LinkObjId="SW-35512_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1093b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-471 4129,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e5ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-416 4129,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5644@0" ObjectIDZND0="5643@x" ObjectIDZND1="5648@x" Pin0InfoVect0LinkObjId="SW-35511_0" Pin0InfoVect1LinkObjId="SW-35516_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-416 4129,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e5ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-402 4129,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5644@x" ObjectIDND1="5648@x" ObjectIDZND0="5643@1" Pin0InfoVect0LinkObjId="SW-35511_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35512_0" Pin1InfoVect1LinkObjId="SW-35516_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-402 4129,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e7320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-402 4111,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5643@x" ObjectIDND1="5644@x" ObjectIDZND0="5648@1" Pin0InfoVect0LinkObjId="SW-35516_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35511_0" Pin1InfoVect1LinkObjId="SW-35512_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-402 4111,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e7530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4058,-392 4058,-402 4075,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11e7790@0" ObjectIDZND0="5648@0" Pin0InfoVect0LinkObjId="SW-35516_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11e7790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4058,-392 4058,-402 4075,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1183310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-344 4111,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5643@x" ObjectIDND1="5645@x" ObjectIDZND0="5649@1" Pin0InfoVect0LinkObjId="SW-35517_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35511_0" Pin1InfoVect1LinkObjId="SW-35513_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-344 4111,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1183590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4058,-334 4058,-344 4075,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11837c0@0" ObjectIDZND0="5649@0" Pin0InfoVect0LinkObjId="SW-35517_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11837c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4058,-334 4058,-344 4075,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11841f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-360 4129,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5643@0" ObjectIDZND0="5649@x" ObjectIDZND1="5645@x" Pin0InfoVect0LinkObjId="SW-35517_0" Pin0InfoVect1LinkObjId="SW-35513_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35511_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-360 4129,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1184870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-344 4129,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5643@x" ObjectIDND1="5649@x" ObjectIDZND0="5645@1" Pin0InfoVect0LinkObjId="SW-35513_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35511_0" Pin1InfoVect1LinkObjId="SW-35517_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-344 4129,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11852b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-471 4257,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5551@0" ObjectIDZND0="5651@1" Pin0InfoVect0LinkObjId="SW-35533_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1093b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-471 4257,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1185920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-416 4257,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5651@0" ObjectIDZND0="5650@x" ObjectIDZND1="5655@x" Pin0InfoVect0LinkObjId="SW-35532_0" Pin0InfoVect1LinkObjId="SW-35537_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35533_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-416 4257,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1185b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-402 4257,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5651@x" ObjectIDND1="5655@x" ObjectIDZND0="5650@1" Pin0InfoVect0LinkObjId="SW-35532_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35533_0" Pin1InfoVect1LinkObjId="SW-35537_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-402 4257,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11861d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-402 4239,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5650@x" ObjectIDND1="5651@x" ObjectIDZND0="5655@1" Pin0InfoVect0LinkObjId="SW-35537_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35532_0" Pin1InfoVect1LinkObjId="SW-35533_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-402 4239,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1186400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-392 4186,-402 4203,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1186660@0" ObjectIDZND0="5655@0" Pin0InfoVect0LinkObjId="SW-35537_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1186660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-392 4186,-402 4203,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_114d6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-344 4239,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5650@x" ObjectIDND1="5652@x" ObjectIDZND0="5656@1" Pin0InfoVect0LinkObjId="SW-35538_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35532_0" Pin1InfoVect1LinkObjId="SW-35534_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-344 4239,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_114d900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-334 4186,-344 4203,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_114db60@0" ObjectIDZND0="5656@0" Pin0InfoVect0LinkObjId="SW-35538_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_114db60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-334 4186,-344 4203,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_114e590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-360 4257,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5650@0" ObjectIDZND0="5656@x" ObjectIDZND1="5652@x" Pin0InfoVect0LinkObjId="SW-35538_0" Pin0InfoVect1LinkObjId="SW-35534_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35532_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-360 4257,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_114ec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-344 4257,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5650@x" ObjectIDND1="5656@x" ObjectIDZND0="5652@1" Pin0InfoVect0LinkObjId="SW-35534_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35532_0" Pin1InfoVect1LinkObjId="SW-35538_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-344 4257,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_114f6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-471 3935,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5551@0" ObjectIDZND0="5607@0" Pin0InfoVect0LinkObjId="SW-35362_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1093b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-471 3935,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11514d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-557 3935,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="5607@1" ObjectIDZND0="5608@x" ObjectIDZND1="g_13e44e0@0" Pin0InfoVect0LinkObjId="SW-35363_0" Pin0InfoVect1LinkObjId="g_13e44e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35362_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-557 3935,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1151730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-575 3935,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="5607@x" ObjectIDND1="5608@x" ObjectIDZND0="g_13e44e0@0" Pin0InfoVect0LinkObjId="g_13e44e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35362_0" Pin1InfoVect1LinkObjId="SW-35363_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-575 3935,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_113a860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-575 3915,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="5607@x" ObjectIDND1="g_13e44e0@0" ObjectIDZND0="5608@1" Pin0InfoVect0LinkObjId="SW-35363_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35362_0" Pin1InfoVect1LinkObjId="g_13e44e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-575 3915,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_113aac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-575 3865,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5608@0" ObjectIDZND0="g_113ad20@0" Pin0InfoVect0LinkObjId="g_113ad20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35363_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-575 3865,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_113d920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4276,-471 4276,-573 4292,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5551@0" ObjectIDZND0="5603@0" Pin0InfoVect0LinkObjId="SW-35338_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1093b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4276,-471 4276,-573 4292,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11c63a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4485,-573 4498,-573 4498,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5604@1" ObjectIDZND0="5552@0" Pin0InfoVect0LinkObjId="g_108b330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35339_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4485,-573 4498,-573 4498,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11c7df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-573 4353,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5603@1" ObjectIDZND0="5602@x" ObjectIDZND1="5605@x" Pin0InfoVect0LinkObjId="SW-35337_0" Pin0InfoVect1LinkObjId="SW-35340_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35338_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-573 4353,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11c8050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-573 4377,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5603@x" ObjectIDND1="5605@x" ObjectIDZND0="5602@1" Pin0InfoVect0LinkObjId="SW-35337_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35338_0" Pin1InfoVect1LinkObjId="SW-35340_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-573 4377,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11ba110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-573 4353,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5602@x" ObjectIDND1="5603@x" ObjectIDZND0="5605@1" Pin0InfoVect0LinkObjId="SW-35340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35337_0" Pin1InfoVect1LinkObjId="SW-35338_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-573 4353,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11ba370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-521 4353,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5605@0" ObjectIDZND0="g_11ba5d0@0" Pin0InfoVect0LinkObjId="g_11ba5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-521 4353,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11bd660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-572 4428,-556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5602@x" ObjectIDND1="5604@x" ObjectIDZND0="5606@1" Pin0InfoVect0LinkObjId="SW-35341_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35337_0" Pin1InfoVect1LinkObjId="SW-35339_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-572 4428,-556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11bd8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-520 4428,-505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5606@0" ObjectIDZND0="g_11bdb20@0" Pin0InfoVect0LinkObjId="g_11bdb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35341_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-520 4428,-505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1197120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-573 4428,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5602@0" ObjectIDZND0="5606@x" ObjectIDZND1="5604@x" Pin0InfoVect0LinkObjId="SW-35341_0" Pin0InfoVect1LinkObjId="SW-35339_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35337_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-573 4428,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1197380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-573 4449,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5602@x" ObjectIDND1="5606@x" ObjectIDZND0="5604@0" Pin0InfoVect0LinkObjId="SW-35339_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35337_0" Pin1InfoVect1LinkObjId="SW-35341_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-573 4449,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1199c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-471 5167,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5552@0" ObjectIDZND0="5596@1" Pin0InfoVect0LinkObjId="SW-35317_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11c63a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-471 5167,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_113f3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-414 5167,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5596@0" ObjectIDZND0="5595@x" ObjectIDZND1="5600@x" Pin0InfoVect0LinkObjId="SW-35316_0" Pin0InfoVect1LinkObjId="SW-35321_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35317_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-414 5167,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_113f630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-400 5167,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5596@x" ObjectIDND1="5600@x" ObjectIDZND0="5595@1" Pin0InfoVect0LinkObjId="SW-35316_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35317_0" Pin1InfoVect1LinkObjId="SW-35321_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-400 5167,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1141c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-400 5149,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5595@x" ObjectIDND1="5596@x" ObjectIDZND0="5600@1" Pin0InfoVect0LinkObjId="SW-35321_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35316_0" Pin1InfoVect1LinkObjId="SW-35317_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-400 5149,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1141e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5096,-390 5096,-400 5113,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11420e0@0" ObjectIDZND0="5600@0" Pin0InfoVect0LinkObjId="SW-35321_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11420e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5096,-390 5096,-400 5113,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11783b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-342 5149,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5595@x" ObjectIDND1="5597@x" ObjectIDZND0="5601@1" Pin0InfoVect0LinkObjId="SW-35322_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35316_0" Pin1InfoVect1LinkObjId="SW-35318_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-342 5149,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1178610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5096,-332 5096,-342 5113,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1178870@0" ObjectIDZND0="5601@0" Pin0InfoVect0LinkObjId="SW-35322_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1178870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5096,-332 5096,-342 5113,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11792a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-358 5167,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5595@0" ObjectIDZND0="5601@x" ObjectIDZND1="5597@x" Pin0InfoVect0LinkObjId="SW-35322_0" Pin0InfoVect1LinkObjId="SW-35318_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35316_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-358 5167,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_117bab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-342 5167,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5595@x" ObjectIDND1="5601@x" ObjectIDZND0="5597@1" Pin0InfoVect0LinkObjId="SW-35318_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35316_0" Pin1InfoVect1LinkObjId="SW-35322_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-342 5167,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10cc570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5168,-208 5206,-208 5206,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5597@x" ObjectIDND1="5599@x" ObjectIDND2="g_1045730@0" ObjectIDZND0="g_10cc7d0@0" Pin0InfoVect0LinkObjId="g_10cc7d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35318_0" Pin1InfoVect1LinkObjId="SW-35320_0" Pin1InfoVect2LinkObjId="g_1045730_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5168,-208 5206,-208 5206,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_111fe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-471 5039,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5552@0" ObjectIDZND0="5584@1" Pin0InfoVect0LinkObjId="SW-35277_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11c63a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-471 5039,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1122260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-414 5039,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5584@0" ObjectIDZND0="5583@x" ObjectIDZND1="5588@x" Pin0InfoVect0LinkObjId="SW-35276_0" Pin0InfoVect1LinkObjId="SW-35281_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35277_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-414 5039,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11224c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-400 5039,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5584@x" ObjectIDND1="5588@x" ObjectIDZND0="5583@1" Pin0InfoVect0LinkObjId="SW-35276_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35277_0" Pin1InfoVect1LinkObjId="SW-35281_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-400 5039,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1124c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-400 5021,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5583@x" ObjectIDND1="5584@x" ObjectIDZND0="5588@1" Pin0InfoVect0LinkObjId="SW-35281_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35276_0" Pin1InfoVect1LinkObjId="SW-35277_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-400 5021,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1124eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-390 4968,-400 4985,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1125110@0" ObjectIDZND0="5588@0" Pin0InfoVect0LinkObjId="SW-35281_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1125110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-390 4968,-400 4985,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_116bfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-342 5021,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5583@x" ObjectIDND1="5585@x" ObjectIDZND0="5589@1" Pin0InfoVect0LinkObjId="SW-35282_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35276_0" Pin1InfoVect1LinkObjId="SW-35278_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-342 5021,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_116c200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-332 4968,-342 4985,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_116c460@0" ObjectIDZND0="5589@0" Pin0InfoVect0LinkObjId="SW-35282_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_116c460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-332 4968,-342 4985,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_116ceb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-358 5039,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5583@0" ObjectIDZND0="5589@x" ObjectIDZND1="5585@x" Pin0InfoVect0LinkObjId="SW-35282_0" Pin0InfoVect1LinkObjId="SW-35278_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35276_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-358 5039,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_116f910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-342 5039,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5583@x" ObjectIDND1="5589@x" ObjectIDZND0="5585@1" Pin0InfoVect0LinkObjId="SW-35278_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35276_0" Pin1InfoVect1LinkObjId="SW-35282_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-342 5039,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1189a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-208 5078,-208 5078,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5585@x" ObjectIDND1="5587@x" ObjectIDND2="g_1044d10@0" ObjectIDZND0="g_1189cc0@0" Pin0InfoVect0LinkObjId="g_1189cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35278_0" Pin1InfoVect1LinkObjId="SW-35280_0" Pin1InfoVect2LinkObjId="g_1044d10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-208 5078,-208 5078,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_118d230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-471 4909,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5552@0" ObjectIDZND0="5578@1" Pin0InfoVect0LinkObjId="SW-35257_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11c63a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-471 4909,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1101420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-414 4909,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5578@0" ObjectIDZND0="5577@x" ObjectIDZND1="5581@x" Pin0InfoVect0LinkObjId="SW-35256_0" Pin0InfoVect1LinkObjId="SW-35260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35257_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-414 4909,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1101680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-400 4909,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5578@x" ObjectIDND1="5581@x" ObjectIDZND0="5577@1" Pin0InfoVect0LinkObjId="SW-35256_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35257_0" Pin1InfoVect1LinkObjId="SW-35260_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-400 4909,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1103e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-400 4891,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5577@x" ObjectIDND1="5578@x" ObjectIDZND0="5581@1" Pin0InfoVect0LinkObjId="SW-35260_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35256_0" Pin1InfoVect1LinkObjId="SW-35257_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-400 4891,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1104070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4838,-390 4838,-400 4855,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11042d0@0" ObjectIDZND0="5581@0" Pin0InfoVect0LinkObjId="SW-35260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11042d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4838,-390 4838,-400 4855,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1107250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-342 4891,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5577@x" ObjectIDND1="5579@x" ObjectIDZND0="5582@1" Pin0InfoVect0LinkObjId="SW-35261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35256_0" Pin1InfoVect1LinkObjId="SW-35258_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-342 4891,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11074b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4838,-332 4838,-342 4855,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1107710@0" ObjectIDZND0="5582@0" Pin0InfoVect0LinkObjId="SW-35261_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1107710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4838,-332 4838,-342 4855,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1108160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-358 4909,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5577@0" ObjectIDZND0="5582@x" ObjectIDZND1="5579@x" Pin0InfoVect0LinkObjId="SW-35261_0" Pin0InfoVect1LinkObjId="SW-35258_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35256_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-358 4909,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1144930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-342 4909,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5577@x" ObjectIDND1="5582@x" ObjectIDZND0="5579@1" Pin0InfoVect0LinkObjId="SW-35258_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35256_0" Pin1InfoVect1LinkObjId="SW-35261_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-342 4909,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1147390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4910,-208 4947,-208 4947,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5579@x" ObjectIDND1="5580@x" ObjectIDND2="g_10442f0@0" ObjectIDZND0="g_11475f0@0" Pin0InfoVect0LinkObjId="g_11475f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35258_0" Pin1InfoVect1LinkObjId="SW-35259_0" Pin1InfoVect2LinkObjId="g_10442f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4910,-208 4947,-208 4947,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_114ab60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-471 4776,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5552@0" ObjectIDZND0="5571@1" Pin0InfoVect0LinkObjId="SW-35236_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11c63a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-471 4776,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_114cff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-414 4776,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5571@0" ObjectIDZND0="5570@x" ObjectIDZND1="5575@x" Pin0InfoVect0LinkObjId="SW-35235_0" Pin0InfoVect1LinkObjId="SW-35240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35236_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-414 4776,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_114d250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-400 4776,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5571@x" ObjectIDND1="5575@x" ObjectIDZND0="5570@1" Pin0InfoVect0LinkObjId="SW-35235_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35236_0" Pin1InfoVect1LinkObjId="SW-35240_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-400 4776,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10f86f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-400 4758,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5570@x" ObjectIDND1="5571@x" ObjectIDZND0="5575@1" Pin0InfoVect0LinkObjId="SW-35240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35235_0" Pin1InfoVect1LinkObjId="SW-35236_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-400 4758,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10f8950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-390 4705,-400 4722,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_10f8bb0@0" ObjectIDZND0="5575@0" Pin0InfoVect0LinkObjId="SW-35240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10f8bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-390 4705,-400 4722,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10fbb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-342 4758,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5570@x" ObjectIDND1="5572@x" ObjectIDZND0="5576@1" Pin0InfoVect0LinkObjId="SW-35241_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35235_0" Pin1InfoVect1LinkObjId="SW-35237_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-342 4758,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10fbd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-332 4705,-342 4722,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_10fbff0@0" ObjectIDZND0="5576@0" Pin0InfoVect0LinkObjId="SW-35241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10fbff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-332 4705,-342 4722,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10fca40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-358 4776,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5570@0" ObjectIDZND0="5576@x" ObjectIDZND1="5572@x" Pin0InfoVect0LinkObjId="SW-35241_0" Pin0InfoVect1LinkObjId="SW-35237_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-358 4776,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10ff4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-342 4776,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5570@x" ObjectIDND1="5576@x" ObjectIDZND0="5572@1" Pin0InfoVect0LinkObjId="SW-35237_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35235_0" Pin1InfoVect1LinkObjId="SW-35241_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-342 4776,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_101ec30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4777,-89 4815,-89 4815,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5573@x" ObjectIDND1="g_13d8090@0" ObjectIDZND0="g_101ee90@0" Pin0InfoVect0LinkObjId="g_101ee90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35238_0" Pin1InfoVect1LinkObjId="g_13d8090_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4777,-89 4815,-89 4815,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1022400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-471 4639,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5552@0" ObjectIDZND0="5564@1" Pin0InfoVect0LinkObjId="SW-35215_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11c63a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-471 4639,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1024890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-414 4639,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5564@0" ObjectIDZND0="5563@x" ObjectIDZND1="5568@x" Pin0InfoVect0LinkObjId="SW-35214_0" Pin0InfoVect1LinkObjId="SW-35219_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35215_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-414 4639,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1024af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-400 4639,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5564@x" ObjectIDND1="5568@x" ObjectIDZND0="5563@1" Pin0InfoVect0LinkObjId="SW-35214_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35215_0" Pin1InfoVect1LinkObjId="SW-35219_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-400 4639,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10a3940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-400 4621,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5563@x" ObjectIDND1="5564@x" ObjectIDZND0="5568@1" Pin0InfoVect0LinkObjId="SW-35219_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35214_0" Pin1InfoVect1LinkObjId="SW-35215_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-400 4621,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10a3ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4568,-390 4568,-400 4585,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_10a3e00@0" ObjectIDZND0="5568@0" Pin0InfoVect0LinkObjId="SW-35219_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10a3e00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4568,-390 4568,-400 4585,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10a6d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-342 4621,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5563@x" ObjectIDND1="5565@x" ObjectIDZND0="5569@1" Pin0InfoVect0LinkObjId="SW-35220_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35214_0" Pin1InfoVect1LinkObjId="SW-35216_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-342 4621,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10a6fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4568,-332 4568,-342 4585,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_10a7240@0" ObjectIDZND0="5569@0" Pin0InfoVect0LinkObjId="SW-35220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10a7240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4568,-332 4568,-342 4585,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10a7c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-358 4639,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5563@0" ObjectIDZND0="5569@x" ObjectIDZND1="5565@x" Pin0InfoVect0LinkObjId="SW-35220_0" Pin0InfoVect1LinkObjId="SW-35216_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35214_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-358 4639,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10aa6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-342 4639,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5563@x" ObjectIDND1="5569@x" ObjectIDZND0="5565@1" Pin0InfoVect0LinkObjId="SW-35216_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35214_0" Pin1InfoVect1LinkObjId="SW-35220_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-342 4639,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_119fe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-471 4506,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5552@0" ObjectIDZND0="5591@1" Pin0InfoVect0LinkObjId="SW-35298_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11c63a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-471 4506,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11a22a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-414 4506,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="5591@0" ObjectIDZND0="5590@x" ObjectIDZND1="5593@x" Pin0InfoVect0LinkObjId="SW-35297_0" Pin0InfoVect1LinkObjId="SW-35300_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-414 4506,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11a2500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-400 4506,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="5591@x" ObjectIDND1="5593@x" ObjectIDZND0="5590@1" Pin0InfoVect0LinkObjId="SW-35297_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35298_0" Pin1InfoVect1LinkObjId="SW-35300_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-400 4506,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11a4c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-400 4488,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5590@x" ObjectIDND1="5591@x" ObjectIDZND0="5593@1" Pin0InfoVect0LinkObjId="SW-35300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35297_0" Pin1InfoVect1LinkObjId="SW-35298_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-400 4488,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11a4ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4435,-390 4435,-400 4452,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11a5150@0" ObjectIDZND0="5593@0" Pin0InfoVect0LinkObjId="SW-35300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11a5150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4435,-390 4435,-400 4452,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1095d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-342 4488,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5590@x" ObjectIDND1="5592@x" ObjectIDZND0="5594@1" Pin0InfoVect0LinkObjId="SW-35301_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35297_0" Pin1InfoVect1LinkObjId="SW-35299_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-342 4488,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1095f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4435,-332 4435,-342 4452,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_10961e0@0" ObjectIDZND0="5594@0" Pin0InfoVect0LinkObjId="SW-35301_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10961e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4435,-332 4435,-342 4452,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1096c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-358 4506,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5590@0" ObjectIDZND0="5594@x" ObjectIDZND1="5592@x" Pin0InfoVect0LinkObjId="SW-35301_0" Pin0InfoVect1LinkObjId="SW-35299_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35297_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-358 4506,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1099690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-342 4506,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5590@x" ObjectIDND1="5594@x" ObjectIDZND0="5592@1" Pin0InfoVect0LinkObjId="SW-35299_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35297_0" Pin1InfoVect1LinkObjId="SW-35301_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-342 4506,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10998f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-287 4506,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5592@0" ObjectIDZND0="10437@0" Pin0InfoVect0LinkObjId="g_1033c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35299_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-287 4506,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_109dae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-1140 4146,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5658@1" ObjectIDZND0="g_109d050@0" Pin0InfoVect0LinkObjId="g_109d050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35556_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-1140 4146,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_109eab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-1140 3998,-1140 3998,-1127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_109ed10@0" ObjectIDND1="10572@x" ObjectIDND2="5658@x" ObjectIDZND0="g_109dd40@0" Pin0InfoVect0LinkObjId="g_109dd40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_109ed10_0" Pin1InfoVect1LinkObjId="SW-35555_0" Pin1InfoVect2LinkObjId="SW-35556_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-1140 3998,-1140 3998,-1127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_109fc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-1140 4040,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_109dd40@0" ObjectIDND1="10572@x" ObjectIDND2="5658@x" ObjectIDZND0="g_109ed10@1" Pin0InfoVect0LinkObjId="g_109ed10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_109dd40_0" Pin1InfoVect1LinkObjId="SW-35555_0" Pin1InfoVect2LinkObjId="SW-35556_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-1140 4040,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_109fef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-1081 4040,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_109ed10@0" ObjectIDZND0="g_109f590@0" Pin0InfoVect0LinkObjId="g_109f590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_109ed10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-1081 4040,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10a0150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-880 4220,-880 4220,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="lightningRod" ObjectIDND0="11730@x" ObjectIDND1="5550@0" ObjectIDZND0="g_10a0610@0" Pin0InfoVect0LinkObjId="g_10a0610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-880 4220,-880 4220,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10a03b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-895 4253,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5550@0" ObjectIDZND0="g_10a0610@0" ObjectIDZND1="11730@x" Pin0InfoVect0LinkObjId="g_10a0610_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-895 4253,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10e7b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-880 4253,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="g_10a0610@0" ObjectIDND1="5550@0" ObjectIDZND0="11730@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_10a0610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-880 4253,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10e7dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-788 4253,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_102fa70@0" ObjectIDZND0="g_10f1c90@0" Pin0InfoVect0LinkObjId="g_10f1c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_102fa70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-788 4253,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10e8020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4253,-835 4253,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_10f1c90@1" ObjectIDZND0="11730@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10f1c90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4253,-835 4253,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10ec190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-895 4309,-788 4449,-788 4449,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5550@0" ObjectIDZND0="5661@1" Pin0InfoVect0LinkObjId="SW-35582_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-895 4309,-788 4449,-788 4449,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10ec400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-872 4449,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5661@0" ObjectIDZND0="10436@0" Pin0InfoVect0LinkObjId="g_102ea00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35582_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-872 4449,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10ef620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-1141 4672,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5660@1" ObjectIDZND0="g_10eeb90@0" Pin0InfoVect0LinkObjId="g_10eeb90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35579_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-1141 4672,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10f0800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4566,-1074 4566,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_10ef880@0" ObjectIDZND0="g_10f0100@0" Pin0InfoVect0LinkObjId="g_10f0100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10ef880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4566,-1074 4566,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10f0a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4524,-1128 4524,-1141 4566,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_10f0f20@0" ObjectIDZND0="g_10ef880@0" ObjectIDZND1="5660@x" ObjectIDZND2="10573@x" Pin0InfoVect0LinkObjId="g_10ef880_0" Pin0InfoVect1LinkObjId="SW-35579_0" Pin0InfoVect2LinkObjId="SW-35578_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10f0f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4524,-1128 4524,-1141 4566,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10f0cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4566,-1141 4566,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_10f0f20@0" ObjectIDND1="5660@x" ObjectIDND2="10573@x" ObjectIDZND0="g_10ef880@1" Pin0InfoVect0LinkObjId="g_10ef880_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_10f0f20_0" Pin1InfoVect1LinkObjId="SW-35579_0" Pin1InfoVect2LinkObjId="SW-35578_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4566,-1141 4566,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10888d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4658,-655 4658,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="5662@1" ObjectIDZND0="5560@1" Pin0InfoVect0LinkObjId="SW-35189_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13f0580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4658,-655 4658,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_108b330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4658,-522 4658,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5561@0" ObjectIDZND0="5552@0" Pin0InfoVect0LinkObjId="g_11c63a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4658,-522 4658,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_108e550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-574 4677,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5560@x" ObjectIDND1="5561@x" ObjectIDZND0="5562@0" Pin0InfoVect0LinkObjId="SW-35193_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35189_0" Pin1InfoVect1LinkObjId="SW-35192_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-574 4677,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_108e7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4713,-574 4726,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5562@1" ObjectIDZND0="g_108dac0@0" Pin0InfoVect0LinkObjId="g_108dac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35193_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4713,-574 4726,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_108ea10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4658,-592 4658,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5560@0" ObjectIDZND0="5562@x" ObjectIDZND1="5561@x" Pin0InfoVect0LinkObjId="SW-35193_0" Pin0InfoVect1LinkObjId="SW-35192_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35189_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4658,-592 4658,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_108ec70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4658,-574 4658,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5560@x" ObjectIDND1="5562@x" ObjectIDZND0="5561@1" Pin0InfoVect0LinkObjId="SW-35192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35189_0" Pin1InfoVect1LinkObjId="SW-35193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4658,-574 4658,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1091100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-654 4105,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="5663@1" ObjectIDZND0="5612@1" Pin0InfoVect0LinkObjId="SW-35402_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-654 4105,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1093b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-521 4105,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5613@0" ObjectIDZND0="5551@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35405_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-521 4105,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10b2180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-573 4124,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5612@x" ObjectIDND1="5613@x" ObjectIDZND0="5614@0" Pin0InfoVect0LinkObjId="SW-35406_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35402_0" Pin1InfoVect1LinkObjId="SW-35405_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-573 4124,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10b23e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-573 4173,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5614@1" ObjectIDZND0="g_10b1780@0" Pin0InfoVect0LinkObjId="g_10b1780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35406_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-573 4173,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10b2640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-591 4105,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="5612@0" ObjectIDZND0="5614@x" ObjectIDZND1="5613@x" Pin0InfoVect0LinkObjId="SW-35406_0" Pin0InfoVect1LinkObjId="SW-35405_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35402_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-591 4105,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10b28a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-573 4105,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="5612@x" ObjectIDND1="5614@x" ObjectIDZND0="5613@1" Pin0InfoVect0LinkObjId="SW-35405_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35402_0" Pin1InfoVect1LinkObjId="SW-35406_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-573 4105,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10b51f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5134,-471 5134,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5552@0" ObjectIDZND0="5609@0" Pin0InfoVect0LinkObjId="SW-35364_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11c63a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5134,-471 5134,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10b7980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5134,-558 5134,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="5609@1" ObjectIDZND0="5610@x" ObjectIDZND1="g_13eb3d0@0" Pin0InfoVect0LinkObjId="SW-35365_0" Pin0InfoVect1LinkObjId="g_13eb3d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35364_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5134,-558 5134,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10b7be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5134,-576 5134,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="5609@x" ObjectIDND1="5610@x" ObjectIDZND0="g_13eb3d0@0" Pin0InfoVect0LinkObjId="g_13eb3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35364_0" Pin1InfoVect1LinkObjId="SW-35365_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5134,-576 5134,-598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10b7e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5134,-576 5114,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="5609@x" ObjectIDND1="g_13eb3d0@0" ObjectIDZND0="5610@1" Pin0InfoVect0LinkObjId="SW-35365_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35364_0" Pin1InfoVect1LinkObjId="g_13eb3d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5134,-576 5114,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10b80a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-576 5064,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5610@0" ObjectIDZND0="g_10b8300@0" Pin0InfoVect0LinkObjId="g_10b8300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35365_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-576 5064,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1049fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-716 4050,-716 4050,-707 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1049530@0" Pin0InfoVect0LinkObjId="g_1049530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-716 4050,-716 4050,-707 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_104ae00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4610,-708 4610,-717 4659,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_104a220@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_104a220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4610,-708 4610,-717 4659,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_105be20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-738 4106,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="5663@0" ObjectIDZND0="g_1099db0@0" ObjectIDZND1="5611@x" Pin0InfoVect0LinkObjId="g_1099db0_0" Pin0InfoVect1LinkObjId="SW-35382_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-738 4106,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_105c010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-760 4127,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="5611@x" ObjectIDND1="5663@x" ObjectIDZND0="g_1099db0@0" Pin0InfoVect0LinkObjId="g_1099db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35382_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-760 4127,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10288d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-895 4106,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="5550@0" ObjectIDZND0="5611@1" Pin0InfoVect0LinkObjId="SW-35382_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-895 4106,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1028b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4106,-779 4106,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="5611@0" ObjectIDZND0="g_1099db0@0" ObjectIDZND1="5663@x" Pin0InfoVect0LinkObjId="g_1099db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35382_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4106,-779 4106,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_102ea00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-870 4659,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="5558@1" ObjectIDZND0="10436@0" Pin0InfoVect0LinkObjId="g_10ec400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35168_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-870 4659,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1033c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3554,-232 3554,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5619@0" ObjectIDZND0="10437@0" Pin0InfoVect0LinkObjId="g_10998f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3554,-232 3554,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1033df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3692,-232 3692,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5626@0" ObjectIDZND0="10437@0" Pin0InfoVect0LinkObjId="g_10998f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35452_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3692,-232 3692,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1033fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3829,-232 3829,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5633@0" ObjectIDZND0="10437@0" Pin0InfoVect0LinkObjId="g_10998f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35473_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3829,-232 3829,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1034210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-232 3962,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5640@0" ObjectIDZND0="10437@0" Pin0InfoVect0LinkObjId="g_10998f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35494_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-232 3962,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1034440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4092,-232 4092,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5647@0" ObjectIDZND0="10437@0" Pin0InfoVect0LinkObjId="g_10998f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35515_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4092,-232 4092,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1034670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-232 4220,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5654@0" ObjectIDZND0="10437@0" Pin0InfoVect0LinkObjId="g_10998f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35536_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-232 4220,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10348a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4092,-268 4092,-280 4129,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="5647@1" ObjectIDZND0="5645@x" ObjectIDZND1="g_103e590@0" Pin0InfoVect0LinkObjId="SW-35513_0" Pin0InfoVect1LinkObjId="g_103e590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35515_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4092,-268 4092,-280 4129,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1034ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-280 4129,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="5647@x" ObjectIDND1="g_103e590@0" ObjectIDZND0="5645@0" Pin0InfoVect0LinkObjId="SW-35513_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35515_0" Pin1InfoVect1LinkObjId="g_103e590_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-280 4129,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1034d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-268 4220,-278 4257,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="5654@1" ObjectIDZND0="5652@x" Pin0InfoVect0LinkObjId="SW-35534_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35536_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-268 4220,-278 4257,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1034f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-289 4257,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="5652@0" ObjectIDZND0="5654@x" Pin0InfoVect0LinkObjId="SW-35536_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35534_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-289 4257,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1035160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-232 4602,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5567@0" ObjectIDZND0="10437@0" Pin0InfoVect0LinkObjId="g_10998f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35218_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-232 4602,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1035390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4739,-232 4739,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5574@0" ObjectIDZND0="10437@0" Pin0InfoVect0LinkObjId="g_10998f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35239_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4739,-232 4739,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10355f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-232 4872,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5580@0" ObjectIDZND0="10437@0" Pin0InfoVect0LinkObjId="g_10998f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35259_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-232 4872,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1035850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-268 4602,-275 4639,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="5567@1" ObjectIDZND0="5565@x" ObjectIDZND1="g_1042eb0@0" Pin0InfoVect0LinkObjId="SW-35216_0" Pin0InfoVect1LinkObjId="g_1042eb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35218_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-268 4602,-275 4639,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1035ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-287 4639,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="5565@0" ObjectIDZND0="5567@x" ObjectIDZND1="g_1042eb0@0" Pin0InfoVect0LinkObjId="SW-35218_0" Pin0InfoVect1LinkObjId="g_1042eb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35216_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-287 4639,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1035d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-268 4872,-278 4909,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5580@1" ObjectIDZND0="5579@x" ObjectIDZND1="g_11475f0@0" ObjectIDZND2="g_10442f0@0" Pin0InfoVect0LinkObjId="SW-35258_0" Pin0InfoVect1LinkObjId="g_11475f0_0" Pin0InfoVect2LinkObjId="g_10442f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35259_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-268 4872,-278 4909,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1035f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-278 4909,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="5580@x" ObjectIDND1="g_11475f0@0" ObjectIDND2="g_10442f0@0" ObjectIDZND0="5579@0" Pin0InfoVect0LinkObjId="SW-35258_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35259_0" Pin1InfoVect1LinkObjId="g_11475f0_0" Pin1InfoVect2LinkObjId="g_10442f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-278 4909,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10361d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5002,-268 5002,-278 5039,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5587@1" ObjectIDZND0="5585@x" ObjectIDZND1="g_1189cc0@0" ObjectIDZND2="g_1044d10@0" Pin0InfoVect0LinkObjId="SW-35278_0" Pin0InfoVect1LinkObjId="g_1189cc0_0" Pin0InfoVect2LinkObjId="g_1044d10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5002,-268 5002,-278 5039,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1036430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-278 5039,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="5587@x" ObjectIDND1="g_1189cc0@0" ObjectIDND2="g_1044d10@0" ObjectIDZND0="5585@0" Pin0InfoVect0LinkObjId="SW-35278_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35280_0" Pin1InfoVect1LinkObjId="g_1189cc0_0" Pin1InfoVect2LinkObjId="g_1044d10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-278 5039,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1036690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-268 5130,-278 5167,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5599@1" ObjectIDZND0="5597@x" ObjectIDZND1="g_10cc7d0@0" ObjectIDZND2="g_1045730@0" Pin0InfoVect0LinkObjId="SW-35318_0" Pin0InfoVect1LinkObjId="g_10cc7d0_0" Pin0InfoVect2LinkObjId="g_1045730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-268 5130,-278 5167,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10368f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-278 5167,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="5599@x" ObjectIDND1="g_10cc7d0@0" ObjectIDND2="g_1045730@0" ObjectIDZND0="5597@0" Pin0InfoVect0LinkObjId="SW-35318_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35320_0" Pin1InfoVect1LinkObjId="g_10cc7d0_0" Pin1InfoVect2LinkObjId="g_1045730_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-278 5167,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1036b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-232 5130,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5599@0" ObjectIDZND0="10437@0" Pin0InfoVect0LinkObjId="g_10998f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5130,-232 5130,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1036db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5002,-232 5002,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5587@0" ObjectIDZND0="10437@0" Pin0InfoVect0LinkObjId="g_10998f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5002,-232 5002,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10379f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3554,-268 3554,-278 3588,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="5619@1" ObjectIDZND0="5617@x" ObjectIDZND1="g_1037010@0" Pin0InfoVect0LinkObjId="SW-35429_0" Pin0InfoVect1LinkObjId="g_1037010_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35431_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3554,-268 3554,-278 3588,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1037c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-278 3588,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="5619@x" ObjectIDND1="g_1037010@0" ObjectIDZND0="5617@0" Pin0InfoVect0LinkObjId="SW-35429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35431_0" Pin1InfoVect1LinkObjId="g_1037010_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-278 3588,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1037eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-278 3588,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5617@x" ObjectIDND1="5619@x" ObjectIDZND0="g_1037010@1" Pin0InfoVect0LinkObjId="g_1037010_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35429_0" Pin1InfoVect1LinkObjId="SW-35431_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-278 3588,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1038110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-157 3588,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1037010@0" ObjectIDZND0="5618@1" Pin0InfoVect0LinkObjId="SW-35430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1037010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-157 3588,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1038ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3626,-100 3588,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_121ccf0@0" ObjectIDZND0="5618@x" ObjectIDZND1="g_13d3820@0" Pin0InfoVect0LinkObjId="SW-35430_0" Pin0InfoVect1LinkObjId="g_13d3820_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_121ccf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3626,-100 3588,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1038d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3692,-268 3692,-277 3729,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="5626@1" ObjectIDZND0="5624@x" ObjectIDZND1="g_103a070@0" Pin0InfoVect0LinkObjId="SW-35450_0" Pin0InfoVect1LinkObjId="g_103a070_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35452_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3692,-268 3692,-277 3729,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1038f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-277 3729,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="5626@x" ObjectIDND1="g_103a070@0" ObjectIDZND0="5624@0" Pin0InfoVect0LinkObjId="SW-35450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35452_0" Pin1InfoVect1LinkObjId="g_103a070_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-277 3729,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10391f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3767,-84 3767,-99 3729,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_11f85d0@0" ObjectIDZND0="5625@x" ObjectIDZND1="g_13d4590@0" Pin0InfoVect0LinkObjId="SW-35451_0" Pin0InfoVect1LinkObjId="g_13d4590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11f85d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3767,-84 3767,-99 3729,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1039bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3829,-268 3829,-278 3866,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="5633@1" ObjectIDZND0="5631@x" ObjectIDZND1="g_103af50@0" Pin0InfoVect0LinkObjId="SW-35471_0" Pin0InfoVect1LinkObjId="g_103af50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35473_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3829,-268 3829,-278 3866,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1039e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-278 3866,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="5633@x" ObjectIDND1="g_103af50@0" ObjectIDZND0="5631@0" Pin0InfoVect0LinkObjId="SW-35471_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35473_0" Pin1InfoVect1LinkObjId="g_103af50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-278 3866,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103aa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-141 3729,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5625@1" ObjectIDZND0="g_103a070@0" Pin0InfoVect0LinkObjId="g_103a070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-141 3729,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103acf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-208 3729,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_103a070@1" ObjectIDZND0="5624@x" ObjectIDZND1="5626@x" Pin0InfoVect0LinkObjId="SW-35450_0" Pin0InfoVect1LinkObjId="SW-35452_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_103a070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-208 3729,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103b970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-278 3866,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5631@x" ObjectIDND1="5633@x" ObjectIDZND0="g_103af50@1" Pin0InfoVect0LinkObjId="g_103af50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35471_0" Pin1InfoVect1LinkObjId="SW-35473_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-278 3866,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103bbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-159 3866,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_103af50@0" ObjectIDZND0="5632@1" Pin0InfoVect0LinkObjId="SW-35472_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_103af50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-159 3866,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103c850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-278 3999,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5640@x" ObjectIDND1="5638@x" ObjectIDZND0="g_103be30@1" Pin0InfoVect0LinkObjId="g_103be30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35494_0" Pin1InfoVect1LinkObjId="SW-35492_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-278 3999,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103cab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3962,-268 3962,-278 3999,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5640@1" ObjectIDZND0="g_103be30@0" ObjectIDZND1="5638@x" Pin0InfoVect0LinkObjId="g_103be30_0" Pin0InfoVect1LinkObjId="SW-35492_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35494_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3962,-268 3962,-278 3999,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103cd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-278 3999,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="5640@x" ObjectIDND1="g_103be30@0" ObjectIDZND0="5638@0" Pin0InfoVect0LinkObjId="SW-35492_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35494_0" Pin1InfoVect1LinkObjId="g_103be30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-278 3999,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103efb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-36 4129,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34384@0" ObjectIDZND0="g_103e590@0" Pin0InfoVect0LinkObjId="g_103e590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_KFQ.456Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-36 4129,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103f210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-203 4129,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_103e590@1" ObjectIDZND0="5645@x" ObjectIDZND1="5647@x" Pin0InfoVect0LinkObjId="SW-35513_0" Pin0InfoVect1LinkObjId="SW-35515_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_103e590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-203 4129,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103f470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-109 3588,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5618@0" ObjectIDZND0="g_121ccf0@0" ObjectIDZND1="g_13d3820@0" Pin0InfoVect0LinkObjId="g_121ccf0_0" Pin0InfoVect1LinkObjId="g_13d3820_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-109 3588,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103f6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-99 3729,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_11f85d0@0" ObjectIDND1="g_13d4590@0" ObjectIDZND0="5625@0" Pin0InfoVect0LinkObjId="SW-35451_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11f85d0_0" Pin1InfoVect1LinkObjId="g_13d4590_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-99 3729,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103f930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-97 3866,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_11d8c70@0" ObjectIDND1="g_13d53f0@0" ObjectIDZND0="5632@0" Pin0InfoVect0LinkObjId="SW-35472_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11d8c70_0" Pin1InfoVect1LinkObjId="g_13d53f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-97 3866,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103fb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-86 3904,-101 3866,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_11d8c70@0" ObjectIDZND0="5632@x" ObjectIDZND1="g_13d53f0@0" Pin0InfoVect0LinkObjId="SW-35472_0" Pin0InfoVect1LinkObjId="g_13d53f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11d8c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-86 3904,-101 3866,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_103fdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4038,-89 4038,-97 3999,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_117feb0@0" ObjectIDZND0="5639@x" ObjectIDZND1="g_13d62d0@0" Pin0InfoVect0LinkObjId="SW-35493_0" Pin0InfoVect1LinkObjId="g_13d62d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_117feb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4038,-89 4038,-97 3999,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1040050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-105 3999,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5639@0" ObjectIDZND0="g_117feb0@0" ObjectIDZND1="g_13d62d0@0" Pin0InfoVect0LinkObjId="g_117feb0_0" Pin0InfoVect1LinkObjId="g_13d62d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35493_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-105 3999,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1042790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-208 5167,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_10cc7d0@0" ObjectIDND1="g_1045730@0" ObjectIDZND0="5597@x" ObjectIDZND1="5599@x" Pin0InfoVect0LinkObjId="SW-35318_0" Pin0InfoVect1LinkObjId="SW-35320_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_10cc7d0_0" Pin1InfoVect1LinkObjId="g_1045730_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-208 5167,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10429f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-208 5039,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1189cc0@0" ObjectIDND1="g_1044d10@0" ObjectIDZND0="5585@x" ObjectIDZND1="5587@x" Pin0InfoVect0LinkObjId="SW-35278_0" Pin0InfoVect1LinkObjId="SW-35280_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1189cc0_0" Pin1InfoVect1LinkObjId="g_1044d10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-208 5039,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1042c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-208 4909,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_11475f0@0" ObjectIDND1="g_10442f0@0" ObjectIDZND0="5579@x" ObjectIDZND1="5580@x" Pin0InfoVect0LinkObjId="SW-35258_0" Pin0InfoVect1LinkObjId="SW-35259_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11475f0_0" Pin1InfoVect1LinkObjId="g_10442f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-208 4909,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1046150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-275 4639,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5565@x" ObjectIDND1="5567@x" ObjectIDZND0="g_1042eb0@1" Pin0InfoVect0LinkObjId="g_1042eb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35216_0" Pin1InfoVect1LinkObjId="SW-35218_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-275 4639,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10463b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-156 4639,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1042eb0@0" ObjectIDZND0="5566@1" Pin0InfoVect0LinkObjId="SW-35217_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1042eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-156 4639,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13548c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-143 4776,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_10438d0@0" ObjectIDZND0="5573@1" Pin0InfoVect0LinkObjId="SW-35238_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10438d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-143 4776,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1354b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-97 4776,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5573@0" ObjectIDZND0="g_101ee90@0" ObjectIDZND1="g_13d8090@0" Pin0InfoVect0LinkObjId="g_101ee90_0" Pin0InfoVect1LinkObjId="g_13d8090_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35238_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-97 4776,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1354d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4676,-88 4676,-102 4639,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_119c8a0@0" ObjectIDZND0="5566@x" ObjectIDZND1="g_13d71b0@0" Pin0InfoVect0LinkObjId="SW-35217_0" Pin0InfoVect1LinkObjId="g_13d71b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_119c8a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4676,-88 4676,-102 4639,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1354fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-111 4639,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5566@0" ObjectIDZND0="g_119c8a0@0" ObjectIDZND1="g_13d71b0@0" Pin0InfoVect0LinkObjId="g_119c8a0_0" Pin0InfoVect1LinkObjId="g_13d71b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35217_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-111 4639,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1355240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-39 4909,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34388@0" ObjectIDZND0="g_10442f0@0" Pin0InfoVect0LinkObjId="g_10442f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_KFQ.493Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-39 4909,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13554a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-188 4909,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_10442f0@1" ObjectIDZND0="5579@x" ObjectIDZND1="5580@x" ObjectIDZND2="g_11475f0@0" Pin0InfoVect0LinkObjId="SW-35258_0" Pin0InfoVect1LinkObjId="SW-35259_0" Pin0InfoVect2LinkObjId="g_11475f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10442f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-188 4909,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1355700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-40 5039,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34389@0" ObjectIDZND0="g_1044d10@0" Pin0InfoVect0LinkObjId="g_1044d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_KFQ.494Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-40 5039,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1355960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-187 5039,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1044d10@1" ObjectIDZND0="5585@x" ObjectIDZND1="5587@x" ObjectIDZND2="g_1189cc0@0" Pin0InfoVect0LinkObjId="SW-35278_0" Pin0InfoVect1LinkObjId="SW-35280_0" Pin0InfoVect2LinkObjId="g_1189cc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1044d10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-187 5039,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1355bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-40 5167,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34386@0" ObjectIDZND0="g_1045730@0" Pin0InfoVect0LinkObjId="g_1045730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_KFQ.496Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-40 5167,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1355e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5167,-187 5167,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1045730@1" ObjectIDZND0="5597@x" ObjectIDZND1="5599@x" ObjectIDZND2="g_10cc7d0@0" Pin0InfoVect0LinkObjId="SW-35318_0" Pin0InfoVect1LinkObjId="SW-35320_0" Pin0InfoVect2LinkObjId="g_10cc7d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1045730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5167,-187 5167,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1356be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4739,-268 4739,-279 4776,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5574@1" ObjectIDZND0="5572@x" ObjectIDZND1="g_1356080@0" ObjectIDZND2="g_10438d0@0" Pin0InfoVect0LinkObjId="SW-35237_0" Pin0InfoVect1LinkObjId="g_1356080_0" Pin0InfoVect2LinkObjId="g_10438d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4739,-268 4739,-279 4776,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1356e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-279 4776,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="5574@x" ObjectIDND1="g_1356080@0" ObjectIDND2="g_10438d0@0" ObjectIDZND0="5572@0" Pin0InfoVect0LinkObjId="SW-35237_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35239_0" Pin1InfoVect1LinkObjId="g_1356080_0" Pin1InfoVect2LinkObjId="g_10438d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-279 4776,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1357070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-196 4816,-207 4776,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1356080@0" ObjectIDZND0="5572@x" ObjectIDZND1="5574@x" ObjectIDZND2="g_10438d0@0" Pin0InfoVect0LinkObjId="SW-35237_0" Pin0InfoVect1LinkObjId="SW-35239_0" Pin0InfoVect2LinkObjId="g_10438d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1356080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-196 4816,-207 4776,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13572d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-279 4776,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5572@x" ObjectIDND1="5574@x" ObjectIDZND0="g_1356080@0" ObjectIDZND1="g_10438d0@0" Pin0InfoVect0LinkObjId="g_1356080_0" Pin0InfoVect1LinkObjId="g_10438d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35237_0" Pin1InfoVect1LinkObjId="SW-35239_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-279 4776,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1357530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-203 4776,-196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5572@x" ObjectIDND1="5574@x" ObjectIDND2="g_1356080@0" ObjectIDZND0="g_10438d0@1" Pin0InfoVect0LinkObjId="g_10438d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35237_0" Pin1InfoVect1LinkObjId="SW-35239_0" Pin1InfoVect2LinkObjId="g_1356080_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-203 4776,-196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1357790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-159 3999,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_103be30@0" ObjectIDZND0="5639@1" Pin0InfoVect0LinkObjId="SW-35493_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_103be30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-159 3999,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_139f7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-541 3656,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31760@1" ObjectIDZND0="31759@0" Pin0InfoVect0LinkObjId="SW-213669_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-541 3656,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_139fa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-604 3656,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31761@0" ObjectIDZND0="31759@1" Pin0InfoVect0LinkObjId="SW-213669_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-604 3656,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a2350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3604,-647 3604,-628 3638,-628 3638,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="31762@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3604,-647 3604,-628 3638,-628 3638,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a25c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-647 3650,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="31762@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213672_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3640,-647 3650,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b6660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4957,-537 4957,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31765@1" ObjectIDZND0="31764@0" Pin0InfoVect0LinkObjId="SW-213673_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213674_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4957,-537 4957,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b68c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4957,-600 4957,-582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31766@0" ObjectIDZND0="31764@1" Pin0InfoVect0LinkObjId="SW-213673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4957,-600 4957,-582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b91e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4905,-643 4905,-624 4939,-624 4939,-614 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="31767@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4905,-643 4905,-624 4939,-624 4939,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b9450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4941,-643 4957,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="31767@1" ObjectIDZND0="g_13b5910@0" ObjectIDZND1="31766@x" Pin0InfoVect0LinkObjId="g_13b5910_0" Pin0InfoVect1LinkObjId="SW-213675_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213676_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4941,-643 4957,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b96b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4957,-651 4957,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_13b5910@1" ObjectIDZND0="31767@x" ObjectIDZND1="31766@x" Pin0InfoVect0LinkObjId="SW-213676_0" Pin0InfoVect1LinkObjId="SW-213675_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13b5910_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4957,-651 4957,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b9910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4957,-643 4957,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="31767@x" ObjectIDND1="g_13b5910@0" ObjectIDZND0="31766@1" Pin0InfoVect0LinkObjId="SW-213675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213676_0" Pin1InfoVect1LinkObjId="g_13b5910_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4957,-643 4957,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cd370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-708 3656,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_139ea80@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_139ea80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-708 3656,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cd6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4957,-704 4957,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_13b5910@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13b5910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4957,-704 4957,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cd8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-472 3656,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5551@0" ObjectIDZND0="31760@0" Pin0InfoVect0LinkObjId="SW-213670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1093b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-472 3656,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cdae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4957,-471 4957,-505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5552@0" ObjectIDZND0="31765@0" Pin0InfoVect0LinkObjId="SW-213674_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11c63a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4957,-471 4957,-505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d1d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-655 3656,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_139ea80@1" ObjectIDZND0="31761@1" Pin0InfoVect0LinkObjId="SW-213671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_139ea80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-655 3656,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d40d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-100 3588,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5618@x" ObjectIDND1="g_121ccf0@0" ObjectIDZND0="g_13d3820@1" Pin0InfoVect0LinkObjId="g_13d3820_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35430_0" Pin1InfoVect1LinkObjId="g_121ccf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-100 3588,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d4330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3588,-52 3588,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_13d3820@0" ObjectIDZND0="34380@0" Pin0InfoVect0LinkObjId="EC-CX_KFQ.451Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d3820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-52 3588,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d4f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-41 3729,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34381@0" ObjectIDZND0="g_13d4590@0" Pin0InfoVect0LinkObjId="g_13d4590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_KFQ.452Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-41 3729,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d5190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-82 3729,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_13d4590@1" ObjectIDZND0="5625@x" ObjectIDZND1="g_11f85d0@0" Pin0InfoVect0LinkObjId="SW-35451_0" Pin0InfoVect1LinkObjId="g_11f85d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d4590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-82 3729,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d5e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-97 3866,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_11d8c70@0" ObjectIDND1="5632@x" ObjectIDZND0="g_13d53f0@1" Pin0InfoVect0LinkObjId="g_13d53f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11d8c70_0" Pin1InfoVect1LinkObjId="SW-35472_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-97 3866,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d6070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-56 3866,-41 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_13d53f0@0" ObjectIDZND0="34382@0" Pin0InfoVect0LinkObjId="EC-CX_KFQ.453Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d53f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-56 3866,-41 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d6cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-42 3999,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34383@0" ObjectIDZND0="g_13d62d0@0" Pin0InfoVect0LinkObjId="g_13d62d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_KFQ.454Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-42 3999,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d6f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-81 3999,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_13d62d0@1" ObjectIDZND0="5639@x" ObjectIDZND1="g_117feb0@0" Pin0InfoVect0LinkObjId="SW-35493_0" Pin0InfoVect1LinkObjId="g_117feb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d62d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-81 3999,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d7bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-102 4639,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5566@x" ObjectIDND1="g_119c8a0@0" ObjectIDZND0="g_13d71b0@1" Pin0InfoVect0LinkObjId="g_13d71b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-35217_0" Pin1InfoVect1LinkObjId="g_119c8a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-102 4639,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d7e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-54 4639,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_13d71b0@0" ObjectIDZND0="34385@0" Pin0InfoVect0LinkObjId="EC-CX_KFQ.491Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d71b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-54 4639,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d8ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-40 4776,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="34387@0" ObjectIDZND0="g_13d8090@0" Pin0InfoVect0LinkObjId="g_13d8090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_KFQ.492Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-40 4776,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d8d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-75 4776,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_13d8090@1" ObjectIDZND0="5573@x" ObjectIDZND1="g_101ee90@0" Pin0InfoVect0LinkObjId="SW-35238_0" Pin0InfoVect1LinkObjId="g_101ee90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d8090_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-75 4776,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13e7190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4258,-46 4257,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="5652@x" ObjectIDZND1="5654@x" Pin0InfoVect0LinkObjId="SW-35534_0" Pin0InfoVect1LinkObjId="SW-35536_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4258,-46 4257,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f0580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-781 4659,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="5558@0" ObjectIDZND0="5662@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-781 4659,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f27b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-895 4078,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5550@0" ObjectIDZND0="18162@0" Pin0InfoVect0LinkObjId="SW-35555_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-895 4078,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f29a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-956 4078,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18162@1" ObjectIDZND0="5657@0" Pin0InfoVect0LinkObjId="SW-35553_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35555_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-956 4078,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f2b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-1001 4078,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5657@1" ObjectIDZND0="10572@1" Pin0InfoVect0LinkObjId="SW-35555_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35553_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-1001 4078,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f2da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-1036 4078,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10572@0" ObjectIDZND0="5658@x" ObjectIDZND1="g_109dd40@0" ObjectIDZND2="g_109ed10@0" Pin0InfoVect0LinkObjId="SW-35556_0" Pin0InfoVect1LinkObjId="g_109dd40_0" Pin0InfoVect2LinkObjId="g_109ed10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35555_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-1036 4078,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f3a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-1140 4078,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5658@0" ObjectIDZND0="10572@x" ObjectIDZND1="g_109dd40@0" ObjectIDZND2="g_109ed10@0" Pin0InfoVect0LinkObjId="SW-35555_0" Pin0InfoVect1LinkObjId="g_109dd40_0" Pin0InfoVect2LinkObjId="g_109ed10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35556_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-1140 4078,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f3c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-1140 4040,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="10572@x" ObjectIDND1="5658@x" ObjectIDND2="38079@1" ObjectIDZND0="g_109dd40@0" ObjectIDZND1="g_109ed10@0" Pin0InfoVect0LinkObjId="g_109dd40_0" Pin0InfoVect1LinkObjId="g_109ed10_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35555_0" Pin1InfoVect1LinkObjId="SW-35556_0" Pin1InfoVect2LinkObjId="g_13f3ed0_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-1140 4040,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f3ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-1140 4078,-1172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="10572@x" ObjectIDND1="5658@x" ObjectIDND2="g_109dd40@0" ObjectIDZND0="38079@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35555_0" Pin1InfoVect1LinkObjId="SW-35556_0" Pin1InfoVect2LinkObjId="g_109dd40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-1140 4078,-1172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f5a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4623,-1141 4604,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="5660@0" ObjectIDZND0="g_10f0f20@0" ObjectIDZND1="g_10ef880@0" ObjectIDZND2="10573@x" Pin0InfoVect0LinkObjId="g_10f0f20_0" Pin0InfoVect1LinkObjId="g_10ef880_0" Pin0InfoVect2LinkObjId="SW-35578_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35579_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4623,-1141 4604,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f5c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-1141 4566,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5660@x" ObjectIDND1="10573@x" ObjectIDND2="18038@1" ObjectIDZND0="g_10f0f20@0" ObjectIDZND1="g_10ef880@0" Pin0InfoVect0LinkObjId="g_10f0f20_0" Pin0InfoVect1LinkObjId="g_10ef880_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35579_0" Pin1InfoVect1LinkObjId="SW-35578_0" Pin1InfoVect2LinkObjId="g_13f6850_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-1141 4566,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f5ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-895 4604,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10436@0" ObjectIDZND0="18161@0" Pin0InfoVect0LinkObjId="SW-35578_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10ec400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-895 4604,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f6130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-957 4604,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18161@1" ObjectIDZND0="5659@0" Pin0InfoVect0LinkObjId="SW-35576_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35578_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-957 4604,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f6390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-1004 4604,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5659@1" ObjectIDZND0="10573@1" Pin0InfoVect0LinkObjId="SW-35578_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35576_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-1004 4604,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f65f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-1037 4604,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10573@0" ObjectIDZND0="5660@x" ObjectIDZND1="g_10f0f20@0" ObjectIDZND2="g_10ef880@0" Pin0InfoVect0LinkObjId="SW-35579_0" Pin0InfoVect1LinkObjId="g_10f0f20_0" Pin0InfoVect2LinkObjId="g_10ef880_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-35578_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-1037 4604,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f6850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-1140 4604,-1188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="5660@x" ObjectIDND1="g_10f0f20@0" ObjectIDND2="g_10ef880@0" ObjectIDZND0="18038@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-35579_0" Pin1InfoVect1LinkObjId="g_10f0f20_0" Pin1InfoVect2LinkObjId="g_10ef880_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-1140 4604,-1188 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35104" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4719.000000 -1009.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35104" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5659"/>
     <cge:Term_Ref ObjectID="8249"/>
    <cge:TPSR_Ref TObjectID="5659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35105" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4719.000000 -1009.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5659"/>
     <cge:Term_Ref ObjectID="8249"/>
    <cge:TPSR_Ref TObjectID="5659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4719.000000 -1009.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5659"/>
     <cge:Term_Ref ObjectID="8249"/>
    <cge:TPSR_Ref TObjectID="5659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35054" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -645.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35054" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5612"/>
     <cge:Term_Ref ObjectID="8155"/>
    <cge:TPSR_Ref TObjectID="5612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35055" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -645.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35055" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5612"/>
     <cge:Term_Ref ObjectID="8155"/>
    <cge:TPSR_Ref TObjectID="5612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35051" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -645.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5612"/>
     <cge:Term_Ref ObjectID="8155"/>
    <cge:TPSR_Ref TObjectID="5612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35042" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -649.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35042" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5602"/>
     <cge:Term_Ref ObjectID="8135"/>
    <cge:TPSR_Ref TObjectID="5602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35043" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -649.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35043" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5602"/>
     <cge:Term_Ref ObjectID="8135"/>
    <cge:TPSR_Ref TObjectID="5602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35040" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -649.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35040" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5602"/>
     <cge:Term_Ref ObjectID="8135"/>
    <cge:TPSR_Ref TObjectID="5602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-34999" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.000000 -658.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34999" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5560"/>
     <cge:Term_Ref ObjectID="8051"/>
    <cge:TPSR_Ref TObjectID="5560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35000" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.000000 -658.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35000" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5560"/>
     <cge:Term_Ref ObjectID="8051"/>
    <cge:TPSR_Ref TObjectID="5560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-34996" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.000000 -658.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34996" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5560"/>
     <cge:Term_Ref ObjectID="8051"/>
    <cge:TPSR_Ref TObjectID="5560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35062" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3573.000000 17.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35062" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5615"/>
     <cge:Term_Ref ObjectID="8161"/>
    <cge:TPSR_Ref TObjectID="5615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35063" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3573.000000 17.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35063" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5615"/>
     <cge:Term_Ref ObjectID="8161"/>
    <cge:TPSR_Ref TObjectID="5615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35059" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3573.000000 17.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35059" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5615"/>
     <cge:Term_Ref ObjectID="8161"/>
    <cge:TPSR_Ref TObjectID="5615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35074" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3855.000000 17.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5629"/>
     <cge:Term_Ref ObjectID="8189"/>
    <cge:TPSR_Ref TObjectID="5629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35075" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3855.000000 17.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35075" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5629"/>
     <cge:Term_Ref ObjectID="8189"/>
    <cge:TPSR_Ref TObjectID="5629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35071" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3855.000000 17.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35071" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5629"/>
     <cge:Term_Ref ObjectID="8189"/>
    <cge:TPSR_Ref TObjectID="5629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35080" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3990.000000 17.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35080" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5636"/>
     <cge:Term_Ref ObjectID="8203"/>
    <cge:TPSR_Ref TObjectID="5636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35081" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3990.000000 17.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35081" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5636"/>
     <cge:Term_Ref ObjectID="8203"/>
    <cge:TPSR_Ref TObjectID="5636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35077" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3990.000000 17.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5636"/>
     <cge:Term_Ref ObjectID="8203"/>
    <cge:TPSR_Ref TObjectID="5636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35086" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4115.000000 17.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35086" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5643"/>
     <cge:Term_Ref ObjectID="8217"/>
    <cge:TPSR_Ref TObjectID="5643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35087" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4115.000000 17.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5643"/>
     <cge:Term_Ref ObjectID="8217"/>
    <cge:TPSR_Ref TObjectID="5643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35083" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4115.000000 17.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35083" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5643"/>
     <cge:Term_Ref ObjectID="8217"/>
    <cge:TPSR_Ref TObjectID="5643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35092" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4249.000000 17.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35092" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5650"/>
     <cge:Term_Ref ObjectID="8231"/>
    <cge:TPSR_Ref TObjectID="5650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35093" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4249.000000 17.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35093" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5650"/>
     <cge:Term_Ref ObjectID="8231"/>
    <cge:TPSR_Ref TObjectID="5650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35089" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4249.000000 17.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5650"/>
     <cge:Term_Ref ObjectID="8231"/>
    <cge:TPSR_Ref TObjectID="5650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35031" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.000000 -213.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5590"/>
     <cge:Term_Ref ObjectID="8111"/>
    <cge:TPSR_Ref TObjectID="5590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35032" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.000000 -213.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5590"/>
     <cge:Term_Ref ObjectID="8111"/>
    <cge:TPSR_Ref TObjectID="5590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35028" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.000000 -213.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35028" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5590"/>
     <cge:Term_Ref ObjectID="8111"/>
    <cge:TPSR_Ref TObjectID="5590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35007" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4629.000000 17.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35007" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5563"/>
     <cge:Term_Ref ObjectID="8057"/>
    <cge:TPSR_Ref TObjectID="5563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35008" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4629.000000 17.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35008" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5563"/>
     <cge:Term_Ref ObjectID="8057"/>
    <cge:TPSR_Ref TObjectID="5563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35004" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4629.000000 17.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35004" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5563"/>
     <cge:Term_Ref ObjectID="8057"/>
    <cge:TPSR_Ref TObjectID="5563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 17.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5570"/>
     <cge:Term_Ref ObjectID="8071"/>
    <cge:TPSR_Ref TObjectID="5570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35014" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 17.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35014" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5570"/>
     <cge:Term_Ref ObjectID="8071"/>
    <cge:TPSR_Ref TObjectID="5570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35010" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 17.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35010" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5570"/>
     <cge:Term_Ref ObjectID="8071"/>
    <cge:TPSR_Ref TObjectID="5570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35019" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 17.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35019" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5577"/>
     <cge:Term_Ref ObjectID="8085"/>
    <cge:TPSR_Ref TObjectID="5577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35020" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 17.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35020" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5577"/>
     <cge:Term_Ref ObjectID="8085"/>
    <cge:TPSR_Ref TObjectID="5577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35016" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 17.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35016" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5577"/>
     <cge:Term_Ref ObjectID="8085"/>
    <cge:TPSR_Ref TObjectID="5577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35025" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5030.000000 17.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35025" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5583"/>
     <cge:Term_Ref ObjectID="8097"/>
    <cge:TPSR_Ref TObjectID="5583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35026" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5030.000000 17.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35026" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5583"/>
     <cge:Term_Ref ObjectID="8097"/>
    <cge:TPSR_Ref TObjectID="5583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35022" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5030.000000 17.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35022" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5583"/>
     <cge:Term_Ref ObjectID="8097"/>
    <cge:TPSR_Ref TObjectID="5583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35037" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5156.000000 17.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35037" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5595"/>
     <cge:Term_Ref ObjectID="8121"/>
    <cge:TPSR_Ref TObjectID="5595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35038" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5156.000000 17.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35038" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5595"/>
     <cge:Term_Ref ObjectID="8121"/>
    <cge:TPSR_Ref TObjectID="5595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35034" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5156.000000 17.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35034" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5595"/>
     <cge:Term_Ref ObjectID="8121"/>
    <cge:TPSR_Ref TObjectID="5595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-34969" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -928.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34969" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5550"/>
     <cge:Term_Ref ObjectID="8034"/>
    <cge:TPSR_Ref TObjectID="5550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-34970" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -928.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34970" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5550"/>
     <cge:Term_Ref ObjectID="8034"/>
    <cge:TPSR_Ref TObjectID="5550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-34971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -928.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5550"/>
     <cge:Term_Ref ObjectID="8034"/>
    <cge:TPSR_Ref TObjectID="5550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-34972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -928.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5550"/>
     <cge:Term_Ref ObjectID="8034"/>
    <cge:TPSR_Ref TObjectID="5550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-34975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -928.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5550"/>
     <cge:Term_Ref ObjectID="8034"/>
    <cge:TPSR_Ref TObjectID="5550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-34976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3488.000000 -503.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5551"/>
     <cge:Term_Ref ObjectID="8035"/>
    <cge:TPSR_Ref TObjectID="5551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-34977" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3488.000000 -503.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34977" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5551"/>
     <cge:Term_Ref ObjectID="8035"/>
    <cge:TPSR_Ref TObjectID="5551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-34978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3488.000000 -503.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5551"/>
     <cge:Term_Ref ObjectID="8035"/>
    <cge:TPSR_Ref TObjectID="5551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-34979" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3488.000000 -503.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5551"/>
     <cge:Term_Ref ObjectID="8035"/>
    <cge:TPSR_Ref TObjectID="5551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-34982" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3488.000000 -503.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5551"/>
     <cge:Term_Ref ObjectID="8035"/>
    <cge:TPSR_Ref TObjectID="5551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-34983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5290.000000 -498.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5552"/>
     <cge:Term_Ref ObjectID="8036"/>
    <cge:TPSR_Ref TObjectID="5552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-34984" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5290.000000 -498.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34984" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5552"/>
     <cge:Term_Ref ObjectID="8036"/>
    <cge:TPSR_Ref TObjectID="5552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-34985" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5290.000000 -498.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5552"/>
     <cge:Term_Ref ObjectID="8036"/>
    <cge:TPSR_Ref TObjectID="5552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-34986" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5290.000000 -498.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5552"/>
     <cge:Term_Ref ObjectID="8036"/>
    <cge:TPSR_Ref TObjectID="5552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-34989" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5290.000000 -498.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34989" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5552"/>
     <cge:Term_Ref ObjectID="8036"/>
    <cge:TPSR_Ref TObjectID="5552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-35058" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -704.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35058" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5663"/>
     <cge:Term_Ref ObjectID="8259"/>
    <cge:TPSR_Ref TObjectID="5663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-35057" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -704.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35057" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5663"/>
     <cge:Term_Ref ObjectID="8259"/>
    <cge:TPSR_Ref TObjectID="5663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-35003" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4781.000000 -709.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5662"/>
     <cge:Term_Ref ObjectID="8255"/>
    <cge:TPSR_Ref TObjectID="5662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-35002" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4781.000000 -709.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35002" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5662"/>
     <cge:Term_Ref ObjectID="8255"/>
    <cge:TPSR_Ref TObjectID="5662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35048" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -838.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35048" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5611"/>
     <cge:Term_Ref ObjectID="8153"/>
    <cge:TPSR_Ref TObjectID="5611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35049" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -838.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35049" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5611"/>
     <cge:Term_Ref ObjectID="8153"/>
    <cge:TPSR_Ref TObjectID="5611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35045" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -838.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35045" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5611"/>
     <cge:Term_Ref ObjectID="8153"/>
    <cge:TPSR_Ref TObjectID="5611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-35050" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -838.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35050" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5611"/>
     <cge:Term_Ref ObjectID="8153"/>
    <cge:TPSR_Ref TObjectID="5611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-34993" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4786.000000 -858.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34993" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5558"/>
     <cge:Term_Ref ObjectID="8047"/>
    <cge:TPSR_Ref TObjectID="5558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-34994" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4786.000000 -858.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5558"/>
     <cge:Term_Ref ObjectID="8047"/>
    <cge:TPSR_Ref TObjectID="5558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-34990" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4786.000000 -858.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34990" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5558"/>
     <cge:Term_Ref ObjectID="8047"/>
    <cge:TPSR_Ref TObjectID="5558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-34995" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4786.000000 -858.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="34995" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5558"/>
     <cge:Term_Ref ObjectID="8047"/>
    <cge:TPSR_Ref TObjectID="5558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-35098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4198.000000 -1005.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5657"/>
     <cge:Term_Ref ObjectID="8245"/>
    <cge:TPSR_Ref TObjectID="5657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35099" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4198.000000 -1005.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35099" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5657"/>
     <cge:Term_Ref ObjectID="8245"/>
    <cge:TPSR_Ref TObjectID="5657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-35095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4198.000000 -1005.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5657"/>
     <cge:Term_Ref ObjectID="8245"/>
    <cge:TPSR_Ref TObjectID="5657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-35068" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3715.000000 15.000000) translate(0,12)">35068.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35068" ObjectName="CX_KFQ.CX_KFQ_452BK:F"/>
     <cge:PSR_Ref ObjectID="5622"/>
     <cge:Term_Ref ObjectID="8175"/>
    <cge:TPSR_Ref TObjectID="5622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-35069" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3715.000000 15.000000) translate(0,27)">35069.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35069" ObjectName="CX_KFQ.CX_KFQ_452BK:F"/>
     <cge:PSR_Ref ObjectID="5622"/>
     <cge:Term_Ref ObjectID="8175"/>
    <cge:TPSR_Ref TObjectID="5622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-35065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3715.000000 15.000000) translate(0,42)">35065.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="35065" ObjectName="CX_KFQ.CX_KFQ_452BK:F"/>
     <cge:PSR_Ref ObjectID="5622"/>
     <cge:Term_Ref ObjectID="8175"/>
    <cge:TPSR_Ref TObjectID="5622"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3233" y="-1162"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3233" y="-1162"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3185" y="-1179"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3185" y="-1179"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4088" y="-995"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4088" y="-995"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4614" y="-998"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4614" y="-998"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4378" y="-597"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4378" y="-597"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3597" y="-381"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3597" y="-381"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3738" y="-381"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3738" y="-381"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3875" y="-381"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3875" y="-381"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4008" y="-381"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4008" y="-381"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4138" y="-381"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4138" y="-381"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4266" y="-381"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4266" y="-381"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4515" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4515" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4648" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4648" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4785" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4785" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4918" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4918" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5048" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5048" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5176" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5176" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3665" y="-580"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3665" y="-580"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4966" y="-576"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4966" y="-576"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3390" y="-1144"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3390" y="-1144"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3390" y="-1179"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3390" y="-1179"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="75" x="3157" y="-815"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="75" x="3157" y="-815"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3379" y="-1055"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3379" y="-1055"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="4149" y="-733"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="4149" y="-733"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="69" x="4527" y="-695"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="69" x="4527" y="-695"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="16" qtmmishow="hidden" width="72" x="3157" y="-780"/>
    </a>
   <metadata/><rect fill="white" height="16" opacity="0" stroke="white" transform="" width="72" x="3157" y="-780"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3233" y="-1162"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3185" y="-1179"/></g>
   <g href="35kV开发区变35kV谢开Ⅰ回线351断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4088" y="-995"/></g>
   <g href="35kV开发区变35kV谢开Ⅱ回线352断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4614" y="-998"/></g>
   <g href="35kV开发区变10kV母联490断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4378" y="-597"/></g>
   <g href="35kV开发区变10kV开发区Ⅰ回线451断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3597" y="-381"/></g>
   <g href="35kV开发区变10kV开发区Ⅱ回线452断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3738" y="-381"/></g>
   <g href="35kV开发区变10kV三江线453断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3875" y="-381"/></g>
   <g href="35kV开发区变10kV桃园线454断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4008" y="-381"/></g>
   <g href="35kV开发区变10kV开发区Ⅵ回线456断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4138" y="-381"/></g>
   <g href="35kV开发区变10kV备用一线457断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4266" y="-381"/></g>
   <g href="35kV开发区变10kV旁路495断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4515" y="-379"/></g>
   <g href="35kV开发区变10kV官屯线491断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4648" y="-379"/></g>
   <g href="35kV开发区变10kV开发区Ⅳ线492断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4785" y="-379"/></g>
   <g href="35kV开发区变10kV开发区Ⅲ线493断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4918" y="-379"/></g>
   <g href="35kV开发区变10kV开发区Ⅴ线494断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5048" y="-379"/></g>
   <g href="35kV开发区变10kV开发区Ⅷ回线496断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5176" y="-379"/></g>
   <g href="35kV开发区变10kV＃1接地站用变058断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3665" y="-580"/></g>
   <g href="35kV开发区变10kV＃2接地站用变097断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4966" y="-576"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3390" y="-1144"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3390" y="-1179"/></g>
   <g href="35kV开发区变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="75" x="3157" y="-815"/></g>
   <g href="AVC开发区站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3379" y="-1055"/></g>
   <g href="35kV开发区变＃1主变间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="4149" y="-733"/></g>
   <g href="35kV开发区变＃2主变间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="69" x="4527" y="-695"/></g>
   <g href="35kV开发区变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="16" qtmmishow="hidden" width="72" x="3157" y="-780"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1133c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.000000 1005.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11350d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 990.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1136140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 975.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1136ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4661.000000 1010.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1136ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 995.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4675.000000 980.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1137500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4718.000000 658.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11377c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4707.000000 643.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1137a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4732.000000 628.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1137e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 645.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11380e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4145.000000 630.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1138320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4170.000000 615.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1138740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.000000 649.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1138a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4345.000000 634.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1138c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 619.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1139060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3517.000000 -17.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1139320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3506.000000 -32.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1139560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3531.000000 -47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1139980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4442.000000 213.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1139c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4431.000000 198.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1139e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4456.000000 183.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_113a2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4571.000000 -17.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_113a560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4560.000000 -32.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1287400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1046830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 706.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1047450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 691.000000) translate(0,12)">油温（℃）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1048570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4690.000000 710.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1048810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4690.000000 695.000000) translate(0,12)">油温（℃）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13830e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3864.000000 884.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1384130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 869.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1384d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 898.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1385290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 913.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13854d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 927.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1385800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3424.000000 458.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1385a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3440.000000 443.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1385cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3433.000000 472.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1385ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3433.000000 487.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1386130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3433.000000 501.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1386460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5224.000000 454.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13866d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5240.000000 439.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1386910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 468.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1386b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 483.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1386d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5233.000000 497.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138a130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 1009.000000) translate(0,12)">注：351手车位置信号不会变化，</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138a130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 1009.000000) translate(0,27)">始终处于分位，现场回复因信号端</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138a130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 1009.000000) translate(0,42)">子不够未接入远动测控装置。</text>
    <rect DF8003:Layer="PUBLIC" fill="none" height="48" stroke="rgb(255,0,0)" stroke-width="1" width="226" x="3768" y="963"/>
   <metadata/><rect fill="white" height="48" opacity="0" stroke="white" transform="" width="226" x="3768" y="963"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1390e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3959.000000 825.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1391430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3984.000000 810.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1391670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3989.000000 794.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1392190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.000000 840.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1392a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4716.000000 844.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1392c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4741.000000 829.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1392ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4746.000000 813.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13930e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.000000 859.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4049" x2="4040" y1="1127" y2="1132"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4049" x2="4040" y1="1122" y2="1127"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4031" x2="4040" y1="1122" y2="1127"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4031" x2="4040" y1="1127" y2="1132"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="4049" x2="4040" y1="1127" y2="1132"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4575" x2="4566" y1="1127" y2="1132"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4575" x2="4566" y1="1122" y2="1127"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4557" x2="4566" y1="1122" y2="1127"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4557" x2="4566" y1="1127" y2="1132"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="4575" x2="4566" y1="1127" y2="1132"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d91b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3661.000000 -17.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3650.000000 -32.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3675.000000 -47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3800.000000 -17.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13da120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3789.000000 -32.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13da360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13da780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3932.000000 -17.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13daa40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3921.000000 -32.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dac80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13db0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -17.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13db360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4046.000000 -32.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13db5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13db9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.000000 -17.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dbc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4178.000000 -32.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dbec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 -47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dc2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4709.000000 -17.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dc5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.000000 -32.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dc7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.000000 -47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dcc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.000000 -17.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dcec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -32.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dd100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4855.000000 -47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dd520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4976.000000 -17.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dd7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4965.000000 -32.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dda20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4990.000000 -47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dde40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5098.000000 -17.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13de100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5087.000000 -32.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13de340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5112.000000 -47.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-35427">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3579.000000 -352.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5615" ObjectName="SW-CX_KFQ.CX_KFQ_451BK"/>
     <cge:Meas_Ref ObjectId="35427"/>
    <cge:TPSR_Ref TObjectID="5615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35448">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -352.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5622" ObjectName="SW-CX_KFQ.CX_KFQ_452BK"/>
     <cge:Meas_Ref ObjectId="35448"/>
    <cge:TPSR_Ref TObjectID="5622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35469">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3857.000000 -352.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5629" ObjectName="SW-CX_KFQ.CX_KFQ_453BK"/>
     <cge:Meas_Ref ObjectId="35469"/>
    <cge:TPSR_Ref TObjectID="5629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35490">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 -352.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5636" ObjectName="SW-CX_KFQ.CX_KFQ_454BK"/>
     <cge:Meas_Ref ObjectId="35490"/>
    <cge:TPSR_Ref TObjectID="5636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35511">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -352.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5643" ObjectName="SW-CX_KFQ.CX_KFQ_456BK"/>
     <cge:Meas_Ref ObjectId="35511"/>
    <cge:TPSR_Ref TObjectID="5643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35532">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4248.000000 -352.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5650" ObjectName="SW-CX_KFQ.CX_KFQ_457BK"/>
     <cge:Meas_Ref ObjectId="35532"/>
    <cge:TPSR_Ref TObjectID="5650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35337">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.000000 -563.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5602" ObjectName="SW-CX_KFQ.CX_KFQ_490BK"/>
     <cge:Meas_Ref ObjectId="35337"/>
    <cge:TPSR_Ref TObjectID="5602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35316">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5158.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5595" ObjectName="SW-CX_KFQ.CX_KFQ_496BK"/>
     <cge:Meas_Ref ObjectId="35316"/>
    <cge:TPSR_Ref TObjectID="5595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35276">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5030.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5583" ObjectName="SW-CX_KFQ.CX_KFQ_494BK"/>
     <cge:Meas_Ref ObjectId="35276"/>
    <cge:TPSR_Ref TObjectID="5583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35256">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4900.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5577" ObjectName="SW-CX_KFQ.CX_KFQ_493BK"/>
     <cge:Meas_Ref ObjectId="35256"/>
    <cge:TPSR_Ref TObjectID="5577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35235">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5570" ObjectName="SW-CX_KFQ.CX_KFQ_492BK"/>
     <cge:Meas_Ref ObjectId="35235"/>
    <cge:TPSR_Ref TObjectID="5570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35214">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5563" ObjectName="SW-CX_KFQ.CX_KFQ_491BK"/>
     <cge:Meas_Ref ObjectId="35214"/>
    <cge:TPSR_Ref TObjectID="5563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35297">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5590" ObjectName="SW-CX_KFQ.CX_KFQ_495BK"/>
     <cge:Meas_Ref ObjectId="35297"/>
    <cge:TPSR_Ref TObjectID="5590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35189">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.000000 -584.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5560" ObjectName="SW-CX_KFQ.CX_KFQ_422BK"/>
     <cge:Meas_Ref ObjectId="35189"/>
    <cge:TPSR_Ref TObjectID="5560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35402">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 -583.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5612" ObjectName="SW-CX_KFQ.CX_KFQ_421BK"/>
     <cge:Meas_Ref ObjectId="35402"/>
    <cge:TPSR_Ref TObjectID="5612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35553">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.000000 -966.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5657" ObjectName="SW-CX_KFQ.CX_KFQ_351BK"/>
     <cge:Meas_Ref ObjectId="35553"/>
    <cge:TPSR_Ref TObjectID="5657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35576">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -969.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5659" ObjectName="SW-CX_KFQ.CX_KFQ_352BK"/>
     <cge:Meas_Ref ObjectId="35576"/>
    <cge:TPSR_Ref TObjectID="5659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 -771.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5611" ObjectName="SW-CX_KFQ.CX_KFQ_311BK"/>
     <cge:Meas_Ref ObjectId="35382"/>
    <cge:TPSR_Ref TObjectID="5611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-35168">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.000000 -773.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5558" ObjectName="SW-CX_KFQ.CX_KFQ_312BK"/>
     <cge:Meas_Ref ObjectId="35168"/>
    <cge:TPSR_Ref TObjectID="5558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213669">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -551.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31759" ObjectName="SW-CX_KFQ.CX_KFQ_058BK"/>
     <cge:Meas_Ref ObjectId="213669"/>
    <cge:TPSR_Ref TObjectID="31759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213673">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4948.000000 -547.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31764" ObjectName="SW-CX_KFQ.CX_KFQ_097BK"/>
     <cge:Meas_Ref ObjectId="213673"/>
    <cge:TPSR_Ref TObjectID="31764"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_XJH" endPointId="0" endStationName="CX_KFQ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_XieKaiI" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4078,-1170 4078,-1212 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38079" ObjectName="AC-35kV.LN_XieKaiI"/>
    <cge:TPSR_Ref TObjectID="38079_SS-29"/></metadata>
   <polyline fill="none" opacity="0" points="4078,-1170 4078,-1212 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_XJH" endPointId="0" endStationName="CX_KFQ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_XieKaiII" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4604,-1185 4604,-1221 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18038" ObjectName="AC-35kV.LN_XieKaiII"/>
    <cge:TPSR_Ref TObjectID="18038_SS-29"/></metadata>
   <polyline fill="none" opacity="0" points="4604,-1185 4604,-1221 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_121ccf0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3619.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11f85d0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3760.000000 -30.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11d8c70">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3897.000000 -32.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_117feb0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4031.000000 -35.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10cc7d0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5199.000000 -137.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1189cc0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5071.000000 -137.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11475f0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4940.000000 -138.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_101ee90">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4808.000000 -20.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_119c8a0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4669.000000 -34.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1099db0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4181.500000 -753.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_109dd40">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3991.000000 -1073.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_109ed10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.000000 -1076.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_109f590">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4028.000000 -1031.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10a0610">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4213.000000 -813.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10ef880">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4557.000000 -1069.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10f0100">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -1024.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10f0f20">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4517.000000 -1074.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10f1c90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -799.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1049530">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4043.000000 -653.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_104a220">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4603.000000 -654.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_102fa70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -749.000000)" xlink:href="#lightningRod:shape81"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1037010">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3583.000000 -152.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_103a070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3724.000000 -150.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_103af50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 -154.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_103be30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3994.000000 -154.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_103e590">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 -145.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1042eb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.000000 -151.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10438d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4771.000000 -138.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10442f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.000000 -130.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1044d10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5034.000000 -129.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1045730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5162.000000 -129.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1356080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -138.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_139ea80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3650.648489 -650.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13a2820">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3694.000000 -734.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b5910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.648489 -646.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b9b70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4995.000000 -730.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13d3820">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.523810 3583.000000 -49.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13d4590">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.523810 3724.000000 -52.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13d53f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.523810 3861.000000 -53.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13d62d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.523810 3994.000000 -51.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13d71b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.523810 4634.000000 -51.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13d8090">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.523810 4771.000000 -45.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="5551" cx="3588" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5551" cx="3729" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5551" cx="3866" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5551" cx="3999" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5551" cx="4129" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5551" cx="4257" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5551" cx="4276" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5552" cx="4498" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5552" cx="5167" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5552" cx="5039" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5552" cx="4909" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5552" cx="4776" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5552" cx="4639" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5552" cx="4506" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10437" cx="4506" cy="-218" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5550" cx="4309" cy="-895" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10436" cx="4449" cy="-895" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5552" cx="4658" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5551" cx="4105" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5550" cx="4106" cy="-895" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10436" cx="4659" cy="-895" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10437" cx="3554" cy="-218" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10437" cx="3692" cy="-218" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10437" cx="3829" cy="-218" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10437" cx="3962" cy="-218" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10437" cx="4092" cy="-218" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10437" cx="4220" cy="-218" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10437" cx="4602" cy="-218" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10437" cx="4739" cy="-218" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10437" cx="4872" cy="-218" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10437" cx="5130" cy="-218" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10437" cx="5002" cy="-218" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5552" cx="4957" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5551" cx="3935" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5551" cx="3656" cy="-471" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5550" cx="4253" cy="-895" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5550" cx="4078" cy="-895" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10436" cx="4604" cy="-895" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10b8d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4097.000000 -1166.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10d5a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3952.000000 -1179.000000) translate(0,15)">谢开Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10d6d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4505.000000 -1166.000000) translate(0,15)">谢开II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10d7450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4453.000000 -1110.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10d7ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3928.000000 -1117.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d8330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4623.000000 -1170.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10d8830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -917.000000) translate(0,15)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10d96d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3523.000000 -495.000000) translate(0,15)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10da0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5136.000000 -495.000000) translate(0,15)">10kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10da350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4461.000000 -845.000000) translate(0,12)">分段3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10daba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4114.000000 -614.000000) translate(0,12)">421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10db170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -548.000000) translate(0,12)">4212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10db3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -597.000000) translate(0,12)">7212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10db630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -688.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10db630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -688.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10db630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -688.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10db630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -688.000000) translate(0,69)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10db630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -688.000000) translate(0,87)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10db630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -688.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10db630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -688.000000) translate(0,123)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dbb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3942.000000 -548.000000) translate(0,12)">4601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dc0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -604.000000) translate(0,12)">7601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dc330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4378.000000 -597.000000) translate(0,12)">490</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dc850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4294.000000 -599.000000) translate(0,12)">4901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dcad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4451.000000 -599.000000) translate(0,12)">4902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dcd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4360.000000 -546.000000) translate(0,12)">7901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dcf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4435.000000 -545.000000) translate(0,12)">7902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dd190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4687.000000 -781.000000) translate(0,12)">3712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dd3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4667.000000 -613.000000) translate(0,12)">422</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dd610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4679.000000 -600.000000) translate(0,12)">7222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dd850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4665.000000 -547.000000) translate(0,12)">4222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dda90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5079.000000 -602.000000) translate(0,12)">7602</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10ddcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5141.000000 -547.000000) translate(0,12)">4602</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10ddf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3597.000000 -381.000000) translate(0,12)">451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10de150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3595.000000 -441.000000) translate(0,12)">4511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10de390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3595.000000 -314.000000) translate(0,12)">4512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10de5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3595.000000 -134.000000) translate(0,12)">4513</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10de810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3508.000000 -257.000000) translate(0,12)">4514</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dea50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -428.000000) translate(0,12)">7511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dec90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3536.000000 -370.000000) translate(0,12)">7512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10deed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -125.000000) translate(0,15)">开</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10deed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -125.000000) translate(0,33)">发</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10deed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -125.000000) translate(0,51)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10deed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -125.000000) translate(0,69)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10deed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -125.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10deed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -125.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10df750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -381.000000) translate(0,12)">452</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10df9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -441.000000) translate(0,12)">4521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dfbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -314.000000) translate(0,12)">4522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dfe30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -133.000000) translate(0,12)">4523</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e0070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3645.000000 -238.000000) translate(0,12)">4524</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e02b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3677.000000 -428.000000) translate(0,12)">7521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e04f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3677.000000 -370.000000) translate(0,12)">7522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e0730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3701.000000 -125.000000) translate(0,15)">开</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e0730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3701.000000 -125.000000) translate(0,33)">发</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e0730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3701.000000 -125.000000) translate(0,51)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e0730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3701.000000 -125.000000) translate(0,69)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e0730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3701.000000 -125.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e0730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3701.000000 -125.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e0c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3875.000000 -381.000000) translate(0,12)">453</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e0ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3873.000000 -441.000000) translate(0,12)">4531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e1110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3873.000000 -314.000000) translate(0,12)">4532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e1350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3873.000000 -135.000000) translate(0,12)">4533</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e1590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3782.000000 -238.000000) translate(0,12)">4534</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e17d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -71.000000) translate(0,15)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e17d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -71.000000) translate(0,33)">江</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e17d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -71.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e2010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3975.000000 -72.000000) translate(0,15)">桃</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e2010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3975.000000 -72.000000) translate(0,33)">园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e2010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3975.000000 -72.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e2890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -125.000000) translate(0,15)">开</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e2890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -125.000000) translate(0,33)">发</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e2890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -125.000000) translate(0,51)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e2890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -125.000000) translate(0,69)">Ⅵ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e2890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -125.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e2890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -125.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e2e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -71.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e2e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -71.000000) translate(0,33)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e2e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -71.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e3570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -71.000000) translate(0,15)">官</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e3570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -71.000000) translate(0,33)">屯</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e3570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -71.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4747.000000 -110.000000) translate(0,15)">开</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4747.000000 -110.000000) translate(0,33)">发</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4747.000000 -110.000000) translate(0,51)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4747.000000 -110.000000) translate(0,69)">Ⅳ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10e3ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4747.000000 -110.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e4460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3815.000000 -428.000000) translate(0,12)">7531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e46d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3814.000000 -370.000000) translate(0,12)">7532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e4910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4008.000000 -381.000000) translate(0,12)">454</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e4b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4006.000000 -441.000000) translate(0,12)">4541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e4d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4006.000000 -314.000000) translate(0,12)">4542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e4fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4008.000000 -135.000000) translate(0,12)">4543</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e5210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -238.000000) translate(0,12)">4544</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e5450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -428.000000) translate(0,12)">7541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e5690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -370.000000) translate(0,12)">7542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e58d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4138.000000 -381.000000) translate(0,12)">456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e5b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4136.000000 -441.000000) translate(0,12)">4561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e5d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4136.000000 -314.000000) translate(0,12)">4562</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e5f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4047.000000 -238.000000) translate(0,12)">4564</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e61d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4077.000000 -428.000000) translate(0,12)">7561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1126f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4077.000000 -370.000000) translate(0,12)">7562</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1127160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4266.000000 -381.000000) translate(0,12)">457</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11273a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -441.000000) translate(0,12)">4571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11275e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -314.000000) translate(0,12)">4572</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1127820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4175.000000 -238.000000) translate(0,12)">4574</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1127a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.000000 -428.000000) translate(0,12)">7571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1127ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.000000 -370.000000) translate(0,12)">7572</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1127ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4515.000000 -379.000000) translate(0,12)">495</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1128120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4513.000000 -439.000000) translate(0,12)">4951</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1128360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4513.000000 -312.000000) translate(0,12)">4952</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11285a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -426.000000) translate(0,12)">7951</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11287e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -368.000000) translate(0,12)">7952</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1128a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4482.000000 -253.000000) translate(0,15)">旁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1128a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4482.000000 -253.000000) translate(0,33)">路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1128e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4648.000000 -379.000000) translate(0,12)">491</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1129150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -439.000000) translate(0,12)">4911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1129390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -312.000000) translate(0,12)">4912</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11295d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -136.000000) translate(0,12)">4913</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1129810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4555.000000 -236.000000) translate(0,12)">4914</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1129a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4587.000000 -426.000000) translate(0,12)">7911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1129c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4587.000000 -368.000000) translate(0,12)">7912</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1129ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4785.000000 -379.000000) translate(0,12)">492</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112a110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4783.000000 -439.000000) translate(0,12)">4921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112a350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4783.000000 -312.000000) translate(0,12)">4922</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112a590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4783.000000 -122.000000) translate(0,12)">4923</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112a7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -236.000000) translate(0,12)">4924</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112aa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4724.000000 -426.000000) translate(0,12)">7921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112ac50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4723.000000 -368.000000) translate(0,12)">7922</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112ae90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4876.000000 -125.000000) translate(0,15)">开</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112ae90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4876.000000 -125.000000) translate(0,33)">发</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112ae90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4876.000000 -125.000000) translate(0,51)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112ae90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4876.000000 -125.000000) translate(0,69)">Ⅲ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112ae90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4876.000000 -125.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112ae90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4876.000000 -125.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5006.000000 -132.000000) translate(0,15)">开</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5006.000000 -132.000000) translate(0,33)">发</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5006.000000 -132.000000) translate(0,51)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5006.000000 -132.000000) translate(0,69)">Ⅴ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5006.000000 -132.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5006.000000 -132.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112b930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -125.000000) translate(0,15)">开</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112b930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -125.000000) translate(0,33)">发</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112b930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -125.000000) translate(0,51)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112b930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -125.000000) translate(0,69)">VIII</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112b930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -125.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_112b930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -125.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112bb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4918.000000 -379.000000) translate(0,12)">493</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112bdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4916.000000 -439.000000) translate(0,12)">4931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112c010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4916.000000 -312.000000) translate(0,12)">4932</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112c250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -236.000000) translate(0,12)">4934</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112c490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4857.000000 -426.000000) translate(0,12)">7931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112c6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4856.000000 -368.000000) translate(0,12)">7932</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112c910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5046.000000 -439.000000) translate(0,12)">4941</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112cb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5046.000000 -312.000000) translate(0,12)">4942</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112cd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4987.000000 -426.000000) translate(0,12)">7941</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112cfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4987.000000 -368.000000) translate(0,12)">7942</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112d210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5048.000000 -379.000000) translate(0,12)">494</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112d450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4954.000000 -236.000000) translate(0,12)">4944</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112d690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5176.000000 -379.000000) translate(0,12)">496</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112d8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5174.000000 -439.000000) translate(0,12)">4961</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112db10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5174.000000 -312.000000) translate(0,12)">4962</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112dd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5084.000000 -236.000000) translate(0,12)">4964</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112df90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5115.000000 -426.000000) translate(0,12)">7961</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_112e1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5114.000000 -368.000000) translate(0,12)">7962</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1287640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4694.000000 -917.000000) translate(0,15)">35kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1287880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3269.500000 -1151.500000) translate(0,16)">开发区变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1288e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -573.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1294dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1011.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1294dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1011.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1294dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1011.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1294dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1011.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1294dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1011.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1294dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1011.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1294dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1011.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1293370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3517.000000 -238.000000) translate(0,12)">旁母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_104b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5189.000000 -679.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_104b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5189.000000 -679.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_104b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5189.000000 -679.000000) translate(0,51)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_104b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5189.000000 -679.000000) translate(0,69)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_104b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5189.000000 -679.000000) translate(0,87)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_104b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5189.000000 -679.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_104b060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5189.000000 -679.000000) translate(0,123)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_105b5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -995.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_105bbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4614.000000 -998.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_102ec60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4118.000000 -832.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_102f290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4671.000000 -832.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1030130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -870.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1030130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -870.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1030130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -870.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1030130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -870.000000) translate(0,69)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1030130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -870.000000) translate(0,87)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1030130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -870.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1030130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -870.000000) translate(0,123)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1030710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -758.000000) translate(0,15)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1030710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -758.000000) translate(0,33)">SFZ8-16000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1033340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4525.000000 -755.000000) translate(0,15)">2号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1033340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4525.000000 -755.000000) translate(0,33)">SFZ9-16000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1388aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3249.000000 -230.000000) translate(0,17)">4914</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1388aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3249.000000 -230.000000) translate(0,38)">3393304</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13adf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3665.000000 -580.000000) translate(0,12)">058</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ae6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -530.000000) translate(0,12)">0581</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13aec30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3672.000000 -623.000000) translate(0,12)">0586</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13aee70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3602.000000 -673.000000) translate(0,12)">05867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13af0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3747.000000 -779.000000) translate(0,12)">0010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ca5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5048.000000 -775.000000) translate(0,12)">0020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cabe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4966.000000 -576.000000) translate(0,12)">097</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cae20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4973.000000 -527.000000) translate(0,12)">0972</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cb060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4971.000000 -621.000000) translate(0,12)">0976</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cb2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4902.000000 -669.000000) translate(0,12)">09767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13cb4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -986.000000) translate(0,15)">1号接地站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13cb4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -986.000000) translate(0,33)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13ccd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4937.000000 -982.000000) translate(0,15)">2号接地站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13ccd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4937.000000 -982.000000) translate(0,33)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_13cef60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3401.000000 -1136.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1290350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3401.000000 -1171.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d2050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -814.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13de580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -176.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13de580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -176.000000) translate(0,38)">心变运一班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13e0750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3249.000000 -161.500000) translate(0,17)">13908784302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_13e1890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3395.500000 -1043.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13e8a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4527.000000 -695.000000) translate(0,16)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ec980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4149.000000 -733.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_13f07e0" transform="matrix(0.750000 -0.000000 -0.000000 0.666667 3158.500000 -779.333333) translate(0,20)">隔刀远控</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="3713" cy="-910" fill="none" fillStyle="0" r="4.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3740" cy="-910" fill="none" fillStyle="0" r="4.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="3740" cy="-930" fill="none" fillStyle="0" r="4.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="5014" cy="-906" fill="none" fillStyle="0" r="4.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="5041" cy="-906" fill="none" fillStyle="0" r="4.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="5041" cy="-926" fill="none" fillStyle="0" r="4.5" stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_13e44e0">
    <use class="BV-10KV" transform="matrix(1.120000 -0.000000 0.000000 -1.263889 3911.000000 -591.000000)" xlink:href="#voltageTransformer:shape111"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13eb3d0">
    <use class="BV-10KV" transform="matrix(1.120000 -0.000000 0.000000 -1.263889 5110.000000 -592.000000)" xlink:href="#voltageTransformer:shape111"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3221.500000 -1103.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62648" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3266.538462 -970.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62648" ObjectName="CX_KFQ:CX_KFQ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79882" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3266.538462 -927.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79882" ObjectName="CX_KFQ:CX_KFQ_sumQ"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3112" y="-1183"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3111" y="-583"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(0,255,0)" stroke-width="1" width="6" x="3737" y="-897"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(0,255,0)" stroke-width="1" width="6" x="3788" y="-872"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="64" stroke="rgb(0,255,0)" stroke-width="1" width="37" x="3721" y="-864"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="61" stroke="rgb(0,255,0)" stroke-width="1" width="65" x="3693" y="-937"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="99" stroke="rgb(0,255,0)" stroke-width="1" width="19" x="3780" y="-903"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(0,255,0)" stroke-width="1" width="6" x="5038" y="-893"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="12" stroke="rgb(0,255,0)" stroke-width="1" width="6" x="5089" y="-868"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="64" stroke="rgb(0,255,0)" stroke-width="1" width="37" x="5022" y="-860"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="61" stroke="rgb(0,255,0)" stroke-width="1" width="65" x="4994" y="-933"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="99" stroke="rgb(0,255,0)" stroke-width="1" width="19" x="5081" y="-899"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3379" y="-1054"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-29" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3405.000000 -1071.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29" ObjectName="DYN-CX_KFQ"/>
     <cge:Meas_Ref ObjectId="29"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 3672.000000 -825.000000)" xlink:href="#transformer2:shape62_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 3672.000000 -825.000000)" xlink:href="#transformer2:shape62_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_KFQ.CX_KFQ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="8261"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.030303 -0.000000 0.000000 -0.911765 4066.000000 -650.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.030303 -0.000000 0.000000 -0.911765 4066.000000 -650.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5663" ObjectName="TF-CX_KFQ.CX_KFQ_1T"/>
    <cge:TPSR_Ref TObjectID="5663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_KFQ.CX_KFQ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="8257"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.954545 -0.000000 0.000000 -0.872549 4623.000000 -655.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.954545 -0.000000 0.000000 -0.872549 4623.000000 -655.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="5662" ObjectName="TF-CX_KFQ.CX_KFQ_2T"/>
    <cge:TPSR_Ref TObjectID="5662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.957447 0.000000 0.000000 0.932039 4972.000000 -812.000000)" xlink:href="#transformer2:shape62_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.957447 0.000000 0.000000 0.932039 4972.000000 -812.000000)" xlink:href="#transformer2:shape62_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_KFQ"/>
</svg>