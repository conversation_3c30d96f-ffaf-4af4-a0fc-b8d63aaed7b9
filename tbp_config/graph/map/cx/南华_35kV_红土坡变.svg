<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-198" aopId="3965442" id="thSvg" product="E8000V2" version="1.0" viewBox="1348 -1565 1896 1367">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape125">
    <ellipse cx="14" cy="17" fillStyle="0" rx="9" ry="7" stroke-width="0.153636"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="20" x2="20" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="8" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="14" y1="16" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape189">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="13" y1="21" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="13,64 38,64 38,35 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="13" y1="58" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="32" x2="44" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="41" x2="35" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="39" x2="37" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="13" y1="21" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="53" y2="0"/>
    <circle cx="13" cy="66" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="80" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="84" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="88" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="60" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="64" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="68" y2="64"/>
    <ellipse cx="13" cy="82" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape44_0">
    <circle cx="26" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="27" y1="57" y2="27"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="26,57 2,57 1,57 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="27,14 21,27 34,27 27,14 27,15 27,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="25,87 21,78 31,78 25,87 "/>
   </symbol>
   <symbol id="transformer2:shape44_1">
    <circle cx="25" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="30" y1="57" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="21" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="57" y2="52"/>
   </symbol>
   <symbol id="voltageTransformer:shape5">
    <circle cx="7" cy="9" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="6" cy="18" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="13" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_109c2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_fee270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_feec50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1346ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_f88560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_f89010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_faae90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_fab760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1151490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1151490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_10b1f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_10b1f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_10863f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_10863f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_132bd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_11d3f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_10c9d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_10ca930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_169bfe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1014570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1013ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_f75020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1679140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1679ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_167bcc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_167c740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1328950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_13293a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_108ae20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_108bfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_f7c050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1158de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1022930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1023c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_12c5700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1377" width="1906" x="1343" y="-1570"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="1685" y="-1314"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-132006">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2247.000000 -1131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24195" ObjectName="SW-NH_HTP.NH_HTP_3111SW"/>
     <cge:Meas_Ref ObjectId="132006"/>
    <cge:TPSR_Ref TObjectID="24195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132052">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2247.000000 -754.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24197" ObjectName="SW-NH_HTP.NH_HTP_0011SW"/>
     <cge:Meas_Ref ObjectId="132052"/>
    <cge:TPSR_Ref TObjectID="24197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132081">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3129.000000 -648.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24198" ObjectName="SW-NH_HTP.NH_HTP_0901SW"/>
     <cge:Meas_Ref ObjectId="132081"/>
    <cge:TPSR_Ref TObjectID="24198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132158">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1979.000000 -649.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24209" ObjectName="SW-NH_HTP.NH_HTP_0641SW"/>
     <cge:Meas_Ref ObjectId="132158"/>
    <cge:TPSR_Ref TObjectID="24209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132159">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1979.000000 -526.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24210" ObjectName="SW-NH_HTP.NH_HTP_0642SW"/>
     <cge:Meas_Ref ObjectId="132159"/>
    <cge:TPSR_Ref TObjectID="24210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309763">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1979.000000 -420.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24215" ObjectName="SW-NH_HTP.NH_HTP_0646SW"/>
     <cge:Meas_Ref ObjectId="309763"/>
    <cge:TPSR_Ref TObjectID="24215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132134">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2302.000000 -650.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24206" ObjectName="SW-NH_HTP.NH_HTP_0631SW"/>
     <cge:Meas_Ref ObjectId="132134"/>
    <cge:TPSR_Ref TObjectID="24206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132135">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2302.000000 -527.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24207" ObjectName="SW-NH_HTP.NH_HTP_0632SW"/>
     <cge:Meas_Ref ObjectId="132135"/>
    <cge:TPSR_Ref TObjectID="24207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309764">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2302.000000 -417.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24214" ObjectName="SW-NH_HTP.NH_HTP_0636SW"/>
     <cge:Meas_Ref ObjectId="309764"/>
    <cge:TPSR_Ref TObjectID="24214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132110">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2618.000000 -646.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24203" ObjectName="SW-NH_HTP.NH_HTP_0621SW"/>
     <cge:Meas_Ref ObjectId="132110"/>
    <cge:TPSR_Ref TObjectID="24203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132111">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2618.000000 -523.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24204" ObjectName="SW-NH_HTP.NH_HTP_0622SW"/>
     <cge:Meas_Ref ObjectId="132111"/>
    <cge:TPSR_Ref TObjectID="24204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309765">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2618.000000 -417.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24213" ObjectName="SW-NH_HTP.NH_HTP_0626SW"/>
     <cge:Meas_Ref ObjectId="309765"/>
    <cge:TPSR_Ref TObjectID="24213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132086">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2937.000000 -647.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24200" ObjectName="SW-NH_HTP.NH_HTP_0611SW"/>
     <cge:Meas_Ref ObjectId="132086"/>
    <cge:TPSR_Ref TObjectID="24200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132087">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2937.000000 -524.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24201" ObjectName="SW-NH_HTP.NH_HTP_0612SW"/>
     <cge:Meas_Ref ObjectId="132087"/>
    <cge:TPSR_Ref TObjectID="24201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309766">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2937.000000 -418.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24212" ObjectName="SW-NH_HTP.NH_HTP_0616SW"/>
     <cge:Meas_Ref ObjectId="309766"/>
    <cge:TPSR_Ref TObjectID="24212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-286424">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2358.063605 -1252.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44790" ObjectName="SW-NH_HTP.NH_HTP_3511SW"/>
     <cge:Meas_Ref ObjectId="286424"/>
    <cge:TPSR_Ref TObjectID="44790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-286425">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2358.063605 -1374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44791" ObjectName="SW-NH_HTP.NH_HTP_3516SW"/>
     <cge:Meas_Ref ObjectId="286425"/>
    <cge:TPSR_Ref TObjectID="44791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-286426">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2391.063605 -1442.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44792" ObjectName="SW-NH_HTP.NH_HTP_35167SW"/>
     <cge:Meas_Ref ObjectId="286426"/>
    <cge:TPSR_Ref TObjectID="44792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-286457">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2914.832016 -1252.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44794" ObjectName="SW-NH_HTP.NH_HTP_3521SW"/>
     <cge:Meas_Ref ObjectId="286457"/>
    <cge:TPSR_Ref TObjectID="44794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-286458">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2914.832016 -1374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44795" ObjectName="SW-NH_HTP.NH_HTP_3526SW"/>
     <cge:Meas_Ref ObjectId="286458"/>
    <cge:TPSR_Ref TObjectID="44795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-286459">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2947.832016 -1442.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44796" ObjectName="SW-NH_HTP.NH_HTP_35267SW"/>
     <cge:Meas_Ref ObjectId="286459"/>
    <cge:TPSR_Ref TObjectID="44796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-286505">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2664.391395 -1138.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44799" ObjectName="SW-NH_HTP.NH_HTP_3801SW"/>
     <cge:Meas_Ref ObjectId="286505"/>
    <cge:TPSR_Ref TObjectID="44799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-286500">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2868.391395 -1130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44797" ObjectName="SW-NH_HTP.NH_HTP_3901SW"/>
     <cge:Meas_Ref ObjectId="286500"/>
    <cge:TPSR_Ref TObjectID="44797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-286501">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2915.832016 -1102.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44798" ObjectName="SW-NH_HTP.NH_HTP_39017SW"/>
     <cge:Meas_Ref ObjectId="286501"/>
    <cge:TPSR_Ref TObjectID="44798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-286409">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2247.000000 -1022.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44788" ObjectName="SW-NH_HTP.NH_HTP_3116SW"/>
     <cge:Meas_Ref ObjectId="286409"/>
    <cge:TPSR_Ref TObjectID="44788"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NH_HTP.NH_HTP_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1856,-725 3244,-725 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24193" ObjectName="BS-NH_HTP.NH_HTP_9IM"/>
    <cge:TPSR_Ref TObjectID="24193"/></metadata>
   <polyline fill="none" opacity="0" points="1856,-725 3244,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NH_HTP.NH_HTP_35IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1928,-1220 3121,-1220 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="44801" ObjectName="BS-NH_HTP.NH_HTP_35IM"/>
    <cge:TPSR_Ref TObjectID="44801"/></metadata>
   <polyline fill="none" opacity="0" points="1928,-1220 3121,-1220 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-NH_HTP.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1978.000000 -224.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34109" ObjectName="EC-NH_HTP.064Ld"/>
    <cge:TPSR_Ref TObjectID="34109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_HTP.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2302.000000 -222.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34108" ObjectName="EC-NH_HTP.063Ld"/>
    <cge:TPSR_Ref TObjectID="34108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_HTP.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2617.000000 -221.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34107" ObjectName="EC-NH_HTP.062Ld"/>
    <cge:TPSR_Ref TObjectID="34107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_HTP.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2937.000000 -223.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34106" ObjectName="EC-NH_HTP.061Ld"/>
    <cge:TPSR_Ref TObjectID="34106"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1098ae0">
    <use class="BV-10KV" transform="matrix(-1.250000 -0.000000 0.000000 -1.576923 3155.000000 -507.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_106d920">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3194.333333 -542.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_ff0c70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3129.000000 -568.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10ef9b0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2025.842315 -355.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1105ba0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2343.842315 -352.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1097d40">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2663.842315 -352.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10b4990">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2982.842315 -352.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1809780">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1978.000000 -468.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_fe5fd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2301.000000 -473.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1968f30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2617.000000 -466.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19669b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2936.000000 -468.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f85a80">
    <use class="BV-35KV" transform="matrix(1.322581 -0.000000 0.000000 -1.246753 2276.063605 -1379.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12bfd70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2393.063605 -1494.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10bfe60">
    <use class="BV-35KV" transform="matrix(1.322581 -0.000000 0.000000 -1.246753 2832.832016 -1379.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12ba610">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2949.832016 -1494.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1025ff0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2660.391395 -917.000000)" xlink:href="#lightningRod:shape189"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10b54b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2664.391395 -1083.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_fb16c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2663.391395 -1022.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_131e170">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2916.063605 -1062.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_133f290">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2868.391395 -1004.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f86d70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2246.000000 -850.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f6e180">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2287.063605 -902.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_167f630">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2316.063605 -976.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1828ee0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1900.842315 -301.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17276a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1916.000000 -301.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1412.500000 -1318.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226305" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 -1232.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226305" ObjectName="NH_HTP:NH_HTP_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226305" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1477.000000 -1191.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226305" ObjectName="NH_HTP:NH_HTP_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-286551" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2125.000000 -860.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286551" ObjectName="NH_HTP:NH_HTP_1T_Tmp1"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2897.000000 -453.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24199"/>
     <cge:Term_Ref ObjectID="34129"/>
    <cge:TPSR_Ref TObjectID="24199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2897.000000 -453.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24199"/>
     <cge:Term_Ref ObjectID="34129"/>
    <cge:TPSR_Ref TObjectID="24199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131943" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2897.000000 -453.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24199"/>
     <cge:Term_Ref ObjectID="34129"/>
    <cge:TPSR_Ref TObjectID="24199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2385.000000 -841.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24196"/>
     <cge:Term_Ref ObjectID="14995"/>
    <cge:TPSR_Ref TObjectID="24196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2385.000000 -841.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24196"/>
     <cge:Term_Ref ObjectID="14995"/>
    <cge:TPSR_Ref TObjectID="24196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2385.000000 -841.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24196"/>
     <cge:Term_Ref ObjectID="14995"/>
    <cge:TPSR_Ref TObjectID="24196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-131930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1908.000000 -824.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24193"/>
     <cge:Term_Ref ObjectID="11621"/>
    <cge:TPSR_Ref TObjectID="24193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-131931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1908.000000 -824.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24193"/>
     <cge:Term_Ref ObjectID="11621"/>
    <cge:TPSR_Ref TObjectID="24193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-131932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1908.000000 -824.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24193"/>
     <cge:Term_Ref ObjectID="11621"/>
    <cge:TPSR_Ref TObjectID="24193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-131936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1908.000000 -824.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24193"/>
     <cge:Term_Ref ObjectID="11621"/>
    <cge:TPSR_Ref TObjectID="24193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-131933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1908.000000 -824.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24193"/>
     <cge:Term_Ref ObjectID="11621"/>
    <cge:TPSR_Ref TObjectID="24193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-286548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2387.000000 -1110.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24194"/>
     <cge:Term_Ref ObjectID="12925"/>
    <cge:TPSR_Ref TObjectID="24194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-286549" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2387.000000 -1110.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24194"/>
     <cge:Term_Ref ObjectID="12925"/>
    <cge:TPSR_Ref TObjectID="24194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2387.000000 -1110.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24194"/>
     <cge:Term_Ref ObjectID="12925"/>
    <cge:TPSR_Ref TObjectID="24194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2568.000000 -446.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24202"/>
     <cge:Term_Ref ObjectID="34135"/>
    <cge:TPSR_Ref TObjectID="24202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2568.000000 -446.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24202"/>
     <cge:Term_Ref ObjectID="34135"/>
    <cge:TPSR_Ref TObjectID="24202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2568.000000 -446.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24202"/>
     <cge:Term_Ref ObjectID="34135"/>
    <cge:TPSR_Ref TObjectID="24202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131967" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2255.000000 -426.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24205"/>
     <cge:Term_Ref ObjectID="34141"/>
    <cge:TPSR_Ref TObjectID="24205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2255.000000 -426.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24205"/>
     <cge:Term_Ref ObjectID="34141"/>
    <cge:TPSR_Ref TObjectID="24205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131965" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2255.000000 -426.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131965" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24205"/>
     <cge:Term_Ref ObjectID="34141"/>
    <cge:TPSR_Ref TObjectID="24205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-131978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1830.000000 -409.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24208"/>
     <cge:Term_Ref ObjectID="34147"/>
    <cge:TPSR_Ref TObjectID="24208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-131979" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1830.000000 -409.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24208"/>
     <cge:Term_Ref ObjectID="34147"/>
    <cge:TPSR_Ref TObjectID="24208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-131976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1830.000000 -409.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="131976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24208"/>
     <cge:Term_Ref ObjectID="34147"/>
    <cge:TPSR_Ref TObjectID="24208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-286553" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2043.000000 -1321.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44801"/>
     <cge:Term_Ref ObjectID="22394"/>
    <cge:TPSR_Ref TObjectID="44801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-286554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2043.000000 -1321.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44801"/>
     <cge:Term_Ref ObjectID="22394"/>
    <cge:TPSR_Ref TObjectID="44801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-286555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2043.000000 -1321.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44801"/>
     <cge:Term_Ref ObjectID="22394"/>
    <cge:TPSR_Ref TObjectID="44801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-286556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2043.000000 -1321.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44801"/>
     <cge:Term_Ref ObjectID="22394"/>
    <cge:TPSR_Ref TObjectID="44801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-286557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2043.000000 -1321.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44801"/>
     <cge:Term_Ref ObjectID="22394"/>
    <cge:TPSR_Ref TObjectID="44801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-286564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2484.000000 -1357.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44789"/>
     <cge:Term_Ref ObjectID="22372"/>
    <cge:TPSR_Ref TObjectID="44789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-286565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2484.000000 -1357.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44789"/>
     <cge:Term_Ref ObjectID="22372"/>
    <cge:TPSR_Ref TObjectID="44789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-286561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2484.000000 -1357.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44789"/>
     <cge:Term_Ref ObjectID="22372"/>
    <cge:TPSR_Ref TObjectID="44789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-286570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3060.000000 -1366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44793"/>
     <cge:Term_Ref ObjectID="22380"/>
    <cge:TPSR_Ref TObjectID="44793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-286571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3060.000000 -1366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44793"/>
     <cge:Term_Ref ObjectID="22380"/>
    <cge:TPSR_Ref TObjectID="44793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-286567" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3060.000000 -1366.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44793"/>
     <cge:Term_Ref ObjectID="22380"/>
    <cge:TPSR_Ref TObjectID="44793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-286552" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2124.000000 -876.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="286552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24216"/>
     <cge:Term_Ref ObjectID="34166"/>
    <cge:TPSR_Ref TObjectID="24216"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="1460" y="-1386"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="1460" y="-1386"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1412" y="-1403"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1412" y="-1403"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1653" y="-1360"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1653" y="-1360"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1653" y="-1399"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1653" y="-1399"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="1362" y="-1027"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="1362" y="-1027"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2376" y="-1348"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2376" y="-1348"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2933" y="-1348"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2933" y="-1348"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="2139" y="-988"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="2139" y="-988"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="1685" y="-1315"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="1685" y="-1315"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2317" y="-602"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2317" y="-602"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2636" y="-602"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2636" y="-602"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2955" y="-603"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2955" y="-603"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1996" y="-606"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1996" y="-606"/></g>
  </g><g id="MotifButton_Layer">
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="1460" y="-1386"/></g>
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1412" y="-1403"/></g>
   <g href="cx_配调_配网接线图35_南华.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1653" y="-1360"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1653" y="-1399"/></g>
   <g href="35kV红土坡变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="1362" y="-1027"/></g>
   <g href="35kV红土坡变NH_HTP_351间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2376" y="-1348"/></g>
   <g href="35kV红土坡变NH_HTP_352间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2933" y="-1348"/></g>
   <g href="35kV红土坡变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="2139" y="-988"/></g>
   <g href="AVC红土坡站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="1685" y="-1315"/></g>
   <g href="35kV红土坡变10kV五顶山线063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2317" y="-602"/></g>
   <g href="35kV红土坡变10kV红罗线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2636" y="-602"/></g>
   <g href="35kV红土坡变10kV机关线061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2955" y="-603"/></g>
   <g href="35kV红土坡变10kV簪花线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1996" y="-606"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-132004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2246.788423 -1073.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24194" ObjectName="SW-NH_HTP.NH_HTP_311BK"/>
     <cge:Meas_Ref ObjectId="132004"/>
    <cge:TPSR_Ref TObjectID="24194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132050">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2246.788423 -800.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24196" ObjectName="SW-NH_HTP.NH_HTP_001BK"/>
     <cge:Meas_Ref ObjectId="132050"/>
    <cge:TPSR_Ref TObjectID="24196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132156">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1979.000000 -576.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24208" ObjectName="SW-NH_HTP.NH_HTP_064BK"/>
     <cge:Meas_Ref ObjectId="132156"/>
    <cge:TPSR_Ref TObjectID="24208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2302.000000 -577.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24205" ObjectName="SW-NH_HTP.NH_HTP_063BK"/>
     <cge:Meas_Ref ObjectId="132132"/>
    <cge:TPSR_Ref TObjectID="24205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132108">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2618.000000 -573.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24202" ObjectName="SW-NH_HTP.NH_HTP_062BK"/>
     <cge:Meas_Ref ObjectId="132108"/>
    <cge:TPSR_Ref TObjectID="24202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-132084">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2937.000000 -574.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24199" ObjectName="SW-NH_HTP.NH_HTP_061BK"/>
     <cge:Meas_Ref ObjectId="132084"/>
    <cge:TPSR_Ref TObjectID="24199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-286423">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2358.063605 -1319.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44789" ObjectName="SW-NH_HTP.NH_HTP_351BK"/>
     <cge:Meas_Ref ObjectId="286423"/>
    <cge:TPSR_Ref TObjectID="44789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-286456">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2914.832016 -1319.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44793" ObjectName="SW-NH_HTP.NH_HTP_352BK"/>
     <cge:Meas_Ref ObjectId="286456"/>
    <cge:TPSR_Ref TObjectID="44793"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="NH_MJ" endPointId="0" endStationName="NH_HTP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_mahongTmj" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2367,-1563 2367,-1536 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34577" ObjectName="AC-35kV.LN_mahongTmj"/>
    <cge:TPSR_Ref TObjectID="34577_SS-198"/></metadata>
   <polyline fill="none" opacity="0" points="2367,-1563 2367,-1536 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="NH_DSY" endPointId="0" endStationName="NH_HTP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dahongTdsy" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2924,-1563 2924,-1536 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34576" ObjectName="AC-35kV.LN_dahongTdsy"/>
    <cge:TPSR_Ref TObjectID="34576_SS-198"/></metadata>
   <polyline fill="none" opacity="0" points="2924,-1563 2924,-1536 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_1105d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2256,-808 2256,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24196@0" ObjectIDZND0="24197@1" Pin0InfoVect0LinkObjId="SW-132052_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2256,-808 2256,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18d6400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3187,-596 3187,-624 3138,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_106d920@0" ObjectIDZND0="g_ff0c70@0" ObjectIDZND1="24198@x" Pin0InfoVect0LinkObjId="g_ff0c70_0" Pin0InfoVect1LinkObjId="SW-132081_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_106d920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3187,-596 3187,-624 3138,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18247b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3138,-605 3138,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_ff0c70@0" ObjectIDZND0="24198@x" ObjectIDZND1="g_106d920@0" Pin0InfoVect0LinkObjId="SW-132081_0" Pin0InfoVect1LinkObjId="g_106d920_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_ff0c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3138,-605 3138,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1098640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3138,-625 3138,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_ff0c70@0" ObjectIDND1="g_106d920@0" ObjectIDZND0="24198@0" Pin0InfoVect0LinkObjId="SW-132081_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_ff0c70_0" Pin1InfoVect1LinkObjId="g_106d920_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3138,-625 3138,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_111ed90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3138,-573 3138,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_ff0c70@1" ObjectIDZND0="g_1098ae0@0" Pin0InfoVect0LinkObjId="g_1098ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_ff0c70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3138,-573 3138,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10b0320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3138,-689 3138,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24198@1" ObjectIDZND0="24193@0" Pin0InfoVect0LinkObjId="g_1684670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132081_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3138,-689 3138,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10788d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,-725 1988,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24193@0" ObjectIDZND0="24209@1" Pin0InfoVect0LinkObjId="SW-132158_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10b0320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1988,-725 1988,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_187e290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,-654 1988,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24209@0" ObjectIDZND0="24208@1" Pin0InfoVect0LinkObjId="SW-132156_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132158_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1988,-654 1988,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_171e5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,-584 1988,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24208@0" ObjectIDZND0="24210@1" Pin0InfoVect0LinkObjId="SW-132159_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132156_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1988,-584 1988,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_182abb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,-425 1988,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="24215@0" ObjectIDZND0="g_10ef9b0@0" ObjectIDZND1="34109@x" ObjectIDZND2="g_1828ee0@0" Pin0InfoVect0LinkObjId="g_10ef9b0_0" Pin0InfoVect1LinkObjId="EC-NH_HTP.064Ld_0" Pin0InfoVect2LinkObjId="g_1828ee0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1988,-425 1988,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1106280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1989,-410 2017,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24215@x" ObjectIDND1="34109@x" ObjectIDND2="g_1828ee0@0" ObjectIDZND0="g_10ef9b0@0" Pin0InfoVect0LinkObjId="g_10ef9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-309763_0" Pin1InfoVect1LinkObjId="EC-NH_HTP.064Ld_0" Pin1InfoVect2LinkObjId="g_1828ee0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1989,-410 2017,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_169dc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2311,-725 2311,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24193@0" ObjectIDZND0="24206@1" Pin0InfoVect0LinkObjId="SW-132134_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10b0320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2311,-725 2311,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10668d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2311,-655 2311,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24206@0" ObjectIDZND0="24205@1" Pin0InfoVect0LinkObjId="SW-132132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132134_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2311,-655 2311,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1069d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2311,-585 2311,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24205@0" ObjectIDZND0="24207@1" Pin0InfoVect0LinkObjId="SW-132135_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2311,-585 2311,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1965470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2311,-422 2311,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24214@0" ObjectIDZND0="g_1105ba0@0" ObjectIDZND1="34108@x" Pin0InfoVect0LinkObjId="g_1105ba0_0" Pin0InfoVect1LinkObjId="EC-NH_HTP.063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309764_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2311,-422 2311,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19650b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2311,-406 2337,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="24214@x" ObjectIDND1="34108@x" ObjectIDZND0="g_1105ba0@0" Pin0InfoVect0LinkObjId="g_1105ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-309764_0" Pin1InfoVect1LinkObjId="EC-NH_HTP.063Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2311,-406 2337,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1969930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2627,-725 2627,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24193@0" ObjectIDZND0="24203@1" Pin0InfoVect0LinkObjId="SW-132110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10b0320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2627,-725 2627,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1969bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2627,-651 2627,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24203@0" ObjectIDZND0="24202@1" Pin0InfoVect0LinkObjId="SW-132108_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2627,-651 2627,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_196a0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2627,-581 2627,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24202@0" ObjectIDZND0="24204@1" Pin0InfoVect0LinkObjId="SW-132111_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132108_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2627,-581 2627,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_196b9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2627,-422 2627,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24213@0" ObjectIDZND0="g_1097d40@0" ObjectIDZND1="34107@x" Pin0InfoVect0LinkObjId="g_1097d40_0" Pin0InfoVect1LinkObjId="EC-NH_HTP.062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2627,-422 2627,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_168ee70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2629,-407 2656,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="24213@x" ObjectIDND1="34107@x" ObjectIDZND0="g_1097d40@0" Pin0InfoVect0LinkObjId="g_1097d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-309765_0" Pin1InfoVect1LinkObjId="EC-NH_HTP.062Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2629,-407 2656,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1092e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2946,-725 2946,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24193@0" ObjectIDZND0="24200@1" Pin0InfoVect0LinkObjId="SW-132086_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10b0320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2946,-725 2946,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10e0a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2946,-652 2946,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24200@0" ObjectIDZND0="24199@1" Pin0InfoVect0LinkObjId="SW-132084_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132086_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2946,-652 2946,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_fe5760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2946,-582 2946,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24199@0" ObjectIDZND0="24201@1" Pin0InfoVect0LinkObjId="SW-132087_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132084_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2946,-582 2946,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_fe64b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,-531 1988,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24210@0" ObjectIDZND0="g_1809780@1" Pin0InfoVect0LinkObjId="g_1809780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132159_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1988,-531 1988,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_fe66a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,-473 1988,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1809780@0" ObjectIDZND0="24215@1" Pin0InfoVect0LinkObjId="SW-309763_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1809780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1988,-473 1988,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_fe6ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2311,-532 2311,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24207@0" ObjectIDZND0="g_fe5fd0@1" Pin0InfoVect0LinkObjId="g_fe5fd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132135_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2311,-532 2311,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_fe7110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2311,-478 2311,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_fe5fd0@0" ObjectIDZND0="24214@1" Pin0InfoVect0LinkObjId="SW-309764_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_fe5fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2311,-478 2311,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_196a470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2627,-528 2627,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24204@0" ObjectIDZND0="g_1968f30@1" Pin0InfoVect0LinkObjId="g_1968f30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132111_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2627,-528 2627,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_196a690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2627,-471 2627,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1968f30@0" ObjectIDZND0="24213@1" Pin0InfoVect0LinkObjId="SW-309765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1968f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2627,-471 2627,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_ffb820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2946,-529 2946,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24201@0" ObjectIDZND0="g_19669b0@1" Pin0InfoVect0LinkObjId="g_19669b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132087_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2946,-529 2946,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_ffba10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2946,-473 2946,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_19669b0@0" ObjectIDZND0="24212@1" Pin0InfoVect0LinkObjId="SW-309766_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19669b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2946,-473 2946,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10879f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2976,-406 2946,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_10b4990@0" ObjectIDZND0="24212@x" ObjectIDZND1="34106@x" Pin0InfoVect0LinkObjId="SW-309766_0" Pin0InfoVect1LinkObjId="EC-NH_HTP.061Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10b4990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2976,-406 2946,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d1520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2946,-406 2946,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_10b4990@0" ObjectIDND1="34106@x" ObjectIDZND0="24212@0" Pin0InfoVect0LinkObjId="SW-309766_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_10b4990_0" Pin1InfoVect1LinkObjId="EC-NH_HTP.061Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2946,-406 2946,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11215e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2367,-1379 2367,-1354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44791@0" ObjectIDZND0="44789@1" Pin0InfoVect0LinkObjId="SW-286423_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2367,-1379 2367,-1354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1121800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2367,-1327 2367,-1293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44789@0" ObjectIDZND0="44790@1" Pin0InfoVect0LinkObjId="SW-286424_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286423_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2367,-1327 2367,-1293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_132f9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2397,-1502 2367,-1502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_12bfd70@0" ObjectIDZND0="g_f85a80@0" ObjectIDZND1="44792@x" ObjectIDZND2="44791@x" Pin0InfoVect0LinkObjId="g_f85a80_0" Pin0InfoVect1LinkObjId="SW-286426_0" Pin0InfoVect2LinkObjId="SW-286425_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12bfd70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2397,-1502 2367,-1502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_132fbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2367,-1537 2367,-1502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="34577@1" ObjectIDZND0="g_12bfd70@0" ObjectIDZND1="g_f85a80@0" ObjectIDZND2="44792@x" Pin0InfoVect0LinkObjId="g_12bfd70_0" Pin0InfoVect1LinkObjId="g_f85a80_0" Pin0InfoVect2LinkObjId="SW-286426_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2367,-1537 2367,-1502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_132fde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2396,-1449 2367,-1449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44792@0" ObjectIDZND0="44791@x" ObjectIDZND1="g_f85a80@0" ObjectIDZND2="g_12bfd70@0" Pin0InfoVect0LinkObjId="SW-286425_0" Pin0InfoVect1LinkObjId="g_f85a80_0" Pin0InfoVect2LinkObjId="g_12bfd70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2396,-1449 2367,-1449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f77fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2367,-1449 2367,-1415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="44792@x" ObjectIDND1="g_f85a80@0" ObjectIDND2="g_12bfd70@0" ObjectIDZND0="44791@1" Pin0InfoVect0LinkObjId="SW-286425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-286426_0" Pin1InfoVect1LinkObjId="g_f85a80_0" Pin1InfoVect2LinkObjId="g_12bfd70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2367,-1449 2367,-1415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12c48c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2924,-1379 2924,-1354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44795@0" ObjectIDZND0="44793@1" Pin0InfoVect0LinkObjId="SW-286456_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286458_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2924,-1379 2924,-1354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1011cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2924,-1327 2924,-1293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44793@0" ObjectIDZND0="44794@1" Pin0InfoVect0LinkObjId="SW-286457_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286456_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2924,-1327 2924,-1293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1011f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2367,-1257 2367,-1220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44790@0" ObjectIDZND0="44801@0" Pin0InfoVect0LinkObjId="g_1012190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286424_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2367,-1257 2367,-1220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1012190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2924,-1257 2924,-1220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44794@0" ObjectIDZND0="44801@0" Pin0InfoVect0LinkObjId="g_1011f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286457_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2924,-1257 2924,-1220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10123f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2954,-1502 2924,-1502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_12ba610@0" ObjectIDZND0="34576@1" ObjectIDZND1="g_10bfe60@0" ObjectIDZND2="44796@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="g_10bfe60_0" Pin0InfoVect2LinkObjId="SW-286459_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12ba610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2954,-1502 2924,-1502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1823130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2924,-1537 2924,-1502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="34576@1" ObjectIDZND0="g_12ba610@0" ObjectIDZND1="g_10bfe60@0" ObjectIDZND2="44796@x" Pin0InfoVect0LinkObjId="g_12ba610_0" Pin0InfoVect1LinkObjId="g_10bfe60_0" Pin0InfoVect2LinkObjId="SW-286459_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10123f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2924,-1537 2924,-1502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1823390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2289,-1469 2289,-1483 2367,-1483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_f85a80@0" ObjectIDZND0="g_12bfd70@0" ObjectIDZND1="34577@1" ObjectIDZND2="44792@x" Pin0InfoVect0LinkObjId="g_12bfd70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="SW-286426_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f85a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2289,-1469 2289,-1483 2367,-1483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18235f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2367,-1502 2367,-1483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_12bfd70@0" ObjectIDND1="34577@1" ObjectIDZND0="g_f85a80@0" ObjectIDZND1="44792@x" ObjectIDZND2="44791@x" Pin0InfoVect0LinkObjId="g_f85a80_0" Pin0InfoVect1LinkObjId="SW-286426_0" Pin0InfoVect2LinkObjId="SW-286425_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_12bfd70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2367,-1502 2367,-1483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1823850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2367,-1483 2367,-1449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_f85a80@0" ObjectIDND1="g_12bfd70@0" ObjectIDND2="34577@1" ObjectIDZND0="44792@x" ObjectIDZND1="44791@x" Pin0InfoVect0LinkObjId="SW-286426_0" Pin0InfoVect1LinkObjId="SW-286425_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_f85a80_0" Pin1InfoVect1LinkObjId="g_12bfd70_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2367,-1483 2367,-1449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_fb2740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,-1120 2673,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_10b54b0@0" ObjectIDZND0="44799@0" Pin0InfoVect0LinkObjId="SW-286505_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10b54b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2673,-1120 2673,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_fb29a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,-1088 2673,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_10b54b0@1" ObjectIDZND0="g_fb16c0@1" Pin0InfoVect0LinkObjId="g_fb16c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10b54b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2673,-1088 2673,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_fb2c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,-1012 2673,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1025ff0@0" ObjectIDZND0="g_fb16c0@0" Pin0InfoVect0LinkObjId="g_fb16c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1025ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2673,-1012 2673,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16a16c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2921,-1109 2877,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44798@0" ObjectIDZND0="44797@x" ObjectIDZND1="g_131e170@0" ObjectIDZND2="g_133f290@0" Pin0InfoVect0LinkObjId="SW-286500_0" Pin0InfoVect1LinkObjId="g_131e170_0" Pin0InfoVect2LinkObjId="g_133f290_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286501_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2921,-1109 2877,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12c0800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2877,-1109 2877,-1135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="44798@x" ObjectIDND1="g_131e170@0" ObjectIDND2="g_133f290@0" ObjectIDZND0="44797@0" Pin0InfoVect0LinkObjId="SW-286500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-286501_0" Pin1InfoVect1LinkObjId="g_131e170_0" Pin1InfoVect2LinkObjId="g_133f290_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2877,-1109 2877,-1135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12c0a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2920,-1070 2877,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_131e170@0" ObjectIDZND0="g_133f290@0" ObjectIDZND1="44798@x" ObjectIDZND2="44797@x" Pin0InfoVect0LinkObjId="g_133f290_0" Pin0InfoVect1LinkObjId="SW-286501_0" Pin0InfoVect2LinkObjId="SW-286500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_131e170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2920,-1070 2877,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10cfd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2877,-1041 2877,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_133f290@0" ObjectIDZND0="g_131e170@0" ObjectIDZND1="44798@x" ObjectIDZND2="44797@x" Pin0InfoVect0LinkObjId="g_131e170_0" Pin0InfoVect1LinkObjId="SW-286501_0" Pin0InfoVect2LinkObjId="SW-286500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_133f290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2877,-1041 2877,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10cffb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2877,-1070 2877,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_131e170@0" ObjectIDND1="g_133f290@0" ObjectIDZND0="44798@x" ObjectIDZND1="44797@x" Pin0InfoVect0LinkObjId="SW-286501_0" Pin0InfoVect1LinkObjId="SW-286500_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_131e170_0" Pin1InfoVect1LinkObjId="g_133f290_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2877,-1070 2877,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f86b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2876,-970 2877,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_133ec70@0" ObjectIDZND0="g_133f290@1" Pin0InfoVect0LinkObjId="g_133f290_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_133ec70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2876,-970 2877,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_f75fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2256,-832 2256,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="24196@1" ObjectIDZND0="g_f86d70@0" Pin0InfoVect0LinkObjId="g_f86d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2256,-832 2256,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_f76230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2291,-910 2256,-910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_f6e180@0" ObjectIDZND0="g_f86d70@0" ObjectIDZND1="24216@x" Pin0InfoVect0LinkObjId="g_f86d70_0" Pin0InfoVect1LinkObjId="g_fb0410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f6e180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2291,-910 2256,-910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_fb01b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2256,-894 2256,-910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_f86d70@1" ObjectIDZND0="g_f6e180@0" ObjectIDZND1="24216@x" Pin0InfoVect0LinkObjId="g_f6e180_0" Pin0InfoVect1LinkObjId="g_fb0410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f86d70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2256,-894 2256,-910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_fb0410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2256,-910 2256,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_f6e180@0" ObjectIDND1="g_f86d70@0" ObjectIDZND0="24216@0" Pin0InfoVect0LinkObjId="g_10deff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_f6e180_0" Pin1InfoVect1LinkObjId="g_f86d70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2256,-910 2256,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10deff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2320,-984 2256,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_167f630@0" ObjectIDZND0="24216@x" Pin0InfoVect0LinkObjId="g_fb0410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_167f630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2320,-984 2256,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_192e650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2256,-1136 2256,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24195@0" ObjectIDZND0="24194@1" Pin0InfoVect0LinkObjId="SW-132004_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2256,-1136 2256,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f7cc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2256,-1063 2256,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44788@1" ObjectIDZND0="24194@0" Pin0InfoVect0LinkObjId="SW-132004_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286409_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2256,-1063 2256,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f7cec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2256,-1027 2256,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="44788@0" ObjectIDZND0="24216@1" Pin0InfoVect0LinkObjId="g_fb0410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286409_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2256,-1027 2256,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_f807c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1894,-355 1894,-367 1921,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_1828ee0@0" ObjectIDZND0="g_17276a0@0" ObjectIDZND1="34109@x" ObjectIDZND2="24215@x" Pin0InfoVect0LinkObjId="g_17276a0_0" Pin0InfoVect1LinkObjId="EC-NH_HTP.064Ld_0" Pin0InfoVect2LinkObjId="SW-309763_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1828ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1894,-355 1894,-367 1921,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16a7570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1921,-351 1921,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_17276a0@1" ObjectIDZND0="g_1828ee0@0" ObjectIDZND1="34109@x" ObjectIDZND2="24215@x" Pin0InfoVect0LinkObjId="g_1828ee0_0" Pin0InfoVect1LinkObjId="EC-NH_HTP.064Ld_0" Pin0InfoVect2LinkObjId="SW-309763_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17276a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1921,-351 1921,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16a77d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1921,-291 1921,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_17276a0@0" Pin0InfoVect0LinkObjId="g_17276a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1098ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1921,-291 1921,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10a56b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2946,-250 2946,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34106@0" ObjectIDZND0="g_10b4990@0" ObjectIDZND1="24212@x" Pin0InfoVect0LinkObjId="g_10b4990_0" Pin0InfoVect1LinkObjId="SW-309766_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-NH_HTP.061Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2946,-250 2946,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10a58a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2627,-406 2627,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24213@x" ObjectIDND1="g_1097d40@0" ObjectIDZND0="34107@0" Pin0InfoVect0LinkObjId="EC-NH_HTP.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-309765_0" Pin1InfoVect1LinkObjId="g_1097d40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2627,-406 2627,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10a5a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2311,-406 2311,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24214@x" ObjectIDND1="g_1105ba0@0" ObjectIDZND0="34108@0" Pin0InfoVect0LinkObjId="EC-NH_HTP.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-309764_0" Pin1InfoVect1LinkObjId="g_1105ba0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2311,-406 2311,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10a6100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,-252 1988,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="34109@0" ObjectIDZND0="24215@x" ObjectIDZND1="g_10ef9b0@0" ObjectIDZND2="g_1828ee0@0" Pin0InfoVect0LinkObjId="SW-309763_0" Pin0InfoVect1LinkObjId="g_10ef9b0_0" Pin0InfoVect2LinkObjId="g_1828ee0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-NH_HTP.064Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1988,-252 1988,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12cfe80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,-409 1988,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24215@x" ObjectIDND1="g_10ef9b0@0" ObjectIDZND0="34109@x" ObjectIDZND1="g_1828ee0@0" ObjectIDZND2="g_17276a0@0" Pin0InfoVect0LinkObjId="EC-NH_HTP.064Ld_0" Pin0InfoVect1LinkObjId="g_1828ee0_0" Pin0InfoVect2LinkObjId="g_17276a0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-309763_0" Pin1InfoVect1LinkObjId="g_10ef9b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1988,-409 1988,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d0070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,-379 1921,-379 1921,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="34109@x" ObjectIDND1="24215@x" ObjectIDND2="g_10ef9b0@0" ObjectIDZND0="g_1828ee0@0" ObjectIDZND1="g_17276a0@0" Pin0InfoVect0LinkObjId="g_1828ee0_0" Pin0InfoVect1LinkObjId="g_17276a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-NH_HTP.064Ld_0" Pin1InfoVect1LinkObjId="SW-309763_0" Pin1InfoVect2LinkObjId="g_10ef9b0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1988,-379 1921,-379 1921,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12eba50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,-1179 2673,-1220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44799@1" ObjectIDZND0="44801@0" Pin0InfoVect0LinkObjId="g_1011f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286505_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2673,-1179 2673,-1220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12ebcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2877,-1171 2877,-1220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44797@1" ObjectIDZND0="44801@0" Pin0InfoVect0LinkObjId="g_1011f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2877,-1171 2877,-1220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_130b9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2256,-1172 2256,-1220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24195@1" ObjectIDZND0="44801@0" Pin0InfoVect0LinkObjId="g_1011f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132006_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2256,-1172 2256,-1220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1684670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2256,-759 2256,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24197@0" ObjectIDZND0="24193@0" Pin0InfoVect0LinkObjId="g_10b0320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132052_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2256,-759 2256,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1080f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2846,-1469 2846,-1485 2924,-1485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_10bfe60@0" ObjectIDZND0="g_12ba610@0" ObjectIDZND1="34576@1" ObjectIDZND2="44796@x" Pin0InfoVect0LinkObjId="g_12ba610_0" Pin0InfoVect1LinkObjId="g_10123f0_1" Pin0InfoVect2LinkObjId="SW-286459_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10bfe60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2846,-1469 2846,-1485 2924,-1485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1081170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2924,-1485 2924,-1502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_10bfe60@0" ObjectIDND1="44796@x" ObjectIDND2="44795@x" ObjectIDZND0="g_12ba610@0" ObjectIDZND1="34576@1" Pin0InfoVect0LinkObjId="g_12ba610_0" Pin0InfoVect1LinkObjId="g_10123f0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_10bfe60_0" Pin1InfoVect1LinkObjId="SW-286459_0" Pin1InfoVect2LinkObjId="SW-286458_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2924,-1485 2924,-1502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10813d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2953,-1449 2924,-1449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44796@0" ObjectIDZND0="44795@x" ObjectIDZND1="g_10bfe60@0" ObjectIDZND2="g_12ba610@0" Pin0InfoVect0LinkObjId="SW-286458_0" Pin0InfoVect1LinkObjId="g_10bfe60_0" Pin0InfoVect2LinkObjId="g_12ba610_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286459_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2953,-1449 2924,-1449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1076e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2924,-1415 2924,-1449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44795@1" ObjectIDZND0="44796@x" ObjectIDZND1="g_10bfe60@0" ObjectIDZND2="g_12ba610@0" Pin0InfoVect0LinkObjId="SW-286459_0" Pin0InfoVect1LinkObjId="g_10bfe60_0" Pin0InfoVect2LinkObjId="g_12ba610_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-286458_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2924,-1415 2924,-1449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10770c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2924,-1449 2924,-1485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="44796@x" ObjectIDND1="44795@x" ObjectIDZND0="g_10bfe60@0" ObjectIDZND1="g_12ba610@0" ObjectIDZND2="34576@1" Pin0InfoVect0LinkObjId="g_10bfe60_0" Pin0InfoVect1LinkObjId="g_12ba610_0" Pin0InfoVect2LinkObjId="g_10123f0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-286459_0" Pin1InfoVect1LinkObjId="SW-286458_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2924,-1449 2924,-1485 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-131036" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1558.000000 -1285.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24023" ObjectName="DYN-NH_HTP"/>
     <cge:Meas_Ref ObjectId="131036"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11204e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2326.000000 1110.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1338f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2315.000000 1095.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1339120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2340.000000 1080.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f77890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2328.000000 840.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_168f0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2317.000000 825.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_168f2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2342.000000 810.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e1850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1833.000000 764.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10885c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1841.000000 794.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10887a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1841.000000 809.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_fb3560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1841.000000 825.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_fb3710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1840.000000 779.250000) translate(0,12)">U0(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1011370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2840.000000 454.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1011560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2829.000000 439.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1011710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2854.000000 424.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12daf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1770.000000 413.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12db080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1759.000000 398.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1024e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1784.000000 383.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10251f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2510.000000 448.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_111f200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2499.000000 433.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_111f3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2524.000000 418.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_111f600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2197.000000 427.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10af850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2186.000000 412.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10afaf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2211.000000 397.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f7d240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1971.000000 1261.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f7d730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1979.000000 1291.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_132d550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1979.000000 1306.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_132d790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1979.000000 1322.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_132d9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1978.000000 1276.250000) translate(0,12)">U0(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1155550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2430.000000 1359.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10f0b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2419.000000 1344.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10f0d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2444.000000 1329.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10f1120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3005.000000 1366.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10f13b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2994.000000 1351.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10f15f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3019.000000 1336.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1104e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2035.000000 860.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1105930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2035.000000 875.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-NH_HTP.NH_HTP_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34165"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2225.000000 -925.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2225.000000 -925.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24216" ObjectName="TF-NH_HTP.NH_HTP_1T"/>
    <cge:TPSR_Ref TObjectID="24216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1895.000000 -198.000000)" xlink:href="#transformer2:shape44_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1895.000000 -198.000000)" xlink:href="#transformer2:shape44_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1341ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1832.000000 -710.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_119ff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fe8cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3095.000000 -694.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fe8cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3095.000000 -694.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fe8cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3095.000000 -694.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fe8cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3095.000000 -694.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fe8cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3095.000000 -694.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_fe9810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1493.000000 -1377.000000) translate(0,16)">红土坡变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1359.000000 -797.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_fe9c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_fe9ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3148.000000 -678.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d7fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -679.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -556.000000) translate(0,12)">0642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f7dff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1995.000000 -450.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f7e1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2317.000000 -602.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f817b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2315.000000 -676.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f81990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2315.000000 -553.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1120910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2315.000000 -447.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1120af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2955.000000 -603.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_111e790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -677.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_111e970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -554.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_ff1ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2953.000000 -448.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_ff20b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2636.000000 -602.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_180ddf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2634.000000 -676.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_180dfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2634.000000 -553.000000) translate(0,12)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f6eec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2634.000000 -447.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f6f0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2265.000000 -829.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1145310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2263.000000 -784.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11454f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2275.000000 -1098.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_ffbe00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2271.000000 -1160.000000) translate(0,12)">3111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_180d930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1664.000000 -1353.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_10be720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1664.000000 -1390.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10be960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1362.000000 -1027.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_f7f1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2077.000000 -957.000000) translate(0,15)">35kV1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_f7f1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2077.000000 -957.000000) translate(0,33)">SZ11-5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_f7f1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2077.000000 -957.000000) translate(0,51)">Yn,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_ff1840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.000000 -346.000000) translate(0,15)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_ff1840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.000000 -346.000000) translate(0,33)">顶</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_ff1840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.000000 -346.000000) translate(0,51)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_ff1840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.000000 -346.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fe6990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -322.000000) translate(0,15)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fe6990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -322.000000) translate(0,33)">罗</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fe6990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -322.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12bbf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -396.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12bbf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -396.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12bc0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -406.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12bc0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -406.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12bc0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -406.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12bc320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -439.000000) translate(0,17)">7351251</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10de6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2903.000000 -331.000000) translate(0,15)">机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10de6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2903.000000 -331.000000) translate(0,33)">关</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10de6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2903.000000 -331.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18d5980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2955.000000 -1556.000000) translate(0,15)">35KV大红线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18d5f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2393.000000 -1547.000000) translate(0,15)">35KV马红线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18d61f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2207.000000 -1369.000000) translate(0,15)">线路电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1025bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2762.000000 -1376.000000) translate(0,15)">线路电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fb2e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2713.000000 -1011.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fb2e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2713.000000 -1011.000000) translate(0,33)">S13-50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_fb2e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2713.000000 -1011.000000) translate(0,51)">D,yn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16a7a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1766.000000 -290.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16a7f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1766.000000 -276.000000) translate(0,15)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16a7f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1766.000000 -276.000000) translate(0,33)">SZ13-50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16a7f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1766.000000 -276.000000) translate(0,51)">D.yn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10126c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1974.000000 -1202.000000) translate(0,12)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1012920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2139.000000 -988.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1012d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2263.000000 -1052.000000) translate(0,12)">3116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1012f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2376.000000 -1348.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1013170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2374.000000 -1282.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10133b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2374.000000 -1404.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1681f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2399.000000 -1475.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1682160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2933.000000 -1348.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16823a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2931.000000 -1282.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16825e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2931.000000 -1404.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1682820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2956.000000 -1475.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1682a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2680.000000 -1168.000000) translate(0,12)">3801</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1682ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2884.000000 -1160.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11546d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2924.000000 -1135.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10a5c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2016.000000 -319.000000) translate(0,15)">簪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10a5c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2016.000000 -319.000000) translate(0,33)">花</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_10a5c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2016.000000 -319.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_132f3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1701.500000 -1303.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_182a920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1997.000000 -605.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1684d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2839.000000 -894.000000) translate(0,15)">35kV母线电压互感器</text>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_133ec70">
    <use class="BV-35KV" transform="matrix(-3.050000 -0.000000 -0.000000 3.080000 2898.000000 -980.000000)" xlink:href="#voltageTransformer:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24193" cx="3138" cy="-725" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24193" cx="1988" cy="-725" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24193" cx="2311" cy="-725" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24193" cx="2627" cy="-725" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24193" cx="2946" cy="-725" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44801" cx="2924" cy="-1220" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44801" cx="2673" cy="-1220" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44801" cx="2877" cy="-1220" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="44801" cx="2256" cy="-1220" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24193" cx="2256" cy="-725" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="NH_HTP"/>
</svg>