<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-280" aopId="2884606" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1241 2120 1290">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape49">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.766467" x1="5" x2="5" y1="137" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="56" x2="43" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="43" x2="43" y1="94" y2="105"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="24" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="61" y2="57"/>
    <polyline arcFlag="1" points="60,24 61,24 62,24 62,24 63,24 63,25 64,25 64,26 65,26 65,27 65,27 65,28 66,29 66,29 66,30 65,31 65,31 65,32 65,32 64,33 64,33 63,34 63,34 62,34 62,35 61,35 60,35 " stroke-width="1"/>
    <polyline arcFlag="1" points="60,46 61,46 62,46 62,47 63,47 63,47 64,48 64,48 65,49 65,49 65,50 65,50 66,51 66,52 66,52 65,53 65,54 65,54 65,55 64,55 64,56 63,56 63,57 62,57 62,57 61,57 60,57 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.24021" x1="43" x2="43" y1="81" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="26" x2="59" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="43" x2="60" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="43" x2="60" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="26" x2="26" y1="12" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="59" x2="59" y1="12" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="36" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="35" x2="51" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="51" y1="43" y2="43"/>
    <polyline arcFlag="1" points="60,35 61,35 62,35 62,35 63,36 63,36 64,36 64,37 65,37 65,38 65,39 65,39 66,40 66,41 66,41 65,42 65,43 65,43 65,44 64,44 64,45 63,45 63,45 62,46 62,46 61,46 60,46 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,94 41,94 39,93 38,93 36,92 35,91 33,90 32,88 31,86 31,85 30,83 30,81 30,79 31,77 31,76 32,74 33,73 35,71 36,70 38,69 39,69 41,68 43,68 45,68 47,69 48,69 50,70 51,71 53,73 54,74 55,76 55,77 56,79 56,81 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0856224" x1="43" x2="43" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="5" x2="43" y1="2" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape6_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="38" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="38" y2="13"/>
   </symbol>
   <symbol id="switch2:shape6_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="13" y2="14"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="11" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape37_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="21" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="4" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="4" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape36">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline points="19,9 35,9 35,22 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="18" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="7" x2="5" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="voltageTransformer:shape143">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="31" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.352502" x1="19" x2="19" y1="29" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="28" x2="28" y1="29" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="8" x2="8" y1="34" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="20" x2="20" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="7" x2="13" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="10" x2="7" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="10" x2="13" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="20" x2="20" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="19" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="28" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="28" y1="13" y2="11"/>
    <circle cx="11" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="19" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="28" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="19" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="19" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="4" x2="4" y1="32" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="18" y2="18"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="8,29 28,29 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2391bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2392760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2392e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2393d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2394dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2395a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2396610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2397010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d52f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d52f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_239a1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_239a1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_239c020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_239c020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_239d030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_239ecc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_239f910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23a07f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23a10d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23a2ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23a3530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23a3df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23a45b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23a5690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23a6010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23a6b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23a74c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23a89b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23a94d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_23aa520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ab170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23b9480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23aca70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23ae160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23af740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1300" width="2130" x="3112" y="-1246"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3632" x2="3692" y1="-203" y2="-203"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3658" x2="3658" y1="-203" y2="-195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3632" x2="3632" y1="-203" y2="-195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3632" x2="3632" y1="-161" y2="-149"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3633" x2="3633" y1="-132" y2="-123"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3626" x2="3640" y1="-123" y2="-123"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3628" x2="3638" y1="-120" y2="-120"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3631" x2="3636" y1="-117" y2="-117"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3924" x2="3963" y1="-223" y2="-223"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3947" x2="3966" y1="-199" y2="-199"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3947" x2="3947" y1="-207" y2="-199"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4073" x2="4113" y1="-223" y2="-223"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4097" x2="4116" y1="-199" y2="-199"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4097" x2="4097" y1="-207" y2="-199"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4517.000000 -797.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231497">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.638298 -959.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38590" ObjectName="SW-CX_HD.CX_HD_151BK"/>
     <cge:Meas_Ref ObjectId="231497"/>
    <cge:TPSR_Ref TObjectID="38590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231542">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -516.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38600" ObjectName="SW-CX_HD.CX_HD_001BK"/>
     <cge:Meas_Ref ObjectId="231542"/>
    <cge:TPSR_Ref TObjectID="38600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3682.000000 -373.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38603" ObjectName="SW-CX_HD.CX_HD_051BK"/>
     <cge:Meas_Ref ObjectId="231665"/>
    <cge:TPSR_Ref TObjectID="38603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231672">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -374.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38608" ObjectName="SW-CX_HD.CX_HD_052BK"/>
     <cge:Meas_Ref ObjectId="231672"/>
    <cge:TPSR_Ref TObjectID="38608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3957.000000 -376.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38612" ObjectName="SW-CX_HD.CX_HD_053BK"/>
     <cge:Meas_Ref ObjectId="231678"/>
    <cge:TPSR_Ref TObjectID="38612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231686">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -377.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38618" ObjectName="SW-CX_HD.CX_HD_054BK"/>
     <cge:Meas_Ref ObjectId="231686"/>
    <cge:TPSR_Ref TObjectID="38618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231694">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4246.000000 -374.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38624" ObjectName="SW-CX_HD.CX_HD_055BK"/>
     <cge:Meas_Ref ObjectId="231694"/>
    <cge:TPSR_Ref TObjectID="38624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231702">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.000000 -375.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38629" ObjectName="SW-CX_HD.CX_HD_056BK"/>
     <cge:Meas_Ref ObjectId="231702"/>
    <cge:TPSR_Ref TObjectID="38629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231710">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 -375.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38634" ObjectName="SW-CX_HD.CX_HD_057BK"/>
     <cge:Meas_Ref ObjectId="231710"/>
    <cge:TPSR_Ref TObjectID="38634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231718">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4652.000000 -376.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38639" ObjectName="SW-CX_HD.CX_HD_058BK"/>
     <cge:Meas_Ref ObjectId="231718"/>
    <cge:TPSR_Ref TObjectID="38639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231726">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -377.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38644" ObjectName="SW-CX_HD.CX_HD_059BK"/>
     <cge:Meas_Ref ObjectId="231726"/>
    <cge:TPSR_Ref TObjectID="38644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4943.000000 -365.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_HD.CX_HD_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="57939"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -649.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="57941"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -649.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="57943"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -649.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="38651" ObjectName="TF-CX_HD.CX_HD_1T"/>
    <cge:TPSR_Ref TObjectID="38651"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_HD.CX_HD_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-465 4886,-465 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38588" ObjectName="BS-CX_HD.CX_HD_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="38588"/></metadata>
   <polyline fill="none" opacity="0" points="3640,-465 4886,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HD.CX_HD_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4102,-882 4512,-882 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38587" ObjectName="BS-CX_HD.CX_HD_1IM"/>
    <cge:TPSR_Ref TObjectID="38587"/></metadata>
   <polyline fill="none" opacity="0" points="4102,-882 4512,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4568,-882 4805,-882 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4568,-882 4805,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-465 5237,-465 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5000,-465 5237,-465 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_HD.CX_HD_2C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4073.000000 -44.000000)" xlink:href="#capacitor:shape49"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49446" ObjectName="CB-CX_HD.CX_HD_2C"/>
    <cge:TPSR_Ref TObjectID="49446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_HD.CX_HD_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.858156 3923.000000 -62.000000)" xlink:href="#capacitor:shape49"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49445" ObjectName="CB-CX_HD.CX_HD_1C"/>
    <cge:TPSR_Ref TObjectID="49445"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3801.000000 -129.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3801.000000 -129.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1af85b0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4241.000000 -650.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c087f0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4267.000000 -653.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd7590">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3844.000000 -537.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd7d60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3881.000000 -531.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b52690">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.067797 3652.000000 -137.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b0b940">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4348.000000 -597.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b8f910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4259.000000 -621.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b2cd60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -180.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b2da30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 -265.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd29b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3711.000000 -271.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd36e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3806.000000 -266.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ae03b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.000000 -272.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a63500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.000000 -268.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a80f50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -274.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b15370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -269.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1af0f40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4136.000000 -275.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1af4b90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -266.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a8a2d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4275.000000 -272.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a8df20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4398.000000 -267.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ace970">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4428.000000 -273.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ad25c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 -267.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19d9260">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -273.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19dc4b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4651.000000 -268.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19e4f20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4681.000000 -274.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a657e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -269.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a6f150">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 -275.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b41e90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 -186.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b4a0e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 -186.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c52240">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4231.000000 -121.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c52d70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.000000 -118.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c53aa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4510.000000 -118.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c547d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4637.000000 -118.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c55500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4765.000000 -118.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a9ec00">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4308.000000 -1117.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3226.000000 -1119.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3259.538462 -960.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3259.538462 -919.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3258.538462 -1041.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3258.538462 -1002.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-231412" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -980.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231412" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38587"/>
     <cge:Term_Ref ObjectID="57812"/>
    <cge:TPSR_Ref TObjectID="38587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-231413" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -980.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231413" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38587"/>
     <cge:Term_Ref ObjectID="57812"/>
    <cge:TPSR_Ref TObjectID="38587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-231414" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -980.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231414" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38587"/>
     <cge:Term_Ref ObjectID="57812"/>
    <cge:TPSR_Ref TObjectID="38587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-231418" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -980.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231418" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38587"/>
     <cge:Term_Ref ObjectID="57812"/>
    <cge:TPSR_Ref TObjectID="38587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-231415" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -980.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231415" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38587"/>
     <cge:Term_Ref ObjectID="57812"/>
    <cge:TPSR_Ref TObjectID="38587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-231419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -980.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231419" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38587"/>
     <cge:Term_Ref ObjectID="57812"/>
    <cge:TPSR_Ref TObjectID="38587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-231420" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3687.000000 -558.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231420" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38588"/>
     <cge:Term_Ref ObjectID="57813"/>
    <cge:TPSR_Ref TObjectID="38588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-231421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3687.000000 -558.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231421" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38588"/>
     <cge:Term_Ref ObjectID="57813"/>
    <cge:TPSR_Ref TObjectID="38588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-231422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3687.000000 -558.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231422" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38588"/>
     <cge:Term_Ref ObjectID="57813"/>
    <cge:TPSR_Ref TObjectID="38588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-231426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3687.000000 -558.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38588"/>
     <cge:Term_Ref ObjectID="57813"/>
    <cge:TPSR_Ref TObjectID="38588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-231423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3687.000000 -558.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38588"/>
     <cge:Term_Ref ObjectID="57813"/>
    <cge:TPSR_Ref TObjectID="38588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231381" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4350.000000 -1240.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231381" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38590"/>
     <cge:Term_Ref ObjectID="57816"/>
    <cge:TPSR_Ref TObjectID="38590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231382" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4350.000000 -1240.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231382" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38590"/>
     <cge:Term_Ref ObjectID="57816"/>
    <cge:TPSR_Ref TObjectID="38590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231372" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4350.000000 -1240.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231372" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38590"/>
     <cge:Term_Ref ObjectID="57816"/>
    <cge:TPSR_Ref TObjectID="38590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4466.000000 -559.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38600"/>
     <cge:Term_Ref ObjectID="57836"/>
    <cge:TPSR_Ref TObjectID="38600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4466.000000 -559.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38600"/>
     <cge:Term_Ref ObjectID="57836"/>
    <cge:TPSR_Ref TObjectID="38600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231396" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4466.000000 -559.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231396" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38600"/>
     <cge:Term_Ref ObjectID="57836"/>
    <cge:TPSR_Ref TObjectID="38600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3615.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231430" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38603"/>
     <cge:Term_Ref ObjectID="57842"/>
    <cge:TPSR_Ref TObjectID="38603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3615.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38603"/>
     <cge:Term_Ref ObjectID="57842"/>
    <cge:TPSR_Ref TObjectID="38603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231427" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3615.000000 4.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231427" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38603"/>
     <cge:Term_Ref ObjectID="57842"/>
    <cge:TPSR_Ref TObjectID="38603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231436" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3777.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231436" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38608"/>
     <cge:Term_Ref ObjectID="57852"/>
    <cge:TPSR_Ref TObjectID="38608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231437" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3777.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231437" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38608"/>
     <cge:Term_Ref ObjectID="57852"/>
    <cge:TPSR_Ref TObjectID="38608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231433" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3777.000000 4.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231433" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38608"/>
     <cge:Term_Ref ObjectID="57852"/>
    <cge:TPSR_Ref TObjectID="38608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38612"/>
     <cge:Term_Ref ObjectID="57860"/>
    <cge:TPSR_Ref TObjectID="38612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38612"/>
     <cge:Term_Ref ObjectID="57860"/>
    <cge:TPSR_Ref TObjectID="38612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231439" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3921.000000 4.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231439" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38612"/>
     <cge:Term_Ref ObjectID="57860"/>
    <cge:TPSR_Ref TObjectID="38612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231448" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4076.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38618"/>
     <cge:Term_Ref ObjectID="57872"/>
    <cge:TPSR_Ref TObjectID="38618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4076.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38618"/>
     <cge:Term_Ref ObjectID="57872"/>
    <cge:TPSR_Ref TObjectID="38618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4076.000000 4.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38618"/>
     <cge:Term_Ref ObjectID="57872"/>
    <cge:TPSR_Ref TObjectID="38618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4213.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38624"/>
     <cge:Term_Ref ObjectID="57884"/>
    <cge:TPSR_Ref TObjectID="38624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231455" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4213.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38624"/>
     <cge:Term_Ref ObjectID="57884"/>
    <cge:TPSR_Ref TObjectID="38624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4213.000000 4.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38624"/>
     <cge:Term_Ref ObjectID="57884"/>
    <cge:TPSR_Ref TObjectID="38624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231460" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38629"/>
     <cge:Term_Ref ObjectID="57894"/>
    <cge:TPSR_Ref TObjectID="38629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38629"/>
     <cge:Term_Ref ObjectID="57894"/>
    <cge:TPSR_Ref TObjectID="38629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 4.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38629"/>
     <cge:Term_Ref ObjectID="57894"/>
    <cge:TPSR_Ref TObjectID="38629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38634"/>
     <cge:Term_Ref ObjectID="57904"/>
    <cge:TPSR_Ref TObjectID="38634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38634"/>
     <cge:Term_Ref ObjectID="57904"/>
    <cge:TPSR_Ref TObjectID="38634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 4.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38634"/>
     <cge:Term_Ref ObjectID="57904"/>
    <cge:TPSR_Ref TObjectID="38634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4623.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38639"/>
     <cge:Term_Ref ObjectID="57914"/>
    <cge:TPSR_Ref TObjectID="38639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4623.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38639"/>
     <cge:Term_Ref ObjectID="57914"/>
    <cge:TPSR_Ref TObjectID="38639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4623.000000 4.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38639"/>
     <cge:Term_Ref ObjectID="57914"/>
    <cge:TPSR_Ref TObjectID="38639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 4.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38644"/>
     <cge:Term_Ref ObjectID="57924"/>
    <cge:TPSR_Ref TObjectID="38644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 4.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38644"/>
     <cge:Term_Ref ObjectID="57924"/>
    <cge:TPSR_Ref TObjectID="38644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 4.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38644"/>
     <cge:Term_Ref ObjectID="57924"/>
    <cge:TPSR_Ref TObjectID="38644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231393" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -843.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38596"/>
     <cge:Term_Ref ObjectID="57828"/>
    <cge:TPSR_Ref TObjectID="38596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231394" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -843.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231394" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38596"/>
     <cge:Term_Ref ObjectID="57828"/>
    <cge:TPSR_Ref TObjectID="38596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="43" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231384" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -843.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38596"/>
     <cge:Term_Ref ObjectID="57828"/>
    <cge:TPSR_Ref TObjectID="38596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-231409" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4456.000000 -676.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231409" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38651"/>
     <cge:Term_Ref ObjectID="57940"/>
    <cge:TPSR_Ref TObjectID="38651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-231410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4456.000000 -676.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38651"/>
     <cge:Term_Ref ObjectID="57940"/>
    <cge:TPSR_Ref TObjectID="38651"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3238" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3238" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3189" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3189" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4393" y="-988"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4393" y="-988"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="4399" y="-708"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="4399" y="-708"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3700" y="-402"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3700" y="-402"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3825" y="-403"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3825" y="-403"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3975" y="-405"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3975" y="-405"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4125" y="-406"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4125" y="-406"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4264" y="-403"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4264" y="-403"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4417" y="-404"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4417" y="-404"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4543" y="-404"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4543" y="-404"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4670" y="-405"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4670" y="-405"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4798" y="-406"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4798" y="-406"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="100" x="3182" y="-813"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="100" x="3182" y="-813"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3238" y="-1178"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3189" y="-1195"/></g>
   <g href="110kV后甸变CX_HD_151间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4393" y="-988"/></g>
   <g href="110kV后甸变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="4399" y="-708"/></g>
   <g href="110kV后甸变CX_HD_051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3700" y="-402"/></g>
   <g href="110kV后甸变CX_HD_052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3825" y="-403"/></g>
   <g href="110kV后甸变CX_HD_053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3975" y="-405"/></g>
   <g href="110kV后甸变CX_HD_054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4125" y="-406"/></g>
   <g href="110kV后甸变CX_HD_055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4264" y="-403"/></g>
   <g href="110kV后甸变CX_HD_056间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4417" y="-404"/></g>
   <g href="110kV后甸变CX_HD_057间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4543" y="-404"/></g>
   <g href="110kV后甸变CX_HD_058间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4670" y="-405"/></g>
   <g href="110kV后甸变CX_HD_059间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4798" y="-406"/></g>
   <g href="110kV后甸变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="100" x="3182" y="-813"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3174" y="-1194"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="17" stroke="rgb(50,205,50)" stroke-width="1" width="7" x="3629" y="-149"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1bfa710">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3871.000000 -578.000000)" xlink:href="#voltageTransformer:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b85550">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -1089.000000)" xlink:href="#voltageTransformer:shape143"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_HD.CX_HD_055LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4246.000000 -106.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49447" ObjectName="EC-CX_HD.CX_HD_055LD"/>
    <cge:TPSR_Ref TObjectID="49447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HD.CX_HD_056LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.000000 -104.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49448" ObjectName="EC-CX_HD.CX_HD_056LD"/>
    <cge:TPSR_Ref TObjectID="49448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HD.CX_HD_057LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 -101.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49449" ObjectName="EC-CX_HD.CX_HD_057LD"/>
    <cge:TPSR_Ref TObjectID="49449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HD.CX_HD_058LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4652.000000 -104.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49450" ObjectName="EC-CX_HD.CX_HD_058LD"/>
    <cge:TPSR_Ref TObjectID="49450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HD.CX_HD_059LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -102.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49451" ObjectName="EC-CX_HD.CX_HD_059LD"/>
    <cge:TPSR_Ref TObjectID="49451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HD.CX_HD_051LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3682.000000 -214.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49498" ObjectName="EC-CX_HD.CX_HD_051LD"/>
    <cge:TPSR_Ref TObjectID="49498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HD.CX_HD_052LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -221.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49499" ObjectName="EC-CX_HD.CX_HD_052LD"/>
    <cge:TPSR_Ref TObjectID="49499"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_1bf86f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4247,-720 4247,-707 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38599@x" ObjectIDND1="38651@x" ObjectIDND2="g_1c087f0@0" ObjectIDZND0="g_1af85b0@0" Pin0InfoVect0LinkObjId="g_1af85b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-231540_0" Pin1InfoVect1LinkObjId="g_1b8d9f0_0" Pin1InfoVect2LinkObjId="g_1c087f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4247,-720 4247,-707 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf95a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4218,-657 4218,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1839390@0" ObjectIDZND0="38599@1" Pin0InfoVect0LinkObjId="SW-231540_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1839390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4218,-657 4218,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf9790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4493,-881 4493,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38587@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4493,-881 4493,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bf9980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4493,-754 4493,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1bf1ce0@0" Pin0InfoVect0LinkObjId="g_1bf1ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4493,-754 4493,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bf9b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4584,-882 4584,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4584,-882 4584,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bf9d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4584,-755 4584,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1af78b0@0" Pin0InfoVect0LinkObjId="g_1af78b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4584,-755 4584,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bf9f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4493,-822 4493,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4493,-822 4493,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bfa140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4493,-807 4493,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4493,-807 4493,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bfa330">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="4553,-807 4584,-807 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-807 4584,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bfa520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-782 4321,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="38596@x" ObjectIDND1="38651@x" ObjectIDZND0="38598@1" Pin0InfoVect0LinkObjId="SW-231539_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231537_0" Pin1InfoVect1LinkObjId="g_1b8d9f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-782 4321,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd8350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3890,-580 3890,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1bfa710@0" ObjectIDZND0="g_1bd7d60@0" Pin0InfoVect0LinkObjId="g_1bd7d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bfa710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3890,-580 3890,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b51720">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="4493,-807 4526,-807 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4493,-807 4526,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b51910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4584,-791 4584,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4584,-791 4584,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b51b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4584,-807 4584,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4584,-807 4584,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b51cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4285,-782 4273,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38598@0" ObjectIDZND0="g_1b51ee0@0" Pin0InfoVect0LinkObjId="g_1b51ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231539_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4285,-782 4273,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b113b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-881 4384,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38587@0" ObjectIDZND0="38591@0" Pin0InfoVect0LinkObjId="SW-231498_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-881 4384,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a84b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1077 4321,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38595@0" ObjectIDZND0="g_1b29340@0" Pin0InfoVect0LinkObjId="g_1b29340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231502_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1077 4321,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a84d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1009 4321,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38594@0" ObjectIDZND0="g_1b10450@0" Pin0InfoVect0LinkObjId="g_1b10450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231501_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1009 4321,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a84f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-952 4321,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38593@0" ObjectIDZND0="g_1b10c00@0" Pin0InfoVect0LinkObjId="g_1b10c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-952 4321,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a851b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,-1077 4384,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="38595@1" ObjectIDZND0="38592@x" ObjectIDZND1="g_1b85550@0" ObjectIDZND2="g_1a9ec00@0" Pin0InfoVect0LinkObjId="SW-231499_0" Pin0InfoVect1LinkObjId="g_1b85550_0" Pin0InfoVect2LinkObjId="g_1a9ec00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231502_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4368,-1077 4384,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a853d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,-952 4384,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="38593@1" ObjectIDZND0="38591@x" ObjectIDZND1="38590@x" Pin0InfoVect0LinkObjId="SW-231498_0" Pin0InfoVect1LinkObjId="SW-231497_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4368,-952 4384,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a855f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-952 4384,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="38591@x" ObjectIDND1="38593@x" ObjectIDZND0="38590@0" Pin0InfoVect0LinkObjId="SW-231497_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231498_0" Pin1InfoVect1LinkObjId="SW-231500_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-952 4384,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b780d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,-1009 4384,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="38594@1" ObjectIDZND0="38590@x" ObjectIDZND1="38592@x" Pin0InfoVect0LinkObjId="SW-231497_0" Pin0InfoVect1LinkObjId="SW-231499_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231501_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4368,-1009 4384,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b782c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-1009 4384,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="38594@x" ObjectIDND1="38592@x" ObjectIDZND0="38590@1" Pin0InfoVect0LinkObjId="SW-231497_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231501_0" Pin1InfoVect1LinkObjId="SW-231499_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-1009 4384,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b84ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-933 4384,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="38591@1" ObjectIDZND0="38590@x" ObjectIDZND1="38593@x" Pin0InfoVect0LinkObjId="SW-231497_0" Pin0InfoVect1LinkObjId="SW-231500_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231498_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-933 4384,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b85110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-1010 4384,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="38594@x" ObjectIDND1="38590@x" ObjectIDZND0="38592@0" Pin0InfoVect0LinkObjId="SW-231499_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231501_0" Pin1InfoVect1LinkObjId="SW-231497_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-1010 4384,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b85330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-1060 4384,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="38592@1" ObjectIDZND0="38595@x" ObjectIDZND1="g_1b85550@0" ObjectIDZND2="g_1a9ec00@0" Pin0InfoVect0LinkObjId="SW-231502_0" Pin0InfoVect1LinkObjId="g_1b85550_0" Pin0InfoVect2LinkObjId="g_1a9ec00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231499_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-1060 4384,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b09980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-853 4321,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="38587@0" ObjectIDND1="38596@x" ObjectIDZND0="38597@1" Pin0InfoVect0LinkObjId="SW-231538_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-231537_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-853 4321,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8c630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4285,-853 4273,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38597@0" ObjectIDZND0="g_1b8c850@0" Pin0InfoVect0LinkObjId="g_1b8c850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231538_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4285,-853 4273,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8d9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-782 4335,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="38598@x" ObjectIDND1="38596@x" ObjectIDZND0="38651@1" Pin0InfoVect0LinkObjId="g_1b8f570_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231539_0" Pin1InfoVect1LinkObjId="SW-231537_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-782 4335,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8dc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-801 4335,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="38596@1" ObjectIDZND0="38598@x" ObjectIDZND1="38651@x" Pin0InfoVect0LinkObjId="SW-231539_0" Pin0InfoVect1LinkObjId="g_1b8d9f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231537_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-801 4335,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8e6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-881 4335,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38587@0" ObjectIDZND0="38597@x" ObjectIDZND1="38596@x" Pin0InfoVect0LinkObjId="SW-231538_0" Pin0InfoVect1LinkObjId="SW-231537_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-881 4335,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8e8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-853 4335,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="38597@x" ObjectIDND1="38587@0" ObjectIDZND0="38596@0" Pin0InfoVect0LinkObjId="SW-231537_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231538_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-853 4335,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8eae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4274,-720 4274,-707 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="38651@x" ObjectIDND1="g_1af85b0@0" ObjectIDND2="38599@x" ObjectIDZND0="g_1c087f0@0" Pin0InfoVect0LinkObjId="g_1c087f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b8d9f0_0" Pin1InfoVect1LinkObjId="g_1af85b0_0" Pin1InfoVect2LinkObjId="SW-231540_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4274,-720 4274,-707 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8f570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4274,-720 4335,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="g_1af85b0@0" ObjectIDND1="38599@x" ObjectIDND2="g_1c087f0@0" ObjectIDZND0="38651@x" Pin0InfoVect0LinkObjId="g_1b8d9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1af85b0_0" Pin1InfoVect1LinkObjId="SW-231540_0" Pin1InfoVect2LinkObjId="g_1c087f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4274,-720 4335,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ba1380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4218,-707 4218,-720 4247,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="38599@0" ObjectIDZND0="g_1af85b0@0" ObjectIDZND1="38651@x" ObjectIDZND2="g_1c087f0@0" Pin0InfoVect0LinkObjId="g_1af85b0_0" Pin0InfoVect1LinkObjId="g_1b8d9f0_0" Pin0InfoVect2LinkObjId="g_1c087f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4218,-707 4218,-720 4247,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ba15a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4247,-720 4274,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="g_1af85b0@0" ObjectIDND1="38599@x" ObjectIDZND0="38651@x" ObjectIDZND1="g_1c087f0@0" Pin0InfoVect0LinkObjId="g_1b8d9f0_0" Pin0InfoVect1LinkObjId="g_1c087f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1af85b0_0" Pin1InfoVect1LinkObjId="SW-231540_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4247,-720 4274,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba17c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4316,-628 4334,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1b8f910@0" ObjectIDZND0="38651@x" ObjectIDZND1="g_1b0b940@0" ObjectIDZND2="38602@x" Pin0InfoVect0LinkObjId="g_1b8d9f0_0" Pin0InfoVect1LinkObjId="g_1b0b940_0" Pin0InfoVect2LinkObjId="SW-231543_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b8f910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4316,-628 4334,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba2250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-652 4334,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38651@2" ObjectIDZND0="g_1b8f910@0" ObjectIDZND1="g_1b0b940@0" ObjectIDZND2="38602@x" Pin0InfoVect0LinkObjId="g_1b8f910_0" Pin0InfoVect1LinkObjId="g_1b0b940_0" Pin0InfoVect2LinkObjId="SW-231543_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b8d9f0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-652 4334,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b2cb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3691,-270 3691,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1b2da30@0" ObjectIDZND0="49498@0" Pin0InfoVect0LinkObjId="EC-CX_HD.CX_HD_051LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b2da30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3691,-270 3691,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba72a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3691,-464 3691,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38588@0" ObjectIDZND0="38604@0" Pin0InfoVect0LinkObjId="SW-231666_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3691,-464 3691,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba7500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3691,-422 3691,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38604@1" ObjectIDZND0="38603@1" Pin0InfoVect0LinkObjId="SW-231665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231666_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3691,-422 3691,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba7760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3691,-381 3691,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38603@0" ObjectIDZND0="38605@1" Pin0InfoVect0LinkObjId="SW-231666_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3691,-381 3691,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba79c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3691,-352 3691,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38605@0" ObjectIDZND0="g_1b2da30@1" Pin0InfoVect0LinkObjId="g_1b2da30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3691,-352 3691,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd24e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3661,-280 3661,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1bd1a90@0" ObjectIDZND0="38607@0" Pin0InfoVect0LinkObjId="SW-231668_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bd1a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3661,-280 3661,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd2740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3718,-325 3718,-336 3661,-336 3661,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1bd29b0@0" ObjectIDZND0="38607@1" Pin0InfoVect0LinkObjId="SW-231668_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bd29b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3718,-325 3718,-336 3661,-336 3661,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1adfee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3786,-281 3786,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1adf490@0" ObjectIDZND0="38611@0" Pin0InfoVect0LinkObjId="SW-231674_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1adf490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3786,-281 3786,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ae0140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-326 3843,-337 3786,-337 3786,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1ae03b0@0" ObjectIDZND0="38611@1" Pin0InfoVect0LinkObjId="SW-231674_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ae03b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-326 3843,-337 3786,-337 3786,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a61020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-583 4334,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="38602@0" ObjectIDZND0="g_1b8f910@0" ObjectIDZND1="38651@x" ObjectIDZND2="g_1b0b940@0" Pin0InfoVect0LinkObjId="g_1b8f910_0" Pin0InfoVect1LinkObjId="g_1b8d9f0_0" Pin0InfoVect2LinkObjId="g_1b0b940_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231543_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-583 4334,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a61280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-551 4334,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38600@1" ObjectIDZND0="38602@1" Pin0InfoVect0LinkObjId="SW-231543_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231542_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-551 4334,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a61d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3851,-541 3851,-523 3890,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1bd7590@0" ObjectIDZND0="g_1bd7d60@0" ObjectIDZND1="38649@x" Pin0InfoVect0LinkObjId="g_1bd7d60_0" Pin0InfoVect1LinkObjId="SW-231734_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bd7590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3851,-541 3851,-523 3890,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a61fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3890,-523 3890,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1bd7590@0" ObjectIDND1="38649@x" ObjectIDZND0="g_1bd7d60@1" Pin0InfoVect0LinkObjId="g_1bd7d60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bd7590_0" Pin1InfoVect1LinkObjId="SW-231734_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3890,-523 3890,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a62210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3890,-523 3890,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1bd7590@0" ObjectIDND1="g_1bd7d60@0" ObjectIDZND0="38649@1" Pin0InfoVect0LinkObjId="SW-231734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bd7590_0" Pin1InfoVect1LinkObjId="g_1bd7d60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3890,-523 3890,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a62470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3890,-481 3890,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38649@0" ObjectIDZND0="38588@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231734_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3890,-481 3890,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a626d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-353 3816,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38610@0" ObjectIDZND0="g_1bd36e0@1" Pin0InfoVect0LinkObjId="g_1bd36e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-353 3816,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a80a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-283 3936,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a80030@0" ObjectIDZND0="38617@0" Pin0InfoVect0LinkObjId="SW-231682_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a80030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3936,-283 3936,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a80ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3993,-328 3993,-339 3936,-339 3936,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a80f50@0" ObjectIDZND0="38617@1" Pin0InfoVect0LinkObjId="SW-231682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a80f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3993,-328 3993,-339 3936,-339 3936,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b14530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-384 3966,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38612@0" ObjectIDZND0="38614@1" Pin0InfoVect0LinkObjId="SW-231679_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231678_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-384 3966,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b14790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-425 3966,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38613@1" ObjectIDZND0="38612@1" Pin0InfoVect0LinkObjId="SW-231678_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231679_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-425 3966,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b149f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-382 3816,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38608@0" ObjectIDZND0="38610@1" Pin0InfoVect0LinkObjId="SW-231673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-382 3816,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b14c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-464 3816,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38588@0" ObjectIDZND0="38609@0" Pin0InfoVect0LinkObjId="SW-231673_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-464 3816,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b14eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-423 3816,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38609@1" ObjectIDZND0="38608@1" Pin0InfoVect0LinkObjId="SW-231672_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231673_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-423 3816,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b15110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-464 3966,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38588@0" ObjectIDZND0="38613@0" Pin0InfoVect0LinkObjId="SW-231679_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-464 3966,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af0a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4086,-284 4086,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1af0020@0" ObjectIDZND0="38623@0" Pin0InfoVect0LinkObjId="SW-231690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1af0020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4086,-284 4086,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af0cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-329 4143,-340 4086,-340 4086,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1af0f40@0" ObjectIDZND0="38623@1" Pin0InfoVect0LinkObjId="SW-231690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1af0f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-329 4143,-340 4086,-340 4086,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af4470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-385 4116,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38618@0" ObjectIDZND0="38620@1" Pin0InfoVect0LinkObjId="SW-231687_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231686_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-385 4116,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af46d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-426 4116,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38619@1" ObjectIDZND0="38618@1" Pin0InfoVect0LinkObjId="SW-231686_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231687_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-426 4116,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1af4930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-464 4116,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38588@0" ObjectIDZND0="38619@0" Pin0InfoVect0LinkObjId="SW-231687_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-464 4116,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a89e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-281 4225,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a893b0@0" ObjectIDZND0="38628@0" Pin0InfoVect0LinkObjId="SW-231697_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a893b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-281 4225,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8a060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4282,-326 4282,-337 4225,-337 4225,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a8a2d0@0" ObjectIDZND0="38628@1" Pin0InfoVect0LinkObjId="SW-231697_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a8a2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4282,-326 4282,-337 4225,-337 4225,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8d800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-382 4255,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38624@0" ObjectIDZND0="38626@1" Pin0InfoVect0LinkObjId="SW-231695_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231694_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-382 4255,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8da60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-423 4255,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38625@1" ObjectIDZND0="38624@1" Pin0InfoVect0LinkObjId="SW-231694_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231695_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-423 4255,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a8dcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-464 4255,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38588@0" ObjectIDZND0="38625@0" Pin0InfoVect0LinkObjId="SW-231695_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-464 4255,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ace4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-282 4378,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1acda50@0" ObjectIDZND0="38633@0" Pin0InfoVect0LinkObjId="SW-231705_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acda50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-282 4378,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ace700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4435,-327 4435,-338 4378,-338 4378,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1ace970@0" ObjectIDZND0="38633@1" Pin0InfoVect0LinkObjId="SW-231705_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ace970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4435,-327 4435,-338 4378,-338 4378,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ad1ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-383 4408,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38629@0" ObjectIDZND0="38631@1" Pin0InfoVect0LinkObjId="SW-231703_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231702_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-383 4408,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ad2100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-424 4408,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38630@1" ObjectIDZND0="38629@1" Pin0InfoVect0LinkObjId="SW-231702_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231703_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-424 4408,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ad2360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-464 4408,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38588@0" ObjectIDZND0="38630@0" Pin0InfoVect0LinkObjId="SW-231703_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-464 4408,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d8db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4504,-282 4504,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_19d8400@0" ObjectIDZND0="38638@0" Pin0InfoVect0LinkObjId="SW-231713_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d8400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4504,-282 4504,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d9010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-327 4561,-338 4504,-338 4504,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_19d9260@0" ObjectIDZND0="38638@1" Pin0InfoVect0LinkObjId="SW-231713_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19d9260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-327 4561,-338 4504,-338 4504,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19e4a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4631,-283 4631,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_19e40c0@0" ObjectIDZND0="38643@0" Pin0InfoVect0LinkObjId="SW-231721_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19e40c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4631,-283 4631,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19e4cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4688,-328 4688,-339 4631,-339 4631,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_19e4f20@0" ObjectIDZND0="38643@1" Pin0InfoVect0LinkObjId="SW-231721_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19e4f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4688,-328 4688,-339 4631,-339 4631,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a650c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4661,-384 4661,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38639@0" ObjectIDZND0="38641@1" Pin0InfoVect0LinkObjId="SW-231719_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231718_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4661,-384 4661,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a65320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4661,-425 4661,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38640@1" ObjectIDZND0="38639@1" Pin0InfoVect0LinkObjId="SW-231718_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231719_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4661,-425 4661,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a65580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4661,-464 4661,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38588@0" ObjectIDZND0="38640@0" Pin0InfoVect0LinkObjId="SW-231719_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4661,-464 4661,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a6ec80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-284 4759,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a6e230@0" ObjectIDZND0="38648@0" Pin0InfoVect0LinkObjId="SW-231729_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a6e230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-284 4759,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a6eee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-329 4816,-340 4759,-340 4759,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a6f150@0" ObjectIDZND0="38648@1" Pin0InfoVect0LinkObjId="SW-231729_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a6f150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-329 4816,-340 4759,-340 4759,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a72680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-385 4789,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38644@0" ObjectIDZND0="38646@1" Pin0InfoVect0LinkObjId="SW-231727_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-385 4789,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a728e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-426 4789,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38645@1" ObjectIDZND0="38644@1" Pin0InfoVect0LinkObjId="SW-231726_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231727_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-426 4789,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a72b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-464 4789,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38588@0" ObjectIDZND0="38645@0" Pin0InfoVect0LinkObjId="SW-231727_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-464 4789,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b499c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-194 4127,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="lightningRod" ObjectIDND0="38621@x" ObjectIDND1="49446@x" ObjectIDZND0="g_1b4a0e0@0" Pin0InfoVect0LinkObjId="g_1b4a0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231688_0" Pin1InfoVect1LinkObjId="CB-CX_HD.CX_HD_2C_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-194 4127,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b49c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-209 4116,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="38621@0" ObjectIDZND0="g_1b4a0e0@0" ObjectIDZND1="49446@x" Pin0InfoVect0LinkObjId="g_1b4a0e0_0" Pin0InfoVect1LinkObjId="CB-CX_HD.CX_HD_2C_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231688_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-209 4116,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b49e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-194 4116,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="g_1b4a0e0@0" ObjectIDND1="38621@x" ObjectIDZND0="49446@1" Pin0InfoVect0LinkObjId="CB-CX_HD.CX_HD_2C_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b4a0e0_0" Pin1InfoVect1LinkObjId="SW-231688_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-194 4116,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b4b6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-356 4116,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38620@0" ObjectIDZND0="g_1b15370@1" Pin0InfoVect0LinkObjId="g_1b15370_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231687_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-356 4116,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b4b940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-274 4116,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1b15370@0" ObjectIDZND0="38621@1" Pin0InfoVect0LinkObjId="SW-231688_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b15370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-274 4116,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b4c530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-353 4255,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38626@0" ObjectIDZND0="g_1af4b90@1" Pin0InfoVect0LinkObjId="g_1af4b90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231695_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-353 4255,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b4ece0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-271 4255,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1af4b90@0" ObjectIDZND0="38627@1" Pin0InfoVect0LinkObjId="SW-231696_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1af4b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-271 4255,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4cde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-354 4408,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38631@0" ObjectIDZND0="g_1a8df20@1" Pin0InfoVect0LinkObjId="g_1a8df20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231703_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-354 4408,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4d040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-354 4534,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38636@0" ObjectIDZND0="g_1ad25c0@1" Pin0InfoVect0LinkObjId="g_1ad25c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-354 4534,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4d2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4661,-355 4661,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38641@0" ObjectIDZND0="g_19dc4b0@1" Pin0InfoVect0LinkObjId="g_19dc4b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231719_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4661,-355 4661,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4d500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-356 4789,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38646@0" ObjectIDZND0="g_1a657e0@1" Pin0InfoVect0LinkObjId="g_1a657e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231727_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-356 4789,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4d760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-272 4408,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a8df20@0" ObjectIDZND0="38632@1" Pin0InfoVect0LinkObjId="SW-231704_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a8df20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-272 4408,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4d9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-272 4534,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1ad25c0@0" ObjectIDZND0="38637@1" Pin0InfoVect0LinkObjId="SW-231712_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ad25c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-272 4534,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4dc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4661,-273 4661,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_19dc4b0@0" ObjectIDZND0="38642@1" Pin0InfoVect0LinkObjId="SW-231720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19dc4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4661,-273 4661,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4de80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-274 4789,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a657e0@0" ObjectIDZND0="38647@1" Pin0InfoVect0LinkObjId="SW-231728_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a657e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-274 4789,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c52050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-179 4255,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1c52240@0" ObjectIDND1="38627@x" ObjectIDZND0="49447@0" Pin0InfoVect0LinkObjId="EC-CX_HD.CX_HD_055LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c52240_0" Pin1InfoVect1LinkObjId="SW-231696_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-179 4255,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c56230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-178 4408,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1c52d70@0" ObjectIDND1="38632@x" ObjectIDZND0="49448@0" Pin0InfoVect0LinkObjId="EC-CX_HD.CX_HD_056LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c52d70_0" Pin1InfoVect1LinkObjId="SW-231704_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-178 4408,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c56490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4661,-178 4661,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="38642@x" ObjectIDND1="g_1c547d0@0" ObjectIDZND0="49450@0" Pin0InfoVect0LinkObjId="EC-CX_HD.CX_HD_058LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231720_0" Pin1InfoVect1LinkObjId="g_1c547d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4661,-178 4661,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c566f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-178 4534,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="38637@x" ObjectIDND1="g_1c53aa0@0" ObjectIDZND0="49449@0" Pin0InfoVect0LinkObjId="EC-CX_HD.CX_HD_057LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231712_0" Pin1InfoVect1LinkObjId="g_1c53aa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-178 4534,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c56950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-178 4789,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="38647@x" ObjectIDND1="g_1c55500@0" ObjectIDZND0="49451@0" Pin0InfoVect0LinkObjId="EC-CX_HD.CX_HD_059LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231728_0" Pin1InfoVect1LinkObjId="g_1c55500_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-178 4789,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a9db60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-464 4857,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38588@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-464 4857,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a9ddc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-430 4857,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-430 4857,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a9e020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-388 4857,-374 4905,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-388 4857,-374 4905,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a9e280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4922,-375 4952,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4922,-375 4952,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a9e4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4979,-375 5007,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4979,-375 5007,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1a9e740">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="5024,-374 5057,-374 5057,-464 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5024,-374 5057,-374 5057,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a9e9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-1099 4414,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="38595@x" ObjectIDND1="38592@x" ObjectIDND2="g_1a9ec00@0" ObjectIDZND0="g_1b85550@0" Pin0InfoVect0LinkObjId="g_1b85550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-231502_0" Pin1InfoVect1LinkObjId="SW-231499_0" Pin1InfoVect2LinkObjId="g_1a9ec00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-1099 4414,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a9f9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-1124 4365,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1b85550@0" ObjectIDND1="38595@x" ObjectIDND2="38592@x" ObjectIDZND0="g_1a9ec00@0" Pin0InfoVect0LinkObjId="g_1a9ec00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b85550_0" Pin1InfoVect1LinkObjId="SW-231502_0" Pin1InfoVect2LinkObjId="SW-231499_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-1124 4365,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a9fc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-355 3966,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38614@0" ObjectIDZND0="g_1a63500@1" Pin0InfoVect0LinkObjId="g_1a63500_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-355 3966,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a9fe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-273 3966,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a63500@0" ObjectIDZND0="38615@1" Pin0InfoVect0LinkObjId="SW-231680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a63500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-273 3966,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1aa0960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-1077 4384,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="38595@x" ObjectIDND1="38592@x" ObjectIDZND0="g_1b85550@0" ObjectIDZND1="g_1a9ec00@0" ObjectIDZND2="49419@1" Pin0InfoVect0LinkObjId="g_1b85550_0" Pin0InfoVect1LinkObjId="g_1a9ec00_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231502_0" Pin1InfoVect1LinkObjId="SW-231499_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-1077 4384,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1aa1450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-1124 4384,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1a9ec00@0" ObjectIDND1="49419@1" ObjectIDZND0="g_1b85550@0" ObjectIDZND1="38595@x" ObjectIDZND2="38592@x" Pin0InfoVect0LinkObjId="g_1b85550_0" Pin0InfoVect1LinkObjId="SW-231502_0" Pin0InfoVect2LinkObjId="SW-231499_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a9ec00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-1124 4384,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1aa16b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-1144 4384,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="49419@1" ObjectIDZND0="g_1a9ec00@0" ObjectIDZND1="g_1b85550@0" ObjectIDZND2="38595@x" Pin0InfoVect0LinkObjId="g_1a9ec00_0" Pin0InfoVect1LinkObjId="g_1b85550_0" Pin0InfoVect2LinkObjId="SW-231502_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-1144 4384,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa1910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-464 4534,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38588@0" ObjectIDZND0="38635@0" Pin0InfoVect0LinkObjId="SW-231711_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-464 4534,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa1b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-423 4534,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38635@1" ObjectIDZND0="38634@1" Pin0InfoVect0LinkObjId="SW-231710_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231711_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-423 4534,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa1dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-383 4534,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38634@0" ObjectIDZND0="38636@1" Pin0InfoVect0LinkObjId="SW-231711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-383 4534,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa2600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-464 4334,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38588@0" ObjectIDZND0="38601@0" Pin0InfoVect0LinkObjId="SW-231543_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a62470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-464 4334,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa2860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-512 4334,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38601@1" ObjectIDZND0="38600@0" Pin0InfoVect0LinkObjId="SW-231542_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231543_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-512 4334,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa3350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4391,-172 4391,-178 4408,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1c52d70@0" ObjectIDZND0="38632@x" ObjectIDZND1="49448@x" Pin0InfoVect0LinkObjId="SW-231704_0" Pin0InfoVect1LinkObjId="EC-CX_HD.CX_HD_056LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c52d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4391,-172 4391,-178 4408,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa35b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-178 4408,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_1c52d70@0" ObjectIDND1="49448@x" ObjectIDZND0="38632@0" Pin0InfoVect0LinkObjId="SW-231704_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c52d70_0" Pin1InfoVect1LinkObjId="EC-CX_HD.CX_HD_056LD_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-178 4408,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa40a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-173 4238,-179 4255,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1c52240@0" ObjectIDZND0="38627@x" ObjectIDZND1="49447@x" Pin0InfoVect0LinkObjId="SW-231696_0" Pin0InfoVect1LinkObjId="EC-CX_HD.CX_HD_055LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c52240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-173 4238,-179 4255,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa4300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-179 4255,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_1c52240@0" ObjectIDND1="49447@x" ObjectIDZND0="38627@0" Pin0InfoVect0LinkObjId="SW-231696_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c52240_0" Pin1InfoVect1LinkObjId="EC-CX_HD.CX_HD_055LD_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-179 4255,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa4df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-203 4534,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="38637@0" ObjectIDZND0="g_1c53aa0@0" ObjectIDZND1="49449@x" Pin0InfoVect0LinkObjId="g_1c53aa0_0" Pin0InfoVect1LinkObjId="EC-CX_HD.CX_HD_057LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231712_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-203 4534,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa5050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-178 4517,-178 4517,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="38637@x" ObjectIDND1="49449@x" ObjectIDZND0="g_1c53aa0@0" Pin0InfoVect0LinkObjId="g_1c53aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231712_0" Pin1InfoVect1LinkObjId="EC-CX_HD.CX_HD_057LD_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-178 4517,-178 4517,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa5b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4661,-204 4661,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="38642@0" ObjectIDZND0="g_1c547d0@0" ObjectIDZND1="49450@x" Pin0InfoVect0LinkObjId="g_1c547d0_0" Pin0InfoVect1LinkObjId="EC-CX_HD.CX_HD_058LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4661,-204 4661,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa5da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4661,-178 4644,-178 4644,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="38642@x" ObjectIDND1="49450@x" ObjectIDZND0="g_1c547d0@0" Pin0InfoVect0LinkObjId="g_1c547d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231720_0" Pin1InfoVect1LinkObjId="EC-CX_HD.CX_HD_058LD_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4661,-178 4644,-178 4644,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa6890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-202 4789,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="38647@0" ObjectIDZND0="g_1c55500@0" ObjectIDZND1="49451@x" Pin0InfoVect0LinkObjId="g_1c55500_0" Pin0InfoVect1LinkObjId="EC-CX_HD.CX_HD_059LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-202 4789,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa6af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-178 4772,-178 4772,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="38647@x" ObjectIDND1="49451@x" ObjectIDZND0="g_1c55500@0" Pin0InfoVect0LinkObjId="g_1c55500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231728_0" Pin1InfoVect1LinkObjId="EC-CX_HD.CX_HD_059LD_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-178 4772,-178 4772,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ab8e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-251 3928,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1ab8420@0" ObjectIDZND0="38616@1" Pin0InfoVect0LinkObjId="SW-231681_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ab8420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-251 3928,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1abc320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-250 4078,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1abb8d0@0" ObjectIDZND0="38622@1" Pin0InfoVect0LinkObjId="SW-231689_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1abb8d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-250 4078,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac7eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-248 3816,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="49499@0" ObjectIDZND0="g_1bd36e0@0" Pin0InfoVect0LinkObjId="g_1bd36e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_HD.CX_HD_052LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-248 3816,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19ec2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-628 4334,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1b8f910@0" ObjectIDND1="38651@x" ObjectIDZND0="g_1b0b940@0" ObjectIDZND1="38602@x" Pin0InfoVect0LinkObjId="g_1b0b940_0" Pin0InfoVect1LinkObjId="SW-231543_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b8f910_0" Pin1InfoVect1LinkObjId="g_1b8d9f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-628 4334,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19ec500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-605 4352,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1b8f910@0" ObjectIDND1="38651@x" ObjectIDND2="38602@x" ObjectIDZND0="g_1b0b940@0" Pin0InfoVect0LinkObjId="g_1b0b940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b8f910_0" Pin1InfoVect1LinkObjId="g_1b8d9f0_0" Pin1InfoVect2LinkObjId="SW-231543_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-605 4352,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_216d4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-194 3977,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="lightningRod" ObjectIDND0="38615@x" ObjectIDND1="49445@x" ObjectIDZND0="g_1b41e90@0" Pin0InfoVect0LinkObjId="g_1b41e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231680_0" Pin1InfoVect1LinkObjId="CB-CX_HD.CX_HD_1C_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-194 3977,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2168740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-209 3966,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="38615@0" ObjectIDZND0="g_1b41e90@0" ObjectIDZND1="49445@x" Pin0InfoVect0LinkObjId="g_1b41e90_0" Pin0InfoVect1LinkObjId="CB-CX_HD.CX_HD_1C_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-209 3966,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2166700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-194 3966,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="g_1b41e90@0" ObjectIDND1="38615@x" ObjectIDZND0="49445@1" Pin0InfoVect0LinkObjId="CB-CX_HD.CX_HD_1C_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b41e90_0" Pin1InfoVect1LinkObjId="SW-231680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-194 3966,-152 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-230747" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3411.000000 -1087.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38455" ObjectName="DYN-CX_HD"/>
     <cge:Meas_Ref ObjectId="230747"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b27730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3518.000000 -4.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b278e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3507.000000 -19.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b27b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3532.000000 -34.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b28570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4122.000000 968.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b28730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4122.000000 953.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b28920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 938.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b28b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 922.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b28cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4122.000000 982.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b28e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.000000 906.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac0ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3618.000000 545.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac0d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3618.000000 530.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac0f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.000000 515.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac1190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3610.000000 499.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac13d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3618.000000 559.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac22f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.000000 1241.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac2580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4282.000000 1226.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac27c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4307.000000 1211.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac2be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4401.000000 556.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac2ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4390.000000 541.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac30e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 526.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac6980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 676.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac73b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 661.000000) translate(0,12)">温度:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f1100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.000000 843.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f13c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4119.000000 828.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f1600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 813.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HD" endPointId="0" endStationName="CX_GY" flowDrawDirect="1" flowShape="0" id="AC-110kV.guohou_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4384,-1142 4384,-1178 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49419" ObjectName="AC-110kV.guohou_line"/>
    <cge:TPSR_Ref TObjectID="49419_SS-280"/></metadata>
   <polyline fill="none" opacity="0" points="4384,-1142 4384,-1178 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="38587" cx="4493" cy="-881" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38587" cx="4384" cy="-881" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4584" cy="-882" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5057" cy="-464" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38588" cx="3691" cy="-464" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38588" cx="3890" cy="-464" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38588" cx="3816" cy="-464" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38588" cx="3966" cy="-464" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38588" cx="4116" cy="-464" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38588" cx="4255" cy="-464" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38588" cx="4408" cy="-464" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38588" cx="4661" cy="-464" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38588" cx="4789" cy="-464" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38588" cx="4857" cy="-464" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38588" cx="4534" cy="-464" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38588" cx="4334" cy="-464" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-231540">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4209.000000 -666.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38599" ObjectName="SW-CX_HD.CX_HD_1010SW"/>
     <cge:Meas_Ref ObjectId="231540"/>
    <cge:TPSR_Ref TObjectID="38599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4484.000000 -817.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4484.000000 -749.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 -818.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 -750.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231537">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4326.000000 -796.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38596" ObjectName="SW-CX_HD.CX_HD_1011SW"/>
     <cge:Meas_Ref ObjectId="231537"/>
    <cge:TPSR_Ref TObjectID="38596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231539">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -777.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38598" ObjectName="SW-CX_HD.CX_HD_10117SW"/>
     <cge:Meas_Ref ObjectId="231539"/>
    <cge:TPSR_Ref TObjectID="38598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231502">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -1072.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38595" ObjectName="SW-CX_HD.CX_HD_15167SW"/>
     <cge:Meas_Ref ObjectId="231502"/>
    <cge:TPSR_Ref TObjectID="38595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231501">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -1004.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38594" ObjectName="SW-CX_HD.CX_HD_15160SW"/>
     <cge:Meas_Ref ObjectId="231501"/>
    <cge:TPSR_Ref TObjectID="38594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231500">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -947.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38593" ObjectName="SW-CX_HD.CX_HD_15117SW"/>
     <cge:Meas_Ref ObjectId="231500"/>
    <cge:TPSR_Ref TObjectID="38593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231499">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4375.000000 -1019.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38592" ObjectName="SW-CX_HD.CX_HD_1516SW"/>
     <cge:Meas_Ref ObjectId="231499"/>
    <cge:TPSR_Ref TObjectID="38592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231498">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4375.000000 -892.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38591" ObjectName="SW-CX_HD.CX_HD_1511SW"/>
     <cge:Meas_Ref ObjectId="231498"/>
    <cge:TPSR_Ref TObjectID="38591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231538">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -848.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38597" ObjectName="SW-CX_HD.CX_HD_10110SW"/>
     <cge:Meas_Ref ObjectId="231538"/>
    <cge:TPSR_Ref TObjectID="38597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231543">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -558.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38602" ObjectName="SW-CX_HD.CX_HD_001XC1"/>
     <cge:Meas_Ref ObjectId="231543"/>
    <cge:TPSR_Ref TObjectID="38602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231543">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -488.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38601" ObjectName="SW-CX_HD.CX_HD_001XC"/>
     <cge:Meas_Ref ObjectId="231543"/>
    <cge:TPSR_Ref TObjectID="38601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231734">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -474.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38649" ObjectName="SW-CX_HD.CX_HD_0901XC"/>
     <cge:Meas_Ref ObjectId="231734"/>
    <cge:TPSR_Ref TObjectID="38649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231667">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 -155.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38606" ObjectName="SW-CX_HD.CX_HD_0010SW"/>
     <cge:Meas_Ref ObjectId="231667"/>
    <cge:TPSR_Ref TObjectID="38606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231666">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 -415.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38604" ObjectName="SW-CX_HD.CX_HD_051XC"/>
     <cge:Meas_Ref ObjectId="231666"/>
    <cge:TPSR_Ref TObjectID="38604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231666">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 -345.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38605" ObjectName="SW-CX_HD.CX_HD_051XC1"/>
     <cge:Meas_Ref ObjectId="231666"/>
    <cge:TPSR_Ref TObjectID="38605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231668">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 -287.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38607" ObjectName="SW-CX_HD.CX_HD_05160SW"/>
     <cge:Meas_Ref ObjectId="231668"/>
    <cge:TPSR_Ref TObjectID="38607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231673">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3806.000000 -416.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38609" ObjectName="SW-CX_HD.CX_HD_052XC"/>
     <cge:Meas_Ref ObjectId="231673"/>
    <cge:TPSR_Ref TObjectID="38609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231673">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3806.000000 -346.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38610" ObjectName="SW-CX_HD.CX_HD_052XC1"/>
     <cge:Meas_Ref ObjectId="231673"/>
    <cge:TPSR_Ref TObjectID="38610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231674">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 -288.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38611" ObjectName="SW-CX_HD.CX_HD_05260SW"/>
     <cge:Meas_Ref ObjectId="231674"/>
    <cge:TPSR_Ref TObjectID="38611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231679">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.000000 -418.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38613" ObjectName="SW-CX_HD.CX_HD_053XC"/>
     <cge:Meas_Ref ObjectId="231679"/>
    <cge:TPSR_Ref TObjectID="38613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231679">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.000000 -348.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38614" ObjectName="SW-CX_HD.CX_HD_053XC1"/>
     <cge:Meas_Ref ObjectId="231679"/>
    <cge:TPSR_Ref TObjectID="38614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231682">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3927.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38617" ObjectName="SW-CX_HD.CX_HD_05360SW"/>
     <cge:Meas_Ref ObjectId="231682"/>
    <cge:TPSR_Ref TObjectID="38617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231687">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -418.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38619" ObjectName="SW-CX_HD.CX_HD_054XC"/>
     <cge:Meas_Ref ObjectId="231687"/>
    <cge:TPSR_Ref TObjectID="38619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231687">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -349.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38620" ObjectName="SW-CX_HD.CX_HD_054XC1"/>
     <cge:Meas_Ref ObjectId="231687"/>
    <cge:TPSR_Ref TObjectID="38620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231690">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4077.000000 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38623" ObjectName="SW-CX_HD.CX_HD_05460SW"/>
     <cge:Meas_Ref ObjectId="231690"/>
    <cge:TPSR_Ref TObjectID="38623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231695">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -415.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38625" ObjectName="SW-CX_HD.CX_HD_055XC"/>
     <cge:Meas_Ref ObjectId="231695"/>
    <cge:TPSR_Ref TObjectID="38625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231695">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -346.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38626" ObjectName="SW-CX_HD.CX_HD_055XC1"/>
     <cge:Meas_Ref ObjectId="231695"/>
    <cge:TPSR_Ref TObjectID="38626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231697">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4216.000000 -288.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38628" ObjectName="SW-CX_HD.CX_HD_05560SW"/>
     <cge:Meas_Ref ObjectId="231697"/>
    <cge:TPSR_Ref TObjectID="38628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231703">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4398.000000 -416.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38630" ObjectName="SW-CX_HD.CX_HD_056XC"/>
     <cge:Meas_Ref ObjectId="231703"/>
    <cge:TPSR_Ref TObjectID="38630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231703">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4398.000000 -347.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38631" ObjectName="SW-CX_HD.CX_HD_056XC1"/>
     <cge:Meas_Ref ObjectId="231703"/>
    <cge:TPSR_Ref TObjectID="38631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231705">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38633" ObjectName="SW-CX_HD.CX_HD_05660SW"/>
     <cge:Meas_Ref ObjectId="231705"/>
    <cge:TPSR_Ref TObjectID="38633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231711">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 -416.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38635" ObjectName="SW-CX_HD.CX_HD_057XC"/>
     <cge:Meas_Ref ObjectId="231711"/>
    <cge:TPSR_Ref TObjectID="38635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231711">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 -347.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38636" ObjectName="SW-CX_HD.CX_HD_057XC1"/>
     <cge:Meas_Ref ObjectId="231711"/>
    <cge:TPSR_Ref TObjectID="38636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231713">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38638" ObjectName="SW-CX_HD.CX_HD_05760SW"/>
     <cge:Meas_Ref ObjectId="231713"/>
    <cge:TPSR_Ref TObjectID="38638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231719">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4651.000000 -417.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38640" ObjectName="SW-CX_HD.CX_HD_058XC"/>
     <cge:Meas_Ref ObjectId="231719"/>
    <cge:TPSR_Ref TObjectID="38640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231719">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4651.000000 -348.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38641" ObjectName="SW-CX_HD.CX_HD_058XC1"/>
     <cge:Meas_Ref ObjectId="231719"/>
    <cge:TPSR_Ref TObjectID="38641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231721">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38643" ObjectName="SW-CX_HD.CX_HD_05860SW"/>
     <cge:Meas_Ref ObjectId="231721"/>
    <cge:TPSR_Ref TObjectID="38643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231727">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -418.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38645" ObjectName="SW-CX_HD.CX_HD_059XC"/>
     <cge:Meas_Ref ObjectId="231727"/>
    <cge:TPSR_Ref TObjectID="38645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231727">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -349.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38646" ObjectName="SW-CX_HD.CX_HD_059XC1"/>
     <cge:Meas_Ref ObjectId="231727"/>
    <cge:TPSR_Ref TObjectID="38646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231729">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38648" ObjectName="SW-CX_HD.CX_HD_05960SW"/>
     <cge:Meas_Ref ObjectId="231729"/>
    <cge:TPSR_Ref TObjectID="38648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231680">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3957.000000 -204.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38615" ObjectName="SW-CX_HD.CX_HD_0536SW"/>
     <cge:Meas_Ref ObjectId="231680"/>
    <cge:TPSR_Ref TObjectID="38615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3938.000000 -201.000000)" xlink:href="#switch2:shape6_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231688">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38621" ObjectName="SW-CX_HD.CX_HD_0546SW"/>
     <cge:Meas_Ref ObjectId="231688"/>
    <cge:TPSR_Ref TObjectID="38621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4088.000000 -201.000000)" xlink:href="#switch2:shape6_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231696">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4246.000000 -199.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38627" ObjectName="SW-CX_HD.CX_HD_0556SW"/>
     <cge:Meas_Ref ObjectId="231696"/>
    <cge:TPSR_Ref TObjectID="38627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231704">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38632" ObjectName="SW-CX_HD.CX_HD_0566SW"/>
     <cge:Meas_Ref ObjectId="231704"/>
    <cge:TPSR_Ref TObjectID="38632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231712">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 -198.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38637" ObjectName="SW-CX_HD.CX_HD_0576SW"/>
     <cge:Meas_Ref ObjectId="231712"/>
    <cge:TPSR_Ref TObjectID="38637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231720">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4652.000000 -199.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38642" ObjectName="SW-CX_HD.CX_HD_0586SW"/>
     <cge:Meas_Ref ObjectId="231720"/>
    <cge:TPSR_Ref TObjectID="38642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231728">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -197.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38647" ObjectName="SW-CX_HD.CX_HD_0596SW"/>
     <cge:Meas_Ref ObjectId="231728"/>
    <cge:TPSR_Ref TObjectID="38647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.000000 -423.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.000000 -381.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4901.000000 -365.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5003.000000 -365.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231681">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38616" ObjectName="SW-CX_HD.CX_HD_05367SW"/>
     <cge:Meas_Ref ObjectId="231681"/>
    <cge:TPSR_Ref TObjectID="38616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231689">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.000000 -199.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38622" ObjectName="SW-CX_HD.CX_HD_05467SW"/>
     <cge:Meas_Ref ObjectId="231689"/>
    <cge:TPSR_Ref TObjectID="38622"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bacb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bacb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bacb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,59)">片区有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bacb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bacb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bacb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bacb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bacb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1bacb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1041.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,17)">危险点：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,374)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,395)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,416)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,437)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,458)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_192f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -589.000000) translate(0,479)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1c2bef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.000000 -1167.500000) translate(0,16)">后甸变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd8540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4030.000000 -900.000000) translate(0,12)">110kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b53020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3662.000000 -90.000000) translate(0,15)">1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_184e3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4528.000000 -831.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b269c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4591.000000 -848.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b26b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4447.000000 -847.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b26d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4441.000000 -779.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b26f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4591.000000 -780.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b271f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4342.000000 -827.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b273a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -693.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b27c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -810.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b27c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -810.000000) translate(0,27)">SZ11-50000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b27c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -810.000000) translate(0,42)">YN，yn0,d11 ONAN</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b27c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -810.000000) translate(0,57)">110±8×1.25%/37±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b27c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -810.000000) translate(0,72)">Ud1-2%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b27c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -810.000000) translate(0,87)">Ud1-3%=18</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b27c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -810.000000) translate(0,102)">Ud2-3%=6.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b28400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -631.000000) translate(0,12)">10kVⅠ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b115a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4393.000000 -988.000000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a85810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -1032.000000) translate(0,12)">15160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b77a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -978.000000) translate(0,12)">15117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b77bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -1103.000000) translate(0,12)">15167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b77dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4400.000000 -1174.000000) translate(0,15)">果后线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b877f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -1049.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b08cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4526.000000 -791.000000) translate(0,12)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a62930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3793.000000 -90.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b42c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -41.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b4bba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4072.000000 -40.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c4e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4225.000000 -90.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c4ec90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4388.000000 -90.000000) translate(0,15)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c4f1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4490.000000 -90.000000) translate(0,15)">10kV锦润Ⅰ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c50b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4612.000000 -90.000000) translate(0,15)">10kV工业园区Ⅰ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c51b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4767.000000 -90.000000) translate(0,15)">10kV锦润Ⅱ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa6d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -918.000000) translate(0,12)">1511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa7380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -1049.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa75c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4279.000000 -847.000000) translate(0,12)">10110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa7800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 -808.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa7a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -708.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa7c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -545.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa7ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3902.000000 -497.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa84e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3700.000000 -402.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa8720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3604.000000 -320.000000) translate(0,12)">05160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa8960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3825.000000 -403.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa8ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3734.000000 -320.000000) translate(0,12)">05260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa8de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3975.000000 -405.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa9020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -320.000000) translate(0,12)">05360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aa9260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3588.000000 -184.000000) translate(0,12)">0010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abc7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3871.000000 -230.000000) translate(0,12)">05367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abce10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3973.000000 -230.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abd050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4125.000000 -406.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abd290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.000000 -320.000000) translate(0,12)">05460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abd4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -230.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abd710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4019.000000 -230.000000) translate(0,12)">05467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abd950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -403.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abdb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4169.000000 -320.000000) translate(0,12)">05560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abddd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4262.000000 -230.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abe010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4417.000000 -404.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abe250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4320.000000 -320.000000) translate(0,12)">05660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abe490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4415.000000 -230.000000) translate(0,12)">0566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abe6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4543.000000 -404.000000) translate(0,12)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abe910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4541.000000 -230.000000) translate(0,12)">0576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abeb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4453.000000 -320.000000) translate(0,12)">05760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abed90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4670.000000 -405.000000) translate(0,12)">058</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abefd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -320.000000) translate(0,12)">05860</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abf210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.000000 -230.000000) translate(0,12)">0586</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abf450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4798.000000 -406.000000) translate(0,12)">059</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abf690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4706.000000 -320.000000) translate(0,12)">05960</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abf8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4796.000000 -230.000000) translate(0,12)">0596</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1abfb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3571.000000 -465.000000) translate(0,12)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1acab60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3182.000000 -813.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_19ec760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -128.000000) translate(0,16)">8985122</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1839390" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4212.000000 -640.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf1ce0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.000000 -719.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1af78b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4578.000000 -720.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b51ee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -776.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b29340" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -1071.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b10450" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -1003.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b10c00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -946.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b8c850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -847.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd1a90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3655.000000 -262.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1adf490" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -263.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a80030" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.000000 -265.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1af0020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4080.000000 -266.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a893b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -263.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1acda50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4372.000000 -264.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19d8400" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4498.000000 -264.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19e40c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4625.000000 -265.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a6e230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4753.000000 -266.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ab8420" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3934.000000 -269.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1abb8d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4084.000000 -268.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_HD"/>
</svg>