<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-53" aopId="2" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1200 1892 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="hydroGenerator:shape2">
    <polyline points="12,27 11,28 12,28 12,29 12,30 13,31 13,32 14,33 15,33 15,34 16,34 17,35 18,35 19,35 20,35 21,35 22,34 23,34 24,33 25,33 25,32 26,31 26,30 27,29 27,28 27,28 27,27 " stroke-width="0.06"/>
    <circle cx="27" cy="27" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <polyline arcFlag="1" points="28,27 28,26 28,25 28,24 29,23 29,22 30,21 30,21 31,20 32,20 33,19 34,19 35,19 36,18 37,19 38,19 39,19 40,20 40,20 41,21 42,21 42,22 43,23 43,24 43,25 44,26 43,27 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape116">
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="14" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape101">
    <ellipse cx="12" cy="33" rx="11.5" ry="12" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="9" x2="15" y1="13" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="15" x2="15" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="9" x2="15" y1="13" y2="18"/>
    <ellipse cx="12" cy="13" rx="11.5" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="17" x2="12" y1="36" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="33" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="36" y2="33"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape61">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="14" y2="14"/>
    <circle cx="37" cy="7" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="30" cy="15" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="30" cy="7" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="37" cy="15" fillStyle="0" r="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="75" y2="22"/>
    <rect height="27" stroke-width="0.416667" width="14" x="23" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="29" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="65" y2="34"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="20" y2="29"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198e740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198f120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_198fb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19902c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19912d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1991ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1992a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19934c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1993d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_19975e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19991b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1999da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_199ab60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199b4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199c520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199cd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199d410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19a0480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a0e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a4100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19a4d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19b3590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19b3dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a6f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a8560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1902" x="3112" y="-1205"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-46612">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.000000 -823.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8264" ObjectName="SW-CX_YCH.CX_YCH_30160SW"/>
     <cge:Meas_Ref ObjectId="46612"/>
    <cge:TPSR_Ref TObjectID="8264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46617">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4003.000000 -1013.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8269" ObjectName="SW-CX_YCH.CX_YCH_3019SW"/>
     <cge:Meas_Ref ObjectId="46617"/>
    <cge:TPSR_Ref TObjectID="8269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.000000 -400.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46614">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.000000 -479.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8266" ObjectName="SW-CX_YCH.CX_YCH_6011SW"/>
     <cge:Meas_Ref ObjectId="46614"/>
    <cge:TPSR_Ref TObjectID="8266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4116.000000 -527.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4069.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8262" ObjectName="SW-CX_YCH.CX_YCH_3016SW"/>
     <cge:Meas_Ref ObjectId="46610"/>
    <cge:TPSR_Ref TObjectID="8262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46611">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -920.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8263" ObjectName="SW-CX_YCH.CX_YCH_30167SW"/>
     <cge:Meas_Ref ObjectId="46611"/>
    <cge:TPSR_Ref TObjectID="8263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46620">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.000000 -405.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8272" ObjectName="SW-CX_YCH.CX_YCH_6611SW"/>
     <cge:Meas_Ref ObjectId="46620"/>
    <cge:TPSR_Ref TObjectID="8272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 -391.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46621">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3760.000000 -228.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8273" ObjectName="SW-CX_YCH.CX_YCH_6612SW"/>
     <cge:Meas_Ref ObjectId="46621"/>
    <cge:TPSR_Ref TObjectID="8273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46624">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.000000 -404.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8276" ObjectName="SW-CX_YCH.CX_YCH_6621SW"/>
     <cge:Meas_Ref ObjectId="46624"/>
    <cge:TPSR_Ref TObjectID="8276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4306.000000 -390.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46625">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8277" ObjectName="SW-CX_YCH.CX_YCH_6622SW"/>
     <cge:Meas_Ref ObjectId="46625"/>
    <cge:TPSR_Ref TObjectID="8277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 -374.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4886.000000 -298.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4886.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4936.000000 -438.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46615">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4353.000000 -477.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8267" ObjectName="SW-CX_YCH.CX_YCH_6901SW"/>
     <cge:Meas_Ref ObjectId="46615"/>
    <cge:TPSR_Ref TObjectID="8267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46616">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4400.000000 -526.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8268" ObjectName="SW-CX_YCH.CX_YCH_69017SW"/>
     <cge:Meas_Ref ObjectId="46616"/>
    <cge:TPSR_Ref TObjectID="8268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 -206.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3927.000000 -217.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 -205.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4342.000000 -216.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46618">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.000000 -984.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8270" ObjectName="SW-CX_YCH.CX_YCH_30197SW"/>
     <cge:Meas_Ref ObjectId="46618"/>
    <cge:TPSR_Ref TObjectID="8270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46622">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -234.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8274" ObjectName="SW-CX_YCH.CX_YCH_6613SW"/>
     <cge:Meas_Ref ObjectId="46622"/>
    <cge:TPSR_Ref TObjectID="8274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46626">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4370.000000 -233.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8278" ObjectName="SW-CX_YCH.CX_YCH_6623SW"/>
     <cge:Meas_Ref ObjectId="46626"/>
    <cge:TPSR_Ref TObjectID="8278"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4084,-1108 4084,-1119 4544,-1119 4544,-599 4901,-599 4901,-573 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4084,-1108 4084,-1119 4544,-1119 4544,-599 4901,-599 4901,-573 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_290a380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.000000 -839.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2369df0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4160.000000 -543.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10d7ac0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.000000 -936.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20ef4e0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3944.000000 -1020.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c838a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.000000 -407.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1066030" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3907.000000 -253.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_244b770" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3714.000000 -242.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23d5e50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4348.000000 -406.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_227f8b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4324.000000 -252.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e3930" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4131.000000 -241.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27238f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4654.000000 -390.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_efb5b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4978.000000 -454.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2439a60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.000000 -542.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1cb1150">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -170.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18f0870">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4004.000000 -179.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2570e40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4237.000000 -990.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23d7fb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 -889.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23f9ef0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3761.000000 -184.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2170600">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4371.000000 -169.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20cbef0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4178.000000 -183.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f29bc0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4853.000000 -475.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f59de0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4044.000000 -968.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f666b0">
    <use class="BV-6KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 3831.500000 -346.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f3cdb0">
    <use class="BV-6KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4248.500000 -337.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fb7200">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3959.000000 -197.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1562780">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4376.000000 -198.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_238f6a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4568.000000 -348.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c0b3e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4937.000000 -548.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cf50c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3978.000000 -925.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23e5f30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4856.000000 -521.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27172f0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4002.000000 -116.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_290da20">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -100.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f27460">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 -210.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2381ac0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.000000 -218.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2013280">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4426.000000 -164.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c82a80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4572.000000 -165.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23b8010">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4079.000000 -605.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fae310">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4238.000000 -805.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2381690">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4338.000000 -652.000000)" xlink:href="#lightningRod:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3237.000000 -1120.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46573" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3796.000000 -562.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46573" ObjectName="CX_YCH:CX_YCH_601BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46574" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3796.000000 -549.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46574" ObjectName="CX_YCH:CX_YCH_601BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46575" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3796.000000 -536.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46575" ObjectName="CX_YCH:CX_YCH_601BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46570" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3796.000000 -523.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46570" ObjectName="CX_YCH:CX_YCH_601BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-46563" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3273.000000 -983.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46563" ObjectName="CX_YCH:CX_YCH_301BK_P"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46563" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3990.000000 -827.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46563" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8261"/>
     <cge:Term_Ref ObjectID="11626"/>
    <cge:TPSR_Ref TObjectID="8261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46564" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3990.000000 -827.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8261"/>
     <cge:Term_Ref ObjectID="11626"/>
    <cge:TPSR_Ref TObjectID="8261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46565" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3990.000000 -827.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8261"/>
     <cge:Term_Ref ObjectID="11626"/>
    <cge:TPSR_Ref TObjectID="8261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46576" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3990.000000 -608.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8265"/>
     <cge:Term_Ref ObjectID="11634"/>
    <cge:TPSR_Ref TObjectID="8265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46577" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3990.000000 -608.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46577" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8265"/>
     <cge:Term_Ref ObjectID="11634"/>
    <cge:TPSR_Ref TObjectID="8265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46578" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3990.000000 -608.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8265"/>
     <cge:Term_Ref ObjectID="11634"/>
    <cge:TPSR_Ref TObjectID="8265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46589" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3978.000000 -398.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8271"/>
     <cge:Term_Ref ObjectID="11646"/>
    <cge:TPSR_Ref TObjectID="8271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46590" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3978.000000 -398.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46590" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8271"/>
     <cge:Term_Ref ObjectID="11646"/>
    <cge:TPSR_Ref TObjectID="8271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46591" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3978.000000 -398.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46591" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8271"/>
     <cge:Term_Ref ObjectID="11646"/>
    <cge:TPSR_Ref TObjectID="8271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46602" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4395.000000 -398.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8275"/>
     <cge:Term_Ref ObjectID="11654"/>
    <cge:TPSR_Ref TObjectID="8275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46603" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4395.000000 -398.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8275"/>
     <cge:Term_Ref ObjectID="11654"/>
    <cge:TPSR_Ref TObjectID="8275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46604" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4395.000000 -398.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8275"/>
     <cge:Term_Ref ObjectID="11654"/>
    <cge:TPSR_Ref TObjectID="8275"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19da6b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4117.000000 -718.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1697700" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3821.000000 -116.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1694940" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3761.000000 -176.000000) translate(0,12)">1YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1561830" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4547.000000 -141.000000) translate(0,12)">1号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2432d00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4337.000000 -672.000000) translate(0,12)">6kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18aad00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3953.000000 -159.000000) translate(0,12)">1YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14ec4d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3998.000000 -111.000000) translate(0,12)">1LCB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a3e90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4238.000000 -115.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be15a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4178.000000 -175.000000) translate(0,12)">1YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a380f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4370.000000 -158.000000) translate(0,12)">1YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f2d50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4415.000000 -94.000000) translate(0,12)">1LCB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a3aaf0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3964.000000 -877.000000) translate(0,12)">35kV线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_225e7b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4215.000000 -773.000000) translate(0,12)">2号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a6ae0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4126.000000 -1137.000000) translate(0,12)">永插线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243d1e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4853.000000 -295.000000) translate(0,12)">插甸变35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2185bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4093.000000 -592.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a4510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4091.000000 -526.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a4750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4093.000000 -811.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd9550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -875.000000) translate(0,12)">30160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd9740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4091.000000 -910.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10116a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -972.000000) translate(0,12)">30167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10118a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -1036.000000) translate(0,12)">30197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a365a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4025.000000 -1060.000000) translate(0,12)">3019</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a367b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4375.000000 -524.000000) translate(0,12)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ccce60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -577.000000) translate(0,12)">69017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ccd080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3863.000000 -394.000000) translate(0,12)">661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2460dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3861.000000 -452.000000) translate(0,12)">6611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1acd0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3975.000000 -281.000000) translate(0,12)">6613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2489e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3782.000000 -275.000000) translate(0,12)">6612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f10050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.000000 -393.000000) translate(0,12)">662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_fe5fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4278.000000 -451.000000) translate(0,12)">6621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193b110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -274.000000) translate(0,12)">6622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ea0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4392.000000 -280.000000) translate(0,12)">6623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22483a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1027.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22483a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1027.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22483a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1027.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22483a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1027.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22483a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1027.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22483a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1027.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22483a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -1027.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2475020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3146.000000 -589.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2898470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3276.000000 -1168.500000) translate(0,16)">永厂河电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21575d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3711.000000 -499.000000) translate(0,12)">6kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_270b600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -440.000000) translate(0,12)">66117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d71c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4316.000000 -440.000000) translate(0,12)">66217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d68ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3720.000000 -255.000000) translate(0,12)">66127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2323200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -267.000000) translate(0,12)">66137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16791b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -255.000000) translate(0,12)">66227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a4edd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4328.000000 -266.000000) translate(0,12)">66237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a4f000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -577.000000) translate(0,12)">60117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2503dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -446.000000) translate(0,12)">6631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2504010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4622.000000 -424.000000) translate(0,12)">66317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cc6bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -435.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cc6de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4933.000000 -459.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b114d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4911.500000 -393.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b116e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.500000 -345.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffc230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3892.000000 -741.000000) translate(0,12)">1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffc230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3892.000000 -741.000000) translate(0,27)">SF9-12500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffc230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3892.000000 -741.000000) translate(0,42)">12500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffc230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3892.000000 -741.000000) translate(0,57)">38.5±2×2.5%/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffc230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3892.000000 -741.000000) translate(0,72)">Ud=8%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffc230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3892.000000 -741.000000) translate(0,87)">YN,d11</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-46609">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -782.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8261" ObjectName="SW-CX_YCH.CX_YCH_301BK"/>
     <cge:Meas_Ref ObjectId="46609"/>
    <cge:TPSR_Ref TObjectID="8261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46619">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3845.000000 -365.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8271" ObjectName="SW-CX_YCH.CX_YCH_661BK"/>
     <cge:Meas_Ref ObjectId="46619"/>
    <cge:TPSR_Ref TObjectID="8271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46613">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -563.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8265" ObjectName="SW-CX_YCH.CX_YCH_601BK"/>
     <cge:Meas_Ref ObjectId="46613"/>
    <cge:TPSR_Ref TObjectID="8265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46623">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.000000 -364.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8275" ObjectName="SW-CX_YCH.CX_YCH_662BK"/>
     <cge:Meas_Ref ObjectId="46623"/>
    <cge:TPSR_Ref TObjectID="8275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4892.000000 -363.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4059.000000 -669.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4059.000000 -669.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.000000 -880.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.000000 -880.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.000000 -224.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.000000 -224.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2427680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4087,-849 4109,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8261@x" ObjectIDND1="8262@x" ObjectIDZND0="8264@0" Pin0InfoVect0LinkObjId="SW-46612_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46609_0" Pin1InfoVect1LinkObjId="SW-46610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4087,-849 4109,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20a2a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4145,-849 4164,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8264@1" ObjectIDZND0="g_290a380@0" Pin0InfoVect0LinkObjId="g_290a380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46612_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4145,-849 4164,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_27236c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-339 3826,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8273@x" ObjectIDND1="8274@x" ObjectIDND2="g_f27460@0" ObjectIDZND0="g_1f666b0@0" Pin0InfoVect0LinkObjId="g_1f666b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46621_0" Pin1InfoVect1LinkObjId="SW-46622_0" Pin1InfoVect2LinkObjId="g_f27460_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-339 3826,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1cc6230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4577,-479 4577,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18277@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2266bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4577,-479 4577,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_220ef90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-849 4084,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8264@x" ObjectIDND1="8262@x" ObjectIDZND0="8261@1" Pin0InfoVect0LinkObjId="SW-46609_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46612_0" Pin1InfoVect1LinkObjId="SW-46610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-849 4084,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aa1b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-790 4084,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="8261@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46609_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-790 4084,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_174bd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-849 4084,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8264@x" ObjectIDND1="8261@x" ObjectIDZND0="8262@0" Pin0InfoVect0LinkObjId="SW-46610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46612_0" Pin1InfoVect1LinkObjId="SW-46609_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-849 4084,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225d860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4243,-995 4243,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2570e40@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2570e40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4243,-995 4243,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5b910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-1089 4018,-1089 4018,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDND1="g_2570e40@0" ObjectIDND2="8262@x" ObjectIDZND0="8269@1" Pin0InfoVect0LinkObjId="SW-46617_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="g_2570e40_0" Pin1InfoVect2LinkObjId="SW-46610_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-1089 4018,-1089 4018,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23d8550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-1035 4018,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8269@0" ObjectIDZND0="g_1f59de0@0" ObjectIDZND1="g_1cf50c0@0" ObjectIDZND2="8270@x" Pin0InfoVect0LinkObjId="g_1f59de0_0" Pin0InfoVect1LinkObjId="g_1cf50c0_0" Pin0InfoVect2LinkObjId="SW-46618_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46617_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-1035 4018,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1562b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-1010 4018,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8269@x" ObjectIDND1="8270@x" ObjectIDZND0="g_1f59de0@0" ObjectIDZND1="g_1cf50c0@0" Pin0InfoVect0LinkObjId="g_1f59de0_0" Pin0InfoVect1LinkObjId="g_1cf50c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46617_0" Pin1InfoVect1LinkObjId="SW-46618_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-1010 4018,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_226e6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-978 4051,-978 4051,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8269@x" ObjectIDND1="8270@x" ObjectIDND2="g_1cf50c0@0" ObjectIDZND0="g_1f59de0@0" Pin0InfoVect0LinkObjId="g_1f59de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46617_0" Pin1InfoVect1LinkObjId="SW-46618_0" Pin1InfoVect2LinkObjId="g_1cf50c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-978 4051,-978 4051,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2266bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-463 3854,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8272@1" ObjectIDZND0="18277@0" Pin0InfoVect0LinkObjId="g_1a7fe10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46620_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-463 3854,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2155e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-553 4084,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8266@x" ObjectIDND1="0@x" ObjectIDZND0="8265@0" Pin0InfoVect0LinkObjId="SW-46613_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46614_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-553 4084,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_192f330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-553 4084,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8265@x" ObjectIDND1="0@x" ObjectIDZND0="8266@1" Pin0InfoVect0LinkObjId="SW-46614_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46613_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-553 4084,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1a7fe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-501 4084,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8266@0" ObjectIDZND0="18277@0" Pin0InfoVect0LinkObjId="g_2266bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46614_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-501 4084,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22807e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-427 3854,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="8272@0" ObjectIDZND0="8271@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-46619_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-427 3854,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1ccca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-417 3854,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8272@x" ObjectIDND1="0@x" ObjectIDZND0="8271@1" Pin0InfoVect0LinkObjId="SW-46619_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46620_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-417 3854,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22db290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-417 3880,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8272@x" ObjectIDND1="8271@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46620_0" Pin1InfoVect1LinkObjId="SW-46619_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-417 3880,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_193b5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-417 3936,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1c838a0@0" Pin0InfoVect0LinkObjId="g_1c838a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-417 3936,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fff760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-553 4143,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2369df0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2369df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-553 4143,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_18637c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-553 4084,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="8265@x" ObjectIDZND1="8266@x" Pin0InfoVect0LinkObjId="SW-46613_0" Pin0InfoVect1LinkObjId="SW-46614_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-553 4084,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1212ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-161 4014,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_27172f0@0" ObjectIDZND0="g_18f0870@0" Pin0InfoVect0LinkObjId="g_18f0870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27172f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-161 4014,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23262f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-223 4014,-243 3968,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_18f0870@1" ObjectIDZND0="g_1fb7200@0" ObjectIDZND1="0@x" ObjectIDZND2="8274@x" Pin0InfoVect0LinkObjId="g_1fb7200_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-46622_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18f0870_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-223 4014,-243 3968,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1926490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-300 3775,-300 3775,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="8274@x" ObjectIDND1="g_1f666b0@0" ObjectIDND2="8271@x" ObjectIDZND0="8273@1" Pin0InfoVect0LinkObjId="SW-46621_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46622_0" Pin1InfoVect1LinkObjId="g_1f666b0_0" Pin1InfoVect2LinkObjId="SW-46619_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-300 3775,-300 3775,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_225c4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-250 3775,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8273@0" ObjectIDZND0="g_23f9ef0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_23f9ef0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46621_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-250 3775,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2383e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-232 3775,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8273@x" ObjectIDND1="0@x" ObjectIDZND0="g_23f9ef0@0" Pin0InfoVect0LinkObjId="g_23f9ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46621_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-232 3775,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_242de50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-330 4243,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8277@x" ObjectIDND1="8278@x" ObjectIDND2="g_2381ac0@0" ObjectIDZND0="g_f3cdb0@0" Pin0InfoVect0LinkObjId="g_f3cdb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46625_0" Pin1InfoVect1LinkObjId="SW-46626_0" Pin1InfoVect2LinkObjId="g_2381ac0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-330 4243,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bffe20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-462 4271,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8276@1" ObjectIDZND0="18277@0" Pin0InfoVect0LinkObjId="g_2266bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46624_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-462 4271,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_212d1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-426 4271,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="8276@0" ObjectIDZND0="8275@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-46623_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46624_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-426 4271,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_214a080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-416 4271,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8276@x" ObjectIDND1="0@x" ObjectIDZND0="8275@1" Pin0InfoVect0LinkObjId="SW-46623_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46624_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-416 4271,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c18a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-416 4297,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8275@x" ObjectIDND1="8276@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46623_0" Pin1InfoVect1LinkObjId="SW-46624_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-416 4297,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1956240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-416 4353,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_23d5e50@0" Pin0InfoVect0LinkObjId="g_23d5e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-416 4353,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23e7db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-145 4431,-167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_290da20@0" ObjectIDZND0="g_2013280@1" Pin0InfoVect0LinkObjId="g_2013280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_290da20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-145 4431,-167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1066670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-222 4431,-242 4385,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2013280@0" ObjectIDZND0="g_1562780@0" ObjectIDZND1="0@x" ObjectIDZND2="8278@x" Pin0InfoVect0LinkObjId="g_1562780_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-46626_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2013280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-222 4431,-242 4385,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2713d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-299 4192,-299 4192,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_f3cdb0@0" ObjectIDND1="8275@x" ObjectIDND2="8278@x" ObjectIDZND0="8277@1" Pin0InfoVect0LinkObjId="SW-46625_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_f3cdb0_0" Pin1InfoVect1LinkObjId="SW-46623_0" Pin1InfoVect2LinkObjId="SW-46626_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-299 4192,-299 4192,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_209a5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-249 4192,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8277@0" ObjectIDZND0="g_20cbef0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_20cbef0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46625_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-249 4192,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21251d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-231 4192,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8277@x" ObjectIDND1="0@x" ObjectIDZND0="g_20cbef0@0" Pin0InfoVect0LinkObjId="g_20cbef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46625_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-231 4192,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bca3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4577,-400 4603,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_238f6a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_238f6a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4577,-400 4603,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2480130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-400 4659,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_27238f0@0" Pin0InfoVect0LinkObjId="g_27238f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-400 4659,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_229cb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4577,-400 4577,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_238f6a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_238f6a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4577,-400 4577,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_237c770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4901,-301 4901,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4901,-301 4901,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2713aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4901,-356 4901,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4901,-356 4901,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22874c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4901,-398 4901,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4901,-398 4901,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_218bba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4901,-464 4927,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1c0b3e0@0" ObjectIDND2="g_23e5f30@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1c0b3e0_0" Pin1InfoVect2LinkObjId="g_23e5f30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4901,-464 4927,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23c6db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4963,-464 4983,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_efb5b0@0" Pin0InfoVect0LinkObjId="g_efb5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4963,-464 4983,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_243baa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4901,-449 4901,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1c0b3e0@0" ObjectIDZND2="g_23e5f30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1c0b3e0_0" Pin0InfoVect2LinkObjId="g_23e5f30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4901,-449 4901,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2139150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4901,-464 4901,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_1c0b3e0@0" ObjectIDZND1="g_23e5f30@0" Pin0InfoVect0LinkObjId="g_1c0b3e0_0" Pin0InfoVect1LinkObjId="g_23e5f30_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4901,-464 4901,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16982d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-573 4944,-573 4944,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_23e5f30@0" ObjectIDZND0="g_1c0b3e0@0" Pin0InfoVect0LinkObjId="g_1c0b3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_23e5f30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-573 4944,-573 4944,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1ab7590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,-499 4368,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8267@0" ObjectIDZND0="18277@0" Pin0InfoVect0LinkObjId="g_2266bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46615_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4368,-499 4368,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2483a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-552 4427,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2439a60@0" ObjectIDZND0="8268@1" Pin0InfoVect0LinkObjId="SW-46616_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2439a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-552 4427,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2483760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3968,-194 3968,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1cb1150@0" ObjectIDZND0="g_1fb7200@0" Pin0InfoVect0LinkObjId="g_1fb7200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cb1150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3968,-194 3968,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_209a880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3968,-233 3968,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1fb7200@1" ObjectIDZND0="g_18f0870@0" ObjectIDZND1="0@x" ObjectIDZND2="8274@x" Pin0InfoVect0LinkObjId="g_18f0870_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-46622_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fb7200_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3968,-233 3968,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f5ae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-193 4385,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2170600@0" ObjectIDZND0="g_1562780@0" Pin0InfoVect0LinkObjId="g_1562780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2170600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-193 4385,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f9ff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-234 4385,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1562780@1" ObjectIDZND0="0@x" ObjectIDZND1="8278@x" ObjectIDZND2="g_2013280@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-46626_0" Pin0InfoVect2LinkObjId="g_2013280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1562780_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-234 4385,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22705a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4577,-400 4577,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_238f6a0@1" Pin0InfoVect0LinkObjId="g_238f6a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4577,-400 4577,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2156fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4577,-353 4577,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_238f6a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_238f6a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4577,-353 4577,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_194e550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3775,-232 3760,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8273@x" ObjectIDND1="g_23f9ef0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46621_0" Pin1InfoVect1LinkObjId="g_23f9ef0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3775,-232 3760,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19df100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3724,-232 3709,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_244b770@0" Pin0InfoVect0LinkObjId="g_244b770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3724,-232 3709,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_191b340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3968,-243 3954,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_18f0870@0" ObjectIDND1="g_1fb7200@0" ObjectIDND2="8274@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18f0870_0" Pin1InfoVect1LinkObjId="g_1fb7200_0" Pin1InfoVect2LinkObjId="SW-46622_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3968,-243 3954,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2242350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3918,-243 3902,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1066030@0" Pin0InfoVect0LinkObjId="g_1066030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3918,-243 3902,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1a39990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-231 4175,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8277@x" ObjectIDND1="g_20cbef0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46625_0" Pin1InfoVect1LinkObjId="g_20cbef0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-231 4175,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24c4250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4139,-231 4126,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_20e3930@0" Pin0InfoVect0LinkObjId="g_20e3930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4139,-231 4126,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bfe9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-242 4369,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1562780@0" ObjectIDND1="8278@x" ObjectIDND2="g_2013280@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1562780_0" Pin1InfoVect1LinkObjId="SW-46626_0" Pin1InfoVect2LinkObjId="g_2013280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-242 4369,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16a8940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-242 4319,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_227f8b0@0" Pin0InfoVect0LinkObjId="g_227f8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-242 4319,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21162b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3987,-913 3987,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_23d7fb0@0" ObjectIDZND0="g_1cf50c0@0" Pin0InfoVect0LinkObjId="g_1cf50c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23d7fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3987,-913 3987,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2714050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3987,-961 3987,-978 4018,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1cf50c0@1" ObjectIDZND0="8269@x" ObjectIDZND1="8270@x" ObjectIDZND2="g_1f59de0@0" Pin0InfoVect0LinkObjId="SW-46617_0" Pin0InfoVect1LinkObjId="SW-46618_0" Pin0InfoVect2LinkObjId="g_1f59de0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cf50c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3987,-961 3987,-978 4018,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22389b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4901,-573 4865,-573 4865,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_1c0b3e0@0" ObjectIDZND0="g_23e5f30@1" Pin0InfoVect0LinkObjId="g_23e5f30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1c0b3e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4901,-573 4865,-573 4865,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2420980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4865,-526 4865,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_23e5f30@0" ObjectIDZND0="g_f29bc0@0" Pin0InfoVect0LinkObjId="g_f29bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23e5f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4865,-526 4865,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_100d170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-1010 3997,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8269@x" ObjectIDND1="g_1f59de0@0" ObjectIDND2="g_1cf50c0@0" ObjectIDZND0="8270@1" Pin0InfoVect0LinkObjId="SW-46618_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46617_0" Pin1InfoVect1LinkObjId="g_1f59de0_0" Pin1InfoVect2LinkObjId="g_1cf50c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-1010 3997,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2185980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-1010 3939,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8270@0" ObjectIDZND0="g_20ef4e0@0" Pin0InfoVect0LinkObjId="g_20ef4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46618_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-1010 3939,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21b6f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3968,-243 3968,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_18f0870@0" ObjectIDND1="g_1fb7200@0" ObjectIDND2="0@x" ObjectIDZND0="8274@0" Pin0InfoVect0LinkObjId="SW-46622_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18f0870_0" Pin1InfoVect1LinkObjId="g_1fb7200_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3968,-243 3968,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1acce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3968,-292 3968,-300 3854,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="8274@1" ObjectIDZND0="8273@x" ObjectIDZND1="g_1f666b0@0" ObjectIDZND2="8271@x" Pin0InfoVect0LinkObjId="SW-46621_0" Pin0InfoVect1LinkObjId="g_1f666b0_0" Pin0InfoVect2LinkObjId="SW-46619_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46622_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3968,-292 3968,-300 3854,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_169c9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-300 3854,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="8273@x" ObjectIDND1="8274@x" ObjectIDND2="g_f27460@0" ObjectIDZND0="g_1f666b0@0" ObjectIDZND1="8271@x" Pin0InfoVect0LinkObjId="g_1f666b0_0" Pin0InfoVect1LinkObjId="SW-46619_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46621_0" Pin1InfoVect1LinkObjId="SW-46622_0" Pin1InfoVect2LinkObjId="g_f27460_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-300 3854,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_169cbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-373 3854,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="8271@0" ObjectIDZND0="g_1f666b0@0" ObjectIDZND1="8273@x" ObjectIDZND2="8274@x" Pin0InfoVect0LinkObjId="g_1f666b0_0" Pin0InfoVect1LinkObjId="SW-46621_0" Pin0InfoVect2LinkObjId="SW-46622_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46619_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-373 3854,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_f0fdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-299 4271,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="8277@x" ObjectIDND1="8278@x" ObjectIDND2="g_2381ac0@0" ObjectIDZND0="g_f3cdb0@0" ObjectIDZND1="8275@x" Pin0InfoVect0LinkObjId="g_f3cdb0_0" Pin0InfoVect1LinkObjId="SW-46623_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46625_0" Pin1InfoVect1LinkObjId="SW-46626_0" Pin1InfoVect2LinkObjId="g_2381ac0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-299 4271,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_fe5d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-372 4271,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="8275@0" ObjectIDZND0="g_f3cdb0@0" ObjectIDZND1="8277@x" ObjectIDZND2="8278@x" Pin0InfoVect0LinkObjId="g_f3cdb0_0" Pin0InfoVect1LinkObjId="SW-46625_0" Pin0InfoVect2LinkObjId="SW-46626_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46623_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-372 4271,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2260580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-242 4385,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1562780@0" ObjectIDND1="0@x" ObjectIDND2="g_2013280@0" ObjectIDZND0="8278@0" Pin0InfoVect0LinkObjId="SW-46626_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1562780_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2013280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-242 4385,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_20e9e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-291 4385,-299 4271,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="8278@1" ObjectIDZND0="8277@x" ObjectIDZND1="g_f3cdb0@0" ObjectIDZND2="8275@x" Pin0InfoVect0LinkObjId="SW-46625_0" Pin0InfoVect1LinkObjId="g_f3cdb0_0" Pin0InfoVect2LinkObjId="SW-46623_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46626_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-291 4385,-299 4271,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1cddef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-176 3854,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="43441@0" ObjectIDZND0="g_f27460@1" Pin0InfoVect0LinkObjId="g_f27460_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YCH.Gn1_test_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-176 3854,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2381890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-268 3854,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_f27460@0" ObjectIDZND0="8273@x" ObjectIDZND1="8274@x" ObjectIDZND2="g_1f666b0@0" Pin0InfoVect0LinkObjId="SW-46621_0" Pin0InfoVect1LinkObjId="SW-46622_0" Pin0InfoVect2LinkObjId="g_1f666b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f27460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-268 3854,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17fd8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-175 4271,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="9242@0" ObjectIDZND0="g_2381ac0@1" Pin0InfoVect0LinkObjId="g_2381ac0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_YCH.Gn2_test_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-175 4271,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2013020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-276 4271,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_2381ac0@0" ObjectIDZND0="8277@x" ObjectIDZND1="g_f3cdb0@0" ObjectIDZND2="8275@x" Pin0InfoVect0LinkObjId="SW-46625_0" Pin0InfoVect1LinkObjId="g_f3cdb0_0" Pin0InfoVect2LinkObjId="SW-46623_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2381ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-276 4271,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23b7db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4577,-266 4577,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1c82a80@0" Pin0InfoVect0LinkObjId="g_1c82a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4577,-266 4577,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_227aa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4577,-170 4577,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1c82a80@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c82a80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4577,-170 4577,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1fae0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-598 4084,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="8265@1" ObjectIDZND0="g_23b8010@1" Pin0InfoVect0LinkObjId="g_23b8010_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46613_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-598 4084,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_feeb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-663 4084,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_23b8010@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23b8010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-663 4084,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_152ae50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4243,-787 4243,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1fae310@1" Pin0InfoVect0LinkObjId="g_1fae310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4243,-787 4243,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2381450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4243,-863 4243,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1fae310@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fae310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4243,-863 4243,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_18f0be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,-552 4368,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8268@x" ObjectIDND1="8267@x" ObjectIDZND0="g_2381690@0" Pin0InfoVect0LinkObjId="g_2381690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46616_0" Pin1InfoVect1LinkObjId="SW-46615_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4368,-552 4368,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2242f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4391,-552 4368,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8268@0" ObjectIDZND0="8267@x" ObjectIDZND1="g_2381690@0" Pin0InfoVect0LinkObjId="SW-46615_0" Pin0InfoVect1LinkObjId="g_2381690_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46616_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4391,-552 4368,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22431b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,-552 4368,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8268@x" ObjectIDND1="g_2381690@0" ObjectIDZND0="8267@1" Pin0InfoVect0LinkObjId="SW-46615_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46616_0" Pin1InfoVect1LinkObjId="g_2381690_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4368,-552 4368,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b6980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-1089 4243,-1089 4243,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDND1="8269@x" ObjectIDND2="8262@x" ObjectIDZND0="g_2570e40@0" Pin0InfoVect0LinkObjId="g_2570e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="SW-46617_0" Pin1InfoVect2LinkObjId="SW-46610_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-1089 4243,-1089 4243,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_209ca70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-1089 4084,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2570e40@0" ObjectIDND1="8269@x" ObjectIDND2="8262@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2570e40_0" Pin1InfoVect1LinkObjId="SW-46617_0" Pin1InfoVect2LinkObjId="SW-46610_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-1089 4084,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d68c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-921 4084,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8262@1" ObjectIDZND0="0@1" ObjectIDZND1="g_2570e40@0" ObjectIDZND2="8269@x" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="g_2570e40_0" Pin0InfoVect2LinkObjId="SW-46617_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-921 4084,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2322fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-946 4084,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8262@x" ObjectIDND1="8263@x" ObjectIDZND0="0@1" ObjectIDZND1="g_2570e40@0" ObjectIDZND2="8269@x" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="g_2570e40_0" Pin0InfoVect2LinkObjId="SW-46617_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46610_0" Pin1InfoVect1LinkObjId="SW-46611_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-946 4084,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1678f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-946 4105,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8262@x" ObjectIDND1="0@1" ObjectIDND2="g_2570e40@0" ObjectIDZND0="8263@0" Pin0InfoVect0LinkObjId="SW-46611_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46610_0" Pin1InfoVect1LinkObjId="SW-0_1" Pin1InfoVect2LinkObjId="g_2570e40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-946 4105,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2157370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-946 4164,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8263@1" ObjectIDZND0="g_10d7ac0@0" Pin0InfoVect0LinkObjId="g_10d7ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46611_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-946 4164,-946 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3249" y="-1179"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3249" y="-1179"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3200" y="-1196"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3200" y="-1196"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1192130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.000000 827.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_245a800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 812.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_245aa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 797.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fe3210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.000000 608.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c00c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 593.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c00eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 578.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d3df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.000000 398.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d0bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3907.000000 383.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d0e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3932.000000 368.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_229c1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 398.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a3f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 383.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a4190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4350.000000 368.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_eb3e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 523.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_eb4080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 536.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202a920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 549.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202ab30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 562.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_YCH.Gn1_test">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3826.000000 -123.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43441" ObjectName="SM-CX_YCH.Gn1_test"/>
    <cge:TPSR_Ref TObjectID="43441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YCH.Gn2_test">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -122.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9242" ObjectName="SM-CX_YCH.Gn2_test"/>
    <cge:TPSR_Ref TObjectID="9242"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3249" y="-1179"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3200" y="-1196"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YCH.CX_YCH_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3712,-479 4684,-479 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18277" ObjectName="BS-CX_YCH.CX_YCH_6IM"/>
    <cge:TPSR_Ref TObjectID="18277"/></metadata>
   <polyline fill="none" opacity="0" points="3712,-479 4684,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-301 4993,-301 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4826,-301 4993,-301 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="18277" cx="4577" cy="-479" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18277" cx="3854" cy="-479" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18277" cx="4084" cy="-479" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18277" cx="4271" cy="-479" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18277" cx="4368" cy="-479" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YCH"/>
</svg>