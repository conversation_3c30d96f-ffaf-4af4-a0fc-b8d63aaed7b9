<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" aopId="0" id="thSvg" viewBox="3116 -1198 2152 1202">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
    <line stroke-width="0.5" x1="3" x2="16" y1="35" y2="10"/>
    <line stroke-width="0.5" x1="14" x2="14" y1="9" y2="9"/>
    <line stroke-width="0.5" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line stroke-width="0.5" x1="15" x2="3" y1="35" y2="10"/>
    <line stroke-width="0.5" x1="14" x2="14" y1="9" y2="9"/>
    <line stroke-width="0.5" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape9">
    <line stroke-width="0.5" x1="0" x2="21" y1="7" y2="7"/>
    <line stroke-width="0.5" x1="36" x2="51" y1="23" y2="7"/>
    <line stroke-width="0.130437" x1="14" x2="22" y1="33" y2="25"/>
    <line stroke-width="0.13551" x1="8" x2="18" y1="29" y2="19"/>
    <line stroke-width="0.5" x1="25" x2="18" y1="36" y2="29"/>
    <line stroke-width="0.5" x1="14" x2="0" y1="22" y2="7"/>
    <line stroke-width="0.130437" x1="36" x2="28" y1="33" y2="25"/>
    <line stroke-width="0.13551" x1="42" x2="32" y1="29" y2="19"/>
    <line stroke-width="0.5" x1="25" x2="32" y1="36" y2="29"/>
    <line stroke-width="0.5" x1="28" x2="51" y1="7" y2="7"/>
    <line stroke-width="0.130437" x1="21" x2="21" y1="13" y2="1"/>
    <line stroke-width="0.13551" x1="28" x2="28" y1="13" y2="0"/>
   </symbol>
   <symbol id="generator:shape3">
    <polyline fill="none" points="26,-19 26,-18 26,-17 26,-16 25,-15 25,-14 24,-13 24,-13 23,-12 22,-12 21,-11 20,-11 19,-11 18,-10 17,-11 16,-11 15,-11 14,-12 14,-12 13,-13 12,-13 12,-14 11,-15 11,-16 11,-17 10,-18 11,-19 "/>
    <circle cx="25" cy="-20" r="24"/>
    <polyline fill="none" points="41,-19 42,-20 41,-20 41,-21 41,-22 40,-23 40,-24 39,-25 38,-25 38,-26 37,-26 36,-27 35,-27 34,-27 33,-27 32,-27 31,-26 30,-26 29,-25 28,-25 28,-24 27,-23 27,-22 26,-21 26,-20 26,-20 26,-19 "/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" rx="10.5" ry="11.5"/>
    <ellipse cx="11" cy="25" rx="10.5" ry="11.5"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.25" width="16" x="1" y="5"/>
    <line stroke-width="0.5" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line stroke-width="0.5" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="0.5" width="12" x="1" y="20"/>
    <line stroke-width="0.125" x1="9" x2="6" y1="63" y2="63"/>
    <line stroke-width="0.196875" x1="11" x2="4" y1="60" y2="60"/>
    <line stroke-width="0.125874" x1="7" x2="7" y1="55" y2="47"/>
    <line stroke-width="0.305732" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line stroke-width="0.305732" x1="2" x2="13" y1="8" y2="8"/>
    <line stroke-width="0.125874" x1="7" x2="7" y1="9" y2="17"/>
    <line stroke-width="0.196875" x1="11" x2="4" y1="4" y2="4"/>
    <line stroke-width="0.125" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="0.5" width="12" x="1" y="18"/>
    <line stroke-width="0.5" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line stroke-width="0.5" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" rx="8.5" ry="7.5"/>
    <line stroke-width="0.0778547" x1="25" x2="20" y1="8" y2="10"/>
    <line stroke-width="0.0778547" x1="25" x2="20" y1="8" y2="5"/>
    <line stroke-width="0.0778547" x1="20" x2="20" y1="10" y2="5"/>
    <line stroke-width="0.0778547" x1="8" x2="5" y1="7" y2="9"/>
    <line stroke-width="0.0778547" x1="10" x2="8" y1="9" y2="7"/>
    <line stroke-width="0.0778547" x1="8" x2="8" y1="4" y2="7"/>
    <line stroke-width="0.0778547" x1="14" x2="11" y1="18" y2="20"/>
    <line stroke-width="0.0778547" x1="16" x2="14" y1="20" y2="18"/>
    <line stroke-width="0.0778547" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" rx="8.5" ry="7.5"/>
    <ellipse cx="14" cy="16" rx="9" ry="7.5"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line stroke-width="0.222222" x1="5" x2="5" y1="5" y2="13"/>
    <line stroke-width="0.111111" x1="5" x2="5" y1="29" y2="33"/>
    <polyline points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line stroke-width="0.222222" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line stroke-width="0.162432" x1="7" x2="15" y1="48" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line stroke-width="0.162432" x1="15" x2="15" y1="51" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line stroke-width="0.162432" x1="7" x2="15" y1="48" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line stroke-width="0.162432" x1="15" x2="15" y1="51" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5"/>
    <line stroke-width="0.132653" x1="9" x2="13" y1="40" y2="36"/>
    <line stroke-width="0.132653" x1="13" x2="17" y1="36" y2="40"/>
    <line stroke-width="0.132653" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" r="13"/>
    <line stroke-width="0.132653" x1="9" x2="13" y1="20" y2="16"/>
    <line stroke-width="0.132653" x1="13" x2="17" y1="16" y2="20"/>
    <line stroke-width="0.132653" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" r="24"/>
    <line stroke-width="0.255102" x1="26" x2="17" y1="34" y2="18"/>
    <line stroke-width="0.255102" x1="26" x2="34" y1="34" y2="18"/>
    <line stroke-width="0.255102" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" rx="24" ry="24.5"/>
    <line stroke-width="0.255102" x1="24" x2="24" y1="58" y2="66"/>
    <line stroke-width="0.255102" x1="24" x2="32" y1="66" y2="74"/>
    <line stroke-width="0.255102" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="2.07143" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="2.07143" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="0.5" width="111" x="0" y="0"/>
    <line stroke="rgb(50,205,50)" stroke-width="1.5" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 19.000000) translate(0,12)">禁止刷新</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(255,255,255);fill:none}
.BKBV-22KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-380KV { stroke:rgb(255,255,255);fill:none}
.BKBV-380KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1212" width="2162" x="3111" y="-1203"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(213,0,0)" stroke-width="0.5" width="360" x="3117" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="1200" stroke="rgb(213,0,0)" stroke-width="0.5" width="2150" x="3117" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(213,0,0)" stroke-width="0.5" width="360" x="3117" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(213,0,0)" stroke-width="0.5" width="360" x="3117" y="-597"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4326.000000 -839.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4326.000000 -974.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3601.000000 -766.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3601.000000 -663.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3536.000000 -587.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3766.000000 -770.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3766.000000 -667.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -769.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.000000 -770.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.500000 -771.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.500000 -586.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.500000 -490.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4048.708955 -371.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.119403 -370.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4230.000000 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.477612 -371.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4422.947761 -371.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.111940 -371.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4790.000000 -768.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4694.000000 -646.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4694.000000 -436.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4772.000000 -324.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4990.000000 -371.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5058.947761 -373.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5172.111940 -373.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-841 5088,-841 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3525,-841 5088,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4025,-444 4609,-444 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4025,-444 4609,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4682,-397 4915,-397 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4682,-397 4915,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4969,-444 5232,-444 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4969,-444 5232,-444 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 -673.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 -673.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.076124 -652.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.076124 -652.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.119403 -171.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4130.119403 -171.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4992.000000 -320.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4992.000000 -320.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="300b750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-841 4341,-861 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-841 4341,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="36cec00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-897 4341,-911 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-897 4341,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="37f0350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-961 4354,-961 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="36fcfc0@0" ObjectIDND2="0@x" ObjectIDZND0="3348510@1" Pin0InfoVect0LinkObjId="3348510_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="36fcfc0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-961 4354,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="38f1a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-961 4397,-961 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3348510@0" ObjectIDZND0="36dda20@0" Pin0InfoVect0LinkObjId="36dda20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3348510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-961 4397,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2ee1110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-938 4341,-961 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="3348510@0" ObjectIDZND1="36fcfc0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="3348510_0" Pin0InfoVect1LinkObjId="36fcfc0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-938 4341,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="39990d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-1032 4341,-1100 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-1032 4341,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3785e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-979 4307,-979 4307,-986 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="3348510@0" ObjectIDND2="0@x" ObjectIDZND0="36fcfc0@0" Pin0InfoVect0LinkObjId="36fcfc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3348510_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-979 4307,-979 4307,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="38e6b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-961 4341,-979 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="3348510@0" ObjectIDZND0="36fcfc0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="36fcfc0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3348510_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-961 4341,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3a4b880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4341,-979 4341,-996 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="3348510@0" ObjectIDND2="36fcfc0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3348510_0" Pin1InfoVect2LinkObjId="36fcfc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4341,-979 4341,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="354ef20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-841 3616,-824 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-841 3616,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="36fbfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-788 3616,-769 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-788 3616,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3a21750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-742 3616,-721 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-742 3616,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="392edc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-674 3659,-674 3659,-662 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="3734780@0" Pin0InfoVect0LinkObjId="3734780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-674 3659,-674 3659,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="374f1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-685 3616,-674 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="3734780@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="3734780_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-685 3616,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3896090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-674 3616,-658 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="3734780@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3734780_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-674 3616,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3334d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-658 3616,-512 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="3734780@0" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3734780_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-658 3616,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3ae4a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-658 3551,-658 3551,-645 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="3734780@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3734780_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-658 3551,-658 3551,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="385ea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-593 3551,-609 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="3a32ed0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3a32ed0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-593 3551,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3a6fca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-545 3551,-562 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="3a32ed0@0" Pin0InfoVect0LinkObjId="3a32ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-545 3551,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3b44fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-499 3551,-518 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="3ad2310@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3ad2310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-499 3551,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="36f1fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-451 3558,-451 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="3ad2310@0" ObjectIDZND0="334c490@0" Pin0InfoVect0LinkObjId="334c490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3ad2310_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-451 3558,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3aa7cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-424 3551,-451 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="334c490@0" ObjectIDZND1="3ad2310@0" Pin0InfoVect0LinkObjId="334c490_0" Pin0InfoVect1LinkObjId="3ad2310_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-424 3551,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3c7a3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-451 3551,-468 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="334c490@0" ObjectIDZND0="3ad2310@0" Pin0InfoVect0LinkObjId="3ad2310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="334c490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-451 3551,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="22cfc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-841 3781,-828 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-841 3781,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="37d6c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-792 3781,-773 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-792 3781,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3851b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-746 3781,-725 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-746 3781,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3a6acd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-678 3824,-678 3824,-666 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="3c21d40@0" Pin0InfoVect0LinkObjId="3c21d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-678 3824,-678 3824,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="389d880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-689 3781,-678 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="3c21d40@0" Pin0InfoVect0LinkObjId="3c21d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-689 3781,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3742080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3781,-516 3781,-678 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="3c21d40@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="3c21d40_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3781,-516 3781,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3225e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-841 4141,-827 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-841 4141,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3b92c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-791 4141,-770 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="3cbc930@1" Pin0InfoVect0LinkObjId="3cbc930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-791 4141,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3888030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-739 4141,-720 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="3cbc930@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3cbc930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-739 4141,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3b91f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-721 3985,-694 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3855370@0" ObjectIDZND0="3df5790@0" Pin0InfoVect0LinkObjId="3df5790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3855370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-721 3985,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3a6ecf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-769 3952,-769 3952,-762 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3855370@0" ObjectIDND1="0@x" ObjectIDZND0="3cb09b0@0" Pin0InfoVect0LinkObjId="3cb09b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="3855370_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-769 3952,-769 3952,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3222280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-769 3985,-752 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3cb09b0@0" ObjectIDND1="0@x" ObjectIDZND0="3855370@1" Pin0InfoVect0LinkObjId="3855370_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="3cb09b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-769 3985,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="39d29a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-737 4281,-752 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-737 4281,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3a35820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-563 4281,-548 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-563 4281,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="39d6ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-657 4281,-644 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-657 4281,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3857c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-608 4281,-590 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-608 4281,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3017cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-779 4281,-793 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-779 4281,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3a23100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-829 4281,-841 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-829 4281,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="391d360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-769 3985,-792 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3855370@0" ObjectIDND1="3cb09b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="3855370_0" Pin1InfoVect1LinkObjId="3cb09b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-769 3985,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3a27180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-828 3985,-841 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-828 3985,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="357a370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-444 4064,-429 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-444 4064,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="390e4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-393 4064,-374 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-393 4064,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3c80190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4064,-347 4064,-228 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4064,-347 4064,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3ec12d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-444 4143,-428 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-444 4143,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="33475f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-367 4108,-367 4108,-358 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="3f26e30@0" ObjectIDZND0="3c421f0@0" Pin0InfoVect0LinkObjId="3c421f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3f26e30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-367 4108,-367 4108,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3ca0ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-392 4143,-367 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="3c421f0@0" ObjectIDZND1="3f26e30@0" Pin0InfoVect0LinkObjId="3c421f0_0" Pin0InfoVect1LinkObjId="3f26e30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-392 4143,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3bef400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-367 4143,-343 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="3c421f0@0" ObjectIDZND0="3f26e30@1" Pin0InfoVect0LinkObjId="3f26e30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3c421f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-367 4143,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3bfb420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-312 4143,-292 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3f26e30@0" ObjectIDZND0="3edaf90@0" Pin0InfoVect0LinkObjId="3edaf90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3f26e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-312 4143,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3d14860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-239 4143,-218 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="3edaf90@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3edaf90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-239 4143,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3d44aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-325 4351,-298 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3fa2300@0" ObjectIDZND0="3bf7c90@0" Pin0InfoVect0LinkObjId="3bf7c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3fa2300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-325 4351,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3224530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-373 4318,-373 4318,-366 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3fa2300@0" ObjectIDND1="0@x" ObjectIDZND0="3dc7700@0" Pin0InfoVect0LinkObjId="3dc7700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="3fa2300_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-373 4318,-373 4318,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="39df5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-373 4351,-356 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3dc7700@0" ObjectIDND1="0@x" ObjectIDZND0="3fa2300@1" Pin0InfoVect0LinkObjId="3fa2300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="3dc7700_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-373 4351,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3b70930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-373 4351,-393 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3fa2300@0" ObjectIDND1="3dc7700@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="3fa2300_0" Pin1InfoVect1LinkObjId="3dc7700_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-373 4351,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3f26800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-429 4351,-444 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-429 4351,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3f2b960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-444 4438,-429 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-444 4438,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3ed5c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-393 4438,-376 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-393 4438,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3bc2bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-349 4438,-335 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="39683f0@0" ObjectIDZND1="38082b0@0" Pin0InfoVect0LinkObjId="39683f0_0" Pin0InfoVect1LinkObjId="38082b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-349 4438,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4b72440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-335 4482,-335 4482,-323 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="38082b0@0" ObjectIDZND0="39683f0@1" Pin0InfoVect0LinkObjId="39683f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="38082b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-335 4482,-335 4482,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3f0cf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-292 4482,-280 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="39683f0@0" ObjectIDZND0="26a19a0@0" Pin0InfoVect0LinkObjId="26a19a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="39683f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-292 4482,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3e47280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-335 4438,-304 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="39683f0@0" ObjectIDND1="0@x" ObjectIDZND0="38082b0@0" Pin0InfoVect0LinkObjId="38082b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="39683f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-335 4438,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="334d6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4438,-251 4438,-223 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="38082b0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="38082b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4438,-251 4438,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3e277d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-444 4551,-429 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-444 4551,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3bd0320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-393 4551,-376 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-393 4551,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3650690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-349 4551,-335 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="3770c80@0" ObjectIDZND1="3a20fc0@0" Pin0InfoVect0LinkObjId="3770c80_0" Pin0InfoVect1LinkObjId="3a20fc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-349 4551,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4c05710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-335 4595,-335 4595,-323 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="3a20fc0@0" ObjectIDZND0="3770c80@1" Pin0InfoVect0LinkObjId="3770c80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3a20fc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-335 4595,-335 4595,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2f72e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-292 4595,-280 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3770c80@0" ObjectIDZND0="2697040@0" Pin0InfoVect0LinkObjId="2697040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3770c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-292 4595,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3dff9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-335 4551,-304 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="3770c80@0" ObjectIDND1="0@x" ObjectIDZND0="3a20fc0@0" Pin0InfoVect0LinkObjId="3a20fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="3770c80_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-335 4551,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="347e690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-251 4551,-223 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="3a20fc0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3a20fc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-251 4551,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="33ee0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-512 4281,-479 3916,-479 3916,-128 4245,-128 4245,-396 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-512 4281,-479 3916,-479 3916,-128 4245,-128 4245,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3320900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-432 4245,-444 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-432 4245,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="34a9f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4805,-841 4805,-826 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4805,-841 4805,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="39bfa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4805,-790 4805,-772 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4805,-790 4805,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4d42780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-704 4709,-718 4805,-718 4805,-745 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-704 4709,-718 4805,-718 4805,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="377dd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-647 4673,-647 4673,-658 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="3fd4ef0@0" ObjectIDND2="0@x" ObjectIDZND0="3985c90@0" Pin0InfoVect0LinkObjId="3985c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3fd4ef0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-647 4673,-647 4673,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="30e6570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-647 4709,-668 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="3985c90@0" ObjectIDND1="3fd4ef0@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="3985c90_0" Pin1InfoVect1LinkObjId="3fd4ef0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-647 4709,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3f46f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-636 4753,-636 4753,-624 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="3985c90@0" ObjectIDND2="0@x" ObjectIDZND0="3fd4ef0@1" Pin0InfoVect0LinkObjId="3fd4ef0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3985c90_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-636 4753,-636 4753,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="27d6550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-593 4753,-581 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3fd4ef0@0" ObjectIDZND0="31f6a00@0" Pin0InfoVect0LinkObjId="31f6a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3fd4ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-593 4753,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="32e7710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-636 4709,-647 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3fd4ef0@0" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="3985c90@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="3985c90_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="3fd4ef0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-636 4709,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3c06950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-540 4709,-636 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="3985c90@0" ObjectIDZND2="3fd4ef0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="3985c90_0" Pin0InfoVect2LinkObjId="3fd4ef0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-540 4709,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3a73230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-397 4709,-458 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-397 4709,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3e568d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-494 4709,-513 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-494 4709,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3b775d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4858,-397 4858,-412 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4858,-397 4858,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="37e72d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4858,-521 4858,-532 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3e3ef40@1" ObjectIDZND0="400f6e0@0" Pin0InfoVect0LinkObjId="400f6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3e3ef40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4858,-521 4858,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3b49880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4858,-468 4892,-468 4892,-478 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="3e3ef40@0" ObjectIDZND0="3924e50@0" Pin0InfoVect0LinkObjId="3924e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3e3ef40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4858,-468 4892,-468 4892,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4b6a950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4858,-448 4858,-468 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="3924e50@0" ObjectIDZND1="3e3ef40@0" Pin0InfoVect0LinkObjId="3924e50_0" Pin0InfoVect1LinkObjId="3e3ef40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4858,-448 4858,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3da8c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4858,-468 4858,-490 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="3924e50@0" ObjectIDZND0="3e3ef40@0" Pin0InfoVect0LinkObjId="3e3ef40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3924e50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4858,-468 4858,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="35f20a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-397 4787,-382 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-397 4787,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3e7ec70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-346 4787,-327 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-346 4787,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4246230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-429 5005,-444 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-429 5005,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3b11db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-300 4787,-273 5005,-273 5005,-325 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-300 4787,-273 5005,-273 5005,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="30207e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-367 5005,-393 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-367 5005,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3810060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-444 5074,-431 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-444 5074,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="36fc590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-395 5074,-378 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-395 5074,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3b38010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-351 5074,-337 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="4a8bf30@0" ObjectIDZND1="3896550@0" Pin0InfoVect0LinkObjId="4a8bf30_0" Pin0InfoVect1LinkObjId="3896550_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-351 5074,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3ea77c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-337 5118,-337 5118,-325 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="3896550@0" ObjectIDZND0="4a8bf30@1" Pin0InfoVect0LinkObjId="4a8bf30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="3896550_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-337 5118,-337 5118,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4a67a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5118,-294 5118,-282 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="4a8bf30@0" ObjectIDZND0="36a79b0@0" Pin0InfoVect0LinkObjId="36a79b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4a8bf30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5118,-294 5118,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4c759f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-337 5074,-306 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="4a8bf30@0" ObjectIDND1="0@x" ObjectIDZND0="3896550@0" Pin0InfoVect0LinkObjId="3896550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="4a8bf30_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-337 5074,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4b050c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-253 5074,-225 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="3896550@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="3896550_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-253 5074,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3fec290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5187,-444 5187,-431 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5187,-444 5187,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="27f36e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5187,-395 5187,-378 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5187,-395 5187,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3e7e5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5187,-351 5187,-337 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="378d8e0@0" ObjectIDZND1="4cac410@0" Pin0InfoVect0LinkObjId="378d8e0_0" Pin0InfoVect1LinkObjId="4cac410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5187,-351 5187,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3d32ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5187,-337 5231,-337 5231,-325 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="4cac410@0" ObjectIDZND0="378d8e0@1" Pin0InfoVect0LinkObjId="378d8e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="4cac410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5187,-337 5231,-337 5231,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4bbc060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5231,-294 5231,-282 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="378d8e0@0" ObjectIDZND0="4b66650@0" Pin0InfoVect0LinkObjId="4b66650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="378d8e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5231,-294 5231,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="4ca6480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5187,-337 5187,-306 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="378d8e0@0" ObjectIDND1="0@x" ObjectIDZND0="4cac410@0" Pin0InfoVect0LinkObjId="4cac410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="378d8e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5187,-337 5187,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3fe72b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5187,-253 5187,-225 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="4cac410@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="4cac410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5187,-253 5187,-225 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer"/><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 -903.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -734.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -510.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 -738.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.500000 -744.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.500000 -555.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.708955 -339.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4428.947761 -341.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4542.111940 -341.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 -737.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4700.000000 -505.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4778.000000 -292.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5064.947761 -343.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5178.111940 -343.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3526.000000 -389.000000)" xlink:href="#capacitor:shape9"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" stroke="rgb(255,0,0)" width="138" x="3248" y="-1177"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" stroke="rgb(255,0,0)" width="77" x="3199" y="-1194"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="36dda20">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4433.000000 -949.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3348510">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4390.500000 -952.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="36fcfc0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.000000 -981.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3734780">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 -604.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3a32ed0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -557.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3ad2310">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -463.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="334c490">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3582.000000 -438.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3c21d40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3817.000000 -608.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3cbc930">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 -734.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3855370">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.000000 -716.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3cb09b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.000000 -704.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3df5790">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -670.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3f26e30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.119403 -307.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3c421f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.119403 -300.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3edaf90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.119403 -234.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3fa2300">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4342.477612 -320.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3dc7700">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4310.477612 -308.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3bf7c90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4337.477612 -274.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="39683f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4472.947761 -287.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="26a19a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4469.947761 -244.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="38082b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4432.947761 -246.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3770c80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.111940 -287.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2697040">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4583.111940 -244.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3a20fc0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.111940 -246.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3985c90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 -653.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3fd4ef0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.111940 -588.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="31f6a00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4741.111940 -545.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3e3ef40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.111940 -485.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="400f6e0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4872.000000 -556.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3924e50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4885.000000 -473.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4a8bf30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5108.947761 -289.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="36a79b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5105.947761 -246.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3896550">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5068.947761 -248.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="378d8e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5222.111940 -289.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4b66650">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5219.111940 -246.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="4cac410">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5182.111940 -248.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.947761 -218.000000)" xlink:href="#generator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4526.111940 -218.000000)" xlink:href="#generator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5048.947761 -220.000000)" xlink:href="#generator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5162.111940 -220.000000)" xlink:href="#generator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">滚水变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3511.000000 -878.000000) translate(0,18)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4299.500000 -1133.000000) translate(0,18)">10kV白星线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4352.000000 -933.000000) translate(0,15)">431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3579.000000 -506.000000) translate(0,18)">水泥厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3626.000000 -765.000000) translate(0,15)">434</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3792.000000 -765.000000) translate(0,15)">433</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3743.000000 -506.000000) translate(0,18)">罗川线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -659.000000) translate(0,18)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -659.000000) translate(0,18)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4293.500000 -777.000000) translate(0,15)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4293.500000 -585.000000) translate(0,15)">422</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4319.000000 -712.000000) translate(0,18)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4150.500000 -817.000000) translate(0,15)">4381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3990.500000 -817.000000) translate(0,15)">4371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4038.000000 -219.000000) translate(0,18)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4152.500000 -418.000000) translate(0,15)">4381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4110.000000 -165.000000) translate(0,18)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4259.500000 -418.000000) translate(0,15)">6212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4354.500000 -419.000000) translate(0,15)">6211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4405.000000 -170.000000) translate(0,18)">400kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4450.500000 -371.000000) translate(0,15)">612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4517.000000 -170.000000) translate(0,18)">400kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4562.500000 -371.000000) translate(0,15)">611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4448.000000 -125.000000) translate(0,18)">I级发电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4248.000000 -472.000000) translate(0,18)">6.3kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4821.500000 -768.000000) translate(0,15)">432</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4715.500000 -695.000000) translate(0,15)">#1杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4725.500000 -536.000000) translate(0,15)">436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4863.500000 -438.000000) translate(0,15)">4381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4797.500000 -323.000000) translate(0,15)">437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4910.500000 -355.000000) translate(0,15)">630kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5014.500000 -418.000000) translate(0,15)">2131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5041.000000 -172.000000) translate(0,18)">250kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5086.500000 -373.000000) translate(0,15)">211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5153.000000 -172.000000) translate(0,18)">250kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5198.500000 -373.000000) translate(0,15)">212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5084.000000 -127.000000) translate(0,18)">II级发电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5053.000000 -473.000000) translate(0,18)">0.4kV母线</text>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4"/>
</svg>