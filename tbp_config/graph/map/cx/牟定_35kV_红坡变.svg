<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-203" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-587 -1167 2059 1274">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape141">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="84" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape170">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <polyline points="16,30 7,30 " stroke-width="1"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <polyline points="15,10 6,10 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape12">
    <polyline points="9,14 3,17 1,18 1,19 1,19 3,21 6,22 10,24 11,25 11,25 11,26 10,27 6,28 3,30 2,30 2,31 2,32 3,33 6,34 10,36 11,36 11,37 11,38 10,38 6,40 3,41 2,42 2,43 2,44 3,44 6,46 10,47 11,48 11,49 11,49 10,50 6,52 3,53 1,55 1,55 1,56 3,57 9,60 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="15" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="0" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape93">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="16" x2="16" y1="47" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="3" x2="6" y1="64" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="59" y2="59"/>
    <polyline arcFlag="1" points="26,37 25,37 25,37 24,37 23,37 23,38 22,38 21,39 21,39 21,40 20,41 20,41 20,42 20,43 20,44 20,44 21,45 21,46 21,46 22,47 23,47 23,48 24,48 25,48 25,48 26,48 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="26,26 25,26 25,26 24,26 23,26 23,27 22,27 21,28 21,28 21,29 20,30 20,30 20,31 20,32 20,33 20,33 21,34 21,35 21,35 22,36 23,36 23,37 24,37 25,37 25,37 26,37 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="26,15 25,15 25,15 24,15 23,15 23,16 22,16 21,17 21,17 21,18 20,19 20,19 20,20 20,21 20,22 20,22 21,23 21,24 21,24 22,25 23,25 23,26 24,26 25,26 25,26 26,26 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="6,37 7,37 7,37 8,37 9,37 9,38 10,38 11,39 11,39 11,40 12,41 12,41 12,42 12,43 12,44 12,44 11,45 11,46 11,46 10,47 9,47 9,48 8,48 7,48 7,48 6,48 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="6,26 7,26 7,26 8,26 9,26 9,27 10,27 11,28 11,28 11,29 12,30 12,30 12,31 12,32 12,33 12,33 11,34 11,35 11,35 10,36 9,36 9,37 8,37 7,37 7,37 6,37 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="6,15 7,15 7,15 8,15 9,15 9,16 10,16 11,17 11,17 11,18 12,19 12,19 12,20 12,21 12,22 12,22 11,23 11,24 11,24 10,25 9,25 9,26 8,26 7,26 7,26 6,26 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="6" x2="6" y1="48" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="6" x2="6" y1="5" y2="15"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape40_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <circle cx="15" cy="19" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="10" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="10" x2="15" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="15" cy="41" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="11" x2="15" y1="50" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="45" y2="50"/>
   </symbol>
   <symbol id="voltageTransformer:shape129">
    <rect height="24" stroke-width="0.379884" width="14" x="7" y="44"/>
    <ellipse cx="9" cy="9" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="81" y2="28"/>
    <circle cx="15" cy="19" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="21" cy="10" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1cdf920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1346e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ce17a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ce2460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ce3660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ce4270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ce4e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1ce5850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13434f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13434f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ce7ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ce7ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ce9c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ce9c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1ceac80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cec880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ced470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cee230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ceeb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf0230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf0e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf16c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cf1e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf2f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf38e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf43d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cf4d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cf61e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cf6d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1cf7d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cf89c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d07190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cfa040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1cfb1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1cfc7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1284" width="2069" x="-592" y="-1172"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="369,36 370,36 370,36 371,36 371,36 372,36 372,35 372,35 372,35 373,34 373,34 373,33 373,33 373,32 373,32 373,31 373,31 372,30 372,30 372,29 372,29 371,29 371,29 370,28 370,28 369,28 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="369,18 370,18 370,18 371,18 371,18 372,17 372,17 372,17 372,16 373,16 373,15 373,15 373,14 373,14 373,13 373,13 373,12 372,12 372,12 372,11 372,11 371,11 371,10 370,10 370,10 369,10 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="369,19 370,19 370,18 371,18 371,18 372,18 372,17 372,17 372,17 373,16 373,16 373,15 373,15 373,14 373,14 373,13 373,13 372,12 372,12 372,12 372,11 371,11 371,11 370,11 370,10 369,10 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="369,26 370,26 370,26 371,26 371,26 372,25 372,25 372,25 372,24 373,24 373,23 373,23 373,22 373,22 373,21 373,21 373,20 372,20 372,20 372,19 372,19 371,19 371,18 370,18 370,18 369,18 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="349" x2="332" y1="-150" y2="-135"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="333" x2="349" y1="-150" y2="-150"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="332" x2="349" y1="-135" y2="-135"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="439" x2="443" y1="-1048" y2="-1043"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="889" x2="893" y1="-1046" y2="-1041"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.611465" x1="419" x2="407" y1="-262" y2="-262"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.39375" x1="409" x2="417" y1="-259" y2="-259"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.25" x1="412" x2="415" y1="-256" y2="-256"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="413" x2="438" y1="-284" y2="-265"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.25" x1="697" x2="701" y1="-236" y2="-236"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.39375" x1="695" x2="703" y1="-239" y2="-239"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.611465" x1="705" x2="693" y1="-242" y2="-242"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.25" x1="535" x2="539" y1="-238" y2="-238"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.39375" x1="533" x2="541" y1="-241" y2="-241"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.611465" x1="543" x2="531" y1="-244" y2="-244"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.25" x1="1116" x2="1120" y1="-587" y2="-587"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.39375" x1="1114" x2="1122" y1="-590" y2="-590"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.611465" x1="1124" x2="1112" y1="-593" y2="-593"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="375" x2="375" y1="-3" y2="13"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="375" x2="375" y1="20" y2="36"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="369" x2="345" y1="26" y2="26"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="369" x2="350" y1="11" y2="11"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="369" x2="364" y1="18" y2="18"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="369" x2="364" y1="28" y2="28"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="345" x2="345" y1="31" y2="26"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="345" x2="328" y1="32" y2="32"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="340" x2="328" y1="11" y2="11"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="328" x2="328" y1="19" y2="11"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="328" x2="328" y1="24" y2="32"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="438,-250 431,-265 446,-265 438,-250 438,-250 438,-250 " stroke="rgb(60,120,255)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-135206">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.000000 -854.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24477" ObjectName="SW-MD_HP.MD_HP_391BK"/>
     <cge:Meas_Ref ObjectId="135206"/>
    <cge:TPSR_Ref TObjectID="24477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135222">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 819.000000 -854.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24481" ObjectName="SW-MD_HP.MD_HP_392BK"/>
     <cge:Meas_Ref ObjectId="135222"/>
    <cge:TPSR_Ref TObjectID="24481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135254">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.980530 -671.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24487" ObjectName="SW-MD_HP.MD_HP_301BK"/>
     <cge:Meas_Ref ObjectId="135254"/>
    <cge:TPSR_Ref TObjectID="24487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135305">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 876.554483 -668.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24492" ObjectName="SW-MD_HP.MD_HP_302BK"/>
     <cge:Meas_Ref ObjectId="135305"/>
    <cge:TPSR_Ref TObjectID="24492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135312">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 876.554483 -484.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24494" ObjectName="SW-MD_HP.MD_HP_402BK"/>
     <cge:Meas_Ref ObjectId="135312"/>
    <cge:TPSR_Ref TObjectID="24494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135260">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 321.980530 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24489" ObjectName="SW-MD_HP.MD_HP_401BK"/>
     <cge:Meas_Ref ObjectId="135260"/>
    <cge:TPSR_Ref TObjectID="24489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135360">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -196.693092 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24499" ObjectName="SW-MD_HP.MD_HP_491BK"/>
     <cge:Meas_Ref ObjectId="135360"/>
    <cge:TPSR_Ref TObjectID="24499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135376">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -66.359759 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24503" ObjectName="SW-MD_HP.MD_HP_492BK"/>
     <cge:Meas_Ref ObjectId="135376"/>
    <cge:TPSR_Ref TObjectID="24503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135392">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 66.973574 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24507" ObjectName="SW-MD_HP.MD_HP_493BK"/>
     <cge:Meas_Ref ObjectId="135392"/>
    <cge:TPSR_Ref TObjectID="24507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135408">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 194.306908 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24511" ObjectName="SW-MD_HP.MD_HP_494BK"/>
     <cge:Meas_Ref ObjectId="135408"/>
    <cge:TPSR_Ref TObjectID="24511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135424">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 868.306908 -312.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24515" ObjectName="SW-MD_HP.MD_HP_495BK"/>
     <cge:Meas_Ref ObjectId="135424"/>
    <cge:TPSR_Ref TObjectID="24515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135440">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1040.640241 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24519" ObjectName="SW-MD_HP.MD_HP_496BK"/>
     <cge:Meas_Ref ObjectId="135440"/>
    <cge:TPSR_Ref TObjectID="24519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135456">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.973574 -306.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24523" ObjectName="SW-MD_HP.MD_HP_497BK"/>
     <cge:Meas_Ref ObjectId="135456"/>
    <cge:TPSR_Ref TObjectID="24523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1388.306908 -311.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24527" ObjectName="SW-MD_HP.MD_HP_498BK"/>
     <cge:Meas_Ref ObjectId="135472"/>
    <cge:TPSR_Ref TObjectID="24527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135488">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 602.000000 -488.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24531" ObjectName="SW-MD_HP.MD_HP_412BK"/>
     <cge:Meas_Ref ObjectId="135488"/>
    <cge:TPSR_Ref TObjectID="24531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-301787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 -312.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46594" ObjectName="SW-MD_HP.MD_HP_499BK"/>
     <cge:Meas_Ref ObjectId="301787"/>
    <cge:TPSR_Ref TObjectID="46594"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_13be5b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 555.000000 -255.000000)" xlink:href="#voltageTransformer:shape129"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13bf1a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 -253.000000)" xlink:href="#voltageTransformer:shape129"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c1000">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1136.000000 -604.000000)" xlink:href="#voltageTransformer:shape129"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="MD_HP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_hongpoT" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="378,-1091 378,-1054 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37794" ObjectName="AC-35kV.LN_hongpoT"/>
    <cge:TPSR_Ref TObjectID="37794_SS-203"/></metadata>
   <polyline fill="none" opacity="0" points="378,-1091 378,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_MD" endPointId="0" endStationName="MD_HP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_hongpo" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="828,-1093 828,-1056 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37786" ObjectName="AC-35kV.LN_hongpo"/>
    <cge:TPSR_Ref TObjectID="37786_SS-203"/></metadata>
   <polyline fill="none" opacity="0" points="828,-1093 828,-1056 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-MD_HP.MD_HP_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34630"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 300.000000 -577.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 300.000000 -577.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24536" ObjectName="TF-MD_HP.MD_HP_1T"/>
    <cge:TPSR_Ref TObjectID="24536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-MD_HP.MD_HP_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34634"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.000000 -574.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.000000 -574.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24537" ObjectName="TF-MD_HP.MD_HP_2T"/>
    <cge:TPSR_Ref TObjectID="24537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 688.000000 -591.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 688.000000 -591.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 423.000000 -278.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 423.000000 -278.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_11de200">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 417.000000 -77.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1411660">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 336.000000 -1084.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_144c920">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 780.000000 -1084.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14d0890">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 768.000000 -290.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1512ef0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 608.000000 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13f5bb0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1187.000000 -641.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14d6200">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -151.693092 -77.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14cb4c0">
    <use class="BV-10KV" transform="matrix(0.000000 -0.595506 -1.000000 -0.000000 -177.693092 -198.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14cbe40">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -22.359759 -77.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_141c660">
    <use class="BV-10KV" transform="matrix(0.000000 -0.595506 -1.000000 -0.000000 -48.359759 -198.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_141e200">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 110.973574 -77.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_143ebc0">
    <use class="BV-10KV" transform="matrix(0.000000 -0.595506 -1.000000 -0.000000 84.973574 -198.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14406e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 238.306908 -77.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14c08a0">
    <use class="BV-10KV" transform="matrix(0.000000 -0.595506 -1.000000 -0.000000 212.306908 -198.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14b29b0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 912.306908 -76.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_144f6d0">
    <use class="BV-10KV" transform="matrix(0.000000 -0.595506 -1.000000 -0.000000 886.306908 -197.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14517a0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1084.640241 -77.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a1a70">
    <use class="BV-10KV" transform="matrix(0.000000 -0.595506 -1.000000 -0.000000 1058.640241 -198.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14a3be0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1258.973574 -70.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_140a4f0">
    <use class="BV-10KV" transform="matrix(0.000000 -0.595506 -1.000000 -0.000000 1232.973574 -191.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_140c660">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1432.306908 -75.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13cfa50">
    <use class="BV-10KV" transform="matrix(0.000000 -0.595506 -1.000000 -0.000000 1406.306908 -196.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f8e50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 418.000000 -1024.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14fb570">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 411.000000 -1123.000000)" xlink:href="#lightningRod:shape170"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1348570">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 861.000000 -1124.000000)" xlink:href="#lightningRod:shape170"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1349290">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 868.000000 -1022.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15a9360">
    <use class="BV-10KV" transform="matrix(0.000000 -0.595506 -1.000000 -0.000000 351.306908 -168.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13789d0">
    <use class="BV-0KV" transform="matrix(0.312500 -0.000000 0.000000 -0.430252 415.000000 81.058824)" xlink:href="#lightningRod:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1379d70">
    <use class="BV-0KV" transform="matrix(0.312500 -0.000000 0.000000 -0.430252 372.000000 106.058824)" xlink:href="#lightningRod:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_137de50">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 1.000000 339.000000 -70.000000)" xlink:href="#lightningRod:shape93"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -480.000000 -1053.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215176" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -459.000000 -958.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215176" ObjectName="MD_HP:MD_HP_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215176" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -459.000000 -919.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215176" ObjectName="MD_HP:MD_HP_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-135097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -881.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24473"/>
     <cge:Term_Ref ObjectID="34505"/>
    <cge:TPSR_Ref TObjectID="24473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-135098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -881.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24473"/>
     <cge:Term_Ref ObjectID="34505"/>
    <cge:TPSR_Ref TObjectID="24473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-135099" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -881.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135099" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24473"/>
     <cge:Term_Ref ObjectID="34505"/>
    <cge:TPSR_Ref TObjectID="24473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-135102" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -881.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24473"/>
     <cge:Term_Ref ObjectID="34505"/>
    <cge:TPSR_Ref TObjectID="24473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-135100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -881.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24473"/>
     <cge:Term_Ref ObjectID="34505"/>
    <cge:TPSR_Ref TObjectID="24473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-135103" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -881.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135103" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24473"/>
     <cge:Term_Ref ObjectID="34505"/>
    <cge:TPSR_Ref TObjectID="24473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-135104" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -126.000000 -538.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135104" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24474"/>
     <cge:Term_Ref ObjectID="34506"/>
    <cge:TPSR_Ref TObjectID="24474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-135105" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -126.000000 -538.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24474"/>
     <cge:Term_Ref ObjectID="34506"/>
    <cge:TPSR_Ref TObjectID="24474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-135106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -126.000000 -538.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24474"/>
     <cge:Term_Ref ObjectID="34506"/>
    <cge:TPSR_Ref TObjectID="24474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-135109" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -126.000000 -538.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135109" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24474"/>
     <cge:Term_Ref ObjectID="34506"/>
    <cge:TPSR_Ref TObjectID="24474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-135107" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -126.000000 -538.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135107" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24474"/>
     <cge:Term_Ref ObjectID="34506"/>
    <cge:TPSR_Ref TObjectID="24474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-135110" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -126.000000 -538.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24474"/>
     <cge:Term_Ref ObjectID="34506"/>
    <cge:TPSR_Ref TObjectID="24474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-135111" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -536.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135111" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24475"/>
     <cge:Term_Ref ObjectID="34507"/>
    <cge:TPSR_Ref TObjectID="24475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-135112" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -536.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24475"/>
     <cge:Term_Ref ObjectID="34507"/>
    <cge:TPSR_Ref TObjectID="24475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-135113" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -536.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24475"/>
     <cge:Term_Ref ObjectID="34507"/>
    <cge:TPSR_Ref TObjectID="24475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-135116" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -536.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135116" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24475"/>
     <cge:Term_Ref ObjectID="34507"/>
    <cge:TPSR_Ref TObjectID="24475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-135114" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -536.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135114" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24475"/>
     <cge:Term_Ref ObjectID="34507"/>
    <cge:TPSR_Ref TObjectID="24475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-135117" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -536.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135117" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24475"/>
     <cge:Term_Ref ObjectID="34507"/>
    <cge:TPSR_Ref TObjectID="24475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135189" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 639.000000 -556.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135189" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24531"/>
     <cge:Term_Ref ObjectID="34618"/>
    <cge:TPSR_Ref TObjectID="24531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135190" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 639.000000 -556.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135190" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24531"/>
     <cge:Term_Ref ObjectID="34618"/>
    <cge:TPSR_Ref TObjectID="24531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135186" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 639.000000 -556.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135186" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24531"/>
     <cge:Term_Ref ObjectID="34618"/>
    <cge:TPSR_Ref TObjectID="24531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135088" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 -1167.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24477"/>
     <cge:Term_Ref ObjectID="34510"/>
    <cge:TPSR_Ref TObjectID="24477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135089" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 -1167.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24477"/>
     <cge:Term_Ref ObjectID="34510"/>
    <cge:TPSR_Ref TObjectID="24477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135085" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 -1167.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135085" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24477"/>
     <cge:Term_Ref ObjectID="34510"/>
    <cge:TPSR_Ref TObjectID="24477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 814.000000 -1161.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24481"/>
     <cge:Term_Ref ObjectID="34518"/>
    <cge:TPSR_Ref TObjectID="24481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 814.000000 -1161.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24481"/>
     <cge:Term_Ref ObjectID="34518"/>
    <cge:TPSR_Ref TObjectID="24481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 814.000000 -1161.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24481"/>
     <cge:Term_Ref ObjectID="34518"/>
    <cge:TPSR_Ref TObjectID="24481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135121" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 447.000000 -715.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135121" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24487"/>
     <cge:Term_Ref ObjectID="34530"/>
    <cge:TPSR_Ref TObjectID="24487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135122" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 447.000000 -715.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24487"/>
     <cge:Term_Ref ObjectID="34530"/>
    <cge:TPSR_Ref TObjectID="24487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135118" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 447.000000 -715.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24487"/>
     <cge:Term_Ref ObjectID="34530"/>
    <cge:TPSR_Ref TObjectID="24487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-135123" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 447.000000 -715.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24487"/>
     <cge:Term_Ref ObjectID="34530"/>
    <cge:TPSR_Ref TObjectID="24487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 -715.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24492"/>
     <cge:Term_Ref ObjectID="34540"/>
    <cge:TPSR_Ref TObjectID="24492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135136" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 -715.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24492"/>
     <cge:Term_Ref ObjectID="34540"/>
    <cge:TPSR_Ref TObjectID="24492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135132" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 -715.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24492"/>
     <cge:Term_Ref ObjectID="34540"/>
    <cge:TPSR_Ref TObjectID="24492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-135137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 -715.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24492"/>
     <cge:Term_Ref ObjectID="34540"/>
    <cge:TPSR_Ref TObjectID="24492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135127" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 448.000000 -527.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24489"/>
     <cge:Term_Ref ObjectID="34534"/>
    <cge:TPSR_Ref TObjectID="24489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135128" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 448.000000 -527.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24489"/>
     <cge:Term_Ref ObjectID="34534"/>
    <cge:TPSR_Ref TObjectID="24489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135124" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 448.000000 -527.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135124" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24489"/>
     <cge:Term_Ref ObjectID="34534"/>
    <cge:TPSR_Ref TObjectID="24489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-135129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 448.000000 -527.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24489"/>
     <cge:Term_Ref ObjectID="34534"/>
    <cge:TPSR_Ref TObjectID="24489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135141" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 -527.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24494"/>
     <cge:Term_Ref ObjectID="34544"/>
    <cge:TPSR_Ref TObjectID="24494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 -527.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24494"/>
     <cge:Term_Ref ObjectID="34544"/>
    <cge:TPSR_Ref TObjectID="24494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 -527.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24494"/>
     <cge:Term_Ref ObjectID="34544"/>
    <cge:TPSR_Ref TObjectID="24494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-135143" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 -527.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24494"/>
     <cge:Term_Ref ObjectID="34544"/>
    <cge:TPSR_Ref TObjectID="24494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135149" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -201.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135149" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24499"/>
     <cge:Term_Ref ObjectID="34554"/>
    <cge:TPSR_Ref TObjectID="24499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135150" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -201.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24499"/>
     <cge:Term_Ref ObjectID="34554"/>
    <cge:TPSR_Ref TObjectID="24499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135146" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -201.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24499"/>
     <cge:Term_Ref ObjectID="34554"/>
    <cge:TPSR_Ref TObjectID="24499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135147" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -201.000000 -13.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24499"/>
     <cge:Term_Ref ObjectID="34554"/>
    <cge:TPSR_Ref TObjectID="24499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135148" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -201.000000 -13.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24499"/>
     <cge:Term_Ref ObjectID="34554"/>
    <cge:TPSR_Ref TObjectID="24499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -66.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24503"/>
     <cge:Term_Ref ObjectID="34562"/>
    <cge:TPSR_Ref TObjectID="24503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135155" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -66.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24503"/>
     <cge:Term_Ref ObjectID="34562"/>
    <cge:TPSR_Ref TObjectID="24503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -66.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24503"/>
     <cge:Term_Ref ObjectID="34562"/>
    <cge:TPSR_Ref TObjectID="24503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135152" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -66.000000 -13.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24503"/>
     <cge:Term_Ref ObjectID="34562"/>
    <cge:TPSR_Ref TObjectID="24503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135153" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -66.000000 -13.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24503"/>
     <cge:Term_Ref ObjectID="34562"/>
    <cge:TPSR_Ref TObjectID="24503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 60.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24507"/>
     <cge:Term_Ref ObjectID="34570"/>
    <cge:TPSR_Ref TObjectID="24507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 60.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24507"/>
     <cge:Term_Ref ObjectID="34570"/>
    <cge:TPSR_Ref TObjectID="24507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135156" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 60.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24507"/>
     <cge:Term_Ref ObjectID="34570"/>
    <cge:TPSR_Ref TObjectID="24507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135157" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 60.000000 -13.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24507"/>
     <cge:Term_Ref ObjectID="34570"/>
    <cge:TPSR_Ref TObjectID="24507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135158" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 60.000000 -13.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135158" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24507"/>
     <cge:Term_Ref ObjectID="34570"/>
    <cge:TPSR_Ref TObjectID="24507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135164" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 197.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24511"/>
     <cge:Term_Ref ObjectID="34578"/>
    <cge:TPSR_Ref TObjectID="24511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135165" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 197.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135165" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24511"/>
     <cge:Term_Ref ObjectID="34578"/>
    <cge:TPSR_Ref TObjectID="24511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135161" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 197.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24511"/>
     <cge:Term_Ref ObjectID="34578"/>
    <cge:TPSR_Ref TObjectID="24511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 197.000000 -13.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24511"/>
     <cge:Term_Ref ObjectID="34578"/>
    <cge:TPSR_Ref TObjectID="24511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 197.000000 -13.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24511"/>
     <cge:Term_Ref ObjectID="34578"/>
    <cge:TPSR_Ref TObjectID="24511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135169" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 872.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24515"/>
     <cge:Term_Ref ObjectID="34586"/>
    <cge:TPSR_Ref TObjectID="24515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135170" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 872.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135170" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24515"/>
     <cge:Term_Ref ObjectID="34586"/>
    <cge:TPSR_Ref TObjectID="24515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135166" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 872.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135166" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24515"/>
     <cge:Term_Ref ObjectID="34586"/>
    <cge:TPSR_Ref TObjectID="24515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135167" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 872.000000 -13.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135167" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24515"/>
     <cge:Term_Ref ObjectID="34586"/>
    <cge:TPSR_Ref TObjectID="24515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135168" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 872.000000 -13.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135168" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24515"/>
     <cge:Term_Ref ObjectID="34586"/>
    <cge:TPSR_Ref TObjectID="24515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135174" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135174" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24519"/>
     <cge:Term_Ref ObjectID="34594"/>
    <cge:TPSR_Ref TObjectID="24519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135175" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135175" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24519"/>
     <cge:Term_Ref ObjectID="34594"/>
    <cge:TPSR_Ref TObjectID="24519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135171" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135171" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24519"/>
     <cge:Term_Ref ObjectID="34594"/>
    <cge:TPSR_Ref TObjectID="24519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135172" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -13.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135172" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24519"/>
     <cge:Term_Ref ObjectID="34594"/>
    <cge:TPSR_Ref TObjectID="24519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135173" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -13.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135173" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24519"/>
     <cge:Term_Ref ObjectID="34594"/>
    <cge:TPSR_Ref TObjectID="24519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135179" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135179" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24523"/>
     <cge:Term_Ref ObjectID="34602"/>
    <cge:TPSR_Ref TObjectID="24523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135180" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135180" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24523"/>
     <cge:Term_Ref ObjectID="34602"/>
    <cge:TPSR_Ref TObjectID="24523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135176" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135176" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24523"/>
     <cge:Term_Ref ObjectID="34602"/>
    <cge:TPSR_Ref TObjectID="24523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135177" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 -13.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135177" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24523"/>
     <cge:Term_Ref ObjectID="34602"/>
    <cge:TPSR_Ref TObjectID="24523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135178" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 -13.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135178" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24523"/>
     <cge:Term_Ref ObjectID="34602"/>
    <cge:TPSR_Ref TObjectID="24523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135184" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1388.000000 -14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135184" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24527"/>
     <cge:Term_Ref ObjectID="34610"/>
    <cge:TPSR_Ref TObjectID="24527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135185" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1388.000000 -14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24527"/>
     <cge:Term_Ref ObjectID="34610"/>
    <cge:TPSR_Ref TObjectID="24527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135181" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1388.000000 -14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135181" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24527"/>
     <cge:Term_Ref ObjectID="34610"/>
    <cge:TPSR_Ref TObjectID="24527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-135182" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1388.000000 -14.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24527"/>
     <cge:Term_Ref ObjectID="34610"/>
    <cge:TPSR_Ref TObjectID="24527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-135183" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1388.000000 -14.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135183" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24527"/>
     <cge:Term_Ref ObjectID="34610"/>
    <cge:TPSR_Ref TObjectID="24527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-135130" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 456.000000 -622.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24536"/>
     <cge:Term_Ref ObjectID="34628"/>
    <cge:TPSR_Ref TObjectID="24536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-135131" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 456.000000 -622.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135131" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24536"/>
     <cge:Term_Ref ObjectID="34628"/>
    <cge:TPSR_Ref TObjectID="24536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-135144" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -622.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24537"/>
     <cge:Term_Ref ObjectID="34632"/>
    <cge:TPSR_Ref TObjectID="24537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-135145" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -622.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24537"/>
     <cge:Term_Ref ObjectID="34632"/>
    <cge:TPSR_Ref TObjectID="24537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-301784" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="301784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46594"/>
     <cge:Term_Ref ObjectID="38079"/>
    <cge:TPSR_Ref TObjectID="46594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-301785" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="301785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46594"/>
     <cge:Term_Ref ObjectID="38079"/>
    <cge:TPSR_Ref TObjectID="46594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-301781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="301781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46594"/>
     <cge:Term_Ref ObjectID="38079"/>
    <cge:TPSR_Ref TObjectID="46594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-301782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.000000 -13.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="301782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46594"/>
     <cge:Term_Ref ObjectID="38079"/>
    <cge:TPSR_Ref TObjectID="46594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-301783" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.000000 -13.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="301783" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46594"/>
     <cge:Term_Ref ObjectID="38079"/>
    <cge:TPSR_Ref TObjectID="46594"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="-469" y="-1112"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="-469" y="-1112"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-517" y="-1129"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-517" y="-1129"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-176" y="-342"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-176" y="-342"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-48" y="-342"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-48" y="-342"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="85" y="-342"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="85" y="-342"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="213" y="-342"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="213" y="-342"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="891" y="-340"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="891" y="-340"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1059" y="-342"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1059" y="-342"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1233" y="-335"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1233" y="-335"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1408" y="-339"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1408" y="-339"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="387" y="-883"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="387" y="-883"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="837" y="-883"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="837" y="-883"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="233" y="-649"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="233" y="-649"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="792" y="-631"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="792" y="-631"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="616" y="-486"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="616" y="-486"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-320" y="-1091"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-320" y="-1091"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-320" y="-1126"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-320" y="-1126"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="57" x="-566" y="-758"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="57" x="-566" y="-758"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="359" y="-340"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="359" y="-340"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="-469" y="-1112"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-517" y="-1129"/></g>
   <g href="35kV红坡变10kV牟城Ⅵ回线491间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-176" y="-342"/></g>
   <g href="35kV红坡变10kV牟城Ⅳ回线492间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-48" y="-342"/></g>
   <g href="35kV红坡变10kV军屯线493间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="85" y="-342"/></g>
   <g href="35kV红坡变10kV蟠猫线494间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="213" y="-342"/></g>
   <g href="35kV红坡变10kV牟城Ⅴ回线495间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="891" y="-340"/></g>
   <g href="35kV红坡变10kV清波线496间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1059" y="-342"/></g>
   <g href="35kV红坡变10kV马厂线497间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1233" y="-335"/></g>
   <g href="35kV红坡变10kV万寿宫线498间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1408" y="-339"/></g>
   <g href="35kV红坡变35kV红坡T接线391间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="387" y="-883"/></g>
   <g href="35kV红坡变35kV红坡线392间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="837" y="-883"/></g>
   <g href="35kV红坡变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="233" y="-649"/></g>
   <g href="35kV红坡变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="792" y="-631"/></g>
   <g href="35kV红坡变10kV母联分段412间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="616" y="-486"/></g>
   <g href="cx_配调_配网接线图35_牟定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-320" y="-1091"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-320" y="-1126"/></g>
   <g href="35kV红坡变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="57" x="-566" y="-758"/></g>
   <g href="35kV红坡变10kV1号消弧线圈及接地变499间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="359" y="-340"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d77c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 1161.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d87c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 745.000000 1146.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 1131.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d97b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.000000 1166.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 307.000000 1151.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 332.000000 1136.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d9fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 105.000000 822.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13da860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.000000 852.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dadc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.000000 867.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13db040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.000000 883.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13db280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 119.000000 837.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13db4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 119.000000 806.000000) translate(0,12)">F(HZ):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13dc150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1180.000000 477.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1463ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1188.000000 507.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1463ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1188.000000 522.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1464130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1188.000000 538.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1464370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1194.000000 492.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14645b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1194.000000 461.000000) translate(0,12)">F(HZ):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14648e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -195.000000 479.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1464b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -187.000000 509.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1464da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -187.000000 524.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1464fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -187.000000 540.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1465220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -181.000000 494.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1465460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -181.000000 463.000000) translate(0,12)">F(HZ):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14664c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 583.000000 556.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1466790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.000000 541.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14669d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 597.000000 526.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146f0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 607.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146ff20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 622.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1470890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 943.000000 607.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1470b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 943.000000 622.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133c400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -274.000000 -52.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133c670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -274.000000 -36.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133c8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -274.000000 -18.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133caf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -288.000000 12.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133cd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -299.000000 -4.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133d060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 412.000000 670.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133dcc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 408.000000 688.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133df40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 394.000000 718.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133e180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 703.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133e4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 412.000000 480.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133e720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 408.000000 498.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133e960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 394.000000 528.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133eba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 513.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133eed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 972.000000 670.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133f140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 968.000000 688.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133f380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 954.000000 718.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133f5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 943.000000 703.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133f8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 972.000000 483.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133fb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 968.000000 501.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133fda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 954.000000 531.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133ffe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 943.000000 516.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="64" stroke="rgb(0,255,0)" stroke-width="1" width="28" x="358" y="-9"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="1" width="6" x="415" y="4"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="119" stroke="rgb(0,255,0)" stroke-width="1" width="27" x="406" y="-67"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(0,255,0)" stroke-width="1" width="34" x="321" y="4"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(0,255,0)" stroke-width="1" width="10" x="340" y="7"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-MD_HP.MD_HP_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="106,-783 1184,-783 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24473" ObjectName="BS-MD_HP.MD_HP_3IM"/>
    <cge:TPSR_Ref TObjectID="24473"/></metadata>
   <polyline fill="none" opacity="0" points="106,-783 1184,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_HP.MD_HP_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-222,-437 596,-437 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24474" ObjectName="BS-MD_HP.MD_HP_9IM"/>
    <cge:TPSR_Ref TObjectID="24474"/></metadata>
   <polyline fill="none" opacity="0" points="-222,-437 596,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_HP.MD_HP_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="669,-437 1472,-437 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24475" ObjectName="BS-MD_HP.MD_HP_9IIM"/>
    <cge:TPSR_Ref TObjectID="24475"/></metadata>
   <polyline fill="none" opacity="0" points="669,-437 1472,-437 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_145cc90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 285.000000 -972.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_145d600" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 744.000000 -972.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14c6b50" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1061.500000 -709.500000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14c7400" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 474.000000 -345.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14c7e30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 -344.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_152e400" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -267.000000 -362.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f1120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -136.666667 -361.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13f9450" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -4.333333 -361.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14be650" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 124.000000 -361.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_148d910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 797.000000 -360.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13e3ba0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 970.333333 -361.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14082a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1143.666667 -354.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13f1a50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1317.000000 -359.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15a0a20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 264.000000 -354.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15a3f10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 261.000000 -225.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24473" cx="828" cy="-783" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24475" cx="731" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24475" cx="886" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24473" cx="1150" cy="-783" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24475" cx="877" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24475" cx="1050" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24475" cx="1224" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24475" cx="1397" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24473" cx="886" cy="-783" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24473" cx="703" cy="-783" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24473" cx="378" cy="-783" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24473" cx="332" cy="-783" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24475" cx="680" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24474" cx="331" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24474" cx="566" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24474" cx="569" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24474" cx="438" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24474" cx="-187" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24474" cx="-57" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24474" cx="76" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24474" cx="203" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24474" cx="342" cy="-437" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1531710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -428.000000 -1100.500000) translate(0,16)">红坡变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14f8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1292c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 391.000000 -826.000000) translate(0,12)">3911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a1050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 315.500000 -998.000000) translate(0,12)">39167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_9f97a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 391.500000 -955.000000) translate(0,12)">3916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14108b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 840.000000 -822.000000) translate(0,12)">3921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1410a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 840.500000 -948.000000) translate(0,12)">3926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1410c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 768.000000 -998.000000) translate(0,12)">39267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1410dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 107.000000 -774.000000) translate(0,12)">35kV  I母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1410f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -466.000000) translate(0,12)">10kV  I母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14111d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 192.000000 -614.000000) translate(0,12)">Sz11-5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1411380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 312.000000 -1114.000000) translate(0,12)">35kV红坡T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1519010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1084.000000 -687.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1519340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -745.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15194f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -702.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15196a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -517.000000) translate(0,12)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1519850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1138.000000 -601.000000) translate(0,12)">35kV电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1519a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -745.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1519d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -702.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1519ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 744.000000 -608.000000) translate(0,12)">Sz11-5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_151a090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.000000 -466.000000) translate(0,12)">10kV  II母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_151a240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -220.000000 -67.000000) translate(0,12)">10kV牟城VI回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_151a430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 518.000000 -474.000000) translate(0,12)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_151a5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 694.000000 -474.000000) translate(0,12)">4122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_151a760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -517.000000) translate(0,12)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_144c770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -1113.000000) translate(0,12)">35kV红坡线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1438920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 393.000000 -229.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f3670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 612.000000 -675.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14591e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1166.000000 -741.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_145b930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -551.000000) translate(0,12)">4016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_145be20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 345.000000 -473.000000) translate(0,12)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f4d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -551.000000) translate(0,12)">4026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b5ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 903.000000 -473.000000) translate(0,12)">4021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b6100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -257.000000 -396.000000) translate(0,12)">49117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b6340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -405.000000) translate(0,12)">4911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b6580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -287.000000) translate(0,12)">4912</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b67c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -174.000000) translate(0,12)">4916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b6a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -45.000000 -405.000000) translate(0,12)">4921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b6c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -45.000000 -287.000000) translate(0,12)">4922</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b6e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -45.000000 -174.000000) translate(0,12)">4926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b70c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -131.000000 -396.000000) translate(0,12)">49217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b7300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 89.000000 -405.000000) translate(0,12)">4931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b7540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 89.000000 -287.000000) translate(0,12)">4932</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b7780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 89.000000 -174.000000) translate(0,12)">4936</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b79c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 8.000000 -396.000000) translate(0,12)">49317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b7c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 216.000000 -405.000000) translate(0,12)">4941</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b7e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 216.000000 -287.000000) translate(0,12)">4942</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b8080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -396.000000) translate(0,12)">49417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b82c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 216.000000 -174.000000) translate(0,12)">4946</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b8500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 445.000000 -391.000000) translate(0,12)">4900</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b8740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 575.000000 -388.000000) translate(0,12)">4901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b8980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 507.000000 -373.000000) translate(0,12)">49017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b8bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 737.000000 -384.000000) translate(0,12)">4902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b9080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 -372.000000) translate(0,12)">49027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147a110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 891.000000 -405.000000) translate(0,12)">4951</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147a340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 891.000000 -287.000000) translate(0,12)">4952</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147a580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 891.000000 -174.000000) translate(0,12)">4956</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147a7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 812.000000 -387.000000) translate(0,12)">49517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147aa00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 985.000000 -396.000000) translate(0,12)">49617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1063.000000 -287.000000) translate(0,12)">4962</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147ae80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1063.000000 -174.000000) translate(0,12)">4966</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147b0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 -405.000000) translate(0,12)">4971</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147b300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 -287.000000) translate(0,12)">4972</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147b540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 -174.000000) translate(0,12)">4976</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147b780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1410.000000 -287.000000) translate(0,12)">4982</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147b9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1410.000000 -174.000000) translate(0,12)">4986</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147bc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -396.000000) translate(0,12)">49817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147be40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1063.000000 -405.000000) translate(0,12)">4961</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147c080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1154.000000 -396.000000) translate(0,12)">49717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147c2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -85.000000 -67.000000) translate(0,12)">10kV牟城IV回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147c510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 60.000000 -67.000000) translate(0,12)">10kV军屯线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 183.000000 -67.000000) translate(0,12)"> 10kV蟠猫线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147ca10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 515.000000 -237.000000) translate(0,12)"> 10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147ca10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 515.000000 -237.000000) translate(0,27)"> 电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147ccb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 680.000000 -237.000000) translate(0,12)"> 10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147ccb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 680.000000 -237.000000) translate(0,27)"> 电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147cef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -66.000000) translate(0,12)">10kV牟城V回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147d140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1032.000000 -66.000000) translate(0,12)">10kV清波线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147d370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1201.000000 -61.000000) translate(0,12)">10kV马厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_145c190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -64.000000) translate(0,12)">10kV万寿宫线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d70f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1410.000000 -405.000000) translate(0,12)">4981</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1471870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -176.000000 -342.000000) translate(0,12)">491</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1471bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -48.000000 -342.000000) translate(0,12)">492</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133a340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -342.000000) translate(0,12)">493</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133a580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 213.000000 -342.000000) translate(0,12)">494</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133a7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 891.000000 -340.000000) translate(0,12)">495</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133aa00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -342.000000) translate(0,12)">496</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -335.000000) translate(0,12)">497</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133ae80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -339.000000) translate(0,12)">498</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133bb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -883.000000) translate(0,12)">391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133c0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 837.000000 -883.000000) translate(0,12)">392</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1342070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 233.000000 -649.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1342a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 792.000000 -631.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1342ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1342ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1342ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1342ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1342ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1342ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1342ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1342ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1342ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_134e820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 616.000000 -486.000000) translate(0,12)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_14ecf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -309.000000 -1083.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_147f300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -309.000000 -1118.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_14dfae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -566.000000 -758.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="20" graphid="g_14e0510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 -580.000000) translate(0,16)">1、2号主变档位联调</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13c3720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -587.000000 -119.500000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13c3720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -587.000000 -119.500000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13c5db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -460.000000 -128.500000) translate(0,16)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13c5db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -460.000000 -128.500000) translate(0,36)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13c5db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -460.000000 -128.500000) translate(0,56)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13c7810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -460.000000 -173.000000) translate(0,16)">5211726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13c7810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -460.000000 -173.000000) translate(0,36)">2255</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13813f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 451.000000 -88.000000) translate(0,12)"> 10kV1号消弧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13813f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 451.000000 -88.000000) translate(0,27)">线圈及接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1382a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 359.000000 -340.000000) translate(0,12)">499</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1382cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 355.000000 -406.000000) translate(0,12)">4991</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1382f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 355.000000 -287.000000) translate(0,12)">4996</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1383150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 282.000000 -384.000000) translate(0,12)">49917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1383390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 289.000000 -254.000000) translate(0,12)">49967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13835d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 327.000000 -113.000000) translate(0,12)">4990</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="340" cy="-142" fill="none" fillStyle="0" r="15" stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-135208">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.000000 -797.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24478" ObjectName="SW-MD_HP.MD_HP_3911SW"/>
     <cge:Meas_Ref ObjectId="135208"/>
    <cge:TPSR_Ref TObjectID="24478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135209">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 369.000000 -912.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24479" ObjectName="SW-MD_HP.MD_HP_3916SW"/>
     <cge:Meas_Ref ObjectId="135209"/>
    <cge:TPSR_Ref TObjectID="24479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135225">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 819.000000 -912.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24483" ObjectName="SW-MD_HP.MD_HP_3926SW"/>
     <cge:Meas_Ref ObjectId="135225"/>
    <cge:TPSR_Ref TObjectID="24483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135224">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 819.000000 -797.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24482" ObjectName="SW-MD_HP.MD_HP_3921SW"/>
     <cge:Meas_Ref ObjectId="135224"/>
    <cge:TPSR_Ref TObjectID="24482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135210">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 365.000000 -969.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24480" ObjectName="SW-MD_HP.MD_HP_39167SW"/>
     <cge:Meas_Ref ObjectId="135210"/>
    <cge:TPSR_Ref TObjectID="24480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135226">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 822.000000 -969.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24484" ObjectName="SW-MD_HP.MD_HP_39267SW"/>
     <cge:Meas_Ref ObjectId="135226"/>
    <cge:TPSR_Ref TObjectID="24484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135307">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 876.554483 -724.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24493" ObjectName="SW-MD_HP.MD_HP_3021SW"/>
     <cge:Meas_Ref ObjectId="135307"/>
    <cge:TPSR_Ref TObjectID="24493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135256">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.980530 -727.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24488" ObjectName="SW-MD_HP.MD_HP_3011SW"/>
     <cge:Meas_Ref ObjectId="135256"/>
    <cge:TPSR_Ref TObjectID="24488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135262">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.217396 -529.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24490" ObjectName="SW-MD_HP.MD_HP_4016SW"/>
     <cge:Meas_Ref ObjectId="135262"/>
    <cge:TPSR_Ref TObjectID="24490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.217396 -444.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24491" ObjectName="SW-MD_HP.MD_HP_4011SW"/>
     <cge:Meas_Ref ObjectId="135263"/>
    <cge:TPSR_Ref TObjectID="24491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135315">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 876.554483 -441.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24496" ObjectName="SW-MD_HP.MD_HP_4021SW"/>
     <cge:Meas_Ref ObjectId="135315"/>
    <cge:TPSR_Ref TObjectID="24496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135314">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 876.554483 -525.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24495" ObjectName="SW-MD_HP.MD_HP_4026SW"/>
     <cge:Meas_Ref ObjectId="135314"/>
    <cge:TPSR_Ref TObjectID="24495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 670.630140 -443.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24533" ObjectName="SW-MD_HP.MD_HP_4122SW"/>
     <cge:Meas_Ref ObjectId="135491"/>
    <cge:TPSR_Ref TObjectID="24533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135490">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.630140 -445.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24532" ObjectName="SW-MD_HP.MD_HP_4121SW"/>
     <cge:Meas_Ref ObjectId="135490"/>
    <cge:TPSR_Ref TObjectID="24532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 674.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135359">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 722.000000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24498" ObjectName="SW-MD_HP.MD_HP_4902SW"/>
     <cge:Meas_Ref ObjectId="135359"/>
    <cge:TPSR_Ref TObjectID="24498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135356">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 560.000000 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24497" ObjectName="SW-MD_HP.MD_HP_4901SW"/>
     <cge:Meas_Ref ObjectId="135356"/>
    <cge:TPSR_Ref TObjectID="24497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 508.000000 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135506">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 429.402135 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24534" ObjectName="SW-MD_HP.MD_HP_4900SW"/>
     <cge:Meas_Ref ObjectId="135506"/>
    <cge:TPSR_Ref TObjectID="24534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.500000 -706.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135251">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1135.500000 -694.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24486" ObjectName="SW-MD_HP.MD_HP_39017SW"/>
     <cge:Meas_Ref ObjectId="135251"/>
    <cge:TPSR_Ref TObjectID="24486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135250">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1141.000000 -709.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24485" ObjectName="SW-MD_HP.MD_HP_3901SW"/>
     <cge:Meas_Ref ObjectId="135250"/>
    <cge:TPSR_Ref TObjectID="24485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 0.913043 0.000000 -241.000000 -376.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135363">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -196.324772 -259.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24501" ObjectName="SW-MD_HP.MD_HP_4912SW"/>
     <cge:Meas_Ref ObjectId="135363"/>
    <cge:TPSR_Ref TObjectID="24501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135362">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -196.324772 -377.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24500" ObjectName="SW-MD_HP.MD_HP_4911SW"/>
     <cge:Meas_Ref ObjectId="135362"/>
    <cge:TPSR_Ref TObjectID="24500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135364">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -195.693092 -148.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24502" ObjectName="SW-MD_HP.MD_HP_4916SW"/>
     <cge:Meas_Ref ObjectId="135364"/>
    <cge:TPSR_Ref TObjectID="24502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 0.913043 0.000000 -110.666667 -376.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135379">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -65.991439 -259.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24505" ObjectName="SW-MD_HP.MD_HP_4922SW"/>
     <cge:Meas_Ref ObjectId="135379"/>
    <cge:TPSR_Ref TObjectID="24505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -65.991439 -377.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24504" ObjectName="SW-MD_HP.MD_HP_4921SW"/>
     <cge:Meas_Ref ObjectId="135378"/>
    <cge:TPSR_Ref TObjectID="24504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135380">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -66.359759 -148.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24506" ObjectName="SW-MD_HP.MD_HP_4926SW"/>
     <cge:Meas_Ref ObjectId="135380"/>
    <cge:TPSR_Ref TObjectID="24506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 0.913043 0.000000 22.666667 -376.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135395">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 67.341894 -259.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24509" ObjectName="SW-MD_HP.MD_HP_4932SW"/>
     <cge:Meas_Ref ObjectId="135395"/>
    <cge:TPSR_Ref TObjectID="24509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135394">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 67.341894 -377.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24508" ObjectName="SW-MD_HP.MD_HP_4931SW"/>
     <cge:Meas_Ref ObjectId="135394"/>
    <cge:TPSR_Ref TObjectID="24508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135396">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 66.973574 -148.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24510" ObjectName="SW-MD_HP.MD_HP_4936SW"/>
     <cge:Meas_Ref ObjectId="135396"/>
    <cge:TPSR_Ref TObjectID="24510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 0.913043 0.000000 150.000000 -376.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135411">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 193.675228 -259.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24513" ObjectName="SW-MD_HP.MD_HP_4942SW"/>
     <cge:Meas_Ref ObjectId="135411"/>
    <cge:TPSR_Ref TObjectID="24513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135410">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 193.675228 -377.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24512" ObjectName="SW-MD_HP.MD_HP_4941SW"/>
     <cge:Meas_Ref ObjectId="135410"/>
    <cge:TPSR_Ref TObjectID="24512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 194.306908 -148.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24514" ObjectName="SW-MD_HP.MD_HP_4946SW"/>
     <cge:Meas_Ref ObjectId="135412"/>
    <cge:TPSR_Ref TObjectID="24514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 0.913043 0.000000 824.000000 -375.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135427">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 867.675228 -258.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24517" ObjectName="SW-MD_HP.MD_HP_4952SW"/>
     <cge:Meas_Ref ObjectId="135427"/>
    <cge:TPSR_Ref TObjectID="24517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135426">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 867.675228 -376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24516" ObjectName="SW-MD_HP.MD_HP_4951SW"/>
     <cge:Meas_Ref ObjectId="135426"/>
    <cge:TPSR_Ref TObjectID="24516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135428">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 868.306908 -147.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24518" ObjectName="SW-MD_HP.MD_HP_4956SW"/>
     <cge:Meas_Ref ObjectId="135428"/>
    <cge:TPSR_Ref TObjectID="24518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 0.913043 0.000000 996.333333 -376.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135443">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1041.008561 -259.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24521" ObjectName="SW-MD_HP.MD_HP_4962SW"/>
     <cge:Meas_Ref ObjectId="135443"/>
    <cge:TPSR_Ref TObjectID="24521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135442">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1041.008561 -377.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24520" ObjectName="SW-MD_HP.MD_HP_4961SW"/>
     <cge:Meas_Ref ObjectId="135442"/>
    <cge:TPSR_Ref TObjectID="24520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135444">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1040.640241 -148.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24522" ObjectName="SW-MD_HP.MD_HP_4966SW"/>
     <cge:Meas_Ref ObjectId="135444"/>
    <cge:TPSR_Ref TObjectID="24522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 0.913043 0.000000 1170.666667 -369.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135459">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.341894 -252.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24525" ObjectName="SW-MD_HP.MD_HP_4972SW"/>
     <cge:Meas_Ref ObjectId="135459"/>
    <cge:TPSR_Ref TObjectID="24525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135458">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.341894 -370.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24524" ObjectName="SW-MD_HP.MD_HP_4971SW"/>
     <cge:Meas_Ref ObjectId="135458"/>
    <cge:TPSR_Ref TObjectID="24524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135460">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.973574 -141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24526" ObjectName="SW-MD_HP.MD_HP_4976SW"/>
     <cge:Meas_Ref ObjectId="135460"/>
    <cge:TPSR_Ref TObjectID="24526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 0.913043 0.000000 1344.000000 -374.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135475">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1387.675228 -257.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24529" ObjectName="SW-MD_HP.MD_HP_4982SW"/>
     <cge:Meas_Ref ObjectId="135475"/>
    <cge:TPSR_Ref TObjectID="24529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135474">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1387.675228 -375.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24528" ObjectName="SW-MD_HP.MD_HP_4981SW"/>
     <cge:Meas_Ref ObjectId="135474"/>
    <cge:TPSR_Ref TObjectID="24528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-135476">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1388.306908 -146.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24530" ObjectName="SW-MD_HP.MD_HP_4986SW"/>
     <cge:Meas_Ref ObjectId="135476"/>
    <cge:TPSR_Ref TObjectID="24530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-301788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 -376.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46595" ObjectName="SW-MD_HP.MD_HP_4991SW"/>
     <cge:Meas_Ref ObjectId="301788"/>
    <cge:TPSR_Ref TObjectID="46595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-301789">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 -256.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46596" ObjectName="SW-MD_HP.MD_HP_4996SW"/>
     <cge:Meas_Ref ObjectId="301789"/>
    <cge:TPSR_Ref TObjectID="46596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-301790">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 0.913043 0.000000 290.000000 -369.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46597" ObjectName="SW-MD_HP.MD_HP_49917SW"/>
     <cge:Meas_Ref ObjectId="301790"/>
    <cge:TPSR_Ref TObjectID="46597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-301791">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 0.913043 0.000000 287.000000 -240.033815)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46598" ObjectName="SW-MD_HP.MD_HP_49967SW"/>
     <cge:Meas_Ref ObjectId="301791"/>
    <cge:TPSR_Ref TObjectID="46598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-301816">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.000000 -85.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46599" ObjectName="SW-MD_HP.MD_HP_4990SW"/>
     <cge:Meas_Ref ObjectId="301816"/>
    <cge:TPSR_Ref TObjectID="46599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.642857 -0.000000 0.000000 -0.630435 425.000000 -12.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1518090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-917 378,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24479@0" ObjectIDZND0="24477@1" Pin0InfoVect0LinkObjId="SW-135206_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135209_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,-917 378,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1518280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="332,-783 332,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24473@0" ObjectIDZND0="24488@1" Pin0InfoVect0LinkObjId="SW-135256_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1518850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="332,-783 332,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1518470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="332,-732 332,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24488@0" ObjectIDZND0="24487@1" Pin0InfoVect0LinkObjId="SW-135254_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135256_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="332,-732 332,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1518660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="332,-679 332,-662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24487@0" ObjectIDZND0="24536@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135254_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="332,-679 332,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1518850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="886,-765 886,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24493@1" ObjectIDZND0="24473@0" Pin0InfoVect0LinkObjId="g_146e830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135307_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="886,-765 886,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1518a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="886,-729 886,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24493@0" ObjectIDZND0="24492@1" Pin0InfoVect0LinkObjId="SW-135305_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135307_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="886,-729 886,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1518c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="886,-676 886,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24492@0" ObjectIDZND0="24537@1" Pin0InfoVect0LinkObjId="g_1501960_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135305_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="886,-676 886,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1518e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-862 378,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24477@0" ObjectIDZND0="24478@1" Pin0InfoVect0LinkObjId="SW-135208_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135206_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,-862 378,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14da810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="331,-582 331,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="24536@0" ObjectIDZND0="24490@1" Pin0InfoVect0LinkObjId="SW-135262_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1518660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="331,-582 331,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14daa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="331,-534 331,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24490@0" ObjectIDZND0="24489@1" Pin0InfoVect0LinkObjId="SW-135260_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135262_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="331,-534 331,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14dac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="331,-496 331,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24489@0" ObjectIDZND0="24491@1" Pin0InfoVect0LinkObjId="SW-135263_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="331,-496 331,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14dae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="331,-449 331,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24491@0" ObjectIDZND0="24474@0" Pin0InfoVect0LinkObjId="g_14cddf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135263_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="331,-449 331,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1501960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="886,-566 886,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="24495@1" ObjectIDZND0="24537@0" Pin0InfoVect0LinkObjId="g_1518c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135314_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="886,-566 886,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1501b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="886,-530 886,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24495@0" ObjectIDZND0="24494@1" Pin0InfoVect0LinkObjId="SW-135312_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="886,-530 886,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1501da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="886,-492 886,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24494@0" ObjectIDZND0="24496@1" Pin0InfoVect0LinkObjId="SW-135315_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135312_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="886,-492 886,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1501fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="886,-446 886,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24496@0" ObjectIDZND0="24475@0" Pin0InfoVect0LinkObjId="g_14ce450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135315_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="886,-446 886,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14cddf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-450 566,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24532@0" ObjectIDZND0="24474@0" Pin0InfoVect0LinkObjId="g_14dae70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="566,-450 566,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ce010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-486 566,-499 611,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24532@1" ObjectIDZND0="24531@1" Pin0InfoVect0LinkObjId="SW-135488_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135490_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="566,-486 566,-499 611,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ce230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-498 680,-498 680,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24531@0" ObjectIDZND0="24533@1" Pin0InfoVect0LinkObjId="SW-135491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135488_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-498 680,-498 680,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ce450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="680,-448 680,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24533@0" ObjectIDZND0="24475@0" Pin0InfoVect0LinkObjId="g_1501fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="680,-448 680,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_140d950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-344 731,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_14d0890@0" ObjectIDND1="0@x" ObjectIDND2="24498@x" ObjectIDZND0="g_13bf1a0@0" Pin0InfoVect0LinkObjId="g_13bf1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14d0890_0" Pin1InfoVect1LinkObjId="g_13be5b0_0" Pin1InfoVect2LinkObjId="SW-135359_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="731,-344 731,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_140db70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="775,-344 731,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_14d0890@0" ObjectIDZND0="g_13bf1a0@0" ObjectIDZND1="0@x" ObjectIDZND2="24498@x" Pin0InfoVect0LinkObjId="g_13bf1a0_0" Pin0InfoVect1LinkObjId="g_13be5b0_0" Pin0InfoVect2LinkObjId="SW-135359_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14d0890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="775,-344 731,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_140dd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-437 731,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24475@0" ObjectIDZND0="24498@1" Pin0InfoVect0LinkObjId="SW-135359_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1501fc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="731,-437 731,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1513a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="569,-437 569,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24474@0" ObjectIDZND0="24497@1" Pin0InfoVect0LinkObjId="SW-135356_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14dae70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="569,-437 569,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f1e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="438,-332 438,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="24534@0" Pin0InfoVect0LinkObjId="SW-135506_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="438,-332 438,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f2090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="438,-401 438,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24534@1" ObjectIDZND0="24474@0" Pin0InfoVect0LinkObjId="g_14dae70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135506_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="438,-401 438,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f3230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="703,-783 703,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24473@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_13be5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1518850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="703,-783 703,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13f3450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="703,-709 703,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_13be5b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="703,-709 703,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1458da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1150,-695 1150,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_13f5bb0@0" ObjectIDND1="24486@x" ObjectIDND2="24485@x" ObjectIDZND0="g_13c1000@0" Pin0InfoVect0LinkObjId="g_13c1000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13f5bb0_0" Pin1InfoVect1LinkObjId="SW-135251_0" Pin1InfoVect2LinkObjId="SW-135250_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1150,-695 1150,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1458fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1194,-695 1150,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13f5bb0@0" ObjectIDZND0="g_13c1000@0" ObjectIDZND1="24486@x" ObjectIDZND2="24485@x" Pin0InfoVect0LinkObjId="g_13c1000_0" Pin0InfoVect1LinkObjId="SW-135251_0" Pin0InfoVect2LinkObjId="SW-135250_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13f5bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1194,-695 1150,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14595b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1150,-783 1150,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24473@0" ObjectIDZND0="24485@1" Pin0InfoVect0LinkObjId="SW-135250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1518850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1150,-783 1150,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145e030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="828,-783 828,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24473@0" ObjectIDZND0="24482@0" Pin0InfoVect0LinkObjId="SW-135224_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1518850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="828,-783 828,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145e290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="828,-838 828,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24482@1" ObjectIDZND0="24481@0" Pin0InfoVect0LinkObjId="SW-135222_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135224_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="828,-838 828,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145e4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="828,-889 828,-917 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24481@1" ObjectIDZND0="24483@0" Pin0InfoVect0LinkObjId="SW-135225_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135222_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="828,-889 828,-917 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145e750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-978 828,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24484@0" ObjectIDZND0="24483@x" ObjectIDZND1="g_144c920@0" ObjectIDZND2="g_1349290@0" Pin0InfoVect0LinkObjId="SW-135225_0" Pin0InfoVect1LinkObjId="g_144c920_0" Pin0InfoVect2LinkObjId="g_1349290_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135226_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="817,-978 828,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145f220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="828,-953 828,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24483@1" ObjectIDZND0="24484@x" ObjectIDZND1="g_144c920@0" ObjectIDZND2="g_1349290@0" Pin0InfoVect0LinkObjId="SW-135226_0" Pin0InfoVect1LinkObjId="g_144c920_0" Pin0InfoVect2LinkObjId="g_1349290_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135225_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="828,-953 828,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145f480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-978 303,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24480@1" ObjectIDZND0="g_145cc90@0" Pin0InfoVect0LinkObjId="g_145cc90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-978 303,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14c5770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="781,-978 762,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24484@1" ObjectIDZND0="g_145d600@0" Pin0InfoVect0LinkObjId="g_145d600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135226_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="781,-978 762,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14c59d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="773,-1030 773,-1014 828,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_144c920@0" ObjectIDZND0="24484@x" ObjectIDZND1="24483@x" ObjectIDZND2="g_1349290@0" Pin0InfoVect0LinkObjId="SW-135226_0" Pin0InfoVect1LinkObjId="SW-135225_0" Pin0InfoVect2LinkObjId="g_1349290_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_144c920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="773,-1030 773,-1014 828,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14c6690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="828,-978 828,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24484@x" ObjectIDND1="24483@x" ObjectIDZND0="g_144c920@0" ObjectIDZND1="g_1349290@0" ObjectIDZND2="37786@1" Pin0InfoVect0LinkObjId="g_144c920_0" Pin0InfoVect1LinkObjId="g_1349290_0" Pin0InfoVect2LinkObjId="g_14c68f0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135226_0" Pin1InfoVect1LinkObjId="SW-135225_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="828,-978 828,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14c68f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="828,-1014 828,-1056 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_144c920@0" ObjectIDND1="24484@x" ObjectIDND2="24483@x" ObjectIDZND0="37786@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_144c920_0" Pin1InfoVect1LinkObjId="SW-135226_0" Pin1InfoVect2LinkObjId="SW-135225_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="828,-1014 828,-1056 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14c6f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1079,-704 1094,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_14c6b50@0" ObjectIDZND0="24486@1" Pin0InfoVect0LinkObjId="SW-135251_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14c6b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1079,-704 1094,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14c71a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="438,-283 438,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="438,-283 438,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c8860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="715,-350 731,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="24498@x" ObjectIDZND1="g_13bf1a0@0" ObjectIDZND2="g_14d0890@0" Pin0InfoVect0LinkObjId="SW-135359_0" Pin0InfoVect1LinkObjId="g_13bf1a0_0" Pin0InfoVect2LinkObjId="g_14d0890_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="715,-350 731,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14d48f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-363 731,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="24498@0" ObjectIDZND0="0@x" ObjectIDZND1="g_13bf1a0@0" ObjectIDZND2="g_14d0890@0" Pin0InfoVect0LinkObjId="g_13be5b0_0" Pin0InfoVect1LinkObjId="g_13bf1a0_0" Pin0InfoVect2LinkObjId="g_14d0890_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135359_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="731,-363 731,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14d4b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="731,-350 731,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="24498@x" ObjectIDZND0="g_13bf1a0@0" ObjectIDZND1="g_14d0890@0" Pin0InfoVect0LinkObjId="g_13bf1a0_0" Pin0InfoVect1LinkObjId="g_14d0890_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="SW-135359_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="731,-350 731,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14d4db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="679,-350 663,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_14c7e30@0" Pin0InfoVect0LinkObjId="g_14c7e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="679,-350 663,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14d5010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="513,-351 492,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_14c7400@0" Pin0InfoVect0LinkObjId="g_14c7400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="513,-351 492,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d5270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1130,-704 1150,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="24486@0" ObjectIDZND0="24485@x" ObjectIDZND1="g_13c1000@0" ObjectIDZND2="g_13f5bb0@0" Pin0InfoVect0LinkObjId="SW-135250_0" Pin0InfoVect1LinkObjId="g_13c1000_0" Pin0InfoVect2LinkObjId="g_13f5bb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135251_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1130,-704 1150,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d5d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1150,-714 1150,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="24485@0" ObjectIDZND0="24486@x" ObjectIDZND1="g_13c1000@0" ObjectIDZND2="g_13f5bb0@0" Pin0InfoVect0LinkObjId="SW-135251_0" Pin0InfoVect1LinkObjId="g_13c1000_0" Pin0InfoVect2LinkObjId="g_13f5bb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1150,-714 1150,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d5fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1150,-704 1150,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="24486@x" ObjectIDND1="24485@x" ObjectIDZND0="g_13c1000@0" ObjectIDZND1="g_13f5bb0@0" Pin0InfoVect0LinkObjId="g_13c1000_0" Pin0InfoVect1LinkObjId="g_13f5bb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135251_0" Pin1InfoVect1LinkObjId="SW-135250_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1150,-704 1150,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_152bc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-187,-321 -187,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24499@0" ObjectIDZND0="24501@1" Pin0InfoVect0LinkObjId="SW-135363_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-187,-321 -187,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_152df40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-187,-264 -187,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24501@0" ObjectIDZND0="g_14cb4c0@0" Pin0InfoVect0LinkObjId="g_14cb4c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135363_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-187,-264 -187,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_152e1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-187,-201 -187,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_14cb4c0@1" ObjectIDZND0="24502@1" Pin0InfoVect0LinkObjId="SW-135364_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14cb4c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-187,-201 -187,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_152ee90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-203,-367 -187,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="24500@x" ObjectIDZND1="24499@x" Pin0InfoVect0LinkObjId="SW-135362_0" Pin0InfoVect1LinkObjId="SW-135360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-203,-367 -187,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_152f0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-187,-348 -187,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24499@1" ObjectIDZND0="24500@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-135362_0" Pin0InfoVect1LinkObjId="g_13be5b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-187,-348 -187,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ca240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-187,-367 -187,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24499@x" ObjectIDND1="0@x" ObjectIDZND0="24500@0" Pin0InfoVect0LinkObjId="SW-135362_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135360_0" Pin1InfoVect1LinkObjId="g_13be5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-187,-367 -187,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14ca4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-236,-367 -249,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_152e400@0" Pin0InfoVect0LinkObjId="g_152e400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-236,-367 -249,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14cada0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-145,-131 -145,-144 -187,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_14d6200@0" ObjectIDZND0="24502@x" ObjectIDZND1="34287@x" Pin0InfoVect0LinkObjId="SW-135364_0" Pin0InfoVect1LinkObjId="EC-MD_HP.491Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14d6200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-145,-131 -145,-144 -187,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14cb000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-187,-153 -187,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24502@0" ObjectIDZND0="g_14d6200@0" ObjectIDZND1="34287@x" Pin0InfoVect0LinkObjId="g_14d6200_0" Pin0InfoVect1LinkObjId="EC-MD_HP.491Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135364_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-187,-153 -187,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14cb260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-187,-144 -187,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_14d6200@0" ObjectIDND1="24502@x" ObjectIDZND0="34287@0" Pin0InfoVect0LinkObjId="EC-MD_HP.491Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14d6200_0" Pin1InfoVect1LinkObjId="SW-135364_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-187,-144 -187,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14ee930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-57,-321 -57,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24503@0" ObjectIDZND0="24505@1" Pin0InfoVect0LinkObjId="SW-135379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-57,-321 -57,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f0c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-57,-264 -57,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24505@0" ObjectIDZND0="g_141c660@0" Pin0InfoVect0LinkObjId="g_141c660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-57,-264 -57,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f0ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-57,-201 -57,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_141c660@1" ObjectIDZND0="24506@1" Pin0InfoVect0LinkObjId="SW-135380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_141c660_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-57,-201 -57,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f1bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-73,-367 -57,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="24503@x" ObjectIDZND1="24504@x" Pin0InfoVect0LinkObjId="SW-135376_0" Pin0InfoVect1LinkObjId="SW-135378_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-73,-367 -57,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_141b120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-57,-348 -57,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24503@1" ObjectIDZND0="0@x" ObjectIDZND1="24504@x" Pin0InfoVect0LinkObjId="g_13be5b0_0" Pin0InfoVect1LinkObjId="SW-135378_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135376_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-57,-348 -57,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_141b360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-57,-367 -57,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24503@x" ObjectIDND1="0@x" ObjectIDZND0="24504@0" Pin0InfoVect0LinkObjId="SW-135378_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135376_0" Pin1InfoVect1LinkObjId="g_13be5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-57,-367 -57,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_141b5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-106,-367 -119,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_14f1120@0" Pin0InfoVect0LinkObjId="g_14f1120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-106,-367 -119,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_141bf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-15,-131 -15,-144 -57,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_14cbe40@0" ObjectIDZND0="24506@x" ObjectIDZND1="34288@x" Pin0InfoVect0LinkObjId="SW-135380_0" Pin0InfoVect1LinkObjId="EC-MD_HP.492Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14cbe40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-15,-131 -15,-144 -57,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_141c1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-57,-153 -57,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24506@0" ObjectIDZND0="g_14cbe40@0" ObjectIDZND1="34288@x" Pin0InfoVect0LinkObjId="g_14cbe40_0" Pin0InfoVect1LinkObjId="EC-MD_HP.492Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-57,-153 -57,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_141c400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-57,-144 -57,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24506@x" ObjectIDND1="g_14cbe40@0" ObjectIDZND0="34288@0" Pin0InfoVect0LinkObjId="EC-MD_HP.492Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135380_0" Pin1InfoVect1LinkObjId="g_14cbe40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-57,-144 -57,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13f6c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="76,-321 76,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24507@0" ObjectIDZND0="24509@1" Pin0InfoVect0LinkObjId="SW-135395_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135392_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="76,-321 76,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13f8f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="76,-264 76,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24509@0" ObjectIDZND0="g_143ebc0@0" Pin0InfoVect0LinkObjId="g_143ebc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="76,-264 76,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13f91f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="76,-201 76,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_143ebc0@1" ObjectIDZND0="24510@1" Pin0InfoVect0LinkObjId="SW-135396_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_143ebc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="76,-201 76,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13f9ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="60,-367 76,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="24507@x" ObjectIDZND1="24508@x" Pin0InfoVect0LinkObjId="SW-135392_0" Pin0InfoVect1LinkObjId="SW-135394_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="60,-367 76,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13fa140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="76,-348 76,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24507@1" ObjectIDZND0="0@x" ObjectIDZND1="24508@x" Pin0InfoVect0LinkObjId="g_13be5b0_0" Pin0InfoVect1LinkObjId="SW-135394_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="76,-348 76,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13fa3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="76,-367 76,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24507@x" ObjectIDND1="0@x" ObjectIDZND0="24508@0" Pin0InfoVect0LinkObjId="SW-135394_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135392_0" Pin1InfoVect1LinkObjId="g_13be5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="76,-367 76,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13fa600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="27,-367 14,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_13f9450@0" Pin0InfoVect0LinkObjId="g_13f9450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="27,-367 14,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13faf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="118,-131 118,-144 76,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_141e200@0" ObjectIDZND0="24510@x" ObjectIDZND1="34289@x" Pin0InfoVect0LinkObjId="SW-135396_0" Pin0InfoVect1LinkObjId="EC-MD_HP.493Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_141e200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="118,-131 118,-144 76,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_143e700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="76,-153 76,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24510@0" ObjectIDZND0="g_141e200@0" ObjectIDZND1="34289@x" Pin0InfoVect0LinkObjId="g_141e200_0" Pin0InfoVect1LinkObjId="EC-MD_HP.493Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135396_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="76,-153 76,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_143e960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="76,-144 76,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24510@x" ObjectIDND1="g_141e200@0" ObjectIDZND0="34289@0" Pin0InfoVect0LinkObjId="EC-MD_HP.493Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135396_0" Pin1InfoVect1LinkObjId="g_141e200_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="76,-144 76,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14bbe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="203,-321 203,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24511@0" ObjectIDZND0="24513@1" Pin0InfoVect0LinkObjId="SW-135411_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135408_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="203,-321 203,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14be190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="203,-264 203,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24513@0" ObjectIDZND0="g_14c08a0@0" Pin0InfoVect0LinkObjId="g_14c08a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135411_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="203,-264 203,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14be3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="203,-201 203,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_14c08a0@1" ObjectIDZND0="24514@1" Pin0InfoVect0LinkObjId="SW-135412_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14c08a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="203,-201 203,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14bf0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,-367 203,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="24511@x" ObjectIDZND1="24512@x" Pin0InfoVect0LinkObjId="SW-135408_0" Pin0InfoVect1LinkObjId="SW-135410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="187,-367 203,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14bf340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="203,-348 203,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24511@1" ObjectIDZND0="0@x" ObjectIDZND1="24512@x" Pin0InfoVect0LinkObjId="g_13be5b0_0" Pin0InfoVect1LinkObjId="SW-135410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135408_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="203,-348 203,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14bf5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="203,-367 203,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24511@x" ObjectIDND1="0@x" ObjectIDZND0="24512@0" Pin0InfoVect0LinkObjId="SW-135410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135408_0" Pin1InfoVect1LinkObjId="g_13be5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="203,-367 203,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14bf800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="154,-367 142,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_14be650@0" Pin0InfoVect0LinkObjId="g_14be650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="154,-367 142,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c0180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="245,-131 245,-144 203,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_14406e0@0" ObjectIDZND0="24514@x" ObjectIDZND1="34290@x" Pin0InfoVect0LinkObjId="SW-135412_0" Pin0InfoVect1LinkObjId="EC-MD_HP.494Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14406e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="245,-131 245,-144 203,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c03e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="203,-153 203,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24514@0" ObjectIDZND0="g_14406e0@0" ObjectIDZND1="34290@x" Pin0InfoVect0LinkObjId="g_14406e0_0" Pin0InfoVect1LinkObjId="EC-MD_HP.494Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135412_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="203,-153 203,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14c0640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="203,-144 203,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24514@x" ObjectIDND1="g_14406e0@0" ObjectIDZND0="34290@0" Pin0InfoVect0LinkObjId="EC-MD_HP.494Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135412_0" Pin1InfoVect1LinkObjId="g_14406e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="203,-144 203,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148aec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="877,-417 877,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24516@1" ObjectIDZND0="24475@0" Pin0InfoVect0LinkObjId="g_1501fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135426_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="877,-417 877,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148b120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="877,-320 877,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24515@0" ObjectIDZND0="24517@1" Pin0InfoVect0LinkObjId="SW-135427_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135424_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="877,-320 877,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148d450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="877,-263 877,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24517@0" ObjectIDZND0="g_144f6d0@0" Pin0InfoVect0LinkObjId="g_144f6d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="877,-263 877,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148d6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="877,-200 877,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_144f6d0@1" ObjectIDZND0="24518@1" Pin0InfoVect0LinkObjId="SW-135428_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_144f6d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="877,-200 877,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148e3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="861,-366 877,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="24515@x" ObjectIDZND1="24516@x" Pin0InfoVect0LinkObjId="SW-135424_0" Pin0InfoVect1LinkObjId="SW-135426_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="861,-366 877,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148e600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="877,-347 877,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24515@1" ObjectIDZND0="0@x" ObjectIDZND1="24516@x" Pin0InfoVect0LinkObjId="g_13be5b0_0" Pin0InfoVect1LinkObjId="SW-135426_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135424_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="877,-347 877,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148e860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="877,-366 877,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24515@x" ObjectIDND1="0@x" ObjectIDZND0="24516@0" Pin0InfoVect0LinkObjId="SW-135426_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135424_0" Pin1InfoVect1LinkObjId="g_13be5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="877,-366 877,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_148eac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="828,-366 815,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_148d910@0" Pin0InfoVect0LinkObjId="g_148d910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="828,-366 815,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148f440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="919,-130 919,-143 877,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_14b29b0@0" ObjectIDZND0="24518@x" ObjectIDZND1="34291@x" Pin0InfoVect0LinkObjId="SW-135428_0" Pin0InfoVect1LinkObjId="EC-MD_HP.495Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14b29b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="919,-130 919,-143 877,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_144f210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="877,-152 877,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24518@0" ObjectIDZND0="g_14b29b0@0" ObjectIDZND1="34291@x" Pin0InfoVect0LinkObjId="g_14b29b0_0" Pin0InfoVect1LinkObjId="EC-MD_HP.495Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="877,-152 877,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_144f470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="877,-143 877,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24518@x" ObjectIDND1="g_14b29b0@0" ObjectIDZND0="34291@0" Pin0InfoVect0LinkObjId="EC-MD_HP.495Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135428_0" Pin1InfoVect1LinkObjId="g_14b29b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="877,-143 877,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13e1150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-418 1050,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24520@1" ObjectIDZND0="24475@0" Pin0InfoVect0LinkObjId="g_1501fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135442_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-418 1050,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13e13b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-321 1050,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24519@0" ObjectIDZND0="24521@1" Pin0InfoVect0LinkObjId="SW-135443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-321 1050,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13e36e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-264 1050,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24521@0" ObjectIDZND0="g_14a1a70@0" Pin0InfoVect0LinkObjId="g_14a1a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-264 1050,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13e3940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-201 1050,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_14a1a70@1" ObjectIDZND0="24522@1" Pin0InfoVect0LinkObjId="SW-135444_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a1a70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-201 1050,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13e4630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1034,-367 1050,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="24519@x" ObjectIDZND1="24520@x" Pin0InfoVect0LinkObjId="SW-135440_0" Pin0InfoVect1LinkObjId="SW-135442_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1034,-367 1050,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13e4890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-348 1050,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24519@1" ObjectIDZND0="0@x" ObjectIDZND1="24520@x" Pin0InfoVect0LinkObjId="g_13be5b0_0" Pin0InfoVect1LinkObjId="SW-135442_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-348 1050,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13e4af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-367 1050,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24519@x" ObjectIDND1="0@x" ObjectIDZND0="24520@0" Pin0InfoVect0LinkObjId="SW-135442_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135440_0" Pin1InfoVect1LinkObjId="g_13be5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-367 1050,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13e4d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1001,-367 988,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_13e3ba0@0" Pin0InfoVect0LinkObjId="g_13e3ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1001,-367 988,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14a1360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-131 1092,-144 1050,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_14517a0@0" ObjectIDZND0="24522@x" ObjectIDZND1="34292@x" Pin0InfoVect0LinkObjId="SW-135444_0" Pin0InfoVect1LinkObjId="EC-MD_HP.496Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14517a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-131 1092,-144 1050,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14a15e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-153 1050,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24522@0" ObjectIDZND0="g_14517a0@0" ObjectIDZND1="34292@x" Pin0InfoVect0LinkObjId="g_14517a0_0" Pin0InfoVect1LinkObjId="EC-MD_HP.496Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135444_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-153 1050,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14a1810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-144 1050,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24522@x" ObjectIDND1="g_14517a0@0" ObjectIDZND0="34292@0" Pin0InfoVect0LinkObjId="EC-MD_HP.496Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135444_0" Pin1InfoVect1LinkObjId="g_14517a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-144 1050,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1405850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-411 1224,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24524@1" ObjectIDZND0="24475@0" Pin0InfoVect0LinkObjId="g_1501fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135458_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-411 1224,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1405ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-314 1224,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24523@0" ObjectIDZND0="24525@1" Pin0InfoVect0LinkObjId="SW-135459_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135456_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-314 1224,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1407de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-257 1224,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24525@0" ObjectIDZND0="g_140a4f0@0" Pin0InfoVect0LinkObjId="g_140a4f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135459_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-257 1224,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1408040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-194 1224,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_140a4f0@1" ObjectIDZND0="24526@1" Pin0InfoVect0LinkObjId="SW-135460_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_140a4f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-194 1224,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1408d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1208,-360 1224,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="24523@x" ObjectIDZND1="24524@x" Pin0InfoVect0LinkObjId="SW-135456_0" Pin0InfoVect1LinkObjId="SW-135458_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1208,-360 1224,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1408f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-341 1224,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24523@1" ObjectIDZND0="0@x" ObjectIDZND1="24524@x" Pin0InfoVect0LinkObjId="g_13be5b0_0" Pin0InfoVect1LinkObjId="SW-135458_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135456_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-341 1224,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14091f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-360 1224,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24523@x" ObjectIDND1="0@x" ObjectIDZND0="24524@0" Pin0InfoVect0LinkObjId="SW-135458_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135456_0" Pin1InfoVect1LinkObjId="g_13be5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-360 1224,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1409450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1175,-360 1162,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_14082a0@0" Pin0InfoVect0LinkObjId="g_14082a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1175,-360 1162,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1409dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1266,-124 1266,-137 1224,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_14a3be0@0" ObjectIDZND0="24526@x" ObjectIDZND1="34293@x" Pin0InfoVect0LinkObjId="SW-135460_0" Pin0InfoVect1LinkObjId="EC-MD_HP.497Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a3be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1266,-124 1266,-137 1224,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_140a030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-146 1224,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24526@0" ObjectIDZND0="g_14a3be0@0" ObjectIDZND1="34293@x" Pin0InfoVect0LinkObjId="g_14a3be0_0" Pin0InfoVect1LinkObjId="EC-MD_HP.497Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-146 1224,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_140a290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-137 1224,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24526@x" ObjectIDND1="g_14a3be0@0" ObjectIDZND0="34293@0" Pin0InfoVect0LinkObjId="EC-MD_HP.497Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135460_0" Pin1InfoVect1LinkObjId="g_14a3be0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-137 1224,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13ef000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1397,-416 1397,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24528@1" ObjectIDZND0="24475@0" Pin0InfoVect0LinkObjId="g_1501fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135474_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1397,-416 1397,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13ef260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1397,-319 1397,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24527@0" ObjectIDZND0="24529@1" Pin0InfoVect0LinkObjId="SW-135475_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135472_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1397,-319 1397,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13f1590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1397,-262 1397,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24529@0" ObjectIDZND0="g_13cfa50@0" Pin0InfoVect0LinkObjId="g_13cfa50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135475_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1397,-262 1397,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13f17f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1397,-199 1397,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_13cfa50@1" ObjectIDZND0="24530@1" Pin0InfoVect0LinkObjId="SW-135476_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13cfa50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1397,-199 1397,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13ce290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1381,-365 1397,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="24527@x" ObjectIDZND1="24528@x" Pin0InfoVect0LinkObjId="SW-135472_0" Pin0InfoVect1LinkObjId="SW-135474_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1381,-365 1397,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13ce4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1397,-346 1397,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24527@1" ObjectIDZND0="0@x" ObjectIDZND1="24528@x" Pin0InfoVect0LinkObjId="g_13be5b0_0" Pin0InfoVect1LinkObjId="SW-135474_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135472_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1397,-346 1397,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13ce750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1397,-365 1397,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24527@x" ObjectIDND1="0@x" ObjectIDZND0="24528@0" Pin0InfoVect0LinkObjId="SW-135474_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135472_0" Pin1InfoVect1LinkObjId="g_13be5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1397,-365 1397,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13ce9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1348,-365 1335,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_13f1a50@0" Pin0InfoVect0LinkObjId="g_13f1a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1348,-365 1335,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cf330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1439,-129 1439,-142 1397,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_140c660@0" ObjectIDZND0="24530@x" ObjectIDZND1="34294@x" Pin0InfoVect0LinkObjId="SW-135476_0" Pin0InfoVect1LinkObjId="EC-MD_HP.498Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_140c660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1439,-129 1439,-142 1397,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cf590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1397,-151 1397,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24530@0" ObjectIDZND0="g_140c660@0" ObjectIDZND1="34294@x" Pin0InfoVect0LinkObjId="g_140c660_0" Pin0InfoVect1LinkObjId="EC-MD_HP.498Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135476_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1397,-151 1397,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cf7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1397,-142 1397,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24530@x" ObjectIDND1="g_140c660@0" ObjectIDZND0="34294@0" Pin0InfoVect0LinkObjId="EC-MD_HP.498Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135476_0" Pin1InfoVect1LinkObjId="g_140c660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1397,-142 1397,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13d51f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-1030 329,-1016 378,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1411660@0" ObjectIDZND0="24480@x" ObjectIDZND1="24479@x" ObjectIDZND2="g_14f8e50@0" Pin0InfoVect0LinkObjId="SW-135210_0" Pin0InfoVect1LinkObjId="SW-135209_0" Pin0InfoVect2LinkObjId="g_14f8e50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1411660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="329,-1030 329,-1016 378,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13d5ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-1016 378,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1411660@0" ObjectIDND1="24480@x" ObjectIDND2="24479@x" ObjectIDZND0="37794@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1411660_0" Pin1InfoVect1LinkObjId="SW-135210_0" Pin1InfoVect2LinkObjId="SW-135209_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,-1016 378,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13d6140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="360,-978 378,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24480@0" ObjectIDZND0="24479@x" ObjectIDZND1="g_1411660@0" ObjectIDZND2="g_14f8e50@0" Pin0InfoVect0LinkObjId="SW-135209_0" Pin0InfoVect1LinkObjId="g_1411660_0" Pin0InfoVect2LinkObjId="g_14f8e50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="360,-978 378,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13d6c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-953 378,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24479@1" ObjectIDZND0="24480@x" ObjectIDZND1="g_1411660@0" ObjectIDZND2="g_14f8e50@0" Pin0InfoVect0LinkObjId="SW-135210_0" Pin0InfoVect1LinkObjId="g_1411660_0" Pin0InfoVect2LinkObjId="g_14f8e50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135209_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="378,-953 378,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13d6e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-978 378,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24480@x" ObjectIDND1="24479@x" ObjectIDZND0="g_1411660@0" ObjectIDZND1="g_14f8e50@0" ObjectIDZND2="37794@1" Pin0InfoVect0LinkObjId="g_1411660_0" Pin0InfoVect1LinkObjId="g_14f8e50_0" Pin0InfoVect2LinkObjId="g_13d5ee0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135210_0" Pin1InfoVect1LinkObjId="SW-135209_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="378,-978 378,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146dfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="549,-351 569,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="24497@x" ObjectIDZND1="g_1512ef0@0" ObjectIDZND2="g_13be5b0@0" Pin0InfoVect0LinkObjId="SW-135356_0" Pin0InfoVect1LinkObjId="g_1512ef0_0" Pin0InfoVect2LinkObjId="g_13be5b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="549,-351 569,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146e1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="569,-366 569,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="24497@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1512ef0@0" ObjectIDZND2="g_13be5b0@0" Pin0InfoVect0LinkObjId="g_13be5b0_0" Pin0InfoVect1LinkObjId="g_1512ef0_0" Pin0InfoVect2LinkObjId="g_13be5b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135356_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="569,-366 569,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146e3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="569,-351 569,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="24497@x" ObjectIDND2="g_1512ef0@0" ObjectIDZND0="g_13be5b0@0" Pin0InfoVect0LinkObjId="g_13be5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="SW-135356_0" Pin1InfoVect2LinkObjId="g_1512ef0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="569,-351 569,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146e600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="615,-347 569,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1512ef0@0" ObjectIDZND0="0@x" ObjectIDZND1="24497@x" ObjectIDZND2="g_13be5b0@0" Pin0InfoVect0LinkObjId="g_13be5b0_0" Pin0InfoVect1LinkObjId="SW-135356_0" Pin0InfoVect2LinkObjId="g_13be5b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1512ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="615,-347 569,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_146e830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-802 378,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24478@0" ObjectIDZND0="24473@0" Pin0InfoVect0LinkObjId="g_1518850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135208_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,-802 378,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14fb0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-1016 423,-1016 423,-1029 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1411660@0" ObjectIDND1="24480@x" ObjectIDND2="24479@x" ObjectIDZND0="g_14f8e50@0" Pin0InfoVect0LinkObjId="g_14f8e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1411660_0" Pin1InfoVect1LinkObjId="SW-135210_0" Pin1InfoVect2LinkObjId="SW-135209_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,-1016 423,-1016 423,-1029 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14fb310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="423,-1074 423,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_14f8e50@1" ObjectIDZND0="g_14fb570@0" Pin0InfoVect0LinkObjId="g_14fb570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14f8e50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="423,-1074 423,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13480b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="828,-1014 873,-1014 873,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_144c920@0" ObjectIDND1="24484@x" ObjectIDND2="24483@x" ObjectIDZND0="g_1349290@0" Pin0InfoVect0LinkObjId="g_1349290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_144c920_0" Pin1InfoVect1LinkObjId="SW-135226_0" Pin1InfoVect2LinkObjId="SW-135225_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="828,-1014 873,-1014 873,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1348310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="873,-1072 873,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1349290@1" ObjectIDZND0="g_1348570@0" Pin0InfoVect0LinkObjId="g_1348570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1349290_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,-1072 873,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cb970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-187,-418 -187,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24500@1" ObjectIDZND0="24474@0" Pin0InfoVect0LinkObjId="g_14dae70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135362_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-187,-418 -187,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cc1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-57,-418 -57,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24504@1" ObjectIDZND0="24474@0" Pin0InfoVect0LinkObjId="g_14dae70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135378_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-57,-418 -57,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cc9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="76,-418 76,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24508@1" ObjectIDZND0="24474@0" Pin0InfoVect0LinkObjId="g_14dae70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135394_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="76,-418 76,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cd200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="203,-418 203,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24512@1" ObjectIDZND0="24474@0" Pin0InfoVect0LinkObjId="g_14dae70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="203,-418 203,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a14b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="294,-360 282,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46597@0" ObjectIDZND0="g_15a0a20@0" Pin0InfoVect0LinkObjId="g_15a0a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-301790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="294,-360 282,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a49a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="291,-231 279,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46598@0" ObjectIDZND0="g_15a3f10@0" Pin0InfoVect0LinkObjId="g_15a3f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-301791_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="291,-231 279,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a4c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="342,-293 342,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="46596@1" ObjectIDZND0="46594@0" Pin0InfoVect0LinkObjId="SW-301787_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-301789_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="342,-293 342,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a4e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="327,-360 342,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46597@1" ObjectIDZND0="46595@x" ObjectIDZND1="46594@x" Pin0InfoVect0LinkObjId="SW-301788_0" Pin0InfoVect1LinkObjId="SW-301787_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-301790_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="327,-360 342,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a5950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="342,-381 342,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46595@0" ObjectIDZND0="46597@x" ObjectIDZND1="46594@x" Pin0InfoVect0LinkObjId="SW-301790_0" Pin0InfoVect1LinkObjId="SW-301787_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-301788_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="342,-381 342,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a5bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="342,-360 342,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46597@x" ObjectIDND1="46595@x" ObjectIDZND0="46594@1" Pin0InfoVect0LinkObjId="SW-301787_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-301790_0" Pin1InfoVect1LinkObjId="SW-301788_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="342,-360 342,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a8610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-231 342,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="46598@1" ObjectIDZND0="46596@x" ObjectIDZND1="g_15a9360@0" Pin0InfoVect0LinkObjId="SW-301789_0" Pin0InfoVect1LinkObjId="g_15a9360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-301791_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="324,-231 342,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a9100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="342,-261 342,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="46596@0" ObjectIDZND0="46598@x" ObjectIDZND1="g_15a9360@0" Pin0InfoVect0LinkObjId="SW-301791_0" Pin0InfoVect1LinkObjId="g_15a9360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-301789_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="342,-261 342,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1377560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="342,-231 342,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="46598@x" ObjectIDND1="46596@x" ObjectIDZND0="g_15a9360@0" Pin0InfoVect0LinkObjId="g_15a9360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-301791_0" Pin1InfoVect1LinkObjId="SW-301789_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="342,-231 342,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1378050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="424,-131 424,-145 375,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_11de200@0" ObjectIDZND0="g_15a9360@0" ObjectIDZND1="46599@x" Pin0InfoVect0LinkObjId="g_15a9360_0" Pin0InfoVect1LinkObjId="SW-301816_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11de200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="424,-131 424,-145 375,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13782b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,-145 342,-145 342,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_11de200@0" ObjectIDND1="46599@x" ObjectIDZND0="g_15a9360@1" Pin0InfoVect0LinkObjId="g_15a9360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11de200_0" Pin1InfoVect1LinkObjId="SW-301816_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,-145 342,-145 342,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1378510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,-145 375,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_11de200@0" ObjectIDND1="g_15a9360@0" ObjectIDZND0="46599@1" Pin0InfoVect0LinkObjId="SW-301816_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11de200_0" Pin1InfoVect1LinkObjId="g_15a9360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,-145 375,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1378770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,-90 375,-3 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="46599@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-301816_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="375,-90 375,-3 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_137ac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,32 375,77 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1379d70@0" Pin0InfoVect0LinkObjId="g_1379d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,32 375,77 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_137b050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="418,30 418,52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_13789d0@0" Pin0InfoVect0LinkObjId="g_13789d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="418,30 418,52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_137b450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="419,-15 419,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="419,-15 419,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1383810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="419,-37 419,-65 333,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_137de50@0" Pin0InfoVect0LinkObjId="g_137de50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13be5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="419,-37 419,-65 333,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1384010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="342,-413 342,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46595@1" ObjectIDZND0="24474@0" Pin0InfoVect0LinkObjId="g_14dae70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-301788_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="342,-413 342,-437 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="438,-293 413,-293 413,-262 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="724,-273 699,-273 699,-242 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="562,-275 537,-275 537,-244 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1143,-624 1118,-624 1118,-593 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-135056" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -282.000000 -1017.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24444" ObjectName="DYN-MD_HP"/>
     <cge:Meas_Ref ObjectId="135056"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-MD_HP.491Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -196.000000 -97.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34287" ObjectName="EC-MD_HP.491Ld"/>
    <cge:TPSR_Ref TObjectID="34287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_HP.492Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -65.666667 -97.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34288" ObjectName="EC-MD_HP.492Ld"/>
    <cge:TPSR_Ref TObjectID="34288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_HP.493Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 66.666667 -97.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34289" ObjectName="EC-MD_HP.493Ld"/>
    <cge:TPSR_Ref TObjectID="34289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_HP.494Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 194.000000 -97.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34290" ObjectName="EC-MD_HP.494Ld"/>
    <cge:TPSR_Ref TObjectID="34290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_HP.495Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 868.000000 -96.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34291" ObjectName="EC-MD_HP.495Ld"/>
    <cge:TPSR_Ref TObjectID="34291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_HP.496Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1041.333333 -97.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34292" ObjectName="EC-MD_HP.496Ld"/>
    <cge:TPSR_Ref TObjectID="34292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_HP.497Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.666667 -90.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34293" ObjectName="EC-MD_HP.497Ld"/>
    <cge:TPSR_Ref TObjectID="34293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_HP.498Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1388.000000 -95.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34294" ObjectName="EC-MD_HP.498Ld"/>
    <cge:TPSR_Ref TObjectID="34294"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="MD_HP"/>
</svg>