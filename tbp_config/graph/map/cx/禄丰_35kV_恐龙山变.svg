<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-92" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3095 -1275 1979 1360">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape25">
    <polyline arcFlag="1" points="26,105 24,105 22,104 21,104 19,103 18,102 16,101 15,99 14,97 14,96 13,94 13,92 13,90 14,88 14,87 15,85 16,84 18,82 19,81 21,80 22,80 24,79 26,79 28,79 30,80 31,80 33,81 34,82 36,84 37,85 38,87 38,88 39,90 39,92 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="39" x2="26" y1="92" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="60" y2="52"/>
    <polyline arcFlag="1" points="43,19 44,19 45,19 45,19 46,19 46,20 47,20 47,21 48,21 48,22 48,22 48,23 49,24 49,24 49,25 48,26 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,29 45,30 44,30 43,30 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,41 44,41 45,41 45,42 46,42 46,42 47,43 47,43 48,44 48,44 48,45 48,45 49,46 49,47 49,47 48,48 48,49 48,49 48,50 47,50 47,51 46,51 46,52 45,52 45,52 44,52 43,52 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="26" x2="26" y1="92" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="26" x2="43" y1="13" y2="13"/>
    <rect height="23" stroke-width="0.369608" width="12" x="20" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="7" x2="43" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="9" x2="9" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="42" x2="42" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="19" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="17" x2="33" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="18" x2="33" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="60" y2="35"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="29"/>
    <polyline arcFlag="1" points="43,30 44,30 45,30 45,30 46,31 46,31 47,31 47,32 48,32 48,33 48,34 48,34 49,35 49,36 49,36 48,37 48,38 48,38 48,39 47,39 47,40 46,40 46,40 45,41 45,41 44,41 43,41 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape110">
    <ellipse cx="13" cy="51" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <circle cx="13" cy="68" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="45" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="49" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="53" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="67" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="37" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="49" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="42" x2="30" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="35" x2="37" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="33" x2="39" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="39" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="36" y1="23" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="32" y2="32"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape204">
    <rect height="31" stroke-width="0.5" width="16" x="12" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="30" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="2" y1="30" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape137">
    <ellipse cx="18" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="25" x2="28" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="28" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="28" x2="28" y1="17" y2="19"/>
    <ellipse cx="28" cy="16" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="14" x2="20" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="14" y1="19" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="17" x2="20" y1="19" y2="22"/>
    <ellipse cx="17" cy="21" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="13" y2="16"/>
    <ellipse cx="8" cy="14" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="15" x2="18" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="18" x2="18" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="18" y1="6" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape189">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="13" y1="21" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="13,64 38,64 38,35 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="13" y1="58" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="32" x2="44" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="41" x2="35" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="39" x2="37" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="13" y1="21" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="53" y2="0"/>
    <circle cx="13" cy="66" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="80" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="84" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="88" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="60" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="64" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="68" y2="64"/>
    <ellipse cx="13" cy="82" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b373f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b37c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b38630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b391a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b3a440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b3af60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b3bb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b3c5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b3cff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b3d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b3d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b3f4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b3f4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2b403e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b42010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b42c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b434e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b43e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b455e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b45e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b463a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b46da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b47f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b48900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b493f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b4e8a0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b4f580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b4b180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b4c660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b4d440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2b5be70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2b51570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1370" width="1989" x="3090" y="-1280"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.33333" x1="4555" x2="4565" y1="-959" y2="-959"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-54104">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3985.877221 -862.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9564" ObjectName="SW-LF_KLS.LF_KLS_351BK"/>
     <cge:Meas_Ref ObjectId="54104"/>
    <cge:TPSR_Ref TObjectID="9564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54159">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.573670 -479.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9582" ObjectName="SW-LF_KLS.LF_KLS_001BK"/>
     <cge:Meas_Ref ObjectId="54159"/>
    <cge:TPSR_Ref TObjectID="9582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54208">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.596732 -297.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9598" ObjectName="SW-LF_KLS.LF_KLS_082BK"/>
     <cge:Meas_Ref ObjectId="54208"/>
    <cge:TPSR_Ref TObjectID="9598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54124">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.185567 -297.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9572" ObjectName="SW-LF_KLS.LF_KLS_072BK"/>
     <cge:Meas_Ref ObjectId="54124"/>
    <cge:TPSR_Ref TObjectID="9572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54173">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -840.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9588" ObjectName="SW-LF_KLS.LF_KLS_352BK"/>
     <cge:Meas_Ref ObjectId="54173"/>
    <cge:TPSR_Ref TObjectID="9588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54133">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.185567 -295.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9575" ObjectName="SW-LF_KLS.LF_KLS_073BK"/>
     <cge:Meas_Ref ObjectId="54133"/>
    <cge:TPSR_Ref TObjectID="9575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54115">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.185567 -293.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9569" ObjectName="SW-LF_KLS.LF_KLS_071BK"/>
     <cge:Meas_Ref ObjectId="54115"/>
    <cge:TPSR_Ref TObjectID="9569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.185567 -295.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9578" ObjectName="SW-LF_KLS.LF_KLS_074BK"/>
     <cge:Meas_Ref ObjectId="54142"/>
    <cge:TPSR_Ref TObjectID="9578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54186">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4234.000000 -650.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9592" ObjectName="SW-LF_KLS.LF_KLS_312BK"/>
     <cge:Meas_Ref ObjectId="54186"/>
    <cge:TPSR_Ref TObjectID="9592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54151">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.185567 -294.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9581" ObjectName="SW-LF_KLS.LF_KLS_075BK"/>
     <cge:Meas_Ref ObjectId="54151"/>
    <cge:TPSR_Ref TObjectID="9581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54214">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4404.000000 -479.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9599" ObjectName="SW-LF_KLS.LF_KLS_012BK"/>
     <cge:Meas_Ref ObjectId="54214"/>
    <cge:TPSR_Ref TObjectID="9599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54196">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4545.856727 -299.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9595" ObjectName="SW-LF_KLS.LF_KLS_081BK"/>
     <cge:Meas_Ref ObjectId="54196"/>
    <cge:TPSR_Ref TObjectID="9595"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_KLS.LF_KLS_10_IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4460,-412 5072,-412 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17339" ObjectName="BS-LF_KLS.LF_KLS_10_IIM"/>
    <cge:TPSR_Ref TObjectID="17339"/></metadata>
   <polyline fill="none" opacity="0" points="4460,-412 5072,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_KLS.LF_KLS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3908,-760 4190,-760 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9546" ObjectName="BS-LF_KLS.LF_KLS_3IM"/>
    <cge:TPSR_Ref TObjectID="9546"/></metadata>
   <polyline fill="none" opacity="0" points="3908,-760 4190,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_KLS.LF_KLS_10_IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-412 4419,-412 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17338" ObjectName="BS-LF_KLS.LF_KLS_10_IM"/>
    <cge:TPSR_Ref TObjectID="17338"/></metadata>
   <polyline fill="none" opacity="0" points="3617,-412 4419,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_KLS.LF_KLS_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4343,-760 4625,-760 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9547" ObjectName="BS-LF_KLS.LF_KLS_3IIM"/>
    <cge:TPSR_Ref TObjectID="9547"/></metadata>
   <polyline fill="none" opacity="0" points="4343,-760 4625,-760 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-LF_KLS.LF_KLS_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3668.000000 -104.000000)" xlink:href="#capacitor:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41618" ObjectName="CB-LF_KLS.LF_KLS_Cb1"/>
    <cge:TPSR_Ref TObjectID="41618"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-LF_KLS.LF_KLS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="13647"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.573670 -563.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.573670 -563.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="9602" ObjectName="TF-LF_KLS.LF_KLS_1T"/>
    <cge:TPSR_Ref TObjectID="9602"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_258ce40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 -1022.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_258d5e0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4644.000000 -1151.000000)" xlink:href="#lightningRod:shape110"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b0630">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4793.000000 -494.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24d5630">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4414.000000 -1041.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24d5dd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4085.000000 -1103.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_251bc00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -1008.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_251c8d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.000000 -88.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_251d580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 -86.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_251e230">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -86.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_251eee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4763.000000 -88.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24847e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 -1022.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2485490">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 -1103.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2486140">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 -1041.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2486df0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4829.000000 -495.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_252c730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3697.000000 -486.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2419ac0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.000000 -85.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2438850">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4570.000000 -90.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26e9fd0">
    <use class="BV-0KV" transform="matrix(0.473684 -0.000000 0.000000 -0.512195 4111.000000 -1174.000000)" xlink:href="#lightningRod:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26ef470">
    <use class="BV-0KV" transform="matrix(0.473684 -0.000000 0.000000 -0.512195 4440.000000 -1125.000000)" xlink:href="#lightningRod:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26f0280">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.214286 -0.000000 3687.607143 -601.500000)" xlink:href="#lightningRod:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26f2670">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 -196.000000)" xlink:href="#lightningRod:shape189"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26f4150">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.214286 -0.000000 4820.607143 -608.500000)" xlink:href="#lightningRod:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26f6e30">
    <use class="BV-0KV" transform="matrix(0.473684 -0.000000 0.000000 -0.512195 4824.000000 -644.000000)" xlink:href="#lightningRod:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3208.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-82462" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -947.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82462" ObjectName="LF_KLS:LF_KLS_3011P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-82463" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -903.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="82463" ObjectName="LF_KLS:LF_KLS_3011Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-185400" ratioFlag="0">
    <text fill="rgb(118,238,0)" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -569.000000) translate(0,20)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185400" ObjectName="LF_KLS:LF_KLS_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-53956" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 -533.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53956" ObjectName="LF_KLS:LF_KLS_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-53959" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.322581 -0.000000 -0.000000 1.400000 3795.000000 -488.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53959" ObjectName="LF_KLS:LF_KLS_001BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-53960" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.322581 -0.000000 -0.000000 1.400000 3795.000000 -471.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53960" ObjectName="LF_KLS:LF_KLS_001BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-53958" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.322581 -0.000000 -0.000000 1.400000 3795.000000 -505.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53958" ObjectName="LF_KLS:LF_KLS_001BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-53961" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.322581 -0.000000 -0.000000 1.400000 3795.000000 -454.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53961" ObjectName="LF_KLS:LF_KLS_001BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-54017" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.290323 -0.000000 -0.000000 1.333333 4933.500000 -486.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54017" ObjectName="LF_KLS:LF_KLS_012BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-54018" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.290323 -0.000000 -0.000000 1.333333 4933.500000 -471.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54018" ObjectName="LF_KLS:LF_KLS_012BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-54016" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.290323 -0.000000 -0.000000 1.333333 4933.500000 -503.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54016" ObjectName="LF_KLS:LF_KLS_012BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-54019" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.290323 -0.000000 -0.000000 1.333333 4933.500000 -454.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54019" ObjectName="LF_KLS:LF_KLS_012BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-53978" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3235.000000 -1027.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53978" ObjectName="LF_KLS:LF_KLS_352BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-53978" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3236.000000 -987.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53978" ObjectName="LF_KLS:LF_KLS_352BK_P"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-53887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3918.000000 -1246.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9564"/>
     <cge:Term_Ref ObjectID="13569"/>
    <cge:TPSR_Ref TObjectID="9564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-53888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3918.000000 -1246.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9564"/>
     <cge:Term_Ref ObjectID="13569"/>
    <cge:TPSR_Ref TObjectID="9564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-53885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3918.000000 -1246.000000) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9564"/>
     <cge:Term_Ref ObjectID="13569"/>
    <cge:TPSR_Ref TObjectID="9564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-53978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -1275.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9588"/>
     <cge:Term_Ref ObjectID="13617"/>
    <cge:TPSR_Ref TObjectID="9588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-53979" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -1275.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9588"/>
     <cge:Term_Ref ObjectID="13617"/>
    <cge:TPSR_Ref TObjectID="9588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-53975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -1275.000000) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9588"/>
     <cge:Term_Ref ObjectID="13617"/>
    <cge:TPSR_Ref TObjectID="9588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-53990" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4254.000000 -641.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53990" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9592"/>
     <cge:Term_Ref ObjectID="13625"/>
    <cge:TPSR_Ref TObjectID="9592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-53991" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4254.000000 -641.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9592"/>
     <cge:Term_Ref ObjectID="13625"/>
    <cge:TPSR_Ref TObjectID="9592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-53987" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4254.000000 -641.000000) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53987" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9592"/>
     <cge:Term_Ref ObjectID="13625"/>
    <cge:TPSR_Ref TObjectID="9592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-54012" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 7.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54012" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9598"/>
     <cge:Term_Ref ObjectID="13637"/>
    <cge:TPSR_Ref TObjectID="9598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-54013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 7.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9598"/>
     <cge:Term_Ref ObjectID="13637"/>
    <cge:TPSR_Ref TObjectID="9598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-54010" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 7.000000) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54010" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9598"/>
     <cge:Term_Ref ObjectID="13637"/>
    <cge:TPSR_Ref TObjectID="9598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-54001" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4526.000000 7.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54001" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9595"/>
     <cge:Term_Ref ObjectID="13631"/>
    <cge:TPSR_Ref TObjectID="9595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-54002" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4526.000000 7.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54002" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9595"/>
     <cge:Term_Ref ObjectID="13631"/>
    <cge:TPSR_Ref TObjectID="9595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-53999" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4526.000000 7.000000) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53999" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9595"/>
     <cge:Term_Ref ObjectID="13631"/>
    <cge:TPSR_Ref TObjectID="9595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-53942" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 7.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53942" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9581"/>
     <cge:Term_Ref ObjectID="13603"/>
    <cge:TPSR_Ref TObjectID="9581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-53943" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 7.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9581"/>
     <cge:Term_Ref ObjectID="13603"/>
    <cge:TPSR_Ref TObjectID="9581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-53940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 7.000000) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9581"/>
     <cge:Term_Ref ObjectID="13603"/>
    <cge:TPSR_Ref TObjectID="9581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-53931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 7.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9578"/>
     <cge:Term_Ref ObjectID="13597"/>
    <cge:TPSR_Ref TObjectID="9578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-53932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 7.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9578"/>
     <cge:Term_Ref ObjectID="13597"/>
    <cge:TPSR_Ref TObjectID="9578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-53929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 7.000000) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9578"/>
     <cge:Term_Ref ObjectID="13597"/>
    <cge:TPSR_Ref TObjectID="9578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-53920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3989.000000 7.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9575"/>
     <cge:Term_Ref ObjectID="13591"/>
    <cge:TPSR_Ref TObjectID="9575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-53921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3989.000000 7.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9575"/>
     <cge:Term_Ref ObjectID="13591"/>
    <cge:TPSR_Ref TObjectID="9575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-53918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3989.000000 7.000000) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9575"/>
     <cge:Term_Ref ObjectID="13591"/>
    <cge:TPSR_Ref TObjectID="9575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-53909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 7.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9572"/>
     <cge:Term_Ref ObjectID="13585"/>
    <cge:TPSR_Ref TObjectID="9572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-53910" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 7.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9572"/>
     <cge:Term_Ref ObjectID="13585"/>
    <cge:TPSR_Ref TObjectID="9572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-53907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 7.000000) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9572"/>
     <cge:Term_Ref ObjectID="13585"/>
    <cge:TPSR_Ref TObjectID="9572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-53899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -9.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9569"/>
     <cge:Term_Ref ObjectID="13579"/>
    <cge:TPSR_Ref TObjectID="9569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-53896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -9.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9569"/>
     <cge:Term_Ref ObjectID="13579"/>
    <cge:TPSR_Ref TObjectID="9569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-53833" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -823.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53833" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9546"/>
     <cge:Term_Ref ObjectID="13535"/>
    <cge:TPSR_Ref TObjectID="9546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-53834" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -823.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53834" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9546"/>
     <cge:Term_Ref ObjectID="13535"/>
    <cge:TPSR_Ref TObjectID="9546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-53835" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -823.000000) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53835" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9546"/>
     <cge:Term_Ref ObjectID="13535"/>
    <cge:TPSR_Ref TObjectID="9546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-53843" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -823.000000) translate(0,99)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9546"/>
     <cge:Term_Ref ObjectID="13535"/>
    <cge:TPSR_Ref TObjectID="9546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-53966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4127.000000 -528.000000) translate(0,18)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9582"/>
     <cge:Term_Ref ObjectID="13605"/>
    <cge:TPSR_Ref TObjectID="9582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-53967" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4127.000000 -528.000000) translate(0,40)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9582"/>
     <cge:Term_Ref ObjectID="13605"/>
    <cge:TPSR_Ref TObjectID="9582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-53963" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4127.000000 -528.000000) translate(0,62)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53963" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9582"/>
     <cge:Term_Ref ObjectID="13605"/>
    <cge:TPSR_Ref TObjectID="9582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-53844" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.670103 -0.000000 -0.000000 1.733333 4723.500000 -813.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53844" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9547"/>
     <cge:Term_Ref ObjectID="13536"/>
    <cge:TPSR_Ref TObjectID="9547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-53845" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.670103 -0.000000 -0.000000 1.733333 4723.500000 -813.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53845" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9547"/>
     <cge:Term_Ref ObjectID="13536"/>
    <cge:TPSR_Ref TObjectID="9547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-53846" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.670103 -0.000000 -0.000000 1.733333 4723.500000 -813.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9547"/>
     <cge:Term_Ref ObjectID="13536"/>
    <cge:TPSR_Ref TObjectID="9547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-53854" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.670103 -0.000000 -0.000000 1.733333 4723.500000 -813.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53854" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9547"/>
     <cge:Term_Ref ObjectID="13536"/>
    <cge:TPSR_Ref TObjectID="9547"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3220" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3220" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3171" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3171" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="70" x="3853" y="-631"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="70" x="3853" y="-631"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4757" y="-326"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4757" y="-326"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4564" y="-328"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4564" y="-328"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4313" y="-323"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4313" y="-323"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4161" y="-324"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4161" y="-324"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4001" y="-324"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4001" y="-324"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3838" y="-325"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3838" y="-325"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3654" y="-323"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3654" y="-323"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4414" y="-513"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4414" y="-513"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4244" y="-684"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4244" y="-684"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3381" y="-1156"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3381" y="-1156"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3381" y="-1191"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3381" y="-1191"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4009" y="-889"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4009" y="-889"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4576" y="-867"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4576" y="-867"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="3123" y="-798"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="3123" y="-798"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3399" y="-1048"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3399" y="-1048"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3220" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3171" y="-1194"/></g>
   <g href="35kV恐龙山变35kV1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="70" x="3853" y="-631"/></g>
   <g href="35kV恐龙山变10kV恐龙谷II回线082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4757" y="-326"/></g>
   <g href="35kV恐龙山变10kV备用二线081间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4564" y="-328"/></g>
   <g href="35kV恐龙山变10kV阿纳线075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4313" y="-323"/></g>
   <g href="35kV恐龙山变10kV备用一线074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4161" y="-324"/></g>
   <g href="35kV恐龙山变10kV恐龙谷I回线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4001" y="-324"/></g>
   <g href="35kV恐龙山变10kV城区线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3838" y="-325"/></g>
   <g href="35kV恐龙山变10kV1号电容器071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3654" y="-323"/></g>
   <g href="35kV恐龙山变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4414" y="-513"/></g>
   <g href="35kV恐龙山变35kV内桥312间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4244" y="-684"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3381" y="-1156"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3381" y="-1191"/></g>
   <g href="35kV恐龙山变35kV备用线351间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4009" y="-889"/></g>
   <g href="35kV恐龙山变35kV恐龙山支线352间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4576" y="-867"/></g>
   <g href="35kV恐龙山变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="3123" y="-798"/></g>
   <g href="AVC恐龙山站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3399" y="-1048"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3400" y="-1047"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="LF_KLS" endPointId="0" endStationName="PAS_T3" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_JinYangTkls" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4565,-1125 4565,-1168 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18143" ObjectName="AC-35kV.LN_JinYangTkls"/>
    <cge:TPSR_Ref TObjectID="18143_SS-92"/></metadata>
   <polyline fill="none" opacity="0" points="4565,-1125 4565,-1168 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_252e9c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 -782.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2512870" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3734.000000 -309.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24dd870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.000000 -627.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24c0ab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.000000 -601.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2475af0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.000000 -929.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2476540" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4059.000000 -1015.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2476fd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 -967.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2477a60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4519.000000 -869.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24784f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4607.000000 -804.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2478f80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4517.000000 -764.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2479a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4433.000000 -601.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247a4a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.000000 -624.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247b1a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.000000 -856.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26ec0c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -1198.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26ee780" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.000000 -1149.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26f6140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4827.000000 -668.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="9546" cx="4123" cy="-760" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9546" cx="3995" cy="-760" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17339" cx="4802" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17339" cx="4748" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9546" cx="3994" cy="-760" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17339" cx="4494" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17339" cx="4555" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17339" cx="4959" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17338" cx="3827" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17338" cx="3992" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17338" cx="4152" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17338" cx="3670" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17338" cx="3993" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17338" cx="4304" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17338" cx="4364" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9547" cx="4395" cy="-760" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9547" cx="4565" cy="-760" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9547" cx="4579" cy="-760" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17338" cx="3694" cy="-412" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2226640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -1025.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2226640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2226640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -1025.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2226640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2226640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -1025.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2226640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2226640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -1025.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2226640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -1025.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2226640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -1025.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2259560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_221f8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3255.000000 -1166.500000) translate(0,16)">恐龙山变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24c4bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3636.000000 -100.000000) translate(0,16)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2534e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3790.000000 -25.000000) translate(0,16)">城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_256a5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -25.000000) translate(0,16)">恐龙谷I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2547aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -25.000000) translate(0,16)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2547cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4525.000000 -19.000000) translate(0,16)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24d2420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4279.000000 -21.000000) translate(0,16)">阿纳线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24d2840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4896.000000 -191.000000) translate(0,16)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24d2a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4994.000000 -442.000000) translate(0,16)">10kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24d4ec0" transform="matrix(1.087019 -0.000000 -0.000000 1.000000 3549.910128 -440.000000) translate(0,16)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244c730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4009.000000 -889.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244cca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -816.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244cee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3902.000000 -835.000000) translate(0,12)">35117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244d120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4050.000000 -913.000000) translate(0,12)">35160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244d360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -976.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.000000 -982.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244d7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -1058.000000) translate(0,12)">3519</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244da20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 -1067.000000) translate(0,12)">35197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244de20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -867.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244e060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -811.000000) translate(0,12)">3522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_240fc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4469.000000 -815.000000) translate(0,12)">35227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_240fea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -919.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24100e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4470.000000 -925.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2410320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4380.000000 -1014.000000) translate(0,12)">3529</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2410560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4451.000000 -1021.000000) translate(0,12)">35297</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24107a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -1165.000000) translate(0,16)">35kV #1站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2411ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.000000 -1153.000000) translate(0,16)">35kV线路PT</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250f290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.500000 -785.000000) translate(0,12)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2576ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4326.000000 -784.000000) translate(0,12)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f5210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4244.000000 -684.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f5490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4130.000000 -739.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f56d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4385.000000 -653.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f5910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -734.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f5b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4087.000000 -654.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2569190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -739.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25693d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -680.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2569610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4586.000000 -735.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2569850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4483.000000 -682.000000) translate(0,12)">30227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2569a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -508.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252bdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4000.000000 -458.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252c2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4809.000000 -457.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3677.000000 -451.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252d230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3654.000000 -323.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252d720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3648.000000 -269.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252d960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3648.000000 -375.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -270.000000) translate(0,12)">07167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252dde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3838.000000 -325.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252e020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3839.000000 -272.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252e260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 -378.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252e4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4001.000000 -324.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252e6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -271.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2524b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -376.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2524d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.000000 -324.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25250b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4159.000000 -271.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25254e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4159.000000 -376.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a7210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4414.000000 -513.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a7700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4371.000000 -457.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a7940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 -455.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2439e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4313.000000 -323.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243a380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -270.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243a5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -375.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243a800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -328.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243ad20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4562.000000 -275.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243afa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4562.000000 -380.000000) translate(0,12)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243b1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4757.000000 -326.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243b420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4755.000000 -273.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_241afc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4755.000000 -378.000000) translate(0,12)">0822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_241b200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3853.000000 -631.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2421100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -1194.000000) translate(0,16)">35kV恐龙山支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2421fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3937.000000 -1159.000000) translate(0,16)">35kV备用线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2473290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -22.000000) translate(0,16)">恐龙谷II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_242ddc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4622.000000 -858.000000) translate(0,12)">35260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2466780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3247.000000 -232.000000) translate(0,17)">4724096</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2466780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3247.000000 -232.000000) translate(0,38)">15758580332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2401eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3833.000000 -596.000000) translate(0,16)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24033f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 -733.000000) translate(0,16)">10kV II段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24033f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 -733.000000) translate(0,36)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(118,238,0)" font-family="SimSun" font-size="24" graphid="g_24d39d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3853.000000 -571.000000) translate(0,20)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2409710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3392.000000 -1148.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_240a7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3392.000000 -1183.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_240ada0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3123.000000 -798.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_240cb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -532.000000) translate(0,12)">温度1℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_26f2010" transform="matrix(1.087019 -0.000000 -0.000000 1.000000 3608.910128 -682.000000) translate(0,16)">10kV I段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_26f2010" transform="matrix(1.087019 -0.000000 -0.000000 1.000000 3608.910128 -682.000000) translate(0,36)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2700510" transform="matrix(1.327246 -0.000000 -0.000000 1.621283 3544.000000 -11.638498) translate(0,12)">Q(MVar):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2700750" transform="matrix(1.327246 -0.000000 -0.000000 1.621283 3577.181158 12.680751) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2703110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3251.000000 -180.500000) translate(0,17)">13508785653</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2704970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3095.000000 -190.000000) translate(0,17)">禄丰巡维中心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2704970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3095.000000 -190.000000) translate(0,38)">腰站变值班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_24f2640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3416.500000 -1036.000000) translate(0,16)">AVC</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_241f510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3662.000000 75.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2420bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 60.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_240f9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4878.000000 469.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26e95b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.500000 484.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26e9b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4878.000000 499.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26e9d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4870.000000 454.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4086" x2="4082" y1="1167" y2="1173"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4089" x2="4082" y1="1173" y2="1173"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4094" x2="4098" y1="1156" y2="1159"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4098" x2="4094" y1="1152" y2="1156"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4091" x2="4094" y1="1156" y2="1156"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4102" x2="4105" y1="1171" y2="1174"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4105" x2="4102" y1="1167" y2="1171"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4099" x2="4102" y1="1171" y2="1171"/>
    <circle DF8003:Layer="PUBLIC" cx="4088" cy="1170" fill="none" fillStyle="0" r="10" stroke="rgb(60,120,255)" stroke-width="0.205743"/>
    <ellipse DF8003:Layer="PUBLIC" cx="4094" cy="1158" fill="none" fillStyle="0" rx="10" ry="10.5" stroke="rgb(60,120,255)" stroke-width="0.205743"/>
    <ellipse DF8003:Layer="PUBLIC" cx="4100" cy="1170" fill="none" fillStyle="0" rx="9.5" ry="10.5" stroke="rgb(60,120,255)" stroke-width="0.205743"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4086" x2="4089" y1="1167" y2="1173"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="4086" x2="4082" y1="1167" y2="1173"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4415" x2="4411" y1="1118" y2="1124"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4418" x2="4411" y1="1124" y2="1124"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4423" x2="4427" y1="1107" y2="1110"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4427" x2="4423" y1="1103" y2="1107"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4420" x2="4423" y1="1107" y2="1107"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4431" x2="4434" y1="1122" y2="1125"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4434" x2="4431" y1="1118" y2="1122"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4428" x2="4431" y1="1122" y2="1122"/>
    <circle DF8003:Layer="PUBLIC" cx="4417" cy="1121" fill="none" fillStyle="0" r="10" stroke="rgb(60,120,255)" stroke-width="0.205743"/>
    <ellipse DF8003:Layer="PUBLIC" cx="4423" cy="1109" fill="none" fillStyle="0" rx="10" ry="10.5" stroke="rgb(60,120,255)" stroke-width="0.205743"/>
    <ellipse DF8003:Layer="PUBLIC" cx="4429" cy="1121" fill="none" fillStyle="0" rx="9.5" ry="10.5" stroke="rgb(60,120,255)" stroke-width="0.205743"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.205743" x1="4415" x2="4418" y1="1118" y2="1124"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="4415" x2="4411" y1="1118" y2="1124"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.327246 -0.000000 0.000000 -1.621283 -1128.610500 333.793427)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 975.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fbeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 960.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fc130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 945.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.327246 -0.000000 0.000000 -1.621283 -477.610500 307.793427)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fc550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 975.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fc850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 960.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fca90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 945.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.327246 -0.000000 0.000000 -1.621283 -1217.610500 1587.793427)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fceb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 975.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fd1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 960.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fd3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 945.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.327246 -0.000000 0.000000 -1.621283 -1054.610500 1587.793427)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fd810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 975.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fdb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 960.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fdd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 945.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.327246 -0.000000 0.000000 -1.621283 -888.610500 1587.793427)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fe170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 975.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fe470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 960.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fe6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 945.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.327246 -0.000000 0.000000 -1.621283 -706.610500 1587.793427)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fead0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 975.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26fedd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 960.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ff010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 945.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.327246 -0.000000 0.000000 -1.621283 -520.610500 1587.793427)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ff430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 975.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ff730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 960.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ff970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 945.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.327246 -0.000000 0.000000 -1.621283 -329.610500 1587.793427)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ffd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 975.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2700090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 960.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27002d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 945.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.329435 -0.000000 0.000000 -1.650000 -1246.557505 -0.300000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2700a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3741.000000 468.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2700d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.500000 483.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2700f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3741.000000 498.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27011b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 453.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.329435 -0.000000 0.000000 -1.650000 -325.557505 6.700000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27014e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3741.000000 468.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2701790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.500000 483.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27019d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3741.000000 498.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2701c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 453.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.556647 -0.000000 0.000000 -1.821167 -1657.962932 1133.685768)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2702030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 975.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2702330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 960.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2702570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 945.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.147003 -0.000000 0.000000 -1.243724 -232.762161 688.663451)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2702990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 975.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2702c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 960.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2702ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 945.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f3280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.500000 483.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f37c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3741.000000 498.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f3a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 453.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f3c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3741.000000 468.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_KLS.LD_KLS_072">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.000000 -39.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18147" ObjectName="EC-LF_KLS.LD_KLS_072"/>
    <cge:TPSR_Ref TObjectID="18147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_KLS.LD_KLS_073">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -37.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18151" ObjectName="EC-LF_KLS.LD_KLS_073"/>
    <cge:TPSR_Ref TObjectID="18151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_KLS.LD_KLS_082">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.000000 -39.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18155" ObjectName="EC-LF_KLS.LD_KLS_082"/>
    <cge:TPSR_Ref TObjectID="18155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_KLS.LD_KLS_074">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -37.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18152" ObjectName="EC-LF_KLS.LD_KLS_074"/>
    <cge:TPSR_Ref TObjectID="18152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3986.000000 -1131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_KLS.LD_KLS_075">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 -52.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18153" ObjectName="EC-LF_KLS.LD_KLS_075"/>
    <cge:TPSR_Ref TObjectID="18153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_KLS.LD_KLS_081">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -41.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18154" ObjectName="EC-LF_KLS.LD_KLS_081"/>
    <cge:TPSR_Ref TObjectID="18154"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-52541" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3425.000000 -1072.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9251" ObjectName="DYN-LF_KLS"/>
     <cge:Meas_Ref ObjectId="52541"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-54097">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -786.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9562" ObjectName="SW-LF_KLS.LF_KLS_3511SW"/>
     <cge:Meas_Ref ObjectId="54097"/>
    <cge:TPSR_Ref TObjectID="9562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54171">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -781.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9587" ObjectName="SW-LF_KLS.LF_KLS_3522SW"/>
     <cge:Meas_Ref ObjectId="54171"/>
    <cge:TPSR_Ref TObjectID="9587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54169">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -889.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9585" ObjectName="SW-LF_KLS.LF_KLS_3526SW"/>
     <cge:Meas_Ref ObjectId="54169"/>
    <cge:TPSR_Ref TObjectID="9585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54058">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4414.000000 -984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9550" ObjectName="SW-LF_KLS.LF_KLS_3529SW"/>
     <cge:Meas_Ref ObjectId="54058"/>
    <cge:TPSR_Ref TObjectID="9550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54184">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -709.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9591" ObjectName="SW-LF_KLS.LF_KLS_3121SW"/>
     <cge:Meas_Ref ObjectId="54184"/>
    <cge:TPSR_Ref TObjectID="9591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54086">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4570.000000 -705.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9555" ObjectName="SW-LF_KLS.LF_KLS_3022SW"/>
     <cge:Meas_Ref ObjectId="54086"/>
    <cge:TPSR_Ref TObjectID="9555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3661.000000 -421.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9553" ObjectName="SW-LF_KLS.LF_KLS_0901SW"/>
     <cge:Meas_Ref ObjectId="54080"/>
    <cge:TPSR_Ref TObjectID="9553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54054">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4085.000000 -1028.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9548" ObjectName="SW-LF_KLS.LF_KLS_3519SW"/>
     <cge:Meas_Ref ObjectId="54054"/>
    <cge:TPSR_Ref TObjectID="9548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54096">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.000000 -805.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9561" ObjectName="SW-LF_KLS.LF_KLS_35117SW"/>
     <cge:Meas_Ref ObjectId="54096"/>
    <cge:TPSR_Ref TObjectID="9561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54059">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 -991.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9551" ObjectName="SW-LF_KLS.LF_KLS_35297SW"/>
     <cge:Meas_Ref ObjectId="54059"/>
    <cge:TPSR_Ref TObjectID="9551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54182">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 -704.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9589" ObjectName="SW-LF_KLS.LF_KLS_3122SW"/>
     <cge:Meas_Ref ObjectId="54182"/>
    <cge:TPSR_Ref TObjectID="9589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54083">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4793.000000 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9554" ObjectName="SW-LF_KLS.LF_KLS_0902SW"/>
     <cge:Meas_Ref ObjectId="54083"/>
    <cge:TPSR_Ref TObjectID="9554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54119">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.000000 -348.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9571" ObjectName="SW-LF_KLS.LF_KLS_0721SW"/>
     <cge:Meas_Ref ObjectId="54119"/>
    <cge:TPSR_Ref TObjectID="9571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54128">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9574" ObjectName="SW-LF_KLS.LF_KLS_0731SW"/>
     <cge:Meas_Ref ObjectId="54128"/>
    <cge:TPSR_Ref TObjectID="9574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54137">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9577" ObjectName="SW-LF_KLS.LF_KLS_0741SW"/>
     <cge:Meas_Ref ObjectId="54137"/>
    <cge:TPSR_Ref TObjectID="9577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54202">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.000000 -243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9596" ObjectName="SW-LF_KLS.LF_KLS_0826SW"/>
     <cge:Meas_Ref ObjectId="54202"/>
    <cge:TPSR_Ref TObjectID="9596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54203">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.000000 -348.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9597" ObjectName="SW-LF_KLS.LF_KLS_0822SW"/>
     <cge:Meas_Ref ObjectId="54203"/>
    <cge:TPSR_Ref TObjectID="9597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54118">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.000000 -243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9570" ObjectName="SW-LF_KLS.LF_KLS_0726SW"/>
     <cge:Meas_Ref ObjectId="54118"/>
    <cge:TPSR_Ref TObjectID="9570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54109">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 -239.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9567" ObjectName="SW-LF_KLS.LF_KLS_0716SW"/>
     <cge:Meas_Ref ObjectId="54109"/>
    <cge:TPSR_Ref TObjectID="9567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54127">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -241.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9573" ObjectName="SW-LF_KLS.LF_KLS_0736SW"/>
     <cge:Meas_Ref ObjectId="54127"/>
    <cge:TPSR_Ref TObjectID="9573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54136">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -241.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9576" ObjectName="SW-LF_KLS.LF_KLS_0746SW"/>
     <cge:Meas_Ref ObjectId="54136"/>
    <cge:TPSR_Ref TObjectID="9576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54110">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 -344.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9568" ObjectName="SW-LF_KLS.LF_KLS_0711SW"/>
     <cge:Meas_Ref ObjectId="54110"/>
    <cge:TPSR_Ref TObjectID="9568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54055">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 -1037.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9549" ObjectName="SW-LF_KLS.LF_KLS_35197SW"/>
     <cge:Meas_Ref ObjectId="54055"/>
    <cge:TPSR_Ref TObjectID="9549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54108">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3719.000000 -240.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9566" ObjectName="SW-LF_KLS.LF_KLS_07167SW"/>
     <cge:Meas_Ref ObjectId="54108"/>
    <cge:TPSR_Ref TObjectID="9566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54095">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.000000 -946.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9560" ObjectName="SW-LF_KLS.LF_KLS_3516SW"/>
     <cge:Meas_Ref ObjectId="54095"/>
    <cge:TPSR_Ref TObjectID="9560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54101">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4034.000000 -883.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9563" ObjectName="SW-LF_KLS.LF_KLS_35160SW"/>
     <cge:Meas_Ref ObjectId="54101"/>
    <cge:TPSR_Ref TObjectID="9563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54094">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -953.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9559" ObjectName="SW-LF_KLS.LF_KLS_35167SW"/>
     <cge:Meas_Ref ObjectId="54094"/>
    <cge:TPSR_Ref TObjectID="9559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54170">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4514.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9586" ObjectName="SW-LF_KLS.LF_KLS_35227SW"/>
     <cge:Meas_Ref ObjectId="54170"/>
    <cge:TPSR_Ref TObjectID="9586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54168">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4516.000000 -893.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9584" ObjectName="SW-LF_KLS.LF_KLS_35267SW"/>
     <cge:Meas_Ref ObjectId="54168"/>
    <cge:TPSR_Ref TObjectID="9584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54175">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4604.000000 -831.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17670" ObjectName="SW-LF_KLS.LF_KLS_35260SW"/>
     <cge:Meas_Ref ObjectId="54175"/>
    <cge:TPSR_Ref TObjectID="17670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54091">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -650.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9558" ObjectName="SW-LF_KLS.LF_KLS_30117SW"/>
     <cge:Meas_Ref ObjectId="54091"/>
    <cge:TPSR_Ref TObjectID="9558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54090">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3985.000000 -709.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9557" ObjectName="SW-LF_KLS.LF_KLS_3011SW"/>
     <cge:Meas_Ref ObjectId="54090"/>
    <cge:TPSR_Ref TObjectID="9557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54087">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4527.000000 -648.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9556" ObjectName="SW-LF_KLS.LF_KLS_30227SW"/>
     <cge:Meas_Ref ObjectId="54087"/>
    <cge:TPSR_Ref TObjectID="9556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54183">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -624.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9590" ObjectName="SW-LF_KLS.LF_KLS_35217SW"/>
     <cge:Meas_Ref ObjectId="54183"/>
    <cge:TPSR_Ref TObjectID="9590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54187">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 -625.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17669" ObjectName="SW-LF_KLS.LF_KLS_31227SW"/>
     <cge:Meas_Ref ObjectId="54187"/>
    <cge:TPSR_Ref TObjectID="17669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54161">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3984.000000 -428.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9583" ObjectName="SW-LF_KLS.LF_KLS_0011SW"/>
     <cge:Meas_Ref ObjectId="54161"/>
    <cge:TPSR_Ref TObjectID="9583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54146">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 -345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9580" ObjectName="SW-LF_KLS.LF_KLS_0751SW"/>
     <cge:Meas_Ref ObjectId="54146"/>
    <cge:TPSR_Ref TObjectID="9580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54145">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 -240.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9579" ObjectName="SW-LF_KLS.LF_KLS_0756SW"/>
     <cge:Meas_Ref ObjectId="54145"/>
    <cge:TPSR_Ref TObjectID="9579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54228">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4485.000000 -425.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9601" ObjectName="SW-LF_KLS.LF_KLS_0122SW"/>
     <cge:Meas_Ref ObjectId="54228"/>
    <cge:TPSR_Ref TObjectID="9601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54227">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9600" ObjectName="SW-LF_KLS.LF_KLS_0121SW"/>
     <cge:Meas_Ref ObjectId="54227"/>
    <cge:TPSR_Ref TObjectID="9600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54193">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -245.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9593" ObjectName="SW-LF_KLS.LF_KLS_0816SW"/>
     <cge:Meas_Ref ObjectId="54193"/>
    <cge:TPSR_Ref TObjectID="9593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54194">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9594" ObjectName="SW-LF_KLS.LF_KLS_0811SW"/>
     <cge:Meas_Ref ObjectId="54194"/>
    <cge:TPSR_Ref TObjectID="9594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4626.000000 -966.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3665.000000 -535.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4954.000000 -339.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 -539.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_256aa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3993,-568 3993,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="9602@0" ObjectIDZND0="9582@1" Pin0InfoVect0LinkObjId="SW-54159_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24de500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3993,-568 3993,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_256a020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-305 4748,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9598@0" ObjectIDZND0="9596@1" Pin0InfoVect0LinkObjId="SW-54202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54208_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-305 4748,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_256a210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-412 4748,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17339@0" ObjectIDZND0="9597@1" Pin0InfoVect0LinkObjId="SW-54203_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e5660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-412 4748,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257dfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-353 4748,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9597@0" ObjectIDZND0="9598@1" Pin0InfoVect0LinkObjId="SW-54208_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-353 4748,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257e1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-158 4770,-158 4770,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18155@x" ObjectIDND1="9596@x" ObjectIDZND0="g_251eee0@0" Pin0InfoVect0LinkObjId="g_251eee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_082_0" Pin1InfoVect1LinkObjId="SW-54202_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-158 4770,-158 4770,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25480b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-158 4748,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_251eee0@0" ObjectIDND1="9596@x" ObjectIDZND0="18155@0" Pin0InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_082_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_251eee0_0" Pin1InfoVect1LinkObjId="SW-54202_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-158 4748,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2549260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-305 3827,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9572@0" ObjectIDZND0="9570@1" Pin0InfoVect0LinkObjId="SW-54118_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54124_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-305 3827,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2549450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-412 3827,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9571@1" Pin0InfoVect0LinkObjId="SW-54119_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2553d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-412 3827,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2501b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-353 3827,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9571@0" ObjectIDZND0="9572@1" Pin0InfoVect0LinkObjId="SW-54124_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54119_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-353 3827,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2501d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-158 3849,-158 3849,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18147@x" ObjectIDND1="9570@x" ObjectIDZND0="g_251c8d0@0" Pin0InfoVect0LinkObjId="g_251c8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_072_0" Pin1InfoVect1LinkObjId="SW-54118_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-158 3849,-158 3849,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2501f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-158 3827,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_251c8d0@0" ObjectIDND1="9570@x" ObjectIDZND0="18147@0" Pin0InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_072_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_251c8d0_0" Pin1InfoVect1LinkObjId="SW-54118_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-158 3827,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24e0b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3670,-479 3704,-479 3704,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9553@x" ObjectIDND1="0@x" ObjectIDZND0="g_252c730@0" Pin0InfoVect0LinkObjId="g_252c730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54080_0" Pin1InfoVect1LinkObjId="g_258ce40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3670,-479 3704,-479 3704,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24e0d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-248 3827,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="9570@0" ObjectIDZND0="18147@x" ObjectIDZND1="g_251c8d0@0" Pin0InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_072_0" Pin0InfoVect1LinkObjId="g_251c8d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54118_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-248 3827,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250b1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-303 3992,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9575@0" ObjectIDZND0="9573@1" Pin0InfoVect0LinkObjId="SW-54127_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54133_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-303 3992,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250b3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-412 3992,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9574@1" Pin0InfoVect0LinkObjId="SW-54128_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2553d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-412 3992,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250b5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-351 3992,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9574@0" ObjectIDZND0="9575@1" Pin0InfoVect0LinkObjId="SW-54133_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54128_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-351 3992,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250b7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-156 4014,-156 4014,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18151@x" ObjectIDND1="9573@x" ObjectIDZND0="g_251d580@0" Pin0InfoVect0LinkObjId="g_251d580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_073_0" Pin1InfoVect1LinkObjId="SW-54127_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-156 4014,-156 4014,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250b9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-156 3992,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_251d580@0" ObjectIDND1="9573@x" ObjectIDZND0="18151@0" Pin0InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_073_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_251d580_0" Pin1InfoVect1LinkObjId="SW-54127_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-156 3992,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250bba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-246 3992,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="9573@0" ObjectIDZND0="18151@x" ObjectIDZND1="g_251d580@0" Pin0InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_073_0" Pin0InfoVect1LinkObjId="g_251d580_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-246 3992,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250bd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4748,-248 4748,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="9596@0" ObjectIDZND0="18155@x" ObjectIDZND1="g_251eee0@0" Pin0InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_082_0" Pin0InfoVect1LinkObjId="g_251eee0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4748,-248 4748,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2517ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-349 3694,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9568@0" ObjectIDZND0="9569@1" Pin0InfoVect0LinkObjId="SW-54115_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-349 3694,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25500f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-303 4152,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9578@0" ObjectIDZND0="9576@1" Pin0InfoVect0LinkObjId="SW-54136_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-303 4152,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25502e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-412 4152,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9577@1" Pin0InfoVect0LinkObjId="SW-54137_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2553d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-412 4152,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25504d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-351 4152,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9577@0" ObjectIDZND0="9578@1" Pin0InfoVect0LinkObjId="SW-54142_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54137_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-351 4152,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25506c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-156 4174,-156 4174,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18152@x" ObjectIDND1="9576@x" ObjectIDZND0="g_251e230@0" Pin0InfoVect0LinkObjId="g_251e230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_074_0" Pin1InfoVect1LinkObjId="SW-54136_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-156 4174,-156 4174,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2562870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-156 4152,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_251e230@0" ObjectIDND1="9576@x" ObjectIDZND0="18152@0" Pin0InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_074_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_251e230_0" Pin1InfoVect1LinkObjId="SW-54136_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-156 4152,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2562a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-246 4152,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="9576@0" ObjectIDZND0="18152@x" ObjectIDZND1="g_251e230@0" Pin0InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_074_0" Pin0InfoVect1LinkObjId="g_251e230_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54136_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-246 4152,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_258cc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-985 4444,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2476fd0@0" ObjectIDZND0="9551@0" Pin0InfoVect0LinkObjId="SW-54059_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2476fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-985 4444,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2559950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4631,-1070 4631,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_258d5e0@1" ObjectIDZND0="g_258ce40@1" Pin0InfoVect0LinkObjId="g_258ce40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_258d5e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4631,-1070 4631,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b0410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4802,-488 4836,-488 4836,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_24b0630@0" ObjectIDND1="9554@x" ObjectIDZND0="g_2486df0@0" Pin0InfoVect0LinkObjId="g_2486df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24b0630_0" Pin1InfoVect1LinkObjId="SW-54083_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4802,-488 4836,-488 4836,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24b0dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4631,-1016 4631,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_258ce40@0" Pin0InfoVect0LinkObjId="g_258ce40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_258ce40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4631,-1016 4631,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24cf6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-760 3995,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9546@0" ObjectIDZND0="9562@0" Pin0InfoVect0LinkObjId="SW-54097_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-760 3995,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24c8c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-301 3694,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9569@0" ObjectIDZND0="9567@1" Pin0InfoVect0LinkObjId="SW-54109_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54115_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-301 3694,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24c8eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-1104 3995,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="9560@x" ObjectIDZND1="9559@x" ObjectIDZND2="9548@x" Pin0InfoVect0LinkObjId="SW-54095_0" Pin0InfoVect1LinkObjId="SW-54094_0" Pin0InfoVect2LinkObjId="SW-54054_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_258ce40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-1104 3995,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25e5400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-1042 4065,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9549@0" ObjectIDZND0="g_2476540@0" Pin0InfoVect0LinkObjId="g_2476540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54055_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-1042 4065,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e5660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-389 4959,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="17339@0" Pin0InfoVect0LinkObjId="g_25e5b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_258ce40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-389 4959,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e58c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4802,-488 4802,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2486df0@0" ObjectIDND1="g_24b0630@0" ObjectIDZND0="9554@1" Pin0InfoVect0LinkObjId="SW-54083_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2486df0_0" Pin1InfoVect1LinkObjId="g_24b0630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4802,-488 4802,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e5b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4802,-432 4802,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9554@0" ObjectIDZND0="17339@0" Pin0InfoVect0LinkObjId="g_25e5660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54083_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4802,-432 4802,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25e5d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4802,-609 4802,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_258ce40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4802,-609 4802,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e5fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4802,-544 4802,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_24b0630@1" Pin0InfoVect0LinkObjId="g_24b0630_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_258ce40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4802,-544 4802,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e6240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4802,-499 4802,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_24b0630@0" ObjectIDZND0="g_2486df0@0" ObjectIDZND1="9554@x" Pin0InfoVect0LinkObjId="g_2486df0_0" Pin0InfoVect1LinkObjId="SW-54083_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24b0630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4802,-499 4802,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2553870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3728,-281 3728,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9566@1" ObjectIDZND0="g_2512870@0" Pin0InfoVect0LinkObjId="g_2512870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54108_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3728,-281 3728,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2553ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3670,-479 3670,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_252c730@0" ObjectIDND1="0@x" ObjectIDZND0="9553@1" Pin0InfoVect0LinkObjId="SW-54080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_252c730_0" Pin1InfoVect1LinkObjId="g_258ce40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3670,-479 3670,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2553d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3670,-426 3670,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9553@0" ObjectIDZND0="17338@0" Pin0InfoVect0LinkObjId="g_2412620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3670,-426 3670,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2553f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3670,-602 3670,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_258ce40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3670,-602 3670,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25541f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-799 3954,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_252e9c0@0" ObjectIDZND0="9561@0" Pin0InfoVect0LinkObjId="SW-54096_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_252e9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-799 3954,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2554450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3954,-846 3954,-853 3995,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9561@1" ObjectIDZND0="9562@x" ObjectIDZND1="9564@x" Pin0InfoVect0LinkObjId="SW-54097_0" Pin0InfoVect1LinkObjId="SW-54104_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54096_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3954,-846 3954,-853 3995,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24a2ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-760 4123,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9546@0" ObjectIDZND0="9591@1" Pin0InfoVect0LinkObjId="SW-54184_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-760 4123,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24a3150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-760 4395,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9547@0" ObjectIDZND0="9589@1" Pin0InfoVect0LinkObjId="SW-54182_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26f8630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-760 4395,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24a33b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-1069 4094,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9548@1" ObjectIDZND0="g_2485490@0" ObjectIDZND1="g_24d5dd0@0" ObjectIDZND2="9549@x" Pin0InfoVect0LinkObjId="g_2485490_0" Pin0InfoVect1LinkObjId="g_24d5dd0_0" Pin0InfoVect2LinkObjId="SW-54055_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54054_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-1069 4094,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24a3610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-1089 4065,-1089 4065,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2485490@0" ObjectIDND1="g_24d5dd0@0" ObjectIDND2="9548@x" ObjectIDZND0="9549@1" Pin0InfoVect0LinkObjId="SW-54055_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2485490_0" Pin1InfoVect1LinkObjId="g_24d5dd0_0" Pin1InfoVect2LinkObjId="SW-54054_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-1089 4065,-1089 4065,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2504000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-1095 3995,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="g_24847e0@0" ObjectIDZND0="9560@x" ObjectIDZND1="9559@x" ObjectIDZND2="9548@x" Pin0InfoVect0LinkObjId="SW-54095_0" Pin0InfoVect1LinkObjId="SW-54094_0" Pin0InfoVect2LinkObjId="SW-54054_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_258ce40_0" Pin1InfoVect1LinkObjId="g_24847e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-1095 3995,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2504260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-1033 4094,-1014 3995,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9548@0" ObjectIDZND0="0@x" ObjectIDZND1="g_24847e0@0" ObjectIDZND2="9560@x" Pin0InfoVect0LinkObjId="g_258ce40_0" Pin0InfoVect1LinkObjId="g_24847e0_0" Pin0InfoVect2LinkObjId="SW-54095_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54054_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-1033 4094,-1014 3995,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25044c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-951 3995,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9560@0" ObjectIDZND0="9563@x" ObjectIDZND1="9564@x" Pin0InfoVect0LinkObjId="SW-54101_0" Pin0InfoVect1LinkObjId="SW-54104_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54095_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-951 3995,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_256adb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-936 4043,-936 4043,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="9560@x" ObjectIDND1="9564@x" ObjectIDZND0="9563@1" Pin0InfoVect0LinkObjId="SW-54101_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54095_0" Pin1InfoVect1LinkObjId="SW-54104_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-936 4043,-936 4043,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_256b010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,-888 4043,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9563@0" ObjectIDZND0="g_247b1a0@0" Pin0InfoVect0LinkObjId="g_247b1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54101_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4043,-888 4043,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_256b270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-897 3995,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="9564@1" ObjectIDZND0="9560@x" ObjectIDZND1="9563@x" Pin0InfoVect0LinkObjId="SW-54095_0" Pin0InfoVect1LinkObjId="SW-54101_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54104_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-897 3995,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_256b4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-827 3995,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9562@1" ObjectIDZND0="9561@x" ObjectIDZND1="9564@x" Pin0InfoVect0LinkObjId="SW-54096_0" Pin0InfoVect1LinkObjId="SW-54104_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54097_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-827 3995,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_256b730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-853 3995,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9562@x" ObjectIDND1="9561@x" ObjectIDZND0="9564@0" Pin0InfoVect0LinkObjId="SW-54104_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54097_0" Pin1InfoVect1LinkObjId="SW-54096_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-853 3995,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2493800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-947 3955,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2475af0@0" ObjectIDZND0="9559@0" Pin0InfoVect0LinkObjId="SW-54094_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2475af0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-947 3955,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2493a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-994 3955,-1001 3995,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9559@1" ObjectIDZND0="0@x" ObjectIDZND1="g_24847e0@0" ObjectIDZND2="9548@x" Pin0InfoVect0LinkObjId="g_258ce40_0" Pin0InfoVect1LinkObjId="g_24847e0_0" Pin0InfoVect2LinkObjId="SW-54054_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54094_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-994 3955,-1001 3995,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2493cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-987 3995,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9560@1" ObjectIDZND0="0@x" ObjectIDZND1="g_24847e0@0" ObjectIDZND2="9548@x" Pin0InfoVect0LinkObjId="g_258ce40_0" Pin0InfoVect1LinkObjId="g_24847e0_0" Pin0InfoVect2LinkObjId="SW-54054_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54095_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-987 3995,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2493f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-1001 3995,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9560@x" ObjectIDND1="9559@x" ObjectIDZND0="0@x" ObjectIDZND1="g_24847e0@0" ObjectIDZND2="9548@x" Pin0InfoVect0LinkObjId="g_258ce40_0" Pin0InfoVect1LinkObjId="g_24847e0_0" Pin0InfoVect2LinkObjId="SW-54054_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54095_0" Pin1InfoVect1LinkObjId="SW-54094_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-1001 3995,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ac030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4523,-782 4523,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2478f80@0" ObjectIDZND0="9586@0" Pin0InfoVect0LinkObjId="SW-54170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2478f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4523,-782 4523,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ac290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-887 4525,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2477a60@0" ObjectIDZND0="9584@0" Pin0InfoVect0LinkObjId="SW-54168_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2477a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-887 4525,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ac4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-934 4525,-941 4565,-941 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="9584@1" ObjectIDZND0="9585@x" ObjectIDZND1="0@x" ObjectIDZND2="g_251bc00@0" Pin0InfoVect0LinkObjId="SW-54169_0" Pin0InfoVect1LinkObjId="g_258ce40_0" Pin0InfoVect2LinkObjId="g_251bc00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54168_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-934 4525,-941 4565,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25aece0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-941 4565,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="9584@x" ObjectIDND1="0@x" ObjectIDND2="g_251bc00@0" ObjectIDZND0="9585@1" Pin0InfoVect0LinkObjId="SW-54169_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-54168_0" Pin1InfoVect1LinkObjId="g_258ce40_0" Pin1InfoVect2LinkObjId="g_251bc00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-941 4565,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25aef40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-822 4565,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9587@1" ObjectIDZND0="9588@x" ObjectIDZND1="9586@x" Pin0InfoVect0LinkObjId="SW-54173_0" Pin0InfoVect1LinkObjId="SW-54170_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54171_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-822 4565,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b19a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-836 4523,-836 4523,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9588@x" ObjectIDND1="9587@x" ObjectIDZND0="9586@1" Pin0InfoVect0LinkObjId="SW-54170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54173_0" Pin1InfoVect1LinkObjId="SW-54171_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-836 4523,-836 4523,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b1c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-848 4565,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="9588@0" ObjectIDZND0="9587@x" ObjectIDZND1="9586@x" Pin0InfoVect0LinkObjId="SW-54171_0" Pin0InfoVect1LinkObjId="SW-54170_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54173_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-848 4565,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b4240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-884 4613,-884 4613,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9588@x" ObjectIDND1="9585@x" ObjectIDZND0="17670@1" Pin0InfoVect0LinkObjId="SW-54175_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54173_0" Pin1InfoVect1LinkObjId="SW-54169_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-884 4613,-884 4613,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b44a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-836 4613,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17670@0" ObjectIDZND0="g_24784f0@0" Pin0InfoVect0LinkObjId="g_24784f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54175_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-836 4613,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b4700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-894 4565,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9585@0" ObjectIDZND0="9588@x" ObjectIDZND1="17670@x" Pin0InfoVect0LinkObjId="SW-54173_0" Pin0InfoVect1LinkObjId="SW-54175_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54169_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-894 4565,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b4960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-884 4565,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9585@x" ObjectIDND1="17670@x" ObjectIDZND0="9588@1" Pin0InfoVect0LinkObjId="SW-54173_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54169_0" Pin1InfoVect1LinkObjId="SW-54175_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-884 4565,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24de2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-644 3951,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_24dd870@0" ObjectIDZND0="9558@0" Pin0InfoVect0LinkObjId="SW-54091_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24dd870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-644 3951,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24de500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-691 3951,-698 3994,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="9558@1" ObjectIDZND0="9602@x" ObjectIDZND1="9557@x" Pin0InfoVect0LinkObjId="g_2508bc0_0" Pin0InfoVect1LinkObjId="SW-54090_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54091_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-691 3951,-698 3994,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2508700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-648 3994,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="9602@1" ObjectIDZND0="9558@x" ObjectIDZND1="9557@x" Pin0InfoVect0LinkObjId="SW-54091_0" Pin0InfoVect1LinkObjId="SW-54090_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24de500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-648 3994,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2508960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-760 3994,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9546@0" ObjectIDZND0="9557@1" Pin0InfoVect0LinkObjId="SW-54090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-760 3994,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2508bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3994,-714 3994,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="9557@0" ObjectIDZND0="9602@x" ObjectIDZND1="9558@x" Pin0InfoVect0LinkObjId="g_24de500_0" Pin0InfoVect1LinkObjId="SW-54091_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3994,-714 3994,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24cb970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-642 4535,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_247a4a0@0" ObjectIDZND0="9556@0" Pin0InfoVect0LinkObjId="SW-54087_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_247a4a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-642 4535,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24cbbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4579,-696 4536,-696 4536,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="9555@x" ObjectIDZND0="9556@1" Pin0InfoVect0LinkObjId="SW-54087_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54086_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4579,-696 4536,-696 4536,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24cbe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4579,-652 4579,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="9555@x" ObjectIDZND1="9556@x" Pin0InfoVect0LinkObjId="SW-54086_0" Pin0InfoVect1LinkObjId="SW-54087_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4579,-652 4579,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24cc090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4579,-696 4579,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="9556@x" ObjectIDZND0="9555@0" Pin0InfoVect0LinkObjId="SW-54086_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54087_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4579,-696 4579,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24c14e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-618 4080,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_24c0ab0@0" ObjectIDZND0="9590@0" Pin0InfoVect0LinkObjId="SW-54183_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24c0ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-618 4080,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24c1740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-665 4080,-672 4124,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9590@1" ObjectIDZND0="9591@x" ObjectIDZND1="9592@x" Pin0InfoVect0LinkObjId="SW-54184_0" Pin0InfoVect1LinkObjId="SW-54186_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54183_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-665 4080,-672 4124,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24c19a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-714 4123,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9591@0" ObjectIDZND0="9590@x" ObjectIDZND1="9592@x" Pin0InfoVect0LinkObjId="SW-54183_0" Pin0InfoVect1LinkObjId="SW-54186_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54184_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-714 4123,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24c1c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-672 4123,-660 4243,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9591@x" ObjectIDND1="9590@x" ObjectIDZND0="9592@1" Pin0InfoVect0LinkObjId="SW-54186_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54184_0" Pin1InfoVect1LinkObjId="SW-54183_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-672 4123,-660 4243,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24c4240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4439,-619 4439,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2479a10@0" ObjectIDZND0="17669@0" Pin0InfoVect0LinkObjId="SW-54187_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2479a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4439,-619 4439,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24c44a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4439,-666 4439,-673 4396,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="17669@1" ObjectIDZND0="9589@x" ObjectIDZND1="9592@x" Pin0InfoVect0LinkObjId="SW-54182_0" Pin0InfoVect1LinkObjId="SW-54186_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54187_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4439,-666 4439,-673 4396,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24c4700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-709 4395,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9589@0" ObjectIDZND0="17669@x" ObjectIDZND1="9592@x" Pin0InfoVect0LinkObjId="SW-54187_0" Pin0InfoVect1LinkObjId="SW-54186_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-709 4395,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24c4960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-673 4395,-660 4270,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9589@x" ObjectIDND1="17669@x" ObjectIDZND0="9592@0" Pin0InfoVect0LinkObjId="SW-54186_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54182_0" Pin1InfoVect1LinkObjId="SW-54187_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-673 4395,-660 4270,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d5250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-1089 4094,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9548@x" ObjectIDND1="9549@x" ObjectIDZND0="g_2485490@0" ObjectIDZND1="g_24d5dd0@0" Pin0InfoVect0LinkObjId="g_2485490_0" Pin0InfoVect1LinkObjId="g_24d5dd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54054_0" Pin1InfoVect1LinkObjId="SW-54055_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-1089 4094,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d5440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-1096 4131,-1096 4131,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9548@x" ObjectIDND1="9549@x" ObjectIDND2="g_24d5dd0@0" ObjectIDZND0="g_2485490@0" Pin0InfoVect0LinkObjId="g_2485490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-54054_0" Pin1InfoVect1LinkObjId="SW-54055_0" Pin1InfoVect2LinkObjId="g_24d5dd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-1096 4131,-1096 4131,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d5970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4423,-1025 4423,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9550@1" ObjectIDZND0="g_24d5630@0" Pin0InfoVect0LinkObjId="g_24d5630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54058_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4423,-1025 4423,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d5ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4423,-1077 4423,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_24d5630@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24d5630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4423,-1077 4423,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251b740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-1096 4094,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2485490@0" ObjectIDND1="9548@x" ObjectIDND2="9549@x" ObjectIDZND0="g_24d5dd0@0" Pin0InfoVect0LinkObjId="g_24d5dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2485490_0" Pin1InfoVect1LinkObjId="SW-54054_0" Pin1InfoVect2LinkObjId="SW-54055_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-1096 4094,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251b9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-1139 4094,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_24d5dd0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24d5dd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-1139 4094,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2484580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-1080 3940,-1095 3995,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_24847e0@0" ObjectIDZND0="9560@x" ObjectIDZND1="9559@x" ObjectIDZND2="9548@x" Pin0InfoVect0LinkObjId="SW-54095_0" Pin0InfoVect1LinkObjId="SW-54094_0" Pin0InfoVect2LinkObjId="SW-54054_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24847e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-1080 3940,-1095 3995,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_252b900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3993,-412 3993,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9583@0" Pin0InfoVect0LinkObjId="SW-54161_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2553d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3993,-412 3993,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_252bb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3993,-469 3993,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9583@1" ObjectIDZND0="9582@0" Pin0InfoVect0LinkObjId="SW-54159_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54161_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3993,-469 3993,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2527460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-302 4304,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9581@0" ObjectIDZND0="9579@1" Pin0InfoVect0LinkObjId="SW-54145_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-302 4304,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25276c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-412 4304,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9580@1" Pin0InfoVect0LinkObjId="SW-54146_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2553d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-412 4304,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2527920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-350 4304,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9580@0" ObjectIDZND0="9581@1" Pin0InfoVect0LinkObjId="SW-54151_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54146_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-350 4304,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2527b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-155 4326,-155 4326,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="9579@x" ObjectIDND1="18153@x" ObjectIDZND0="g_2419ac0@0" Pin0InfoVect0LinkObjId="g_2419ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54145_0" Pin1InfoVect1LinkObjId="EC-LF_KLS.LD_KLS_075_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-155 4326,-155 4326,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2527de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-155 4304,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2419ac0@0" ObjectIDND1="9579@x" ObjectIDZND0="18153@0" Pin0InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2419ac0_0" Pin1InfoVect1LinkObjId="SW-54145_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-155 4304,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2528040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-245 4304,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="9579@0" ObjectIDZND0="g_2419ac0@0" ObjectIDZND1="18153@x" Pin0InfoVect0LinkObjId="g_2419ac0_0" Pin0InfoVect1LinkObjId="EC-LF_KLS.LD_KLS_075_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54145_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-245 4304,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f93b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-412 4364,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9600@0" Pin0InfoVect0LinkObjId="SW-54227_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2553d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-412 4364,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24fbd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-412 4494,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17339@0" ObjectIDZND0="9601@0" Pin0InfoVect0LinkObjId="SW-54228_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e5660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-412 4494,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a6d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-466 4494,-489 4440,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9601@1" ObjectIDZND0="9599@0" Pin0InfoVect0LinkObjId="SW-54214_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54228_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-466 4494,-489 4440,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a6fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4413,-489 4364,-489 4364,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9599@1" ObjectIDZND0="9600@1" Pin0InfoVect0LinkObjId="SW-54227_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54214_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4413,-489 4364,-489 4364,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a9920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-307 4555,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9595@0" ObjectIDZND0="9593@1" Pin0InfoVect0LinkObjId="SW-54193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54196_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-307 4555,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a9b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-412 4555,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17339@0" ObjectIDZND0="9594@1" Pin0InfoVect0LinkObjId="SW-54194_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e5660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-412 4555,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a9de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-355 4555,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9594@0" ObjectIDZND0="9595@1" Pin0InfoVect0LinkObjId="SW-54196_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54194_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-355 4555,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24aa040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-160 4577,-160 4577,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18154@x" ObjectIDND1="9593@x" ObjectIDZND0="g_2438850@0" Pin0InfoVect0LinkObjId="g_2438850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_081_0" Pin1InfoVect1LinkObjId="SW-54193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-160 4577,-160 4577,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24aa2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-160 4555,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2438850@0" ObjectIDND1="9593@x" ObjectIDZND0="18154@0" Pin0InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_081_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2438850_0" Pin1InfoVect1LinkObjId="SW-54193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-160 4555,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24aa500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-250 4555,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="9593@0" ObjectIDZND0="18154@x" ObjectIDZND1="g_2438850@0" Pin0InfoVect0LinkObjId="EC-LF_KLS.LD_KLS_081_0" Pin0InfoVect1LinkObjId="g_2438850_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-250 4555,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_247af30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4376,-1046 4376,-1039 4444,-1039 4444,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2486140@0" ObjectIDZND0="9551@1" Pin0InfoVect0LinkObjId="SW-54059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2486140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4376,-1046 4376,-1039 4444,-1039 4444,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_245f800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-344 4959,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_26f2670@0" Pin0InfoVect0LinkObjId="g_26f2670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_258ce40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-344 4959,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2468d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-1125 4565,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="18143@1" ObjectIDZND0="g_251bc00@0" ObjectIDZND1="0@x" ObjectIDZND2="9585@x" Pin0InfoVect0LinkObjId="g_251bc00_0" Pin0InfoVect1LinkObjId="g_258ce40_0" Pin0InfoVect2LinkObjId="SW-54169_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-1125 4565,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24696c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4538,-1066 4538,-1083 4565,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_251bc00@0" ObjectIDZND0="0@x" ObjectIDZND1="9585@x" ObjectIDZND2="9584@x" Pin0InfoVect0LinkObjId="g_258ce40_0" Pin0InfoVect1LinkObjId="SW-54169_0" Pin0InfoVect2LinkObjId="SW-54168_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_251bc00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4538,-1066 4538,-1083 4565,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26ea900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-1177 4120,-1157 4104,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_26e9fd0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26e9fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-1177 4120,-1157 4104,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26ecb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-1192 4120,-1203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_26e9fd0@1" ObjectIDZND0="g_26ec0c0@0" Pin0InfoVect0LinkObjId="g_26ec0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26e9fd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-1192 4120,-1203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26ecdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-1128 4449,-1108 4433,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_26ef470@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26ef470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-1128 4449,-1108 4433,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26ef210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-1143 4449,-1154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_26ef470@1" ObjectIDZND0="g_26ee780@0" Pin0InfoVect0LinkObjId="g_26ee780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26ef470_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-1143 4449,-1154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f0020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3670,-479 3670,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_252c730@0" ObjectIDND1="9553@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_258ce40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_252c730_0" Pin1InfoVect1LinkObjId="SW-54080_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3670,-479 3670,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26f5ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4833,-647 4833,-627 4817,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_26f6e30@0" ObjectIDZND0="g_26f4150@0" Pin0InfoVect0LinkObjId="g_26f4150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26f6e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4833,-647 4833,-627 4817,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26f6bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4833,-662 4833,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_26f6e30@1" ObjectIDZND0="g_26f6140@0" Pin0InfoVect0LinkObjId="g_26f6140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26f6e30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4833,-662 4833,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26f8630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-786 4565,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9587@0" ObjectIDZND0="9547@0" Pin0InfoVect0LinkObjId="g_26f8d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54171_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-786 4565,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26f8d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4579,-746 4579,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9555@1" ObjectIDZND0="9547@0" Pin0InfoVect0LinkObjId="g_26f8630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54086_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4579,-746 4579,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26f9420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-959 4423,-959 4423,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="9550@0" Pin0InfoVect0LinkObjId="SW-54058_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-959 4423,-959 4423,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26f9680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4631,-971 4565,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_251bc00@0" ObjectIDZND1="18143@1" ObjectIDZND2="9585@x" Pin0InfoVect0LinkObjId="g_251bc00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="SW-54169_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_258ce40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4631,-971 4565,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26fa150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-1083 4565,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_251bc00@0" ObjectIDND1="18143@1" ObjectIDZND0="0@x" ObjectIDZND1="9585@x" ObjectIDZND2="9584@x" Pin0InfoVect0LinkObjId="g_258ce40_0" Pin0InfoVect1LinkObjId="SW-54169_0" Pin0InfoVect2LinkObjId="SW-54168_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_251bc00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-1083 4565,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26fa390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-941 4565,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="9585@x" ObjectIDND1="9584@x" ObjectIDZND0="0@x" ObjectIDZND1="g_251bc00@0" ObjectIDZND2="18143@1" Pin0InfoVect0LinkObjId="g_258ce40_0" Pin0InfoVect1LinkObjId="g_251bc00_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54169_0" Pin1InfoVect1LinkObjId="SW-54168_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-941 4565,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2412620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-385 3694,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9568@1" ObjectIDZND0="17338@0" Pin0InfoVect0LinkObjId="g_2553d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-385 3694,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2412d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-232 3728,-232 3728,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="9567@x" ObjectIDND1="41618@x" ObjectIDZND0="9566@0" Pin0InfoVect0LinkObjId="SW-54108_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54109_0" Pin1InfoVect1LinkObjId="CB-LF_KLS.LF_KLS_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-232 3728,-232 3728,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250df50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-244 3694,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="9567@0" ObjectIDZND0="9566@x" ObjectIDZND1="41618@x" Pin0InfoVect0LinkObjId="SW-54108_0" Pin0InfoVect1LinkObjId="CB-LF_KLS.LF_KLS_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54109_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-244 3694,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250e180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-232 3694,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="9566@x" ObjectIDND1="9567@x" ObjectIDZND0="41618@1" Pin0InfoVect0LinkObjId="CB-LF_KLS.LF_KLS_Cb1_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54108_0" Pin1InfoVect1LinkObjId="SW-54109_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-232 3694,-220 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_KLS"/>
</svg>