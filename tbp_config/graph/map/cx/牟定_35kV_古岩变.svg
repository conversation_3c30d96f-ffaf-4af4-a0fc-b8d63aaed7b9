<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-206" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-583 -1129 2184 1223">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape45">
    <polyline arcFlag="1" points="19,100 17,100 15,99 14,99 12,98 11,97 9,96 8,94 7,92 7,91 6,89 6,87 6,85 7,83 7,82 8,80 9,79 11,77 12,76 14,75 15,75 17,74 19,74 21,74 23,75 24,75 26,76 27,77 29,79 30,80 31,82 31,83 32,85 32,87 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="32" x2="19" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="100" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="14" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="55" y2="47"/>
    <polyline arcFlag="1" points="36,14 37,14 38,14 38,14 39,14 39,15 40,15 40,16 41,16 41,17 41,17 41,18 42,19 42,19 42,20 41,21 41,21 41,22 41,22 40,23 40,23 39,24 39,24 38,24 38,25 37,25 36,25 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,36 37,36 38,36 38,37 39,37 39,37 40,38 40,38 41,39 41,39 41,40 41,40 42,41 42,42 42,42 41,43 41,44 41,44 41,45 40,45 40,46 39,46 39,47 38,47 38,47 37,47 36,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="19" x2="19" y1="87" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="19" x2="19" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="10" x2="26" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="21" y2="21"/>
    <polyline arcFlag="1" points="36,25 37,25 38,25 38,25 39,26 39,26 40,26 40,27 41,27 41,28 41,29 41,29 42,30 42,31 42,31 41,32 41,33 41,33 41,34 40,34 40,35 39,35 39,35 38,36 38,36 37,36 36,36 " stroke-width="1"/>
   </symbol>
   <symbol id="capacitor:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="37" x2="37" y1="122" y2="130"/>
    <polyline arcFlag="1" points="37,122 35,122 33,121 32,121 30,120 29,119 27,118 26,116 25,114 25,113 24,111 24,109 24,107 25,105 25,104 26,102 27,101 29,99 30,98 32,97 33,97 35,96 37,96 39,96 41,97 42,97 44,98 45,99 47,101 48,102 49,104 49,105 50,107 50,109 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="50" x2="38" y1="109" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.915724" x1="37" x2="37" y1="109" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="23" y2="14"/>
    <polyline arcFlag="1" points="13,23 12,23 12,23 11,23 10,23 10,24 9,24 8,25 8,25 8,26 7,27 7,27 7,28 7,29 7,30 7,30 8,31 8,32 8,32 9,33 10,33 10,34 11,34 12,34 12,34 13,34 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,34 12,34 12,34 11,34 10,34 10,35 9,35 8,36 8,36 8,37 7,38 7,38 7,39 7,40 7,41 7,41 8,42 8,43 8,43 9,44 10,44 10,45 11,45 12,45 12,45 13,45 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,45 12,45 12,45 11,45 10,45 10,46 9,46 8,47 8,47 8,48 7,49 7,49 7,50 7,51 7,52 7,52 8,53 8,54 8,54 9,55 10,55 10,56 11,56 12,56 12,56 13,56 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="65" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="13" y2="13"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="56" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="16" y2="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape141">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="84" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape24_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape54_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <rect height="28" stroke-width="1" width="14" x="90" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="95" x2="98" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="93" x2="101" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="97" x2="97" y1="28" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="103" x2="91" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="97" x2="97" y1="75" y2="40"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape54_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape3">
    <circle cx="7" cy="19" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape104">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="36" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="38" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="37" y1="43" y2="43"/>
    <ellipse cx="8" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="10" y2="14"/>
    <ellipse cx="19" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <ellipse cx="19" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="44" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="28" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="39" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="12" y2="18"/>
    <rect height="13" stroke-width="1" width="5" x="37" y="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e9030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e9a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33ea3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33eb0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33ec330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33ecf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ed9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ee500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33eed90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ef680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ef680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f1160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f1160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_33f2100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f3d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f48b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f51a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33f5a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f7190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f7960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f8050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33f8a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f9b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33fa510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33fb000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b83bd0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33fda70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33fe240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_340dc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ff5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3400920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33fba80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1233" width="2194" x="-588" y="-1134"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-48" x2="-48" y1="-30" y2="-12"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-107" x2="-49" y1="-12" y2="-12"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-108" x2="-108" y1="-165" y2="-12"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-48" x2="-5" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-5" x2="-5" y1="-106" y2="-93"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-136238">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 917.408592 -675.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24632" ObjectName="SW-MD_GY.MD_GY_301BK"/>
     <cge:Meas_Ref ObjectId="136238"/>
    <cge:TPSR_Ref TObjectID="24632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136261">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 916.408592 -498.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24635" ObjectName="SW-MD_GY.MD_GY_001BK"/>
     <cge:Meas_Ref ObjectId="136261"/>
    <cge:TPSR_Ref TObjectID="24635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136203">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 649.000000 -872.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24626" ObjectName="SW-MD_GY.MD_GY_341BK"/>
     <cge:Meas_Ref ObjectId="136203"/>
    <cge:TPSR_Ref TObjectID="24626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242827">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.764739 -364.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40838" ObjectName="SW-MD_GY.MD_GY_072BK"/>
     <cge:Meas_Ref ObjectId="242827"/>
    <cge:TPSR_Ref TObjectID="40838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136314">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 521.764739 -366.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24638" ObjectName="SW-MD_GY.MD_GY_071BK"/>
     <cge:Meas_Ref ObjectId="136314"/>
    <cge:TPSR_Ref TObjectID="24638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136340">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.764739 -365.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24643" ObjectName="SW-MD_GY.MD_GY_061BK"/>
     <cge:Meas_Ref ObjectId="136340"/>
    <cge:TPSR_Ref TObjectID="24643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136367">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1034.098072 -365.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24648" ObjectName="SW-MD_GY.MD_GY_062BK"/>
     <cge:Meas_Ref ObjectId="136367"/>
    <cge:TPSR_Ref TObjectID="24648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136393">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.431406 -364.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24653" ObjectName="SW-MD_GY.MD_GY_063BK"/>
     <cge:Meas_Ref ObjectId="136393"/>
    <cge:TPSR_Ref TObjectID="24653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136419">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.007692 -0.000000 0.000000 -0.986568 1432.960344 -365.720311)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24658" ObjectName="SW-MD_GY.MD_GY_064BK"/>
     <cge:Meas_Ref ObjectId="136419"/>
    <cge:TPSR_Ref TObjectID="24658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136448">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.000000 -511.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24665" ObjectName="SW-MD_GY.MD_GY_012BK"/>
     <cge:Meas_Ref ObjectId="136448"/>
    <cge:TPSR_Ref TObjectID="24665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242731">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 218.408592 -672.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40833" ObjectName="SW-MD_GY.MD_GY_302BK"/>
     <cge:Meas_Ref ObjectId="242731"/>
    <cge:TPSR_Ref TObjectID="40833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242730">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 218.408592 -486.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40832" ObjectName="SW-MD_GY.MD_GY_002BK"/>
     <cge:Meas_Ref ObjectId="242730"/>
    <cge:TPSR_Ref TObjectID="40832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242856">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.764739 -368.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40842" ObjectName="SW-MD_GY.MD_GY_073BK"/>
     <cge:Meas_Ref ObjectId="242856"/>
    <cge:TPSR_Ref TObjectID="40842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242885">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 129.764739 -365.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40846" ObjectName="SW-MD_GY.MD_GY_074BK"/>
     <cge:Meas_Ref ObjectId="242885"/>
    <cge:TPSR_Ref TObjectID="40846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242915">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -58.235261 -366.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40851" ObjectName="SW-MD_GY.MD_GY_075BK"/>
     <cge:Meas_Ref ObjectId="242915"/>
    <cge:TPSR_Ref TObjectID="40851"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29e31a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 685.000000 -1074.000000)" xlink:href="#voltageTransformer:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28a3d90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 463.000000 -578.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28a9d20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1204.000000 -573.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28dffb0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 606.000000 -638.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="609,-994 592,-994 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="609,-994 592,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="609,-925 594,-925 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="609,-925 594,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="609,-863 594,-863 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="609,-863 594,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="MD_GY" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xinyuguTgy" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="658,-1096 658,-1055 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34586" ObjectName="AC-35kV.LN_xinyuguTgy"/>
    <cge:TPSR_Ref TObjectID="34586_SS-206"/></metadata>
   <polyline fill="none" opacity="0" points="658,-1096 658,-1055 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 388.500000 -129.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_GY.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 522.000000 -123.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34283" ObjectName="EC-MD_GY.071Ld"/>
    <cge:TPSR_Ref TObjectID="34283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_GY.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 808.000000 -122.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34284" ObjectName="EC-MD_GY.061Ld"/>
    <cge:TPSR_Ref TObjectID="34284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_GY.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1034.333333 -122.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34285" ObjectName="EC-MD_GY.062Ld"/>
    <cge:TPSR_Ref TObjectID="34285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_GY.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1260.666667 -121.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34286" ObjectName="EC-MD_GY.063Ld"/>
    <cge:TPSR_Ref TObjectID="34286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 277.500000 -133.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_GY.074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 130.500000 -130.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41630" ObjectName="EC-MD_GY.074Ld"/>
    <cge:TPSR_Ref TObjectID="41630"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29a2f60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 827.000000 -712.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28beff0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 440.000000 -313.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28b72b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -312.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2945020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 952.333333 -312.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b8250" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1179.666667 -311.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28e5d10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 504.000000 -755.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2869180" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 574.000000 -988.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2869db0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 576.000000 -919.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_286a7e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 576.000000 -857.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2871230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1349.000000 -329.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a43a30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 492.000000 -686.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b77210" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -194.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b794e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1436.000000 -11.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e7eaa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 128.000000 -711.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e8d130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 356.000000 -232.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e9a5c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 245.000000 -236.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ea9710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98.000000 -233.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eb9a80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -89.000000 -234.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_29a69e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="693,-1065 693,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_29e31a0@0" Pin0InfoVect0LinkObjId="g_29e31a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e31a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="693,-1065 693,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2916dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-464 724,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24669@0" ObjectIDZND0="24623@0" Pin0InfoVect0LinkObjId="g_2952f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136451_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-464 724,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2916fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-494 724,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="24668@1" ObjectIDZND0="24669@1" Pin0InfoVect0LinkObjId="SW-136451_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-494 724,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29171a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-767 926,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24633@1" ObjectIDZND0="24622@0" Pin0InfoVect0LinkObjId="g_2954220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-767 926,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2917390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-683 926,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24632@0" ObjectIDZND0="24677@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136238_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-683 926,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2917580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-576 925,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="24677@0" ObjectIDZND0="24636@0" Pin0InfoVect0LinkObjId="SW-136263_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2917390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="925,-576 925,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2917770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-549 925,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24636@1" ObjectIDZND0="24635@1" Pin0InfoVect0LinkObjId="SW-136261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136263_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="925,-549 925,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2952d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-506 925,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24635@0" ObjectIDZND0="24637@1" Pin0InfoVect0LinkObjId="SW-136263_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="925,-506 925,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2952f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-472 925,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24637@0" ObjectIDZND0="24623@0" Pin0InfoVect0LinkObjId="g_2916dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136263_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="925,-472 925,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2953560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="897,-718 926,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24634@1" ObjectIDZND0="24633@x" ObjectIDZND1="24632@x" Pin0InfoVect0LinkObjId="SW-136240_0" Pin0InfoVect1LinkObjId="SW-136238_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="897,-718 926,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2953e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-731 926,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="24633@0" ObjectIDZND0="24632@x" ObjectIDZND1="24634@x" Pin0InfoVect0LinkObjId="SW-136238_0" Pin0InfoVect1LinkObjId="SW-136241_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="926,-731 926,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2954030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-718 926,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="24633@x" ObjectIDND1="24634@x" ObjectIDZND0="24632@1" Pin0InfoVect0LinkObjId="SW-136238_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136240_0" Pin1InfoVect1LinkObjId="SW-136241_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-718 926,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2954220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="658,-809 658,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24627@0" ObjectIDZND0="24622@0" Pin0InfoVect0LinkObjId="g_29171a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136205_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="658,-809 658,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2954410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-994 658,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="24631@1" ObjectIDZND0="24629@x" ObjectIDZND1="0@x" ObjectIDZND2="g_29e2810@0" Pin0InfoVect0LinkObjId="SW-136207_0" Pin0InfoVect1LinkObjId="g_29e31a0_0" Pin0InfoVect2LinkObjId="g_29e2810_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136209_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="645,-994 658,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2954cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="658,-973 658,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="24629@1" ObjectIDZND0="24631@x" ObjectIDZND1="0@x" ObjectIDZND2="g_29e2810@0" Pin0InfoVect0LinkObjId="SW-136209_0" Pin0InfoVect1LinkObjId="g_29e31a0_0" Pin0InfoVect2LinkObjId="g_29e2810_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136207_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="658,-973 658,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2954ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-925 658,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="24630@1" ObjectIDZND0="24626@x" ObjectIDZND1="24629@x" Pin0InfoVect0LinkObjId="SW-136203_0" Pin0InfoVect1LinkObjId="SW-136207_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136208_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="645,-925 658,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a1ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="658,-907 658,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24626@1" ObjectIDZND0="24629@x" ObjectIDZND1="24630@x" Pin0InfoVect0LinkObjId="SW-136207_0" Pin0InfoVect1LinkObjId="SW-136208_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136203_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="658,-907 658,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a1f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="658,-925 658,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="24626@x" ObjectIDND1="24630@x" ObjectIDZND0="24629@0" Pin0InfoVect0LinkObjId="SW-136207_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136203_0" Pin1InfoVect1LinkObjId="SW-136208_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="658,-925 658,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a2120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="645,-863 658,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24628@1" ObjectIDZND0="24627@x" ObjectIDZND1="24626@x" Pin0InfoVect0LinkObjId="SW-136205_0" Pin0InfoVect1LinkObjId="SW-136203_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136206_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="645,-863 658,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a2b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="658,-845 658,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="24627@1" ObjectIDZND0="24626@x" ObjectIDZND1="24628@x" Pin0InfoVect0LinkObjId="SW-136203_0" Pin0InfoVect1LinkObjId="SW-136206_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136205_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="658,-845 658,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a2d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="658,-863 658,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="24627@x" ObjectIDND1="24628@x" ObjectIDZND0="24626@0" Pin0InfoVect0LinkObjId="SW-136203_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136205_0" Pin1InfoVect1LinkObjId="SW-136206_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="658,-863 658,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a3890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="845,-718 860,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29a2f60@0" ObjectIDZND0="24634@0" Pin0InfoVect0LinkObjId="SW-136241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29a2f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="845,-718 860,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296a670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="531,-435 531,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24639@0" ObjectIDZND0="24624@0" Pin0InfoVect0LinkObjId="g_28a3b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136316_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="531,-435 531,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296a8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="531,-418 531,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24639@1" ObjectIDZND0="24638@1" Pin0InfoVect0LinkObjId="SW-136314_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136316_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="531,-418 531,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296ab00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="531,-374 531,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24638@0" ObjectIDZND0="24640@1" Pin0InfoVect0LinkObjId="SW-136316_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="531,-374 531,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296ad60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="531,-261 531,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2926690@1" ObjectIDZND0="24641@1" Pin0InfoVect0LinkObjId="SW-136317_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2926690_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="531,-261 531,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28be900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-174 565,-186 531,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2999860@0" ObjectIDZND0="24641@x" ObjectIDZND1="34283@x" Pin0InfoVect0LinkObjId="SW-136317_0" Pin0InfoVect1LinkObjId="EC-MD_GY.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2999860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="565,-174 565,-186 531,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28beb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="531,-206 531,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24641@0" ObjectIDZND0="g_2999860@0" ObjectIDZND1="34283@x" Pin0InfoVect0LinkObjId="g_2999860_0" Pin0InfoVect1LinkObjId="EC-MD_GY.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136317_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="531,-206 531,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28bed90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="531,-186 531,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24641@x" ObjectIDND1="g_2999860@0" ObjectIDZND0="34283@0" Pin0InfoVect0LinkObjId="EC-MD_GY.071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136317_0" Pin1InfoVect1LinkObjId="g_2999860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="531,-186 531,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28bfa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="513,-319 531,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24642@1" ObjectIDZND0="24640@x" ObjectIDZND1="g_2926690@0" Pin0InfoVect0LinkObjId="SW-136316_0" Pin0InfoVect1LinkObjId="g_2926690_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136318_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="513,-319 531,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28bfc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="531,-337 531,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="24640@0" ObjectIDZND0="g_2926690@0" ObjectIDZND1="24642@x" Pin0InfoVect0LinkObjId="g_2926690_0" Pin0InfoVect1LinkObjId="SW-136318_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136316_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="531,-337 531,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28bfee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="531,-319 531,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24640@x" ObjectIDND1="24642@x" ObjectIDZND0="g_2926690@0" Pin0InfoVect0LinkObjId="g_2926690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136316_0" Pin1InfoVect1LinkObjId="SW-136318_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="531,-319 531,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28c0140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="477,-319 458,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24642@0" ObjectIDZND0="g_28beff0@0" Pin0InfoVect0LinkObjId="g_28beff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136318_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="477,-319 458,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b5af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-434 817,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24644@0" ObjectIDZND0="24623@0" Pin0InfoVect0LinkObjId="g_2916dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-434 817,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b5d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-417 817,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24644@1" ObjectIDZND0="24643@1" Pin0InfoVect0LinkObjId="SW-136340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136342_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-417 817,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b5fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-373 817,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24643@0" ObjectIDZND0="24645@1" Pin0InfoVect0LinkObjId="SW-136342_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-373 817,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b6210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-260 817,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_28c89b0@1" ObjectIDZND0="24646@1" Pin0InfoVect0LinkObjId="SW-136343_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28c89b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-260 817,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b6b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-173 851,-185 817,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_291a640@0" ObjectIDZND0="24646@x" ObjectIDZND1="34284@x" Pin0InfoVect0LinkObjId="SW-136343_0" Pin0InfoVect1LinkObjId="EC-MD_GY.061Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_291a640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="851,-173 851,-185 817,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b6df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-205 817,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24646@0" ObjectIDZND0="g_291a640@0" ObjectIDZND1="34284@x" Pin0InfoVect0LinkObjId="g_291a640_0" Pin0InfoVect1LinkObjId="EC-MD_GY.061Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136343_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="817,-205 817,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28b7050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-185 817,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24646@x" ObjectIDND1="g_291a640@0" ObjectIDZND0="34284@0" Pin0InfoVect0LinkObjId="EC-MD_GY.061Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136343_0" Pin1InfoVect1LinkObjId="g_291a640_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-185 817,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28853c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-336 817,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="24645@0" ObjectIDZND0="g_28c89b0@0" ObjectIDZND1="24647@x" Pin0InfoVect0LinkObjId="g_28c89b0_0" Pin0InfoVect1LinkObjId="SW-136344_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="817,-336 817,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2885620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-318 817,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24645@x" ObjectIDND1="24647@x" ObjectIDZND0="g_28c89b0@0" Pin0InfoVect0LinkObjId="g_28c89b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136342_0" Pin1InfoVect1LinkObjId="SW-136344_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-318 817,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2977f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-434 1043,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24649@0" ObjectIDZND0="24623@0" Pin0InfoVect0LinkObjId="g_2916dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136369_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-434 1043,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2978190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-417 1043,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24649@1" ObjectIDZND0="24648@1" Pin0InfoVect0LinkObjId="SW-136367_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136369_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-417 1043,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29783f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-373 1043,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24648@0" ObjectIDZND0="24650@1" Pin0InfoVect0LinkObjId="SW-136369_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136367_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-373 1043,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2978650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-260 1043,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2888970@1" ObjectIDZND0="24651@1" Pin0InfoVect0LinkObjId="SW-136370_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2888970_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-260 1043,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2978fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1007,-169 1007,-185 1043,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_29917b0@0" ObjectIDZND0="24651@x" ObjectIDZND1="0@x" ObjectIDZND2="34285@x" Pin0InfoVect0LinkObjId="SW-136370_0" Pin0InfoVect1LinkObjId="g_29e31a0_0" Pin0InfoVect2LinkObjId="EC-MD_GY.062Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29917b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1007,-169 1007,-185 1043,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2979230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-205 1043,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="24651@0" ObjectIDZND0="g_29917b0@0" ObjectIDZND1="0@x" ObjectIDZND2="34285@x" Pin0InfoVect0LinkObjId="g_29917b0_0" Pin0InfoVect1LinkObjId="g_29e31a0_0" Pin0InfoVect2LinkObjId="EC-MD_GY.062Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-205 1043,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2945a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-318 1043,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24652@1" ObjectIDZND0="24650@x" ObjectIDZND1="g_2888970@0" Pin0InfoVect0LinkObjId="SW-136369_0" Pin0InfoVect1LinkObjId="g_2888970_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136371_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-318 1043,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2945cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-336 1043,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="24650@0" ObjectIDZND0="g_2888970@0" ObjectIDZND1="24652@x" Pin0InfoVect0LinkObjId="g_2888970_0" Pin0InfoVect1LinkObjId="SW-136371_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136369_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-336 1043,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2945f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-318 1043,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24650@x" ObjectIDND1="24652@x" ObjectIDZND0="g_2888970@0" Pin0InfoVect0LinkObjId="g_2888970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136369_0" Pin1InfoVect1LinkObjId="SW-136371_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-318 1043,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2946170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-318 970,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24652@0" ObjectIDZND0="g_2945020@0" Pin0InfoVect0LinkObjId="g_2945020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136371_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,-318 970,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b7410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-433 1270,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24654@0" ObjectIDZND0="24623@0" Pin0InfoVect0LinkObjId="g_2916dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-433 1270,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b7670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-416 1270,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24654@1" ObjectIDZND0="24653@1" Pin0InfoVect0LinkObjId="SW-136393_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136395_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-416 1270,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b78d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-372 1270,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24653@0" ObjectIDZND0="24655@1" Pin0InfoVect0LinkObjId="SW-136395_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-372 1270,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b8ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1252,-317 1270,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="load" ObjectIDND0="24657@1" ObjectIDZND0="24655@x" ObjectIDZND1="34286@x" ObjectIDZND2="34286@x" Pin0InfoVect0LinkObjId="SW-136395_0" Pin0InfoVect1LinkObjId="EC-MD_GY.063Ld_0" Pin0InfoVect2LinkObjId="EC-MD_GY.063Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136397_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1252,-317 1270,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29b8f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-335 1270,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="load" ObjectIDND0="24655@0" ObjectIDZND0="24657@x" ObjectIDZND1="34286@x" ObjectIDZND2="34286@x" Pin0InfoVect0LinkObjId="SW-136397_0" Pin0InfoVect1LinkObjId="EC-MD_GY.063Ld_0" Pin0InfoVect2LinkObjId="EC-MD_GY.063Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-335 1270,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297aa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1216,-317 1198,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24657@0" ObjectIDZND0="g_29b8250@0" Pin0InfoVect0LinkObjId="g_29b8250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-317 1198,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a0b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-426 1442,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24659@0" ObjectIDZND0="24623@0" Pin0InfoVect0LinkObjId="g_2916dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-426 1442,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a0d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1443,-264 1443,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_297da80@1" ObjectIDZND0="24661@1" Pin0InfoVect0LinkObjId="SW-136422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_297da80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1443,-264 1443,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a30a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="746,-318 763,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28b72b0@0" ObjectIDZND0="24647@0" Pin0InfoVect0LinkObjId="SW-136344_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28b72b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="746,-318 763,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a3300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="799,-318 817,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24647@1" ObjectIDZND0="24645@x" ObjectIDZND1="g_28c89b0@0" Pin0InfoVect0LinkObjId="SW-136342_0" Pin0InfoVect1LinkObjId="g_28c89b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136344_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="799,-318 817,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28a3b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="482,-467 482,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24675@0" ObjectIDZND0="24624@0" Pin0InfoVect0LinkObjId="g_296a670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136478_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="482,-467 482,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28af9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1223,-463 1223,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24674@0" ObjectIDZND0="24623@0" Pin0InfoVect0LinkObjId="g_2916dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136474_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1223,-463 1223,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28df3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-511 724,-521 690,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="24668@0" ObjectIDZND0="24667@0" Pin0InfoVect0LinkObjId="SW-136450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136451_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-511 724,-521 690,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28df630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-521 658,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24667@1" ObjectIDZND0="24665@0" Pin0InfoVect0LinkObjId="SW-136448_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136450_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-521 658,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28df890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-521 612,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24665@1" ObjectIDZND0="24666@1" Pin0InfoVect0LinkObjId="SW-136450_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136448_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="631,-521 612,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28dfd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="595,-521 582,-521 582,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24666@0" ObjectIDZND0="24624@0" Pin0InfoVect0LinkObjId="g_296a670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="595,-521 582,-521 582,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28e5740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-761 587,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="24671@1" ObjectIDZND0="24670@x" ObjectIDZND1="24622@0" Pin0InfoVect0LinkObjId="SW-136468_0" Pin0InfoVect1LinkObjId="g_29171a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136469_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="571,-761 587,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28e5930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="587,-747 587,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="24670@1" ObjectIDZND0="24622@0" ObjectIDZND1="24671@x" Pin0InfoVect0LinkObjId="g_29171a0_0" Pin0InfoVect1LinkObjId="SW-136469_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136468_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="587,-747 587,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28e5b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="587,-761 587,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="24670@x" ObjectIDND1="24671@x" ObjectIDZND0="24622@0" Pin0InfoVect0LinkObjId="g_29171a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136468_0" Pin1InfoVect1LinkObjId="SW-136469_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-761 587,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28e6640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="522,-761 534,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28e5d10@0" ObjectIDZND0="24671@0" Pin0InfoVect0LinkObjId="SW-136469_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28e5d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="522,-761 534,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28753f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="707,-755 707,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="24622@0" Pin0InfoVect0LinkObjId="g_29171a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e31a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="707,-755 707,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28755e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="707,-703 707,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_29e31a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e31a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="707,-703 707,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_287b740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="482,-503 482,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="24676@1" ObjectIDZND0="24675@1" Pin0InfoVect0LinkObjId="SW-136478_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136478_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="482,-503 482,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_287e790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1223,-497 1223,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="24673@1" ObjectIDZND0="24674@1" Pin0InfoVect0LinkObjId="SW-136474_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136474_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1223,-497 1223,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2874020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1367,-335 1382,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2871230@0" ObjectIDZND0="24662@0" Pin0InfoVect0LinkObjId="SW-136423_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2871230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1367,-335 1382,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2874280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1418,-335 1442,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="24662@1" ObjectIDZND0="g_297da80@0" ObjectIDZND1="24660@x" Pin0InfoVect0LinkObjId="g_297da80_0" Pin0InfoVect1LinkObjId="SW-136421_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136423_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1418,-335 1442,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a43570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="510,-692 522,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a43a30@0" ObjectIDZND0="24672@0" Pin0InfoVect0LinkObjId="SW-136470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a43a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="510,-692 522,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a437d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,-692 587,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24672@1" ObjectIDZND0="24670@x" ObjectIDZND1="g_2a44ec0@0" ObjectIDZND2="g_2a482e0@0" Pin0InfoVect0LinkObjId="SW-136468_0" Pin0InfoVect1LinkObjId="g_2a44ec0_0" Pin0InfoVect2LinkObjId="g_2a482e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136470_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="558,-692 587,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a45980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="610,-692 587,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2a44ec0@0" ObjectIDZND0="24672@x" ObjectIDZND1="24670@x" ObjectIDZND2="g_2a482e0@0" Pin0InfoVect0LinkObjId="SW-136470_0" Pin0InfoVect1LinkObjId="SW-136468_0" Pin0InfoVect2LinkObjId="g_2a482e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a44ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="610,-692 587,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a480f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="587,-711 587,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24670@0" ObjectIDZND0="24672@x" ObjectIDZND1="g_2a44ec0@0" ObjectIDZND2="g_2a482e0@0" Pin0InfoVect0LinkObjId="SW-136470_0" Pin0InfoVect1LinkObjId="g_2a44ec0_0" Pin0InfoVect2LinkObjId="g_2a482e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136468_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="587,-711 587,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a48aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="587,-692 587,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24672@x" ObjectIDND1="24670@x" ObjectIDND2="g_2a44ec0@0" ObjectIDZND0="g_2a482e0@0" Pin0InfoVect0LinkObjId="g_2a482e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-136470_0" Pin1InfoVect1LinkObjId="SW-136468_0" Pin1InfoVect2LinkObjId="g_2a44ec0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-692 587,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a48d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="587,-649 587,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2a482e0@1" ObjectIDZND0="g_28dffb0@0" Pin0InfoVect0LinkObjId="g_28dffb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a482e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="587,-649 587,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4d240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="482,-583 482,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_28a3d90@0" ObjectIDZND0="g_2a4cae0@0" Pin0InfoVect0LinkObjId="g_2a4cae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28a3d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="482,-583 482,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4e250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="438,-537 438,-531 482,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2a4d4a0@0" ObjectIDZND0="g_2a4cae0@0" ObjectIDZND1="24676@x" Pin0InfoVect0LinkObjId="g_2a4cae0_0" Pin0InfoVect1LinkObjId="SW-136478_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a4d4a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="438,-537 438,-531 482,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4ed40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="482,-541 482,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2a4cae0@1" ObjectIDZND0="g_2a4d4a0@0" ObjectIDZND1="24676@x" Pin0InfoVect0LinkObjId="g_2a4d4a0_0" Pin0InfoVect1LinkObjId="SW-136478_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a4cae0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="482,-541 482,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4efa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="482,-531 482,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a4d4a0@0" ObjectIDND1="g_2a4cae0@0" ObjectIDZND0="24676@0" Pin0InfoVect0LinkObjId="SW-136478_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a4d4a0_0" Pin1InfoVect1LinkObjId="g_2a4cae0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="482,-531 482,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a50e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-104 1092,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_29e31a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e31a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-104 1092,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a510f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-168 1092,-178 1043,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="0@0" ObjectIDZND0="24651@x" ObjectIDZND1="g_29917b0@0" ObjectIDZND2="34285@x" Pin0InfoVect0LinkObjId="SW-136370_0" Pin0InfoVect1LinkObjId="g_29917b0_0" Pin0InfoVect2LinkObjId="EC-MD_GY.062Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e31a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-168 1092,-178 1043,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a51be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-185 1043,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="24651@x" ObjectIDND1="g_29917b0@0" ObjectIDZND0="0@x" ObjectIDZND1="34285@x" Pin0InfoVect0LinkObjId="g_29e31a0_0" Pin0InfoVect1LinkObjId="EC-MD_GY.062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136370_0" Pin1InfoVect1LinkObjId="g_29917b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-185 1043,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a51e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-178 1043,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="24651@x" ObjectIDND2="g_29917b0@0" ObjectIDZND0="34285@0" Pin0InfoVect0LinkObjId="EC-MD_GY.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29e31a0_0" Pin1InfoVect1LinkObjId="SW-136370_0" Pin1InfoVect2LinkObjId="g_29917b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-178 1043,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a52a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1223,-578 1223,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_28a9d20@0" ObjectIDZND0="g_2a520a0@0" Pin0InfoVect0LinkObjId="g_2a520a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28a9d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1223,-578 1223,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a53a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1180,-529 1180,-520 1223,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2a52cd0@0" ObjectIDZND0="g_2a520a0@0" ObjectIDZND1="24673@x" Pin0InfoVect0LinkObjId="g_2a520a0_0" Pin0InfoVect1LinkObjId="SW-136474_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a52cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1180,-529 1180,-520 1223,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a54570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1223,-536 1223,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2a520a0@1" ObjectIDZND0="g_2a52cd0@0" ObjectIDZND1="24673@x" Pin0InfoVect0LinkObjId="g_2a52cd0_0" Pin0InfoVect1LinkObjId="SW-136474_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a520a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1223,-536 1223,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a547d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1223,-520 1223,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a52cd0@0" ObjectIDND1="g_2a520a0@0" ObjectIDZND0="24673@0" Pin0InfoVect0LinkObjId="SW-136474_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a52cd0_0" Pin1InfoVect1LinkObjId="g_2a520a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1223,-520 1223,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b77ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,-200 1382,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2b77210@0" ObjectIDZND0="24663@0" Pin0InfoVect0LinkObjId="SW-136424_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b77210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1366,-200 1382,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b77f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1418,-200 1442,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="24663@1" ObjectIDZND0="24661@x" ObjectIDZND1="40764@x" Pin0InfoVect0LinkObjId="SW-136422_0" Pin0InfoVect1LinkObjId="CB-MD_GY.MD_GY_cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136424_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1418,-200 1442,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b789f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-208 1442,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="24661@0" ObjectIDZND0="24663@x" ObjectIDZND1="40764@x" Pin0InfoVect0LinkObjId="SW-136424_0" Pin0InfoVect1LinkObjId="CB-MD_GY.MD_GY_cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-208 1442,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b78c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-200 1442,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="24663@x" ObjectIDND1="24661@x" ObjectIDZND0="40764@0" Pin0InfoVect0LinkObjId="CB-MD_GY.MD_GY_cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136424_0" Pin1InfoVect1LinkObjId="SW-136422_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-200 1442,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b79cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-79 1442,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="24664@1" Pin0InfoVect0LinkObjId="SW-136425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-79 1442,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b79ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-38 1442,-29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24664@0" ObjectIDZND0="g_2b794e0@0" Pin0InfoVect0LinkObjId="g_2b794e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-38 1442,-29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b7cb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-335 1442,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24662@x" ObjectIDND1="24660@x" ObjectIDZND0="g_297da80@0" Pin0InfoVect0LinkObjId="g_297da80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136423_0" Pin1InfoVect1LinkObjId="SW-136421_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-335 1442,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b7e110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="693,-1020 693,-1009 658,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="24629@x" ObjectIDZND1="24631@x" ObjectIDZND2="g_29e2810@0" Pin0InfoVect0LinkObjId="SW-136207_0" Pin0InfoVect1LinkObjId="SW-136209_0" Pin0InfoVect2LinkObjId="g_29e2810_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e31a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="693,-1020 693,-1009 658,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b7e300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="658,-1009 658,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_29e2810@0" ObjectIDND2="34586@1" ObjectIDZND0="24629@x" ObjectIDZND1="24631@x" Pin0InfoVect0LinkObjId="SW-136207_0" Pin0InfoVect1LinkObjId="SW-136209_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29e31a0_0" Pin1InfoVect1LinkObjId="g_29e2810_0" Pin1InfoVect2LinkObjId="g_2b7f1a0_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="658,-1009 658,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b7ef40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="658,-1009 658,-1029 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="0@x" ObjectIDND1="24629@x" ObjectIDND2="24631@x" ObjectIDZND0="g_29e2810@0" ObjectIDZND1="34586@1" Pin0InfoVect0LinkObjId="g_29e2810_0" Pin0InfoVect1LinkObjId="g_2b7f1a0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29e31a0_0" Pin1InfoVect1LinkObjId="SW-136207_0" Pin1InfoVect2LinkObjId="SW-136209_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="658,-1009 658,-1029 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b7f1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="658,-1029 658,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="0@x" ObjectIDND1="24629@x" ObjectIDND2="24631@x" ObjectIDZND0="34586@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29e31a0_0" Pin1InfoVect1LinkObjId="SW-136207_0" Pin1InfoVect2LinkObjId="SW-136209_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="658,-1029 658,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b7f400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-1043 614,-1029 658,-1029 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_29e2810@0" ObjectIDZND0="0@x" ObjectIDZND1="24629@x" ObjectIDZND2="24631@x" Pin0InfoVect0LinkObjId="g_29e31a0_0" Pin0InfoVect1LinkObjId="SW-136207_0" Pin0InfoVect2LinkObjId="SW-136209_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e2810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="614,-1043 614,-1029 658,-1029 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b88910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-400 1442,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24658@1" ObjectIDZND0="24659@1" Pin0InfoVect0LinkObjId="SW-136421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136419_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-400 1442,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b88b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-335 1442,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="24662@x" ObjectIDND1="g_297da80@0" ObjectIDZND0="24660@0" Pin0InfoVect0LinkObjId="SW-136421_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136423_0" Pin1InfoVect1LinkObjId="g_297da80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-335 1442,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b88d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,-358 1442,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24660@1" ObjectIDZND0="24658@0" Pin0InfoVect0LinkObjId="SW-136419_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136421_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-358 1442,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e7e840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-766 227,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="40836@1" ObjectIDZND0="24622@0" Pin0InfoVect0LinkObjId="g_29171a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242733_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="227,-766 227,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e7f530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="146,-717 161,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e7eaa0@0" ObjectIDZND0="40837@0" Pin0InfoVect0LinkObjId="SW-242734_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e7eaa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="146,-717 161,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e7fdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-450 227,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24624@0" ObjectIDZND0="40835@0" Pin0InfoVect0LinkObjId="SW-242732_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296a670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="227,-450 227,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e7ffb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-480 227,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40835@1" ObjectIDZND0="40832@0" Pin0InfoVect0LinkObjId="SW-242730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242732_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="227,-480 227,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e801a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-521 227,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40832@1" ObjectIDZND0="40834@1" Pin0InfoVect0LinkObjId="SW-242732_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="227,-521 227,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e839a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-551 227,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="40834@0" ObjectIDZND0="g_2e80390@0" ObjectIDZND1="40857@x" Pin0InfoVect0LinkObjId="g_2e80390_0" Pin0InfoVect1LinkObjId="g_2e84bb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242732_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="227,-551 227,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e83c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-562 191,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="40834@x" ObjectIDND1="40857@x" ObjectIDZND0="g_2e80390@0" Pin0InfoVect0LinkObjId="g_2e80390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-242732_0" Pin1InfoVect1LinkObjId="g_2e84bb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="227,-562 191,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e846f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-730 227,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="40836@0" ObjectIDZND0="40837@x" ObjectIDZND1="40833@x" Pin0InfoVect0LinkObjId="SW-242734_0" Pin0InfoVect1LinkObjId="SW-242731_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="227,-730 227,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e84950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-717 198,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="40836@x" ObjectIDND1="40833@x" ObjectIDZND0="40837@1" Pin0InfoVect0LinkObjId="SW-242734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-242733_0" Pin1InfoVect1LinkObjId="SW-242731_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="227,-717 198,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e84bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-562 227,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="40834@x" ObjectIDND1="g_2e80390@0" ObjectIDZND0="40857@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-242732_0" Pin1InfoVect1LinkObjId="g_2e80390_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="227,-562 227,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e876c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-669 227,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="40857@1" ObjectIDZND0="40833@0" Pin0InfoVect0LinkObjId="SW-242731_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e84bb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="227,-669 227,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e87920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-707 227,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="40833@1" ObjectIDZND0="40836@x" ObjectIDZND1="40837@x" Pin0InfoVect0LinkObjId="SW-242733_0" Pin0InfoVect1LinkObjId="SW-242734_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242731_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="227,-707 227,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e888c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="397,-450 397,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24624@0" ObjectIDZND0="40839@0" Pin0InfoVect0LinkObjId="SW-242828_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296a670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="397,-450 397,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e88ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="397,-416 397,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40839@1" ObjectIDZND0="40838@1" Pin0InfoVect0LinkObjId="SW-242827_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242828_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="397,-416 397,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e88d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="397,-372 397,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40838@0" ObjectIDZND0="40840@1" Pin0InfoVect0LinkObjId="SW-242828_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242827_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="397,-372 397,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e8a230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="397,-313 362,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="40840@x" ObjectIDND1="0@x" ObjectIDZND0="40841@x" ObjectIDZND1="g_29a3ab0@0" Pin0InfoVect0LinkObjId="SW-242829_0" Pin0InfoVect1LinkObjId="g_29a3ab0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-242828_0" Pin1InfoVect1LinkObjId="g_29e31a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="397,-313 362,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e8cc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="362,-313 362,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="40840@x" ObjectIDND1="0@x" ObjectIDND2="g_29a3ab0@0" ObjectIDZND0="40841@1" Pin0InfoVect0LinkObjId="SW-242829_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-242828_0" Pin1InfoVect1LinkObjId="g_29e31a0_0" Pin1InfoVect2LinkObjId="g_29a3ab0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="362,-313 362,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e8ced0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="362,-263 362,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="40841@0" ObjectIDZND0="g_2e8d130@0" Pin0InfoVect0LinkObjId="g_2e8d130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="362,-263 362,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e96f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="286,-450 286,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24624@0" ObjectIDZND0="40843@0" Pin0InfoVect0LinkObjId="SW-242857_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296a670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="286,-450 286,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e971e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="286,-420 286,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40843@1" ObjectIDZND0="40842@1" Pin0InfoVect0LinkObjId="SW-242856_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242857_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="286,-420 286,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e97440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="286,-376 286,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40842@0" ObjectIDZND0="40844@1" Pin0InfoVect0LinkObjId="SW-242857_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242856_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="286,-376 286,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e976a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="286,-317 251,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="40844@x" ObjectIDND1="0@x" ObjectIDZND0="40845@x" ObjectIDZND1="g_2e8db80@0" Pin0InfoVect0LinkObjId="SW-242858_0" Pin0InfoVect1LinkObjId="g_2e8db80_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-242857_0" Pin1InfoVect1LinkObjId="g_29e31a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="286,-317 251,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e9a100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="251,-317 251,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="40844@x" ObjectIDND1="0@x" ObjectIDND2="g_2e8db80@0" ObjectIDZND0="40845@1" Pin0InfoVect0LinkObjId="SW-242858_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-242857_0" Pin1InfoVect1LinkObjId="g_29e31a0_0" Pin1InfoVect2LinkObjId="g_2e8db80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-317 251,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e9a360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="251,-267 251,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="40845@0" ObjectIDZND0="g_2e9a5c0@0" Pin0InfoVect0LinkObjId="g_2e9a5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242858_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-267 251,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea60d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="139,-450 139,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24624@0" ObjectIDZND0="40847@0" Pin0InfoVect0LinkObjId="SW-242886_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296a670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="139,-450 139,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea6330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="139,-417 139,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40847@1" ObjectIDZND0="40846@1" Pin0InfoVect0LinkObjId="SW-242885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242886_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="139,-417 139,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea6590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="139,-373 139,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40846@0" ObjectIDZND0="40848@1" Pin0InfoVect0LinkObjId="SW-242886_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="139,-373 139,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea67f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="139,-314 104,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="40848@x" ObjectIDND1="g_2eaca60@0" ObjectIDZND0="40850@x" ObjectIDZND1="g_2e9cf20@0" Pin0InfoVect0LinkObjId="SW-242888_0" Pin0InfoVect1LinkObjId="g_2e9cf20_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-242886_0" Pin1InfoVect1LinkObjId="g_2eaca60_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="139,-314 104,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea9250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="104,-314 104,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="40848@x" ObjectIDND1="g_2eaca60@0" ObjectIDND2="g_2e9cf20@0" ObjectIDZND0="40850@1" Pin0InfoVect0LinkObjId="SW-242888_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-242886_0" Pin1InfoVect1LinkObjId="g_2eaca60_0" Pin1InfoVect2LinkObjId="g_2e9cf20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="104,-314 104,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ea94b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="104,-264 104,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="40850@0" ObjectIDZND0="g_2ea9710@0" Pin0InfoVect0LinkObjId="g_2ea9710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242888_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="104,-264 104,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eaafc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="286,-341 286,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="40844@0" ObjectIDZND0="40845@x" ObjectIDZND1="g_2e8db80@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-242858_0" Pin0InfoVect1LinkObjId="g_2e8db80_0" Pin0InfoVect2LinkObjId="g_29e31a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242857_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="286,-341 286,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eabab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="397,-337 397,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="40840@0" ObjectIDZND0="40841@x" ObjectIDZND1="g_29a3ab0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-242829_0" Pin0InfoVect1LinkObjId="g_29a3ab0_0" Pin0InfoVect2LinkObjId="g_29e31a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242828_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="397,-337 397,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eac5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="139,-338 139,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="40848@0" ObjectIDZND0="40850@x" ObjectIDZND1="g_2e9cf20@0" ObjectIDZND2="g_2eaca60@0" Pin0InfoVect0LinkObjId="SW-242888_0" Pin0InfoVect1LinkObjId="g_2e9cf20_0" Pin0InfoVect2LinkObjId="g_2eaca60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242886_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="139,-338 139,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eac800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="397,-156 397,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="40841@x" ObjectIDZND1="g_29a3ab0@0" ObjectIDZND2="40840@x" Pin0InfoVect0LinkObjId="SW-242829_0" Pin0InfoVect1LinkObjId="g_29a3ab0_0" Pin0InfoVect2LinkObjId="SW-242828_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e31a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="397,-156 397,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ead4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="139,-306 139,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2eaca60@0" ObjectIDZND0="40850@x" ObjectIDZND1="g_2e9cf20@0" ObjectIDZND2="40848@x" Pin0InfoVect0LinkObjId="SW-242888_0" Pin0InfoVect1LinkObjId="g_2e9cf20_0" Pin0InfoVect2LinkObjId="SW-242886_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eaca60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="139,-306 139,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb6440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-48,-450 -48,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24624@0" ObjectIDZND0="40852@0" Pin0InfoVect0LinkObjId="SW-242916_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296a670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-48,-450 -48,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb66a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-48,-418 -48,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="40852@1" ObjectIDZND0="40851@1" Pin0InfoVect0LinkObjId="SW-242915_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-48,-418 -48,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb6900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-48,-374 -48,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="40851@0" ObjectIDZND0="40853@1" Pin0InfoVect0LinkObjId="SW-242916_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242915_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-48,-374 -48,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb6b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-48,-315 -83,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="40853@x" ObjectIDND1="g_2eba730@0" ObjectIDZND0="40855@x" ObjectIDZND1="g_2ead740@0" Pin0InfoVect0LinkObjId="SW-242918_0" Pin0InfoVect1LinkObjId="g_2ead740_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-242916_0" Pin1InfoVect1LinkObjId="g_2eba730_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-48,-315 -83,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb95c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-83,-315 -83,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="40853@x" ObjectIDND1="g_2eba730@0" ObjectIDND2="g_2ead740@0" ObjectIDZND0="40855@1" Pin0InfoVect0LinkObjId="SW-242918_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-242916_0" Pin1InfoVect1LinkObjId="g_2eba730_0" Pin1InfoVect2LinkObjId="g_2ead740_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-83,-315 -83,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eb9820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-83,-265 -83,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="40855@0" ObjectIDZND0="g_2eb9a80@0" Pin0InfoVect0LinkObjId="g_2eb9a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242918_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-83,-265 -83,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eba4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-48,-339 -48,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="40853@0" ObjectIDZND0="40855@x" ObjectIDZND1="g_2ead740@0" ObjectIDZND2="g_2eba730@0" Pin0InfoVect0LinkObjId="SW-242918_0" Pin0InfoVect1LinkObjId="g_2ead740_0" Pin0InfoVect2LinkObjId="g_2eba730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-48,-339 -48,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ebb1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-48,-307 -48,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2eba730@0" ObjectIDZND0="40853@x" ObjectIDZND1="40855@x" ObjectIDZND2="g_2ead740@0" Pin0InfoVect0LinkObjId="SW-242916_0" Pin0InfoVect1LinkObjId="SW-242918_0" Pin0InfoVect2LinkObjId="g_2ead740_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eba730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-48,-307 -48,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ebcb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="139,-201 107,-201 107,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="41630@x" ObjectIDND1="40849@x" ObjectIDZND0="g_2ebd850@0" Pin0InfoVect0LinkObjId="g_2ebd850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-MD_GY.074Ld_0" Pin1InfoVect1LinkObjId="SW-242887_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="139,-201 107,-201 107,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ebd5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="139,-157 139,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="41630@0" ObjectIDZND0="g_2ebd850@0" ObjectIDZND1="40849@x" Pin0InfoVect0LinkObjId="g_2ebd850_0" Pin0InfoVect1LinkObjId="SW-242887_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-MD_GY.074Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="139,-157 139,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec0d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="139,-201 139,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="41630@x" ObjectIDND1="g_2ebd850@0" ObjectIDZND0="40849@0" Pin0InfoVect0LinkObjId="SW-242887_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-MD_GY.074Ld_0" Pin1InfoVect1LinkObjId="g_2ebd850_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="139,-201 139,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec0fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="139,-250 139,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40849@1" ObjectIDZND0="g_2eaca60@1" Pin0InfoVect0LinkObjId="g_2eaca60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242887_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="139,-250 139,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec42d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-48,-250 -48,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="40854@1" ObjectIDZND0="g_2eba730@1" Pin0InfoVect0LinkObjId="g_2eba730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-242917_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-48,-250 -48,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ec4530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-48,-194 -107,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="40854@x" ObjectIDND1="40858@x" ObjectIDZND0="40856@0" Pin0InfoVect0LinkObjId="SW-242919_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-242917_0" Pin1InfoVect1LinkObjId="CB-MD_GY.MD_GY_cb2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-48,-194 -107,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ece6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-48,-194 -48,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="40856@x" ObjectIDND1="40858@x" ObjectIDZND0="40854@0" Pin0InfoVect0LinkObjId="SW-242917_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-242919_0" Pin1InfoVect1LinkObjId="CB-MD_GY.MD_GY_cb2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-48,-194 -48,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ece900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-48,-158 -48,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="40858@0" ObjectIDZND0="40856@x" ObjectIDZND1="40854@x" Pin0InfoVect0LinkObjId="SW-242919_0" Pin0InfoVect1LinkObjId="SW-242917_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-MD_GY.MD_GY_cb2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-48,-158 -48,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ed17b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="285,-318 285,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="40845@x" ObjectIDND1="g_2e8db80@0" ObjectIDND2="40844@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_29e31a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-242858_0" Pin1InfoVect1LinkObjId="g_2e8db80_0" Pin1InfoVect2LinkObjId="SW-242857_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="285,-318 285,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eda8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-157,-297 -157,-315 -83,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2ead740@0" ObjectIDZND0="40853@x" ObjectIDZND1="g_2eba730@0" ObjectIDZND2="40855@x" Pin0InfoVect0LinkObjId="SW-242916_0" Pin0InfoVect1LinkObjId="g_2eba730_0" Pin0InfoVect2LinkObjId="SW-242918_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ead740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-157,-297 -157,-315 -83,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2edacd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="40,-300 40,-314 104,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2e9cf20@0" ObjectIDZND0="40848@x" ObjectIDZND1="g_2eaca60@0" ObjectIDZND2="40850@x" Pin0InfoVect0LinkObjId="SW-242886_0" Pin0InfoVect1LinkObjId="g_2eaca60_0" Pin0InfoVect2LinkObjId="SW-242888_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e9cf20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="40,-300 40,-314 104,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2edaf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="192,-306 192,-317 251,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_2e8db80@0" ObjectIDZND0="40844@x" ObjectIDZND1="0@x" ObjectIDZND2="40845@x" Pin0InfoVect0LinkObjId="SW-242857_0" Pin0InfoVect1LinkObjId="g_29e31a0_0" Pin0InfoVect2LinkObjId="SW-242858_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e8db80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="192,-306 192,-317 251,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2edb130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="309,-298 309,-313 362,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_29a3ab0@0" ObjectIDZND0="40840@x" ObjectIDZND1="0@x" ObjectIDZND2="40841@x" Pin0InfoVect0LinkObjId="SW-242828_0" Pin0InfoVect1LinkObjId="g_29e31a0_0" Pin0InfoVect2LinkObjId="SW-242829_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29a3ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="309,-298 309,-313 362,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2edbd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-204 1270,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" EndDevType1="load" EndDevType2="switch" ObjectIDND0="24655@x" ObjectIDND1="24657@x" ObjectIDND2="24655@x" ObjectIDZND0="34286@x" ObjectIDZND1="34286@x" ObjectIDZND2="24655@x" Pin0InfoVect0LinkObjId="EC-MD_GY.063Ld_0" Pin0InfoVect1LinkObjId="EC-MD_GY.063Ld_0" Pin0InfoVect2LinkObjId="SW-136395_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-136395_0" Pin1InfoVect1LinkObjId="SW-136397_0" Pin1InfoVect2LinkObjId="SW-136395_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-204 1270,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2edbff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-197 1270,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="24655@x" ObjectIDND1="24657@x" ObjectIDND2="24655@x" ObjectIDZND0="34286@0" Pin0InfoVect0LinkObjId="EC-MD_GY.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-136395_0" Pin1InfoVect1LinkObjId="SW-136397_0" Pin1InfoVect2LinkObjId="SW-136395_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-197 1270,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2edcac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-317 1270,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" EndDevType1="load" EndDevType2="switch" ObjectIDND0="24655@x" ObjectIDND1="24657@x" ObjectIDZND0="34286@x" ObjectIDZND1="34286@x" ObjectIDZND2="24655@x" Pin0InfoVect0LinkObjId="EC-MD_GY.063Ld_0" Pin0InfoVect1LinkObjId="EC-MD_GY.063Ld_0" Pin0InfoVect2LinkObjId="SW-136395_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136395_0" Pin1InfoVect1LinkObjId="SW-136397_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-317 1270,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2edcd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-204 1270,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="34286@x" ObjectIDND1="24655@x" ObjectIDND2="24657@x" ObjectIDZND0="24655@x" ObjectIDZND1="24657@x" ObjectIDZND2="34286@x" Pin0InfoVect0LinkObjId="SW-136395_0" Pin0InfoVect1LinkObjId="SW-136397_0" Pin0InfoVect2LinkObjId="EC-MD_GY.063Ld_0" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-MD_GY.063Ld_0" Pin1InfoVect1LinkObjId="SW-136395_0" Pin1InfoVect2LinkObjId="SW-136397_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-204 1270,-197 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24623" cx="925" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24623" cx="1043" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24623" cx="1270" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24623" cx="1442" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24623" cx="1223" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24623" cx="724" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24623" cx="817" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24622" cx="926" cy="-782" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24622" cx="658" cy="-782" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24622" cx="587" cy="-782" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24622" cx="707" cy="-782" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24624" cx="531" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24624" cx="482" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24624" cx="582" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24624" cx="397" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24622" cx="227" cy="-782" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24624" cx="227" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24624" cx="286" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24624" cx="139" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24624" cx="-48" cy="-450" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-136077" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -282.000000 -1022.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24606" ObjectName="DYN-MD_GY"/>
     <cge:Meas_Ref ObjectId="136077"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_28b9d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -431.000000 -1101.500000) translate(0,16)">古岩变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2793de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2793de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2793de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2793de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2793de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2793de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2793de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2793de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2793de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -960.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2030880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -571.000000 -522.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24908b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1123.000000 -807.000000) translate(0,12)">35kV  I母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_269b3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -217.000000 -476.000000) translate(0,12)">10kV II母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d64b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 612.000000 -1122.000000) translate(0,12)">35kV新余古线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1c380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -745.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1c6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 548.000000 -582.000000) translate(0,12)">35kVⅠ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1c9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 941.000000 -753.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1cd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 963.000000 -612.000000) translate(0,12)">SZ11-2500kVA/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1cd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 963.000000 -612.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1cd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 963.000000 -612.000000) translate(0,42)">yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1cec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1519.000000 -471.000000) translate(0,12)">10kV  I母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1d1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 742.000000 -490.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e7950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 -832.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e7b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.500000 -958.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e7d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 600.000000 -1015.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e3690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 600.000000 -943.000000) translate(0,12)">34160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e3880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 601.000000 -882.000000) translate(0,12)">34117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298cf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 517.000000 -98.000000) translate(0,12)">联丰线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298d110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 807.000000 -98.000000) translate(0,12)">碑厅线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298d2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1015.000000 -98.000000) translate(0,12)">双龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298d470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1249.000000 -98.000000) translate(0,12)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28b0210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1240.000000 -493.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28b0a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1180.000000 -635.000000) translate(0,12)">10kV I母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28b0cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -646.000000) translate(0,12)">10kV II母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28e4ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -750.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28e5500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 596.000000 -733.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ea3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 677.000000 -582.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2878490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 -901.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2878a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 632.000000 -545.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2878cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 943.000000 -704.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287b980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 495.000000 -495.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287e9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 545.000000 -396.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287ef60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -396.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287f690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1052.000000 -396.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287f9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1280.000000 -396.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287fbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1451.000000 -396.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287fe30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 935.000000 -527.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2885160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 615.000000 -503.000000) translate(0,12)">10kV分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_286fca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -566.000000 -740.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a44460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -718.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2a45d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -306.000000 -1082.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2a47a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -306.000000 -1117.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a4c3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 -637.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b78eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1377.000000 -229.000000) translate(0,12)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7a140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1449.000000 -60.000000) translate(0,12)">06400</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7b200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -350.000000) translate(0,12)">07127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7b440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 546.000000 -234.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7b680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -349.000000) translate(0,12)">06127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 -231.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7bb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 981.000000 -348.000000) translate(0,12)">06227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7bd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -230.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7bf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1205.000000 -350.000000) translate(0,12)">06317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7c1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1455.000000 -229.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1380.000000 -361.000000) translate(0,12)">06417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b82f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -583.000000 -120.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b82f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -583.000000 -120.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b85480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -451.000000 -129.000000) translate(0,16)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b85480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -451.000000 -129.000000) translate(0,36)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b85480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -451.000000 -129.000000) translate(0,56)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b87520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -451.000000 -175.000000) translate(0,16)">5357303</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b87520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -451.000000 -175.000000) translate(0,36)">2258</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b87940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1103.000000 -28.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2b87ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -293.500000 -989.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b88f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1400.000000 -11.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7f790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.000000 -703.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e87b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 388.000000 -98.000000) translate(0,12)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9c700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 267.000000 -98.000000) translate(0,12)">备用三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ec1240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 117.000000 -98.000000) translate(0,12)">矿区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ecfd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -101.000000 -0.000000) translate(0,12)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed0370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -40.000000 -395.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed05b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -41.000000 -239.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed07f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -133.000000 -280.000000) translate(0,12)">07527</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed0a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -160.000000 -221.000000) translate(0,12)">07567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed0c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 148.000000 -394.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed0eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 -239.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed10f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 57.000000 -280.000000) translate(0,12)">07427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 295.000000 -397.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed1570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 205.000000 -280.000000) translate(0,12)">07327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 406.000000 -393.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed1c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 316.000000 -282.000000) translate(0,12)">07227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed1e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 234.000000 -755.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 160.000000 -743.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed2310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -515.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed2550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 47.000000 -666.000000) translate(0,12)">35kV2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed2550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 47.000000 -666.000000) translate(0,27)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed2550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 47.000000 -666.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed2550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 47.000000 -666.000000) translate(0,57)">YNd11</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-310" y="-1000"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-MD_GY.MD_GY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,-782 1244,-782 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24622" ObjectName="BS-MD_GY.MD_GY_3IM"/>
    <cge:TPSR_Ref TObjectID="24622"/></metadata>
   <polyline fill="none" opacity="0" points="20,-782 1244,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_GY.MD_GY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-450 1601,-450 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24623" ObjectName="BS-MD_GY.MD_GY_9IM"/>
    <cge:TPSR_Ref TObjectID="24623"/></metadata>
   <polyline fill="none" opacity="0" points="673,-450 1601,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_GY.MD_GY_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-217,-450 604,-450 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24624" ObjectName="BS-MD_GY.MD_GY_9IIM"/>
    <cge:TPSR_Ref TObjectID="24624"/></metadata>
   <polyline fill="none" opacity="0" points="-217,-450 604,-450 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 676.000000 -588.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 676.000000 -588.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-MD_GY.MD_GY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34830"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.000000 -571.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.000000 -571.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24677" ObjectName="TF-MD_GY.MD_GY_1T"/>
    <cge:TPSR_Ref TObjectID="24677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1061.000000 -11.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1061.000000 -11.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-MD_GY.MD_GY_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="61851"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.000000 -572.000000)" xlink:href="#transformer2:shape54_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.000000 -572.000000)" xlink:href="#transformer2:shape54_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="40857" ObjectName="TF-MD_GY.MD_GY_2T"/>
    <cge:TPSR_Ref TObjectID="40857"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -480.000000 -1053.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-255531" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -455.000000 -832.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255531" ObjectName="MD_GY:MD_GY_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-255530" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -457.000000 -874.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255530" ObjectName="MD_GY:MD_GY_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-255530" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -460.000000 -959.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255530" ObjectName="MD_GY:MD_GY_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-255530" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -458.000000 -916.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="255530" ObjectName="MD_GY:MD_GY_sumP"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="-469" y="-1112"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="-469" y="-1112"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-517" y="-1129"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-517" y="-1129"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="545" y="-396"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="545" y="-396"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="826" y="-396"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="826" y="-396"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1052" y="-396"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1052" y="-396"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1280" y="-396"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1280" y="-396"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1451" y="-396"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1451" y="-396"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="671" y="-901"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="671" y="-901"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="80" x="-566" y="-740"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="80" x="-566" y="-740"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="823" y="-638"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="823" y="-638"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="632" y="-545"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="632" y="-545"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-317" y="-1090"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-317" y="-1090"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-317" y="-1125"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-317" y="-1125"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-311" y="-1001"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-311" y="-1001"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="148" y="-394"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="148" y="-394"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="295" y="-397"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="295" y="-397"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="406" y="-393"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="406" y="-393"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-40" y="-395"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-40" y="-395"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="60" qtmmishow="hidden" width="136" x="47" y="-666"/>
    </a>
   <metadata/><rect fill="white" height="60" opacity="0" stroke="white" transform="" width="136" x="47" y="-666"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2880160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 978.000000 639.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2880b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 978.000000 654.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2881770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 731.000000 914.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2882410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 720.000000 899.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2882db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 745.000000 884.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2864cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 296.000000 875.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28655b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 296.000000 891.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2865950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 845.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2865b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 288.000000 830.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2865dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 813.000000) translate(0,12)">F(HZ):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2866580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 296.000000 860.500000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2866c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.000000 548.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2866e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.000000 564.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28670d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 518.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2867310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1366.000000 503.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2867550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 486.000000) translate(0,12)">F(HZ):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2867790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.000000 533.500000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2867ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -117.000000 546.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2867d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -117.000000 562.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2867f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -111.000000 516.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28681c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -125.000000 501.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2868400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -111.000000 484.000000) translate(0,12)">F(HZ):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2868640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -117.000000 531.500000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 685.672131 -11.354430)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286b7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -57.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286bdd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -41.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286c010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -23.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286c250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 7.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286c490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -187.000000 -9.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286c7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1019.000000 688.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286d350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1015.000000 706.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286d5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1001.000000 736.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286d810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.000000 721.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286db40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1008.000000 495.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286ddb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 513.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286dff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.000000 543.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_286e230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 979.000000 528.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 914.672131 -11.354430)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b7fdb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -57.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b802a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -41.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b804e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -23.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b80720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 7.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b80960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -187.000000 -9.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 1144.672131 -13.354430)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b80c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -57.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b80f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -41.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b81180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -23.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b813c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 7.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b81600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -187.000000 -9.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 1372.672131 -11.354430)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b81930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -57.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b81be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -41.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b81e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -23.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b82060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 7.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b822a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -187.000000 -9.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b825d0" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 1425.950820 -57.759494) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b82840" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 1425.950820 -42.569620) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b82a80" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 1425.950820 -25.481013) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b82cc0" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 1403.000000 -12.189873) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 393.672131 -11.354430)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed40d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -57.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed4360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -41.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed45a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -23.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed47e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 7.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed4a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -187.000000 -9.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 245.672131 -11.354430)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed4d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -57.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed5000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -41.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed5240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -23.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed5480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 7.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed56c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -187.000000 -9.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 526.672131 -11.354430)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed59f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -57.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed5ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -41.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed5ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -23.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed6120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 7.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed6360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -187.000000 -9.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed6690" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 -98.049180 -76.759494) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed6900" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 -98.049180 -61.569620) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed6b40" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 -98.049180 -44.481013) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed6d80" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 -121.000000 -31.189873) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 446.672131 -734.354430)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed76c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -57.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed7950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -41.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed7b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -23.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed7dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 7.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed8010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -187.000000 -9.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.918033 -0.000000 0.000000 -0.949367 448.672131 -556.354430)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed8340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -57.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed85f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -41.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed8830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.000000 -23.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed8a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 7.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ed8cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -187.000000 -9.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eda1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 303.000000 615.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eda410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 303.000000 630.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-MD_GY.MD_GY_cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1423.000000 -76.000000)" xlink:href="#capacitor:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40764" ObjectName="CB-MD_GY.MD_GY_cb1"/>
    <cge:TPSR_Ref TObjectID="40764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-MD_GY.MD_GY_cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -85.000000 -27.000000)" xlink:href="#capacitor:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40858" ObjectName="CB-MD_GY.MD_GY_cb2"/>
    <cge:TPSR_Ref TObjectID="40858"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-136165" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136165" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24658"/>
     <cge:Term_Ref ObjectID="34790"/>
    <cge:TPSR_Ref TObjectID="24658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-136162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24658"/>
     <cge:Term_Ref ObjectID="34790"/>
    <cge:TPSR_Ref TObjectID="24658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="1" id="ME-136163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24658"/>
     <cge:Term_Ref ObjectID="34790"/>
    <cge:TPSR_Ref TObjectID="24658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-136164" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 14.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24658"/>
     <cge:Term_Ref ObjectID="34790"/>
    <cge:TPSR_Ref TObjectID="24658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-136136" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1055.000000 -653.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24677"/>
     <cge:Term_Ref ObjectID="34831"/>
    <cge:TPSR_Ref TObjectID="24677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-136137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1055.000000 -653.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24677"/>
     <cge:Term_Ref ObjectID="34831"/>
    <cge:TPSR_Ref TObjectID="24677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 785.000000 -915.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24626"/>
     <cge:Term_Ref ObjectID="34726"/>
    <cge:TPSR_Ref TObjectID="24626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 785.000000 -915.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24626"/>
     <cge:Term_Ref ObjectID="34726"/>
    <cge:TPSR_Ref TObjectID="24626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 785.000000 -915.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24626"/>
     <cge:Term_Ref ObjectID="34726"/>
    <cge:TPSR_Ref TObjectID="24626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136127" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -738.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24632"/>
     <cge:Term_Ref ObjectID="34738"/>
    <cge:TPSR_Ref TObjectID="24632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136128" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -738.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24632"/>
     <cge:Term_Ref ObjectID="34738"/>
    <cge:TPSR_Ref TObjectID="24632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136124" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -738.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136124" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24632"/>
     <cge:Term_Ref ObjectID="34738"/>
    <cge:TPSR_Ref TObjectID="24632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-136129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -738.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24632"/>
     <cge:Term_Ref ObjectID="34738"/>
    <cge:TPSR_Ref TObjectID="24632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136133" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -542.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24635"/>
     <cge:Term_Ref ObjectID="34744"/>
    <cge:TPSR_Ref TObjectID="24635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136134" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -542.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136134" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24635"/>
     <cge:Term_Ref ObjectID="34744"/>
    <cge:TPSR_Ref TObjectID="24635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136130" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -542.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24635"/>
     <cge:Term_Ref ObjectID="34744"/>
    <cge:TPSR_Ref TObjectID="24635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-136135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -542.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24635"/>
     <cge:Term_Ref ObjectID="34744"/>
    <cge:TPSR_Ref TObjectID="24635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-136141" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24638"/>
     <cge:Term_Ref ObjectID="34750"/>
    <cge:TPSR_Ref TObjectID="24638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-136142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24638"/>
     <cge:Term_Ref ObjectID="34750"/>
    <cge:TPSR_Ref TObjectID="24638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-136138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24638"/>
     <cge:Term_Ref ObjectID="34750"/>
    <cge:TPSR_Ref TObjectID="24638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="1" id="ME-136139" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -18.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136139" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24638"/>
     <cge:Term_Ref ObjectID="34750"/>
    <cge:TPSR_Ref TObjectID="24638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-136140" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -18.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136140" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24638"/>
     <cge:Term_Ref ObjectID="34750"/>
    <cge:TPSR_Ref TObjectID="24638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-136147" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 801.000000 -18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24643"/>
     <cge:Term_Ref ObjectID="34760"/>
    <cge:TPSR_Ref TObjectID="24643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-136148" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 801.000000 -18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24643"/>
     <cge:Term_Ref ObjectID="34760"/>
    <cge:TPSR_Ref TObjectID="24643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-136144" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 801.000000 -18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24643"/>
     <cge:Term_Ref ObjectID="34760"/>
    <cge:TPSR_Ref TObjectID="24643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="1" id="ME-136145" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 801.000000 -18.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24643"/>
     <cge:Term_Ref ObjectID="34760"/>
    <cge:TPSR_Ref TObjectID="24643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-136146" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 801.000000 -18.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24643"/>
     <cge:Term_Ref ObjectID="34760"/>
    <cge:TPSR_Ref TObjectID="24643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-136153" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1031.000000 -18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24648"/>
     <cge:Term_Ref ObjectID="34770"/>
    <cge:TPSR_Ref TObjectID="24648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-136154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1031.000000 -18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24648"/>
     <cge:Term_Ref ObjectID="34770"/>
    <cge:TPSR_Ref TObjectID="24648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-136150" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1031.000000 -18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24648"/>
     <cge:Term_Ref ObjectID="34770"/>
    <cge:TPSR_Ref TObjectID="24648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="1" id="ME-136151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1031.000000 -18.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24648"/>
     <cge:Term_Ref ObjectID="34770"/>
    <cge:TPSR_Ref TObjectID="24648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-136152" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1031.000000 -18.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24648"/>
     <cge:Term_Ref ObjectID="34770"/>
    <cge:TPSR_Ref TObjectID="24648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-136159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24653"/>
     <cge:Term_Ref ObjectID="34780"/>
    <cge:TPSR_Ref TObjectID="24653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-136160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24653"/>
     <cge:Term_Ref ObjectID="34780"/>
    <cge:TPSR_Ref TObjectID="24653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-136156" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24653"/>
     <cge:Term_Ref ObjectID="34780"/>
    <cge:TPSR_Ref TObjectID="24653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="1" id="ME-136157" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -18.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24653"/>
     <cge:Term_Ref ObjectID="34780"/>
    <cge:TPSR_Ref TObjectID="24653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-136158" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -18.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136158" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24653"/>
     <cge:Term_Ref ObjectID="34780"/>
    <cge:TPSR_Ref TObjectID="24653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-136100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -888.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24622"/>
     <cge:Term_Ref ObjectID="34721"/>
    <cge:TPSR_Ref TObjectID="24622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-136101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -888.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24622"/>
     <cge:Term_Ref ObjectID="34721"/>
    <cge:TPSR_Ref TObjectID="24622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-136102" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -888.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24622"/>
     <cge:Term_Ref ObjectID="34721"/>
    <cge:TPSR_Ref TObjectID="24622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-136106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -888.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24622"/>
     <cge:Term_Ref ObjectID="34721"/>
    <cge:TPSR_Ref TObjectID="24622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-136103" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -888.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136103" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24622"/>
     <cge:Term_Ref ObjectID="34721"/>
    <cge:TPSR_Ref TObjectID="24622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-136107" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -888.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136107" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24622"/>
     <cge:Term_Ref ObjectID="34721"/>
    <cge:TPSR_Ref TObjectID="24622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-136116" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -556.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136116" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24624"/>
     <cge:Term_Ref ObjectID="34723"/>
    <cge:TPSR_Ref TObjectID="24624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-136117" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -556.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136117" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24624"/>
     <cge:Term_Ref ObjectID="34723"/>
    <cge:TPSR_Ref TObjectID="24624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-136118" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -556.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24624"/>
     <cge:Term_Ref ObjectID="34723"/>
    <cge:TPSR_Ref TObjectID="24624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-136122" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -556.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24624"/>
     <cge:Term_Ref ObjectID="34723"/>
    <cge:TPSR_Ref TObjectID="24624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-136119" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -556.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24624"/>
     <cge:Term_Ref ObjectID="34723"/>
    <cge:TPSR_Ref TObjectID="24624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-136123" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -556.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24624"/>
     <cge:Term_Ref ObjectID="34723"/>
    <cge:TPSR_Ref TObjectID="24624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-136108" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -559.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136108" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24623"/>
     <cge:Term_Ref ObjectID="34722"/>
    <cge:TPSR_Ref TObjectID="24623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-136109" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -559.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136109" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24623"/>
     <cge:Term_Ref ObjectID="34722"/>
    <cge:TPSR_Ref TObjectID="24623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-136110" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -559.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24623"/>
     <cge:Term_Ref ObjectID="34722"/>
    <cge:TPSR_Ref TObjectID="24623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-136114" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -559.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136114" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24623"/>
     <cge:Term_Ref ObjectID="34722"/>
    <cge:TPSR_Ref TObjectID="24623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-136111" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -559.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136111" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24623"/>
     <cge:Term_Ref ObjectID="34722"/>
    <cge:TPSR_Ref TObjectID="24623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-136115" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -559.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24623"/>
     <cge:Term_Ref ObjectID="34722"/>
    <cge:TPSR_Ref TObjectID="24623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-242723" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40846"/>
     <cge:Term_Ref ObjectID="61827"/>
    <cge:TPSR_Ref TObjectID="40846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-242724" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242724" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40846"/>
     <cge:Term_Ref ObjectID="61827"/>
    <cge:TPSR_Ref TObjectID="40846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-242720" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242720" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40846"/>
     <cge:Term_Ref ObjectID="61827"/>
    <cge:TPSR_Ref TObjectID="40846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-242721" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -18.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242721" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40846"/>
     <cge:Term_Ref ObjectID="61827"/>
    <cge:TPSR_Ref TObjectID="40846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-242722" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -18.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242722" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40846"/>
     <cge:Term_Ref ObjectID="61827"/>
    <cge:TPSR_Ref TObjectID="40846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-242717" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 -18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242717" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40842"/>
     <cge:Term_Ref ObjectID="61819"/>
    <cge:TPSR_Ref TObjectID="40842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-242718" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 -18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242718" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40842"/>
     <cge:Term_Ref ObjectID="61819"/>
    <cge:TPSR_Ref TObjectID="40842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-242714" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 -18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242714" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40842"/>
     <cge:Term_Ref ObjectID="61819"/>
    <cge:TPSR_Ref TObjectID="40842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-242715" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 -18.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242715" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40842"/>
     <cge:Term_Ref ObjectID="61819"/>
    <cge:TPSR_Ref TObjectID="40842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-242716" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 -18.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242716" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40842"/>
     <cge:Term_Ref ObjectID="61819"/>
    <cge:TPSR_Ref TObjectID="40842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-242711" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 410.000000 -18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242711" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40838"/>
     <cge:Term_Ref ObjectID="61811"/>
    <cge:TPSR_Ref TObjectID="40838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-242712" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 410.000000 -18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242712" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40838"/>
     <cge:Term_Ref ObjectID="61811"/>
    <cge:TPSR_Ref TObjectID="40838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-242708" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 410.000000 -18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242708" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40838"/>
     <cge:Term_Ref ObjectID="61811"/>
    <cge:TPSR_Ref TObjectID="40838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-242709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 410.000000 -18.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40838"/>
     <cge:Term_Ref ObjectID="61811"/>
    <cge:TPSR_Ref TObjectID="40838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-242710" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 410.000000 -18.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40838"/>
     <cge:Term_Ref ObjectID="61811"/>
    <cge:TPSR_Ref TObjectID="40838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-242689" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -737.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242689" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40833"/>
     <cge:Term_Ref ObjectID="61801"/>
    <cge:TPSR_Ref TObjectID="40833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-242690" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -737.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242690" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40833"/>
     <cge:Term_Ref ObjectID="61801"/>
    <cge:TPSR_Ref TObjectID="40833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-242686" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -737.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40833"/>
     <cge:Term_Ref ObjectID="61801"/>
    <cge:TPSR_Ref TObjectID="40833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-242687" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -737.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40833"/>
     <cge:Term_Ref ObjectID="61801"/>
    <cge:TPSR_Ref TObjectID="40833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-242688" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -737.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242688" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40833"/>
     <cge:Term_Ref ObjectID="61801"/>
    <cge:TPSR_Ref TObjectID="40833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-242702" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -562.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40832"/>
     <cge:Term_Ref ObjectID="61799"/>
    <cge:TPSR_Ref TObjectID="40832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-242703" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -562.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242703" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40832"/>
     <cge:Term_Ref ObjectID="61799"/>
    <cge:TPSR_Ref TObjectID="40832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-242699" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -562.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242699" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40832"/>
     <cge:Term_Ref ObjectID="61799"/>
    <cge:TPSR_Ref TObjectID="40832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-242700" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -562.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40832"/>
     <cge:Term_Ref ObjectID="61799"/>
    <cge:TPSR_Ref TObjectID="40832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-242701" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -562.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40832"/>
     <cge:Term_Ref ObjectID="61799"/>
    <cge:TPSR_Ref TObjectID="40832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-242729" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -58.000000 34.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242729" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40851"/>
     <cge:Term_Ref ObjectID="61837"/>
    <cge:TPSR_Ref TObjectID="40851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-242726" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -58.000000 34.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242726" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40851"/>
     <cge:Term_Ref ObjectID="61837"/>
    <cge:TPSR_Ref TObjectID="40851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-242727" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -58.000000 34.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242727" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40851"/>
     <cge:Term_Ref ObjectID="61837"/>
    <cge:TPSR_Ref TObjectID="40851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-242728" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -58.000000 34.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242728" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40851"/>
     <cge:Term_Ref ObjectID="61837"/>
    <cge:TPSR_Ref TObjectID="40851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-242707" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 371.000000 -628.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242707" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40857"/>
     <cge:Term_Ref ObjectID="61852"/>
    <cge:TPSR_Ref TObjectID="40857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-242706" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 371.000000 -628.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="242706" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="40857"/>
     <cge:Term_Ref ObjectID="61852"/>
    <cge:TPSR_Ref TObjectID="40857"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-136240">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 917.408592 -726.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24633" ObjectName="SW-MD_GY.MD_GY_3011SW"/>
     <cge:Meas_Ref ObjectId="136240"/>
    <cge:TPSR_Ref TObjectID="24633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 915.408592 -542.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24636" ObjectName="SW-MD_GY.MD_GY_001XC"/>
     <cge:Meas_Ref ObjectId="136263"/>
    <cge:TPSR_Ref TObjectID="24636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 915.408592 -465.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24637" ObjectName="SW-MD_GY.MD_GY_001XC1"/>
     <cge:Meas_Ref ObjectId="136263"/>
    <cge:TPSR_Ref TObjectID="24637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.986842 -0.000000 0.000000 -0.979839 714.501546 -487.084677)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24668" ObjectName="SW-MD_GY.MD_GY_0121XC"/>
     <cge:Meas_Ref ObjectId="136451"/>
    <cge:TPSR_Ref TObjectID="24668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.038781 -0.000000 0.000000 -0.979839 713.975311 -455.540323)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24669" ObjectName="SW-MD_GY.MD_GY_0121XC1"/>
     <cge:Meas_Ref ObjectId="136451"/>
    <cge:TPSR_Ref TObjectID="24669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136207">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 649.000000 -932.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24629" ObjectName="SW-MD_GY.MD_GY_3416SW"/>
     <cge:Meas_Ref ObjectId="136207"/>
    <cge:TPSR_Ref TObjectID="24629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136205">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 649.000000 -804.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24627" ObjectName="SW-MD_GY.MD_GY_3411SW"/>
     <cge:Meas_Ref ObjectId="136205"/>
    <cge:TPSR_Ref TObjectID="24627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136209">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 -989.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24631" ObjectName="SW-MD_GY.MD_GY_34167SW"/>
     <cge:Meas_Ref ObjectId="136209"/>
    <cge:TPSR_Ref TObjectID="24631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136206">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 -858.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24628" ObjectName="SW-MD_GY.MD_GY_34117SW"/>
     <cge:Meas_Ref ObjectId="136206"/>
    <cge:TPSR_Ref TObjectID="24628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136208">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 -920.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24630" ObjectName="SW-MD_GY.MD_GY_34160SW"/>
     <cge:Meas_Ref ObjectId="136208"/>
    <cge:TPSR_Ref TObjectID="24630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 688.000000 -1015.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136241">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.000000 -713.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24634" ObjectName="SW-MD_GY.MD_GY_30117SW"/>
     <cge:Meas_Ref ObjectId="136241"/>
    <cge:TPSR_Ref TObjectID="24634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1087.500000 -115.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242828">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.264739 -330.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40840" ObjectName="SW-MD_GY.MD_GY_072XC1"/>
     <cge:Meas_Ref ObjectId="242828"/>
    <cge:TPSR_Ref TObjectID="40840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242828">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.264739 -409.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40839" ObjectName="SW-MD_GY.MD_GY_072XC"/>
     <cge:Meas_Ref ObjectId="242828"/>
    <cge:TPSR_Ref TObjectID="40839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136317">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 521.764739 -201.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24641" ObjectName="SW-MD_GY.MD_GY_0716SW"/>
     <cge:Meas_Ref ObjectId="136317"/>
    <cge:TPSR_Ref TObjectID="24641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136318">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 472.000000 -314.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24642" ObjectName="SW-MD_GY.MD_GY_07127SW"/>
     <cge:Meas_Ref ObjectId="136318"/>
    <cge:TPSR_Ref TObjectID="24642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136316">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 520.764739 -330.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24640" ObjectName="SW-MD_GY.MD_GY_071XC1"/>
     <cge:Meas_Ref ObjectId="136316"/>
    <cge:TPSR_Ref TObjectID="24640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136316">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 520.764739 -411.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24639" ObjectName="SW-MD_GY.MD_GY_071XC"/>
     <cge:Meas_Ref ObjectId="136316"/>
    <cge:TPSR_Ref TObjectID="24639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136343">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.764739 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24646" ObjectName="SW-MD_GY.MD_GY_0616SW"/>
     <cge:Meas_Ref ObjectId="136343"/>
    <cge:TPSR_Ref TObjectID="24646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136344">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 758.000000 -313.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24647" ObjectName="SW-MD_GY.MD_GY_06127SW"/>
     <cge:Meas_Ref ObjectId="136344"/>
    <cge:TPSR_Ref TObjectID="24647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136342">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 806.764739 -329.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24645" ObjectName="SW-MD_GY.MD_GY_061XC1"/>
     <cge:Meas_Ref ObjectId="136342"/>
    <cge:TPSR_Ref TObjectID="24645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136342">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 806.764739 -410.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24644" ObjectName="SW-MD_GY.MD_GY_061XC"/>
     <cge:Meas_Ref ObjectId="136342"/>
    <cge:TPSR_Ref TObjectID="24644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136370">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1034.098072 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24651" ObjectName="SW-MD_GY.MD_GY_0626SW"/>
     <cge:Meas_Ref ObjectId="136370"/>
    <cge:TPSR_Ref TObjectID="24651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136371">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 -313.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24652" ObjectName="SW-MD_GY.MD_GY_06227SW"/>
     <cge:Meas_Ref ObjectId="136371"/>
    <cge:TPSR_Ref TObjectID="24652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136369">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.098072 -329.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24650" ObjectName="SW-MD_GY.MD_GY_062XC1"/>
     <cge:Meas_Ref ObjectId="136369"/>
    <cge:TPSR_Ref TObjectID="24650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136369">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.098072 -410.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24649" ObjectName="SW-MD_GY.MD_GY_062XC"/>
     <cge:Meas_Ref ObjectId="136369"/>
    <cge:TPSR_Ref TObjectID="24649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136397">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1211.000000 -312.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24657" ObjectName="SW-MD_GY.MD_GY_06317SW"/>
     <cge:Meas_Ref ObjectId="136397"/>
    <cge:TPSR_Ref TObjectID="24657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136395">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1260.431406 -328.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24655" ObjectName="SW-MD_GY.MD_GY_063XC1"/>
     <cge:Meas_Ref ObjectId="136395"/>
    <cge:TPSR_Ref TObjectID="24655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136395">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1260.431406 -409.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24654" ObjectName="SW-MD_GY.MD_GY_063XC"/>
     <cge:Meas_Ref ObjectId="136395"/>
    <cge:TPSR_Ref TObjectID="24654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136422">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.873333 -0.000000 0.000000 -0.778870 1435.098965 -204.531941)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24661" ObjectName="SW-MD_GY.MD_GY_0646SW"/>
     <cge:Meas_Ref ObjectId="136422"/>
    <cge:TPSR_Ref TObjectID="24661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136421">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.873333 -0.000000 0.000000 -0.778870 1433.225632 -339.613022)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24660" ObjectName="SW-MD_GY.MD_GY_064XC1"/>
     <cge:Meas_Ref ObjectId="136421"/>
    <cge:TPSR_Ref TObjectID="24660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136425">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.873333 0.000000 0.000000 -0.778870 1434.210000 -34.479115)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24664" ObjectName="SW-MD_GY.MD_GY_06400SW"/>
     <cge:Meas_Ref ObjectId="136425"/>
    <cge:TPSR_Ref TObjectID="24664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136421">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.873333 -0.000000 0.000000 -0.778870 1434.225632 -407.912776)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24659" ObjectName="SW-MD_GY.MD_GY_064XC"/>
     <cge:Meas_Ref ObjectId="136421"/>
    <cge:TPSR_Ref TObjectID="24659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136478">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.991837 472.477964 -460.016327)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24675" ObjectName="SW-MD_GY.MD_GY_0902XC"/>
     <cge:Meas_Ref ObjectId="136478"/>
    <cge:TPSR_Ref TObjectID="24675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136474">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.991837 1213.477964 -456.016327)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24674" ObjectName="SW-MD_GY.MD_GY_0901XC1"/>
     <cge:Meas_Ref ObjectId="136474"/>
    <cge:TPSR_Ref TObjectID="24674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136450">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 666.500000 -531.500000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24667" ObjectName="SW-MD_GY.MD_GY_012XC1"/>
     <cge:Meas_Ref ObjectId="136450"/>
    <cge:TPSR_Ref TObjectID="24667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136450">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 619.500000 -511.500000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24666" ObjectName="SW-MD_GY.MD_GY_012XC"/>
     <cge:Meas_Ref ObjectId="136450"/>
    <cge:TPSR_Ref TObjectID="24666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136469">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 529.000000 -756.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24671" ObjectName="SW-MD_GY.MD_GY_39010SW"/>
     <cge:Meas_Ref ObjectId="136469"/>
    <cge:TPSR_Ref TObjectID="24671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136468">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 -706.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24670" ObjectName="SW-MD_GY.MD_GY_3901SW"/>
     <cge:Meas_Ref ObjectId="136468"/>
    <cge:TPSR_Ref TObjectID="24670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 702.500000 -702.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136478">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 471.764739 -496.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24676" ObjectName="SW-MD_GY.MD_GY_0902XC1"/>
     <cge:Meas_Ref ObjectId="136478"/>
    <cge:TPSR_Ref TObjectID="24676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136474">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1213.431406 -490.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24673" ObjectName="SW-MD_GY.MD_GY_0901XC"/>
     <cge:Meas_Ref ObjectId="136474"/>
    <cge:TPSR_Ref TObjectID="24673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136423">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1377.000000 -330.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24662" ObjectName="SW-MD_GY.MD_GY_06417SW"/>
     <cge:Meas_Ref ObjectId="136423"/>
    <cge:TPSR_Ref TObjectID="24662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136470">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 517.000000 -687.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24672" ObjectName="SW-MD_GY.MD_GY_39017SW"/>
     <cge:Meas_Ref ObjectId="136470"/>
    <cge:TPSR_Ref TObjectID="24672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136424">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1377.000000 -195.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24663" ObjectName="SW-MD_GY.MD_GY_06467SW"/>
     <cge:Meas_Ref ObjectId="136424"/>
    <cge:TPSR_Ref TObjectID="24663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242733">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 218.408592 -725.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40836" ObjectName="SW-MD_GY.MD_GY_3021SW"/>
     <cge:Meas_Ref ObjectId="242733"/>
    <cge:TPSR_Ref TObjectID="40836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242732">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 217.408592 -527.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40834" ObjectName="SW-MD_GY.MD_GY_002XC"/>
     <cge:Meas_Ref ObjectId="242732"/>
    <cge:TPSR_Ref TObjectID="40834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242732">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 217.408592 -456.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40835" ObjectName="SW-MD_GY.MD_GY_002XC1"/>
     <cge:Meas_Ref ObjectId="242732"/>
    <cge:TPSR_Ref TObjectID="40835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242734">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 157.000000 -712.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40837" ObjectName="SW-MD_GY.MD_GY_30217SW"/>
     <cge:Meas_Ref ObjectId="242734"/>
    <cge:TPSR_Ref TObjectID="40837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242829">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.764739 -258.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40841" ObjectName="SW-MD_GY.MD_GY_07227SW"/>
     <cge:Meas_Ref ObjectId="242829"/>
    <cge:TPSR_Ref TObjectID="40841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242857">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.264739 -334.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40844" ObjectName="SW-MD_GY.MD_GY_073XC1"/>
     <cge:Meas_Ref ObjectId="242857"/>
    <cge:TPSR_Ref TObjectID="40844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242857">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.264739 -413.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40843" ObjectName="SW-MD_GY.MD_GY_073XC"/>
     <cge:Meas_Ref ObjectId="242857"/>
    <cge:TPSR_Ref TObjectID="40843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242858">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 241.764739 -262.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40845" ObjectName="SW-MD_GY.MD_GY_07327SW"/>
     <cge:Meas_Ref ObjectId="242858"/>
    <cge:TPSR_Ref TObjectID="40845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242886">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 129.264739 -331.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40848" ObjectName="SW-MD_GY.MD_GY_074XC1"/>
     <cge:Meas_Ref ObjectId="242886"/>
    <cge:TPSR_Ref TObjectID="40848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242886">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 129.264739 -410.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40847" ObjectName="SW-MD_GY.MD_GY_074XC"/>
     <cge:Meas_Ref ObjectId="242886"/>
    <cge:TPSR_Ref TObjectID="40847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242888">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 94.764739 -259.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40850" ObjectName="SW-MD_GY.MD_GY_07427SW"/>
     <cge:Meas_Ref ObjectId="242888"/>
    <cge:TPSR_Ref TObjectID="40850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -57.735261 -332.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40853" ObjectName="SW-MD_GY.MD_GY_075XC1"/>
     <cge:Meas_Ref ObjectId="242916"/>
    <cge:TPSR_Ref TObjectID="40853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -57.735261 -411.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40852" ObjectName="SW-MD_GY.MD_GY_075XC"/>
     <cge:Meas_Ref ObjectId="242916"/>
    <cge:TPSR_Ref TObjectID="40852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242918">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -92.235261 -260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40855" ObjectName="SW-MD_GY.MD_GY_07527SW"/>
     <cge:Meas_Ref ObjectId="242918"/>
    <cge:TPSR_Ref TObjectID="40855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242887">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 129.764739 -209.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40849" ObjectName="SW-MD_GY.MD_GY_0746SW"/>
     <cge:Meas_Ref ObjectId="242887"/>
    <cge:TPSR_Ref TObjectID="40849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242917">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -57.235261 -209.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40854" ObjectName="SW-MD_GY.MD_GY_0756SW"/>
     <cge:Meas_Ref ObjectId="242917"/>
    <cge:TPSR_Ref TObjectID="40854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-242919">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 -102.000000 -158.000000)" xlink:href="#switch2:shape24_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40856" ObjectName="SW-MD_GY.MD_GY_07567SW"/>
     <cge:Meas_Ref ObjectId="242919"/>
    <cge:TPSR_Ref TObjectID="40856"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="-469" y="-1112"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-517" y="-1129"/></g>
   <g href="35kV古岩变MD_GY_071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="545" y="-396"/></g>
   <g href="35kV古岩变MD_GY_061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="826" y="-396"/></g>
   <g href="35kV古岩变MD_GY_062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1052" y="-396"/></g>
   <g href="35kV古岩变MD_GY_063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1280" y="-396"/></g>
   <g href="35kV古岩变MD_GY_064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1451" y="-396"/></g>
   <g href="35kV古岩变MD_GY_341间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="671" y="-901"/></g>
   <g href="35kV古岩变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="80" x="-566" y="-740"/></g>
   <g href="35kV古岩变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="823" y="-638"/></g>
   <g href="35kV古岩变MD_GY_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="632" y="-545"/></g>
   <g href="cx_配调_配网接线图35_牟定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-317" y="-1090"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-317" y="-1125"/></g>
   <g href="AVC古岩站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-311" y="-1001"/></g>
   <g href="35kV古岩变MD_GY_074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="148" y="-394"/></g>
   <g href="35kV古岩变MD_GY_073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="295" y="-397"/></g>
   <g href="35kV古岩变MD_GY_072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="406" y="-393"/></g>
   <g href="35kV古岩变MD_GY_075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-40" y="-395"/></g>
   <g href="35kV古岩变35kV2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="60" qtmmishow="hidden" width="136" x="47" y="-666"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29e2810">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 621.000000 -1097.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29a3ab0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 302.264739 -244.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2926690">
    <use class="BV-10KV" transform="matrix(0.000000 -0.516854 -1.000000 -0.000000 539.764739 -258.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2999860">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 557.764739 -120.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28c89b0">
    <use class="BV-10KV" transform="matrix(0.000000 -0.516854 -1.000000 -0.000000 825.764739 -257.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291a640">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 843.764739 -119.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2888970">
    <use class="BV-10KV" transform="matrix(0.000000 -0.516854 -1.000000 -0.000000 1052.098072 -257.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29917b0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1000.098072 -115.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_297da80">
    <use class="BV-10KV" transform="matrix(0.000000 -0.778870 -0.873333 -0.000000 1450.818965 -259.726044)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a44ec0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 606.000000 -684.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a482e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 -644.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a4cae0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.000000 -536.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a4d4a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 -533.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a520a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.000000 -531.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a52cd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1173.000000 -525.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e80390">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 134.000000 -555.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e8db80">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 185.264739 -252.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e9cf20">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 33.264739 -246.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eaca60">
    <use class="BV-10KV" transform="matrix(0.000000 -0.516854 -1.000000 -0.000000 147.764739 -263.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ead740">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -163.735261 -243.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eba730">
    <use class="BV-10KV" transform="matrix(0.000000 -0.516854 -1.000000 -0.000000 -39.235261 -264.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ebd850">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 100.264739 -136.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eceb60">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -11.735261 -41.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="MD_GY"/>
</svg>