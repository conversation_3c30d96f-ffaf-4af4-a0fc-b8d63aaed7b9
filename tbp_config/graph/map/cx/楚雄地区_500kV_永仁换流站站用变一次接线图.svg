<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-183" aopId="1835010" id="thSvg" product="E8000V2" version="1.0" viewBox="3330 -1282 2399 1652">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="currentTransformer:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="25" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="42" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="51" x2="51" y1="43" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="55" x2="55" y1="34" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="58" x2="58" y1="35" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="35" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="35" x2="35" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="42" x2="42" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="16" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="16" x2="16" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="23" x2="23" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="36" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="7" y2="4"/>
    <circle cx="41" cy="17" r="7.5" stroke-width="1"/>
    <circle cx="17" cy="17" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="26" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="20" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="26" y1="9" y2="5"/>
    <circle cx="35" cy="7" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="42" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="42" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="30" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="30" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="18" y2="15"/>
    <circle cx="29" cy="17" r="7.5" stroke-width="1"/>
    <circle cx="23" cy="7" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="17" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="17" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="18" y2="15"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape53_0">
    <circle cx="13" cy="45" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="41" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="45" x2="29" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="39" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="37" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="25" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="12,44 8,53 18,53 12,44 "/>
   </symbol>
   <symbol id="transformer2:shape53_1">
    <ellipse cx="13" cy="26" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="21" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="25" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="29" y2="25"/>
   </symbol>
   <symbol id="voltageTransformer:shape104">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="36" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="38" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="37" y1="43" y2="43"/>
    <ellipse cx="8" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="10" y2="14"/>
    <ellipse cx="19" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <ellipse cx="19" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="44" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="28" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="39" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="12" y2="18"/>
    <rect height="13" stroke-width="1" width="5" x="37" y="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="voltageTransformer:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="32" y1="33" y2="33"/>
    <ellipse cx="22" cy="14" rx="7.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="30" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="46" y1="32" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="31" y2="35"/>
    <ellipse cx="12" cy="20" rx="7.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="21" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="27" y1="16" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="21" y1="16" y2="12"/>
    <ellipse cx="12" cy="8" rx="7.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="18" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="16" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="16" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="29" y1="38" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="32" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="33" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="33" y2="33"/>
    <rect height="5" stroke-width="1" width="13" x="19" y="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape107">
    <ellipse cx="14" cy="25" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="30" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="21" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="28" y1="28" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="8" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="8" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="37" y2="35"/>
    <ellipse cx="8" cy="35" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="25" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="23" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="21" y2="27"/>
    <ellipse cx="20" cy="35" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="33" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="36" y1="6" y2="6"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2671af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26724d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2672eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26740c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26753b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2676050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2676bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26775f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2677e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2678800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2678800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_267a600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_267a600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_267b610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_267d2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_267deb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_267e860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_267f170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2680950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2681150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2681840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2682260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2683440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2683dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26848b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689b70" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_268a800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2686600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2687c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2688530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_26965c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_268c610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1662" width="2409" x="3325" y="-1287"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3c8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3600.000000 1106.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3da80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 1136.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3e610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3575.000000 1121.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d41030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3427.000000 617.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d41ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 617.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d42890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4564.000000 617.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3761,-362 3756,-372 3766,-372 3761,-362 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3761,-350 3756,-340 3766,-340 3761,-350 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3633,-362 3628,-372 3638,-372 3633,-362 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3633,-350 3628,-340 3638,-340 3633,-350 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3505,-362 3500,-372 3510,-372 3505,-362 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3505,-350 3500,-340 3510,-340 3505,-350 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4737,-364 4732,-374 4742,-374 4737,-364 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4737,-352 4732,-342 4742,-342 4737,-352 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4865,-364 4860,-374 4870,-374 4865,-364 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4865,-352 4860,-342 4870,-342 4865,-352 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4993,-364 4988,-374 4998,-374 4993,-364 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4993,-352 4988,-342 4998,-342 4993,-352 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3683,-930 3678,-940 3688,-940 3683,-930 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3683,-918 3678,-908 3688,-908 3683,-918 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4228,-777 4223,-787 4233,-787 4228,-777 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4228,-765 4223,-755 4233,-755 4228,-765 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4822,-930 4817,-940 4827,-940 4822,-930 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4822,-918 4817,-908 4827,-908 4822,-918 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3889,-362 3884,-372 3894,-372 3889,-362 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3889,-350 3884,-340 3894,-340 3889,-350 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3889,-234 3884,-244 3894,-244 3889,-234 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3889,-222 3884,-212 3894,-212 3889,-222 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5121,-364 5116,-374 5126,-374 5121,-364 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5121,-352 5116,-342 5126,-342 5121,-352 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5121,-236 5116,-246 5126,-246 5121,-236 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5121,-224 5116,-214 5126,-214 5121,-224 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-127256">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3495.000000 -447.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23411" ObjectName="SW-CX_YRH.CX_YRH_051BK"/>
     <cge:Meas_Ref ObjectId="127256"/>
    <cge:TPSR_Ref TObjectID="23411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127257">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 -446.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23412" ObjectName="SW-CX_YRH.CX_YRH_052BK"/>
     <cge:Meas_Ref ObjectId="127257"/>
    <cge:TPSR_Ref TObjectID="23412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127258">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 -446.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23413" ObjectName="SW-CX_YRH.CX_YRH_053BK"/>
     <cge:Meas_Ref ObjectId="127258"/>
    <cge:TPSR_Ref TObjectID="23413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127259">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 -446.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23414" ObjectName="SW-CX_YRH.CX_YRH_054BK"/>
     <cge:Meas_Ref ObjectId="127259"/>
    <cge:TPSR_Ref TObjectID="23414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127277">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.925566 -446.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23432" ObjectName="SW-CX_YRH.CX_YRH_013BK"/>
     <cge:Meas_Ref ObjectId="127277"/>
    <cge:TPSR_Ref TObjectID="23432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127279">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4534.925566 -446.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23434" ObjectName="SW-CX_YRH.CX_YRH_023BK"/>
     <cge:Meas_Ref ObjectId="127279"/>
    <cge:TPSR_Ref TObjectID="23434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127278">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.925566 -447.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23433" ObjectName="SW-CX_YRH.CX_YRH_012BK"/>
     <cge:Meas_Ref ObjectId="127278"/>
    <cge:TPSR_Ref TObjectID="23433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127267">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.000000 -448.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23422" ObjectName="SW-CX_YRH.CX_YRH_061BK"/>
     <cge:Meas_Ref ObjectId="127267"/>
    <cge:TPSR_Ref TObjectID="23422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127268">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4855.000000 -448.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23423" ObjectName="SW-CX_YRH.CX_YRH_062BK"/>
     <cge:Meas_Ref ObjectId="127268"/>
    <cge:TPSR_Ref TObjectID="23423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -448.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23424" ObjectName="SW-CX_YRH.CX_YRH_063BK"/>
     <cge:Meas_Ref ObjectId="127269"/>
    <cge:TPSR_Ref TObjectID="23424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127270">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5111.000000 -448.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23425" ObjectName="SW-CX_YRH.CX_YRH_064BK"/>
     <cge:Meas_Ref ObjectId="127270"/>
    <cge:TPSR_Ref TObjectID="23425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127255">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -601.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23410" ObjectName="SW-CX_YRH.CX_YRH_001BK"/>
     <cge:Meas_Ref ObjectId="127255"/>
    <cge:TPSR_Ref TObjectID="23410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127276">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 -583.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23431" ObjectName="SW-CX_YRH.CX_YRH_003BK"/>
     <cge:Meas_Ref ObjectId="127276"/>
    <cge:TPSR_Ref TObjectID="23431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127250">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -945.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23405" ObjectName="SW-CX_YRH.CX_YRH_311BK"/>
     <cge:Meas_Ref ObjectId="127250"/>
    <cge:TPSR_Ref TObjectID="23405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127266">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 -601.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23421" ObjectName="SW-CX_YRH.CX_YRH_002BK"/>
     <cge:Meas_Ref ObjectId="127266"/>
    <cge:TPSR_Ref TObjectID="23421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127283">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3766.000000 276.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23438" ObjectName="SW-CX_YRH.CX_YRH_401BK"/>
     <cge:Meas_Ref ObjectId="127283"/>
    <cge:TPSR_Ref TObjectID="23438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127285">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.000000 274.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23440" ObjectName="SW-CX_YRH.CX_YRH_402BK"/>
     <cge:Meas_Ref ObjectId="127285"/>
    <cge:TPSR_Ref TObjectID="23440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127289">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 275.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23444" ObjectName="SW-CX_YRH.CX_YRH_403BK"/>
     <cge:Meas_Ref ObjectId="127289"/>
    <cge:TPSR_Ref TObjectID="23444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127291">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4371.000000 275.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23446" ObjectName="SW-CX_YRH.CX_YRH_404BK"/>
     <cge:Meas_Ref ObjectId="127291"/>
    <cge:TPSR_Ref TObjectID="23446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127284">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3911.000000 344.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23439" ObjectName="SW-CX_YRH.CX_YRH_412BK"/>
     <cge:Meas_Ref ObjectId="127284"/>
    <cge:TPSR_Ref TObjectID="23439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127290">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4339.000000 347.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23445" ObjectName="SW-CX_YRH.CX_YRH_434BK"/>
     <cge:Meas_Ref ObjectId="127290"/>
    <cge:TPSR_Ref TObjectID="23445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127286">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4790.000000 280.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23441" ObjectName="SW-CX_YRH.CX_YRH_405BK"/>
     <cge:Meas_Ref ObjectId="127286"/>
    <cge:TPSR_Ref TObjectID="23441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127288">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 283.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23443" ObjectName="SW-CX_YRH.CX_YRH_406BK"/>
     <cge:Meas_Ref ObjectId="127288"/>
    <cge:TPSR_Ref TObjectID="23443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127287">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4951.000000 352.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23442" ObjectName="SW-CX_YRH.CX_YRH_456BK"/>
     <cge:Meas_Ref ObjectId="127287"/>
    <cge:TPSR_Ref TObjectID="23442"/></metadata>
   </g>
  </g><g id="CurrentTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1e26020">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4239.000000 -1062.000000)" xlink:href="#currentTransformer:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YRH.CX_YRH_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4483,-567 5150,-567 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23588" ObjectName="BS-CX_YRH.CX_YRH_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="23588"/></metadata>
   <polyline fill="none" opacity="0" points="4483,-567 5150,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YRH.CX_YRH_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3425,-567 4081,-567 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23587" ObjectName="BS-CX_YRH.CX_YRH_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="23587"/></metadata>
   <polyline fill="none" opacity="0" points="3425,-567 4081,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YRH.CX_YRH_9ⅢM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4131,-567 4418,-567 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23589" ObjectName="BS-CX_YRH.CX_YRH_9ⅢM"/>
    <cge:TPSR_Ref TObjectID="23589"/></metadata>
   <polyline fill="none" opacity="0" points="4131,-567 4418,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3695,303 3807,303 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3695,303 3807,303 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,303 4021,303 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3909,303 4021,303 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4117,302 4229,302 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4117,302 4229,302 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4344,304 4456,304 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4344,304 4456,304 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4729,307 4841,307 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4729,307 4841,307 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4956,309 5068,309 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4956,309 5068,309 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3678.000000 -1129.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 -1141.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -1129.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1ea9510" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3429.000000 -422.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e4aff0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3553.000000 -421.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ee4200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 -421.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e52d40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 -421.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e178b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4657.000000 -423.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dd5ab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -423.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1daf870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4913.000000 -423.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e0db60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5041.000000 -423.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e4dd00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.000000 -838.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1df1990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -714.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d15690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 -926.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d6be80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 -992.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d6f300" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 -1065.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d88cb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4742.000000 -838.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_1e21b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3505,-567 3505,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23587@0" ObjectIDZND0="23411@0" Pin0InfoVect0LinkObjId="SW-127256_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3505,-567 3505,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ea9320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3505,-428 3493,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_1e88f00@0" ObjectIDND1="0@x" ObjectIDND2="23411@x" ObjectIDZND0="23416@1" Pin0InfoVect0LinkObjId="SW-127261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e88f00_0" Pin1InfoVect1LinkObjId="g_1e26020_0" Pin1InfoVect2LinkObjId="SW-127256_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3505,-428 3493,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e88620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3505,-396 3487,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="23416@x" ObjectIDND1="23411@x" ObjectIDND2="0@x" ObjectIDZND0="g_1e88f00@0" Pin0InfoVect0LinkObjId="g_1e88f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127261_0" Pin1InfoVect1LinkObjId="SW-127256_0" Pin1InfoVect2LinkObjId="g_1e26020_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3505,-396 3487,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db3310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3505,-428 3505,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="23416@x" ObjectIDND1="23411@x" ObjectIDZND0="g_1e88f00@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1e88f00_0" Pin0InfoVect1LinkObjId="g_1e26020_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127261_0" Pin1InfoVect1LinkObjId="SW-127256_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3505,-428 3505,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db3b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3505,-396 3505,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="transformer2" ObjectIDND0="g_1e88f00@0" ObjectIDND1="23416@x" ObjectIDND2="23411@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e88f00_0" Pin1InfoVect1LinkObjId="SW-127261_0" Pin1InfoVect2LinkObjId="SW-127256_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3505,-396 3505,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee04e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-567 3633,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23587@0" ObjectIDZND0="23412@0" Pin0InfoVect0LinkObjId="SW-127257_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-567 3633,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee06d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-453 3633,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="23412@1" ObjectIDZND0="23417@x" ObjectIDZND1="g_1e75d10@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-127262_0" Pin0InfoVect1LinkObjId="g_1e75d10_0" Pin0InfoVect2LinkObjId="g_1e26020_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127257_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-453 3633,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e4ac10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-427 3621,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="23412@x" ObjectIDND1="g_1e75d10@0" ObjectIDND2="0@x" ObjectIDZND0="23417@1" Pin0InfoVect0LinkObjId="SW-127262_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127257_0" Pin1InfoVect1LinkObjId="g_1e75d10_0" Pin1InfoVect2LinkObjId="g_1e26020_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-427 3621,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e4ae00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3585,-427 3571,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23417@0" ObjectIDZND0="g_1e4aff0@0" Pin0InfoVect0LinkObjId="g_1e4aff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127262_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3585,-427 3571,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e4b620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-395 3615,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="23412@x" ObjectIDND1="23417@x" ObjectIDND2="0@x" ObjectIDZND0="g_1e75d10@0" Pin0InfoVect0LinkObjId="g_1e75d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127257_0" Pin1InfoVect1LinkObjId="SW-127262_0" Pin1InfoVect2LinkObjId="g_1e26020_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-395 3615,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e4b810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-427 3633,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="23412@x" ObjectIDND1="23417@x" ObjectIDZND0="g_1e75d10@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1e75d10_0" Pin0InfoVect1LinkObjId="g_1e26020_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127257_0" Pin1InfoVect1LinkObjId="SW-127262_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-427 3633,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e764e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-395 3633,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_1e75d10@0" ObjectIDND1="23412@x" ObjectIDND2="23417@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e75d10_0" Pin1InfoVect1LinkObjId="SW-127257_0" Pin1InfoVect2LinkObjId="SW-127262_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-395 3633,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e625e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-567 3761,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23587@0" ObjectIDZND0="23413@0" Pin0InfoVect0LinkObjId="SW-127258_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-567 3761,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e627d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-453 3761,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="23413@1" ObjectIDZND0="23418@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1ea9910@0" Pin0InfoVect0LinkObjId="SW-127263_0" Pin0InfoVect1LinkObjId="g_1e26020_0" Pin0InfoVect2LinkObjId="g_1ea9910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127258_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-453 3761,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee3e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-427 3749,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23413@x" ObjectIDND1="0@x" ObjectIDND2="g_1ea9910@0" ObjectIDZND0="23418@1" Pin0InfoVect0LinkObjId="SW-127263_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127258_0" Pin1InfoVect1LinkObjId="g_1e26020_0" Pin1InfoVect2LinkObjId="g_1ea9910_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-427 3749,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee4010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-427 3699,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23418@0" ObjectIDZND0="g_1ee4200@0" Pin0InfoVect0LinkObjId="g_1ee4200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127263_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-427 3699,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eaa320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-395 3761,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_1ea9910@0" ObjectIDND1="23413@x" ObjectIDND2="23418@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ea9910_0" Pin1InfoVect1LinkObjId="SW-127258_0" Pin1InfoVect2LinkObjId="SW-127263_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-395 3761,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e50d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-567 3889,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23587@0" ObjectIDZND0="23414@0" Pin0InfoVect0LinkObjId="SW-127259_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-567 3889,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e50f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-453 3889,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23414@1" ObjectIDZND0="23419@x" ObjectIDZND1="g_1df5040@0" ObjectIDZND2="g_1d61b20@0" Pin0InfoVect0LinkObjId="SW-127264_0" Pin0InfoVect1LinkObjId="g_1df5040_0" Pin0InfoVect2LinkObjId="g_1d61b20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127259_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-453 3889,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e52960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-427 3877,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23414@x" ObjectIDND1="g_1df5040@0" ObjectIDND2="g_1d61b20@0" ObjectIDZND0="23419@1" Pin0InfoVect0LinkObjId="SW-127264_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127259_0" Pin1InfoVect1LinkObjId="g_1df5040_0" Pin1InfoVect2LinkObjId="g_1d61b20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-427 3877,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e52b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-427 3827,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23419@0" ObjectIDZND0="g_1e52d40@0" Pin0InfoVect0LinkObjId="g_1e52d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127264_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3841,-427 3827,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df4c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-395 3871,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="23414@x" ObjectIDND1="23419@x" ObjectIDND2="g_1d61b20@0" ObjectIDZND0="g_1df5040@0" Pin0InfoVect0LinkObjId="g_1df5040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127259_0" Pin1InfoVect1LinkObjId="SW-127264_0" Pin1InfoVect2LinkObjId="g_1d61b20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-395 3871,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df4e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-427 3889,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23414@x" ObjectIDND1="23419@x" ObjectIDZND0="g_1df5040@0" ObjectIDZND1="g_1d61b20@0" ObjectIDZND2="23916@x" Pin0InfoVect0LinkObjId="g_1df5040_0" Pin0InfoVect1LinkObjId="g_1d61b20_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127259_0" Pin1InfoVect1LinkObjId="SW-127264_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-427 3889,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ebdbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-395 3761,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_1ea9910@0" ObjectIDZND0="0@x" ObjectIDZND1="23413@x" ObjectIDZND2="23418@x" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="SW-127258_0" Pin0InfoVect2LinkObjId="SW-127263_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ea9910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-395 3761,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ebddb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-395 3761,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_1ea9910@0" ObjectIDZND0="23413@x" ObjectIDZND1="23418@x" Pin0InfoVect0LinkObjId="SW-127258_0" Pin0InfoVect1LinkObjId="SW-127263_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="g_1ea9910_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-395 3761,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ebe730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3969,-567 3969,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23587@0" ObjectIDZND0="23420@1" Pin0InfoVect0LinkObjId="SW-127265_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3969,-567 3969,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e9f010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-567 4049,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23587@0" ObjectIDZND0="23432@0" Pin0InfoVect0LinkObjId="SW-127277_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-567 4049,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eea1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4177,-542 4177,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23437@1" ObjectIDZND0="23589@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4177,-542 4177,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eea3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-428 4062,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="23432@x" ObjectIDND1="23437@x" ObjectIDZND0="g_1e7ed30@0" Pin0InfoVect0LinkObjId="g_1e7ed30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127277_0" Pin1InfoVect1LinkObjId="SW-127282_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-428 4062,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eeae20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-453 4049,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23432@1" ObjectIDZND0="g_1e7ed30@0" ObjectIDZND1="23437@x" Pin0InfoVect0LinkObjId="g_1e7ed30_0" Pin0InfoVect1LinkObjId="SW-127282_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127277_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-453 4049,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e7eae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-427 4049,-387 4177,-387 4177,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_1e7ed30@0" ObjectIDND1="23432@x" ObjectIDZND0="23437@0" Pin0InfoVect0LinkObjId="SW-127282_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e7ed30_0" Pin1InfoVect1LinkObjId="SW-127277_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-427 4049,-387 4177,-387 4177,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e12830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-567 4257,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23589@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1eea1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-567 4257,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e12a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-508 4282,-508 4282,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1dba990@0" ObjectIDZND0="g_1e138d0@0" Pin0InfoVect0LinkObjId="g_1e138d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="g_1dba990_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-508 4282,-508 4282,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e13490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-525 4257,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1dba990@0" ObjectIDZND1="g_1e138d0@0" Pin0InfoVect0LinkObjId="g_1dba990_0" Pin0InfoVect1LinkObjId="g_1e138d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-525 4257,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e136b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-508 4257,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="g_1e138d0@0" ObjectIDZND0="g_1dba990@0" Pin0InfoVect0LinkObjId="g_1dba990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="g_1e138d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-508 4257,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e25170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-567 4385,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23589@0" ObjectIDZND0="23436@1" Pin0InfoVect0LinkObjId="SW-127281_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1eea1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-567 4385,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f378f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-546 4545,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="23434@0" ObjectIDZND0="23588@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127279_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-546 4545,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f37b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-428 4529,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="23436@x" ObjectIDND1="23434@x" ObjectIDZND0="g_1f38980@0" Pin0InfoVect0LinkObjId="g_1f38980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127281_0" Pin1InfoVect1LinkObjId="SW-127279_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-428 4529,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f38510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-473 4385,-387 4545,-387 4545,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="23436@0" ObjectIDZND0="g_1f38980@0" ObjectIDZND1="23434@x" Pin0InfoVect0LinkObjId="g_1f38980_0" Pin0InfoVect1LinkObjId="SW-127279_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-473 4385,-387 4545,-387 4545,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f38760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-426 4545,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_1f38980@0" ObjectIDND1="23436@x" ObjectIDZND0="23434@1" Pin0InfoVect0LinkObjId="SW-127279_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f38980_0" Pin1InfoVect1LinkObjId="SW-127281_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-426 4545,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e38980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-567 4641,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23588@0" ObjectIDZND0="23433@0" Pin0InfoVect0LinkObjId="SW-127278_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f378f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-567 4641,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e7b2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-567 4737,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23588@0" ObjectIDZND0="23422@0" Pin0InfoVect0LinkObjId="SW-127267_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f378f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-567 4737,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e7b4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-455 4737,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="23422@1" ObjectIDZND0="23427@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1e182a0@0" Pin0InfoVect0LinkObjId="SW-127272_0" Pin0InfoVect1LinkObjId="g_1e26020_0" Pin0InfoVect2LinkObjId="g_1e182a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127267_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-455 4737,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e7da10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-429 4725,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23422@x" ObjectIDND1="0@x" ObjectIDND2="g_1e182a0@0" ObjectIDZND0="23427@1" Pin0InfoVect0LinkObjId="SW-127272_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127267_0" Pin1InfoVect1LinkObjId="g_1e26020_0" Pin1InfoVect2LinkObjId="g_1e182a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-429 4725,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e17650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-429 4675,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23427@0" ObjectIDZND0="g_1e178b0@0" Pin0InfoVect0LinkObjId="g_1e178b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127272_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-429 4675,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e18f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-397 4737,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_1e182a0@0" ObjectIDND1="23422@x" ObjectIDND2="23427@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e182a0_0" Pin1InfoVect1LinkObjId="SW-127267_0" Pin1InfoVect2LinkObjId="SW-127272_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-397 4737,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e19470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4719,-397 4737,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_1e182a0@0" ObjectIDZND0="0@x" ObjectIDZND1="23422@x" ObjectIDZND2="23427@x" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="SW-127267_0" Pin0InfoVect2LinkObjId="SW-127272_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e182a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4719,-397 4737,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e196d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-397 4737,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_1e182a0@0" ObjectIDZND0="23422@x" ObjectIDZND1="23427@x" Pin0InfoVect0LinkObjId="SW-127267_0" Pin0InfoVect1LinkObjId="SW-127272_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="g_1e182a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-397 4737,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dd2e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4865,-567 4865,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23588@0" ObjectIDZND0="23423@0" Pin0InfoVect0LinkObjId="SW-127268_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f378f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4865,-567 4865,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dd30d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4865,-455 4865,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="23423@1" ObjectIDZND0="23428@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1e5b1b0@0" Pin0InfoVect0LinkObjId="SW-127273_0" Pin0InfoVect1LinkObjId="g_1e26020_0" Pin0InfoVect2LinkObjId="g_1e5b1b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127268_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4865,-455 4865,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dd55f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4865,-429 4853,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23423@x" ObjectIDND1="0@x" ObjectIDND2="g_1e5b1b0@0" ObjectIDZND0="23428@1" Pin0InfoVect0LinkObjId="SW-127273_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127268_0" Pin1InfoVect1LinkObjId="g_1e26020_0" Pin1InfoVect2LinkObjId="g_1e5b1b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4865,-429 4853,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dd5850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-429 4803,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23428@0" ObjectIDZND0="g_1dd5ab0@0" Pin0InfoVect0LinkObjId="g_1dd5ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127273_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-429 4803,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e5bea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4865,-397 4865,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_1e5b1b0@0" ObjectIDND1="23423@x" ObjectIDND2="23428@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e5b1b0_0" Pin1InfoVect1LinkObjId="SW-127268_0" Pin1InfoVect2LinkObjId="SW-127273_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4865,-397 4865,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e5c380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4847,-397 4865,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_1e5b1b0@0" ObjectIDZND0="0@x" ObjectIDZND1="23423@x" ObjectIDZND2="23428@x" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="SW-127268_0" Pin0InfoVect2LinkObjId="SW-127273_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e5b1b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4847,-397 4865,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e5c5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4865,-397 4865,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_1e5b1b0@0" ObjectIDZND0="23423@x" ObjectIDZND1="23428@x" Pin0InfoVect0LinkObjId="SW-127268_0" Pin0InfoVect1LinkObjId="SW-127273_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="g_1e5b1b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4865,-397 4865,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ec4bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-567 4993,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23588@0" ObjectIDZND0="23424@0" Pin0InfoVect0LinkObjId="SW-127269_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f378f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-567 4993,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ec4e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-455 4993,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="23424@1" ObjectIDZND0="23429@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1db02a0@0" Pin0InfoVect0LinkObjId="SW-127274_0" Pin0InfoVect1LinkObjId="g_1e26020_0" Pin0InfoVect2LinkObjId="g_1db02a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-455 4993,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1daf3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-429 4981,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23424@x" ObjectIDND1="0@x" ObjectIDND2="g_1db02a0@0" ObjectIDZND0="23429@1" Pin0InfoVect0LinkObjId="SW-127274_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127269_0" Pin1InfoVect1LinkObjId="g_1e26020_0" Pin1InfoVect2LinkObjId="g_1db02a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-429 4981,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1daf610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4945,-429 4931,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23429@0" ObjectIDZND0="g_1daf870@0" Pin0InfoVect0LinkObjId="g_1daf870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127274_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4945,-429 4931,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1db0f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-397 4993,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_1db02a0@0" ObjectIDND1="23424@x" ObjectIDND2="23429@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1db02a0_0" Pin1InfoVect1LinkObjId="SW-127269_0" Pin1InfoVect2LinkObjId="SW-127274_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-397 4993,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1de60d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4975,-397 4993,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_1db02a0@0" ObjectIDZND0="0@x" ObjectIDZND1="23424@x" ObjectIDZND2="23429@x" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="SW-127269_0" Pin0InfoVect2LinkObjId="SW-127274_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1db02a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4975,-397 4993,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1de62c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-397 4993,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_1db02a0@0" ObjectIDZND0="23424@x" ObjectIDZND1="23429@x" Pin0InfoVect0LinkObjId="SW-127269_0" Pin0InfoVect1LinkObjId="SW-127274_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="g_1db02a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-397 4993,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d45f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-567 5120,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23588@0" ObjectIDZND0="23425@0" Pin0InfoVect0LinkObjId="SW-127270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f378f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-567 5120,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d46150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-455 5121,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23425@1" ObjectIDZND0="23430@x" ObjectIDZND1="g_1d7c9b0@0" ObjectIDZND2="23918@x" Pin0InfoVect0LinkObjId="SW-127275_0" Pin0InfoVect1LinkObjId="g_1d7c9b0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-455 5121,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e0d6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-429 5109,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23425@x" ObjectIDND1="g_1d7c9b0@0" ObjectIDND2="23918@x" ObjectIDZND0="23430@1" Pin0InfoVect0LinkObjId="SW-127275_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127270_0" Pin1InfoVect1LinkObjId="g_1d7c9b0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-429 5109,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e0d900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-429 5059,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23430@0" ObjectIDZND0="g_1e0db60@0" Pin0InfoVect0LinkObjId="g_1e0db60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127275_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-429 5059,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e560f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-567 3683,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23587@0" ObjectIDZND0="23410@1" Pin0InfoVect0LinkObjId="SW-127255_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-567 3683,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e56350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-812 3619,-812 3619,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="23410@x" ObjectIDND1="23415@x" ObjectIDND2="g_1e4ebd0@0" ObjectIDZND0="g_1dbf880@0" Pin0InfoVect0LinkObjId="g_1dbf880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127255_0" Pin1InfoVect1LinkObjId="SW-127260_0" Pin1InfoVect2LinkObjId="g_1e4ebd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-812 3619,-812 3619,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e56ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-454 4641,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23433@1" ObjectIDZND0="g_1e39150@0" ObjectIDZND1="23420@x" Pin0InfoVect0LinkObjId="g_1e39150_0" Pin0InfoVect1LinkObjId="SW-127265_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127278_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-454 4641,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e57130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-428 4626,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="23433@x" ObjectIDND1="23420@x" ObjectIDZND0="g_1e39150@0" Pin0InfoVect0LinkObjId="g_1e39150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127278_0" Pin1InfoVect1LinkObjId="SW-127265_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-428 4626,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e4daa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3635,-844 3621,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23415@0" ObjectIDZND0="g_1e4dd00@0" Pin0InfoVect0LinkObjId="g_1e4dd00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3635,-844 3621,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e4e730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-844 3671,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="g_1e4ebd0@0" ObjectIDND1="g_1e4fb80@0" ObjectIDND2="23584@x" ObjectIDZND0="23415@1" Pin0InfoVect0LinkObjId="SW-127260_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e4ebd0_0" Pin1InfoVect1LinkObjId="g_1e4fb80_0" Pin1InfoVect2LinkObjId="g_1d99e20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-844 3671,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e4e970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-876 3698,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="23415@x" ObjectIDND1="g_1dbf880@0" ObjectIDND2="23410@x" ObjectIDZND0="g_1e4ebd0@0" Pin0InfoVect0LinkObjId="g_1e4ebd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127260_0" Pin1InfoVect1LinkObjId="g_1dbf880_0" Pin1InfoVect2LinkObjId="SW-127255_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-876 3698,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d99e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3682,-1134 3682,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="23584@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3682,-1134 3682,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1deb610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3812,-567 3812,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23587@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3812,-567 3812,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1dee200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3812,-651 3780,-651 3780,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1deb870@0" ObjectIDZND0="g_1def100@0" Pin0InfoVect0LinkObjId="g_1def100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="g_1deb870_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3812,-651 3780,-651 3780,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1deec40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3812,-629 3812,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1deb870@0" ObjectIDZND1="g_1def100@0" Pin0InfoVect0LinkObjId="g_1deb870_0" Pin0InfoVect1LinkObjId="g_1def100_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3812,-629 3812,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1deeea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3812,-651 3812,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="g_1def100@0" ObjectIDZND0="g_1deb870@0" Pin0InfoVect0LinkObjId="g_1deb870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="g_1def100_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3812,-651 3812,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df1330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-567 4228,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23589@0" ObjectIDZND0="23431@1" Pin0InfoVect0LinkObjId="SW-127276_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1eea1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-567 4228,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df1730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4181,-720 4167,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23435@0" ObjectIDZND0="g_1df1990@0" Pin0InfoVect0LinkObjId="g_1df1990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4181,-720 4167,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d15430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-932 4166,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23409@0" ObjectIDZND0="g_1d15690@0" Pin0InfoVect0LinkObjId="g_1d15690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127254_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-932 4166,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d16120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-932 4216,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="23405@x" ObjectIDND1="23586@x" ObjectIDZND0="23409@1" Pin0InfoVect0LinkObjId="SW-127254_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127250_0" Pin1InfoVect1LinkObjId="g_1d563a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-932 4216,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6bc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-998 4166,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23408@0" ObjectIDZND0="g_1d6be80@0" Pin0InfoVect0LinkObjId="g_1d6be80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127253_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-998 4166,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6c910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-998 4216,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23406@x" ObjectIDND1="23405@x" ObjectIDZND0="23408@1" Pin0InfoVect0LinkObjId="SW-127253_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127251_0" Pin1InfoVect1LinkObjId="SW-127250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-998 4216,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6f0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4179,-1071 4165,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23407@0" ObjectIDZND0="g_1d6f300@0" Pin0InfoVect0LinkObjId="g_1d6f300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127252_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4179,-1071 4165,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-1071 4215,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_1e26020@0" ObjectIDND1="g_1e2a4b0@0" ObjectIDND2="0@x" ObjectIDZND0="23407@1" Pin0InfoVect0LinkObjId="SW-127252_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="g_1e2a4b0_0" Pin1InfoVect2LinkObjId="g_1e26020_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-1071 4215,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e25550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1100 4244,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="currentTransformer" ObjectIDND0="g_1e2a4b0@0" ObjectIDND1="0@x" ObjectIDND2="23407@x" ObjectIDZND0="g_1e26020@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e2a4b0_0" Pin1InfoVect1LinkObjId="g_1e26020_0" Pin1InfoVect2LinkObjId="SW-127252_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1100 4244,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e29500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1124 4213,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1e26020@0" ObjectIDND1="23407@x" ObjectIDND2="23406@x" ObjectIDZND0="g_1e2a4b0@0" Pin0InfoVect0LinkObjId="g_1e2a4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="SW-127252_0" Pin1InfoVect2LinkObjId="SW-127251_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1124 4213,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e29ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1100 4228,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_1e26020@0" ObjectIDND1="23407@x" ObjectIDND2="23406@x" ObjectIDZND0="g_1e2a4b0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1e2a4b0_0" Pin0InfoVect1LinkObjId="g_1e26020_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="SW-127252_0" Pin1InfoVect2LinkObjId="SW-127251_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1100 4228,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e2a250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1124 4228,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="currentTransformer" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1e2a4b0@0" ObjectIDND1="g_1e26020@0" ObjectIDND2="23407@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e2a4b0_0" Pin1InfoVect1LinkObjId="g_1e26020_0" Pin1InfoVect2LinkObjId="SW-127252_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1124 4228,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d63f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-1008 3683,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23584@0" ObjectIDZND0="g_1e4fb80@0" ObjectIDZND1="g_1e4ebd0@0" ObjectIDZND2="23415@x" Pin0InfoVect0LinkObjId="g_1e4fb80_0" Pin0InfoVect1LinkObjId="g_1e4ebd0_0" Pin0InfoVect2LinkObjId="SW-127260_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d99e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-1008 3683,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d650a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-844 3683,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="23415@x" ObjectIDND1="g_1dbf880@0" ObjectIDND2="23410@x" ObjectIDZND0="g_1e4ebd0@0" ObjectIDZND1="g_1e4fb80@0" ObjectIDZND2="23584@x" Pin0InfoVect0LinkObjId="g_1e4ebd0_0" Pin0InfoVect1LinkObjId="g_1e4fb80_0" Pin0InfoVect2LinkObjId="g_1d99e20_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127260_0" Pin1InfoVect1LinkObjId="g_1dbf880_0" Pin1InfoVect2LinkObjId="SW-127255_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-844 3683,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d66b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-700 3683,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23410@0" ObjectIDZND0="g_1dbf880@0" ObjectIDZND1="23415@x" ObjectIDZND2="g_1e4ebd0@0" Pin0InfoVect0LinkObjId="g_1dbf880_0" Pin0InfoVect1LinkObjId="SW-127260_0" Pin0InfoVect2LinkObjId="g_1e4ebd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127255_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-700 3683,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d66d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-844 3683,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" EndDevType1="breaker" ObjectIDND0="23415@x" ObjectIDND1="g_1e4ebd0@0" ObjectIDND2="g_1e4fb80@0" ObjectIDZND0="g_1dbf880@0" ObjectIDZND1="23410@x" Pin0InfoVect0LinkObjId="g_1dbf880_0" Pin0InfoVect1LinkObjId="SW-127255_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127260_0" Pin1InfoVect1LinkObjId="g_1e4ebd0_0" Pin1InfoVect2LinkObjId="g_1e4fb80_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-844 3683,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d85ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-567 4822,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23588@0" ObjectIDZND0="23421@1" Pin0InfoVect0LinkObjId="SW-127266_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f378f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-567 4822,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d86120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-812 4758,-812 4758,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="23421@x" ObjectIDND1="23426@x" ObjectIDND2="g_1d89c00@0" ObjectIDZND0="g_1dc1e20@0" Pin0InfoVect0LinkObjId="g_1dc1e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127266_0" Pin1InfoVect1LinkObjId="SW-127271_0" Pin1InfoVect2LinkObjId="g_1d89c00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-812 4758,-812 4758,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d88a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4774,-844 4760,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23426@0" ObjectIDZND0="g_1d88cb0@0" Pin0InfoVect0LinkObjId="g_1d88cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4774,-844 4760,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d89740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-844 4810,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d89c00@0" ObjectIDND1="23585@x" ObjectIDND2="g_1d8acb0@0" ObjectIDZND0="23426@1" Pin0InfoVect0LinkObjId="SW-127271_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d89c00_0" Pin1InfoVect1LinkObjId="g_1d8ba20_0" Pin1InfoVect2LinkObjId="g_1d8acb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-844 4810,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d899a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-876 4837,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="23426@x" ObjectIDND1="g_1dc1e20@0" ObjectIDND2="23421@x" ObjectIDZND0="g_1d89c00@0" Pin0InfoVect0LinkObjId="g_1d89c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127271_0" Pin1InfoVect1LinkObjId="g_1dc1e20_0" Pin1InfoVect2LinkObjId="SW-127266_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-876 4837,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d8ba20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-1134 4822,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="23585@1" Pin0InfoVect0LinkObjId="g_1d5a290_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-1134 4822,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8f890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-567 4948,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23588@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f378f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-567 4948,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d92890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-652 4916,-652 4916,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1d8faf0@0" ObjectIDZND0="g_1d92fb0@0" Pin0InfoVect0LinkObjId="g_1d92fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="g_1d8faf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-652 4916,-652 4916,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d92af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-630 4948,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1d8faf0@0" ObjectIDZND1="g_1d92fb0@0" Pin0InfoVect0LinkObjId="g_1d8faf0_0" Pin0InfoVect1LinkObjId="g_1d92fb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-630 4948,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d92d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-652 4948,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="g_1d92fb0@0" ObjectIDZND0="g_1d8faf0@0" Pin0InfoVect0LinkObjId="g_1d8faf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="g_1d92fb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-652 4948,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d95600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-1007 4822,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23585@0" ObjectIDZND0="g_1d8acb0@0" ObjectIDZND1="g_1d89c00@0" ObjectIDZND2="23426@x" Pin0InfoVect0LinkObjId="g_1d8acb0_0" Pin0InfoVect1LinkObjId="g_1d89c00_0" Pin0InfoVect2LinkObjId="SW-127271_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d8ba20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-1007 4822,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d957f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-844 4822,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="breaker" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="23426@x" ObjectIDND1="g_1dc1e20@0" ObjectIDND2="23421@x" ObjectIDZND0="g_1d89c00@0" ObjectIDZND1="23585@x" ObjectIDZND2="g_1d8acb0@0" Pin0InfoVect0LinkObjId="g_1d89c00_0" Pin0InfoVect1LinkObjId="g_1d8ba20_0" Pin0InfoVect2LinkObjId="g_1d8acb0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127271_0" Pin1InfoVect1LinkObjId="g_1dc1e20_0" Pin1InfoVect2LinkObjId="SW-127266_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-844 4822,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d959e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-700 4822,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23421@0" ObjectIDZND0="g_1dc1e20@0" ObjectIDZND1="23426@x" ObjectIDZND2="g_1d89c00@0" Pin0InfoVect0LinkObjId="g_1dc1e20_0" Pin0InfoVect1LinkObjId="SW-127271_0" Pin0InfoVect2LinkObjId="g_1d89c00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127266_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-700 4822,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d95bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-844 4822,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="voltageTransformer" EndDevType1="breaker" ObjectIDND0="23426@x" ObjectIDND1="g_1d89c00@0" ObjectIDND2="23585@x" ObjectIDZND0="g_1dc1e20@0" ObjectIDZND1="23421@x" Pin0InfoVect0LinkObjId="g_1dc1e20_0" Pin0InfoVect1LinkObjId="SW-127266_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127271_0" Pin1InfoVect1LinkObjId="g_1d89c00_0" Pin1InfoVect2LinkObjId="g_1d8ba20_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-844 4822,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d53870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1071 4228,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="currentTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="23407@x" ObjectIDND1="23406@x" ObjectIDZND0="g_1e26020@0" ObjectIDZND1="g_1e2a4b0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="g_1e2a4b0_0" Pin0InfoVect2LinkObjId="g_1e26020_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127252_0" Pin1InfoVect1LinkObjId="SW-127251_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1071 4228,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d54240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-998 4228,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23408@x" ObjectIDND1="23405@x" ObjectIDZND0="23406@0" Pin0InfoVect0LinkObjId="SW-127251_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127253_0" Pin1InfoVect1LinkObjId="SW-127250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-998 4228,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d54cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-932 4228,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="23409@x" ObjectIDND1="23586@x" ObjectIDZND0="23405@0" Pin0InfoVect0LinkObjId="SW-127250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127254_0" Pin1InfoVect1LinkObjId="g_1d563a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-932 4228,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d54f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-909 4228,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23586@1" ObjectIDZND0="23409@x" ObjectIDZND1="23405@x" Pin0InfoVect0LinkObjId="SW-127254_0" Pin0InfoVect1LinkObjId="SW-127250_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d563a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-909 4228,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d551b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1054 4228,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="currentTransformer" EndDevType2="lightningRod" ObjectIDND0="23406@1" ObjectIDZND0="23407@x" ObjectIDZND1="g_1e26020@0" ObjectIDZND2="g_1e2a4b0@0" Pin0InfoVect0LinkObjId="SW-127252_0" Pin0InfoVect1LinkObjId="g_1e26020_0" Pin0InfoVect2LinkObjId="g_1e2a4b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127251_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1054 4228,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d55410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-980 4228,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23405@1" ObjectIDZND0="23408@x" ObjectIDZND1="23406@x" Pin0InfoVect0LinkObjId="SW-127253_0" Pin0InfoVect1LinkObjId="SW-127251_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-980 4228,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d55ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-829 4228,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23586@0" ObjectIDZND0="g_1d14970@0" ObjectIDZND1="g_1d13400@0" ObjectIDZND2="23435@x" Pin0InfoVect0LinkObjId="g_1d14970_0" Pin0InfoVect1LinkObjId="g_1d13400_0" Pin0InfoVect2LinkObjId="SW-127280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d563a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-829 4228,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d56140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-803 4243,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23586@x" ObjectIDND1="g_1d13400@0" ObjectIDND2="23435@x" ObjectIDZND0="g_1d14970@0" Pin0InfoVect0LinkObjId="g_1d14970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d563a0_0" Pin1InfoVect1LinkObjId="g_1d13400_0" Pin1InfoVect2LinkObjId="SW-127280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-803 4243,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d563a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-742 4228,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1d13400@0" ObjectIDZND0="23586@x" ObjectIDZND1="g_1d14970@0" ObjectIDZND2="23435@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="g_1d14970_0" Pin0InfoVect2LinkObjId="SW-127280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d13400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-742 4228,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d56e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-803 4228,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="23586@x" ObjectIDND1="g_1d14970@0" ObjectIDZND0="g_1d13400@0" ObjectIDZND1="23435@x" ObjectIDZND2="23431@x" Pin0InfoVect0LinkObjId="g_1d13400_0" Pin0InfoVect1LinkObjId="SW-127280_0" Pin0InfoVect2LinkObjId="SW-127276_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d563a0_0" Pin1InfoVect1LinkObjId="g_1d14970_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-803 4228,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d570d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4217,-720 4228,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="23435@1" ObjectIDZND0="g_1d13400@0" ObjectIDZND1="23586@x" ObjectIDZND2="g_1d14970@0" Pin0InfoVect0LinkObjId="g_1d13400_0" Pin0InfoVect1LinkObjId="g_1d563a0_0" Pin0InfoVect2LinkObjId="g_1d14970_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4217,-720 4228,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d57aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-742 4228,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="voltageTransformer" ObjectIDND0="g_1d13400@0" ObjectIDND1="23586@x" ObjectIDND2="g_1d14970@0" ObjectIDZND0="23435@x" ObjectIDZND1="23431@x" ObjectIDZND2="g_1dbd2e0@0" Pin0InfoVect0LinkObjId="SW-127280_0" Pin0InfoVect1LinkObjId="SW-127276_0" Pin0InfoVect2LinkObjId="g_1dbd2e0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d13400_0" Pin1InfoVect1LinkObjId="g_1d563a0_0" Pin1InfoVect2LinkObjId="g_1d14970_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-742 4228,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d57d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-706 4228,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1dbd2e0@0" ObjectIDND1="23435@x" ObjectIDND2="g_1d13400@0" ObjectIDZND0="23431@0" Pin0InfoVect0LinkObjId="SW-127276_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1dbd2e0_0" Pin1InfoVect1LinkObjId="SW-127280_0" Pin1InfoVect2LinkObjId="g_1d13400_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-706 4228,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d587f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-706 4228,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1dbd2e0@0" ObjectIDZND0="23431@x" ObjectIDZND1="23435@x" ObjectIDZND2="g_1d13400@0" Pin0InfoVect0LinkObjId="SW-127276_0" Pin0InfoVect1LinkObjId="SW-127280_0" Pin0InfoVect2LinkObjId="g_1d13400_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbd2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-706 4228,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d58a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-706 4228,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="23431@x" ObjectIDND1="g_1dbd2e0@0" ObjectIDZND0="23435@x" ObjectIDZND1="g_1d13400@0" ObjectIDZND2="23586@x" Pin0InfoVect0LinkObjId="SW-127280_0" Pin0InfoVect1LinkObjId="g_1d13400_0" Pin0InfoVect2LinkObjId="g_1d563a0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127276_0" Pin1InfoVect1LinkObjId="g_1dbd2e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-706 4228,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d59540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3700,-972 3683,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1e4fb80@0" ObjectIDZND0="g_1e4ebd0@0" ObjectIDZND1="23415@x" ObjectIDZND2="g_1dbf880@0" Pin0InfoVect0LinkObjId="g_1e4ebd0_0" Pin0InfoVect1LinkObjId="SW-127260_0" Pin0InfoVect2LinkObjId="g_1dbf880_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e4fb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3700,-972 3683,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d597a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-972 3683,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1e4fb80@0" ObjectIDND1="23584@x" ObjectIDZND0="g_1e4ebd0@0" ObjectIDZND1="23415@x" ObjectIDZND2="g_1dbf880@0" Pin0InfoVect0LinkObjId="g_1e4ebd0_0" Pin0InfoVect1LinkObjId="SW-127260_0" Pin0InfoVect2LinkObjId="g_1dbf880_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e4fb80_0" Pin1InfoVect1LinkObjId="g_1d99e20_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-972 3683,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d5a290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-972 4822,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1d8acb0@0" ObjectIDZND0="23585@x" ObjectIDZND1="g_1d89c00@0" ObjectIDZND2="23426@x" Pin0InfoVect0LinkObjId="g_1d8ba20_0" Pin0InfoVect1LinkObjId="g_1d89c00_0" Pin0InfoVect2LinkObjId="SW-127271_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d8acb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-972 4822,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d5a4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-972 4822,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="23585@x" ObjectIDND1="g_1d8acb0@0" ObjectIDZND0="g_1d89c00@0" ObjectIDZND1="23426@x" ObjectIDZND2="g_1dc1e20@0" Pin0InfoVect0LinkObjId="g_1d89c00_0" Pin0InfoVect1LinkObjId="SW-127271_0" Pin0InfoVect2LinkObjId="g_1dc1e20_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d8ba20_0" Pin1InfoVect1LinkObjId="g_1d8acb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-972 4822,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d618c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-322 3871,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1df5040@0" ObjectIDND1="23414@x" ObjectIDND2="23419@x" ObjectIDZND0="g_1d61b20@0" Pin0InfoVect0LinkObjId="g_1d61b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1df5040_0" Pin1InfoVect1LinkObjId="SW-127259_0" Pin1InfoVect2LinkObjId="SW-127264_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-322 3871,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d63160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-395 3889,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1df5040@0" ObjectIDND1="23414@x" ObjectIDND2="23419@x" ObjectIDZND0="g_1d61b20@0" ObjectIDZND1="23916@x" Pin0InfoVect0LinkObjId="g_1d61b20_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1df5040_0" Pin1InfoVect1LinkObjId="SW-127259_0" Pin1InfoVect2LinkObjId="SW-127264_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-395 3889,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d72ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-322 3889,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_1d61b20@0" ObjectIDND1="g_1df5040@0" ObjectIDND2="23414@x" ObjectIDZND0="23916@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d61b20_0" Pin1InfoVect1LinkObjId="g_1df5040_0" Pin1InfoVect2LinkObjId="SW-127259_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-322 3889,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d734d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-257 3871,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23916@x" ObjectIDND1="g_1d75150@0" ObjectIDND2="23917@x" ObjectIDZND0="g_1d73730@0" Pin0InfoVect0LinkObjId="g_1d73730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1d75150_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-257 3871,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d74c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-271 3889,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23916@0" ObjectIDZND0="g_1d73730@0" ObjectIDZND1="g_1d75150@0" ObjectIDZND2="23917@x" Pin0InfoVect0LinkObjId="g_1d73730_0" Pin0InfoVect1LinkObjId="g_1d75150_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-271 3889,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d74ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-201 3871,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d73730@0" ObjectIDND1="23916@x" ObjectIDND2="23917@x" ObjectIDZND0="g_1d75150@0" Pin0InfoVect0LinkObjId="g_1d75150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d73730_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-201 3871,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d76670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-257 3889,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1d73730@0" ObjectIDND1="23916@x" ObjectIDZND0="g_1d75150@0" ObjectIDZND1="23917@x" Pin0InfoVect0LinkObjId="g_1d75150_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d73730_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-257 3889,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d768d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-201 3889,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1d75150@0" ObjectIDND1="g_1d73730@0" ObjectIDND2="23916@x" ObjectIDZND0="23917@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d75150_0" Pin1InfoVect1LinkObjId="g_1d73730_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-201 3889,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7acf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-118 3889,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_1d797b0@0" ObjectIDZND0="23917@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1e26020_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d797b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-118 3889,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7af50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-118 3889,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_1d797b0@0" ObjectIDND1="0@x" ObjectIDZND0="23917@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d797b0_0" Pin1InfoVect1LinkObjId="g_1e26020_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-118 3889,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7c230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-118 3889,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_1d797b0@0" ObjectIDND1="23917@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1e26020_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d797b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-118 3889,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7c750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-324 5103,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_1e0e590@0" ObjectIDND1="23430@x" ObjectIDND2="23425@x" ObjectIDZND0="g_1d7c9b0@0" Pin0InfoVect0LinkObjId="g_1d7c9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e0e590_0" Pin1InfoVect1LinkObjId="SW-127275_0" Pin1InfoVect2LinkObjId="SW-127270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-324 5103,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7d6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-397 5121,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1e0e590@0" ObjectIDND1="23430@x" ObjectIDND2="23425@x" ObjectIDZND0="g_1d7c9b0@0" ObjectIDZND1="23918@x" Pin0InfoVect0LinkObjId="g_1d7c9b0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e0e590_0" Pin1InfoVect1LinkObjId="SW-127275_0" Pin1InfoVect2LinkObjId="SW-127270_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-397 5121,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7ff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-324 5121,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1d7c9b0@0" ObjectIDND1="g_1e0e590@0" ObjectIDND2="23430@x" ObjectIDZND0="23918@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d7c9b0_0" Pin1InfoVect1LinkObjId="g_1e0e590_0" Pin1InfoVect2LinkObjId="SW-127275_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-324 5121,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d80400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-259 5103,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23918@x" ObjectIDND1="g_1d81810@0" ObjectIDND2="23919@x" ObjectIDZND0="g_1d80660@0" Pin0InfoVect0LinkObjId="g_1d80660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1d81810_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-259 5103,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d81350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-273 5121,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23918@0" ObjectIDZND0="g_1d80660@0" ObjectIDZND1="g_1d81810@0" ObjectIDZND2="23919@x" Pin0InfoVect0LinkObjId="g_1d80660_0" Pin0InfoVect1LinkObjId="g_1d81810_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-273 5121,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d815b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-203 5103,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d80660@0" ObjectIDND1="23918@x" ObjectIDND2="23919@x" ObjectIDZND0="g_1d81810@0" Pin0InfoVect0LinkObjId="g_1d81810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d80660_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-203 5103,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d82500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-259 5121,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1d80660@0" ObjectIDND1="23918@x" ObjectIDZND0="g_1d81810@0" ObjectIDZND1="23919@x" Pin0InfoVect0LinkObjId="g_1d81810_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d80660_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-259 5121,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d82760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-203 5121,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1d81810@0" ObjectIDND1="g_1d80660@0" ObjectIDND2="23918@x" ObjectIDZND0="23919@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d81810_0" Pin1InfoVect1LinkObjId="g_1d80660_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-203 5121,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d19400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-120 5121,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_1d18730@0" ObjectIDZND0="23919@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1e26020_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d18730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-120 5121,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d19660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-120 5121,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_1d18730@0" ObjectIDND1="0@x" ObjectIDZND0="23919@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d18730_0" Pin1InfoVect1LinkObjId="g_1e26020_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-120 5121,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d1a980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-120 5121,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_1d18730@0" ObjectIDND1="23919@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1e26020_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d18730_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-120 5121,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d1d5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-397 5121,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1e0e590@0" ObjectIDZND0="g_1d7c9b0@0" ObjectIDZND1="23918@x" ObjectIDZND2="23430@x" Pin0InfoVect0LinkObjId="g_1d7c9b0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-127275_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e0e590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-397 5121,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d1d830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-397 5121,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1d7c9b0@0" ObjectIDND1="23918@x" ObjectIDND2="g_1e0e590@0" ObjectIDZND0="23430@x" ObjectIDZND1="23425@x" Pin0InfoVect0LinkObjId="SW-127275_0" Pin0InfoVect1LinkObjId="SW-127270_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d7c9b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1e0e590_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-397 5121,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d20fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3505,-454 3505,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="23411@1" ObjectIDZND0="23416@x" ObjectIDZND1="g_1e88f00@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-127261_0" Pin0InfoVect1LinkObjId="g_1e88f00_0" Pin0InfoVect2LinkObjId="g_1e26020_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127256_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3505,-454 3505,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d211b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3457,-428 3447,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23416@0" ObjectIDZND0="g_1ea9510@0" Pin0InfoVect0LinkObjId="g_1ea9510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3457,-428 3447,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_205bce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,177 3776,144 3774,144 3502,144 3504,143 3504,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="23438@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1e26020_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127283_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,177 3776,144 3774,144 3502,144 3504,143 3504,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_205bf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,303 3776,269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="23438@1" Pin0InfoVect0LinkObjId="SW-127283_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,303 3776,269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2060f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,303 3940,268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="23440@1" Pin0InfoVect0LinkObjId="SW-127285_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,303 3940,268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2061d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3812,334 3776,334 3776,303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="23439@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127284_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3812,334 3776,334 3776,303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2062600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,334 3940,334 3940,302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="23439@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3904,334 3940,334 3940,302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20631d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-276 4737,0 3940,0 3940,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="23440@0" Pin0InfoVect0LinkObjId="SW-127285_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-276 4737,0 3940,0 3940,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2064190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,113 3634,113 3633,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1e26020_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3929,113 3634,113 3633,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2064530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-428 4641,-331 3969,-331 3969,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="23433@x" ObjectIDND1="g_1e39150@0" ObjectIDZND0="23420@0" Pin0InfoVect0LinkObjId="SW-127265_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127278_0" Pin1InfoVect1LinkObjId="g_1e39150_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-428 4641,-331 3969,-331 3969,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2068e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,176 4192,112 3949,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="23444@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127289_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4192,176 4192,112 3949,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2069090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,302 4192,268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="23444@1" Pin0InfoVect0LinkObjId="SW-127289_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,302 4192,268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2069ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4241,338 4192,338 4192,302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="23445@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4241,338 4192,338 4192,302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206a2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,337 4381,337 4381,304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="23445@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127290_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,337 4381,337 4381,304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206ad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4381,304 4381,271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="23446@1" Pin0InfoVect0LinkObjId="SW-127291_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4381,304 4381,271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206c240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4865,-278 4865,32 4381,32 4381,176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="23446@0" Pin0InfoVect0LinkObjId="SW-127291_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4865,-278 4865,32 4381,32 4381,176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2462940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,342 4993,342 4993,309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="23442@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127287_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4944,342 4993,342 4993,309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2462da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,309 4993,276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="23443@1" Pin0InfoVect0LinkObjId="SW-127288_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,309 4993,276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2464dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-276 4993,181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="23443@0" Pin0InfoVect0LinkObjId="SW-127288_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-276 4993,181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24651b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,343 4800,343 4800,307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="23442@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1e26020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127287_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4849,343 4800,343 4800,307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2465970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4800,307 4800,275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="23441@1" Pin0InfoVect0LinkObjId="SW-127286_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4800,307 4800,275 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3505" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3633" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3761" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3889" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3969" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="4049" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23589" cx="4177" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23589" cx="4257" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23589" cx="4385" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4545" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4641" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4737" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4865" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4993" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3683" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3812" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23589" cx="4228" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4822" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4948" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3776" cy="304" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3776" cy="304" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3940" cy="303" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3940" cy="303" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4192" cy="302" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4192" cy="302" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4381" cy="304" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4381" cy="304" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4993" cy="309" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4993" cy="309" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4800" cy="307" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4800" cy="307" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="5120" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="322" x="3421" y="-1260"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="322" x="3421" y="-1260"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3384" y="-1277"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3384" y="-1277"/></g>
  </g><g id="MotifButton_Layer">
   <g href="楚雄地区_500kV_永仁换流站.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="322" x="3421" y="-1260"/></g>
   <g href="楚雄地区_500kV_永仁换流站.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3384" y="-1277"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3906" x2="3916" y1="-147" y2="-137"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5138" x2="5148" y1="-149" y2="-139"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5258" x2="5258" y1="-523" y2="-523"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5247" x2="5726" y1="-523" y2="-523"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5247" x2="5726" y1="-475" y2="-475"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5248" x2="5726" y1="-427" y2="-427"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5248" x2="5726" y1="-379" y2="-379"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5247" x2="5726" y1="-288" y2="-288"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5247" x2="5726" y1="-240" y2="-240"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5248" x2="5726" y1="-192" y2="-192"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5248" x2="5726" y1="-144" y2="-144"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5248" x2="5727" y1="-54" y2="-54"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5248" x2="5727" y1="-6" y2="-6"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5249" x2="5727" y1="42" y2="42"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="5249" x2="5727" y1="90" y2="90"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-127261">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3452.000000 -423.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23416" ObjectName="SW-CX_YRH.CX_YRH_05167SW"/>
     <cge:Meas_Ref ObjectId="127261"/>
    <cge:TPSR_Ref TObjectID="23416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127262">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3580.000000 -422.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23417" ObjectName="SW-CX_YRH.CX_YRH_05267SW"/>
     <cge:Meas_Ref ObjectId="127262"/>
    <cge:TPSR_Ref TObjectID="23417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3708.000000 -422.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23418" ObjectName="SW-CX_YRH.CX_YRH_05367SW"/>
     <cge:Meas_Ref ObjectId="127263"/>
    <cge:TPSR_Ref TObjectID="23418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127264">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.000000 -422.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23419" ObjectName="SW-CX_YRH.CX_YRH_05467SW"/>
     <cge:Meas_Ref ObjectId="127264"/>
    <cge:TPSR_Ref TObjectID="23419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127265">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3959.000000 -466.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23420" ObjectName="SW-CX_YRH.CX_YRH_0121SW"/>
     <cge:Meas_Ref ObjectId="127265"/>
    <cge:TPSR_Ref TObjectID="23420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127282">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4166.925566 -468.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23437" ObjectName="SW-CX_YRH.CX_YRH_0133SW"/>
     <cge:Meas_Ref ObjectId="127282"/>
    <cge:TPSR_Ref TObjectID="23437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4246.582524 -518.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127281">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.925566 -468.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23436" ObjectName="SW-CX_YRH.CX_YRH_0233SW"/>
     <cge:Meas_Ref ObjectId="127281"/>
    <cge:TPSR_Ref TObjectID="23436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127272">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4684.000000 -424.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23427" ObjectName="SW-CX_YRH.CX_YRH_06167SW"/>
     <cge:Meas_Ref ObjectId="127272"/>
    <cge:TPSR_Ref TObjectID="23427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127273">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 -424.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23428" ObjectName="SW-CX_YRH.CX_YRH_06267SW"/>
     <cge:Meas_Ref ObjectId="127273"/>
    <cge:TPSR_Ref TObjectID="23428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127274">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 -424.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23429" ObjectName="SW-CX_YRH.CX_YRH_06367SW"/>
     <cge:Meas_Ref ObjectId="127274"/>
    <cge:TPSR_Ref TObjectID="23429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127275">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5068.000000 -424.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23430" ObjectName="SW-CX_YRH.CX_YRH_06467SW"/>
     <cge:Meas_Ref ObjectId="127275"/>
    <cge:TPSR_Ref TObjectID="23430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127260">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3630.000000 -839.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23415" ObjectName="SW-CX_YRH.CX_YRH_0167SW"/>
     <cge:Meas_Ref ObjectId="127260"/>
    <cge:TPSR_Ref TObjectID="23415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3802.000000 -605.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127280">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4176.000000 -715.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23435" ObjectName="SW-CX_YRH.CX_YRH_0367SW"/>
     <cge:Meas_Ref ObjectId="127280"/>
    <cge:TPSR_Ref TObjectID="23435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127254">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -927.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23409" ObjectName="SW-CX_YRH.CX_YRH_31167SW"/>
     <cge:Meas_Ref ObjectId="127254"/>
    <cge:TPSR_Ref TObjectID="23409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127253">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -993.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23408" ObjectName="SW-CX_YRH.CX_YRH_31110SW"/>
     <cge:Meas_Ref ObjectId="127253"/>
    <cge:TPSR_Ref TObjectID="23408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127252">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -1066.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23407" ObjectName="SW-CX_YRH.CX_YRH_31117SW"/>
     <cge:Meas_Ref ObjectId="127252"/>
    <cge:TPSR_Ref TObjectID="23407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127251">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -1013.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23406" ObjectName="SW-CX_YRH.CX_YRH_3111SW"/>
     <cge:Meas_Ref ObjectId="127251"/>
    <cge:TPSR_Ref TObjectID="23406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127271">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4769.000000 -839.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23426" ObjectName="SW-CX_YRH.CX_YRH_00267SW"/>
     <cge:Meas_Ref ObjectId="127271"/>
    <cge:TPSR_Ref TObjectID="23426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4938.000000 -606.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -266.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23916" ObjectName="SW-CX_YRH.CX_YRH_0546SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="23916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 -125.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23917" ObjectName="SW-CX_YRH.CX_YRH_0816SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="23917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5112.000000 -268.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23918" ObjectName="SW-CX_YRH.CX_YRH_0646SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="23918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5114.000000 -127.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23919" ObjectName="SW-CX_YRH.CX_YRH_0826SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="23919"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3761,-274 3761,80 3930,80 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3761,-274 3761,80 3930,80 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3953,80 4369,80 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3953,80 4369,80 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4392,80 4799,80 4799,179 4800,180 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4392,80 4799,80 4799,179 4800,180 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1e88f00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3430.000000 -389.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e75d10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3558.000000 -388.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ea9910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3686.000000 -388.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1df5040">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -388.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e7ed30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.925566 -420.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e138d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.582524 -449.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f38980">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4472.000000 -421.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e39150">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4569.000000 -421.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e182a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4662.000000 -390.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e5b1b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4790.000000 -390.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1db02a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4918.000000 -390.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e0e590">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -390.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e4ebd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3694.000000 -868.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e4fb80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3696.000000 -964.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1def100">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3773.000000 -652.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d13400">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4238.000000 -734.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d14970">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4239.000000 -795.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e2a4b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -1117.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d89c00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4833.000000 -868.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d8acb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4835.000000 -964.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d92fb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4909.000000 -653.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d61b20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -315.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d73730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -250.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d75150">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -194.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d797b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -111.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d7c9b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -317.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d80660">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -252.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d81810">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -196.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d18730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -113.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-0" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.000000 -1169.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,113 3927,112 3927,111 3928,110 3928,109 3929,108 3930,107 3931,107 3932,106 3933,105 3934,105 3936,105 3937,105 3939,105 3940,105 3942,105 3943,105 3944,106 3945,107 3946,107 3947,108 3948,109 3948,110 3949,111 3949,112 3949,113 " stroke="rgb(0,255,0)" stroke-width="0.229412"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,81 4368,80 4368,79 4369,78 4369,77 4370,76 4371,75 4372,75 4374,74 4375,73 4377,73 4378,73 4380,73 4381,73 4383,73 4384,73 4386,73 4387,74 4389,75 4390,75 4391,76 4392,77 4392,78 4393,79 4393,80 4393,81 " stroke="rgb(0,255,0)" stroke-width="0.229412"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3930,80 3930,79 3930,78 3931,77 3931,76 3932,75 3933,74 3934,74 3935,73 3936,72 3937,72 3939,72 3940,72 3942,72 3943,72 3945,72 3946,72 3947,73 3948,74 3949,74 3950,75 3951,76 3951,77 3952,78 3952,79 3952,80 " stroke="rgb(0,255,0)" stroke-width="0.229412"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1deb870">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -720.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d8faf0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.000000 -721.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dba990">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.000000 -387.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dbd2e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -698.000000)" xlink:href="#voltageTransformer:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dbf880">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3611.000000 -696.000000)" xlink:href="#voltageTransformer:shape107"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dc1e20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 -696.000000)" xlink:href="#voltageTransformer:shape107"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3420.500000 -1201.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127081" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3643.000000 -1135.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127081" ObjectName="CX_YRH:CX_YRH_Zyb3_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127082" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3643.000000 -1120.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127082" ObjectName="CX_YRH:CX_YRH_Zyb3_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127080" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3643.000000 -1105.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127080" ObjectName="CX_YRH:CX_YRH_Zyb3_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127085" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4784.000000 -1133.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127085" ObjectName="CX_YRH:CX_YRH_Zyb4_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127086" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4784.000000 -1118.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127086" ObjectName="CX_YRH:CX_YRH_Zyb4_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127084" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4784.000000 -1103.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127084" ObjectName="CX_YRH:CX_YRH_Zyb4_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127089" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4248.000000 -1160.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127089" ObjectName="CX_YRH:CX_YRH_Zyb5_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127090" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4248.000000 -1145.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127090" ObjectName="CX_YRH:CX_YRH_Zyb5_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127088" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4248.000000 -1130.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127088" ObjectName="CX_YRH:CX_YRH_Zyb5_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127083" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3483.000000 -617.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127083" ObjectName="CX_YRH:CX_YRH_Zyb3_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127091" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4361.000000 -617.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127091" ObjectName="CX_YRH:CX_YRH_Zyb5_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127087" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -617.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127087" ObjectName="CX_YRH:CX_YRH_Zyb4_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127083" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5585.000000 -367.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127083" ObjectName="CX_YRH:CX_YRH_Zyb3_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127084" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5587.000000 -281.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127084" ObjectName="CX_YRH:CX_YRH_Zyb4_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127085" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5587.000000 -233.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127085" ObjectName="CX_YRH:CX_YRH_Zyb4_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127080" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5585.000000 -513.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127080" ObjectName="CX_YRH:CX_YRH_Zyb3_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127081" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5585.000000 -468.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127081" ObjectName="CX_YRH:CX_YRH_Zyb3_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127082" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5585.000000 -417.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127082" ObjectName="CX_YRH:CX_YRH_Zyb3_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127086" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5587.000000 -185.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127086" ObjectName="CX_YRH:CX_YRH_Zyb4_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127087" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5587.000000 -134.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127087" ObjectName="CX_YRH:CX_YRH_Zyb4_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127088" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5576.000000 -43.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127088" ObjectName="CX_YRH:CX_YRH_Zyb5_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127089" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5576.000000 5.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127089" ObjectName="CX_YRH:CX_YRH_Zyb5_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127090" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5576.000000 53.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127090" ObjectName="CX_YRH:CX_YRH_Zyb5_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127091" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5578.000000 104.000000) translate(0,18)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127091" ObjectName="CX_YRH:CX_YRH_Zyb5_Uab"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3331" y="-1281"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="28" stroke="rgb(60,120,255)" stroke-width="1" width="11" x="4251" y="-482"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="28" stroke="rgb(0,255,0)" stroke-width="1" width="11" x="3613" y="-791"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="28" stroke="rgb(60,120,255)" stroke-width="1" width="11" x="3806" y="-706"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="11" stroke="rgb(0,255,0)" stroke-width="1" width="28" x="4266" y="-712"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="28" stroke="rgb(0,255,0)" stroke-width="1" width="11" x="4752" y="-791"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="28" stroke="rgb(60,120,255)" stroke-width="1" width="11" x="4942" y="-707"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="235" stroke="rgb(0,238,0)" stroke-width="0.440075" width="479" x="5248" y="-572"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="235" stroke="rgb(0,238,0)" stroke-width="0.440075" width="479" x="5248" y="-337"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="235" stroke="rgb(0,238,0)" stroke-width="0.440075" width="479" x="5249" y="-103"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1de5ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3462.500000 -1249.500000) translate(0,16)">永仁换流站站用变一次接线图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1df6830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3519.000000 -417.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1df6b60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.000000 -448.000000) translate(0,15)">极</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1df6b60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.000000 -448.000000) translate(0,33)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1df6b60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.000000 -448.000000) translate(0,51)"/>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1df6b60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.000000 -448.000000) translate(0,69)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1df6b60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.000000 -448.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1df6b60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.000000 -448.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebcbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3652.000000 -415.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebced0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -430.000000) translate(0,15)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebced0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -430.000000) translate(0,33)">公</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebced0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -430.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebced0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -430.000000) translate(0,69)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebced0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -430.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebd080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 -450.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebe420" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,15)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebe420" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,33)">外</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebe420" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,51)">泵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebe420" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,69)">房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebe420" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,87)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebe420" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,105)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ebe420" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,123)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e7faa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4063.000000 -408.000000) translate(0,15)">Ⅰ Ⅲ母联线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e802c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4063.000000 -352.000000) translate(0,15)">Ⅰ Ⅱ母联线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e2fa10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4253.000000 -396.000000) translate(0,15)">10kVⅢ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e21970" transform="matrix(1.000000 0.000000 0.000000 1.000000 4228.000000 -377.000000) translate(0,15)">母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dd6650" transform="matrix(1.000000 0.000000 0.000000 1.000000 4419.000000 -408.000000) translate(0,15)">Ⅱ Ⅲ母联线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e38bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4213.000000 -529.000000) translate(0,12)">0903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db11f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5005.000000 -448.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db11f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5005.000000 -448.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db11f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5005.000000 -448.000000) translate(0,51)">公</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db11f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5005.000000 -448.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db11f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5005.000000 -448.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db11f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5005.000000 -448.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f280" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,15)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f280" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,33)">外</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f280" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,51)">泵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f280" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,69)">房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f280" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,87)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f280" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,105)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f280" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,123)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f890" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -448.000000) translate(0,15)">极</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f890" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -448.000000) translate(0,33)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f890" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -448.000000) translate(0,51)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f890" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -448.000000) translate(0,69)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f890" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -448.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0f890" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -448.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0fc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4876.000000 -448.000000) translate(0,15)">极</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0fc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4876.000000 -448.000000) translate(0,33)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0fc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4876.000000 -448.000000) translate(0,51)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0fc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4876.000000 -448.000000) translate(0,69)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0fc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4876.000000 -448.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0fc30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4876.000000 -448.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e0ff10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3430.000000 -592.000000) translate(0,15)">10kV Ⅰ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e636a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -448.000000) translate(0,15)">极</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e636a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -448.000000) translate(0,33)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e636a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -448.000000) translate(0,51)"/>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e636a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -448.000000) translate(0,69)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e636a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -448.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e636a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -448.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e63cb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -1096.000000) translate(0,15)">1号站用变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e63cb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -1096.000000) translate(0,33)">油浸自冷式（ONAN）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e63cb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -1096.000000) translate(0,51)">36±4×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e63cb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -1096.000000) translate(0,69)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e63cb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -1096.000000) translate(0,87)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e63cb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -1096.000000) translate(0,105)">Uk%=7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e67010" transform="matrix(1.000000 0.000000 0.000000 1.000000 3595.000000 -1181.000000) translate(0,15)">引自1号站用变35kV低压侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dd9560" transform="matrix(1.000000 0.000000 0.000000 1.000000 3534.000000 -1059.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dd9990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3631.000000 -837.000000) translate(0,12)">00167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dd9f60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3585.000000 -729.000000) translate(0,15)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dda6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3826.000000 -625.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dda980" transform="matrix(1.000000 0.000000 0.000000 1.000000 3753.000000 -822.000000) translate(0,15)">10kVⅠ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ddabc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3753.000000 -802.000000) translate(0,15)">母线电压</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ddae00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3762.000000 -778.000000) translate(0,15)">互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d143e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4341.000000 -696.000000) translate(0,15)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e2be30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4126.000000 -1193.000000) translate(0,15)">引自220kV方山变35kV间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d65b70" transform="matrix(1.000000 0.000000 0.000000 1.000000 4277.000000 -924.000000) translate(0,15)">3号站用变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d65b70" transform="matrix(1.000000 0.000000 0.000000 1.000000 4277.000000 -924.000000) translate(0,33)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d65b70" transform="matrix(1.000000 0.000000 0.000000 1.000000 4277.000000 -924.000000) translate(0,51)">35±4×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d65b70" transform="matrix(1.000000 0.000000 0.000000 1.000000 4277.000000 -924.000000) translate(0,69)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d65b70" transform="matrix(1.000000 0.000000 0.000000 1.000000 4277.000000 -924.000000) translate(0,87)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d65b70" transform="matrix(1.000000 0.000000 0.000000 1.000000 4277.000000 -924.000000) translate(0,105)">Uk%=7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d66f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4301.000000 -592.000000) translate(0,15)">10kV Ⅲ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d93d60" transform="matrix(1.000000 0.000000 0.000000 1.000000 4868.000000 -1096.000000) translate(0,15)">2号站用变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d93d60" transform="matrix(1.000000 0.000000 0.000000 1.000000 4868.000000 -1096.000000) translate(0,33)">油浸自冷式（ONAN）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d93d60" transform="matrix(1.000000 0.000000 0.000000 1.000000 4868.000000 -1096.000000) translate(0,51)">36±4×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d93d60" transform="matrix(1.000000 0.000000 0.000000 1.000000 4868.000000 -1096.000000) translate(0,69)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d93d60" transform="matrix(1.000000 0.000000 0.000000 1.000000 4868.000000 -1096.000000) translate(0,87)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d93d60" transform="matrix(1.000000 0.000000 0.000000 1.000000 4868.000000 -1096.000000) translate(0,105)">Uk%=7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d94310" transform="matrix(1.000000 0.000000 0.000000 1.000000 4734.000000 -1181.000000) translate(0,15)">引自2号站用变35kV低压侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d94570" transform="matrix(1.000000 0.000000 0.000000 1.000000 4673.000000 -1059.000000) translate(0,15)">35kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d947b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4724.000000 -729.000000) translate(0,15)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d949e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4962.000000 -626.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d94d20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4889.000000 -823.000000) translate(0,15)">10kVⅡ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d95180" transform="matrix(1.000000 0.000000 0.000000 1.000000 4889.000000 -803.000000) translate(0,15)">母线电压</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d953c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4898.000000 -779.000000) translate(0,15)">互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d97f50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3447.000000 -326.000000) translate(0,15)">极一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d98400" transform="matrix(1.000000 0.000000 0.000000 1.000000 3451.500000 -306.000000) translate(0,15)">1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d98640" transform="matrix(1.000000 0.000000 0.000000 1.000000 3438.000000 -288.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d98880" transform="matrix(1.000000 0.000000 0.000000 1.000000 3572.000000 -324.000000) translate(0,15)">极二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d98ac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3576.500000 -304.000000) translate(0,15)">1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d98d00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3563.000000 -286.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d98f40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3705.000000 -327.000000) translate(0,15)">1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d99180" transform="matrix(1.000000 0.000000 0.000000 1.000000 3700.500000 -307.000000) translate(0,15)">公用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db7380" transform="matrix(1.000000 0.000000 0.000000 1.000000 3691.500000 -289.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db7590" transform="matrix(1.000000 0.000000 0.000000 1.000000 4671.000000 -328.000000) translate(0,15)">极一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db77d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4675.500000 -308.000000) translate(0,15)">2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db7a10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4662.000000 -290.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db7c50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4806.000000 -330.000000) translate(0,15)">极二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db7e90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4810.500000 -310.000000) translate(0,15)">2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db80d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4797.000000 -292.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db8310" transform="matrix(1.000000 0.000000 0.000000 1.000000 4939.000000 -327.000000) translate(0,15)">2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db8550" transform="matrix(1.000000 0.000000 0.000000 1.000000 4934.500000 -307.000000) translate(0,15)">公用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db8790" transform="matrix(1.000000 0.000000 0.000000 1.000000 4925.500000 -289.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dba4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -592.000000) translate(0,15)">10kV Ⅱ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4239.000000 -974.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d36f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -1041.000000) translate(0,12)">3111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -1095.000000) translate(0,12)">31117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d373b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4172.000000 -1022.000000) translate(0,12)">31110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d375f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -956.000000) translate(0,12)">31167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3695.000000 -659.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.000000 -646.000000) translate(0,12)">003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4175.000000 -743.000000) translate(0,12)">00367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d37ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4834.000000 -661.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d38130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -867.000000) translate(0,12)">00267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d38370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3469.000000 -505.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d385b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3455.000000 -451.000000) translate(0,12)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d387f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 -506.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d38a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3583.000000 -451.000000) translate(0,12)">05267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d38c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3727.000000 -506.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d38eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -451.000000) translate(0,12)">05367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d390f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -505.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d39430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3839.000000 -449.000000) translate(0,12)">05467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d39890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.000000 -506.000000) translate(0,12)">013</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d39ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4138.000000 -508.000000) translate(0,12)">0133</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d39d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3934.000000 -511.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d39f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4510.000000 -504.000000) translate(0,12)">023</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3a190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4395.000000 -515.000000) translate(0,12)">0233</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3a3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4653.000000 -507.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3a610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -508.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3a850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -452.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3aa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4877.000000 -508.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3acd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4811.000000 -452.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3af10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5005.000000 -508.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3b150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4939.000000 -452.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3b390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5133.000000 -508.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d3b5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5068.000000 -452.000000) translate(0,12)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d42af0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4108.000000 -1100.000000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d42af0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4108.000000 -1100.000000) translate(0,33)">仁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d42af0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4108.000000 -1100.000000) translate(0,51)">换</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d42af0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4108.000000 -1100.000000) translate(0,69)">流</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d42af0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4108.000000 -1100.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d42af0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4108.000000 -1100.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1da90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -110.000000) translate(0,15)">1号抽水专变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1da90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -110.000000) translate(0,33)">S11-100/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1da90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -110.000000) translate(0,51)">10±2×2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1da90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -110.000000) translate(0,69)">Dyn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1da90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -110.000000) translate(0,87)">Uk%=4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1edc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5054.000000 -8.000000) translate(0,15)">2号抽水专变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1edc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5054.000000 -8.000000) translate(0,33)">S11-100/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1edc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5054.000000 -8.000000) translate(0,51)">10±2×2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1edc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5054.000000 -8.000000) translate(0,69)">Dyn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1edc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5054.000000 -8.000000) translate(0,87)">Uk%=4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1f070" transform="matrix(1.000000 0.000000 0.000000 1.000000 3798.000000 -92.000000) translate(0,15)">10kV1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1f070" transform="matrix(1.000000 0.000000 0.000000 1.000000 3798.000000 -92.000000) translate(0,33)">抽水专变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1f2c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5034.000000 -85.000000) translate(0,15)">10kV2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1f2c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5034.000000 -85.000000) translate(0,33)">抽水专变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1f4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -295.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1f710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -159.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5076.000000 -299.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d1fe40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5076.000000 -165.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d20080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -878.000000) translate(0,12)">35kV_3号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d33fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.000000 218.000000) translate(0,12)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2052730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3905.000000 218.000000) translate(0,12)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2061fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 348.000000) translate(0,12)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2063420" transform="matrix(1.000000 0.000000 0.000000 1.000000 3717.000000 274.000000) translate(0,15)">P1_1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2063f10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3964.000000 274.000000) translate(0,15)">P1_2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_206b550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4276.000000 350.000000) translate(0,12)">434</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_206bb80" transform="matrix(1.000000 0.000000 0.000000 1.000000 4135.000000 274.000000) translate(0,15)">P2_1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_206bdc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4404.000000 276.000000) translate(0,15)">P2_2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_206c000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4203.000000 220.000000) translate(0,12)">403</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2463000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4888.000000 355.000000) translate(0,12)">456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2463630" transform="matrix(1.000000 0.000000 0.000000 1.000000 4737.000000 279.000000) translate(0,15)">CM_1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2463f60" transform="matrix(1.000000 0.000000 0.000000 1.000000 5016.000000 281.000000) translate(0,15)">CM_2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24641b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4812.000000 225.000000) translate(0,12)">405</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2466150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 223.000000) translate(0,12)">404</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2466780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5005.000000 226.000000) translate(0,12)">406</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24669c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 310.000000) translate(0,12)">434</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2466c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4886.000000 315.000000) translate(0,12)">456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2466e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 -509.000000) translate(0,15)">35kV_1号站用变高压侧电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2467810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 -464.000000) translate(0,15)">35kV_1号站用变高压侧有功功率P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_24684e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 -413.000000) translate(0,15)">35kV_1号站用变高压侧无功功率Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2468c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 -277.000000) translate(0,15)">35kV_2号站用变高压侧电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2469110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 -229.000000) translate(0,15)">35kV_2号站用变高压侧有功功率P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2469360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 -363.000000) translate(0,15)">35kV_1号站用变10kV侧进线电压Uab</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2469920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 -181.000000) translate(0,15)">35kV_2号站用变高压侧无功功率Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_246aac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5662.000000 -363.000000) translate(0,15)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_246b100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5669.000000 -277.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_246b770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5664.000000 -229.000000) translate(0,15)">MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_246c1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5657.000000 -509.000000) translate(0,15)">A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_246c810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5657.000000 -464.000000) translate(0,15)">MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_246ce80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5657.000000 -413.000000) translate(0,15)">WVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_246d840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5418.000000 -557.000000) translate(0,15)">1号站用变遥测量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_246f560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5655.000000 -180.000000) translate(0,15)">WVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_246fb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 -130.000000) translate(0,15)">35kV_2号站用变10kV侧进线电压Uab</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2470220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5665.000000 -129.000000) translate(0,15)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2470460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 -39.000000) translate(0,15)">35kV_3号站用变高压侧电流Ia</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_24706a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 9.000000) translate(0,15)">35kV_3号站用变高压侧有功功率P</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_24708f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 57.000000) translate(0,15)">35kV_3号站用变高压侧无功功率Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2470f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5653.000000 -39.000000) translate(0,15)"> A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_24715f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5653.000000 9.000000) translate(0,15)">MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2471c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5644.000000 58.000000) translate(0,15)">WVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2471ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5257.000000 108.000000) translate(0,15)">35kV_3号站用变10kV侧进线电压Uab</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,255)" font-family="SimSun" font-size="18" graphid="g_2472520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5655.000000 109.000000) translate(0,15)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_2472840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5418.000000 -322.000000) translate(0,15)">2号站用变遥测量</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(205,51,51)" font-family="SimSun" font-size="18" graphid="g_2473100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5419.000000 -88.000000) translate(0,15)">3号站用变遥测量</text>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.740000 -0.000000 0.000000 -0.752467 3871.000000 -40.353772)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.740000 -0.000000 0.000000 -0.752467 3871.000000 -40.353772)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.740000 -0.000000 0.000000 -0.752467 5103.000000 -42.353772)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.740000 -0.000000 0.000000 -0.752467 5103.000000 -42.353772)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YRH.CX_YRH_Zyb3">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33339"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 -1003.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.000000 -1003.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23584" ObjectName="TF-CX_YRH.CX_YRH_Zyb3"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YRH.CX_YRH_Zyb4">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33343"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4791.000000 -1002.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4791.000000 -1002.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23585" ObjectName="TF-CX_YRH.CX_YRH_Zyb4"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YRH.CX_YRH_Zyb5">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33347"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4197.000000 -824.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4197.000000 -824.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23586" ObjectName="TF-CX_YRH.CX_YRH_Zyb5"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3492.000000 -262.000000)" xlink:href="#transformer2:shape53_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3492.000000 -262.000000)" xlink:href="#transformer2:shape53_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3620.000000 -263.000000)" xlink:href="#transformer2:shape53_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3620.000000 -263.000000)" xlink:href="#transformer2:shape53_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3748.000000 -260.000000)" xlink:href="#transformer2:shape53_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3748.000000 -260.000000)" xlink:href="#transformer2:shape53_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 -262.000000)" xlink:href="#transformer2:shape53_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 -262.000000)" xlink:href="#transformer2:shape53_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4852.000000 -262.000000)" xlink:href="#transformer2:shape53_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4852.000000 -262.000000)" xlink:href="#transformer2:shape53_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4980.000000 -262.000000)" xlink:href="#transformer2:shape53_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4980.000000 -262.000000)" xlink:href="#transformer2:shape53_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>