<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-99" aopId="256" id="thSvg" viewBox="3122 -1197 2081 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1211" width="2091" x="3117" y="-1202"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3123" y="-1076"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3123" y="-1196"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3123" y="-596"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-76266">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -890.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16564" ObjectName="SW-LF_DDB.LF_DDB_3811SW"/>
     <cge:Meas_Ref ObjectId="76266"/>
    <cge:TPSR_Ref TObjectID="16564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76267">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -982.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16565" ObjectName="SW-LF_DDB.LF_DDB_3816SW"/>
     <cge:Meas_Ref ObjectId="76267"/>
    <cge:TPSR_Ref TObjectID="16565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76294">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.000000 -889.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16568" ObjectName="SW-LF_DDB.LF_DDB_3821SW"/>
     <cge:Meas_Ref ObjectId="76294"/>
    <cge:TPSR_Ref TObjectID="16568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76295">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.000000 -981.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16569" ObjectName="SW-LF_DDB.LF_DDB_3826SW"/>
     <cge:Meas_Ref ObjectId="76295"/>
    <cge:TPSR_Ref TObjectID="16569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76232">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4930.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16563" ObjectName="SW-LF_DDB.LF_DDB_30217SW"/>
     <cge:Meas_Ref ObjectId="76232"/>
    <cge:TPSR_Ref TObjectID="16563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76190">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 -822.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16556" ObjectName="SW-LF_DDB.LF_DDB_3011SW"/>
     <cge:Meas_Ref ObjectId="76190"/>
    <cge:TPSR_Ref TObjectID="16556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76193">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16559" ObjectName="SW-LF_DDB.LF_DDB_30117SW"/>
     <cge:Meas_Ref ObjectId="76193"/>
    <cge:TPSR_Ref TObjectID="16559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76325">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4542.000000 -822.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16571" ObjectName="SW-LF_DDB.LF_DDB_3901SW"/>
     <cge:Meas_Ref ObjectId="76325"/>
    <cge:TPSR_Ref TObjectID="16571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76328">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4589.000000 -762.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16572" ObjectName="SW-LF_DDB.LF_DDB_39017SW"/>
     <cge:Meas_Ref ObjectId="76328"/>
    <cge:TPSR_Ref TObjectID="16572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76268">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3782.000000 -1036.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16566" ObjectName="SW-LF_DDB.LF_DDB_38167SW"/>
     <cge:Meas_Ref ObjectId="76268"/>
    <cge:TPSR_Ref TObjectID="16566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76296">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4936.000000 -1050.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16570" ObjectName="SW-LF_DDB.LF_DDB_3829SW"/>
     <cge:Meas_Ref ObjectId="76296"/>
    <cge:TPSR_Ref TObjectID="16570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76091">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16541" ObjectName="SW-LF_DDB.LF_DDB_0811SW"/>
     <cge:Meas_Ref ObjectId="76091"/>
    <cge:TPSR_Ref TObjectID="16541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76092">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -363.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16542" ObjectName="SW-LF_DDB.LF_DDB_0812SW"/>
     <cge:Meas_Ref ObjectId="76092"/>
    <cge:TPSR_Ref TObjectID="16542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76090">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -231.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16540" ObjectName="SW-LF_DDB.LF_DDB_0815SW"/>
     <cge:Meas_Ref ObjectId="76090"/>
    <cge:TPSR_Ref TObjectID="16540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76093">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16543" ObjectName="SW-LF_DDB.LF_DDB_0816SW"/>
     <cge:Meas_Ref ObjectId="76093"/>
    <cge:TPSR_Ref TObjectID="16543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76348">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4119.000000 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16573" ObjectName="SW-LF_DDB.LF_DDB_0901SW"/>
     <cge:Meas_Ref ObjectId="76348"/>
    <cge:TPSR_Ref TObjectID="16573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76111">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16545" ObjectName="SW-LF_DDB.LF_DDB_0821SW"/>
     <cge:Meas_Ref ObjectId="76111"/>
    <cge:TPSR_Ref TObjectID="16545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76112">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -363.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16546" ObjectName="SW-LF_DDB.LF_DDB_0822SW"/>
     <cge:Meas_Ref ObjectId="76112"/>
    <cge:TPSR_Ref TObjectID="16546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76110">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -231.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16544" ObjectName="SW-LF_DDB.LF_DDB_0825SW"/>
     <cge:Meas_Ref ObjectId="76110"/>
    <cge:TPSR_Ref TObjectID="16544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76113">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4330.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16547" ObjectName="SW-LF_DDB.LF_DDB_0826SW"/>
     <cge:Meas_Ref ObjectId="76113"/>
    <cge:TPSR_Ref TObjectID="16547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16550" ObjectName="SW-LF_DDB.LF_DDB_0832SW"/>
     <cge:Meas_Ref ObjectId="76132"/>
    <cge:TPSR_Ref TObjectID="16550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76130">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 -230.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16548" ObjectName="SW-LF_DDB.LF_DDB_0835SW"/>
     <cge:Meas_Ref ObjectId="76130"/>
    <cge:TPSR_Ref TObjectID="16548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76133">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 -168.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16551" ObjectName="SW-LF_DDB.LF_DDB_0836SW"/>
     <cge:Meas_Ref ObjectId="76133"/>
    <cge:TPSR_Ref TObjectID="16551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76131">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16549" ObjectName="SW-LF_DDB.LF_DDB_0831SW"/>
     <cge:Meas_Ref ObjectId="76131"/>
    <cge:TPSR_Ref TObjectID="16549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76474">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16576" ObjectName="SW-LF_DDB.LF_DDB_0852SW"/>
     <cge:Meas_Ref ObjectId="76474"/>
    <cge:TPSR_Ref TObjectID="16576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76475">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16577" ObjectName="SW-LF_DDB.LF_DDB_0855SW"/>
     <cge:Meas_Ref ObjectId="76475"/>
    <cge:TPSR_Ref TObjectID="16577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76473">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16575" ObjectName="SW-LF_DDB.LF_DDB_0851SW"/>
     <cge:Meas_Ref ObjectId="76473"/>
    <cge:TPSR_Ref TObjectID="16575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76152">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -364.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16554" ObjectName="SW-LF_DDB.LF_DDB_0862SW"/>
     <cge:Meas_Ref ObjectId="76152"/>
    <cge:TPSR_Ref TObjectID="16554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76150">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -232.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16552" ObjectName="SW-LF_DDB.LF_DDB_0865SW"/>
     <cge:Meas_Ref ObjectId="76150"/>
    <cge:TPSR_Ref TObjectID="16552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76153">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 -170.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16555" ObjectName="SW-LF_DDB.LF_DDB_0866SW"/>
     <cge:Meas_Ref ObjectId="76153"/>
    <cge:TPSR_Ref TObjectID="16555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76151">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -470.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16553" ObjectName="SW-LF_DDB.LF_DDB_0861SW"/>
     <cge:Meas_Ref ObjectId="76151"/>
    <cge:TPSR_Ref TObjectID="16553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76231">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5021.000000 -471.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16562" ObjectName="SW-LF_DDB.LF_DDB_0021SW"/>
     <cge:Meas_Ref ObjectId="76231"/>
    <cge:TPSR_Ref TObjectID="16562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 -470.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16558" ObjectName="SW-LF_DDB.LF_DDB_0011SW"/>
     <cge:Meas_Ref ObjectId="76192"/>
    <cge:TPSR_Ref TObjectID="16558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76463">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16574" ObjectName="SW-LF_DDB.LF_DDB_0031SW"/>
     <cge:Meas_Ref ObjectId="76463"/>
    <cge:TPSR_Ref TObjectID="16574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76229">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 -819.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16560" ObjectName="SW-LF_DDB.LF_DDB_3021SW"/>
     <cge:Meas_Ref ObjectId="76229"/>
    <cge:TPSR_Ref TObjectID="16560"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-LF_DDB.LF_DDB_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="23970"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4865.515038 -615.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4865.515038 -615.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="16579" ObjectName="TF-LF_DDB.LF_DDB_2T"/>
    <cge:TPSR_Ref TObjectID="16579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_DDB.LF_DDB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="23966"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.515038 -615.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.515038 -615.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="16578" ObjectName="TF-LF_DDB.LF_DDB_1T"/>
    <cge:TPSR_Ref TObjectID="16578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -405.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -405.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20ae650">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3845.000000 -1083.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b5130">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5027.500000 -1041.500000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b5f20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4854.000000 -1069.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20cef70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -786.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20cfec0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4542.000000 -722.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20d0b40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 -679.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20dbd50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 -286.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e2a00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 -64.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e8250">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 -398.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e8a30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 -371.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e9700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4083.000000 -354.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20f23b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4276.000000 -286.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20fda70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4440.000000 -285.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2104410">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4479.000000 -63.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_210cd70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 -284.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2110ca0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4655.000000 -155.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2114f10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -64.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_211acb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 -287.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2122220">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -65.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2209670">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3561.000000 -388.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_220cc20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5066.000000 -334.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78775" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3924.000000 -975.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78775" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16538"/>
     <cge:Term_Ref ObjectID="6961"/>
    <cge:TPSR_Ref TObjectID="16538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78776" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3924.000000 -975.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16538"/>
     <cge:Term_Ref ObjectID="6961"/>
    <cge:TPSR_Ref TObjectID="16538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78767" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3924.000000 -975.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78767" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16538"/>
     <cge:Term_Ref ObjectID="6961"/>
    <cge:TPSR_Ref TObjectID="16538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78748" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3927.000000 -761.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78748" ObjectName="LF_DDB.LF_DDB_301BK:F"/>
     <cge:PSR_Ref ObjectID="16534"/>
     <cge:Term_Ref ObjectID="16116"/>
    <cge:TPSR_Ref TObjectID="16534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78749" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3927.000000 -761.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78749" ObjectName="LF_DDB.LF_DDB_301BK:F"/>
     <cge:PSR_Ref ObjectID="16534"/>
     <cge:Term_Ref ObjectID="16116"/>
    <cge:TPSR_Ref TObjectID="16534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78734" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3927.000000 -761.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78734" ObjectName="LF_DDB.LF_DDB_301BK:F"/>
     <cge:PSR_Ref ObjectID="16534"/>
     <cge:Term_Ref ObjectID="16116"/>
    <cge:TPSR_Ref TObjectID="16534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78787" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4790.000000 -975.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16539"/>
     <cge:Term_Ref ObjectID="6969"/>
    <cge:TPSR_Ref TObjectID="16539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78788" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4790.000000 -975.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16539"/>
     <cge:Term_Ref ObjectID="6969"/>
    <cge:TPSR_Ref TObjectID="16539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78779" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4790.000000 -975.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78779" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16539"/>
     <cge:Term_Ref ObjectID="6969"/>
    <cge:TPSR_Ref TObjectID="16539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78751" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3513.000000 -452.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78751" ObjectName="LF_DDB.LF_DDB_001BK:F"/>
     <cge:PSR_Ref ObjectID="16535"/>
     <cge:Term_Ref ObjectID="16118"/>
    <cge:TPSR_Ref TObjectID="16535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78752" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3513.000000 -452.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78752" ObjectName="LF_DDB.LF_DDB_001BK:F"/>
     <cge:PSR_Ref ObjectID="16535"/>
     <cge:Term_Ref ObjectID="16118"/>
    <cge:TPSR_Ref TObjectID="16535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78740" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3513.000000 -452.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78740" ObjectName="LF_DDB.LF_DDB_001BK:F"/>
     <cge:PSR_Ref ObjectID="16535"/>
     <cge:Term_Ref ObjectID="16118"/>
    <cge:TPSR_Ref TObjectID="16535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78699" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -334.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78699" ObjectName="LF_DDB.LF_DDB_081BK:F"/>
     <cge:PSR_Ref ObjectID="16530"/>
     <cge:Term_Ref ObjectID="6941"/>
    <cge:TPSR_Ref TObjectID="16530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78700" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -334.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78700" ObjectName="LF_DDB.LF_DDB_081BK:F"/>
     <cge:PSR_Ref ObjectID="16530"/>
     <cge:Term_Ref ObjectID="6941"/>
    <cge:TPSR_Ref TObjectID="16530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78691" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -334.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78691" ObjectName="LF_DDB.LF_DDB_081BK:F"/>
     <cge:PSR_Ref ObjectID="16530"/>
     <cge:Term_Ref ObjectID="6941"/>
    <cge:TPSR_Ref TObjectID="16530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78709" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4384.000000 -334.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78709" ObjectName="LF_DDB.LF_DDB_082BK:F"/>
     <cge:PSR_Ref ObjectID="16531"/>
     <cge:Term_Ref ObjectID="6943"/>
    <cge:TPSR_Ref TObjectID="16531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78710" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4384.000000 -334.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78710" ObjectName="LF_DDB.LF_DDB_082BK:F"/>
     <cge:PSR_Ref ObjectID="16531"/>
     <cge:Term_Ref ObjectID="6943"/>
    <cge:TPSR_Ref TObjectID="16531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78702" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4384.000000 -334.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78702" ObjectName="LF_DDB.LF_DDB_082BK:F"/>
     <cge:PSR_Ref ObjectID="16531"/>
     <cge:Term_Ref ObjectID="6943"/>
    <cge:TPSR_Ref TObjectID="16531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78720" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4548.000000 -334.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78720" ObjectName="LF_DDB.LF_DDB_083BK:F"/>
     <cge:PSR_Ref ObjectID="16532"/>
     <cge:Term_Ref ObjectID="6945"/>
    <cge:TPSR_Ref TObjectID="16532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78721" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4548.000000 -334.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78721" ObjectName="LF_DDB.LF_DDB_083BK:F"/>
     <cge:PSR_Ref ObjectID="16532"/>
     <cge:Term_Ref ObjectID="6945"/>
    <cge:TPSR_Ref TObjectID="16532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78712" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4548.000000 -334.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78712" ObjectName="LF_DDB.LF_DDB_083BK:F"/>
     <cge:PSR_Ref ObjectID="16532"/>
     <cge:Term_Ref ObjectID="6945"/>
    <cge:TPSR_Ref TObjectID="16532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78688" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4701.000000 -334.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78688" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16528"/>
     <cge:Term_Ref ObjectID="6933"/>
    <cge:TPSR_Ref TObjectID="16528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78689" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4701.000000 -334.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78689" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16528"/>
     <cge:Term_Ref ObjectID="6933"/>
    <cge:TPSR_Ref TObjectID="16528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78680" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4701.000000 -334.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16528"/>
     <cge:Term_Ref ObjectID="6933"/>
    <cge:TPSR_Ref TObjectID="16528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78731" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4859.000000 -331.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78731" ObjectName="LF_DDB.LF_DDB_086BK:F"/>
     <cge:PSR_Ref ObjectID="16533"/>
     <cge:Term_Ref ObjectID="6953"/>
    <cge:TPSR_Ref TObjectID="16533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78732" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4859.000000 -331.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78732" ObjectName="LF_DDB.LF_DDB_086BK:F"/>
     <cge:PSR_Ref ObjectID="16533"/>
     <cge:Term_Ref ObjectID="6953"/>
    <cge:TPSR_Ref TObjectID="16533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78723" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4859.000000 -331.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78723" ObjectName="LF_DDB.LF_DDB_086BK:F"/>
     <cge:PSR_Ref ObjectID="16533"/>
     <cge:Term_Ref ObjectID="6953"/>
    <cge:TPSR_Ref TObjectID="16533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78764" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5178.000000 -450.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78764" ObjectName="LF_DDB.LF_DDB_002BK:F"/>
     <cge:PSR_Ref ObjectID="16537"/>
     <cge:Term_Ref ObjectID="6959"/>
    <cge:TPSR_Ref TObjectID="16537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78765" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5178.000000 -450.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78765" ObjectName="LF_DDB.LF_DDB_002BK:F"/>
     <cge:PSR_Ref ObjectID="16537"/>
     <cge:Term_Ref ObjectID="6959"/>
    <cge:TPSR_Ref TObjectID="16537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78808" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5178.000000 -450.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78808" ObjectName="LF_DDB.LF_DDB_002BK:F"/>
     <cge:PSR_Ref ObjectID="16537"/>
     <cge:Term_Ref ObjectID="6959"/>
    <cge:TPSR_Ref TObjectID="16537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-78761" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4790.000000 -761.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78761" ObjectName="LF_DDB.LF_DDB_302BK:F"/>
     <cge:PSR_Ref ObjectID="16536"/>
     <cge:Term_Ref ObjectID="6957"/>
    <cge:TPSR_Ref TObjectID="16536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-78762" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4790.000000 -761.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78762" ObjectName="LF_DDB.LF_DDB_302BK:F"/>
     <cge:PSR_Ref ObjectID="16536"/>
     <cge:Term_Ref ObjectID="6957"/>
    <cge:TPSR_Ref TObjectID="16536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-78754" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4790.000000 -761.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78754" ObjectName="LF_DDB.LF_DDB_302BK:F"/>
     <cge:PSR_Ref ObjectID="16536"/>
     <cge:Term_Ref ObjectID="6957"/>
    <cge:TPSR_Ref TObjectID="16536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-78794" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3715.000000 -912.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78794" ObjectName="LF_DDB.LF_DDB_35M:F"/>
     <cge:PSR_Ref ObjectID="17360"/>
     <cge:Term_Ref ObjectID="23899"/>
    <cge:TPSR_Ref TObjectID="17360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-78802" prefix="">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5119.000000 -569.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17361"/>
     <cge:Term_Ref ObjectID="23900"/>
    <cge:TPSR_Ref TObjectID="17361"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">大德变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3739.000000 -1140.000000) translate(0,18)">上大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3782.000000 -967.000000) translate(0,15)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4897.000000 -1140.000000) translate(0,18)">35kV大九线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4937.000000 -966.000000) translate(0,15)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5030.000000 -1066.000000) translate(0,18)">35kV线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4852.000000 -761.000000) translate(0,15)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4928.000000 -669.000000) translate(0,18)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3702.000000 -761.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3778.000000 -669.000000) translate(0,18)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4354.000000 -916.000000) translate(0,18)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3519.000000 -563.000000) translate(0,18)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3794.000000 -55.000000) translate(0,18)">路溪线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3922.000000 -395.000000) translate(0,18)">10kV站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4079.000000 -334.000000) translate(0,18)">10kV线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4296.000000 -449.000000) translate(0,15)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4261.000000 -55.000000) translate(0,18)">库区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4407.000000 -55.000000) translate(0,18)">卸油泵房线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4619.000000 -449.000000) translate(0,15)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4584.000000 -124.000000) translate(0,18)">旁路母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4779.000000 -449.000000) translate(0,15)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4744.000000 -55.000000) translate(0,18)">和平线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5048.000000 -445.000000) translate(0,15)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3532.000000 -180.000000) translate(0,18)">10kV旁路母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3778.000000 -920.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3778.000000 -1012.000000) translate(0,15)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3785.000000 -1067.000000) translate(0,15)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4558.000000 -851.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4605.000000 -792.000000) translate(0,15)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4933.000000 -919.000000) translate(0,15)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4933.000000 -1011.000000) translate(0,15)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4942.000000 -1081.000000) translate(0,15)">3829</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4898.000000 -849.000000) translate(0,15)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4946.000000 -774.000000) translate(0,15)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5037.000000 -501.000000) translate(0,15)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4771.000000 -500.000000) translate(0,15)">0861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4771.000000 -394.000000) translate(0,15)">0862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4771.000000 -262.000000) translate(0,15)">0865</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4611.000000 -497.000000) translate(0,15)">0851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4611.000000 -391.000000) translate(0,15)">0852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4611.000000 -259.000000) translate(0,15)">0855</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4460.000000 -449.000000) translate(0,15)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4452.000000 -498.000000) translate(0,15)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4452.000000 -392.000000) translate(0,15)">0832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4452.000000 -260.000000) translate(0,15)">0835</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4510.000000 -198.000000) translate(0,15)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4829.000000 -200.000000) translate(0,15)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4288.000000 -499.000000) translate(0,15)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4288.000000 -393.000000) translate(0,15)">0822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4288.000000 -261.000000) translate(0,15)">0825</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4346.000000 -199.000000) translate(0,15)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4135.000000 -499.000000) translate(0,15)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3974.000000 -499.000000) translate(0,15)">0031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3823.000000 -446.000000) translate(0,15)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3821.000000 -499.000000) translate(0,15)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3821.000000 -393.000000) translate(0,15)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3821.000000 -261.000000) translate(0,15)">0815</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3879.000000 -199.000000) translate(0,15)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3625.000000 -442.000000) translate(0,15)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3622.000000 -500.000000) translate(0,15)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3749.000000 -852.000000) translate(0,15)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3796.000000 -774.000000) translate(0,15)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 0.000000 0.000000 1.000000 4776.000000 -318.000000) translate(0,16)">Q(MVar):</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-78618">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -937.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16538" ObjectName="SW-LF_DDB.LF_DDB_381BK"/>
     <cge:Meas_Ref ObjectId="78618"/>
    <cge:TPSR_Ref TObjectID="16538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76044">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.000000 -936.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16539" ObjectName="SW-LF_DDB.LF_DDB_382BK"/>
     <cge:Meas_Ref ObjectId="76044"/>
    <cge:TPSR_Ref TObjectID="16539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76041">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4881.515038 -730.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16536" ObjectName="SW-LF_DDB.LF_DDB_302BK"/>
     <cge:Meas_Ref ObjectId="76041"/>
    <cge:TPSR_Ref TObjectID="16536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76039">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.515038 -730.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16534" ObjectName="SW-LF_DDB.LF_DDB_301BK"/>
     <cge:Meas_Ref ObjectId="76039"/>
    <cge:TPSR_Ref TObjectID="16534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76040">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3605.515038 -414.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16535" ObjectName="SW-LF_DDB.LF_DDB_001BK"/>
     <cge:Meas_Ref ObjectId="76040"/>
    <cge:TPSR_Ref TObjectID="16535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76035">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.515038 -417.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16530" ObjectName="SW-LF_DDB.LF_DDB_081BK"/>
     <cge:Meas_Ref ObjectId="76035"/>
    <cge:TPSR_Ref TObjectID="16530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76036">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.515038 -417.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16531" ObjectName="SW-LF_DDB.LF_DDB_082BK"/>
     <cge:Meas_Ref ObjectId="76036"/>
    <cge:TPSR_Ref TObjectID="16531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76037">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.515038 -416.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16532" ObjectName="SW-LF_DDB.LF_DDB_083BK"/>
     <cge:Meas_Ref ObjectId="76037"/>
    <cge:TPSR_Ref TObjectID="16532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76032">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.515038 -415.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16528" ObjectName="SW-LF_DDB.LF_DDB_085BK"/>
     <cge:Meas_Ref ObjectId="76032"/>
    <cge:TPSR_Ref TObjectID="16528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76038">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4754.515038 -418.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16533" ObjectName="SW-LF_DDB.LF_DDB_086BK"/>
     <cge:Meas_Ref ObjectId="76038"/>
    <cge:TPSR_Ref TObjectID="16533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76042">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5020.515038 -415.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16537" ObjectName="SW-LF_DDB.LF_DDB_002BK"/>
     <cge:Meas_Ref ObjectId="76042"/>
    <cge:TPSR_Ref TObjectID="16537"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20ba220">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4928.515038 -710.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c2150">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3778.515038 -710.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c9040">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4588.515038 -728.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21fd5b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 -1035.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer"/><g id="MotifButton_Layer">
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3853.000000 760.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.000000 730.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3838.000000 745.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3847.000000 333.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 303.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3832.000000 318.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4309.000000 333.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 303.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 318.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4475.000000 333.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4491.000000 303.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4460.000000 318.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 333.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4645.000000 303.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 318.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4803.000000 299.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 329.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5124.000000 416.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5108.000000 446.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5093.000000 431.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5026.000000 568.000000) translate(0,12)">Uab（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 974.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 944.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 959.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3458.000000 420.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3442.000000 450.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3427.000000 435.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.000000 944.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3838.000000 959.000000) translate(0,16)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3853.000000 974.000000) translate(0,16)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4735.000000 730.000000) translate(0,16)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 760.000000) translate(0,16)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 745.000000) translate(0,16)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -7.000000 -0.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3630.000000 910.000000) translate(0,12)">Uab（kV）：</text>
   <metadata/></g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2054220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-878 3771,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17360@0" ObjectIDZND0="16564@0" Pin0InfoVect0LinkObjId="SW-76266_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-878 3771,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f3d3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-931 3771,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16564@1" ObjectIDZND0="16538@0" Pin0InfoVect0LinkObjId="SW-78618_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76266_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-931 3771,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ae460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-972 3771,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16538@1" ObjectIDZND0="16565@0" Pin0InfoVect0LinkObjId="SW-76267_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78618_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-972 3771,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b2260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-878 4926,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17360@0" ObjectIDZND0="16568@0" Pin0InfoVect0LinkObjId="SW-76294_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-878 4926,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b35c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-930 4926,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16568@1" ObjectIDZND0="16539@0" Pin0InfoVect0LinkObjId="SW-76044_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76294_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-930 4926,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b4f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-971 4926,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16539@1" ObjectIDZND0="16569@0" Pin0InfoVect0LinkObjId="SW-76295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-971 4926,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ba990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-798 4939,-798 4939,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="16536@x" ObjectIDND1="16536@x" ObjectIDND2="16560@x" ObjectIDZND0="16563@1" Pin0InfoVect0LinkObjId="SW-76232_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76041_0" Pin1InfoVect1LinkObjId="SW-76041_0" Pin1InfoVect2LinkObjId="SW-76229_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-798 4939,-798 4939,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20bab80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-749 4939,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16563@0" ObjectIDZND0="g_20ba220@0" Pin0InfoVect0LinkObjId="g_20ba220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76232_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-749 4939,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20bbaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-738 4891,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="16536@0" ObjectIDZND0="16579@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76041_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-738 4891,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20be140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-878 3742,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17360@0" ObjectIDZND0="16556@1" Pin0InfoVect0LinkObjId="SW-76190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-878 3742,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c2a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-798 3789,-798 3789,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16534@x" ObjectIDND1="16556@x" ObjectIDZND0="16559@1" Pin0InfoVect0LinkObjId="SW-76193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76039_0" Pin1InfoVect1LinkObjId="SW-76190_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-798 3789,-798 3789,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c2c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-749 3789,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16559@0" ObjectIDZND0="g_20c2150@0" Pin0InfoVect0LinkObjId="g_20c2150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-749 3789,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c3e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-738 3742,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="16534@0" ObjectIDZND0="16578@1" Pin0InfoVect0LinkObjId="g_22068e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76039_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-738 3742,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c6a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-878 4551,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17360@0" ObjectIDZND0="16571@1" Pin0InfoVect0LinkObjId="SW-76325_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-878 4551,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c6c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-827 4551,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="16571@0" ObjectIDZND0="g_20cef70@0" ObjectIDZND1="g_20cfec0@0" ObjectIDZND2="16572@x" Pin0InfoVect0LinkObjId="g_20cef70_0" Pin0InfoVect1LinkObjId="g_20cfec0_0" Pin0InfoVect2LinkObjId="SW-76328_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-827 4551,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c9930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-816 4598,-816 4598,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_20cef70@0" ObjectIDND1="g_20cfec0@0" ObjectIDND2="16571@x" ObjectIDZND0="16572@1" Pin0InfoVect0LinkObjId="SW-76328_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20cef70_0" Pin1InfoVect1LinkObjId="g_20cfec0_0" Pin1InfoVect2LinkObjId="SW-76325_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-816 4598,-816 4598,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c9b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4598,-767 4598,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16572@0" ObjectIDZND0="g_20c9040@0" Pin0InfoVect0LinkObjId="g_20c9040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76328_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4598,-767 4598,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ca280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-1023 3771,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="16565@1" ObjectIDZND0="16566@x" ObjectIDZND1="g_20ae650@0" Pin0InfoVect0LinkObjId="SW-76268_0" Pin0InfoVect1LinkObjId="g_20ae650_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76267_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-1023 3771,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cbe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-1041 3787,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="16565@x" ObjectIDND1="g_20ae650@0" ObjectIDZND0="16566@0" Pin0InfoVect0LinkObjId="SW-76268_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76267_0" Pin1InfoVect1LinkObjId="g_20ae650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-1041 3787,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cc0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-1041 3837,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16566@1" ObjectIDZND0="g_21fd5b0@0" Pin0InfoVect0LinkObjId="g_21fd5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76268_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-1041 3837,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cc320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-1022 4926,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="16569@1" ObjectIDZND0="16570@x" ObjectIDZND1="g_20b5f20@0" Pin0InfoVect0LinkObjId="SW-76296_0" Pin0InfoVect1LinkObjId="g_20b5f20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-1022 4926,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ce850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5003,-1055 4977,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_20b5130@0" ObjectIDZND0="16570@1" Pin0InfoVect0LinkObjId="SW-76296_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20b5130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5003,-1055 4977,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ceab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4941,-1055 4926,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="16570@0" ObjectIDZND0="16569@x" ObjectIDZND1="g_20b5f20@0" Pin0InfoVect0LinkObjId="SW-76295_0" Pin0InfoVect1LinkObjId="g_20b5f20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4941,-1055 4926,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ced10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-793 4539,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16571@x" ObjectIDND1="16572@x" ObjectIDND2="g_20cfec0@0" ObjectIDZND0="g_20cef70@0" Pin0InfoVect0LinkObjId="g_20cef70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76325_0" Pin1InfoVect1LinkObjId="SW-76328_0" Pin1InfoVect2LinkObjId="g_20cfec0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-793 4539,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cfc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-816 4551,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="16571@x" ObjectIDND1="16572@x" ObjectIDZND0="g_20cef70@0" ObjectIDZND1="g_20cfec0@0" Pin0InfoVect0LinkObjId="g_20cef70_0" Pin0InfoVect1LinkObjId="g_20cfec0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76325_0" Pin1InfoVect1LinkObjId="SW-76328_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-816 4551,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d0680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-792 4551,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16571@x" ObjectIDND1="16572@x" ObjectIDND2="g_20cef70@0" ObjectIDZND0="g_20cfec0@1" Pin0InfoVect0LinkObjId="g_20cfec0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76325_0" Pin1InfoVect1LinkObjId="SW-76328_0" Pin1InfoVect2LinkObjId="g_20cef70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-792 4551,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d08e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-727 4551,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_20cfec0@0" ObjectIDZND0="g_20d0b40@0" Pin0InfoVect0LinkObjId="g_20d0b40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20cfec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-727 4551,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d2210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="16536@x" ObjectIDND1="16560@x" ObjectIDND2="16563@x" ObjectIDZND0="16536@x" ObjectIDZND1="16560@x" ObjectIDZND2="16563@x" Pin0InfoVect0LinkObjId="SW-76041_0" Pin0InfoVect1LinkObjId="SW-76229_0" Pin0InfoVect2LinkObjId="SW-76232_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76041_0" Pin1InfoVect1LinkObjId="SW-76229_0" Pin1InfoVect2LinkObjId="SW-76232_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d2470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-798 4891,-765 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="16536@x" ObjectIDND1="16560@x" ObjectIDND2="16563@x" ObjectIDZND0="16536@1" Pin0InfoVect0LinkObjId="SW-76041_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76041_0" Pin1InfoVect1LinkObjId="SW-76229_0" Pin1InfoVect2LinkObjId="SW-76232_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-798 4891,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d26d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-798 3742,-765 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16556@x" ObjectIDND1="16559@x" ObjectIDZND0="16534@1" Pin0InfoVect0LinkObjId="SW-76039_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76190_0" Pin1InfoVect1LinkObjId="SW-76193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-798 3742,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d2930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-827 3742,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16556@0" ObjectIDZND0="16534@x" ObjectIDZND1="16559@x" Pin0InfoVect0LinkObjId="SW-76039_0" Pin0InfoVect1LinkObjId="SW-76193_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-827 3742,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d2b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-878 4891,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17360@0" ObjectIDZND0="16560@1" Pin0InfoVect0LinkObjId="SW-76229_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-878 4891,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d2df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-824 4891,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="16560@0" ObjectIDZND0="16536@x" ObjectIDZND1="16536@x" ObjectIDZND2="16560@x" Pin0InfoVect0LinkObjId="SW-76041_0" Pin0InfoVect1LinkObjId="SW-76041_0" Pin0InfoVect2LinkObjId="SW-76229_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76229_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-824 4891,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d4b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-529 3615,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16558@1" Pin0InfoVect0LinkObjId="SW-76192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2128c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-529 3615,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d4df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-475 3615,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16558@0" ObjectIDZND0="16535@1" Pin0InfoVect0LinkObjId="SW-76040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-475 3615,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d75a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-529 3814,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16541@1" Pin0InfoVect0LinkObjId="SW-76091_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2128c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-529 3814,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d9340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-474 3814,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16541@0" ObjectIDZND0="16530@1" Pin0InfoVect0LinkObjId="SW-76035_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76091_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-474 3814,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20dbaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-425 3814,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16530@0" ObjectIDZND0="16542@1" Pin0InfoVect0LinkObjId="SW-76092_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76035_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-425 3814,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20dc9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-368 3814,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16542@0" ObjectIDZND0="g_20dbd50@0" Pin0InfoVect0LinkObjId="g_20dbd50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76092_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-368 3814,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20df170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-291 3814,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_20dbd50@1" ObjectIDZND0="16540@1" Pin0InfoVect0LinkObjId="SW-76090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dbd50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-291 3814,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20df630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-236 3814,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16540@0" ObjectIDZND0="g_20e2a00@0" ObjectIDZND1="16543@x" Pin0InfoVect0LinkObjId="g_20e2a00_0" Pin0InfoVect1LinkObjId="SW-76093_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-236 3814,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e1e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-224 3872,-224 3872,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_20e2a00@0" ObjectIDND1="16540@x" ObjectIDZND0="16543@1" Pin0InfoVect0LinkObjId="SW-76093_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20e2a00_0" Pin1InfoVect1LinkObjId="SW-76090_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-224 3872,-224 3872,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e2080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-174 3872,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16543@0" ObjectIDZND0="17373@0" Pin0InfoVect0LinkObjId="g_20f86b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76093_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-174 3872,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e22e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-137 3855,-137 3855,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16540@x" ObjectIDND1="16543@x" ObjectIDZND0="g_20e2a00@0" Pin0InfoVect0LinkObjId="g_20e2a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76090_0" Pin1InfoVect1LinkObjId="SW-76093_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-137 3855,-137 3855,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e2540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-224 3814,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16540@x" ObjectIDND1="16543@x" ObjectIDZND0="g_20e2a00@0" Pin0InfoVect0LinkObjId="g_20e2a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76090_0" Pin1InfoVect1LinkObjId="SW-76093_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-224 3814,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e27a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-137 3814,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="16540@x" ObjectIDND1="16543@x" ObjectIDND2="g_20e2a00@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76090_0" Pin1InfoVect1LinkObjId="SW-76093_0" Pin1InfoVect2LinkObjId="g_20e2a00_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-137 3814,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20af320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-529 3967,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16574@1" Pin0InfoVect0LinkObjId="SW-76463_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2128c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-529 3967,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20af510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-474 3967,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="16574@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76463_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-474 3967,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e7b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4128,-529 4128,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16573@1" Pin0InfoVect0LinkObjId="SW-76348_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2128c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4128,-529 4128,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e7d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4128,-474 4128,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="16573@0" ObjectIDZND0="g_20e8a30@0" ObjectIDZND1="g_20e8250@0" Pin0InfoVect0LinkObjId="g_20e8a30_0" Pin0InfoVect1LinkObjId="g_20e8250_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76348_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4128,-474 4128,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20e7ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4128,-456 4171,-456 4171,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_20e8250@0" ObjectIDND1="16573@x" ObjectIDZND0="g_20e8a30@0" Pin0InfoVect0LinkObjId="g_20e8a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20e8250_0" Pin1InfoVect1LinkObjId="SW-76348_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4128,-456 4171,-456 4171,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20eadf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-378 4096,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_20e9700@0" ObjectIDZND0="g_20e8250@0" Pin0InfoVect0LinkObjId="g_20e8250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e9700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-378 4096,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20eb030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-434 4096,-456 4128,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_20e8250@1" ObjectIDZND0="g_20e8a30@0" ObjectIDZND1="16573@x" Pin0InfoVect0LinkObjId="g_20e8a30_0" Pin0InfoVect1LinkObjId="SW-76348_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e8250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-434 4096,-456 4128,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20edad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-529 4281,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16545@1" Pin0InfoVect0LinkObjId="SW-76111_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2128c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-529 4281,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ef980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-474 4281,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16545@0" ObjectIDZND0="16531@1" Pin0InfoVect0LinkObjId="SW-76036_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76111_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-474 4281,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f2150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-425 4281,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16531@0" ObjectIDZND0="16546@1" Pin0InfoVect0LinkObjId="SW-76112_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76036_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-425 4281,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f2fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-368 4281,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16546@0" ObjectIDZND0="g_20f23b0@0" Pin0InfoVect0LinkObjId="g_20f23b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-368 4281,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f5a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-291 4281,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_20f23b0@1" ObjectIDZND0="16544@1" Pin0InfoVect0LinkObjId="SW-76110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20f23b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-291 4281,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f5c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-236 4281,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16544@0" ObjectIDZND0="g_2114f10@0" ObjectIDZND1="16547@x" Pin0InfoVect0LinkObjId="g_2114f10_0" Pin0InfoVect1LinkObjId="SW-76113_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-236 4281,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f8450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-224 4339,-224 4339,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2114f10@0" ObjectIDND1="16544@x" ObjectIDZND0="16547@1" Pin0InfoVect0LinkObjId="SW-76113_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2114f10_0" Pin1InfoVect1LinkObjId="SW-76110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-224 4339,-224 4339,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f86b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-174 4339,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16547@0" ObjectIDZND0="17373@0" Pin0InfoVect0LinkObjId="g_20e2080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76113_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-174 4339,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f8910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-137 4322,-137 4322,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16544@x" ObjectIDND1="16547@x" ObjectIDZND0="g_2114f10@0" Pin0InfoVect0LinkObjId="g_2114f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76110_0" Pin1InfoVect1LinkObjId="SW-76113_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-137 4322,-137 4322,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f8b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-224 4281,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16544@x" ObjectIDND1="16547@x" ObjectIDZND0="g_2114f10@0" Pin0InfoVect0LinkObjId="g_2114f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76110_0" Pin1InfoVect1LinkObjId="SW-76113_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-224 4281,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f8dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-137 4281,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="16544@x" ObjectIDND1="16547@x" ObjectIDND2="g_2114f10@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76110_0" Pin1InfoVect1LinkObjId="SW-76113_0" Pin1InfoVect2LinkObjId="g_2114f10_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-137 4281,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f9810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-529 4445,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16549@1" Pin0InfoVect0LinkObjId="SW-76131_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2128c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-529 4445,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20fb090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-473 4445,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16549@0" ObjectIDZND0="16532@1" Pin0InfoVect0LinkObjId="SW-76037_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76131_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-473 4445,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20fd810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-424 4445,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16532@0" ObjectIDZND0="16550@1" Pin0InfoVect0LinkObjId="SW-76132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76037_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-424 4445,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20fe660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-367 4445,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16550@0" ObjectIDZND0="g_20fda70@0" Pin0InfoVect0LinkObjId="g_20fda70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-367 4445,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2100e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-290 4445,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_20fda70@1" ObjectIDZND0="16548@1" Pin0InfoVect0LinkObjId="SW-76130_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20fda70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-290 4445,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21010a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-235 4445,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16548@0" ObjectIDZND0="g_2104410@0" ObjectIDZND1="16551@x" Pin0InfoVect0LinkObjId="g_2104410_0" Pin0InfoVect1LinkObjId="SW-76133_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-235 4445,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2103830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-223 4503,-223 4503,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2104410@0" ObjectIDND1="16548@x" ObjectIDZND0="16551@1" Pin0InfoVect0LinkObjId="SW-76133_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2104410_0" Pin1InfoVect1LinkObjId="SW-76130_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-223 4503,-223 4503,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2103a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4503,-173 4503,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16551@0" ObjectIDZND0="17373@0" Pin0InfoVect0LinkObjId="g_20e2080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76133_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4503,-173 4503,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2103cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-136 4486,-136 4486,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16548@x" ObjectIDND1="16551@x" ObjectIDZND0="g_2104410@0" Pin0InfoVect0LinkObjId="g_2104410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76130_0" Pin1InfoVect1LinkObjId="SW-76133_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-136 4486,-136 4486,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2103f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-223 4445,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16548@x" ObjectIDND1="16551@x" ObjectIDZND0="g_2104410@0" Pin0InfoVect0LinkObjId="g_2104410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76130_0" Pin1InfoVect1LinkObjId="SW-76133_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-223 4445,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21041b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-136 4445,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="16548@x" ObjectIDND1="16551@x" ObjectIDND2="g_2104410@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76130_0" Pin1InfoVect1LinkObjId="SW-76133_0" Pin1InfoVect2LinkObjId="g_2104410_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-136 4445,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2107d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-529 4604,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16575@1" Pin0InfoVect0LinkObjId="SW-76473_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2128c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-529 4604,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_210a0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-472 4604,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16575@0" ObjectIDZND0="16528@1" Pin0InfoVect0LinkObjId="SW-76032_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76473_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-472 4604,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_210cb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-423 4604,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16528@0" ObjectIDZND0="16576@1" Pin0InfoVect0LinkObjId="SW-76474_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76032_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-423 4604,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_210dac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-366 4604,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16576@0" ObjectIDZND0="g_210cd70@0" Pin0InfoVect0LinkObjId="g_210cd70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76474_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-366 4604,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2110a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-289 4604,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_210cd70@1" ObjectIDZND0="16577@1" Pin0InfoVect0LinkObjId="SW-76475_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_210cd70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-289 4604,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21147f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-222 4604,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_2110ca0@0" ObjectIDND1="16577@x" ObjectIDZND0="17373@0" Pin0InfoVect0LinkObjId="g_20e2080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2110ca0_0" Pin1InfoVect1LinkObjId="SW-76475_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-222 4604,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2114a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-234 4604,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="lightningRod" ObjectIDND0="16577@0" ObjectIDZND0="17373@0" ObjectIDZND1="g_2110ca0@0" Pin0InfoVect0LinkObjId="g_20e2080_0" Pin0InfoVect1LinkObjId="g_2110ca0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76475_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-234 4604,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2114cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-222 4662,-222 4662,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="17373@0" ObjectIDND1="16577@x" ObjectIDZND0="g_2110ca0@0" Pin0InfoVect0LinkObjId="g_2110ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20e2080_0" Pin1InfoVect1LinkObjId="SW-76475_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-222 4662,-222 4662,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2115cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-529 4764,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16553@1" Pin0InfoVect0LinkObjId="SW-76151_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2128c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-529 4764,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2117ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-475 4764,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16553@0" ObjectIDZND0="16533@1" Pin0InfoVect0LinkObjId="SW-76038_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-475 4764,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_211aa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-426 4764,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16533@0" ObjectIDZND0="16554@1" Pin0InfoVect0LinkObjId="SW-76152_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76038_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-426 4764,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_211ba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-369 4764,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16554@0" ObjectIDZND0="g_211acb0@0" Pin0InfoVect0LinkObjId="g_211acb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76152_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-369 4764,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_211e980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-292 4764,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_211acb0@1" ObjectIDZND0="16552@1" Pin0InfoVect0LinkObjId="SW-76150_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211acb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-292 4764,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_211ebe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-237 4764,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16552@0" ObjectIDZND0="g_2122220@0" ObjectIDZND1="16555@x" Pin0InfoVect0LinkObjId="g_2122220_0" Pin0InfoVect1LinkObjId="SW-76153_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-237 4764,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2121640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-225 4822,-225 4822,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2122220@0" ObjectIDND1="16552@x" ObjectIDZND0="16555@1" Pin0InfoVect0LinkObjId="SW-76153_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2122220_0" Pin1InfoVect1LinkObjId="SW-76150_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-225 4822,-225 4822,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21218a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-175 4822,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16555@0" ObjectIDZND0="17373@0" Pin0InfoVect0LinkObjId="g_20e2080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76153_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-175 4822,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2121b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-138 4805,-138 4805,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16552@x" ObjectIDND1="16555@x" ObjectIDZND0="g_2122220@0" Pin0InfoVect0LinkObjId="g_2122220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76150_0" Pin1InfoVect1LinkObjId="SW-76153_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-138 4805,-138 4805,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2121d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-225 4764,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16552@x" ObjectIDND1="16555@x" ObjectIDZND0="g_2122220@0" Pin0InfoVect0LinkObjId="g_2122220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76150_0" Pin1InfoVect1LinkObjId="SW-76153_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-225 4764,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2121fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-138 4764,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="16552@x" ObjectIDND1="16555@x" ObjectIDND2="g_2122220@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76150_0" Pin1InfoVect1LinkObjId="SW-76153_0" Pin1InfoVect2LinkObjId="g_2122220_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-138 4764,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2128a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-450 5030,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16537@1" ObjectIDZND0="16562@0" Pin0InfoVect0LinkObjId="SW-76231_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76042_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-450 5030,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2128c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-512 5030,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16562@1" ObjectIDZND0="17361@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76231_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-512 5030,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2128ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-407 5073,-407 5073,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="16579@x" ObjectIDND1="16537@x" ObjectIDZND0="g_220cc20@0" Pin0InfoVect0LinkObjId="g_220cc20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20bbaa0_0" Pin1InfoVect1LinkObjId="SW-76042_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-407 5073,-407 5073,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2129150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-620 4890,-350 5030,-350 5030,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="16579@0" ObjectIDZND0="g_220cc20@0" ObjectIDZND1="16537@x" Pin0InfoVect0LinkObjId="g_220cc20_0" Pin0InfoVect1LinkObjId="SW-76042_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20bbaa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-620 4890,-350 5030,-350 5030,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21293c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-407 5030,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="16579@x" ObjectIDND1="g_220cc20@0" ObjectIDZND0="16537@0" Pin0InfoVect0LinkObjId="SW-76042_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20bbaa0_0" Pin1InfoVect1LinkObjId="g_220cc20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-407 5030,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21fe000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3786,-1076 3771,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_20ae650@0" ObjectIDZND0="16566@x" ObjectIDZND1="16565@x" Pin0InfoVect0LinkObjId="SW-76268_0" Pin0InfoVect1LinkObjId="SW-76267_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20ae650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3786,-1076 3771,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21feaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-1041 3771,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16566@x" ObjectIDND1="16565@x" ObjectIDZND0="g_20ae650@0" Pin0InfoVect0LinkObjId="g_20ae650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76268_0" Pin1InfoVect1LinkObjId="SW-76267_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-1041 3771,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21fed50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-1076 3771,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_20ae650@0" ObjectIDND1="16566@x" ObjectIDND2="16565@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20ae650_0" Pin1InfoVect1LinkObjId="SW-76268_0" Pin1InfoVect2LinkObjId="SW-76267_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-1076 3771,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21fefb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-1076 4926,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_20b5f20@0" ObjectIDZND0="16570@x" ObjectIDZND1="16569@x" Pin0InfoVect0LinkObjId="SW-76296_0" Pin0InfoVect1LinkObjId="SW-76295_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20b5f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-1076 4926,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ffaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-1055 4926,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16570@x" ObjectIDND1="16569@x" ObjectIDZND0="g_20b5f20@0" Pin0InfoVect0LinkObjId="g_20b5f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76296_0" Pin1InfoVect1LinkObjId="SW-76295_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-1055 4926,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ffd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-1075 4926,-1116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_20b5f20@0" ObjectIDND1="16570@x" ObjectIDND2="16569@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_20b5f20_0" Pin1InfoVect1LinkObjId="SW-76296_0" Pin1InfoVect2LinkObjId="SW-76295_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-1075 4926,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22068e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-422 3615,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="16535@0" ObjectIDZND0="16578@x" ObjectIDZND1="g_2209670@0" Pin0InfoVect0LinkObjId="g_20c3e80_0" Pin0InfoVect1LinkObjId="g_2209670_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-422 3615,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2206ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-379 3615,-350 3740,-350 3741,-353 3741,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="16535@x" ObjectIDND1="g_2209670@0" ObjectIDZND0="16578@0" Pin0InfoVect0LinkObjId="g_20c3e80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76040_0" Pin1InfoVect1LinkObjId="g_2209670_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-379 3615,-350 3740,-350 3741,-353 3741,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2206cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-379 3568,-379 3568,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="16535@x" ObjectIDND1="16578@x" ObjectIDZND0="g_2209670@0" Pin0InfoVect0LinkObjId="g_2209670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76040_0" Pin1InfoVect1LinkObjId="g_20c3e80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-379 3568,-379 3568,-392 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="AC-LF_DDB.LF_DDB_35M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3689,-878 5015,-878 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17360" ObjectName="BS-LF_DDB.LF_DDB_35M"/>
    <cge:TPSR_Ref TObjectID="17360"/></metadata>
   <polyline fill="none" opacity="0" points="3689,-878 5015,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-LF_DDB.LF_DDB_10IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3526,-529 5179,-529 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17361" ObjectName="BS-LF_DDB.LF_DDB_10IM"/>
    <cge:TPSR_Ref TObjectID="17361"/></metadata>
   <polyline fill="none" opacity="0" points="3526,-529 5179,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-LF_DDB.LF_DDB_M">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3526,-148 5179,-148 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17373" ObjectName="BS-LF_DDB.LF_DDB_M"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3526,-148 5179,-148 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" stationName="LF_DDB"/>
</svg>