<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-224" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-694 -1283 2387 1363">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape12">
    <polyline points="9,14 3,17 1,18 1,19 1,19 3,21 6,22 10,24 11,25 11,25 11,26 10,27 6,28 3,30 2,30 2,31 2,32 3,33 6,34 10,36 11,36 11,37 11,38 10,38 6,40 3,41 2,42 2,43 2,44 3,44 6,46 10,47 11,48 11,49 11,49 10,50 6,52 3,53 1,55 1,55 1,56 3,57 9,60 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="15" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="0" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape93">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="16" x2="16" y1="47" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="3" x2="6" y1="64" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="59" y2="59"/>
    <polyline arcFlag="1" points="26,37 25,37 25,37 24,37 23,37 23,38 22,38 21,39 21,39 21,40 20,41 20,41 20,42 20,43 20,44 20,44 21,45 21,46 21,46 22,47 23,47 23,48 24,48 25,48 25,48 26,48 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="26,26 25,26 25,26 24,26 23,26 23,27 22,27 21,28 21,28 21,29 20,30 20,30 20,31 20,32 20,33 20,33 21,34 21,35 21,35 22,36 23,36 23,37 24,37 25,37 25,37 26,37 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="26,15 25,15 25,15 24,15 23,15 23,16 22,16 21,17 21,17 21,18 20,19 20,19 20,20 20,21 20,22 20,22 21,23 21,24 21,24 22,25 23,25 23,26 24,26 25,26 25,26 26,26 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="6,37 7,37 7,37 8,37 9,37 9,38 10,38 11,39 11,39 11,40 12,41 12,41 12,42 12,43 12,44 12,44 11,45 11,46 11,46 10,47 9,47 9,48 8,48 7,48 7,48 6,48 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="6,26 7,26 7,26 8,26 9,26 9,27 10,27 11,28 11,28 11,29 12,30 12,30 12,31 12,32 12,33 12,33 11,34 11,35 11,35 10,36 9,36 9,37 8,37 7,37 7,37 6,37 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="6,15 7,15 7,15 8,15 9,15 9,16 10,16 11,17 11,17 11,18 12,19 12,19 12,20 12,21 12,22 12,22 11,23 11,24 11,24 10,25 9,25 9,26 8,26 7,26 7,26 6,26 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="6" x2="6" y1="48" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="6" x2="6" y1="5" y2="15"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape54">
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="32" y2="23"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3842830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3843730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38440f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3845330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3846620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38472c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3847e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3848860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30cd810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30cd810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_384b8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_384b8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_384d6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_384d6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_384e790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3850390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3850f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3851d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3852680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3853d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3854a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38552e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3855aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3856b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3857500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3857ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3858b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3859da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_385a990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_385b9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_385c600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_386add0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_385def0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_385f4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3860a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1373" width="2397" x="-699" y="-1288"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a90410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -260.000000 146.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a913e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -260.000000 133.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a91980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -260.000000 116.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a91ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -261.500000 100.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a927b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -274.000000 176.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a93040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -285.500000 161.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a96900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 799.000000 398.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a96b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 799.000000 381.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a96da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 799.000000 411.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a97d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 629.000000 792.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a97fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 629.000000 779.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a98200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 629.000000 762.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a98440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.500000 746.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a98680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 615.000000 822.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a988c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.500000 807.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a98bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.000000 611.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a98e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.000000 598.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a990b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.000000 581.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a992f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 788.500000 565.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a99530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 776.000000 641.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a99770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.500000 626.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9a670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1114.000000 816.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9a8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1114.000000 803.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9ab00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1114.000000 786.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9ad40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1112.500000 770.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9af80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1100.000000 846.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9b1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1088.500000 831.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9b4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 626.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9b770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 613.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9b9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 596.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9bbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1268.500000 580.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9be30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1256.000000 656.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9c070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1244.500000 641.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9dc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 926.000000 1010.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9df10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 926.000000 997.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9e150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 926.000000 980.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9e390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 924.500000 964.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9e5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 912.000000 1040.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9e810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 900.500000 1025.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9f180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 998.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9f3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 985.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9f610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 968.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9f850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1262.500000 952.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9fa90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1250.000000 1028.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a9fcd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1238.500000 1013.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa0600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 413.000000 1010.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa0890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 413.000000 997.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa0ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 413.000000 980.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa0d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 411.500000 964.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa0f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 399.000000 1040.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa1190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.500000 1025.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa1ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -89.000000 1004.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa1d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -89.000000 991.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa1f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -89.000000 974.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa21d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -90.500000 958.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa2410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -103.000000 1034.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa2650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -114.500000 1019.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa80e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 472.000000 830.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa8440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.500000 812.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa8680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 472.000000 867.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa88c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 464.000000 777.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa8b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 464.000000 760.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa8d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 464.000000 795.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa8f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 743.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa9830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 472.000000 848.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa9ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -311.000000 624.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa9e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -305.500000 606.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaa070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -311.000000 661.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaa2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -319.000000 571.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaa4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -319.000000 554.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaa730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -319.000000 589.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaa970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -303.000000 537.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaabb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -311.000000 642.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaaee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1506.000000 624.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aab170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.500000 606.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aab3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1506.000000 661.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aab5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1498.000000 571.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aab830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1498.000000 554.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaba70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1498.000000 589.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aabcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1514.000000 537.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aabef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1506.000000 642.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aac220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1502.000000 1010.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aac4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1507.500000 992.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aac6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1502.000000 1047.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aac930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 957.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aacb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 940.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aacdb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 975.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aacff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1510.000000 923.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aad230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1502.000000 1028.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aadb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -120.000000 144.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaddc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -120.000000 131.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aae000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -120.000000 114.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aae240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -121.500000 98.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aae480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -134.000000 174.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aae6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -145.500000 159.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aae9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 144.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaec70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 131.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaeeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 114.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaf0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 14.500000 98.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaf330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 174.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaf570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -9.500000 159.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaf8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 156.000000 142.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aafb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 156.000000 129.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aafd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 156.000000 112.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaffa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 154.500000 96.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab01e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 142.000000 172.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab0420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 130.500000 157.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab0750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 293.000000 142.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab09d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 293.000000 129.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab0c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 293.000000 112.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab0e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.500000 96.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab1090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 172.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab12d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 267.500000 157.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab1600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.000000 147.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab1880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.000000 134.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab1ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.000000 117.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab1d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1031.500000 101.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab1f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1019.000000 177.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab2180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1007.500000 162.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab24b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 148.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab2730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 135.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab2970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 118.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab2bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1274.500000 102.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab2df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1262.000000 178.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab3030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1250.500000 163.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab3360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.000000 148.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab35e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.000000 135.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab3820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.000000 118.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab3a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.500000 102.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab3ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1419.000000 178.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab3ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1407.500000 163.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b01300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -230.000000 1005.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b01550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -224.500000 987.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b01790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -230.000000 1042.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b019d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -238.000000 952.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b01c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -238.000000 935.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b01e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -238.000000 970.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b02090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -222.000000 918.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b022d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -230.000000 1023.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b20620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 141.000000 999.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b20a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 141.000000 986.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b20c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 141.000000 969.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b20ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 139.500000 953.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b21100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 127.000000 1029.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b21340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 115.500000 1014.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b22280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 261.000000 748.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b22510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 261.000000 735.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b22750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 261.000000 718.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b22990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 259.500000 702.000000) translate(0,12)">COS:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b22bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 247.000000 778.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b22e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 235.500000 763.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -5518.500000 -113.000000)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.295918" x1="6010" x2="6015" y1="77" y2="73"/>
    <circle DF8003:Layer="PUBLIC" cx="6014" cy="72" fill="none" fillStyle="0" r="14.5" stroke="rgb(0,255,0)" stroke-width="0.295918"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.295918" x1="6015" x2="6015" y1="68" y2="73"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.295918" x1="6015" x2="6019" y1="73" y2="77"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="6010" x2="6015" y1="77" y2="73"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="-239,-255 -244,-245 -234,-245 -239,-255 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="-239,-267 -244,-277 -234,-277 -239,-267 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="-104,-253 -109,-243 -99,-243 -104,-253 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="-104,-265 -109,-275 -99,-275 -104,-265 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="39,-253 34,-243 44,-243 39,-253 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="39,-265 34,-275 44,-275 39,-265 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="183,-248 178,-238 188,-238 183,-248 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="183,-260 178,-270 188,-270 183,-260 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="317,-248 312,-238 322,-238 317,-248 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="317,-260 312,-270 322,-270 317,-260 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="496,-239 491,-229 501,-229 496,-239 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="496,-251 491,-261 501,-261 496,-251 " stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-153601">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 18.819337 -1019.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26078" ObjectName="SW-DY_BC.DY_BC_353BK"/>
     <cge:Meas_Ref ObjectId="153601"/>
    <cge:TPSR_Ref TObjectID="26078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153589">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.544703 -1025.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26066" ObjectName="SW-DY_BC.DY_BC_351BK"/>
     <cge:Meas_Ref ObjectId="153589"/>
    <cge:TPSR_Ref TObjectID="26066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153595">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1073.881701 -1016.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26072" ObjectName="SW-DY_BC.DY_BC_352BK"/>
     <cge:Meas_Ref ObjectId="153595"/>
    <cge:TPSR_Ref TObjectID="26072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153607">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1350.203031 -1016.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26084" ObjectName="SW-DY_BC.DY_BC_354BK"/>
     <cge:Meas_Ref ObjectId="153607"/>
    <cge:TPSR_Ref TObjectID="26084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153633">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 712.241796 -742.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26093" ObjectName="SW-DY_BC.DY_BC_301BK"/>
     <cge:Meas_Ref ObjectId="153633"/>
    <cge:TPSR_Ref TObjectID="26093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153636">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 710.241796 -576.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26096" ObjectName="SW-DY_BC.DY_BC_001BK"/>
     <cge:Meas_Ref ObjectId="153636"/>
    <cge:TPSR_Ref TObjectID="26096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153700">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1196.650861 -766.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26098" ObjectName="SW-DY_BC.DY_BC_302BK"/>
     <cge:Meas_Ref ObjectId="153700"/>
    <cge:TPSR_Ref TObjectID="26098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153703">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1194.650861 -599.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26101" ObjectName="SW-DY_BC.DY_BC_002BK"/>
     <cge:Meas_Ref ObjectId="153703"/>
    <cge:TPSR_Ref TObjectID="26101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153766">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -249.452245 -370.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26104" ObjectName="SW-DY_BC.DY_BC_051BK"/>
     <cge:Meas_Ref ObjectId="153766"/>
    <cge:TPSR_Ref TObjectID="26104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153769">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -114.022568 -366.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26107" ObjectName="SW-DY_BC.DY_BC_052BK"/>
     <cge:Meas_Ref ObjectId="153769"/>
    <cge:TPSR_Ref TObjectID="26107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 29.680658 -367.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26110" ObjectName="SW-DY_BC.DY_BC_053BK"/>
     <cge:Meas_Ref ObjectId="153772"/>
    <cge:TPSR_Ref TObjectID="26110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153775">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 173.768400 -362.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26113" ObjectName="SW-DY_BC.DY_BC_054BK"/>
     <cge:Meas_Ref ObjectId="153775"/>
    <cge:TPSR_Ref TObjectID="26113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153778">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 308.212271 -363.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26116" ObjectName="SW-DY_BC.DY_BC_055BK"/>
     <cge:Meas_Ref ObjectId="153778"/>
    <cge:TPSR_Ref TObjectID="26116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1445.450980 -365.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26125" ObjectName="SW-DY_BC.DY_BC_073BK"/>
     <cge:Meas_Ref ObjectId="153787"/>
    <cge:TPSR_Ref TObjectID="26125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153784">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1288.450980 -362.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26122" ObjectName="SW-DY_BC.DY_BC_072BK"/>
     <cge:Meas_Ref ObjectId="153784"/>
    <cge:TPSR_Ref TObjectID="26122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153781">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.450980 -356.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26119" ObjectName="SW-DY_BC.DY_BC_071BK"/>
     <cge:Meas_Ref ObjectId="153781"/>
    <cge:TPSR_Ref TObjectID="26119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153794">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 804.000000 -415.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26130" ObjectName="SW-DY_BC.DY_BC_012BK"/>
     <cge:Meas_Ref ObjectId="153794"/>
    <cge:TPSR_Ref TObjectID="26130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233516">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 303.000000 -784.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38932" ObjectName="SW-DY_BC.DY_BC_312BK"/>
     <cge:Meas_Ref ObjectId="233516"/>
    <cge:TPSR_Ref TObjectID="38932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233553">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 224.819337 -1019.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38925" ObjectName="SW-DY_BC.DY_BC_355BK"/>
     <cge:Meas_Ref ObjectId="233553"/>
    <cge:TPSR_Ref TObjectID="38925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-283575">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 487.212271 -359.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44667" ObjectName="SW-DY_BC.DY_BC_056BK"/>
     <cge:Meas_Ref ObjectId="283575"/>
    <cge:TPSR_Ref TObjectID="44667"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_39640c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -38.180663 -1077.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3986c00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 466.544703 -1083.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3974c60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 995.881701 -1073.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3922510">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1272.203031 -1073.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3aba5a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 958.000000 -719.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ac1580">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -272.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ac5e40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1144.000000 -267.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ad3f00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -92.000000 -718.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3af4600">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 164.819337 -1077.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_BM" endPointId="0" endStationName="DY_BC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_beibi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="29,-1234 29,-1266 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38074" ObjectName="AC-35kV.LN_beibi"/>
    <cge:TPSR_Ref TObjectID="38074_SS-224"/></metadata>
   <polyline fill="none" opacity="0" points="29,-1234 29,-1266 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ZX" endPointId="0" endStationName="DY_BC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_zhongbei1" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="554,-1249 554,-1281 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34582" ObjectName="AC-35kV.LN_zhongbei1"/>
    <cge:TPSR_Ref TObjectID="34582_SS-224"/></metadata>
   <polyline fill="none" opacity="0" points="554,-1249 554,-1281 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ZX" endPointId="0" endStationName="DY_BC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_zhongbei2" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1083,-1238 1083,-1270 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34583" ObjectName="AC-35kV.LN_zhongbei2"/>
    <cge:TPSR_Ref TObjectID="34583_SS-224"/></metadata>
   <polyline fill="none" opacity="0" points="1083,-1238 1083,-1270 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_LJ" endPointId="0" endStationName="DY_BC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_beilong" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1358,-1239 1358,-1271 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38075" ObjectName="AC-35kV.LN_beilong"/>
    <cge:TPSR_Ref TObjectID="38075_SS-224"/></metadata>
   <polyline fill="none" opacity="0" points="1358,-1239 1358,-1271 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="234,-1234 234,-1266 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="234,-1234 234,-1266 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-DY_BC.DY_BC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36938"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.418267 -640.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.418267 -640.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26133" ObjectName="TF-DY_BC.DY_BC_1T"/>
    <cge:TPSR_Ref TObjectID="26133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_BC.DY_BC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36942"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1167.827331 -664.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1167.827331 -664.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26134" ObjectName="TF-DY_BC.DY_BC_2T"/>
    <cge:TPSR_Ref TObjectID="26134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1407.000000 -712.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1407.000000 -712.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.000000 -306.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.000000 -306.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_4cff520">
    <use class="BV-0KV" transform="matrix(0.312500 -0.000000 0.000000 -0.430252 566.000000 20.058824)" xlink:href="#lightningRod:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_42af320">
    <use class="BV-0KV" transform="matrix(0.312500 -0.000000 0.000000 -0.430252 523.000000 46.149733)" xlink:href="#lightningRod:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_397af90">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -7.265512 -1132.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3926780">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 518.459855 -1150.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37fb010">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1046.796853 -1141.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39aa0b0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1323.118182 -1141.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a5b770">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 924.141266 -716.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38e6bc0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -209.988074 -216.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_391bf90">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -74.558397 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_397e000">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 68.144829 -213.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_396b080">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 212.232571 -208.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3990c80">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 345.676442 -209.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_394b010">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1482.915152 -211.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39323a0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1325.915152 -208.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a55730">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1083.915152 -202.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a72ae0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1203.676442 -352.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a832d0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 722.676442 -357.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3acece0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -125.858734 -715.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3af3670">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 197.734488 -1132.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b27730">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 1.000000 491.000000 -129.909091)" xlink:href="#lightningRod:shape93"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b2d1a0">
    <use class="BV-10KV" transform="matrix(-0.000000 0.961538 1.000000 0.000000 548.500000 -192.269231)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b37f00">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 524.676442 -200.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -605.000000 -1186.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-154873" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -539.000000 -966.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154873" ObjectName="DY_BC:DY_BC_YGZJ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-154874" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -538.000000 -924.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154874" ObjectName="DY_BC:DY_BC_WGZJ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-154122" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(0.929448 -0.000000 -0.000000 1.724138 -584.101974 -1096.466667) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154122" ObjectName="DY_BC:DY_BC_3ⅠM_Hz"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-154873" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -540.000000 -1049.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154873" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-154873" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -540.000000 -1009.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154873" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154143" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.714286 -0.000000 -0.000000 0.625015 -218.000000 -178.768384) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26104"/>
     <cge:Term_Ref ObjectID="36880"/>
    <cge:TPSR_Ref TObjectID="26104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154144" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.714286 -0.000000 -0.000000 0.625015 -218.000000 -178.768384) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26104"/>
     <cge:Term_Ref ObjectID="36880"/>
    <cge:TPSR_Ref TObjectID="26104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154139" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.714286 -0.000000 -0.000000 0.625015 -218.000000 -178.768384) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154139" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26104"/>
     <cge:Term_Ref ObjectID="36880"/>
    <cge:TPSR_Ref TObjectID="26104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154140" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.714286 -0.000000 -0.000000 0.625015 -218.000000 -178.768384) translate(0,99)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154140" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26104"/>
     <cge:Term_Ref ObjectID="36880"/>
    <cge:TPSR_Ref TObjectID="26104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154141" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.714286 -0.000000 -0.000000 0.625015 -218.000000 -178.768384) translate(0,125)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26104"/>
     <cge:Term_Ref ObjectID="36880"/>
    <cge:TPSR_Ref TObjectID="26104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.714286 -0.000000 -0.000000 0.625015 -218.000000 -178.768384) translate(0,151)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26104"/>
     <cge:Term_Ref ObjectID="36880"/>
    <cge:TPSR_Ref TObjectID="26104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154149" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.097670 -0.000000 -0.000000 1.088889 -79.527778 -178.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154149" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26107"/>
     <cge:Term_Ref ObjectID="36886"/>
    <cge:TPSR_Ref TObjectID="26107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154150" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.097670 -0.000000 -0.000000 1.088889 -79.527778 -178.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26107"/>
     <cge:Term_Ref ObjectID="36886"/>
    <cge:TPSR_Ref TObjectID="26107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154145" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.097670 -0.000000 -0.000000 1.088889 -79.527778 -178.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26107"/>
     <cge:Term_Ref ObjectID="36886"/>
    <cge:TPSR_Ref TObjectID="26107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154146" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.097670 -0.000000 -0.000000 1.088889 -79.527778 -178.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26107"/>
     <cge:Term_Ref ObjectID="36886"/>
    <cge:TPSR_Ref TObjectID="26107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154147" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.097670 -0.000000 -0.000000 1.088889 -79.527778 -178.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26107"/>
     <cge:Term_Ref ObjectID="36886"/>
    <cge:TPSR_Ref TObjectID="26107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154148" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.097670 -0.000000 -0.000000 1.088889 -79.527778 -178.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26107"/>
     <cge:Term_Ref ObjectID="36886"/>
    <cge:TPSR_Ref TObjectID="26107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154155" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 57.000000 -179.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26110"/>
     <cge:Term_Ref ObjectID="36892"/>
    <cge:TPSR_Ref TObjectID="26110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154156" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 57.000000 -179.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26110"/>
     <cge:Term_Ref ObjectID="36892"/>
    <cge:TPSR_Ref TObjectID="26110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 57.000000 -179.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26110"/>
     <cge:Term_Ref ObjectID="36892"/>
    <cge:TPSR_Ref TObjectID="26110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154152" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 57.000000 -179.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26110"/>
     <cge:Term_Ref ObjectID="36892"/>
    <cge:TPSR_Ref TObjectID="26110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154153" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 57.000000 -179.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26110"/>
     <cge:Term_Ref ObjectID="36892"/>
    <cge:TPSR_Ref TObjectID="26110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 57.000000 -179.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26110"/>
     <cge:Term_Ref ObjectID="36892"/>
    <cge:TPSR_Ref TObjectID="26110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154161" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 198.000000 -176.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26113"/>
     <cge:Term_Ref ObjectID="36898"/>
    <cge:TPSR_Ref TObjectID="26113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 198.000000 -176.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26113"/>
     <cge:Term_Ref ObjectID="36898"/>
    <cge:TPSR_Ref TObjectID="26113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154157" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 198.000000 -176.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26113"/>
     <cge:Term_Ref ObjectID="36898"/>
    <cge:TPSR_Ref TObjectID="26113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154158" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 198.000000 -176.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154158" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26113"/>
     <cge:Term_Ref ObjectID="36898"/>
    <cge:TPSR_Ref TObjectID="26113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 198.000000 -176.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26113"/>
     <cge:Term_Ref ObjectID="36898"/>
    <cge:TPSR_Ref TObjectID="26113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 198.000000 -176.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26113"/>
     <cge:Term_Ref ObjectID="36898"/>
    <cge:TPSR_Ref TObjectID="26113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154167" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 337.000000 -175.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154167" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26116"/>
     <cge:Term_Ref ObjectID="36904"/>
    <cge:TPSR_Ref TObjectID="26116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154168" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 337.000000 -175.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154168" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26116"/>
     <cge:Term_Ref ObjectID="36904"/>
    <cge:TPSR_Ref TObjectID="26116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 337.000000 -175.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26116"/>
     <cge:Term_Ref ObjectID="36904"/>
    <cge:TPSR_Ref TObjectID="26116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154164" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 337.000000 -175.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26116"/>
     <cge:Term_Ref ObjectID="36904"/>
    <cge:TPSR_Ref TObjectID="26116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154165" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 337.000000 -175.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154165" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26116"/>
     <cge:Term_Ref ObjectID="36904"/>
    <cge:TPSR_Ref TObjectID="26116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154166" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 337.000000 -175.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154166" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26116"/>
     <cge:Term_Ref ObjectID="36904"/>
    <cge:TPSR_Ref TObjectID="26116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154173" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1078.000000 -181.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154173" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26119"/>
     <cge:Term_Ref ObjectID="36910"/>
    <cge:TPSR_Ref TObjectID="26119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154174" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1078.000000 -181.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154174" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26119"/>
     <cge:Term_Ref ObjectID="36910"/>
    <cge:TPSR_Ref TObjectID="26119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154169" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1078.000000 -181.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26119"/>
     <cge:Term_Ref ObjectID="36910"/>
    <cge:TPSR_Ref TObjectID="26119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154170" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1078.000000 -181.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154170" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26119"/>
     <cge:Term_Ref ObjectID="36910"/>
    <cge:TPSR_Ref TObjectID="26119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154171" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1078.000000 -181.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154171" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26119"/>
     <cge:Term_Ref ObjectID="36910"/>
    <cge:TPSR_Ref TObjectID="26119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154172" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1078.000000 -181.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154172" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26119"/>
     <cge:Term_Ref ObjectID="36910"/>
    <cge:TPSR_Ref TObjectID="26119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154179" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1318.000000 -180.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154179" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26122"/>
     <cge:Term_Ref ObjectID="36944"/>
    <cge:TPSR_Ref TObjectID="26122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154180" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1318.000000 -180.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154180" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26122"/>
     <cge:Term_Ref ObjectID="36944"/>
    <cge:TPSR_Ref TObjectID="26122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154175" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1318.000000 -180.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154175" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26122"/>
     <cge:Term_Ref ObjectID="36944"/>
    <cge:TPSR_Ref TObjectID="26122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154176" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1318.000000 -180.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154176" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26122"/>
     <cge:Term_Ref ObjectID="36944"/>
    <cge:TPSR_Ref TObjectID="26122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154177" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1318.000000 -180.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154177" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26122"/>
     <cge:Term_Ref ObjectID="36944"/>
    <cge:TPSR_Ref TObjectID="26122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154178" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1318.000000 -180.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154178" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26122"/>
     <cge:Term_Ref ObjectID="36944"/>
    <cge:TPSR_Ref TObjectID="26122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154185" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1474.000000 -181.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26125"/>
     <cge:Term_Ref ObjectID="36920"/>
    <cge:TPSR_Ref TObjectID="26125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154186" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1474.000000 -181.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154186" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26125"/>
     <cge:Term_Ref ObjectID="36920"/>
    <cge:TPSR_Ref TObjectID="26125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154181" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1474.000000 -181.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154181" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26125"/>
     <cge:Term_Ref ObjectID="36920"/>
    <cge:TPSR_Ref TObjectID="26125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154182" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1474.000000 -181.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26125"/>
     <cge:Term_Ref ObjectID="36920"/>
    <cge:TPSR_Ref TObjectID="26125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154183" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1474.000000 -181.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154183" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26125"/>
     <cge:Term_Ref ObjectID="36920"/>
    <cge:TPSR_Ref TObjectID="26125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154184" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1474.000000 -181.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154184" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26125"/>
     <cge:Term_Ref ObjectID="36920"/>
    <cge:TPSR_Ref TObjectID="26125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154187" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.111111 841.000000 -413.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154187" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26130"/>
     <cge:Term_Ref ObjectID="36930"/>
    <cge:TPSR_Ref TObjectID="26130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154188" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.111111 841.000000 -413.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154188" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26130"/>
     <cge:Term_Ref ObjectID="36930"/>
    <cge:TPSR_Ref TObjectID="26130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154189" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.111111 841.000000 -413.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154189" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26130"/>
     <cge:Term_Ref ObjectID="36930"/>
    <cge:TPSR_Ref TObjectID="26130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 836.000000 -643.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26096"/>
     <cge:Term_Ref ObjectID="36866"/>
    <cge:TPSR_Ref TObjectID="26096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 836.000000 -643.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26096"/>
     <cge:Term_Ref ObjectID="36866"/>
    <cge:TPSR_Ref TObjectID="26096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154093" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 836.000000 -643.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154093" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26096"/>
     <cge:Term_Ref ObjectID="36866"/>
    <cge:TPSR_Ref TObjectID="26096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 836.000000 -643.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26096"/>
     <cge:Term_Ref ObjectID="36866"/>
    <cge:TPSR_Ref TObjectID="26096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 836.000000 -643.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26096"/>
     <cge:Term_Ref ObjectID="36866"/>
    <cge:TPSR_Ref TObjectID="26096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154096" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 836.000000 -643.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154096" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26096"/>
     <cge:Term_Ref ObjectID="36866"/>
    <cge:TPSR_Ref TObjectID="26096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 674.000000 -824.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26093"/>
     <cge:Term_Ref ObjectID="36860"/>
    <cge:TPSR_Ref TObjectID="26093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154092" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 674.000000 -824.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154092" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26093"/>
     <cge:Term_Ref ObjectID="36860"/>
    <cge:TPSR_Ref TObjectID="26093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154087" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 674.000000 -824.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26093"/>
     <cge:Term_Ref ObjectID="36860"/>
    <cge:TPSR_Ref TObjectID="26093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154088" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 674.000000 -824.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26093"/>
     <cge:Term_Ref ObjectID="36860"/>
    <cge:TPSR_Ref TObjectID="26093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154089" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 674.000000 -824.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26093"/>
     <cge:Term_Ref ObjectID="36860"/>
    <cge:TPSR_Ref TObjectID="26093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154090" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 674.000000 -824.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154090" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26093"/>
     <cge:Term_Ref ObjectID="36860"/>
    <cge:TPSR_Ref TObjectID="26093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154111" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1312.000000 -659.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154111" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26101"/>
     <cge:Term_Ref ObjectID="36876"/>
    <cge:TPSR_Ref TObjectID="26101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154112" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1312.000000 -659.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26101"/>
     <cge:Term_Ref ObjectID="36876"/>
    <cge:TPSR_Ref TObjectID="26101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154107" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1312.000000 -659.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154107" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26101"/>
     <cge:Term_Ref ObjectID="36876"/>
    <cge:TPSR_Ref TObjectID="26101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154108" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1312.000000 -659.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154108" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26101"/>
     <cge:Term_Ref ObjectID="36876"/>
    <cge:TPSR_Ref TObjectID="26101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154109" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1312.000000 -659.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154109" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26101"/>
     <cge:Term_Ref ObjectID="36876"/>
    <cge:TPSR_Ref TObjectID="26101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154110" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1312.000000 -659.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26101"/>
     <cge:Term_Ref ObjectID="36876"/>
    <cge:TPSR_Ref TObjectID="26101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154105" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1159.000000 -848.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26098"/>
     <cge:Term_Ref ObjectID="36870"/>
    <cge:TPSR_Ref TObjectID="26098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1159.000000 -848.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26098"/>
     <cge:Term_Ref ObjectID="36870"/>
    <cge:TPSR_Ref TObjectID="26098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1159.000000 -848.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26098"/>
     <cge:Term_Ref ObjectID="36870"/>
    <cge:TPSR_Ref TObjectID="26098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154102" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1159.000000 -848.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26098"/>
     <cge:Term_Ref ObjectID="36870"/>
    <cge:TPSR_Ref TObjectID="26098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154103" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1159.000000 -848.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154103" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26098"/>
     <cge:Term_Ref ObjectID="36870"/>
    <cge:TPSR_Ref TObjectID="26098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154104" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1159.000000 -848.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154104" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26098"/>
     <cge:Term_Ref ObjectID="36870"/>
    <cge:TPSR_Ref TObjectID="26098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-154123" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 -249.000000 -666.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26158"/>
     <cge:Term_Ref ObjectID="36947"/>
    <cge:TPSR_Ref TObjectID="26158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-154124" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 -249.000000 -666.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154124" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26158"/>
     <cge:Term_Ref ObjectID="36947"/>
    <cge:TPSR_Ref TObjectID="26158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-154125" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 -249.000000 -666.000000) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154125" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26158"/>
     <cge:Term_Ref ObjectID="36947"/>
    <cge:TPSR_Ref TObjectID="26158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-154129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 -249.000000 -666.000000) translate(0,99)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26158"/>
     <cge:Term_Ref ObjectID="36947"/>
    <cge:TPSR_Ref TObjectID="26158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-154126" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 -249.000000 -666.000000) translate(0,125)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26158"/>
     <cge:Term_Ref ObjectID="36947"/>
    <cge:TPSR_Ref TObjectID="26158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-154127" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 -249.000000 -666.000000) translate(0,151)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26158"/>
     <cge:Term_Ref ObjectID="36947"/>
    <cge:TPSR_Ref TObjectID="26158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-154128" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 -249.000000 -666.000000) translate(0,177)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26158"/>
     <cge:Term_Ref ObjectID="36947"/>
    <cge:TPSR_Ref TObjectID="26158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-154130" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 -249.000000 -666.000000) translate(0,203)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26158"/>
     <cge:Term_Ref ObjectID="36947"/>
    <cge:TPSR_Ref TObjectID="26158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-154131" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 1565.000000 -664.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154131" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26159"/>
     <cge:Term_Ref ObjectID="36948"/>
    <cge:TPSR_Ref TObjectID="26159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-154132" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 1565.000000 -664.000000) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26159"/>
     <cge:Term_Ref ObjectID="36948"/>
    <cge:TPSR_Ref TObjectID="26159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-154133" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 1565.000000 -664.000000) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26159"/>
     <cge:Term_Ref ObjectID="36948"/>
    <cge:TPSR_Ref TObjectID="26159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-154137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 1565.000000 -664.000000) translate(0,99)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26159"/>
     <cge:Term_Ref ObjectID="36948"/>
    <cge:TPSR_Ref TObjectID="26159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-154134" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 1565.000000 -664.000000) translate(0,125)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154134" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26159"/>
     <cge:Term_Ref ObjectID="36948"/>
    <cge:TPSR_Ref TObjectID="26159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-154135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 1565.000000 -664.000000) translate(0,151)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26159"/>
     <cge:Term_Ref ObjectID="36948"/>
    <cge:TPSR_Ref TObjectID="26159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-154136" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 1565.000000 -664.000000) translate(0,177)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26159"/>
     <cge:Term_Ref ObjectID="36948"/>
    <cge:TPSR_Ref TObjectID="26159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-154138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.795918 -0.000000 -0.000000 0.703297 1565.000000 -664.000000) translate(0,203)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26159"/>
     <cge:Term_Ref ObjectID="36948"/>
    <cge:TPSR_Ref TObjectID="26159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-154115" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.258065 -0.000000 -0.000000 1.219048 1556.000000 -1050.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-154116" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.258065 -0.000000 -0.000000 1.219048 1556.000000 -1050.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154116" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-154117" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.258065 -0.000000 -0.000000 1.219048 1556.000000 -1050.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154117" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-154121" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.258065 -0.000000 -0.000000 1.219048 1556.000000 -1050.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154121" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-154118" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.258065 -0.000000 -0.000000 1.219048 1556.000000 -1050.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-154119" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.258065 -0.000000 -0.000000 1.219048 1556.000000 -1050.500000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-154120" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.258065 -0.000000 -0.000000 1.219048 1556.000000 -1050.500000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-154122" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.258065 -0.000000 -0.000000 1.219048 1556.000000 -1050.500000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154071" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 976.000000 -1045.000000) translate(0,12)">154071.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154071" ObjectName="DY_BC.DY_BC_352BK:F"/>
     <cge:PSR_Ref ObjectID="26072"/>
     <cge:Term_Ref ObjectID="36818"/>
    <cge:TPSR_Ref TObjectID="26072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154072" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 976.000000 -1045.000000) translate(0,27)">154072.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154072" ObjectName="DY_BC.DY_BC_352BK:F"/>
     <cge:PSR_Ref ObjectID="26072"/>
     <cge:Term_Ref ObjectID="36818"/>
    <cge:TPSR_Ref TObjectID="26072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154066" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 976.000000 -1045.000000) translate(0,42)">154066.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154066" ObjectName="DY_BC.DY_BC_352BK:F"/>
     <cge:PSR_Ref ObjectID="26072"/>
     <cge:Term_Ref ObjectID="36818"/>
    <cge:TPSR_Ref TObjectID="26072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154067" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 976.000000 -1045.000000) translate(0,57)">154067.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154067" ObjectName="DY_BC.DY_BC_352BK:F"/>
     <cge:PSR_Ref ObjectID="26072"/>
     <cge:Term_Ref ObjectID="36818"/>
    <cge:TPSR_Ref TObjectID="26072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154068" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 976.000000 -1045.000000) translate(0,72)">154068.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154068" ObjectName="DY_BC.DY_BC_352BK:F"/>
     <cge:PSR_Ref ObjectID="26072"/>
     <cge:Term_Ref ObjectID="36818"/>
    <cge:TPSR_Ref TObjectID="26072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154070" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 976.000000 -1045.000000) translate(0,87)">154070.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154070" ObjectName="DY_BC.DY_BC_352BK:F"/>
     <cge:PSR_Ref ObjectID="26072"/>
     <cge:Term_Ref ObjectID="36818"/>
    <cge:TPSR_Ref TObjectID="26072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154085" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1308.000000 -1031.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154085" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26084"/>
     <cge:Term_Ref ObjectID="36842"/>
    <cge:TPSR_Ref TObjectID="26084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154086" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1308.000000 -1031.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154086" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26084"/>
     <cge:Term_Ref ObjectID="36842"/>
    <cge:TPSR_Ref TObjectID="26084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154080" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1308.000000 -1031.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154080" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26084"/>
     <cge:Term_Ref ObjectID="36842"/>
    <cge:TPSR_Ref TObjectID="26084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154081" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1308.000000 -1031.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154081" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26084"/>
     <cge:Term_Ref ObjectID="36842"/>
    <cge:TPSR_Ref TObjectID="26084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154082" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1308.000000 -1031.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154082" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26084"/>
     <cge:Term_Ref ObjectID="36842"/>
    <cge:TPSR_Ref TObjectID="26084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154084" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 1308.000000 -1031.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26084"/>
     <cge:Term_Ref ObjectID="36842"/>
    <cge:TPSR_Ref TObjectID="26084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-154064" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 455.000000 -1044.000000) translate(0,12)">154064.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154064" ObjectName="DY_BC.DY_BC_351BK:F"/>
     <cge:PSR_Ref ObjectID="26066"/>
     <cge:Term_Ref ObjectID="36806"/>
    <cge:TPSR_Ref TObjectID="26066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-154065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 455.000000 -1044.000000) translate(0,27)">154065.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154065" ObjectName="DY_BC.DY_BC_351BK:F"/>
     <cge:PSR_Ref ObjectID="26066"/>
     <cge:Term_Ref ObjectID="36806"/>
    <cge:TPSR_Ref TObjectID="26066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-154059" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 455.000000 -1044.000000) translate(0,42)">154059.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154059" ObjectName="DY_BC.DY_BC_351BK:F"/>
     <cge:PSR_Ref ObjectID="26066"/>
     <cge:Term_Ref ObjectID="36806"/>
    <cge:TPSR_Ref TObjectID="26066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154060" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 455.000000 -1044.000000) translate(0,57)">154060.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154060" ObjectName="DY_BC.DY_BC_351BK:F"/>
     <cge:PSR_Ref ObjectID="26066"/>
     <cge:Term_Ref ObjectID="36806"/>
    <cge:TPSR_Ref TObjectID="26066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-154061" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 455.000000 -1044.000000) translate(0,72)">154061.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154061" ObjectName="DY_BC.DY_BC_351BK:F"/>
     <cge:PSR_Ref ObjectID="26066"/>
     <cge:Term_Ref ObjectID="36806"/>
    <cge:TPSR_Ref TObjectID="26066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-154063" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.129032 -0.000000 -0.000000 1.088889 455.000000 -1044.000000) translate(0,87)">154063.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154063" ObjectName="DY_BC.DY_BC_351BK:F"/>
     <cge:PSR_Ref ObjectID="26066"/>
     <cge:Term_Ref ObjectID="36806"/>
    <cge:TPSR_Ref TObjectID="26066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-154078" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.135323 -0.000000 -0.000000 1.088365 -53.073654 -1037.005000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154078" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26078"/>
     <cge:Term_Ref ObjectID="36830"/>
    <cge:TPSR_Ref TObjectID="26078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-154079" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.135323 -0.000000 -0.000000 1.088365 -53.073654 -1037.005000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154079" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26078"/>
     <cge:Term_Ref ObjectID="36830"/>
    <cge:TPSR_Ref TObjectID="26078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-154073" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.135323 -0.000000 -0.000000 1.088365 -53.073654 -1037.005000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154073" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26078"/>
     <cge:Term_Ref ObjectID="36830"/>
    <cge:TPSR_Ref TObjectID="26078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-154074" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.135323 -0.000000 -0.000000 1.088365 -53.073654 -1037.005000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26078"/>
     <cge:Term_Ref ObjectID="36830"/>
    <cge:TPSR_Ref TObjectID="26078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="1" id="ME-154075" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.135323 -0.000000 -0.000000 1.088365 -53.073654 -1037.005000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154075" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26078"/>
     <cge:Term_Ref ObjectID="36830"/>
    <cge:TPSR_Ref TObjectID="26078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-154077" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.135323 -0.000000 -0.000000 1.088365 -53.073654 -1037.005000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26078"/>
     <cge:Term_Ref ObjectID="36830"/>
    <cge:TPSR_Ref TObjectID="26078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-154100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.714286 -0.000000 -0.000000 0.759609 810.000000 -724.927837) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26133"/>
     <cge:Term_Ref ObjectID="36939"/>
    <cge:TPSR_Ref TObjectID="26133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-154099" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.776506 -0.000000 -0.000000 0.730769 826.475610 -681.500000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154099" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26133"/>
     <cge:Term_Ref ObjectID="36939"/>
    <cge:TPSR_Ref TObjectID="26133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-154114" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.637616 -0.000000 -0.000000 0.769231 1296.991143 -754.000000) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154114" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26134"/>
     <cge:Term_Ref ObjectID="36940"/>
    <cge:TPSR_Ref TObjectID="26134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-154113" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.257237 -0.000000 -0.000000 1.263158 1312.012821 -705.973684) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26134"/>
     <cge:Term_Ref ObjectID="36943"/>
    <cge:TPSR_Ref TObjectID="26134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-154208" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.777778 -0.000000 -0.000000 0.697928 533.000000 -872.507692) translate(0,21)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154208" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26160"/>
     <cge:Term_Ref ObjectID="36949"/>
    <cge:TPSR_Ref TObjectID="26160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-154209" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.777778 -0.000000 -0.000000 0.697928 533.000000 -872.507692) translate(0,47)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26160"/>
     <cge:Term_Ref ObjectID="36949"/>
    <cge:TPSR_Ref TObjectID="26160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-154210" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.777778 -0.000000 -0.000000 0.697928 533.000000 -872.507692) translate(0,73)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154210" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26160"/>
     <cge:Term_Ref ObjectID="36949"/>
    <cge:TPSR_Ref TObjectID="26160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-154214" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.777778 -0.000000 -0.000000 0.697928 533.000000 -872.507692) translate(0,99)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154214" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26160"/>
     <cge:Term_Ref ObjectID="36949"/>
    <cge:TPSR_Ref TObjectID="26160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-154211" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.777778 -0.000000 -0.000000 0.697928 533.000000 -872.507692) translate(0,125)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154211" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26160"/>
     <cge:Term_Ref ObjectID="36949"/>
    <cge:TPSR_Ref TObjectID="26160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-154212" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.777778 -0.000000 -0.000000 0.697928 533.000000 -872.507692) translate(0,151)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154212" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26160"/>
     <cge:Term_Ref ObjectID="36949"/>
    <cge:TPSR_Ref TObjectID="26160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-154213" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.777778 -0.000000 -0.000000 0.697928 533.000000 -872.507692) translate(0,177)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154213" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26160"/>
     <cge:Term_Ref ObjectID="36949"/>
    <cge:TPSR_Ref TObjectID="26160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-154215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(0.777778 -0.000000 -0.000000 0.697928 533.000000 -872.507692) translate(0,203)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="154215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26160"/>
     <cge:Term_Ref ObjectID="36949"/>
    <cge:TPSR_Ref TObjectID="26160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-233594" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.225000 -174.000000 -1045.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-233595" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.225000 -174.000000 -1045.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-233596" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.225000 -174.000000 -1045.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233596" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-233600" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.225000 -174.000000 -1045.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-233597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.225000 -174.000000 -1045.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-233598" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.225000 -174.000000 -1045.500000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233598" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-233599" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.225000 -174.000000 -1045.500000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-233601" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.225000 -174.000000 -1045.500000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233601" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26157"/>
     <cge:Term_Ref ObjectID="36946"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-233623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -1028.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38925"/>
     <cge:Term_Ref ObjectID="58410"/>
    <cge:TPSR_Ref TObjectID="38925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-233624" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -1028.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38925"/>
     <cge:Term_Ref ObjectID="58410"/>
    <cge:TPSR_Ref TObjectID="38925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-233617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -1028.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38925"/>
     <cge:Term_Ref ObjectID="58410"/>
    <cge:TPSR_Ref TObjectID="38925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-233618" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -1028.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38925"/>
     <cge:Term_Ref ObjectID="58410"/>
    <cge:TPSR_Ref TObjectID="38925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-233619" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -1028.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38925"/>
     <cge:Term_Ref ObjectID="58410"/>
    <cge:TPSR_Ref TObjectID="38925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-233625" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -1028.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233625" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38925"/>
     <cge:Term_Ref ObjectID="58410"/>
    <cge:TPSR_Ref TObjectID="38925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-233605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -780.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38932"/>
     <cge:Term_Ref ObjectID="58424"/>
    <cge:TPSR_Ref TObjectID="38932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-233606" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -780.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38932"/>
     <cge:Term_Ref ObjectID="58424"/>
    <cge:TPSR_Ref TObjectID="38932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-233602" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -780.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38932"/>
     <cge:Term_Ref ObjectID="58424"/>
    <cge:TPSR_Ref TObjectID="38932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-233603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -780.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38932"/>
     <cge:Term_Ref ObjectID="58424"/>
    <cge:TPSR_Ref TObjectID="38932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-233604" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -780.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38932"/>
     <cge:Term_Ref ObjectID="58424"/>
    <cge:TPSR_Ref TObjectID="38932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-233607" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 303.000000 -780.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="233607" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38932"/>
     <cge:Term_Ref ObjectID="58424"/>
    <cge:TPSR_Ref TObjectID="38932"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-548" y="-1242"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-548" y="-1242"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-597" y="-1259"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-597" y="-1259"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-230" y="-399"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-230" y="-399"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-96" y="-395"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-96" y="-395"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="48" y="-396"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="48" y="-396"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="192" y="-391"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="192" y="-391"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="325" y="-392"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="325" y="-392"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1067" y="-385"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1067" y="-385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1310" y="-391"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1310" y="-391"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1467" y="-394"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1467" y="-394"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="37" y="-1047"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="37" y="-1047"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="562" y="-1053"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="562" y="-1053"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="23" qtmmishow="hidden" width="67" x="609" y="-694"/>
    </a>
   <metadata/><rect fill="white" height="23" opacity="0" stroke="white" transform="" width="67" x="609" y="-694"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="71" x="1075" y="-716"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="71" x="1075" y="-716"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1091" y="-1044"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1091" y="-1044"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="64" x="-643" y="-812"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="64" x="-643" y="-812"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="1373" y="-1047"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="1373" y="-1047"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="814" y="-449"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="814" y="-449"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-397" y="-1221"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-397" y="-1221"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-397" y="-1256"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-397" y="-1256"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="313" y="-818"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="313" y="-818"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="243" y="-1048"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="243" y="-1048"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-251" y="-1240"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-251" y="-1240"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="506" y="-388"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="506" y="-388"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-548" y="-1242"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-597" y="-1259"/></g>
   <g href="35kV北城变DY_BC_051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-230" y="-399"/></g>
   <g href="35kV北城变DY_BC_052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-96" y="-395"/></g>
   <g href="35kV北城变DY_BC_053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="48" y="-396"/></g>
   <g href="35kV北城变DY_BC_054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="192" y="-391"/></g>
   <g href="35kV北城变DY_BC_055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="325" y="-392"/></g>
   <g href="35kV北城变DY_BC_071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1067" y="-385"/></g>
   <g href="35kV北城变DY_BC_072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1310" y="-391"/></g>
   <g href="35kV北城变DY_BC_073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1467" y="-394"/></g>
   <g href="35kV北城变35kV北碧线353间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="37" y="-1047"/></g>
   <g href="35kV北城变35kV中北Ⅰ回线351间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="562" y="-1053"/></g>
   <g href="35kV北城变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="23" qtmmishow="hidden" width="67" x="609" y="-694"/></g>
   <g href="35kV北城变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="71" x="1075" y="-716"/></g>
   <g href="35kV北城变35kV中北Ⅱ回线352间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1091" y="-1044"/></g>
   <g href="35kV北城变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="64" x="-643" y="-812"/></g>
   <g href="35kV北城变35kV北龙线354间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="1373" y="-1047"/></g>
   <g href="35kV北城变DY_BC_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="814" y="-449"/></g>
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-397" y="-1221"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-397" y="-1256"/></g>
   <g href="35kV北城变35kV分段312间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="313" y="-818"/></g>
   <g href="35kV北城变35kV北店线355间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="243" y="-1048"/></g>
   <g href="AVC北城站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-251" y="-1240"/></g>
   <g href="35kV北城变10kV1号消弧线圈及接地变056断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="506" y="-388"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1691" x2="1691" y1="-607" y2="-581"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="479" x2="479" y1="-36" y2="-28"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="479" x2="479" y1="-41" y2="-49"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="491" x2="479" y1="-49" y2="-49"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="496" x2="479" y1="-28" y2="-28"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="496" x2="496" y1="-29" y2="-34"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="520" x2="515" y1="-32" y2="-32"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="520" x2="515" y1="-42" y2="-42"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="520" x2="501" y1="-49" y2="-49"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="520" x2="496" y1="-34" y2="-34"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="526" x2="526" y1="-40" y2="-24"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="526" x2="526" y1="-63" y2="-47"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-153605">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 19.819337 -922.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26082" ObjectName="SW-DY_BC.DY_BC_3531SW"/>
     <cge:Meas_Ref ObjectId="153605"/>
    <cge:TPSR_Ref TObjectID="26082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 39.819337 -990.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26083" ObjectName="SW-DY_BC.DY_BC_35317SW"/>
     <cge:Meas_Ref ObjectId="153606"/>
    <cge:TPSR_Ref TObjectID="26083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153602">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 19.819337 -1116.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26079" ObjectName="SW-DY_BC.DY_BC_3536SW"/>
     <cge:Meas_Ref ObjectId="153602"/>
    <cge:TPSR_Ref TObjectID="26079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 40.819337 -1174.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26081" ObjectName="SW-DY_BC.DY_BC_35367SW"/>
     <cge:Meas_Ref ObjectId="153604"/>
    <cge:TPSR_Ref TObjectID="26081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -34.180663 -1114.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153603">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 38.819337 -1086.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26080" ObjectName="SW-DY_BC.DY_BC_35360SW"/>
     <cge:Meas_Ref ObjectId="153603"/>
    <cge:TPSR_Ref TObjectID="26080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153593">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.544703 -928.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26070" ObjectName="SW-DY_BC.DY_BC_3511SW"/>
     <cge:Meas_Ref ObjectId="153593"/>
    <cge:TPSR_Ref TObjectID="26070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153594">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 565.544703 -996.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26071" ObjectName="SW-DY_BC.DY_BC_35117SW"/>
     <cge:Meas_Ref ObjectId="153594"/>
    <cge:TPSR_Ref TObjectID="26071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153590">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.544703 -1122.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26067" ObjectName="SW-DY_BC.DY_BC_3516SW"/>
     <cge:Meas_Ref ObjectId="153590"/>
    <cge:TPSR_Ref TObjectID="26067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153592">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.544703 -1180.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26069" ObjectName="SW-DY_BC.DY_BC_35167SW"/>
     <cge:Meas_Ref ObjectId="153592"/>
    <cge:TPSR_Ref TObjectID="26069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 470.544703 -1120.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153591">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 564.544703 -1092.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26068" ObjectName="SW-DY_BC.DY_BC_35160SW"/>
     <cge:Meas_Ref ObjectId="153591"/>
    <cge:TPSR_Ref TObjectID="26068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153599">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1073.881701 -919.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26076" ObjectName="SW-DY_BC.DY_BC_3522SW"/>
     <cge:Meas_Ref ObjectId="153599"/>
    <cge:TPSR_Ref TObjectID="26076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153600">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1094.881701 -987.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26077" ObjectName="SW-DY_BC.DY_BC_35227SW"/>
     <cge:Meas_Ref ObjectId="153600"/>
    <cge:TPSR_Ref TObjectID="26077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153596">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1073.881701 -1113.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26073" ObjectName="SW-DY_BC.DY_BC_3526SW"/>
     <cge:Meas_Ref ObjectId="153596"/>
    <cge:TPSR_Ref TObjectID="26073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153598">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1095.881701 -1171.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26075" ObjectName="SW-DY_BC.DY_BC_35267SW"/>
     <cge:Meas_Ref ObjectId="153598"/>
    <cge:TPSR_Ref TObjectID="26075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 999.881701 -1111.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153597">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1093.881701 -1083.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26074" ObjectName="SW-DY_BC.DY_BC_35260SW"/>
     <cge:Meas_Ref ObjectId="153597"/>
    <cge:TPSR_Ref TObjectID="26074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153611">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1350.203031 -919.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26088" ObjectName="SW-DY_BC.DY_BC_3542SW"/>
     <cge:Meas_Ref ObjectId="153611"/>
    <cge:TPSR_Ref TObjectID="26088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153612">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1370.203031 -987.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26089" ObjectName="SW-DY_BC.DY_BC_35427SW"/>
     <cge:Meas_Ref ObjectId="153612"/>
    <cge:TPSR_Ref TObjectID="26089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153608">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1350.203031 -1113.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26085" ObjectName="SW-DY_BC.DY_BC_3546SW"/>
     <cge:Meas_Ref ObjectId="153608"/>
    <cge:TPSR_Ref TObjectID="26085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1371.203031 -1171.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26087" ObjectName="SW-DY_BC.DY_BC_35467SW"/>
     <cge:Meas_Ref ObjectId="153610"/>
    <cge:TPSR_Ref TObjectID="26087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.203031 -1111.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153609">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1369.203031 -1083.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26086" ObjectName="SW-DY_BC.DY_BC_35460SW"/>
     <cge:Meas_Ref ObjectId="153609"/>
    <cge:TPSR_Ref TObjectID="26086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153634">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 712.241796 -840.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26094" ObjectName="SW-DY_BC.DY_BC_3011SW"/>
     <cge:Meas_Ref ObjectId="153634"/>
    <cge:TPSR_Ref TObjectID="26094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153902">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 710.241796 -529.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26155" ObjectName="SW-DY_BC.DY_BC_0011SW"/>
     <cge:Meas_Ref ObjectId="153902"/>
    <cge:TPSR_Ref TObjectID="26155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153635">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.241796 -793.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26095" ObjectName="SW-DY_BC.DY_BC_30117SW"/>
     <cge:Meas_Ref ObjectId="153635"/>
    <cge:TPSR_Ref TObjectID="26095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153701">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1196.650861 -838.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26099" ObjectName="SW-DY_BC.DY_BC_3022SW"/>
     <cge:Meas_Ref ObjectId="153701"/>
    <cge:TPSR_Ref TObjectID="26099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153704">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1194.650861 -527.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26102" ObjectName="SW-DY_BC.DY_BC_0022SW"/>
     <cge:Meas_Ref ObjectId="153704"/>
    <cge:TPSR_Ref TObjectID="26102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153702">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1233.650861 -817.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26100" ObjectName="SW-DY_BC.DY_BC_30227SW"/>
     <cge:Meas_Ref ObjectId="153702"/>
    <cge:TPSR_Ref TObjectID="26100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153613">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 964.226115 -821.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26090" ObjectName="SW-DY_BC.DY_BC_3902SW"/>
     <cge:Meas_Ref ObjectId="153613"/>
    <cge:TPSR_Ref TObjectID="26090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153614">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 998.226115 -798.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26091" ObjectName="SW-DY_BC.DY_BC_39027SW"/>
     <cge:Meas_Ref ObjectId="153614"/>
    <cge:TPSR_Ref TObjectID="26091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153767">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -247.903226 -440.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26105" ObjectName="SW-DY_BC.DY_BC_0511SW"/>
     <cge:Meas_Ref ObjectId="153767"/>
    <cge:TPSR_Ref TObjectID="26105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153768">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -247.903226 -298.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26106" ObjectName="SW-DY_BC.DY_BC_0516SW"/>
     <cge:Meas_Ref ObjectId="153768"/>
    <cge:TPSR_Ref TObjectID="26106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -113.473548 -436.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26108" ObjectName="SW-DY_BC.DY_BC_0521SW"/>
     <cge:Meas_Ref ObjectId="153770"/>
    <cge:TPSR_Ref TObjectID="26108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -113.473548 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26109" ObjectName="SW-DY_BC.DY_BC_0526SW"/>
     <cge:Meas_Ref ObjectId="153771"/>
    <cge:TPSR_Ref TObjectID="26109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 30.229677 -437.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26111" ObjectName="SW-DY_BC.DY_BC_0531SW"/>
     <cge:Meas_Ref ObjectId="153773"/>
    <cge:TPSR_Ref TObjectID="26111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153774">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 30.229677 -295.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26112" ObjectName="SW-DY_BC.DY_BC_0536SW"/>
     <cge:Meas_Ref ObjectId="153774"/>
    <cge:TPSR_Ref TObjectID="26112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153776">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.317419 -432.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26114" ObjectName="SW-DY_BC.DY_BC_0541SW"/>
     <cge:Meas_Ref ObjectId="153776"/>
    <cge:TPSR_Ref TObjectID="26114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153777">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.317419 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26115" ObjectName="SW-DY_BC.DY_BC_0546SW"/>
     <cge:Meas_Ref ObjectId="153777"/>
    <cge:TPSR_Ref TObjectID="26115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153779">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 307.761290 -433.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26117" ObjectName="SW-DY_BC.DY_BC_0551SW"/>
     <cge:Meas_Ref ObjectId="153779"/>
    <cge:TPSR_Ref TObjectID="26117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153780">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 307.761290 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26118" ObjectName="SW-DY_BC.DY_BC_0556SW"/>
     <cge:Meas_Ref ObjectId="153780"/>
    <cge:TPSR_Ref TObjectID="26118"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1445.000000 -435.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26126" ObjectName="SW-DY_BC.DY_BC_0732SW"/>
     <cge:Meas_Ref ObjectId="153788"/>
    <cge:TPSR_Ref TObjectID="26126"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153789">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1445.000000 -293.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26127" ObjectName="SW-DY_BC.DY_BC_0736SW"/>
     <cge:Meas_Ref ObjectId="153789"/>
    <cge:TPSR_Ref TObjectID="26127"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153785">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1288.000000 -432.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26123" ObjectName="SW-DY_BC.DY_BC_0722SW"/>
     <cge:Meas_Ref ObjectId="153785"/>
    <cge:TPSR_Ref TObjectID="26123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1288.000000 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26124" ObjectName="SW-DY_BC.DY_BC_0726SW"/>
     <cge:Meas_Ref ObjectId="153786"/>
    <cge:TPSR_Ref TObjectID="26124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153782">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26120" ObjectName="SW-DY_BC.DY_BC_0712SW"/>
     <cge:Meas_Ref ObjectId="153782"/>
    <cge:TPSR_Ref TObjectID="26120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153783">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26121" ObjectName="SW-DY_BC.DY_BC_0716SW"/>
     <cge:Meas_Ref ObjectId="153783"/>
    <cge:TPSR_Ref TObjectID="26121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 973.298137 -414.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153791">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1160.226115 -429.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26129" ObjectName="SW-DY_BC.DY_BC_0902SW"/>
     <cge:Meas_Ref ObjectId="153791"/>
    <cge:TPSR_Ref TObjectID="26129"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1164.000000 -336.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153795">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.226115 -435.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26131" ObjectName="SW-DY_BC.DY_BC_0121SW"/>
     <cge:Meas_Ref ObjectId="153795"/>
    <cge:TPSR_Ref TObjectID="26131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153796">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.226115 -434.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26132" ObjectName="SW-DY_BC.DY_BC_0122SW"/>
     <cge:Meas_Ref ObjectId="153796"/>
    <cge:TPSR_Ref TObjectID="26132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153790">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 679.226115 -434.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26128" ObjectName="SW-DY_BC.DY_BC_0901SW"/>
     <cge:Meas_Ref ObjectId="153790"/>
    <cge:TPSR_Ref TObjectID="26128"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.000000 -341.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1417.000000 -823.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -85.773885 -810.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38939" ObjectName="SW-DY_BC.DY_BC_3901SW"/>
     <cge:Meas_Ref ObjectId="233579"/>
    <cge:TPSR_Ref TObjectID="38939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233580">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -62.773885 -785.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38940" ObjectName="SW-DY_BC.DY_BC_39017SW"/>
     <cge:Meas_Ref ObjectId="233580"/>
    <cge:TPSR_Ref TObjectID="38940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233517">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 264.226115 -813.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38933" ObjectName="SW-DY_BC.DY_BC_3121SW"/>
     <cge:Meas_Ref ObjectId="233517"/>
    <cge:TPSR_Ref TObjectID="38933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233519">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 371.226115 -812.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38937" ObjectName="SW-DY_BC.DY_BC_3122SW"/>
     <cge:Meas_Ref ObjectId="233519"/>
    <cge:TPSR_Ref TObjectID="38937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233554">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 224.819337 -922.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38926" ObjectName="SW-DY_BC.DY_BC_3551SW"/>
     <cge:Meas_Ref ObjectId="233554"/>
    <cge:TPSR_Ref TObjectID="38926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233555">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 248.819337 -990.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38927" ObjectName="SW-DY_BC.DY_BC_35517SW"/>
     <cge:Meas_Ref ObjectId="233555"/>
    <cge:TPSR_Ref TObjectID="38927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233556">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 224.819337 -1116.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38928" ObjectName="SW-DY_BC.DY_BC_3556SW"/>
     <cge:Meas_Ref ObjectId="233556"/>
    <cge:TPSR_Ref TObjectID="38928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233557">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 249.819337 -1174.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38930" ObjectName="SW-DY_BC.DY_BC_35567SW"/>
     <cge:Meas_Ref ObjectId="233557"/>
    <cge:TPSR_Ref TObjectID="38930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 168.819337 -1114.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 247.819337 -1086.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38931" ObjectName="SW-DY_BC.DY_BC_35560SW"/>
     <cge:Meas_Ref ObjectId="233577"/>
    <cge:TPSR_Ref TObjectID="38931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233508">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -38.000000 -1169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38938" ObjectName="SW-DY_BC.DY_BC_3533SW"/>
     <cge:Meas_Ref ObjectId="233508"/>
    <cge:TPSR_Ref TObjectID="38938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233558">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 165.000000 -1169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38929" ObjectName="SW-DY_BC.DY_BC_3553SW"/>
     <cge:Meas_Ref ObjectId="233558"/>
    <cge:TPSR_Ref TObjectID="38929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233520">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 277.819337 -863.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38936" ObjectName="SW-DY_BC.DY_BC_31210SW"/>
     <cge:Meas_Ref ObjectId="233520"/>
    <cge:TPSR_Ref TObjectID="38936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233521">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 382.819337 -789.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38935" ObjectName="SW-DY_BC.DY_BC_31227SW"/>
     <cge:Meas_Ref ObjectId="233521"/>
    <cge:TPSR_Ref TObjectID="38935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233518">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 218.819337 -789.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38934" ObjectName="SW-DY_BC.DY_BC_31217SW"/>
     <cge:Meas_Ref ObjectId="233518"/>
    <cge:TPSR_Ref TObjectID="38934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-153615">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -62.773885 -861.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26092" ObjectName="SW-DY_BC.DY_BC_39010SW"/>
     <cge:Meas_Ref ObjectId="153615"/>
    <cge:TPSR_Ref TObjectID="26092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.785714 -0.000000 0.000000 -0.652174 577.000000 -71.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-283599">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.739130 517.000000 -129.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44670" ObjectName="SW-DY_BC.DY_BC_0030SW"/>
     <cge:Meas_Ref ObjectId="283599"/>
    <cge:TPSR_Ref TObjectID="44670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-283576">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 486.761290 -429.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44668" ObjectName="SW-DY_BC.DY_BC_0561SW"/>
     <cge:Meas_Ref ObjectId="283576"/>
    <cge:TPSR_Ref TObjectID="44668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-283577">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 486.761290 -287.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44669" ObjectName="SW-DY_BC.DY_BC_0566SW"/>
     <cge:Meas_Ref ObjectId="283577"/>
    <cge:TPSR_Ref TObjectID="44669"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_BC.051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -247.903226 -209.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34217" ObjectName="EC-DY_BC.051Ld"/>
    <cge:TPSR_Ref TObjectID="34217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_BC.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -113.473548 -206.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34218" ObjectName="EC-DY_BC.052Ld"/>
    <cge:TPSR_Ref TObjectID="34218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_BC.053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 30.229677 -207.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34219" ObjectName="EC-DY_BC.053Ld"/>
    <cge:TPSR_Ref TObjectID="34219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_BC.054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.317419 -202.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34220" ObjectName="EC-DY_BC.054Ld"/>
    <cge:TPSR_Ref TObjectID="34220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_BC.055Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 307.761290 -203.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34221" ObjectName="EC-DY_BC.055Ld"/>
    <cge:TPSR_Ref TObjectID="34221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_BC.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1445.000000 -210.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34224" ObjectName="EC-DY_BC.073Ld"/>
    <cge:TPSR_Ref TObjectID="34224"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_BC.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1288.000000 -207.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34223" ObjectName="EC-DY_BC.072Ld"/>
    <cge:TPSR_Ref TObjectID="34223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_BC.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 -201.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34222" ObjectName="EC-DY_BC.071Ld"/>
    <cge:TPSR_Ref TObjectID="34222"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_395fd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="29,-927 29,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26082@0" ObjectIDZND0="26157@0" Pin0InfoVect0LinkObjId="g_3974120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153605_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="29,-927 29,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_385afe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="29,-995 45,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26078@x" ObjectIDND1="26082@x" ObjectIDZND0="26083@1" Pin0InfoVect0LinkObjId="SW-153606_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153601_0" Pin1InfoVect1LinkObjId="SW-153605_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="29,-995 45,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2800ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="29,-1027 29,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26078@0" ObjectIDZND0="26083@x" ObjectIDZND1="26082@x" Pin0InfoVect0LinkObjId="SW-153606_0" Pin0InfoVect1LinkObjId="SW-153605_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="29,-1027 29,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39c59f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="29,-995 29,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26083@x" ObjectIDND1="26078@x" ObjectIDZND0="26082@1" Pin0InfoVect0LinkObjId="SW-153605_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153606_0" Pin1InfoVect1LinkObjId="SW-153601_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="29,-995 29,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39b33f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="81,-995 95,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26083@0" ObjectIDZND0="g_39705c0@0" Pin0InfoVect0LinkObjId="g_39705c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="81,-995 95,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39980a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="82,-1179 96,-1179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26081@0" ObjectIDZND0="g_3939a40@0" Pin0InfoVect0LinkObjId="g_3939a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="82,-1179 96,-1179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_393a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-29,-1119 -29,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_39640c0@0" Pin0InfoVect0LinkObjId="g_39640c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-29,-1119 -29,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3970280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="29,-1091 44,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26078@x" ObjectIDND1="26079@x" ObjectIDZND0="26080@1" Pin0InfoVect0LinkObjId="SW-153603_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153601_0" Pin1InfoVect1LinkObjId="SW-153602_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="29,-1091 44,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39688f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="80,-1091 94,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26080@0" ObjectIDZND0="g_390a5f0@0" Pin0InfoVect0LinkObjId="g_390a5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153603_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="80,-1091 94,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_379e300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="29,-1054 29,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26078@1" ObjectIDZND0="26080@x" ObjectIDZND1="26079@x" Pin0InfoVect0LinkObjId="SW-153603_0" Pin0InfoVect1LinkObjId="SW-153602_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153601_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="29,-1054 29,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3989f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="29,-1091 29,-1121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26080@x" ObjectIDND1="26078@x" ObjectIDZND0="26079@0" Pin0InfoVect0LinkObjId="SW-153602_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153603_0" Pin1InfoVect1LinkObjId="SW-153601_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="29,-1091 29,-1121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39139d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="46,-1179 29,-1179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="26081@1" ObjectIDZND0="26079@x" ObjectIDZND1="g_397af90@0" ObjectIDZND2="38074@1" Pin0InfoVect0LinkObjId="SW-153602_0" Pin0InfoVect1LinkObjId="g_397af90_0" Pin0InfoVect2LinkObjId="g_4cc48e0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="46,-1179 29,-1179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38fb8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="29,-1157 29,-1179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="26079@1" ObjectIDZND0="26081@x" ObjectIDZND1="g_397af90@0" ObjectIDZND2="38074@1" Pin0InfoVect0LinkObjId="SW-153604_0" Pin0InfoVect1LinkObjId="g_397af90_0" Pin0InfoVect2LinkObjId="g_4cc48e0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153602_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="29,-1157 29,-1179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38ef5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="0,-1186 29,-1186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_397af90@0" ObjectIDZND0="26081@x" ObjectIDZND1="26079@x" ObjectIDZND2="38074@1" Pin0InfoVect0LinkObjId="SW-153604_0" Pin0InfoVect1LinkObjId="SW-153602_0" Pin0InfoVect2LinkObjId="g_4cc48e0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_397af90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="0,-1186 29,-1186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4cc48e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="29,-1218 29,-1236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="38938@x" ObjectIDND1="g_397af90@0" ObjectIDND2="26081@x" ObjectIDZND0="38074@1" Pin0InfoVect0LinkObjId="g_3b04dd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-233508_0" Pin1InfoVect1LinkObjId="g_397af90_0" Pin1InfoVect2LinkObjId="SW-153604_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="29,-1218 29,-1236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_395dba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-933 554,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26070@0" ObjectIDZND0="26160@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153593_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,-933 554,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38f1100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-1001 571,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26066@x" ObjectIDND1="26070@x" ObjectIDZND0="26071@1" Pin0InfoVect0LinkObjId="SW-153594_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153589_0" Pin1InfoVect1LinkObjId="SW-153593_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,-1001 571,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38f31a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-1033 554,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26066@0" ObjectIDZND0="26071@x" ObjectIDZND1="26070@x" Pin0InfoVect0LinkObjId="SW-153594_0" Pin0InfoVect1LinkObjId="SW-153593_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153589_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="554,-1033 554,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38f0d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-1001 554,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26066@x" ObjectIDND1="26071@x" ObjectIDZND0="26070@1" Pin0InfoVect0LinkObjId="SW-153593_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153589_0" Pin1InfoVect1LinkObjId="SW-153594_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,-1001 554,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_390a350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,-1001 620,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26071@0" ObjectIDZND0="g_399a1f0@0" Pin0InfoVect0LinkObjId="g_399a1f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153594_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,-1001 620,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3978ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-1185 621,-1185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26069@0" ObjectIDZND0="g_39995c0@0" Pin0InfoVect0LinkObjId="g_39995c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153592_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-1185 621,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_399e270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="476,-1125 476,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3986c00@0" Pin0InfoVect0LinkObjId="g_3986c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="476,-1125 476,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39bd660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-1097 570,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26066@x" ObjectIDND1="26067@x" ObjectIDZND0="26068@1" Pin0InfoVect0LinkObjId="SW-153591_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153589_0" Pin1InfoVect1LinkObjId="SW-153590_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,-1097 570,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3966fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="606,-1097 618,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26068@0" ObjectIDZND0="g_38f7a40@0" Pin0InfoVect0LinkObjId="g_38f7a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="606,-1097 618,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3939c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-1060 554,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26066@1" ObjectIDZND0="26068@x" ObjectIDZND1="26067@x" Pin0InfoVect0LinkObjId="SW-153591_0" Pin0InfoVect1LinkObjId="SW-153590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153589_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="554,-1060 554,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3973200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-1097 554,-1127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26066@x" ObjectIDND1="26068@x" ObjectIDZND0="26067@0" Pin0InfoVect0LinkObjId="SW-153590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153589_0" Pin1InfoVect1LinkObjId="SW-153591_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,-1097 554,-1127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39ae0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="572,-1185 554,-1185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26069@1" ObjectIDZND0="26067@x" ObjectIDZND1="g_3926780@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-153590_0" Pin0InfoVect1LinkObjId="g_3926780_0" Pin0InfoVect2LinkObjId="g_39640c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153592_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="572,-1185 554,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39b1010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-1163 554,-1185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26067@1" ObjectIDZND0="26069@x" ObjectIDZND1="g_3926780@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-153592_0" Pin0InfoVect1LinkObjId="g_3926780_0" Pin0InfoVect2LinkObjId="g_39640c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="554,-1163 554,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3986040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="525,-1204 554,-1204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3926780@0" ObjectIDZND0="26067@x" ObjectIDZND1="26069@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-153590_0" Pin0InfoVect1LinkObjId="SW-153592_0" Pin0InfoVect2LinkObjId="g_39640c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3926780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="525,-1204 554,-1204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_398c890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-1185 554,-1204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="26067@x" ObjectIDND1="26069@x" ObjectIDZND0="g_3926780@0" ObjectIDZND1="0@x" ObjectIDZND2="34582@1" Pin0InfoVect0LinkObjId="g_3926780_0" Pin0InfoVect1LinkObjId="g_39640c0_0" Pin0InfoVect2LinkObjId="g_397ac00_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153590_0" Pin1InfoVect1LinkObjId="SW-153592_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="554,-1185 554,-1204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3998e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-1204 554,-1224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="26067@x" ObjectIDND1="26069@x" ObjectIDND2="g_3926780@0" ObjectIDZND0="0@x" ObjectIDZND1="34582@1" Pin0InfoVect0LinkObjId="g_39640c0_0" Pin0InfoVect1LinkObjId="g_397ac00_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-153590_0" Pin1InfoVect1LinkObjId="SW-153592_0" Pin1InfoVect2LinkObjId="g_3926780_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="554,-1204 554,-1224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_397ac00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-1224 554,-1250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="26067@x" ObjectIDND1="26069@x" ObjectIDND2="g_3926780@0" ObjectIDZND0="34582@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-153590_0" Pin1InfoVect1LinkObjId="SW-153592_0" Pin1InfoVect2LinkObjId="g_3926780_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,-1224 554,-1250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37ba6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="476,-1170 476,-1224 554,-1224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="26067@x" ObjectIDZND1="26069@x" ObjectIDZND2="g_3926780@0" Pin0InfoVect0LinkObjId="SW-153590_0" Pin0InfoVect1LinkObjId="SW-153592_0" Pin0InfoVect2LinkObjId="g_3926780_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="476,-1170 476,-1224 554,-1224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3974120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-926 1083,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26076@0" ObjectIDZND0="26157@0" Pin0InfoVect0LinkObjId="g_395fd20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-926 1083,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_393d700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-992 1100,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26072@x" ObjectIDND1="26076@x" ObjectIDZND0="26077@1" Pin0InfoVect0LinkObjId="SW-153600_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153595_0" Pin1InfoVect1LinkObjId="SW-153599_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-992 1100,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39c0100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-1024 1083,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26072@0" ObjectIDZND0="26077@x" ObjectIDZND1="26076@x" Pin0InfoVect0LinkObjId="SW-153600_0" Pin0InfoVect1LinkObjId="SW-153599_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153595_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-1024 1083,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39c38a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-992 1083,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26077@x" ObjectIDND1="26072@x" ObjectIDZND0="26076@1" Pin0InfoVect0LinkObjId="SW-153599_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153600_0" Pin1InfoVect1LinkObjId="SW-153595_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-992 1083,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_395f930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1136,-992 1150,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26077@0" ObjectIDZND0="g_4cc4e70@0" Pin0InfoVect0LinkObjId="g_4cc4e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1136,-992 1150,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3804c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1137,-1176 1151,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26075@0" ObjectIDZND0="g_38320f0@0" Pin0InfoVect0LinkObjId="g_38320f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153598_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1137,-1176 1151,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3974a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1005,-1116 1005,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3974c60@0" Pin0InfoVect0LinkObjId="g_3974c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1005,-1116 1005,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3952a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1082,-1088 1099,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26072@x" ObjectIDND1="26073@x" ObjectIDZND0="26074@1" Pin0InfoVect0LinkObjId="SW-153597_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153595_0" Pin1InfoVect1LinkObjId="SW-153596_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1082,-1088 1099,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38f5540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1135,-1088 1149,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26074@0" ObjectIDZND0="g_3938c90@0" Pin0InfoVect0LinkObjId="g_3938c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153597_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1135,-1088 1149,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38f5770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-1051 1083,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26072@1" ObjectIDZND0="26074@x" ObjectIDZND1="26073@x" Pin0InfoVect0LinkObjId="SW-153597_0" Pin0InfoVect1LinkObjId="SW-153596_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153595_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-1051 1083,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3989050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-1088 1083,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26074@x" ObjectIDND1="26072@x" ObjectIDZND0="26073@0" Pin0InfoVect0LinkObjId="SW-153596_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153597_0" Pin1InfoVect1LinkObjId="SW-153595_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-1088 1083,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39892b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1101,-1176 1083,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26075@1" ObjectIDZND0="26073@x" ObjectIDZND1="g_37fb010@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-153596_0" Pin0InfoVect1LinkObjId="g_37fb010_0" Pin0InfoVect2LinkObjId="g_39640c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153598_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1101,-1176 1083,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3950730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-1154 1083,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26073@1" ObjectIDZND0="26075@x" ObjectIDZND1="g_37fb010@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-153598_0" Pin0InfoVect1LinkObjId="g_37fb010_0" Pin0InfoVect2LinkObjId="g_39640c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153596_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-1154 1083,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3950990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-1195 1083,-1195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_37fb010@0" ObjectIDZND0="26075@x" ObjectIDZND1="26073@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-153598_0" Pin0InfoVect1LinkObjId="SW-153596_0" Pin0InfoVect2LinkObjId="g_39640c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37fb010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-1195 1083,-1195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39bc130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-1176 1083,-1195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="26075@x" ObjectIDND1="26073@x" ObjectIDZND0="g_37fb010@0" ObjectIDZND1="0@x" ObjectIDZND2="34583@1" Pin0InfoVect0LinkObjId="g_37fb010_0" Pin0InfoVect1LinkObjId="g_39640c0_0" Pin0InfoVect2LinkObjId="g_38275a0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153598_0" Pin1InfoVect1LinkObjId="SW-153596_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-1176 1083,-1195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39bc390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-1195 1083,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="g_37fb010@0" ObjectIDND1="26075@x" ObjectIDND2="26073@x" ObjectIDZND0="0@x" ObjectIDZND1="34583@1" Pin0InfoVect0LinkObjId="g_39640c0_0" Pin0InfoVect1LinkObjId="g_38275a0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_37fb010_0" Pin1InfoVect1LinkObjId="SW-153598_0" Pin1InfoVect2LinkObjId="SW-153596_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-1195 1083,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38275a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-1215 1083,-1241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_37fb010@0" ObjectIDND1="26075@x" ObjectIDND2="26073@x" ObjectIDZND0="34583@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_37fb010_0" Pin1InfoVect1LinkObjId="SW-153598_0" Pin1InfoVect2LinkObjId="SW-153596_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-1215 1083,-1241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3827800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1005,-1161 1005,-1215 1083,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_37fb010@0" ObjectIDZND1="26075@x" ObjectIDZND2="26073@x" Pin0InfoVect0LinkObjId="g_37fb010_0" Pin0InfoVect1LinkObjId="SW-153598_0" Pin0InfoVect2LinkObjId="SW-153596_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1005,-1161 1005,-1215 1083,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3984b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-926 1359,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26088@0" ObjectIDZND0="26157@0" Pin0InfoVect0LinkObjId="g_395fd20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153611_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-926 1359,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d3190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-992 1375,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26084@x" ObjectIDND1="26088@x" ObjectIDZND0="26089@1" Pin0InfoVect0LinkObjId="SW-153612_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153607_0" Pin1InfoVect1LinkObjId="SW-153611_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-992 1375,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37d33f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-1024 1359,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26084@0" ObjectIDZND0="26089@x" ObjectIDZND1="26088@x" Pin0InfoVect0LinkObjId="SW-153612_0" Pin0InfoVect1LinkObjId="SW-153611_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-1024 1359,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3908d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-992 1359,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26084@x" ObjectIDND1="26089@x" ObjectIDZND0="26088@1" Pin0InfoVect0LinkObjId="SW-153611_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153607_0" Pin1InfoVect1LinkObjId="SW-153612_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-992 1359,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_399bd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1411,-992 1421,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26089@0" ObjectIDZND0="g_398c2a0@0" Pin0InfoVect0LinkObjId="g_398c2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153612_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1411,-992 1421,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39bb380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1412,-1176 1422,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26087@0" ObjectIDZND0="g_3929a90@0" Pin0InfoVect0LinkObjId="g_3929a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1412,-1176 1422,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_39222e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1281,-1116 1281,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3922510@0" Pin0InfoVect0LinkObjId="g_3922510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1281,-1116 1281,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3938710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-1088 1374,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26084@x" ObjectIDND1="26085@x" ObjectIDZND0="26086@1" Pin0InfoVect0LinkObjId="SW-153609_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153607_0" Pin1InfoVect1LinkObjId="SW-153608_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-1088 1374,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_398b6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1410,-1088 1420,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26086@0" ObjectIDZND0="g_38f8d30@0" Pin0InfoVect0LinkObjId="g_38f8d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153609_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1410,-1088 1420,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3900d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-1051 1359,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26084@1" ObjectIDZND0="26086@x" ObjectIDZND1="26085@x" Pin0InfoVect0LinkObjId="SW-153609_0" Pin0InfoVect1LinkObjId="SW-153608_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153607_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-1051 1359,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3900f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-1088 1359,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26084@x" ObjectIDND1="26086@x" ObjectIDZND0="26085@0" Pin0InfoVect0LinkObjId="SW-153608_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153607_0" Pin1InfoVect1LinkObjId="SW-153609_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-1088 1359,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3901140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1376,-1176 1359,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26087@1" ObjectIDZND0="26085@x" ObjectIDZND1="g_39aa0b0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-153608_0" Pin0InfoVect1LinkObjId="g_39aa0b0_0" Pin0InfoVect2LinkObjId="g_39640c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1376,-1176 1359,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3901330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-1154 1359,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26085@1" ObjectIDZND0="26087@x" ObjectIDZND1="g_39aa0b0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-153610_0" Pin0InfoVect1LinkObjId="g_39aa0b0_0" Pin0InfoVect2LinkObjId="g_39640c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153608_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-1154 1359,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42ae940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-1195 1359,-1195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_39aa0b0@0" ObjectIDZND0="26085@x" ObjectIDZND1="26087@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-153608_0" Pin0InfoVect1LinkObjId="SW-153610_0" Pin0InfoVect2LinkObjId="g_39640c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39aa0b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-1195 1359,-1195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42aeb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-1176 1359,-1195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="26085@x" ObjectIDND1="26087@x" ObjectIDZND0="g_39aa0b0@0" ObjectIDZND1="0@x" ObjectIDZND2="38075@1" Pin0InfoVect0LinkObjId="g_39aa0b0_0" Pin0InfoVect1LinkObjId="g_39640c0_0" Pin0InfoVect2LinkObjId="g_42aefd0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153608_0" Pin1InfoVect1LinkObjId="SW-153610_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-1176 1359,-1195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42aeda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-1195 1359,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="26085@x" ObjectIDND1="26087@x" ObjectIDND2="g_39aa0b0@0" ObjectIDZND0="0@x" ObjectIDZND1="38075@1" Pin0InfoVect0LinkObjId="g_39640c0_0" Pin0InfoVect1LinkObjId="g_42aefd0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-153608_0" Pin1InfoVect1LinkObjId="SW-153610_0" Pin1InfoVect2LinkObjId="g_39aa0b0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-1195 1359,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42aefd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-1215 1359,-1241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="26085@x" ObjectIDND1="26087@x" ObjectIDND2="g_39aa0b0@0" ObjectIDZND0="38075@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-153608_0" Pin1InfoVect1LinkObjId="SW-153610_0" Pin1InfoVect2LinkObjId="g_39aa0b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-1215 1359,-1241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3983840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1281,-1161 1281,-1215 1359,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="26085@x" ObjectIDZND1="26087@x" ObjectIDZND2="g_39aa0b0@0" Pin0InfoVect0LinkObjId="SW-153608_0" Pin0InfoVect1LinkObjId="SW-153610_0" Pin0InfoVect2LinkObjId="g_39aa0b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1281,-1161 1281,-1215 1359,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cc39a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-645 719,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="26133@0" ObjectIDZND0="26096@1" Pin0InfoVect0LinkObjId="SW-153636_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39757c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-645 719,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3975300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-585 719,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26096@0" ObjectIDZND0="26155@1" Pin0InfoVect0LinkObjId="SW-153902_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153636_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-585 719,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3975560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-534 719,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26155@0" ObjectIDZND0="26158@0" Pin0InfoVect0LinkObjId="g_38e78f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153902_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-534 719,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39757c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-750 721,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="26093@0" ObjectIDZND0="26133@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153633_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-750 721,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3975a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-798 755,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26093@x" ObjectIDND1="26094@x" ObjectIDZND0="26095@1" Pin0InfoVect0LinkObjId="SW-153635_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153633_0" Pin1InfoVect1LinkObjId="SW-153634_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-798 755,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_395ee50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-798 805,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26095@0" ObjectIDZND0="g_399b420@0" Pin0InfoVect0LinkObjId="g_399b420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153635_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-798 805,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_395f5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-881 721,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26094@1" ObjectIDZND0="26157@0" Pin0InfoVect0LinkObjId="g_395fd20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153634_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-881 721,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_399d740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1204,-669 1204,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="26134@0" ObjectIDZND0="26101@1" Pin0InfoVect0LinkObjId="SW-153703_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_390d9d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1204,-669 1204,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f24e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1204,-607 1204,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26101@0" ObjectIDZND0="26102@1" Pin0InfoVect0LinkObjId="SW-153704_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153703_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1204,-607 1204,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_390d9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1206,-774 1206,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="26098@0" ObjectIDZND0="26134@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1206,-774 1206,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_390dc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1206,-822 1239,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26099@x" ObjectIDND1="26098@x" ObjectIDZND0="26100@1" Pin0InfoVect0LinkObjId="SW-153702_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153701_0" Pin1InfoVect1LinkObjId="SW-153700_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1206,-822 1239,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36db440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1275,-822 1289,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26100@0" ObjectIDZND0="g_3901f30@0" Pin0InfoVect0LinkObjId="g_3901f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153702_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1275,-822 1289,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36db6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1206,-843 1206,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26099@0" ObjectIDZND0="26100@x" ObjectIDZND1="26098@x" Pin0InfoVect0LinkObjId="SW-153702_0" Pin0InfoVect1LinkObjId="SW-153700_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1206,-843 1206,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36db900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1206,-822 1206,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26099@x" ObjectIDND1="26100@x" ObjectIDZND0="26098@1" Pin0InfoVect0LinkObjId="SW-153700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153701_0" Pin1InfoVect1LinkObjId="SW-153702_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1206,-822 1206,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a58f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="973,-899 973,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26157@0" ObjectIDZND0="26090@1" Pin0InfoVect0LinkObjId="SW-153613_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_395fd20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="973,-899 973,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38e4af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-770 931,-791 973,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_3a5b770@0" ObjectIDZND0="26091@x" ObjectIDZND1="26090@x" ObjectIDZND2="g_3aba5a0@0" Pin0InfoVect0LinkObjId="SW-153614_0" Pin0InfoVect1LinkObjId="SW-153613_0" Pin0InfoVect2LinkObjId="g_3aba5a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a5b770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="931,-770 931,-791 973,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38e5620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="973,-791 973,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_3a5b770@0" ObjectIDND1="26091@x" ObjectIDND2="26090@x" ObjectIDZND0="g_3aba5a0@0" Pin0InfoVect0LinkObjId="g_3aba5a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3a5b770_0" Pin1InfoVect1LinkObjId="SW-153614_0" Pin1InfoVect2LinkObjId="SW-153613_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="973,-791 973,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38ff410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="971,-803 1003,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="26090@x" ObjectIDND1="g_3a5b770@0" ObjectIDND2="g_3aba5a0@0" ObjectIDZND0="26091@1" Pin0InfoVect0LinkObjId="SW-153614_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-153613_0" Pin1InfoVect1LinkObjId="g_3a5b770_0" Pin1InfoVect2LinkObjId="g_3aba5a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="971,-803 1003,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_395c180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1039,-803 1053,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26091@0" ObjectIDZND0="g_3a58470@0" Pin0InfoVect0LinkObjId="g_3a58470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153614_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1039,-803 1053,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_395ccb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="973,-826 973,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="26090@0" ObjectIDZND0="26091@x" ObjectIDZND1="g_3a5b770@0" ObjectIDZND2="g_3aba5a0@0" Pin0InfoVect0LinkObjId="SW-153614_0" Pin0InfoVect1LinkObjId="g_3a5b770_0" Pin0InfoVect2LinkObjId="g_3aba5a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="973,-826 973,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_395cf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="973,-803 973,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="26091@x" ObjectIDND1="26090@x" ObjectIDZND0="g_3a5b770@0" ObjectIDZND1="g_3aba5a0@0" Pin0InfoVect0LinkObjId="g_3a5b770_0" Pin0InfoVect1LinkObjId="g_3aba5a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153614_0" Pin1InfoVect1LinkObjId="SW-153613_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="973,-803 973,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e78f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-239,-481 -239,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26105@1" ObjectIDZND0="26158@0" Pin0InfoVect0LinkObjId="g_3975560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153767_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-239,-481 -239,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e7b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-239,-445 -239,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26105@0" ObjectIDZND0="26104@1" Pin0InfoVect0LinkObjId="SW-153766_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-239,-445 -239,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e7db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-239,-378 -239,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26104@0" ObjectIDZND0="26106@1" Pin0InfoVect0LinkObjId="SW-153768_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153766_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-239,-378 -239,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38eb1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-203,-270 -203,-285 -239,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_38e6bc0@0" ObjectIDZND0="26106@x" ObjectIDZND1="34217@x" Pin0InfoVect0LinkObjId="SW-153768_0" Pin0InfoVect1LinkObjId="EC-DY_BC.051Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38e6bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-203,-270 -203,-285 -239,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38eb400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-239,-303 -239,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26106@0" ObjectIDZND0="g_38e6bc0@0" ObjectIDZND1="34217@x" Pin0InfoVect0LinkObjId="g_38e6bc0_0" Pin0InfoVect1LinkObjId="EC-DY_BC.051Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153768_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-239,-303 -239,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38eb630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-239,-285 -239,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_38e6bc0@0" ObjectIDND1="26106@x" ObjectIDZND0="34217@0" Pin0InfoVect0LinkObjId="EC-DY_BC.051Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_38e6bc0_0" Pin1InfoVect1LinkObjId="SW-153768_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-239,-285 -239,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_391ccc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-104,-477 -104,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26108@1" ObjectIDZND0="26158@0" Pin0InfoVect0LinkObjId="g_3975560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-104,-477 -104,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_391cf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-104,-441 -104,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26108@0" ObjectIDZND0="26107@1" Pin0InfoVect0LinkObjId="SW-153769_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-104,-441 -104,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_391d180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-104,-374 -104,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26107@0" ObjectIDZND0="26109@1" Pin0InfoVect0LinkObjId="SW-153771_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153769_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-104,-374 -104,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39054f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-68,-266 -68,-281 -104,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_391bf90@0" ObjectIDZND0="26109@x" ObjectIDZND1="34218@x" Pin0InfoVect0LinkObjId="SW-153771_0" Pin0InfoVect1LinkObjId="EC-DY_BC.052Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_391bf90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-68,-266 -68,-281 -104,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3905720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-104,-299 -104,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26109@0" ObjectIDZND0="g_391bf90@0" ObjectIDZND1="34218@x" Pin0InfoVect0LinkObjId="g_391bf90_0" Pin0InfoVect1LinkObjId="EC-DY_BC.052Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153771_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-104,-299 -104,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3905950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-104,-281 -104,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_391bf90@0" ObjectIDND1="26109@x" ObjectIDZND0="34218@0" Pin0InfoVect0LinkObjId="EC-DY_BC.052Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_391bf90_0" Pin1InfoVect1LinkObjId="SW-153771_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-104,-281 -104,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_397ed30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,-478 39,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26111@1" ObjectIDZND0="26158@0" Pin0InfoVect0LinkObjId="g_3975560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153773_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="39,-478 39,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_397ef90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,-442 39,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26111@0" ObjectIDZND0="26110@1" Pin0InfoVect0LinkObjId="SW-153772_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="39,-442 39,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_397f1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,-375 39,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26110@0" ObjectIDZND0="26112@1" Pin0InfoVect0LinkObjId="SW-153774_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="39,-375 39,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3982710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="75,-267 75,-282 39,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_397e000@0" ObjectIDZND0="26112@x" ObjectIDZND1="34219@x" Pin0InfoVect0LinkObjId="SW-153774_0" Pin0InfoVect1LinkObjId="EC-DY_BC.053Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_397e000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="75,-267 75,-282 39,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3982940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,-300 39,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26112@0" ObjectIDZND0="g_397e000@0" ObjectIDZND1="34219@x" Pin0InfoVect0LinkObjId="g_397e000_0" Pin0InfoVect1LinkObjId="EC-DY_BC.053Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="39,-300 39,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a62e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,-282 39,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_397e000@0" ObjectIDND1="26112@x" ObjectIDZND0="34219@0" Pin0InfoVect0LinkObjId="EC-DY_BC.053Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_397e000_0" Pin1InfoVect1LinkObjId="SW-153774_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="39,-282 39,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_396bdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,-473 183,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26114@1" ObjectIDZND0="26158@0" Pin0InfoVect0LinkObjId="g_3975560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153776_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="183,-473 183,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_396c010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,-437 183,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26114@0" ObjectIDZND0="26113@1" Pin0InfoVect0LinkObjId="SW-153775_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153776_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="183,-437 183,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_396c270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,-370 183,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26113@0" ObjectIDZND0="26115@1" Pin0InfoVect0LinkObjId="SW-153777_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153775_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="183,-370 183,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b5910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="219,-262 219,-277 183,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_396b080@0" ObjectIDZND0="26115@x" ObjectIDZND1="34220@x" Pin0InfoVect0LinkObjId="SW-153777_0" Pin0InfoVect1LinkObjId="EC-DY_BC.054Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_396b080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="219,-262 219,-277 183,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b5b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,-295 183,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26115@0" ObjectIDZND0="g_396b080@0" ObjectIDZND1="34220@x" Pin0InfoVect0LinkObjId="g_396b080_0" Pin0InfoVect1LinkObjId="EC-DY_BC.054Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153777_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="183,-295 183,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b5d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="183,-277 183,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_396b080@0" ObjectIDND1="26115@x" ObjectIDZND0="34220@0" Pin0InfoVect0LinkObjId="EC-DY_BC.054Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_396b080_0" Pin1InfoVect1LinkObjId="SW-153777_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="183,-277 183,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3991950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="317,-474 317,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26117@1" ObjectIDZND0="26158@0" Pin0InfoVect0LinkObjId="g_3975560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153779_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="317,-474 317,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3991bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="317,-438 317,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26117@0" ObjectIDZND0="26116@1" Pin0InfoVect0LinkObjId="SW-153778_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="317,-438 317,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3991e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="317,-371 317,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26116@0" ObjectIDZND0="26118@1" Pin0InfoVect0LinkObjId="SW-153780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153778_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="317,-371 317,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3994dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-263 353,-278 317,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3990c80@0" ObjectIDZND0="26118@x" ObjectIDZND1="34221@x" Pin0InfoVect0LinkObjId="SW-153780_0" Pin0InfoVect1LinkObjId="EC-DY_BC.055Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3990c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="353,-263 353,-278 317,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3995000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="317,-296 317,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26118@0" ObjectIDZND0="g_3990c80@0" ObjectIDZND1="34221@x" Pin0InfoVect0LinkObjId="g_3990c80_0" Pin0InfoVect1LinkObjId="EC-DY_BC.055Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="317,-296 317,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3995230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="317,-278 317,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3990c80@0" ObjectIDND1="26118@x" ObjectIDZND0="34221@0" Pin0InfoVect0LinkObjId="EC-DY_BC.055Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3990c80_0" Pin1InfoVect1LinkObjId="SW-153780_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="317,-278 317,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a35c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1454,-479 1454,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26126@1" ObjectIDZND0="26159@0" Pin0InfoVect0LinkObjId="g_39330d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153788_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1454,-479 1454,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a3820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1454,-440 1454,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26126@0" ObjectIDZND0="26125@1" Pin0InfoVect0LinkObjId="SW-153787_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153788_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1454,-440 1454,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a3a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1454,-373 1454,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26125@0" ObjectIDZND0="26127@1" Pin0InfoVect0LinkObjId="SW-153789_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153787_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1454,-373 1454,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a6a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-265 1490,-280 1454,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_394b010@0" ObjectIDZND0="26127@x" ObjectIDZND1="34224@x" Pin0InfoVect0LinkObjId="SW-153789_0" Pin0InfoVect1LinkObjId="EC-DY_BC.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_394b010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-265 1490,-280 1454,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a6c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1454,-298 1454,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26127@0" ObjectIDZND0="g_394b010@0" ObjectIDZND1="34224@x" Pin0InfoVect0LinkObjId="g_394b010_0" Pin0InfoVect1LinkObjId="EC-DY_BC.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153789_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1454,-298 1454,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a6ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1454,-280 1454,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_394b010@0" ObjectIDND1="26127@x" ObjectIDZND0="34224@0" Pin0InfoVect0LinkObjId="EC-DY_BC.073Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_394b010_0" Pin1InfoVect1LinkObjId="SW-153789_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1454,-280 1454,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39330d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1297,-473 1297,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26123@1" ObjectIDZND0="26159@0" Pin0InfoVect0LinkObjId="g_39a35c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153785_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1297,-473 1297,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3933330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1297,-437 1297,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26123@0" ObjectIDZND0="26122@1" Pin0InfoVect0LinkObjId="SW-153784_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153785_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1297,-437 1297,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3933590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1297,-370 1297,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26122@0" ObjectIDZND0="26124@1" Pin0InfoVect0LinkObjId="SW-153786_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153784_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1297,-370 1297,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a4ff40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1333,-262 1333,-277 1297,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_39323a0@0" ObjectIDZND0="26124@x" ObjectIDZND1="34223@x" Pin0InfoVect0LinkObjId="SW-153786_0" Pin0InfoVect1LinkObjId="EC-DY_BC.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39323a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1333,-262 1333,-277 1297,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a50170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1297,-295 1297,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26124@0" ObjectIDZND0="g_39323a0@0" ObjectIDZND1="34223@x" Pin0InfoVect0LinkObjId="g_39323a0_0" Pin0InfoVect1LinkObjId="EC-DY_BC.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153786_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1297,-295 1297,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a503a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1297,-277 1297,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_39323a0@0" ObjectIDND1="26124@x" ObjectIDZND0="34223@0" Pin0InfoVect0LinkObjId="EC-DY_BC.072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_39323a0_0" Pin1InfoVect1LinkObjId="SW-153786_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1297,-277 1297,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a56460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-467 1055,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26120@1" ObjectIDZND0="26159@0" Pin0InfoVect0LinkObjId="g_39a35c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153782_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-467 1055,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a566c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-431 1055,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26120@0" ObjectIDZND0="26119@1" Pin0InfoVect0LinkObjId="SW-153781_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153782_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-431 1055,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a56920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-364 1055,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26119@0" ObjectIDZND0="26121@1" Pin0InfoVect0LinkObjId="SW-153783_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-364 1055,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a6a9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1091,-256 1091,-271 1055,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3a55730@0" ObjectIDZND0="26121@x" ObjectIDZND1="34222@x" Pin0InfoVect0LinkObjId="SW-153783_0" Pin0InfoVect1LinkObjId="EC-DY_BC.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a55730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1091,-256 1091,-271 1055,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a6ac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-289 1055,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26121@0" ObjectIDZND0="g_3a55730@0" ObjectIDZND1="34222@x" Pin0InfoVect0LinkObjId="g_3a55730_0" Pin0InfoVect1LinkObjId="EC-DY_BC.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153783_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-289 1055,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a6ae50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-271 1055,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3a55730@0" ObjectIDND1="26121@x" ObjectIDZND0="34222@0" Pin0InfoVect0LinkObjId="EC-DY_BC.071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3a55730_0" Pin1InfoVect1LinkObjId="SW-153783_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-271 1055,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a6c4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="978,-511 978,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26159@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_39640c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39a35c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="978,-511 978,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a6fe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-511 1169,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26159@0" ObjectIDZND0="26129@1" Pin0InfoVect0LinkObjId="SW-153791_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39a35c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-511 1169,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a723c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1204,-532 1204,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26102@0" ObjectIDZND0="26159@0" Pin0InfoVect0LinkObjId="g_39a35c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153704_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1204,-532 1204,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a72620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1206,-879 1206,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26099@1" ObjectIDZND0="26157@0" Pin0InfoVect0LinkObjId="g_395fd20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153701_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1206,-879 1206,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a72880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-341 1169,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3ac5e40@0" Pin0InfoVect0LinkObjId="g_3ac5e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-341 1169,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a76a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1211,-406 1211,-421 1169,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3a72ae0@0" ObjectIDZND0="26129@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-153791_0" Pin0InfoVect1LinkObjId="g_39640c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a72ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1211,-406 1211,-421 1169,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a77510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-434 1169,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="26129@0" ObjectIDZND0="g_3a72ae0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3a72ae0_0" Pin0InfoVect1LinkObjId="g_39640c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153791_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-434 1169,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a77770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-421 1169,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3a72ae0@0" ObjectIDND1="26129@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_39640c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3a72ae0_0" Pin1InfoVect1LinkObjId="SW-153791_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-421 1169,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a78680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="763,-512 763,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26158@0" ObjectIDZND0="26131@1" Pin0InfoVect0LinkObjId="SW-153795_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3975560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="763,-512 763,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a7ae20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="890,-511 890,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26159@0" ObjectIDZND0="26132@1" Pin0InfoVect0LinkObjId="SW-153796_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39a35c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="890,-511 890,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a7d840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="813,-425 763,-425 763,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26130@1" ObjectIDZND0="26131@0" Pin0InfoVect0LinkObjId="SW-153795_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153794_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="813,-425 763,-425 763,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a7daa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-425 890,-425 890,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26130@0" ObjectIDZND0="26132@0" Pin0InfoVect0LinkObjId="SW-153796_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153794_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="840,-425 890,-425 890,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a80600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="688,-512 688,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26158@0" ObjectIDZND0="26128@1" Pin0InfoVect0LinkObjId="SW-153790_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3975560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="688,-512 688,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a83070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="688,-346 688,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3ac1580@0" Pin0InfoVect0LinkObjId="g_3ac1580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="688,-346 688,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a87110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="730,-411 730,-426 688,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3a832d0@0" ObjectIDZND0="26128@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-153790_0" Pin0InfoVect1LinkObjId="g_39640c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a832d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="730,-411 730,-426 688,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a87370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="688,-439 688,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="26128@0" ObjectIDZND0="g_3a832d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3a832d0_0" Pin0InfoVect1LinkObjId="g_39640c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="688,-439 688,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a875d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="688,-426 688,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="26128@x" ObjectIDND1="g_3a832d0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_39640c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153790_0" Pin1InfoVect1LinkObjId="g_3a832d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="688,-426 688,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3abf660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1421,-878 1421,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="26157@0" Pin0InfoVect0LinkObjId="g_395fd20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1421,-878 1421,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3abf8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1422,-824 1422,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_39640c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1422,-824 1422,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ac5be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="977,-418 977,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_39640c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="977,-418 977,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3acbe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-777 721,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26093@1" ObjectIDZND0="26095@x" ObjectIDZND1="26094@x" Pin0InfoVect0LinkObjId="SW-153635_0" Pin0InfoVect1LinkObjId="SW-153634_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153633_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="721,-777 721,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3acc0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-798 721,-846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26095@x" ObjectIDND1="26093@x" ObjectIDZND0="26094@0" Pin0InfoVect0LinkObjId="SW-153634_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153635_0" Pin1InfoVect1LinkObjId="SW-153633_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-798 721,-846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3acfa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-119,-769 -119,-790 -77,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3acece0@0" ObjectIDZND0="g_3ad3f00@0" ObjectIDZND1="38939@x" ObjectIDZND2="38940@x" Pin0InfoVect0LinkObjId="g_3ad3f00_0" Pin0InfoVect1LinkObjId="SW-233579_0" Pin0InfoVect2LinkObjId="SW-233580_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3acece0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-119,-769 -119,-790 -77,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3acfc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-77,-790 -77,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_3acece0@0" ObjectIDND1="38939@x" ObjectIDND2="38940@x" ObjectIDZND0="g_3ad3f00@0" Pin0InfoVect0LinkObjId="g_3ad3f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3acece0_0" Pin1InfoVect1LinkObjId="SW-233579_0" Pin1InfoVect2LinkObjId="SW-233580_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-77,-790 -77,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3acfed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-77,-790 -58,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3ad3f00@0" ObjectIDND1="g_3acece0@0" ObjectIDND2="38939@x" ObjectIDZND0="38940@1" Pin0InfoVect0LinkObjId="SW-233580_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ad3f00_0" Pin1InfoVect1LinkObjId="g_3acece0_0" Pin1InfoVect2LinkObjId="SW-233579_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-77,-790 -58,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ad3670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-22,-790 -12,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38940@0" ObjectIDZND0="g_3ad2be0@0" Pin0InfoVect0LinkObjId="g_3ad2be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-22,-790 -12,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ae4ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="380,-899 380,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26160@0" ObjectIDZND0="38937@1" Pin0InfoVect0LinkObjId="SW-233519_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_395dba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="380,-899 380,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ae9810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-995 254,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="38925@x" ObjectIDND1="38926@x" ObjectIDZND0="38927@1" Pin0InfoVect0LinkObjId="SW-233555_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-233553_0" Pin1InfoVect1LinkObjId="SW-233554_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-995 254,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ae9a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-1027 234,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38925@0" ObjectIDZND0="38927@x" ObjectIDZND1="38926@x" Pin0InfoVect0LinkObjId="SW-233555_0" Pin0InfoVect1LinkObjId="SW-233554_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233553_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="234,-1027 234,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ae9cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-995 234,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="38927@x" ObjectIDND1="38925@x" ObjectIDZND0="38926@1" Pin0InfoVect0LinkObjId="SW-233554_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-233555_0" Pin1InfoVect1LinkObjId="SW-233553_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-995 234,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3aed470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="290,-995 299,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38927@0" ObjectIDZND0="g_3aec9e0@0" Pin0InfoVect0LinkObjId="g_3aec9e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233555_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="290,-995 299,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3af3410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="286,-1179 300,-1179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38930@0" ObjectIDZND0="g_3af2980@0" Pin0InfoVect0LinkObjId="g_3af2980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233557_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="286,-1179 300,-1179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3af43a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="174,-1119 174,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3af4600@0" Pin0InfoVect0LinkObjId="g_3af4600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="174,-1119 174,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3af9140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-1091 253,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="38925@x" ObjectIDND1="38928@x" ObjectIDZND0="38931@1" Pin0InfoVect0LinkObjId="SW-233577_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-233553_0" Pin1InfoVect1LinkObjId="SW-233556_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-1091 253,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3afc4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-1091 298,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38931@0" ObjectIDZND0="g_3afba20@0" Pin0InfoVect0LinkObjId="g_3afba20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233577_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-1091 298,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3afc710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-1054 234,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38925@1" ObjectIDZND0="38931@x" ObjectIDZND1="38928@x" Pin0InfoVect0LinkObjId="SW-233577_0" Pin0InfoVect1LinkObjId="SW-233556_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233553_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="234,-1054 234,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3afc970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-1091 234,-1121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="38931@x" ObjectIDND1="38925@x" ObjectIDZND0="38928@0" Pin0InfoVect0LinkObjId="SW-233556_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-233577_0" Pin1InfoVect1LinkObjId="SW-233553_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-1091 234,-1121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3afcbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="255,-1179 234,-1179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="38930@1" ObjectIDZND0="38928@x" ObjectIDZND1="g_3af3670@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="SW-233556_0" Pin0InfoVect1LinkObjId="g_3af3670_0" Pin0InfoVect2LinkObjId="g_39640c0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233557_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="255,-1179 234,-1179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3afce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-1157 234,-1179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="38928@1" ObjectIDZND0="38930@x" ObjectIDZND1="g_3af3670@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="SW-233557_0" Pin0InfoVect1LinkObjId="g_3af3670_0" Pin0InfoVect2LinkObjId="g_39640c0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233556_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="234,-1157 234,-1179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3afd090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="205,-1186 234,-1186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_3af3670@0" ObjectIDZND0="38930@x" ObjectIDZND1="38928@x" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="SW-233557_0" Pin0InfoVect1LinkObjId="SW-233556_0" Pin0InfoVect2LinkObjId="g_39640c0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3af3670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="205,-1186 234,-1186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3afd2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-1218 234,-1236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="38929@x" ObjectIDND1="g_3af3670@0" ObjectIDND2="38930@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_39640c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-233558_0" Pin1InfoVect1LinkObjId="g_3af3670_0" Pin1InfoVect2LinkObjId="SW-233557_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-1218 234,-1236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3afd790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-927 234,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38926@0" ObjectIDZND0="26157@0" Pin0InfoVect0LinkObjId="g_395fd20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233554_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-927 234,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b04b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-29,-1164 -29,-1174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="38938@0" Pin0InfoVect0LinkObjId="SW-233508_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-29,-1164 -29,-1174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b04dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-29,-1210 -29,-1218 29,-1218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38938@1" ObjectIDZND0="38074@1" ObjectIDZND1="g_397af90@0" ObjectIDZND2="26081@x" Pin0InfoVect0LinkObjId="g_4cc48e0_1" Pin0InfoVect1LinkObjId="g_397af90_0" Pin0InfoVect2LinkObjId="SW-153604_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233508_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-29,-1210 -29,-1218 29,-1218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b07830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="174,-1164 174,-1174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="38929@0" Pin0InfoVect0LinkObjId="SW-233558_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="174,-1164 174,-1174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b07a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="174,-1210 174,-1218 234,-1218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38929@1" ObjectIDZND0="0@1" ObjectIDZND1="g_3af3670@0" ObjectIDZND2="38930@x" Pin0InfoVect0LinkObjId="g_39640c0_1" Pin0InfoVect1LinkObjId="g_3af3670_0" Pin0InfoVect2LinkObjId="SW-233557_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233558_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="174,-1210 174,-1218 234,-1218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b095e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-1179 234,-1186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="38930@x" ObjectIDND1="38928@x" ObjectIDZND0="g_3af3670@0" ObjectIDZND1="0@1" ObjectIDZND2="38929@x" Pin0InfoVect0LinkObjId="g_3af3670_0" Pin0InfoVect1LinkObjId="g_39640c0_1" Pin0InfoVect2LinkObjId="SW-233558_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-233557_0" Pin1InfoVect1LinkObjId="SW-233556_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="234,-1179 234,-1186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b097d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-1218 234,-1186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDND1="38929@x" ObjectIDZND0="g_3af3670@0" ObjectIDZND1="38930@x" ObjectIDZND2="38928@x" Pin0InfoVect0LinkObjId="g_3af3670_0" Pin0InfoVect1LinkObjId="SW-233557_0" Pin0InfoVect2LinkObjId="SW-233556_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_39640c0_1" Pin1InfoVect1LinkObjId="SW-233558_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="234,-1218 234,-1186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b0a1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="29,-1179 29,-1186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="26081@x" ObjectIDND1="26079@x" ObjectIDZND0="g_397af90@0" ObjectIDZND1="38074@1" ObjectIDZND2="38938@x" Pin0InfoVect0LinkObjId="g_397af90_0" Pin0InfoVect1LinkObjId="g_4cc48e0_1" Pin0InfoVect2LinkObjId="SW-233508_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153604_0" Pin1InfoVect1LinkObjId="SW-153602_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="29,-1179 29,-1186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b0a430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="29,-1186 29,-1218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" EndDevType1="switch" ObjectIDND0="g_397af90@0" ObjectIDND1="26081@x" ObjectIDND2="26079@x" ObjectIDZND0="38074@1" ObjectIDZND1="38938@x" Pin0InfoVect0LinkObjId="g_4cc48e0_1" Pin0InfoVect1LinkObjId="SW-233508_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_397af90_0" Pin1InfoVect1LinkObjId="SW-153604_0" Pin1InfoVect2LinkObjId="SW-153602_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="29,-1186 29,-1218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b0da60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="319,-868 330,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38936@0" ObjectIDZND0="g_3b0cfd0@0" Pin0InfoVect0LinkObjId="g_3b0cfd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="319,-868 330,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b11da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="424,-794 435,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38935@0" ObjectIDZND0="g_3b11310@0" Pin0InfoVect0LinkObjId="g_3b11310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233521_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="424,-794 435,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b12000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-794 380,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="38935@1" ObjectIDZND0="38932@x" ObjectIDZND1="38937@x" Pin0InfoVect0LinkObjId="SW-233516_0" Pin0InfoVect1LinkObjId="SW-233519_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233521_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="388,-794 380,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b12af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="338,-794 380,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38932@0" ObjectIDZND0="38935@x" ObjectIDZND1="38937@x" Pin0InfoVect0LinkObjId="SW-233521_0" Pin0InfoVect1LinkObjId="SW-233519_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233516_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="338,-794 380,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b12d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="380,-794 380,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="38935@x" ObjectIDND1="38932@x" ObjectIDZND0="38937@0" Pin0InfoVect0LinkObjId="SW-233519_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-233521_0" Pin1InfoVect1LinkObjId="SW-233516_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="380,-794 380,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b12fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="260,-794 273,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="38934@0" ObjectIDZND0="38932@x" ObjectIDZND1="38933@x" Pin0InfoVect0LinkObjId="SW-233516_0" Pin0InfoVect1LinkObjId="SW-233517_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233518_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="260,-794 273,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b16550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="312,-794 273,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38932@1" ObjectIDZND0="38934@x" ObjectIDZND1="38933@x" Pin0InfoVect0LinkObjId="SW-233518_0" Pin0InfoVect1LinkObjId="SW-233517_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233516_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="312,-794 273,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b167b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,-794 273,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="38934@x" ObjectIDND1="38932@x" ObjectIDZND0="38933@0" Pin0InfoVect0LinkObjId="SW-233517_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-233518_0" Pin1InfoVect1LinkObjId="SW-233516_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="273,-794 273,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b174a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="213,-794 224,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3b16a10@0" ObjectIDZND0="38934@1" Pin0InfoVect0LinkObjId="SW-233518_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b16a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="213,-794 224,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b17700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="283,-868 273,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="38936@1" ObjectIDZND0="26157@0" ObjectIDZND1="38933@x" Pin0InfoVect0LinkObjId="g_395fd20_0" Pin0InfoVect1LinkObjId="SW-233517_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233520_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="283,-868 273,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b181f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,-899 273,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26157@0" ObjectIDZND0="38936@x" ObjectIDZND1="38933@x" Pin0InfoVect0LinkObjId="SW-233520_0" Pin0InfoVect1LinkObjId="SW-233517_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_395fd20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="273,-899 273,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b18450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,-868 273,-854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="38936@x" ObjectIDND1="26157@0" ObjectIDZND0="38933@1" Pin0InfoVect0LinkObjId="SW-233517_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-233520_0" Pin1InfoVect1LinkObjId="g_395fd20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="273,-868 273,-854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1a3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-77,-790 -77,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3ad3f00@0" ObjectIDND1="g_3acece0@0" ObjectIDND2="38940@x" ObjectIDZND0="38939@0" Pin0InfoVect0LinkObjId="SW-233579_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ad3f00_0" Pin1InfoVect1LinkObjId="g_3acece0_0" Pin1InfoVect2LinkObjId="SW-233580_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-77,-790 -77,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1d870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-22,-866 -12,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26092@0" ObjectIDZND0="g_3b1cde0@0" Pin0InfoVect0LinkObjId="g_3b1cde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-153615_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-22,-866 -12,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1e100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-77,-866 -58,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26157@0" ObjectIDND1="38939@x" ObjectIDZND0="26092@1" Pin0InfoVect0LinkObjId="SW-153615_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_395fd20_0" Pin1InfoVect1LinkObjId="SW-233579_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-77,-866 -58,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1ea70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-77,-899 -77,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26157@0" ObjectIDZND0="26092@x" ObjectIDZND1="38939@x" Pin0InfoVect0LinkObjId="SW-153615_0" Pin0InfoVect1LinkObjId="SW-233579_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_395fd20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-77,-899 -77,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1ec60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-77,-866 -77,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="26092@x" ObjectIDND1="26157@0" ObjectIDZND0="38939@1" Pin0InfoVect0LinkObjId="SW-233579_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-153615_0" Pin1InfoVect1LinkObjId="g_395fd20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-77,-866 -77,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b2ba20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="570,-74 570,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39640c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="570,-74 570,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b2bc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="485,-125 570,-125 570,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3b27730@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_39640c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b27730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="485,-125 570,-125 570,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b2c680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="569,-30 569,-8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_4cff520@0" Pin0InfoVect0LinkObjId="g_4cff520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="569,-30 569,-8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b2ca80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="526,-28 526,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_42af320@0" Pin0InfoVect0LinkObjId="g_42af320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="526,-28 526,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b2df10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="526,-133 526,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="44670@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-283599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="526,-133 526,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b35970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="496,-434 496,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44668@0" ObjectIDZND0="44667@1" Pin0InfoVect0LinkObjId="SW-283575_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-283576_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="496,-434 496,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b35bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="496,-367 496,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44667@0" ObjectIDZND0="44669@1" Pin0InfoVect0LinkObjId="SW-283577_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-283575_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="496,-367 496,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3a9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="526,-159 526,-188 536,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="44670@1" ObjectIDZND0="g_3b2d1a0@0" ObjectIDZND1="44669@x" ObjectIDZND2="g_3b37f00@0" Pin0InfoVect0LinkObjId="g_3b2d1a0_0" Pin0InfoVect1LinkObjId="SW-283577_0" Pin0InfoVect2LinkObjId="g_3b37f00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-283599_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="526,-159 526,-188 536,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3abd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="536,-188 557,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="44670@x" ObjectIDND1="44669@x" ObjectIDND2="g_3b37f00@0" ObjectIDZND0="g_3b2d1a0@0" Pin0InfoVect0LinkObjId="g_3b2d1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-283599_0" Pin1InfoVect1LinkObjId="SW-283577_0" Pin1InfoVect2LinkObjId="g_3b37f00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="536,-188 557,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3af60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="496,-292 496,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="44669@0" ObjectIDZND0="g_3b37f00@0" ObjectIDZND1="44670@x" ObjectIDZND2="g_3b2d1a0@0" Pin0InfoVect0LinkObjId="g_3b37f00_0" Pin0InfoVect1LinkObjId="SW-283599_0" Pin0InfoVect2LinkObjId="g_3b2d1a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-283577_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="496,-292 496,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3ba20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="532,-254 532,-269 496,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3b37f00@0" ObjectIDZND0="44669@x" ObjectIDZND1="44670@x" ObjectIDZND2="g_3b2d1a0@0" Pin0InfoVect0LinkObjId="SW-283577_0" Pin0InfoVect1LinkObjId="SW-283599_0" Pin0InfoVect2LinkObjId="g_3b2d1a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b37f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="532,-254 532,-269 496,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3bc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="496,-269 496,-188 536,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="44669@x" ObjectIDND1="g_3b37f00@0" ObjectIDZND0="44670@x" ObjectIDZND1="g_3b2d1a0@0" Pin0InfoVect0LinkObjId="SW-283599_0" Pin0InfoVect1LinkObjId="g_3b2d1a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-283577_0" Pin1InfoVect1LinkObjId="g_3b37f00_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="496,-269 496,-188 536,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b3bec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="496,-470 496,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44668@1" ObjectIDZND0="26158@0" Pin0InfoVect0LinkObjId="g_3975560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-283576_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="496,-470 496,-512 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153541" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -399.000000 -1152.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26031" ObjectName="DYN-DY_BC"/>
     <cge:Meas_Ref ObjectId="153541"/>
    </metadata>
   </g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="520,-34 521,-34 521,-34 522,-34 522,-34 523,-35 523,-35 523,-35 523,-36 524,-36 524,-37 524,-37 524,-38 524,-38 524,-39 524,-39 524,-40 523,-40 523,-40 523,-41 523,-41 522,-41 522,-42 521,-42 521,-42 520,-42 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="520,-41 521,-41 521,-42 522,-42 522,-42 523,-42 523,-43 523,-43 523,-43 524,-44 524,-44 524,-45 524,-45 524,-46 524,-46 524,-47 524,-47 523,-48 523,-48 523,-48 523,-49 522,-49 522,-49 521,-49 521,-50 520,-50 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="520,-42 521,-42 521,-42 522,-42 522,-42 523,-43 523,-43 523,-43 523,-44 524,-44 524,-45 524,-45 524,-46 524,-46 524,-47 524,-47 524,-48 523,-48 523,-48 523,-49 523,-49 522,-49 522,-50 521,-50 521,-50 520,-50 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="520,-24 521,-24 521,-24 522,-24 522,-24 523,-24 523,-25 523,-25 523,-25 524,-26 524,-26 524,-27 524,-27 524,-28 524,-28 524,-29 524,-29 523,-30 523,-30 523,-31 523,-31 522,-31 522,-31 521,-32 521,-32 520,-32 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_BC.DY_BC_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="667,-899 1603,-899 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26157" ObjectName="BS-DY_BC.DY_BC_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   <polyline fill="none" opacity="0" points="667,-899 1603,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_BC.DY_BC_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="353,-899 631,-899 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26160" ObjectName="BS-DY_BC.DY_BC_3ⅡM"/>
    <cge:TPSR_Ref TObjectID="26160"/></metadata>
   <polyline fill="none" opacity="0" points="353,-899 631,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_BC.DY_BC_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="864,-511 1599,-511 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26159" ObjectName="BS-DY_BC.DY_BC_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="26159"/></metadata>
   <polyline fill="none" opacity="0" points="864,-511 1599,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_BC.DY_BC_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-301,-512 790,-512 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26158" ObjectName="BS-DY_BC.DY_BC_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="26158"/></metadata>
   <polyline fill="none" opacity="0" points="-301,-512 790,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_BC.DY_BC_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-185,-899 300,-899 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26157" ObjectName="BS-DY_BC.DY_BC_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="26157"/></metadata>
   <polyline fill="none" opacity="0" points="-185,-899 300,-899 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="26159" cx="1454" cy="-511" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26159" cx="1297" cy="-511" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26159" cx="1055" cy="-511" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26159" cx="978" cy="-511" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26159" cx="1169" cy="-511" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26159" cx="1204" cy="-511" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26158" cx="719" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26158" cx="-239" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26158" cx="-104" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26158" cx="39" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26158" cx="183" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26158" cx="317" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26158" cx="688" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26159" cx="890" cy="-511" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26157" cx="1083" cy="-899" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26157" cx="1359" cy="-899" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26157" cx="721" cy="-899" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26157" cx="973" cy="-899" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26157" cx="1206" cy="-899" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26157" cx="1421" cy="-899" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26160" cx="554" cy="-899" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26160" cx="380" cy="-899" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26157" cx="273" cy="-899" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26157" cx="-77" cy="-899" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26158" cx="763" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26158" cx="496" cy="-512" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="25" stroke="rgb(238,238,0)" stroke-width="1" width="13" x="967" y="-781"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="25" stroke="rgb(238,238,0)" stroke-width="1" width="13" x="-83" y="-780"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-252" y="-1239"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="119" stroke="rgb(0,255,0)" stroke-width="1" width="27" x="554" y="-127"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(0,255,0)" stroke-width="1" width="10" x="491" y="-53"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(0,255,0)" stroke-width="1" width="34" x="472" y="-56"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="1" width="6" x="566" y="-56"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="64" stroke="rgb(0,255,0)" stroke-width="1" width="28" x="509" y="-69"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimSun" font-size="20" graphid="g_394df00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -501.000000 -1231.500000) translate(0,16)">北城变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3979ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -1090.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3979ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -1090.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3979ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -1090.000000) translate(0,59)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3979ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -1090.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3979ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -1090.000000) translate(0,101)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3979ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -1090.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3979ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -1090.000000) translate(0,143)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3979ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -1090.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3979ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -1090.000000) translate(0,185)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3979ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -1090.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3979ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -1090.000000) translate(0,227)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3969dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -651.000000 -652.000000) translate(0,374)">联系方式：0878-6148340</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3827f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 535.000000 26.000000) translate(0,15)">10kV1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3827f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 535.000000 26.000000) translate(0,33)">消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3827f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 535.000000 26.000000) translate(0,51)">及接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3815d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1146.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39a18b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -946.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_399f640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.819337 -1047.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397a9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 52.000000 -1204.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3913700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -66.000000 -1073.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39bad60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 48.000000 -1264.000000) translate(0,15)">北碧线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3924930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 50.000000 -1116.000000) translate(0,12)">35360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3975e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 564.000000 -1151.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_398e2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 561.000000 -957.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_398be10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 562.544703 -1053.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3955320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 -1026.000000) translate(0,12)">35117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3934610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 -1211.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37aff10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 451.000000 -1081.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3951b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 564.000000 -1267.000000) translate(0,15)">中北Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_397b410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 579.000000 -1123.000000) translate(0,12)">35160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3874b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 488.000000 -891.000000) translate(0,15)">35kV临时母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3979d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1090.000000 -1144.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39895c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.881701 -1044.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3804ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1107.000000 -1200.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3963110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 984.000000 -1071.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3954c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1098.000000 -1256.000000) translate(0,15)">中北Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3985d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1370.000000 -1146.000000) translate(0,12)">3546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39a9bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -1204.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3934e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1258.000000 -1070.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39382d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 -1262.000000) translate(0,15)">北龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_398b940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -1115.000000) translate(0,12)">35460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39bbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1517.000000 -892.000000) translate(0,15)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_395f0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -822.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_395f790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 738.823529 -766.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39afac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.823529 -871.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39afd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 736.823529 -601.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39aff40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.823529 -554.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dbb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1216.823529 -627.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36dc7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1366.096525 -706.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_395d170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 934.096525 -701.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38ea1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -271.000000 -200.000000) translate(0,15)">中北联络线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38eb860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -229.903226 -399.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3904300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -94.000000 -465.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39047f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -95.000000 -321.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3904a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -124.000000 -198.000000) translate(0,15)">北金线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3905b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -95.473548 -395.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3981520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 47.000000 -470.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3981a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 49.000000 -320.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3981c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 20.000000 -199.000000) translate(0,15)">北赵线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a63090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 48.229677 -396.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_396e5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 195.000000 -460.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_396ea90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 193.000000 -315.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39b5fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 192.317419 -391.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3994140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 325.000000 -463.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3994630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 326.000000 -317.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3995460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 325.761290 -392.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39a5db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1464.000000 -463.000000) translate(0,12)">0732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39a62a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1467.000000 -327.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39a70d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1467.000000 -394.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a4f2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 -462.000000) translate(0,12)">0722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a4f7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.000000 -319.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a505d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1310.000000 -391.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a69c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -458.000000) translate(0,12)">0712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a6a2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -318.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a6b080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1067.000000 -385.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a6bdf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -322.000000 -491.000000) translate(0,15)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a6c140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1529.000000 -502.000000) translate(0,15)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a6f460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 913.096525 -299.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a779d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1127.000000 -266.000000) translate(0,15)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a87830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 636.000000 -263.000000) translate(0,15)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a88760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 695.000000 -466.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a88fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 774.000000 -470.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a89230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 898.000000 -467.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a89470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 162.000000 -198.000000) translate(0,15)">环北线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a89970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 299.000000 -199.000000) translate(0,15)">环东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a89e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1035.000000 -200.000000) translate(0,15)">北刨线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a8a3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1277.000000 -201.000000) translate(0,15)">中金线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a8a6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -200.000000) translate(0,15)">北鲁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a8aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1177.000000 -463.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a8aec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 47.000000 -1021.000000) translate(0,12)">35317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a8b100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1211.000000 -557.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a8b340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1373.203031 -1047.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a8b580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.823529 -794.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a8d2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -232.000000 -470.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a8d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -232.000000 -328.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa4de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 755.500000 -680.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa53a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 763.000000 -723.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa60a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1251.000000 -751.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa6840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1241.500000 -706.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3aa6fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 614.000000 -690.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3aa75e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1079.000000 -713.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aa7b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1102.000000 -1113.000000) translate(0,12)">35260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ab4120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 517.000000 -664.000000) translate(0,15)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ab4120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 517.000000 -664.000000) translate(0,33)">35±3*2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ab4120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 517.000000 -664.000000) translate(0,51)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ab4120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 517.000000 -664.000000) translate(0,69)">7.0%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ab6670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1024.000000 -682.000000) translate(0,15)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ab6670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1024.000000 -682.000000) translate(0,33)">35±3*1.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ab6670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1024.000000 -682.000000) translate(0,51)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ab6670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1024.000000 -682.000000) translate(0,69)">7.34%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ab6ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -644.000000 -810.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ab7a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 814.000000 -449.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3ab8190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -386.000000 -1213.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3ab9300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -386.000000 -1248.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ad38d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -115.903475 -700.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3af8580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -1073.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3af8bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 253.000000 -1264.000000) translate(0,15)">北店线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b00240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -185.000000 -892.000000) translate(0,12)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b02510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -1048.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b07cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 241.000000 -952.000000) translate(0,12)">3551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b08320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 253.000000 -1021.000000) translate(0,12)">35517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b08560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 251.000000 -1117.000000) translate(0,12)">35560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b087a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 241.000000 -1146.000000) translate(0,12)">3556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b089e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 253.000000 -1205.000000) translate(0,12)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b08c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 182.000000 -1204.000000) translate(0,12)">3553</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b186b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -818.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b18ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -842.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b18f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 386.000000 -817.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b19160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 280.000000 -843.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b193a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 222.000000 -817.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b195e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 281.000000 -891.000000) translate(0,12)">31210</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b19820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -22.000000 -1208.000000) translate(0,12)">3533</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b19a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -122.000000 -840.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b19ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -60.000000 -813.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b1dad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -60.000000 -889.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b1ee70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 928.000000 -851.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b1f2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -829.000000) translate(0,12)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b1f530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1213.000000 -868.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b1f770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1236.000000 -848.000000) translate(0,12)">30227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b1f9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1366.000000 -949.000000) translate(0,12)">3542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b1fbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1373.000000 -1018.000000) translate(0,12)">35427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b1fe30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1090.000000 -949.000000) translate(0,12)">3522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b20070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1098.000000 -1018.000000) translate(0,12)">35227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b23130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -694.000000 -258.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b24740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -547.000000 -268.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b24740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -547.000000 -268.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3b25100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -232.500000 -1227.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b38f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 506.000000 -388.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b395a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 -459.000000) translate(0,12)">0561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b397e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 -317.000000) translate(0,12)">0566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b39a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 482.000000 -153.000000) translate(0,12)">0030</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_39705c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 90.819337 -989.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3939a40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 91.819337 -1173.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_390a5f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 89.819337 -1085.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_399a1f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 615.544703 -995.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39995c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 616.544703 -1179.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38f7a40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 613.544703 -1091.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4cc4e70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1145.881701 -986.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38320f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1146.881701 -1170.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3938c90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1144.881701 -1082.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_398c2a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1417.203031 -986.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3929a90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.203031 -1170.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38f8d30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1416.203031 -1082.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_399b420" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 801.241796 -792.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3901f30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1284.650861 -816.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a58470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1049.226115 -797.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ad2be0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -16.773885 -784.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3aec9e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 294.819337 -989.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3af2980" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.819337 -1173.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3afba20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 293.819337 -1085.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b0cfd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 325.819337 -862.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b11310" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 430.819337 -788.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b16a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 195.000000 -788.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b1cde0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -16.773885 -860.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_BC"/>
</svg>