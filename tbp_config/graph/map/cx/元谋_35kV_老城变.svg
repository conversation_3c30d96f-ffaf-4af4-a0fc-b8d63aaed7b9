<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-222" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-739 -1269 2176 1098">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape20">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="37" x2="37" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="102" y2="115"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="116" y2="110"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="63" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="116" y2="110"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="105" y2="105"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="116" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="53" y2="62"/>
    <polyline arcFlag="1" points="13,73 12,73 12,73 11,73 10,73 10,72 9,72 8,71 8,71 8,70 7,69 7,69 7,68 7,67 7,66 7,66 8,65 8,64 8,64 9,63 10,63 10,62 11,62 12,62 12,62 13,62 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,84 12,84 12,84 11,84 10,84 10,83 9,83 8,82 8,82 8,81 7,80 7,80 7,79 7,78 7,77 7,77 8,76 8,75 8,75 9,74 10,74 10,73 11,73 12,73 12,73 13,73 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,95 12,95 12,95 11,95 10,95 10,94 9,94 8,93 8,93 8,92 7,91 7,91 7,90 7,89 7,88 7,88 8,87 8,86 8,86 9,85 10,85 10,84 11,84 12,84 12,84 13,84 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="95" y2="104"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="101" y2="101"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="93" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.774005" x1="37" x2="37" y1="26" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="24" x2="36" y1="26" y2="26"/>
    <polyline arcFlag="1" points="37,13 39,13 41,14 42,14 44,15 45,16 47,17 48,19 49,21 49,22 50,24 50,26 50,28 49,30 49,31 48,33 47,34 45,36 44,37 42,38 41,38 39,39 37,39 35,39 33,38 32,38 30,37 29,36 27,34 26,33 25,31 25,30 24,28 24,26 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape188">
    <polyline DF8003:Layer="PUBLIC" points="19,18 10,0 1,19 19,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape18">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,5 5,52 " stroke-width="4.5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape53_0">
    <circle cx="13" cy="45" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="41" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="45" x2="29" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="39" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="37" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="25" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="12,44 8,53 18,53 12,44 "/>
   </symbol>
   <symbol id="transformer2:shape53_1">
    <ellipse cx="13" cy="26" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="21" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="25" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="29" y2="25"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape112">
    <circle cx="32" cy="16" fillStyle="0" r="8" stroke-width="0.570276"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="5" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="9" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="32" x2="35" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="29" x2="32" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="32" x2="32" y1="16" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="20" x2="23" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="17" x2="20" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="20" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="17" x2="20" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="20" x2="23" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="20" x2="20" y1="23" y2="20"/>
    <circle cx="8" cy="16" fillStyle="0" r="7.5" stroke-width="0.536731"/>
    <circle cx="20" cy="9" fillStyle="0" r="8" stroke-width="0.570276"/>
    <circle cx="20" cy="23" fillStyle="0" r="8" stroke-width="0.570276"/>
   </symbol>
   <symbol id="voltageTransformer:shape2">
    <circle cx="7" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape50">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
   </symbol>
   <symbol id="voltageTransformer:shape34">
    <polyline points="41,18 8,18 8,43 " stroke-width="1.16071"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="39" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="39" y2="36"/>
    <ellipse cx="42" cy="19" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <ellipse cx="40" cy="37" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <ellipse cx="54" cy="29" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <ellipse cx="28" cy="30" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="50" x2="54" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="55" x2="55" y1="26" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="59" x2="55" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21447" x1="30" x2="34" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21568" x1="30" x2="25" y1="32" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.17171" x1="25" x2="34" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="36" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.618687" x1="6" x2="10" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.789474" x1="0" x2="15" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.789474" x1="5" x2="11" y1="47" y2="47"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1afc150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ae14e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a20bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1afe060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1413c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d64b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_13f6ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_10ea2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1bcec60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1bcec60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c85a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c85a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cc7430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cc7430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1c25720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cc59c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_13d10a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_17d5610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c2b810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e9860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_10f4880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d75e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c459a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_177bb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19596d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_13d6e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19e36f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c1c220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_178b640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1b4b060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1af0400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c07230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_17e87b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b55b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1be8a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1108" width="2186" x="-744" y="-1274"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-236" y="-1239"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-166765">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 39.000000 -935.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27027" ObjectName="SW-YM_LC.YM_LC_3901SW"/>
     <cge:Meas_Ref ObjectId="166765"/>
    <cge:TPSR_Ref TObjectID="27027"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166766">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 82.819337 -1001.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27028" ObjectName="SW-YM_LC.YM_LC_39017SW"/>
     <cge:Meas_Ref ObjectId="166766"/>
    <cge:TPSR_Ref TObjectID="27028"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166726">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 227.000000 -929.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27020" ObjectName="SW-YM_LC.YM_LC_3511SW"/>
     <cge:Meas_Ref ObjectId="166726"/>
    <cge:TPSR_Ref TObjectID="27020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166727">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 227.000000 -1037.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27021" ObjectName="SW-YM_LC.YM_LC_3516SW"/>
     <cge:Meas_Ref ObjectId="166727"/>
    <cge:TPSR_Ref TObjectID="27021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166728">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 263.819337 -1087.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27022" ObjectName="SW-YM_LC.YM_LC_35167SW"/>
     <cge:Meas_Ref ObjectId="166728"/>
    <cge:TPSR_Ref TObjectID="27022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166745">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 557.000000 -930.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27024" ObjectName="SW-YM_LC.YM_LC_3521SW"/>
     <cge:Meas_Ref ObjectId="166745"/>
    <cge:TPSR_Ref TObjectID="27024"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166746">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 557.000000 -1038.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27025" ObjectName="SW-YM_LC.YM_LC_3526SW"/>
     <cge:Meas_Ref ObjectId="166746"/>
    <cge:TPSR_Ref TObjectID="27025"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166747">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 593.819337 -1088.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27026" ObjectName="SW-YM_LC.YM_LC_35267SW"/>
     <cge:Meas_Ref ObjectId="166747"/>
    <cge:TPSR_Ref TObjectID="27026"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166778">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 81.000000 -844.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27032" ObjectName="SW-YM_LC.YM_LC_3011SW"/>
     <cge:Meas_Ref ObjectId="166778"/>
    <cge:TPSR_Ref TObjectID="27032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166784">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 81.000000 -573.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27034" ObjectName="SW-YM_LC.YM_LC_0011SW"/>
     <cge:Meas_Ref ObjectId="166784"/>
    <cge:TPSR_Ref TObjectID="27034"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.819337 -842.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166807">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 792.000000 -843.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27036" ObjectName="SW-YM_LC.YM_LC_3021SW"/>
     <cge:Meas_Ref ObjectId="166807"/>
    <cge:TPSR_Ref TObjectID="27036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166813">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 792.000000 -572.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27038" ObjectName="SW-YM_LC.YM_LC_0021SW"/>
     <cge:Meas_Ref ObjectId="166813"/>
    <cge:TPSR_Ref TObjectID="27038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166836">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -215.903226 -494.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27040" ObjectName="SW-YM_LC.YM_LC_0511SW"/>
     <cge:Meas_Ref ObjectId="166836"/>
    <cge:TPSR_Ref TObjectID="27040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166837">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -215.903226 -385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27041" ObjectName="SW-YM_LC.YM_LC_0516SW"/>
     <cge:Meas_Ref ObjectId="166837"/>
    <cge:TPSR_Ref TObjectID="27041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166852">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -49.903226 -492.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27043" ObjectName="SW-YM_LC.YM_LC_0521SW"/>
     <cge:Meas_Ref ObjectId="166852"/>
    <cge:TPSR_Ref TObjectID="27043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166853">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -49.903226 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27044" ObjectName="SW-YM_LC.YM_LC_0526SW"/>
     <cge:Meas_Ref ObjectId="166853"/>
    <cge:TPSR_Ref TObjectID="27044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166868">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 104.096774 -493.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27046" ObjectName="SW-YM_LC.YM_LC_0531SW"/>
     <cge:Meas_Ref ObjectId="166868"/>
    <cge:TPSR_Ref TObjectID="27046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166869">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 104.096774 -384.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27047" ObjectName="SW-YM_LC.YM_LC_0536SW"/>
     <cge:Meas_Ref ObjectId="166869"/>
    <cge:TPSR_Ref TObjectID="27047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166884">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 265.096774 -490.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27049" ObjectName="SW-YM_LC.YM_LC_0541SW"/>
     <cge:Meas_Ref ObjectId="166884"/>
    <cge:TPSR_Ref TObjectID="27049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166885">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 265.096774 -381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27050" ObjectName="SW-YM_LC.YM_LC_0546SW"/>
     <cge:Meas_Ref ObjectId="166885"/>
    <cge:TPSR_Ref TObjectID="27050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166998">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 466.226115 -478.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27072" ObjectName="SW-YM_LC.YM_LC_0121SW"/>
     <cge:Meas_Ref ObjectId="166998"/>
    <cge:TPSR_Ref TObjectID="27072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166999">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.226115 -477.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27073" ObjectName="SW-YM_LC.YM_LC_0122SW"/>
     <cge:Meas_Ref ObjectId="166999"/>
    <cge:TPSR_Ref TObjectID="27073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166900">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 712.096774 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27052" ObjectName="SW-YM_LC.YM_LC_0551SW"/>
     <cge:Meas_Ref ObjectId="166900"/>
    <cge:TPSR_Ref TObjectID="27052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166901">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 712.096774 -377.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27053" ObjectName="SW-YM_LC.YM_LC_0556SW"/>
     <cge:Meas_Ref ObjectId="166901"/>
    <cge:TPSR_Ref TObjectID="27053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 916.096774 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27055" ObjectName="SW-YM_LC.YM_LC_0561SW"/>
     <cge:Meas_Ref ObjectId="166916"/>
    <cge:TPSR_Ref TObjectID="27055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166917">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 916.096774 -377.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27056" ObjectName="SW-YM_LC.YM_LC_0566SW"/>
     <cge:Meas_Ref ObjectId="166917"/>
    <cge:TPSR_Ref TObjectID="27056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166932">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1054.096774 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27058" ObjectName="SW-YM_LC.YM_LC_0571SW"/>
     <cge:Meas_Ref ObjectId="166932"/>
    <cge:TPSR_Ref TObjectID="27058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166933">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1054.096774 -377.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27059" ObjectName="SW-YM_LC.YM_LC_0576SW"/>
     <cge:Meas_Ref ObjectId="166933"/>
    <cge:TPSR_Ref TObjectID="27059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166948">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1184.096774 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27061" ObjectName="SW-YM_LC.YM_LC_0581SW"/>
     <cge:Meas_Ref ObjectId="166948"/>
    <cge:TPSR_Ref TObjectID="27061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1184.096774 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27062" ObjectName="SW-YM_LC.YM_LC_0586SW"/>
     <cge:Meas_Ref ObjectId="166949"/>
    <cge:TPSR_Ref TObjectID="27062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166964">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -161.000000 -577.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27064" ObjectName="SW-YM_LC.YM_LC_0591SW"/>
     <cge:Meas_Ref ObjectId="166964"/>
    <cge:TPSR_Ref TObjectID="27064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166965">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -161.000000 -748.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27065" ObjectName="SW-YM_LC.YM_LC_0596SW"/>
     <cge:Meas_Ref ObjectId="166965"/>
    <cge:TPSR_Ref TObjectID="27065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166966">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -125.180663 -796.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27066" ObjectName="SW-YM_LC.YM_LC_05967SW"/>
     <cge:Meas_Ref ObjectId="166966"/>
    <cge:TPSR_Ref TObjectID="27066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166981">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1079.000000 -574.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27068" ObjectName="SW-YM_LC.YM_LC_0601SW"/>
     <cge:Meas_Ref ObjectId="166981"/>
    <cge:TPSR_Ref TObjectID="27068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166982">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1079.000000 -745.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27069" ObjectName="SW-YM_LC.YM_LC_0606SW"/>
     <cge:Meas_Ref ObjectId="166982"/>
    <cge:TPSR_Ref TObjectID="27069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166983">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1114.819337 -793.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27070" ObjectName="SW-YM_LC.YM_LC_06067SW"/>
     <cge:Meas_Ref ObjectId="166983"/>
    <cge:TPSR_Ref TObjectID="27070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.000000 -568.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27029" ObjectName="SW-YM_LC.YM_LC_0901SW"/>
     <cge:Meas_Ref ObjectId="166771"/>
    <cge:TPSR_Ref TObjectID="27029"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166775">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 658.000000 -571.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27030" ObjectName="SW-YM_LC.YM_LC_0902SW"/>
     <cge:Meas_Ref ObjectId="166775"/>
    <cge:TPSR_Ref TObjectID="27030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1307.819337 -485.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280929">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 840.000000 -928.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44522" ObjectName="SW-YM_LC.YM_LC_3531SW"/>
     <cge:Meas_Ref ObjectId="280929"/>
    <cge:TPSR_Ref TObjectID="44522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280935">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 840.000000 -1036.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44523" ObjectName="SW-YM_LC.YM_LC_3536SW"/>
     <cge:Meas_Ref ObjectId="280935"/>
    <cge:TPSR_Ref TObjectID="44523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280941">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 876.819337 -1086.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44524" ObjectName="SW-YM_LC.YM_LC_35367SW"/>
     <cge:Meas_Ref ObjectId="280941"/>
    <cge:TPSR_Ref TObjectID="44524"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YM_LC.YM_LC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-123,-908 1147,-908 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27015" ObjectName="BS-YM_LC.YM_LC_3IM"/>
    <cge:TPSR_Ref TObjectID="27015"/></metadata>
   <polyline fill="none" opacity="0" points="-123,-908 1147,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_LC.YM_LC_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-318,-553 501,-553 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27016" ObjectName="BS-YM_LC.YM_LC_9IM"/>
    <cge:TPSR_Ref TObjectID="27016"/></metadata>
   <polyline fill="none" opacity="0" points="-318,-553 501,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_LC.YM_LC_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="577,-552 1368,-552 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27017" ObjectName="BS-YM_LC.YM_LC_9IIM"/>
    <cge:TPSR_Ref TObjectID="27017"/></metadata>
   <polyline fill="none" opacity="0" points="577,-552 1368,-552 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YM_LC.YM_LC_1C">
    <use class="BV-10KV" transform="matrix(0.661017 0.000000 0.000000 -0.611570 -177.000000 -819.000000)" xlink:href="#capacitor:shape20"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42248" ObjectName="CB-YM_LC.YM_LC_1C"/>
    <cge:TPSR_Ref TObjectID="42248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-YM_LC.YM_LC_2C">
    <use class="BV-10KV" transform="matrix(0.661017 0.000000 0.000000 -0.611570 1064.000000 -808.000000)" xlink:href="#capacitor:shape20"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42249" ObjectName="CB-YM_LC.YM_LC_2C"/>
    <cge:TPSR_Ref TObjectID="42249"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YM_LC.YM_LC_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="38214"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 504.000000 -771.000000)" xlink:href="#transformer2:shape53_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 504.000000 -771.000000)" xlink:href="#transformer2:shape53_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="27076" ObjectName="TF-YM_LC.YM_LC_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YM_LC.YM_LC_Zyb2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="38218"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1300.000000 -414.000000)" xlink:href="#transformer2:shape53_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1300.000000 -414.000000)" xlink:href="#transformer2:shape53_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="27077" ObjectName="TF-YM_LC.YM_LC_Zyb2"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YM_LC.YM_LC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="38206"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 52.000000 -689.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 52.000000 -689.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="27074" ObjectName="TF-YM_LC.YM_LC_1T"/>
    <cge:TPSR_Ref TObjectID="27074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YM_LC.YM_LC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="38210"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 763.000000 -685.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 763.000000 -685.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="27075" ObjectName="TF-YM_LC.YM_LC_2T"/>
    <cge:TPSR_Ref TObjectID="27075"/></metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-0KV" transform="matrix(0.612245 -0.000000 0.000000 -0.629630 830.000000 -308.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-10KV" transform="matrix(0.612245 -0.000000 0.000000 -0.629630 210.000000 -286.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_111b000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-1063 48,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_11bda60@1" ObjectIDZND0="g_1138060@0" Pin0InfoVect0LinkObjId="g_1138060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11bda60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-1063 48,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_123bda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-1127 48,-1145 84,-1145 84,-1127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_eb3bd0@0" Pin0InfoVect0LinkObjId="g_eb3bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-1127 48,-1145 84,-1145 84,-1127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1126980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="49,-1007 49,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="27028@x" ObjectIDND1="27027@x" ObjectIDND2="g_11e7560@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166766_0" Pin1InfoVect1LinkObjId="SW-166765_0" Pin1InfoVect2LinkObjId="g_11e7560_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="49,-1007 49,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1119440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="124,-1006 144,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27028@0" ObjectIDZND0="g_11f7960@0" Pin0InfoVect0LinkObjId="g_11f7960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166766_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="124,-1006 144,-1006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_119d4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="236,-908 236,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27015@0" ObjectIDZND0="27020@0" Pin0InfoVect0LinkObjId="SW-166726_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_115f5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="236,-908 236,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12341d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="236,-970 236,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27020@1" ObjectIDZND0="27019@0" Pin0InfoVect0LinkObjId="SW-166724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166726_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="236,-970 236,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11bcd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="236,-1019 236,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27019@1" ObjectIDZND0="27021@0" Pin0InfoVect0LinkObjId="SW-166727_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="236,-1019 236,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11c9660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="305,-1092 325,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27022@0" ObjectIDZND0="g_11c8ef0@0" Pin0InfoVect0LinkObjId="g_11c8ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="305,-1092 325,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11c9850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="269,-1092 237,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="27022@1" ObjectIDZND0="27021@x" ObjectIDZND1="g_11bcf30@0" ObjectIDZND2="g_11bd700@0" Pin0InfoVect0LinkObjId="SW-166727_0" Pin0InfoVect1LinkObjId="g_11bcf30_0" Pin0InfoVect2LinkObjId="g_11bd700_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166728_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="269,-1092 237,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11c9a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="236,-1092 236,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="27022@x" ObjectIDND1="g_11bcf30@0" ObjectIDND2="g_11bd700@0" ObjectIDZND0="27021@1" Pin0InfoVect0LinkObjId="SW-166727_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166728_0" Pin1InfoVect1LinkObjId="g_11bcf30_0" Pin1InfoVect2LinkObjId="g_11bd700_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="236,-1092 236,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1170f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-971 566,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27024@1" ObjectIDZND0="27023@0" Pin0InfoVect0LinkObjId="SW-166743_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166745_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="566,-971 566,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11b5900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-1020 566,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27023@1" ObjectIDZND0="27025@0" Pin0InfoVect0LinkObjId="SW-166746_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166743_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="566,-1020 566,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1240650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-1092 634,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1149b70@0" ObjectIDZND0="27026@0" Pin0InfoVect0LinkObjId="SW-166747_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1149b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-1092 634,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10ab7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="90,-849 90,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27032@0" ObjectIDZND0="27031@1" Pin0InfoVect0LinkObjId="SW-166776_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166778_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="90,-849 90,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10ab990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="90,-797 90,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="27031@0" ObjectIDZND0="27074@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166776_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="90,-797 90,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1203070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="90,-694 90,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="27074@1" ObjectIDZND0="27033@1" Pin0InfoVect0LinkObjId="SW-166782_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10ab990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="90,-694 90,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11eb7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="90,-637 90,-614 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27033@0" ObjectIDZND0="27034@1" Pin0InfoVect0LinkObjId="SW-166784_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166782_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="90,-637 90,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_11eb990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="333,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="333,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_114dda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="517,-729 517,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_114d140@0" ObjectIDZND0="g_114d960@0" Pin0InfoVect0LinkObjId="g_114d960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_114d140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="517,-729 517,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11df720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-848 801,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27036@0" ObjectIDZND0="27035@1" Pin0InfoVect0LinkObjId="SW-166805_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166807_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-848 801,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11df940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-796 801,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="27035@0" ObjectIDZND0="27075@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166805_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-796 801,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1251390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-690 801,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="27075@1" ObjectIDZND0="27037@1" Pin0InfoVect0LinkObjId="SW-166811_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11df940_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-690 801,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11068d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-636 801,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27037@0" ObjectIDZND0="27038@1" Pin0InfoVect0LinkObjId="SW-166813_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166811_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-636 801,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f7f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-207,-499 -207,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27040@0" ObjectIDZND0="27039@1" Pin0InfoVect0LinkObjId="SW-166834_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166836_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-207,-499 -207,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f8120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-207,-448 -207,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27039@0" ObjectIDZND0="27041@1" Pin0InfoVect0LinkObjId="SW-166837_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166834_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-207,-448 -207,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f8eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-207,-355 -170,-355 -170,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="27041@x" ObjectIDND1="34188@x" ObjectIDZND0="g_11f8340@0" Pin0InfoVect0LinkObjId="g_11f8340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166837_0" Pin1InfoVect1LinkObjId="EC-YM_LC.051Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-207,-355 -170,-355 -170,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f90d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-207,-390 -207,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27041@0" ObjectIDZND0="g_11f8340@0" ObjectIDZND1="34188@x" Pin0InfoVect0LinkObjId="g_11f8340_0" Pin0InfoVect1LinkObjId="EC-YM_LC.051Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166837_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-207,-390 -207,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f92f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-207,-355 -207,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27041@x" ObjectIDND1="g_11f8340@0" ObjectIDZND0="34188@0" Pin0InfoVect0LinkObjId="EC-YM_LC.051Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166837_0" Pin1InfoVect1LinkObjId="g_11f8340_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-207,-355 -207,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121c820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-41,-497 -41,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27043@0" ObjectIDZND0="27042@1" Pin0InfoVect0LinkObjId="SW-166850_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166852_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-41,-497 -41,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121ca80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-41,-446 -41,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27042@0" ObjectIDZND0="27044@1" Pin0InfoVect0LinkObjId="SW-166853_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-41,-446 -41,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121d910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-41,-353 -4,-353 -4,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="27044@x" ObjectIDND1="34189@x" ObjectIDZND0="g_121cce0@0" Pin0InfoVect0LinkObjId="g_121cce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166853_0" Pin1InfoVect1LinkObjId="EC-YM_LC.052Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-41,-353 -4,-353 -4,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121db70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-41,-388 -41,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27044@0" ObjectIDZND0="g_121cce0@0" ObjectIDZND1="34189@x" Pin0InfoVect0LinkObjId="g_121cce0_0" Pin0InfoVect1LinkObjId="EC-YM_LC.052Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166853_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-41,-388 -41,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121ddd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-41,-353 -41,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27044@x" ObjectIDND1="g_121cce0@0" ObjectIDZND0="34189@0" Pin0InfoVect0LinkObjId="EC-YM_LC.052Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166853_0" Pin1InfoVect1LinkObjId="g_121cce0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-41,-353 -41,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1199fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-498 113,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27046@0" ObjectIDZND0="27045@1" Pin0InfoVect0LinkObjId="SW-166866_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166868_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="113,-498 113,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_119a200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-447 113,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27045@0" ObjectIDZND0="27047@1" Pin0InfoVect0LinkObjId="SW-166869_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166866_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="113,-447 113,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10d8b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-354 150,-354 150,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="27047@x" ObjectIDND1="34190@x" ObjectIDZND0="g_10d7f30@0" Pin0InfoVect0LinkObjId="g_10d7f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166869_0" Pin1InfoVect1LinkObjId="EC-YM_LC.053Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="113,-354 150,-354 150,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10d8de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-389 113,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27047@0" ObjectIDZND0="g_10d7f30@0" ObjectIDZND1="34190@x" Pin0InfoVect0LinkObjId="g_10d7f30_0" Pin0InfoVect1LinkObjId="EC-YM_LC.053Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166869_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="113,-389 113,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10d9040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-354 113,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27047@x" ObjectIDND1="g_10d7f30@0" ObjectIDZND0="34190@0" Pin0InfoVect0LinkObjId="EC-YM_LC.053Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166869_0" Pin1InfoVect1LinkObjId="g_10d7f30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="113,-354 113,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10e4220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="274,-495 274,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27049@0" ObjectIDZND0="27048@1" Pin0InfoVect0LinkObjId="SW-166882_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166884_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="274,-495 274,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10e4480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="274,-444 274,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27048@0" ObjectIDZND0="27050@1" Pin0InfoVect0LinkObjId="SW-166885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166882_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="274,-444 274,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11abb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="274,-351 311,-351 311,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="hydroGenerator" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="27050@x" ObjectIDND1="0@x" ObjectIDND2="34191@x" ObjectIDZND0="g_10e46e0@0" Pin0InfoVect0LinkObjId="g_10e46e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166885_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="EC-YM_LC.054Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="274,-351 311,-351 311,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11abdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="274,-386 274,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="hydroGenerator" EndDevType2="load" ObjectIDND0="27050@0" ObjectIDZND0="g_10e46e0@0" ObjectIDZND1="0@x" ObjectIDZND2="34191@x" Pin0InfoVect0LinkObjId="g_10e46e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="EC-YM_LC.054Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="274,-386 274,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1128690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-468 475,-468 475,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27071@1" ObjectIDZND0="27072@0" Pin0InfoVect0LinkObjId="SW-166998_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,-468 475,-468 475,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11288f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="551,-468 601,-468 601,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27071@0" ObjectIDZND0="27073@0" Pin0InfoVect0LinkObjId="SW-166999_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166996_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="551,-468 601,-468 601,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_119e170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-491 721,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27052@0" ObjectIDZND0="27051@1" Pin0InfoVect0LinkObjId="SW-166898_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-491 721,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_119e3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-440 721,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27051@0" ObjectIDZND0="27053@1" Pin0InfoVect0LinkObjId="SW-166901_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166898_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-440 721,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_119f2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-347 758,-347 758,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="27053@x" ObjectIDND1="34192@x" ObjectIDZND0="g_119e630@0" Pin0InfoVect0LinkObjId="g_119e630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166901_0" Pin1InfoVect1LinkObjId="EC-YM_LC.055Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-347 758,-347 758,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_119f540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-382 721,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27053@0" ObjectIDZND0="g_119e630@0" ObjectIDZND1="34192@x" Pin0InfoVect0LinkObjId="g_119e630_0" Pin0InfoVect1LinkObjId="EC-YM_LC.055Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166901_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="721,-382 721,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_119f7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-347 721,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27053@x" ObjectIDND1="g_119e630@0" ObjectIDZND0="34192@0" Pin0InfoVect0LinkObjId="EC-YM_LC.055Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166901_0" Pin1InfoVect1LinkObjId="g_119e630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-347 721,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11a3230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-491 925,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27055@0" ObjectIDZND0="27054@1" Pin0InfoVect0LinkObjId="SW-166914_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="925,-491 925,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11a3490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-440 925,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27054@0" ObjectIDZND0="27056@1" Pin0InfoVect0LinkObjId="SW-166917_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166914_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="925,-440 925,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11a43a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-339 964,-339 964,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="34193@x" ObjectIDND1="g_11595f0@0" ObjectIDND2="27056@x" ObjectIDZND0="g_11a36f0@0" Pin0InfoVect0LinkObjId="g_11a36f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-YM_LC.056Ld_0" Pin1InfoVect1LinkObjId="g_11595f0_0" Pin1InfoVect2LinkObjId="SW-166917_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="926,-339 964,-339 964,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11b2cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1063,-491 1063,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27058@0" ObjectIDZND0="27057@1" Pin0InfoVect0LinkObjId="SW-166930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1063,-491 1063,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11b2f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1063,-440 1063,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27057@0" ObjectIDZND0="27059@1" Pin0InfoVect0LinkObjId="SW-166933_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1063,-440 1063,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1185340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1063,-347 1100,-347 1100,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="27059@x" ObjectIDND1="34194@x" ObjectIDZND0="g_11b3170@0" Pin0InfoVect0LinkObjId="g_11b3170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166933_0" Pin1InfoVect1LinkObjId="EC-YM_LC.057Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1063,-347 1100,-347 1100,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1185580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1063,-382 1063,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27059@0" ObjectIDZND0="g_11b3170@0" ObjectIDZND1="34194@x" Pin0InfoVect0LinkObjId="g_11b3170_0" Pin0InfoVect1LinkObjId="EC-YM_LC.057Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166933_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1063,-382 1063,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11857e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1063,-347 1063,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27059@x" ObjectIDND1="g_11b3170@0" ObjectIDZND0="34194@0" Pin0InfoVect0LinkObjId="EC-YM_LC.057Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166933_0" Pin1InfoVect1LinkObjId="g_11b3170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1063,-347 1063,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1156e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-488 1193,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27061@0" ObjectIDZND0="27060@1" Pin0InfoVect0LinkObjId="SW-166946_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166948_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-488 1193,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1157080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-437 1193,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27060@0" ObjectIDZND0="27062@1" Pin0InfoVect0LinkObjId="SW-166949_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166946_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-437 1193,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1157f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-344 1230,-344 1230,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="27062@x" ObjectIDND1="34195@x" ObjectIDZND0="g_11572e0@0" Pin0InfoVect0LinkObjId="g_11572e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166949_0" Pin1InfoVect1LinkObjId="EC-YM_LC.058Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-344 1230,-344 1230,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11581f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-379 1193,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="27062@0" ObjectIDZND0="g_11572e0@0" ObjectIDZND1="34195@x" Pin0InfoVect0LinkObjId="g_11572e0_0" Pin0InfoVect1LinkObjId="EC-YM_LC.058Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166949_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-379 1193,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1158450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-344 1193,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="27062@x" ObjectIDND1="g_11572e0@0" ObjectIDZND0="34195@0" Pin0InfoVect0LinkObjId="EC-YM_LC.058Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166949_0" Pin1InfoVect1LinkObjId="g_11572e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-344 1193,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_118d8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="891,-330 891,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_11595f0@0" ObjectIDZND0="g_115a080@0" Pin0InfoVect0LinkObjId="g_115a080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11595f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="891,-330 891,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_118ea70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="224,-316 224,-331 273,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="0@0" ObjectIDZND0="27050@x" ObjectIDZND1="g_10e46e0@0" ObjectIDZND2="34191@x" Pin0InfoVect0LinkObjId="SW-166885_0" Pin0InfoVect1LinkObjId="g_10e46e0_0" Pin0InfoVect2LinkObjId="EC-YM_LC.054Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="224,-316 224,-331 273,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_118ecd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="274,-351 274,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="hydroGenerator" EndDevType1="load" ObjectIDND0="27050@x" ObjectIDND1="g_10e46e0@0" ObjectIDZND0="0@x" ObjectIDZND1="34191@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="EC-YM_LC.054Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166885_0" Pin1InfoVect1LinkObjId="g_10e46e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="274,-351 274,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_118ef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="274,-331 274,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="hydroGenerator" EndDevType0="load" ObjectIDND0="27050@x" ObjectIDND1="g_10e46e0@0" ObjectIDND2="0@x" ObjectIDZND0="34191@0" Pin0InfoVect0LinkObjId="EC-YM_LC.054Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166885_0" Pin1InfoVect1LinkObjId="g_10e46e0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="274,-331 274,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_118f190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="891,-362 925,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="g_11595f0@1" ObjectIDZND0="27056@x" ObjectIDZND1="34193@x" ObjectIDZND2="g_11a36f0@0" Pin0InfoVect0LinkObjId="SW-166917_0" Pin0InfoVect1LinkObjId="EC-YM_LC.056Ld_0" Pin0InfoVect2LinkObjId="g_11a36f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11595f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="891,-362 925,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_118f3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="888,-362 843,-362 843,-337 844,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="hydroGenerator" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="888,-362 843,-362 843,-337 844,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_118f640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-295 925,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="34193@0" ObjectIDZND0="g_11595f0@0" ObjectIDZND1="27056@x" ObjectIDZND2="g_11a36f0@0" Pin0InfoVect0LinkObjId="g_11595f0_0" Pin0InfoVect1LinkObjId="SW-166917_0" Pin0InfoVect2LinkObjId="g_11a36f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YM_LC.056Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="925,-295 925,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11c0e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-152,-618 -152,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27064@1" ObjectIDZND0="27063@0" Pin0InfoVect0LinkObjId="SW-166962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166964_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-152,-618 -152,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11c19f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-152,-665 -152,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="27063@1" ObjectIDZND0="g_11c10f0@0" Pin0InfoVect0LinkObjId="g_11c10f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-152,-665 -152,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e3dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-152,-736 -152,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_11c10f0@1" ObjectIDZND0="27065@0" Pin0InfoVect0LinkObjId="SW-166965_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11c10f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-152,-736 -152,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11fda00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-84,-801 -64,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27066@0" ObjectIDZND0="g_122b780@0" Pin0InfoVect0LinkObjId="g_122b780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166966_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-84,-801 -64,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11b6ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-615 1088,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27068@1" ObjectIDZND0="27067@0" Pin0InfoVect0LinkObjId="SW-166979_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166981_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-615 1088,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11b7b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-662 1088,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="27067@1" ObjectIDZND0="g_11b7130@0" Pin0InfoVect0LinkObjId="g_11b7130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166979_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-662 1088,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11ba5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-733 1088,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_11b7130@1" ObjectIDZND0="27069@0" Pin0InfoVect0LinkObjId="SW-166982_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b7130_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-733 1088,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1226ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1156,-798 1176,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27070@0" ObjectIDZND0="g_1226430@0" Pin0InfoVect0LinkObjId="g_1226430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166983_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1156,-798 1176,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11204f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="341,-614 341,-630 375,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_111f7c0@0" ObjectIDZND0="27029@x" ObjectIDZND1="g_10cf0c0@0" Pin0InfoVect0LinkObjId="SW-166771_0" Pin0InfoVect1LinkObjId="g_10cf0c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_111f7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="341,-614 341,-630 375,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1120750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="564,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="564,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11209b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,-609 375,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="27029@1" ObjectIDZND0="g_111f7c0@0" ObjectIDZND1="g_10cf0c0@0" Pin0InfoVect0LinkObjId="g_111f7c0_0" Pin0InfoVect1LinkObjId="g_10cf0c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166771_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="375,-609 375,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1120c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="625,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1062d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="633,-617 633,-633 668,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1062040@0" ObjectIDZND0="27030@x" ObjectIDZND1="g_10cf7e0@0" Pin0InfoVect0LinkObjId="SW-166775_0" Pin0InfoVect1LinkObjId="g_10cf7e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1062040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="633,-617 633,-633 668,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1062fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="667,-612 667,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="27030@1" ObjectIDZND0="g_1062040@0" ObjectIDZND1="g_10cf7e0@0" Pin0InfoVect0LinkObjId="g_1062040_0" Pin0InfoVect1LinkObjId="g_10cf7e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166775_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="667,-612 667,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c7c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-527 721,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27052@1" ObjectIDZND0="27017@0" Pin0InfoVect0LinkObjId="g_10c7e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-527 721,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c7e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1193,-524 1193,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27061@1" ObjectIDZND0="27017@0" Pin0InfoVect0LinkObjId="g_10c7c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166948_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1193,-524 1193,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c8060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1063,-527 1063,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27058@1" ObjectIDZND0="27017@0" Pin0InfoVect0LinkObjId="g_10c7c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166932_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1063,-527 1063,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c8250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-527 925,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27055@1" ObjectIDZND0="27017@0" Pin0InfoVect0LinkObjId="g_10c7c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="925,-527 925,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c8480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="667,-576 667,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27030@0" ObjectIDZND0="27017@0" Pin0InfoVect0LinkObjId="g_10c7c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166775_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="667,-576 667,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c86b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="601,-518 601,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27073@1" ObjectIDZND0="27017@0" Pin0InfoVect0LinkObjId="g_10c7c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166999_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="601,-518 601,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c88e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="475,-519 475,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27072@1" ObjectIDZND0="27016@0" Pin0InfoVect0LinkObjId="g_10c8b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="475,-519 475,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c8b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,-573 375,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27029@0" ObjectIDZND0="27016@0" Pin0InfoVect0LinkObjId="g_10c88e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166771_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,-573 375,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c8d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="274,-531 274,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27049@1" ObjectIDZND0="27016@0" Pin0InfoVect0LinkObjId="g_10c88e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166884_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="274,-531 274,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c8f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-534 113,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27046@1" ObjectIDZND0="27016@0" Pin0InfoVect0LinkObjId="g_10c88e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166868_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="113,-534 113,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c91d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-41,-533 -41,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27043@1" ObjectIDZND0="27016@0" Pin0InfoVect0LinkObjId="g_10c88e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166852_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-41,-533 -41,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10c9430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-207,-535 -207,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27040@1" ObjectIDZND0="27016@0" Pin0InfoVect0LinkObjId="g_10c88e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166836_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-207,-535 -207,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_115f5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-884 801,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27036@1" ObjectIDZND0="27015@0" Pin0InfoVect0LinkObjId="g_1160ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166807_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-884 801,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_115f7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-577 801,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27038@0" ObjectIDZND0="27017@0" Pin0InfoVect0LinkObjId="g_10c7c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166813_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-577 801,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_115f9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-579 1088,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27068@0" ObjectIDZND0="27017@0" Pin0InfoVect0LinkObjId="g_10c7c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166981_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-579 1088,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1160ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="90,-885 90,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27032@1" ObjectIDZND0="27015@0" Pin0InfoVect0LinkObjId="g_115f5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166778_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="90,-885 90,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11610c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="90,-578 90,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27034@0" ObjectIDZND0="27016@0" Pin0InfoVect0LinkObjId="g_10c88e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166784_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="90,-578 90,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11612b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-152,-582 -152,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27064@0" ObjectIDZND0="27016@0" Pin0InfoVect0LinkObjId="g_10c88e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166964_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-152,-582 -152,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11614a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-940 48,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27027@0" ObjectIDZND0="27015@0" Pin0InfoVect0LinkObjId="g_115f5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="48,-940 48,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11616d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-935 566,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27024@0" ObjectIDZND0="27015@0" Pin0InfoVect0LinkObjId="g_115f5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166745_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="566,-935 566,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_11637f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="517,-847 517,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="27076@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="517,-847 517,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1163a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="517,-785 517,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="27076@1" ObjectIDZND0="g_114d140@1" Pin0InfoVect0LinkObjId="g_114d140_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11637f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="517,-785 517,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_106ae50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-372 1313,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1069fa0@0" ObjectIDZND0="g_106a8a0@0" Pin0InfoVect0LinkObjId="g_106a8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1069fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-372 1313,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_106b0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1360,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1360,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_106ccc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-552 1313,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27017@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10c7c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-552 1313,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_106cf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-490 1313,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="27077@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-490 1313,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_106d180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-428 1313,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="27077@1" ObjectIDZND0="g_1069fa0@1" Pin0InfoVect0LinkObjId="g_1069fa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_106cf20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-428 1313,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10cbaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="48,-976 48,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="27027@1" ObjectIDZND0="27028@x" ObjectIDZND1="g_11e7560@0" Pin0InfoVect0LinkObjId="SW-166766_0" Pin0InfoVect1LinkObjId="g_11e7560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166765_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="48,-976 48,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10cc600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="84,-1007 49,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="27028@1" ObjectIDZND0="27027@x" ObjectIDZND1="g_11e7560@0" Pin0InfoVect0LinkObjId="SW-166765_0" Pin0InfoVect1LinkObjId="g_11e7560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166766_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="84,-1007 49,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10cc7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7,-990 7,-1007 49,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_11e7560@0" ObjectIDZND0="27028@x" ObjectIDZND1="27027@x" Pin0InfoVect0LinkObjId="SW-166766_0" Pin0InfoVect1LinkObjId="SW-166765_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11e7560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="7,-990 7,-1007 49,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10cd570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-152,-822 -152,-789 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="42248@1" ObjectIDZND0="27065@1" Pin0InfoVect0LinkObjId="SW-166965_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-YM_LC.YM_LC_1C_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-152,-822 -152,-789 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10cd760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-186,-780 -186,-801 -120,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_12284f0@0" ObjectIDZND0="27066@1" Pin0InfoVect0LinkObjId="SW-166966_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12284f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-186,-780 -186,-801 -120,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10ce3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-382 925,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="27056@0" ObjectIDZND0="g_11595f0@0" ObjectIDZND1="34193@x" ObjectIDZND2="g_11a36f0@0" Pin0InfoVect0LinkObjId="g_11595f0_0" Pin0InfoVect1LinkObjId="EC-YM_LC.056Ld_0" Pin0InfoVect2LinkObjId="g_11a36f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166917_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="925,-382 925,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10ce5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-362 925,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_11595f0@0" ObjectIDND1="27056@x" ObjectIDZND0="34193@x" ObjectIDZND1="g_11a36f0@0" Pin0InfoVect0LinkObjId="EC-YM_LC.056Ld_0" Pin0InfoVect1LinkObjId="g_11a36f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_11595f0_0" Pin1InfoVect1LinkObjId="SW-166917_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="925,-362 925,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10ce7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-811 1088,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="42249@1" ObjectIDZND0="27069@1" Pin0InfoVect0LinkObjId="SW-166982_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-YM_LC.YM_LC_2C_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-811 1088,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10ce9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1057,-776 1057,-798 1120,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1222c50@0" ObjectIDZND0="27070@1" Pin0InfoVect0LinkObjId="SW-166983_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1222c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1057,-776 1057,-798 1120,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10d01b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,-630 375,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="27029@x" ObjectIDND1="g_111f7c0@0" ObjectIDZND0="g_10cf0c0@1" Pin0InfoVect0LinkObjId="g_10cf0c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166771_0" Pin1InfoVect1LinkObjId="g_111f7c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,-630 375,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10d0410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="375,-693 375,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_10cf0c0@0" ObjectIDZND0="g_111d300@0" Pin0InfoVect0LinkObjId="g_111d300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10cf0c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="375,-693 375,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10d0670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="667,-633 667,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="27030@x" ObjectIDND1="g_1062040@0" ObjectIDZND0="g_10cf7e0@1" Pin0InfoVect0LinkObjId="g_10cf7e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166775_0" Pin1InfoVect1LinkObjId="g_1062040_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="667,-633 667,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_10d08d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="667,-696 667,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_10cf7e0@0" ObjectIDZND0="g_105fb80@0" Pin0InfoVect0LinkObjId="g_105fb80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10cf7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="667,-696 667,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10d0b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="599,-1093 566,-1093 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="27026@1" ObjectIDZND0="27025@x" ObjectIDZND1="g_11b5af0@0" ObjectIDZND2="g_11b6480@0" Pin0InfoVect0LinkObjId="SW-166746_0" Pin0InfoVect1LinkObjId="g_11b5af0_0" Pin0InfoVect2LinkObjId="g_11b6480_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166747_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="599,-1093 566,-1093 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10d0d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-1093 566,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="27026@x" ObjectIDND1="g_11b5af0@0" ObjectIDND2="g_11b6480@0" ObjectIDZND0="27025@1" Pin0InfoVect0LinkObjId="SW-166746_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166747_0" Pin1InfoVect1LinkObjId="g_11b5af0_0" Pin1InfoVect2LinkObjId="g_11b6480_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="566,-1093 566,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10d0ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="527,-1147 566,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_11b5af0@0" ObjectIDZND0="27026@x" ObjectIDZND1="27025@x" ObjectIDZND2="g_11b6480@0" Pin0InfoVect0LinkObjId="SW-166747_0" Pin0InfoVect1LinkObjId="SW-166746_0" Pin0InfoVect2LinkObjId="g_11b6480_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b5af0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="527,-1147 566,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10d1250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-1093 566,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="27026@x" ObjectIDND1="27025@x" ObjectIDZND0="g_11b5af0@0" ObjectIDZND1="g_11b6480@0" ObjectIDZND2="37856@1" Pin0InfoVect0LinkObjId="g_11b5af0_0" Pin0InfoVect1LinkObjId="g_11b6480_0" Pin0InfoVect2LinkObjId="g_10d14b0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166747_0" Pin1InfoVect1LinkObjId="SW-166746_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="566,-1093 566,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10d14b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="566,-1147 566,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_11b5af0@0" ObjectIDND1="27026@x" ObjectIDND2="27025@x" ObjectIDZND0="37856@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_11b5af0_0" Pin1InfoVect1LinkObjId="SW-166747_0" Pin1InfoVect2LinkObjId="SW-166746_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="566,-1147 566,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10d1710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="611,-1147 566,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_11b6480@0" ObjectIDZND0="g_11b5af0@0" ObjectIDZND1="27026@x" ObjectIDZND2="27025@x" Pin0InfoVect0LinkObjId="g_11b5af0_0" Pin0InfoVect1LinkObjId="SW-166747_0" Pin0InfoVect2LinkObjId="SW-166746_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11b6480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="611,-1147 566,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10d1970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="197,-1149 237,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_11bcf30@0" ObjectIDZND0="27022@x" ObjectIDZND1="27021@x" ObjectIDZND2="g_11bd700@0" Pin0InfoVect0LinkObjId="SW-166728_0" Pin0InfoVect1LinkObjId="SW-166727_0" Pin0InfoVect2LinkObjId="g_11bd700_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11bcf30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="197,-1149 237,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10d2680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-1170 237,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="48675@1" ObjectIDZND0="g_11bcf30@0" ObjectIDZND1="27022@x" ObjectIDZND2="27021@x" Pin0InfoVect0LinkObjId="g_11bcf30_0" Pin0InfoVect1LinkObjId="SW-166728_0" Pin0InfoVect2LinkObjId="SW-166727_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="237,-1170 237,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10d28e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-1149 237,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_11bcf30@0" ObjectIDND1="g_11bd700@0" ObjectIDND2="48675@1" ObjectIDZND0="27022@x" ObjectIDZND1="27021@x" Pin0InfoVect0LinkObjId="SW-166728_0" Pin0InfoVect1LinkObjId="SW-166727_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_11bcf30_0" Pin1InfoVect1LinkObjId="g_11bd700_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="237,-1149 237,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10d2b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="287,-1150 237,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_11bd700@0" ObjectIDZND0="g_11bcf30@0" ObjectIDZND1="27022@x" ObjectIDZND2="27021@x" Pin0InfoVect0LinkObjId="g_11bcf30_0" Pin0InfoVect1LinkObjId="SW-166728_0" Pin0InfoVect2LinkObjId="SW-166727_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11bd700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="287,-1150 237,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10b70c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="849,-908 849,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27015@0" ObjectIDZND0="44522@0" Pin0InfoVect0LinkObjId="SW-280929_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_115f5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="849,-908 849,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10b93f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="849,-969 849,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="44522@1" ObjectIDZND0="44521@0" Pin0InfoVect0LinkObjId="SW-280922_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280929_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="849,-969 849,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10bbe50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="849,-1018 849,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="44521@1" ObjectIDZND0="44523@0" Pin0InfoVect0LinkObjId="SW-280935_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280922_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="849,-1018 849,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10c0a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-1091 938,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="44524@0" ObjectIDZND0="g_10bff90@0" Pin0InfoVect0LinkObjId="g_10bff90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280941_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-1091 938,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10c2a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="517,-892 517,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="27015@0" Pin0InfoVect0LinkObjId="g_115f5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="517,-892 517,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10c39e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="882,-1091 849,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="44524@1" ObjectIDZND0="44523@x" ObjectIDZND1="g_10bc0b0@0" ObjectIDZND2="g_10bcde0@0" Pin0InfoVect0LinkObjId="SW-280935_0" Pin0InfoVect1LinkObjId="g_10bc0b0_0" Pin0InfoVect2LinkObjId="g_10bcde0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-280941_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="882,-1091 849,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10c3c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="849,-1091 849,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="44524@x" ObjectIDND1="g_10bc0b0@0" ObjectIDND2="g_10bcde0@0" ObjectIDZND0="44523@1" Pin0InfoVect0LinkObjId="SW-280935_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-280941_0" Pin1InfoVect1LinkObjId="g_10bc0b0_0" Pin1InfoVect2LinkObjId="g_10bcde0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="849,-1091 849,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10e54e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="810,-1148 849,-1148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_10bc0b0@0" ObjectIDZND0="44524@x" ObjectIDZND1="44523@x" ObjectIDZND2="g_10bcde0@0" Pin0InfoVect0LinkObjId="SW-280941_0" Pin0InfoVect1LinkObjId="SW-280935_0" Pin0InfoVect2LinkObjId="g_10bcde0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10bc0b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="810,-1148 849,-1148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10e61d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="849,-1091 849,-1148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="load" ObjectIDND0="44524@x" ObjectIDND1="44523@x" ObjectIDZND0="g_10bc0b0@0" ObjectIDZND1="g_10bcde0@0" ObjectIDZND2="44677@x" Pin0InfoVect0LinkObjId="g_10bc0b0_0" Pin0InfoVect1LinkObjId="g_10bcde0_0" Pin0InfoVect2LinkObjId="EC-YM_LC.353Ld_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-280941_0" Pin1InfoVect1LinkObjId="SW-280935_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="849,-1091 849,-1148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10e6410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="849,-1148 849,-1177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="44524@x" ObjectIDND1="44523@x" ObjectIDND2="g_10bc0b0@0" ObjectIDZND0="44677@0" Pin0InfoVect0LinkObjId="EC-YM_LC.353Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-280941_0" Pin1InfoVect1LinkObjId="SW-280935_0" Pin1InfoVect2LinkObjId="g_10bc0b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="849,-1148 849,-1177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_10e6670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="900,-1149 849,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_10bcde0@0" ObjectIDZND0="44524@x" ObjectIDZND1="44523@x" ObjectIDZND2="g_10bc0b0@0" Pin0InfoVect0LinkObjId="SW-280941_0" Pin0InfoVect1LinkObjId="SW-280935_0" Pin0InfoVect2LinkObjId="g_10bc0b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_10bcde0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="900,-1149 849,-1149 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="27016" cx="475" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27016" cx="375" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27016" cx="274" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27016" cx="113" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27016" cx="-41" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27016" cx="-207" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27017" cx="721" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27017" cx="1193" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27017" cx="1063" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27017" cx="925" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27017" cx="667" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27017" cx="601" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27017" cx="801" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27017" cx="1088" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27016" cx="90" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27016" cx="-152" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27017" cx="1313" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27015" cx="801" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27015" cx="90" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27015" cx="48" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27015" cx="236" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27015" cx="566" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27015" cx="849" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27015" cx="517" cy="-908" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153539" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -436.000000 -1183.000000)" xlink:href="#dynamicPoint:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26029" ObjectName="DYN-YM_LC"/>
     <cge:Meas_Ref ObjectId="153539"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_1243c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -567.000000 -1239.500000) translate(0,16)">老城变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_ed1f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_ed1f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_ed1f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_ed1f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_ed1f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_ed1f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_ed1f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_ed1f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_ed1f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_102b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1063230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 707.000000 -261.000000) translate(0,12)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_110b920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1158.000000 -259.000000) translate(0,12)">10kV尹地线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_110c7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.000000 -1170.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_110d490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 190.000000 -1236.000000) translate(0,12)">35kV凤老小线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_110e040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -1229.000000) translate(0,12)">35kV哨老麻丙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_110ebf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -108.000000 -927.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_110ee60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -30.000000 -576.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_110f6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -573.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_110fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -795.000000) translate(0,12)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_110fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 616.000000 -796.000000) translate(0,12)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11100d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1112.000000 -874.000000) translate(0,12)">10kV2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1111470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1026.000000 -259.000000) translate(0,12)">10kV老城线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c4320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 884.000000 -260.000000) translate(0,12)">10kV那能线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c4ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 241.000000 -258.000000) translate(0,12)">10kV羊花线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c5420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 75.000000 -258.000000) translate(0,12)">10kV波溪线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c5ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -80.000000 -258.000000) translate(0,12)">10kV库南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c6520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -244.000000 -258.000000) translate(0,12)">10kV水库线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c6a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 177.000000 -282.000000) translate(0,12)">六初郎电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c7820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 814.000000 -303.000000) translate(0,12)">那能电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c9b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 525.000000 -451.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10ca000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -199.000000 -469.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10ca240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -33.000000 -467.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10ca480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 -465.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10ca6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 730.000000 -461.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10ca900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 934.000000 -461.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10cabf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1072.000000 -461.000000) translate(0,12)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a4c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1202.000000 -458.000000) translate(0,12)">058</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a51d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1102.000000 -656.000000) translate(0,12)">060</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a5630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -144.000000 -659.000000) translate(0,12)">059</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a5870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 101.000000 -658.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a5ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 101.000000 -818.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a5cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 810.000000 -657.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a5f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 810.000000 -817.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a6170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 575.000000 -1014.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a63b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.000000 -1013.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a65f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -145.000000 -607.000000) translate(0,12)">0591</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a6830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -145.000000 -779.000000) translate(0,12)">0596</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a6a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -122.000000 -827.000000) translate(0,12)">05967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a8470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99.000000 -603.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a8a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99.000000 -874.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a8c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 808.000000 -602.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a8e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 808.000000 -873.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a90c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1095.000000 -604.000000) translate(0,12)">0601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a9300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1095.000000 -775.000000) translate(0,12)">0606</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a9540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1117.000000 -824.000000) translate(0,12)">06067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a9780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 482.000000 -508.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a99c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 608.000000 -507.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a9c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -34.000000 -522.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11a9e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -34.000000 -413.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aa080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1070.000000 -516.000000) translate(0,12)">0571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aa2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1070.000000 -407.000000) translate(0,12)">0576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aa500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 932.000000 -516.000000) translate(0,12)">0561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aa740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 932.000000 -407.000000) translate(0,12)">0566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aa980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -524.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aabc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -415.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11aae00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 281.000000 -520.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ab040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 281.000000 -411.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ab280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1200.000000 -513.000000) translate(0,12)">0581</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ab4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1200.000000 -404.000000) translate(0,12)">0586</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ab700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 120.000000 -523.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ab940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 120.000000 -414.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10db0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 728.000000 -516.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10db2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 728.000000 -407.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10db530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -598.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10db770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 674.000000 -601.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10db9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 55.000000 -965.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dbbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 86.000000 -1032.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dbe30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -959.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dc070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -1067.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dc2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 271.000000 -1114.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dc4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -960.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dc730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -1068.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10dc970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 597.000000 -1119.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_106d3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1286.000000 -331.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_106ddc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 485.000000 -690.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117abe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 124.000000 -468.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="20" graphid="g_113c180" transform="matrix(1.000000 -0.000000 -0.000000 1.333333 -727.000000 -617.000000) translate(0,16)">1、本站10kVⅡ段电容器060断路器缺陷，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="20" graphid="g_113c180" transform="matrix(1.000000 -0.000000 -0.000000 1.333333 -727.000000 -617.000000) translate(0,36)">处冷备用。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="20" graphid="g_113c180" transform="matrix(1.000000 -0.000000 -0.000000 1.333333 -727.000000 -617.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="20" graphid="g_113c180" transform="matrix(1.000000 -0.000000 -0.000000 1.333333 -727.000000 -617.000000) translate(0,76)">2、本站主变调档机构异常，不能进行</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="20" graphid="g_113c180" transform="matrix(1.000000 -0.000000 -0.000000 1.333333 -727.000000 -617.000000) translate(0,96)">调档操作。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_113ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -371.000000 -1246.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1146290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -372.000000 -1207.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_1146a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -698.000000 -882.000000) translate(0,14)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10cca00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 299.000000 -1146.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10cceb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 621.000000 -1142.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10cd0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -5.000000 -733.000000) translate(0,12)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10cd330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 698.000000 -732.000000) translate(0,12)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10cd950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -232.000000 -835.000000) translate(0,12)">600kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10cec10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1013.000000 -829.000000) translate(0,12)">600kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_10d7a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -739.000000 -260.500000) translate(0,17)">元谋巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_10f7390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -593.000000 -270.000000) translate(0,16)">18787879021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_10f7390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -593.000000 -270.000000) translate(0,36)">13908784331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_10f8420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -593.000000 -305.000000) translate(0,16)">8358115</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10fc290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 120.000000 -736.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10fc990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 837.000000 -737.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10fcd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -265.000000 -890.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10fd050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -899.000000) translate(0,12)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_10fd4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -221.500000 -1228.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c0c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 858.000000 -1012.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c1170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 856.000000 -958.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c13b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 856.000000 -1066.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c15f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 884.000000 -1113.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c2290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -1145.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e6d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 813.000000 -1241.000000) translate(0,12)">35kV库区Ⅱ回线</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-166724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 227.000000 -984.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27019" ObjectName="SW-YM_LC.YM_LC_351BK"/>
     <cge:Meas_Ref ObjectId="166724"/>
    <cge:TPSR_Ref TObjectID="27019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166743">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 557.000000 -985.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27023" ObjectName="SW-YM_LC.YM_LC_352BK"/>
     <cge:Meas_Ref ObjectId="166743"/>
    <cge:TPSR_Ref TObjectID="27023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166776">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 81.000000 -789.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27031" ObjectName="SW-YM_LC.YM_LC_301BK"/>
     <cge:Meas_Ref ObjectId="166776"/>
    <cge:TPSR_Ref TObjectID="27031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166782">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 81.000000 -629.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27033" ObjectName="SW-YM_LC.YM_LC_001BK"/>
     <cge:Meas_Ref ObjectId="166782"/>
    <cge:TPSR_Ref TObjectID="27033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166805">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 792.000000 -788.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27035" ObjectName="SW-YM_LC.YM_LC_302BK"/>
     <cge:Meas_Ref ObjectId="166805"/>
    <cge:TPSR_Ref TObjectID="27035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166811">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 792.000000 -628.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27037" ObjectName="SW-YM_LC.YM_LC_002BK"/>
     <cge:Meas_Ref ObjectId="166811"/>
    <cge:TPSR_Ref TObjectID="27037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -217.452245 -440.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27039" ObjectName="SW-YM_LC.YM_LC_051BK"/>
     <cge:Meas_Ref ObjectId="166834"/>
    <cge:TPSR_Ref TObjectID="27039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166850">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -51.452245 -438.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27042" ObjectName="SW-YM_LC.YM_LC_052BK"/>
     <cge:Meas_Ref ObjectId="166850"/>
    <cge:TPSR_Ref TObjectID="27042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166866">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 103.547755 -439.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27045" ObjectName="SW-YM_LC.YM_LC_053BK"/>
     <cge:Meas_Ref ObjectId="166866"/>
    <cge:TPSR_Ref TObjectID="27045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166882">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 264.547755 -436.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27048" ObjectName="SW-YM_LC.YM_LC_054BK"/>
     <cge:Meas_Ref ObjectId="166882"/>
    <cge:TPSR_Ref TObjectID="27048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 515.000000 -458.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27071" ObjectName="SW-YM_LC.YM_LC_012BK"/>
     <cge:Meas_Ref ObjectId="166996"/>
    <cge:TPSR_Ref TObjectID="27071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166898">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.547755 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27051" ObjectName="SW-YM_LC.YM_LC_055BK"/>
     <cge:Meas_Ref ObjectId="166898"/>
    <cge:TPSR_Ref TObjectID="27051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166914">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 915.547755 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27054" ObjectName="SW-YM_LC.YM_LC_056BK"/>
     <cge:Meas_Ref ObjectId="166914"/>
    <cge:TPSR_Ref TObjectID="27054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166930">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1053.547755 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27057" ObjectName="SW-YM_LC.YM_LC_057BK"/>
     <cge:Meas_Ref ObjectId="166930"/>
    <cge:TPSR_Ref TObjectID="27057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166946">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1183.547755 -429.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27060" ObjectName="SW-YM_LC.YM_LC_058BK"/>
     <cge:Meas_Ref ObjectId="166946"/>
    <cge:TPSR_Ref TObjectID="27060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166962">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -162.452245 -630.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27063" ObjectName="SW-YM_LC.YM_LC_059BK"/>
     <cge:Meas_Ref ObjectId="166962"/>
    <cge:TPSR_Ref TObjectID="27063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-166979">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1078.547755 -627.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27067" ObjectName="SW-YM_LC.YM_LC_060BK"/>
     <cge:Meas_Ref ObjectId="166979"/>
    <cge:TPSR_Ref TObjectID="27067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-280922">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 840.000000 -983.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44521" ObjectName="SW-YM_LC.YM_LC_353BK"/>
     <cge:Meas_Ref ObjectId="280922"/>
    <cge:TPSR_Ref TObjectID="44521"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SF" endPointId="0" endStationName="YM_LC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shaolaomabingxian" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="566,-1169 566,-1197 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37856" ObjectName="AC-35kV.LN_shaolaomabingxian"/>
    <cge:TPSR_Ref TObjectID="37856_SS-222"/></metadata>
   <polyline fill="none" opacity="0" points="566,-1169 566,-1197 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YM_LC" endPointId="0" endStationName="YM_TS" flowDrawDirect="1" flowShape="0" id="AC-35kV.TS_tuanshan1" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="237,-1169 237,-1197 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48675" ObjectName="AC-35kV.TS_tuanshan1"/>
    <cge:TPSR_Ref TObjectID="48675_SS-222"/></metadata>
   <polyline fill="none" opacity="0" points="237,-1169 237,-1197 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_eb3bd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 78.000000 -1109.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11f7960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 139.819337 -1000.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11c8ef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 320.819337 -1086.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1149b70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 650.819337 -1086.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_122b780" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -68.180663 -795.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1226430" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1171.819337 -792.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10bff90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 933.819337 -1085.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217876" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -1019.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217876" ObjectName="YM_LC:YM_LC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219733" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -591.000000 -975.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219733" ObjectName="YM_LC:YM_LC_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217876" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -589.000000 -1060.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217876" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217876" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -588.000000 -1099.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217876" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-601" y="-1250"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-601" y="-1250"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-146" y="-660"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-146" y="-660"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="283" y="-465"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="283" y="-465"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="124" y="-468"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="124" y="-468"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-33" y="-467"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-33" y="-467"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-199" y="-469"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-199" y="-469"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="523" y="-452"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="523" y="-452"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="730" y="-461"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="730" y="-461"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="934" y="-461"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="934" y="-461"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1072" y="-461"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1072" y="-461"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1202" y="-458"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1202" y="-458"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1102" y="-656"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1102" y="-656"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="245" y="-1013"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="245" y="-1013"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="575" y="-1014"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="575" y="-1014"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-382" y="-1254"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-382" y="-1254"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-382" y="-1214"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-382" y="-1214"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="69" x="-700" y="-885"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="69" x="-700" y="-885"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="119" y="-736"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="119" y="-736"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="837" y="-737"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="837" y="-737"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-237" y="-1240"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-237" y="-1240"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="858" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="858" y="-1012"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_125a6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -297.000000 219.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11dce00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -308.000000 204.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_ee4de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -283.000000 189.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e1110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -96.000000 677.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e13d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -107.000000 662.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e15d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -82.000000 647.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e19b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1146.000000 662.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e1c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1135.000000 647.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10e1e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1160.000000 632.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115a4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.000000 1029.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115a780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 288.000000 1014.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115a9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 313.000000 999.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115ade0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 459.000000 1029.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115b0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 448.000000 1014.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115b2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.000000 999.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115b610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 181.000000 732.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115c930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 181.000000 747.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115d0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1049.000000 934.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115da20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1041.000000 965.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115e090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1041.000000 980.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115e560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1041.000000 994.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.000000 950.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115eab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -337.000000 582.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115ecc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -329.000000 612.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115ef00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -329.000000 627.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115f140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -329.000000 643.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115f380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -330.000000 597.250000) translate(0,12)">U0(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_115fc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1244.000000 581.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1160040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1252.000000 611.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1160280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1252.000000 626.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11604c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1252.000000 642.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1160700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1251.000000 596.250000) translate(0,12)">U0(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1160a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 727.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1160c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 742.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d39f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -103.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d3f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -114.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d4140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -89.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d4560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 49.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d4820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 38.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d4a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 63.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d4e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 216.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d5140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 205.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d5380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 230.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d57a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 673.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d5a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 662.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d5ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 687.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d60c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 867.000000 217.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d6380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 202.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d65c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.000000 187.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d69e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1006.000000 219.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d6ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 995.000000 204.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d6ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1020.000000 189.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d7300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1137.000000 216.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d75c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1126.000000 201.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10d7800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1151.000000 186.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10fde60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 159.000000 805.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10fe2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 133.500000 820.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10fe530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 145.000000 835.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10fe770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 165.000000 788.125000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10ff380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 159.000000 640.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10ff630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 133.500000 655.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10ff870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 145.000000 670.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10ffab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 165.000000 623.125000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10ffde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 876.000000 805.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1100050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 850.500000 820.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1100290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 862.000000 835.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11004d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 882.000000 788.125000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1100800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 876.000000 639.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1100a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 850.500000 654.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1100cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 862.000000 669.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1100ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 882.000000 622.125000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c1a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 912.000000 1028.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c1cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 901.000000 1013.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10c1f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 926.000000 998.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YM_LC.051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -215.903226 -274.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34188" ObjectName="EC-YM_LC.051Ld"/>
    <cge:TPSR_Ref TObjectID="34188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_LC.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -49.903226 -274.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34189" ObjectName="EC-YM_LC.052Ld"/>
    <cge:TPSR_Ref TObjectID="34189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_LC.053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 104.096774 -275.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34190" ObjectName="EC-YM_LC.053Ld"/>
    <cge:TPSR_Ref TObjectID="34190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_LC.054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 265.096774 -272.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34191" ObjectName="EC-YM_LC.054Ld"/>
    <cge:TPSR_Ref TObjectID="34191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_LC.055Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 712.096774 -268.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34192" ObjectName="EC-YM_LC.055Ld"/>
    <cge:TPSR_Ref TObjectID="34192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_LC.056Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 916.096774 -268.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34193" ObjectName="EC-YM_LC.056Ld"/>
    <cge:TPSR_Ref TObjectID="34193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_LC.057Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1054.096774 -268.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34194" ObjectName="EC-YM_LC.057Ld"/>
    <cge:TPSR_Ref TObjectID="34194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_LC.058Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1184.096774 -265.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34195" ObjectName="EC-YM_LC.058Ld"/>
    <cge:TPSR_Ref TObjectID="34195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_LC.353Ld">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.666667 844.000000 -1174.000000)" xlink:href="#load:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44677" ObjectName="EC-YM_LC.353Ld"/>
    <cge:TPSR_Ref TObjectID="44677"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166648" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -235.000000 -216.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27039"/>
     <cge:Term_Ref ObjectID="38134"/>
    <cge:TPSR_Ref TObjectID="27039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166649" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -235.000000 -216.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166649" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27039"/>
     <cge:Term_Ref ObjectID="38134"/>
    <cge:TPSR_Ref TObjectID="27039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166646" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -235.000000 -216.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27039"/>
     <cge:Term_Ref ObjectID="38134"/>
    <cge:TPSR_Ref TObjectID="27039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166653" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -216.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166653" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27042"/>
     <cge:Term_Ref ObjectID="38140"/>
    <cge:TPSR_Ref TObjectID="27042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166654" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -216.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27042"/>
     <cge:Term_Ref ObjectID="38140"/>
    <cge:TPSR_Ref TObjectID="27042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166651" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -216.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166651" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27042"/>
     <cge:Term_Ref ObjectID="38140"/>
    <cge:TPSR_Ref TObjectID="27042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166658" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 109.000000 -216.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27045"/>
     <cge:Term_Ref ObjectID="38146"/>
    <cge:TPSR_Ref TObjectID="27045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166659" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 109.000000 -216.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27045"/>
     <cge:Term_Ref ObjectID="38146"/>
    <cge:TPSR_Ref TObjectID="27045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166656" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 109.000000 -216.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166656" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27045"/>
     <cge:Term_Ref ObjectID="38146"/>
    <cge:TPSR_Ref TObjectID="27045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166663" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 275.000000 -216.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166663" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27048"/>
     <cge:Term_Ref ObjectID="38152"/>
    <cge:TPSR_Ref TObjectID="27048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166664" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 275.000000 -216.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27048"/>
     <cge:Term_Ref ObjectID="38152"/>
    <cge:TPSR_Ref TObjectID="27048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166661" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 275.000000 -216.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166661" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27048"/>
     <cge:Term_Ref ObjectID="38152"/>
    <cge:TPSR_Ref TObjectID="27048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166668" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -216.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166668" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27051"/>
     <cge:Term_Ref ObjectID="38158"/>
    <cge:TPSR_Ref TObjectID="27051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166669" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -216.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27051"/>
     <cge:Term_Ref ObjectID="38158"/>
    <cge:TPSR_Ref TObjectID="27051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166666" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -216.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166666" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27051"/>
     <cge:Term_Ref ObjectID="38158"/>
    <cge:TPSR_Ref TObjectID="27051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166673" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -216.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166673" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27054"/>
     <cge:Term_Ref ObjectID="38164"/>
    <cge:TPSR_Ref TObjectID="27054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166674" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -216.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166674" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27054"/>
     <cge:Term_Ref ObjectID="38164"/>
    <cge:TPSR_Ref TObjectID="27054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166671" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -216.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27054"/>
     <cge:Term_Ref ObjectID="38164"/>
    <cge:TPSR_Ref TObjectID="27054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166679" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -216.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166679" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27057"/>
     <cge:Term_Ref ObjectID="38170"/>
    <cge:TPSR_Ref TObjectID="27057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166680" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -216.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27057"/>
     <cge:Term_Ref ObjectID="38170"/>
    <cge:TPSR_Ref TObjectID="27057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166677" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -216.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166677" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27057"/>
     <cge:Term_Ref ObjectID="38170"/>
    <cge:TPSR_Ref TObjectID="27057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166684" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1196.000000 -216.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27060"/>
     <cge:Term_Ref ObjectID="38176"/>
    <cge:TPSR_Ref TObjectID="27060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166685" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1196.000000 -216.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166685" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27060"/>
     <cge:Term_Ref ObjectID="38176"/>
    <cge:TPSR_Ref TObjectID="27060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166682" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1196.000000 -216.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166682" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27060"/>
     <cge:Term_Ref ObjectID="38176"/>
    <cge:TPSR_Ref TObjectID="27060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166690" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -679.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166690" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27063"/>
     <cge:Term_Ref ObjectID="38182"/>
    <cge:TPSR_Ref TObjectID="27063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166691" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -679.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166691" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27063"/>
     <cge:Term_Ref ObjectID="38182"/>
    <cge:TPSR_Ref TObjectID="27063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166687" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -679.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27063"/>
     <cge:Term_Ref ObjectID="38182"/>
    <cge:TPSR_Ref TObjectID="27063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166696" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1201.000000 -662.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27067"/>
     <cge:Term_Ref ObjectID="38190"/>
    <cge:TPSR_Ref TObjectID="27067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166697" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1201.000000 -662.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166697" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27067"/>
     <cge:Term_Ref ObjectID="38190"/>
    <cge:TPSR_Ref TObjectID="27067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166693" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1201.000000 -662.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27067"/>
     <cge:Term_Ref ObjectID="38190"/>
    <cge:TPSR_Ref TObjectID="27067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166701" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -425.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27071"/>
     <cge:Term_Ref ObjectID="38198"/>
    <cge:TPSR_Ref TObjectID="27071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166702" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -425.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27071"/>
     <cge:Term_Ref ObjectID="38198"/>
    <cge:TPSR_Ref TObjectID="27071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166699" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -425.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166699" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27071"/>
     <cge:Term_Ref ObjectID="38198"/>
    <cge:TPSR_Ref TObjectID="27071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 206.000000 -670.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27033"/>
     <cge:Term_Ref ObjectID="38122"/>
    <cge:TPSR_Ref TObjectID="27033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 206.000000 -670.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27033"/>
     <cge:Term_Ref ObjectID="38122"/>
    <cge:TPSR_Ref TObjectID="27033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166624" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 206.000000 -670.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27033"/>
     <cge:Term_Ref ObjectID="38122"/>
    <cge:TPSR_Ref TObjectID="27033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-166629" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 206.000000 -670.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166629" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27033"/>
     <cge:Term_Ref ObjectID="38122"/>
    <cge:TPSR_Ref TObjectID="27033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 923.000000 -669.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27037"/>
     <cge:Term_Ref ObjectID="38130"/>
    <cge:TPSR_Ref TObjectID="27037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166642" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 923.000000 -669.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27037"/>
     <cge:Term_Ref ObjectID="38130"/>
    <cge:TPSR_Ref TObjectID="27037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 923.000000 -669.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27037"/>
     <cge:Term_Ref ObjectID="38130"/>
    <cge:TPSR_Ref TObjectID="27037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-166643" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 923.000000 -669.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166643" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27037"/>
     <cge:Term_Ref ObjectID="38130"/>
    <cge:TPSR_Ref TObjectID="27037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 206.000000 -834.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27031"/>
     <cge:Term_Ref ObjectID="38118"/>
    <cge:TPSR_Ref TObjectID="27031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166622" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 206.000000 -834.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166622" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27031"/>
     <cge:Term_Ref ObjectID="38118"/>
    <cge:TPSR_Ref TObjectID="27031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166618" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 206.000000 -834.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27031"/>
     <cge:Term_Ref ObjectID="38118"/>
    <cge:TPSR_Ref TObjectID="27031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-166623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 206.000000 -834.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27031"/>
     <cge:Term_Ref ObjectID="38118"/>
    <cge:TPSR_Ref TObjectID="27031"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 923.000000 -834.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27035"/>
     <cge:Term_Ref ObjectID="38126"/>
    <cge:TPSR_Ref TObjectID="27035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 923.000000 -834.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27035"/>
     <cge:Term_Ref ObjectID="38126"/>
    <cge:TPSR_Ref TObjectID="27035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166632" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 923.000000 -834.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27035"/>
     <cge:Term_Ref ObjectID="38126"/>
    <cge:TPSR_Ref TObjectID="27035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-166637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 923.000000 -834.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27035"/>
     <cge:Term_Ref ObjectID="38126"/>
    <cge:TPSR_Ref TObjectID="27035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-166599" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -993.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27015"/>
     <cge:Term_Ref ObjectID="38089"/>
    <cge:TPSR_Ref TObjectID="27015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-166600" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -993.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27015"/>
     <cge:Term_Ref ObjectID="38089"/>
    <cge:TPSR_Ref TObjectID="27015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-166601" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -993.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166601" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27015"/>
     <cge:Term_Ref ObjectID="38089"/>
    <cge:TPSR_Ref TObjectID="27015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-166602" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -993.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27015"/>
     <cge:Term_Ref ObjectID="38089"/>
    <cge:TPSR_Ref TObjectID="27015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-166605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -993.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27015"/>
     <cge:Term_Ref ObjectID="38089"/>
    <cge:TPSR_Ref TObjectID="27015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-166606" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -271.000000 -640.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27016"/>
     <cge:Term_Ref ObjectID="38090"/>
    <cge:TPSR_Ref TObjectID="27016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-166607" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -271.000000 -640.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166607" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27016"/>
     <cge:Term_Ref ObjectID="38090"/>
    <cge:TPSR_Ref TObjectID="27016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-166608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -271.000000 -640.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27016"/>
     <cge:Term_Ref ObjectID="38090"/>
    <cge:TPSR_Ref TObjectID="27016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-166611" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -271.000000 -640.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27016"/>
     <cge:Term_Ref ObjectID="38090"/>
    <cge:TPSR_Ref TObjectID="27016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-166609" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -271.000000 -640.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27016"/>
     <cge:Term_Ref ObjectID="38090"/>
    <cge:TPSR_Ref TObjectID="27016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-166612" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1310.000000 -640.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27017"/>
     <cge:Term_Ref ObjectID="38091"/>
    <cge:TPSR_Ref TObjectID="27017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-166613" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1310.000000 -640.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27017"/>
     <cge:Term_Ref ObjectID="38091"/>
    <cge:TPSR_Ref TObjectID="27017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-166614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1310.000000 -640.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27017"/>
     <cge:Term_Ref ObjectID="38091"/>
    <cge:TPSR_Ref TObjectID="27017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-166617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1310.000000 -640.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27017"/>
     <cge:Term_Ref ObjectID="38091"/>
    <cge:TPSR_Ref TObjectID="27017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-166615" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1310.000000 -640.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27017"/>
     <cge:Term_Ref ObjectID="38091"/>
    <cge:TPSR_Ref TObjectID="27017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 350.000000 -1028.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27019"/>
     <cge:Term_Ref ObjectID="38094"/>
    <cge:TPSR_Ref TObjectID="27019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166590" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 350.000000 -1028.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166590" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27019"/>
     <cge:Term_Ref ObjectID="38094"/>
    <cge:TPSR_Ref TObjectID="27019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166587" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 350.000000 -1028.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166587" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27019"/>
     <cge:Term_Ref ObjectID="38094"/>
    <cge:TPSR_Ref TObjectID="27019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-166595" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -1027.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27023"/>
     <cge:Term_Ref ObjectID="38102"/>
    <cge:TPSR_Ref TObjectID="27023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-166596" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -1027.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166596" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27023"/>
     <cge:Term_Ref ObjectID="38102"/>
    <cge:TPSR_Ref TObjectID="27023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-166593" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -1027.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166593" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27023"/>
     <cge:Term_Ref ObjectID="38102"/>
    <cge:TPSR_Ref TObjectID="27023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-166630" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 248.000000 -747.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27074"/>
     <cge:Term_Ref ObjectID="38207"/>
    <cge:TPSR_Ref TObjectID="27074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-166631" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 248.000000 -747.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27074"/>
     <cge:Term_Ref ObjectID="38207"/>
    <cge:TPSR_Ref TObjectID="27074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-166644" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 975.000000 -742.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166644" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27075"/>
     <cge:Term_Ref ObjectID="38211"/>
    <cge:TPSR_Ref TObjectID="27075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-166645" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 975.000000 -742.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="166645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27075"/>
     <cge:Term_Ref ObjectID="38211"/>
    <cge:TPSR_Ref TObjectID="27075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-281006" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 968.000000 -1027.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="281006" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44521"/>
     <cge:Term_Ref ObjectID="21940"/>
    <cge:TPSR_Ref TObjectID="44521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-281007" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 968.000000 -1027.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="281007" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44521"/>
     <cge:Term_Ref ObjectID="21940"/>
    <cge:TPSR_Ref TObjectID="44521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-281003" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 968.000000 -1027.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="281003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44521"/>
     <cge:Term_Ref ObjectID="21940"/>
    <cge:TPSR_Ref TObjectID="44521"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1138060">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 28.000000 -1087.000000)" xlink:href="#voltageTransformer:shape112"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11bd700">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 280.000000 -1130.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11b6480">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 -1127.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_115a080">
    <use class="BV-10KV" transform="matrix(0.944444 -0.000000 0.000000 -0.777778 883.000000 -290.000000)" xlink:href="#voltageTransformer:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_111d300">
    <use class="BV-10KV" transform="matrix(0.861538 -0.000000 0.000000 -0.962264 340.000000 -721.000000)" xlink:href="#voltageTransformer:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_105fb80">
    <use class="BV-10KV" transform="matrix(0.861538 -0.000000 0.000000 -0.962264 632.000000 -724.000000)" xlink:href="#voltageTransformer:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10bcde0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.000000 -1129.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-601" y="-1250"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="35kV老城变YM_LC_059间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-146" y="-660"/></g>
   <g href="35kV老城变10kV羊花线054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="283" y="-465"/></g>
   <g href="35kV老城变10kV波溪线053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="124" y="-468"/></g>
   <g href="35kV老城变10kV库南线052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-33" y="-467"/></g>
   <g href="35kV老城变10kV水库线051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-199" y="-469"/></g>
   <g href="35kV老城变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="523" y="-452"/></g>
   <g href="35kV老城变10kV备用线055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="730" y="-461"/></g>
   <g href="35kV老城变10kV那能线056间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="934" y="-461"/></g>
   <g href="35kV老城变10kV老城线057间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1072" y="-461"/></g>
   <g href="35kV老城变10kV尹地线058间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1202" y="-458"/></g>
   <g href="35kV老城变YM_LC_060间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1102" y="-656"/></g>
   <g href="35kV老城变35kV凤老小线351间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="245" y="-1013"/></g>
   <g href="35kV老城变YM_LC_352间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="575" y="-1014"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-382" y="-1254"/></g>
   <g href="cx_配调_配网接线图35_元谋.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-382" y="-1214"/></g>
   <g href="35kV老城变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="69" x="-700" y="-885"/></g>
   <g href="35kV老城变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="119" y="-736"/></g>
   <g href="35kV老城变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="837" y="-737"/></g>
   <g href="AVC老城站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-237" y="-1240"/></g>
   <g href="35kV老城变35kV库区Ⅱ回线353间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="858" y="-1012"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_11e7560">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -0.265512 -936.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11bcf30">
    <use class="BV-35KV" transform="matrix(0.800000 0.000000 0.000000 -0.745763 191.587590 -1109.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11bda60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 41.000000 -1014.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11b5af0">
    <use class="BV-35KV" transform="matrix(0.800000 0.000000 0.000000 -0.745763 521.587590 -1107.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_114d140">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 510.000000 -724.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_114d960">
    <use class="BV-0KV" transform="matrix(0.800000 -0.000000 0.000000 -0.791667 509.000000 -700.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11f8340">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -177.265512 -284.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_121cce0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -11.265512 -282.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10d7f30">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 142.734488 -283.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10e46e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 303.734488 -280.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_119e630">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 750.734488 -276.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11a36f0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 956.734488 -256.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11b3170">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1092.734488 -276.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11572e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1222.734488 -273.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11595f0">
    <use class="BV-10KV" transform="matrix(0.592593 -0.000000 0.000000 -0.727273 888.000000 -326.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11c10f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -157.000000 -678.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12284f0">
    <use class="BV-10KV" transform="matrix(-0.733333 0.000000 -0.000000 -0.728814 -180.538625 -741.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11b7130">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1083.000000 -675.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1222c50">
    <use class="BV-10KV" transform="matrix(-0.733333 0.000000 -0.000000 -0.728814 1062.461375 -737.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_111f7c0">
    <use class="BV-10KV" transform="matrix(0.866667 0.000000 0.000000 -0.864407 335.636556 -568.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1062040">
    <use class="BV-10KV" transform="matrix(0.866667 0.000000 0.000000 -0.864407 627.636556 -571.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1069fa0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1306.000000 -367.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_106a8a0">
    <use class="BV-0KV" transform="matrix(0.800000 -0.000000 0.000000 -0.791667 1305.000000 -343.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10cf0c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.000000 -656.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10cf7e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 658.000000 -659.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_10bc0b0">
    <use class="BV-35KV" transform="matrix(0.800000 0.000000 0.000000 -0.745763 804.587590 -1108.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YM_LC"/>
</svg>