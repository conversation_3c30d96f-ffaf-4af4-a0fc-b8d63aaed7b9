<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-192" aopId="3940870" id="thSvg" product="E8000V2" version="1.0" viewBox="1 -1252 1742 1215">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape197">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="14,14 20,27 7,27 14,14 14,15 14,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="42" y2="0"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="55" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
   </symbol>
   <symbol id="lightningRod:shape46">
    <circle cx="20" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.13333"/>
    <polyline points="58,100 64,100 " stroke-width="1.13333"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.13333"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape41_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="15,84 21,71 8,71 15,84 15,83 15,84 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="32,42 41,42 41,70 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="73" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="44" x2="41" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="15" y1="45" y2="72"/>
    <polyline DF8003:Layer="PUBLIC" points="17,11 21,20 11,20 17,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="17" y2="17"/>
   </symbol>
   <symbol id="transformer2:shape41_1">
    <circle cx="17" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="41" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="41" y2="45"/>
   </symbol>
   <symbol id="voltageTransformer:shape137">
    <ellipse cx="22" cy="27" fillStyle="0" rx="8" ry="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="13" y1="11" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="25" x2="23" y1="25" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="23" x2="20" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="25" x2="23" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="10" x2="8" y1="25" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="8" x2="5" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="10" x2="8" y1="29" y2="27"/>
    <ellipse cx="16" cy="15" fillStyle="0" rx="8" ry="8.5" stroke-width="1"/>
    <ellipse cx="9" cy="27" fillStyle="0" rx="8" ry="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3aa99f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3aaa6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3aaae60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3aabee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3aad160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3aade00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3aae9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3aaf3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b55fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b55fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ab27a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ab27a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ab4530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ab4530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3ab5550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ab7150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3ab7d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3ab8940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3ab9280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3aba960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3abb550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3abbdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3abc5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3abd690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3abe010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3abeb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3abf4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3ac0970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3ac1480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3ac24b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3ac30f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3ad18c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3ac49e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3ac5fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3ac7500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1225" width="1752" x="-4" y="-1257"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="742" x2="746" y1="-487" y2="-483"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1734" x2="1743" y1="-775" y2="-775"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-139808">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 921.000000 -723.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25065" ObjectName="SW-YR_WD.YR_WD_3011SW"/>
     <cge:Meas_Ref ObjectId="139808"/>
    <cge:TPSR_Ref TObjectID="25065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139855">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 -710.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25069" ObjectName="SW-YR_WD.YR_WD_3021SW"/>
     <cge:Meas_Ref ObjectId="139855"/>
    <cge:TPSR_Ref TObjectID="25069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140063">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25087" ObjectName="SW-YR_WD.YR_WD_0351SW"/>
     <cge:Meas_Ref ObjectId="140063"/>
    <cge:TPSR_Ref TObjectID="25087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140064">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 -250.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25088" ObjectName="SW-YR_WD.YR_WD_0356SW"/>
     <cge:Meas_Ref ObjectId="140064"/>
    <cge:TPSR_Ref TObjectID="25088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139991">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 823.000000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25081" ObjectName="SW-YR_WD.YR_WD_0331SW"/>
     <cge:Meas_Ref ObjectId="139991"/>
    <cge:TPSR_Ref TObjectID="25081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139992">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 823.000000 -247.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25082" ObjectName="SW-YR_WD.YR_WD_0336SW"/>
     <cge:Meas_Ref ObjectId="139992"/>
    <cge:TPSR_Ref TObjectID="25082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139955">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 626.000000 -355.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25078" ObjectName="SW-YR_WD.YR_WD_0321SW"/>
     <cge:Meas_Ref ObjectId="139955"/>
    <cge:TPSR_Ref TObjectID="25078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139956">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 626.000000 -244.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25079" ObjectName="SW-YR_WD.YR_WD_0326SW"/>
     <cge:Meas_Ref ObjectId="139956"/>
    <cge:TPSR_Ref TObjectID="25079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140027">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1024.000000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25084" ObjectName="SW-YR_WD.YR_WD_0341SW"/>
     <cge:Meas_Ref ObjectId="140027"/>
    <cge:TPSR_Ref TObjectID="25084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140028">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1024.000000 -247.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25085" ObjectName="SW-YR_WD.YR_WD_0346SW"/>
     <cge:Meas_Ref ObjectId="140028"/>
    <cge:TPSR_Ref TObjectID="25085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139814">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 920.000000 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25067" ObjectName="SW-YR_WD.YR_WD_0011SW"/>
     <cge:Meas_Ref ObjectId="139814"/>
    <cge:TPSR_Ref TObjectID="25067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139860">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1510.000000 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25071" ObjectName="SW-YR_WD.YR_WD_0021SW"/>
     <cge:Meas_Ref ObjectId="139860"/>
    <cge:TPSR_Ref TObjectID="25071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139932">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1492.000000 -824.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25072" ObjectName="SW-YR_WD.YR_WD_3901SW"/>
     <cge:Meas_Ref ObjectId="139932"/>
    <cge:TPSR_Ref TObjectID="25072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139933">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1542.000000 -880.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25073" ObjectName="SW-YR_WD.YR_WD_39017SW"/>
     <cge:Meas_Ref ObjectId="139933"/>
    <cge:TPSR_Ref TObjectID="25073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139934">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1543.000000 -800.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25074" ObjectName="SW-YR_WD.YR_WD_39010SW"/>
     <cge:Meas_Ref ObjectId="139934"/>
    <cge:TPSR_Ref TObjectID="25074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284296">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 921.000000 -812.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44686" ObjectName="SW-YR_WD.YR_WD_3511SW"/>
     <cge:Meas_Ref ObjectId="284296"/>
    <cge:TPSR_Ref TObjectID="44686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284297">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 921.000000 -1000.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44687" ObjectName="SW-YR_WD.YR_WD_3516SW"/>
     <cge:Meas_Ref ObjectId="284297"/>
    <cge:TPSR_Ref TObjectID="44687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284300">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 965.000000 -1071.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44690" ObjectName="SW-YR_WD.YR_WD_35167SW"/>
     <cge:Meas_Ref ObjectId="284300"/>
    <cge:TPSR_Ref TObjectID="44690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284299">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.000000 -964.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44689" ObjectName="SW-YR_WD.YR_WD_35160SW"/>
     <cge:Meas_Ref ObjectId="284299"/>
    <cge:TPSR_Ref TObjectID="44689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284298">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 963.000000 -876.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44688" ObjectName="SW-YR_WD.YR_WD_35117SW"/>
     <cge:Meas_Ref ObjectId="284298"/>
    <cge:TPSR_Ref TObjectID="44688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284301">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.000000 -1150.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44691" ObjectName="SW-YR_WD.YR_WD_3514SW"/>
     <cge:Meas_Ref ObjectId="284301"/>
    <cge:TPSR_Ref TObjectID="44691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284356">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1181.000000 -810.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44693" ObjectName="SW-YR_WD.YR_WD_3521SW"/>
     <cge:Meas_Ref ObjectId="284356"/>
    <cge:TPSR_Ref TObjectID="44693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284357">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1181.000000 -998.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44694" ObjectName="SW-YR_WD.YR_WD_3526SW"/>
     <cge:Meas_Ref ObjectId="284357"/>
    <cge:TPSR_Ref TObjectID="44694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284360">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1225.000000 -1069.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44697" ObjectName="SW-YR_WD.YR_WD_35267SW"/>
     <cge:Meas_Ref ObjectId="284360"/>
    <cge:TPSR_Ref TObjectID="44697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284359">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.000000 -962.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44696" ObjectName="SW-YR_WD.YR_WD_35260SW"/>
     <cge:Meas_Ref ObjectId="284359"/>
    <cge:TPSR_Ref TObjectID="44696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284358">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 -874.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44695" ObjectName="SW-YR_WD.YR_WD_35217SW"/>
     <cge:Meas_Ref ObjectId="284358"/>
    <cge:TPSR_Ref TObjectID="44695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284361">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.000000 -1117.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44698" ObjectName="SW-YR_WD.YR_WD_3524SW"/>
     <cge:Meas_Ref ObjectId="284361"/>
    <cge:TPSR_Ref TObjectID="44698"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YR_WD.YR_WD_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="635,-774 1668,-774 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25059" ObjectName="BS-YR_WD.YR_WD_3IM"/>
    <cge:TPSR_Ref TObjectID="25059"/></metadata>
   <polyline fill="none" opacity="0" points="635,-774 1668,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_WD.YR_WD_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="533,-430 1670,-430 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25060" ObjectName="BS-YR_WD.YR_WD_9IM"/>
    <cge:TPSR_Ref TObjectID="25060"/></metadata>
   <polyline fill="none" opacity="0" points="533,-430 1670,-430 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YR_WD.YR_WD_035Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 -125.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33979" ObjectName="EC-YR_WD.YR_WD_035Ld"/>
    <cge:TPSR_Ref TObjectID="33979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_WD.YR_WD_033Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 823.000000 -122.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33977" ObjectName="EC-YR_WD.YR_WD_033Ld"/>
    <cge:TPSR_Ref TObjectID="33977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_WD.YR_WD_034Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1024.000000 -122.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33978" ObjectName="EC-YR_WD.YR_WD_034Ld"/>
    <cge:TPSR_Ref TObjectID="33978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_WD.YR_WD_032Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 626.000000 -122.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33976" ObjectName="EC-YR_WD.YR_WD_032Ld"/>
    <cge:TPSR_Ref TObjectID="33976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_WD.YR_WD_352Ld">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1185.000000 -1188.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44718" ObjectName="EC-YR_WD.YR_WD_352Ld"/>
    <cge:TPSR_Ref TObjectID="44718"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3342a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1588.000000 -879.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3247230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1589.000000 -799.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2ddc3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-667 930,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25064@0" ObjectIDZND0="25089@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139803_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-667 930,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b985f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-562 929,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25089@0" ObjectIDZND0="25066@1" Pin0InfoVect0LinkObjId="SW-139811_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ddc3b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-562 929,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_323a7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1520,-715 1520,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25069@0" ObjectIDZND0="25068@1" Pin0InfoVect0LinkObjId="SW-139852_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139855_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1520,-715 1520,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_339f1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1520,-667 1520,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25068@0" ObjectIDZND0="25090@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139852_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1520,-667 1520,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2de73e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1519,-565 1519,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25090@0" ObjectIDZND0="25070@1" Pin0InfoVect0LinkObjId="SW-139857_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_339f1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1519,-565 1519,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_324f600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-311 1232,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25086@0" ObjectIDZND0="25088@1" Pin0InfoVect0LinkObjId="SW-140064_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140061_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-311 1232,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_335a480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-366 1232,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25087@0" ObjectIDZND0="25086@1" Pin0InfoVect0LinkObjId="SW-140061_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140063_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-366 1232,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f8a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="832,-312 832,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25080@0" ObjectIDZND0="25082@1" Pin0InfoVect0LinkObjId="SW-139992_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139989_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="832,-312 832,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e64820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="832,-363 832,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25081@0" ObjectIDZND0="25080@1" Pin0InfoVect0LinkObjId="SW-139989_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139991_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="832,-363 832,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdf630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="635,-309 635,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25077@0" ObjectIDZND0="25079@1" Pin0InfoVect0LinkObjId="SW-139956_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139953_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="635,-309 635,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc1680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="635,-360 635,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25078@0" ObjectIDZND0="25077@1" Pin0InfoVect0LinkObjId="SW-139953_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139955_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="635,-360 635,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b48730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="601,-210 601,-228 635,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b988d0@0" ObjectIDZND0="25079@x" ObjectIDZND1="33976@x" Pin0InfoVect0LinkObjId="SW-139956_0" Pin0InfoVect1LinkObjId="EC-YR_WD.YR_WD_032Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b988d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="601,-210 601,-228 635,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca62e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1033,-312 1033,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25083@0" ObjectIDZND0="25085@1" Pin0InfoVect0LinkObjId="SW-140028_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140025_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1033,-312 1033,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_435fde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1033,-363 1033,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25084@0" ObjectIDZND0="25083@1" Pin0InfoVect0LinkObjId="SW-140025_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140027_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1033,-363 1033,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3360630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-213 999,-231 1033,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_326de80@0" ObjectIDZND0="25085@x" ObjectIDZND1="33978@x" Pin0InfoVect0LinkObjId="SW-140028_0" Pin0InfoVect1LinkObjId="EC-YR_WD.YR_WD_034Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_326de80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="999,-213 999,-231 1033,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_340b610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1520,-751 1520,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25069@1" ObjectIDZND0="25059@0" Pin0InfoVect0LinkObjId="g_337ea20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139855_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1520,-751 1520,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_339be40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="929,-515 929,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25066@0" ObjectIDZND0="25067@1" Pin0InfoVect0LinkObjId="SW-139814_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139811_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="929,-515 929,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dec030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1519,-516 1519,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25070@0" ObjectIDZND0="25071@1" Pin0InfoVect0LinkObjId="SW-139860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139857_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1519,-516 1519,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33352c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,-211 1198,-229 1232,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="g_33445d0@0" ObjectIDZND0="25088@x" ObjectIDZND1="33979@x" ObjectIDZND2="g_33c4ca0@0" Pin0InfoVect0LinkObjId="SW-140064_0" Pin0InfoVect1LinkObjId="EC-YR_WD.YR_WD_035Ld_0" Pin0InfoVect2LinkObjId="g_33c4ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33445d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1198,-211 1198,-229 1232,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ddca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="794,-209 794,-227 832,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_33ac7e0@0" ObjectIDZND0="25082@x" ObjectIDZND1="33977@x" Pin0InfoVect0LinkObjId="SW-139992_0" Pin0InfoVect1LinkObjId="EC-YR_WD.YR_WD_033Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33ac7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="794,-209 794,-227 832,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c2830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="635,-249 635,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25079@0" ObjectIDZND0="g_2b988d0@0" ObjectIDZND1="33976@x" Pin0InfoVect0LinkObjId="g_2b988d0_0" Pin0InfoVect1LinkObjId="EC-YR_WD.YR_WD_032Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139956_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="635,-249 635,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c1530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="635,-149 635,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33976@0" ObjectIDZND0="g_2b988d0@0" ObjectIDZND1="25079@x" Pin0InfoVect0LinkObjId="g_2b988d0_0" Pin0InfoVect1LinkObjId="SW-139956_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_WD.YR_WD_032Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="635,-149 635,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c8560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="832,-252 832,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25082@0" ObjectIDZND0="g_33ac7e0@0" ObjectIDZND1="33977@x" Pin0InfoVect0LinkObjId="g_33ac7e0_0" Pin0InfoVect1LinkObjId="EC-YR_WD.YR_WD_033Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139992_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="832,-252 832,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_322ce80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="832,-149 832,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33977@0" ObjectIDZND0="g_33ac7e0@0" ObjectIDZND1="25082@x" Pin0InfoVect0LinkObjId="g_33ac7e0_0" Pin0InfoVect1LinkObjId="SW-139992_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_WD.YR_WD_033Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="832,-149 832,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c1230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1033,-252 1033,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25085@0" ObjectIDZND0="g_326de80@0" ObjectIDZND1="33978@x" Pin0InfoVect0LinkObjId="g_326de80_0" Pin0InfoVect1LinkObjId="EC-YR_WD.YR_WD_034Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140028_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1033,-252 1033,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3206690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1033,-149 1033,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33978@0" ObjectIDZND0="g_326de80@0" ObjectIDZND1="25085@x" Pin0InfoVect0LinkObjId="g_326de80_0" Pin0InfoVect1LinkObjId="SW-140028_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_WD.YR_WD_034Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1033,-149 1033,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_334e280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-255 1232,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="25088@0" ObjectIDZND0="g_33445d0@0" ObjectIDZND1="33979@x" ObjectIDZND2="g_33c4ca0@0" Pin0InfoVect0LinkObjId="g_33445d0_0" Pin0InfoVect1LinkObjId="EC-YR_WD.YR_WD_035Ld_0" Pin0InfoVect2LinkObjId="g_33c4ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140064_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-255 1232,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_337c1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-152 1232,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="33979@0" ObjectIDZND0="g_33445d0@0" ObjectIDZND1="25088@x" ObjectIDZND2="g_33c4ca0@0" Pin0InfoVect0LinkObjId="g_33445d0_0" Pin0InfoVect1LinkObjId="SW-140064_0" Pin0InfoVect2LinkObjId="g_33c4ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_WD.YR_WD_035Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-152 1232,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3242860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="635,-396 635,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25078@1" ObjectIDZND0="25060@0" Pin0InfoVect0LinkObjId="g_32654c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139955_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="635,-396 635,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32654c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="832,-399 832,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25081@1" ObjectIDZND0="25060@0" Pin0InfoVect0LinkObjId="g_3242860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139991_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="832,-399 832,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3358af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1033,-399 1033,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25084@1" ObjectIDZND0="25060@0" Pin0InfoVect0LinkObjId="g_3242860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140027_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1033,-399 1033,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_323fab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-402 1232,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25087@1" ObjectIDZND0="25060@0" Pin0InfoVect0LinkObjId="g_3242860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140063_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-402 1232,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33bb320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1583,-885 1592,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25073@1" ObjectIDZND0="g_3342a10@0" Pin0InfoVect0LinkObjId="g_3342a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139933_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1583,-885 1592,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_322d4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1584,-805 1593,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25074@1" ObjectIDZND0="g_3247230@0" Pin0InfoVect0LinkObjId="g_3247230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139934_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1584,-805 1593,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33e4160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1547,-885 1501,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25073@0" ObjectIDZND0="25072@x" ObjectIDZND1="g_33c3150@0" ObjectIDZND2="g_3230970@0" Pin0InfoVect0LinkObjId="SW-139932_0" Pin0InfoVect1LinkObjId="g_33c3150_0" Pin0InfoVect2LinkObjId="g_3230970_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139933_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1547,-885 1501,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c23c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-885 1501,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25073@x" ObjectIDND1="g_33c3150@0" ObjectIDND2="g_3230970@0" ObjectIDZND0="25072@1" Pin0InfoVect0LinkObjId="SW-139932_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-139933_0" Pin1InfoVect1LinkObjId="g_33c3150_0" Pin1InfoVect2LinkObjId="g_3230970_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-885 1501,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2de2670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1548,-805 1501,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="25074@0" ObjectIDZND0="25072@x" ObjectIDZND1="25059@0" Pin0InfoVect0LinkObjId="SW-139932_0" Pin0InfoVect1LinkObjId="g_340b610_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139934_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1548,-805 1501,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_334f300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-829 1501,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="25072@0" ObjectIDZND0="25074@x" ObjectIDZND1="25059@0" Pin0InfoVect0LinkObjId="SW-139934_0" Pin0InfoVect1LinkObjId="g_340b610_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-829 1501,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_337ea20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-805 1501,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="25074@x" ObjectIDND1="25072@x" ObjectIDZND0="25059@0" Pin0InfoVect0LinkObjId="g_340b610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-139934_0" Pin1InfoVect1LinkObjId="SW-139932_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-805 1501,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3382d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-764 930,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25065@1" ObjectIDZND0="25059@0" Pin0InfoVect0LinkObjId="g_340b610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139808_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-764 930,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c7dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-229 1307,-229 1307,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="g_33445d0@0" ObjectIDND1="25088@x" ObjectIDND2="33979@x" ObjectIDZND0="g_33c4ca0@1" Pin0InfoVect0LinkObjId="g_33c4ca0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33445d0_0" Pin1InfoVect1LinkObjId="SW-140064_0" Pin1InfoVect2LinkObjId="EC-YR_WD.YR_WD_035Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-229 1307,-229 1307,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_339a9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-920 1553,-920 1553,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25073@x" ObjectIDND1="25072@x" ObjectIDND2="g_3230970@0" ObjectIDZND0="g_33c3150@0" Pin0InfoVect0LinkObjId="g_33c3150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-139933_0" Pin1InfoVect1LinkObjId="SW-139932_0" Pin1InfoVect2LinkObjId="g_3230970_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-920 1553,-920 1553,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33ca990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-920 1501,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_33c3150@0" ObjectIDND1="g_3230970@0" ObjectIDZND0="25073@x" ObjectIDZND1="25072@x" Pin0InfoVect0LinkObjId="SW-139933_0" Pin0InfoVect1LinkObjId="SW-139932_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33c3150_0" Pin1InfoVect1LinkObjId="g_3230970_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-920 1501,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31f43b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="968,-883 930,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44688@0" ObjectIDZND0="44686@x" ObjectIDZND1="44685@x" Pin0InfoVect0LinkObjId="SW-284296_0" Pin0InfoVect1LinkObjId="SW-284295_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="968,-883 930,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33edb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-853 930,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44686@1" ObjectIDZND0="44688@x" ObjectIDZND1="44685@x" Pin0InfoVect0LinkObjId="SW-284298_0" Pin0InfoVect1LinkObjId="SW-284295_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284296_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="930,-853 930,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33edd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-883 930,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44688@x" ObjectIDND1="44686@x" ObjectIDZND0="44685@0" Pin0InfoVect0LinkObjId="SW-284295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-284298_0" Pin1InfoVect1LinkObjId="SW-284296_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-883 930,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33edfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,-971 930,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44689@0" ObjectIDZND0="44685@x" ObjectIDZND1="44687@x" Pin0InfoVect0LinkObjId="SW-284295_0" Pin0InfoVect1LinkObjId="SW-284297_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284299_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="967,-971 930,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_338d510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-941 930,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44685@1" ObjectIDZND0="44689@x" ObjectIDZND1="44687@x" Pin0InfoVect0LinkObjId="SW-284299_0" Pin0InfoVect1LinkObjId="SW-284297_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="930,-941 930,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_338d770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-971 930,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="44689@x" ObjectIDND1="44685@x" ObjectIDZND0="44687@0" Pin0InfoVect0LinkObjId="SW-284297_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-284299_0" Pin1InfoVect1LinkObjId="SW-284295_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-971 930,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_323b0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="970,-1078 930,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44690@0" ObjectIDZND0="44687@x" ObjectIDZND1="g_336eee0@0" ObjectIDZND2="44691@x" Pin0InfoVect0LinkObjId="SW-284297_0" Pin0InfoVect1LinkObjId="g_336eee0_0" Pin0InfoVect2LinkObjId="SW-284301_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="970,-1078 930,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bba530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-1041 930,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44687@1" ObjectIDZND0="44690@x" ObjectIDZND1="g_336eee0@0" ObjectIDZND2="44691@x" Pin0InfoVect0LinkObjId="SW-284300_0" Pin0InfoVect1LinkObjId="g_336eee0_0" Pin0InfoVect2LinkObjId="SW-284301_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284297_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="930,-1041 930,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3239f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="878,-1155 930,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_336eee0@0" ObjectIDZND0="44690@x" ObjectIDZND1="44687@x" ObjectIDZND2="44691@x" Pin0InfoVect0LinkObjId="SW-284300_0" Pin0InfoVect1LinkObjId="SW-284297_0" Pin0InfoVect2LinkObjId="SW-284301_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_336eee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="878,-1155 930,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d0ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-1078 930,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="44690@x" ObjectIDND1="44687@x" ObjectIDZND0="g_336eee0@0" ObjectIDZND1="44691@x" ObjectIDZND2="37837@1" Pin0InfoVect0LinkObjId="g_336eee0_0" Pin0InfoVect1LinkObjId="SW-284301_0" Pin0InfoVect2LinkObjId="g_33d1130_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-284300_0" Pin1InfoVect1LinkObjId="SW-284297_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="930,-1078 930,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d1130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-1155 930,-1187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_336eee0@0" ObjectIDND1="44690@x" ObjectIDND2="44687@x" ObjectIDZND0="37837@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_336eee0_0" Pin1InfoVect1LinkObjId="SW-284300_0" Pin1InfoVect2LinkObjId="SW-284297_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-1155 930,-1187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3369d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-1155 962,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_336eee0@0" ObjectIDND1="44690@x" ObjectIDND2="44687@x" ObjectIDZND0="44691@0" Pin0InfoVect0LinkObjId="SW-284301_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_336eee0_0" Pin1InfoVect1LinkObjId="SW-284300_0" Pin1InfoVect2LinkObjId="SW-284297_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-1155 962,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3360200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1228,-881 1190,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44695@0" ObjectIDZND0="44693@x" ObjectIDZND1="44692@x" Pin0InfoVect0LinkObjId="SW-284356_0" Pin0InfoVect1LinkObjId="SW-284355_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284358_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1228,-881 1190,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3397290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-851 1190,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44693@1" ObjectIDZND0="44695@x" ObjectIDZND1="44692@x" Pin0InfoVect0LinkObjId="SW-284358_0" Pin0InfoVect1LinkObjId="SW-284355_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284356_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-851 1190,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33974f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-881 1190,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44693@x" ObjectIDND1="44695@x" ObjectIDZND0="44692@0" Pin0InfoVect0LinkObjId="SW-284355_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-284356_0" Pin1InfoVect1LinkObjId="SW-284358_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-881 1190,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3397750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,-969 1190,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44696@0" ObjectIDZND0="44692@x" ObjectIDZND1="44694@x" Pin0InfoVect0LinkObjId="SW-284355_0" Pin0InfoVect1LinkObjId="SW-284357_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284359_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1227,-969 1190,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33979b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-939 1190,-969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44692@1" ObjectIDZND0="44696@x" ObjectIDZND1="44694@x" Pin0InfoVect0LinkObjId="SW-284359_0" Pin0InfoVect1LinkObjId="SW-284357_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284355_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-939 1190,-969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31f2be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-969 1190,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="44692@x" ObjectIDND1="44696@x" ObjectIDZND0="44694@0" Pin0InfoVect0LinkObjId="SW-284357_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-284355_0" Pin1InfoVect1LinkObjId="SW-284359_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-969 1190,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31f2e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-1076 1190,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44697@0" ObjectIDZND0="44694@x" ObjectIDZND1="g_31f3300@0" ObjectIDZND2="g_3231670@0" Pin0InfoVect0LinkObjId="SW-284357_0" Pin0InfoVect1LinkObjId="g_31f3300_0" Pin0InfoVect2LinkObjId="g_3231670_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-1076 1190,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31f30a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-1039 1190,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="44694@1" ObjectIDZND0="44697@x" ObjectIDZND1="g_31f3300@0" ObjectIDZND2="g_3231670@0" Pin0InfoVect0LinkObjId="SW-284360_0" Pin0InfoVect1LinkObjId="g_31f3300_0" Pin0InfoVect2LinkObjId="g_3231670_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284357_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-1039 1190,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3404b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="929,-455 929,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25067@0" ObjectIDZND0="25060@0" Pin0InfoVect0LinkObjId="g_3242860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139814_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="929,-455 929,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3625bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="699,-927 699,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_33b5c50@1" Pin0InfoVect0LinkObjId="g_33b5c50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3342a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="699,-927 699,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3625e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="699,-838 699,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_33b5c50@0" ObjectIDZND0="25059@0" Pin0InfoVect0LinkObjId="g_340b610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33b5c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="699,-838 699,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ac8c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-728 930,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25065@0" ObjectIDZND0="25064@1" Pin0InfoVect0LinkObjId="SW-139803_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139808_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-728 930,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ac8e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-817 930,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44686@0" ObjectIDZND0="25059@0" Pin0InfoVect0LinkObjId="g_340b610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-817 930,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3239480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-815 1190,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="44693@0" ObjectIDZND0="25059@0" Pin0InfoVect0LinkObjId="g_340b610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284356_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-815 1190,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c98a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1519,-455 1519,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25071@0" ObjectIDZND0="25060@0" Pin0InfoVect0LinkObjId="g_3242860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-139860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1519,-455 1519,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33de540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1150,-1122 1190,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_31f3300@0" ObjectIDZND0="44694@x" ObjectIDZND1="44697@x" ObjectIDZND2="g_3231670@0" Pin0InfoVect0LinkObjId="SW-284357_0" Pin0InfoVect1LinkObjId="SW-284360_0" Pin0InfoVect2LinkObjId="g_3231670_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31f3300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1150,-1122 1190,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e6cf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-1076 1190,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="44694@x" ObjectIDND1="44697@x" ObjectIDZND0="g_31f3300@0" ObjectIDZND1="g_3231670@0" ObjectIDZND2="44698@x" Pin0InfoVect0LinkObjId="g_31f3300_0" Pin0InfoVect1LinkObjId="g_3231670_0" Pin0InfoVect2LinkObjId="SW-284361_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-284357_0" Pin1InfoVect1LinkObjId="SW-284360_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-1076 1190,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e6d160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-1122 1190,-1139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_31f3300@0" ObjectIDND1="44694@x" ObjectIDND2="44697@x" ObjectIDZND0="g_3231670@0" Pin0InfoVect0LinkObjId="g_3231670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_31f3300_0" Pin1InfoVect1LinkObjId="SW-284357_0" Pin1InfoVect2LinkObjId="SW-284360_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-1122 1190,-1139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32304b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-1122 1190,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="44698@0" ObjectIDZND0="g_31f3300@0" ObjectIDZND1="44694@x" ObjectIDZND2="44697@x" Pin0InfoVect0LinkObjId="g_31f3300_0" Pin0InfoVect1LinkObjId="SW-284357_0" Pin0InfoVect2LinkObjId="SW-284360_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-284361_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-1122 1190,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3230710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-1173 1190,-1193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3231670@1" ObjectIDZND0="44718@0" Pin0InfoVect0LinkObjId="EC-YR_WD.YR_WD_352Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3231670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-1173 1190,-1193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33dc880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-993 1501,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_33d56f0@0" ObjectIDZND0="g_3230970@1" Pin0InfoVect0LinkObjId="g_3230970_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d56f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-993 1501,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33dcae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1501,-935 1501,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3230970@0" ObjectIDZND0="g_33c3150@0" ObjectIDZND1="25073@x" ObjectIDZND2="25072@x" Pin0InfoVect0LinkObjId="g_33c3150_0" Pin0InfoVect1LinkObjId="SW-139933_0" Pin0InfoVect2LinkObjId="SW-139932_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3230970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1501,-935 1501,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33dcd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-529 726,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_33cabf0@0" ObjectIDZND0="g_2b58e60@1" Pin0InfoVect0LinkObjId="g_2b58e60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33cabf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="726,-529 726,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33dcfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="683,-458 726,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="g_33b2750@0" ObjectIDZND0="g_2b58e60@0" ObjectIDZND1="25060@0" Pin0InfoVect0LinkObjId="g_2b58e60_0" Pin0InfoVect1LinkObjId="g_3242860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33b2750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="683,-458 726,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32671c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-466 726,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="g_2b58e60@0" ObjectIDZND0="g_33b2750@0" ObjectIDZND1="25060@0" Pin0InfoVect0LinkObjId="g_33b2750_0" Pin0InfoVect1LinkObjId="g_3242860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b58e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="726,-466 726,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3267420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="726,-458 726,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="busSection" ObjectIDND0="g_33b2750@0" ObjectIDND1="g_2b58e60@0" ObjectIDZND0="25060@0" Pin0InfoVect0LinkObjId="g_3242860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33b2750_0" Pin1InfoVect1LinkObjId="g_2b58e60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="726,-458 726,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3351d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1307,-155 1307,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_33dd380@0" ObjectIDZND0="g_33c4ca0@0" Pin0InfoVect0LinkObjId="g_33c4ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33dd380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1307,-155 1307,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b3090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1264,-1122 1250,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2e7a270@1" ObjectIDZND0="44698@1" Pin0InfoVect0LinkObjId="SW-284361_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e7a270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1264,-1122 1250,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b32f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1307,-1122 1317,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2e7a270@0" ObjectIDZND0="g_2e7aa30@0" Pin0InfoVect0LinkObjId="g_2e7aa30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e7a270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1307,-1122 1317,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3232f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1057,-1155 1067,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3233190@0" ObjectIDZND0="g_33b3550@0" Pin0InfoVect0LinkObjId="g_33b3550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3233190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1057,-1155 1067,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3233a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1014,-1155 998,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3233190@1" ObjectIDZND0="44691@1" Pin0InfoVect0LinkObjId="SW-284301_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3233190_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1014,-1155 998,-1155 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25059" cx="930" cy="-774" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25059" cx="1501" cy="-774" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25059" cx="699" cy="-774" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25059" cx="930" cy="-774" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25059" cx="1190" cy="-774" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25060" cx="635" cy="-430" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25060" cx="832" cy="-430" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25060" cx="1033" cy="-430" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25060" cx="1232" cy="-430" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25060" cx="726" cy="-430" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25060" cx="929" cy="-430" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25060" cx="1519" cy="-430" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-130472" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.500000 -912.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23912" ObjectName="DYN-YR_WD"/>
     <cge:Meas_Ref ObjectId="130472"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_33b2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -505.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd7400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd7400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd7400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd7400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd7400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd7400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd7400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd7400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2dd7400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_333da40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 136.000000 -1007.500000) translate(0,16)">维的变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31aa7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1622.500000 -796.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3398180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1310.500000 -453.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dd9fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 804.500000 -119.000000) translate(0,15)">的鲁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3386b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 998.500000 -119.000000) translate(0,15)">夜可腊线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33c60f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1206.500000 -119.000000) translate(0,15)">维的线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ddd4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 640.500000 -1051.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_341fe80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.500000 -55.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_333df20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.500000 -1054.000000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3362200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.500000 -603.000000) translate(0,15)">10kV母线电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3362200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.500000 -603.000000) translate(0,33)">压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_323b7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1509.000000 -856.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324a250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 -831.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dec6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 939.000000 -688.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e2120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -741.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bdeec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 938.000000 -536.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f6290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 936.000000 -480.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33bace0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 644.000000 -330.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337a6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 642.000000 -274.000000) translate(0,12)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33dd570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 642.000000 -385.000000) translate(0,12)">0321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3340e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 841.000000 -333.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_323aa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 839.000000 -277.000000) translate(0,12)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f3620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 839.000000 -391.000000) translate(0,12)">0331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3379c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -333.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 -277.000000) translate(0,12)">0346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_324e320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 -391.000000) translate(0,12)">0341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f7d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1241.000000 -332.000000) translate(0,12)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f6610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1239.000000 -280.000000) translate(0,12)">0356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3398580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1239.000000 -391.000000) translate(0,12)">0351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3385230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1529.000000 -688.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33dd8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1527.000000 -740.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3366d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1528.000000 -537.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 -480.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d9410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1550.000000 -907.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_322e9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 596.500000 -120.000000) translate(0,15)">大保关线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_33f8170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.500000 -470.000000) translate(0,15)">本站所有“四遥”信号未与厂站进行核对</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_33f8170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.500000 -470.000000) translate(0,33)">1号主变档位无法调档</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_33f8170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.500000 -470.000000) translate(0,51)">2号主变间隔所有开关信号不正确</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_33f8170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.500000 -470.000000) translate(0,69)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_33f8170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.500000 -470.000000) translate(0,87)">1号主变、2号主变调档未做过遥控试验</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_33f8170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.500000 -470.000000) translate(0,105)">2号主变高压侧302断路器未做过遥控试验</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,48,48)" font-family="SimSun" font-size="18" graphid="g_33f8170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 24.500000 -470.000000) translate(0,123)">2号主变低压侧002断路器未做过遥控试验</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_2ddccb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 116.000000 -638.000000) translate(0,20)">公用间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3383270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 275.000000 -1020.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3379590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 276.000000 -987.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ed370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 -627.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ed370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 -627.000000) translate(0,27)">SZ11-2000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ed370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 -627.000000) translate(0,42)">2000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ed370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 -627.000000) translate(0,57)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ed370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 -627.000000) translate(0,72)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ed370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 -627.000000) translate(0,87)">Ud=7.82%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3344180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1365.000000 -610.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3344180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1365.000000 -610.000000) translate(0,27)">SZ11-8000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3344180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1365.000000 -610.000000) translate(0,42)">8000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3344180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1365.000000 -610.000000) translate(0,57)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3344180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1365.000000 -610.000000) translate(0,72)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3344180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1365.000000 -610.000000) translate(0,87)">Ud=7.36%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33612d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1.000000 -109.000000) translate(0,17)">永仁巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_323c3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 147.000000 -119.500000) translate(0,17)">13638777384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_323c3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 147.000000 -119.500000) translate(0,38)">13987885824</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33f5770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 147.000000 -149.000000) translate(0,17)">6891047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33bfd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -650.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33cc2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1421.000000 -638.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3375c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.500000 -1250.000000) translate(0,15)">35kV立维线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3374d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1020.500000 -1138.000000) translate(0,12)">线路电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3359370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1343.500000 -1127.000000) translate(0,12)">线路电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33597d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 945.000000 -936.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3688a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -1030.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3688c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 965.000000 -1105.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3688ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -999.000000) translate(0,12)">35160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36890e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -1181.000000) translate(0,12)">3514</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3689320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -842.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_322f170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 962.000000 -909.000000) translate(0,12)">35117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_322f390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1199.000000 -933.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_322f5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1197.000000 -1028.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_322f810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1227.000000 -1104.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_322fa50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -997.000000) translate(0,12)">35260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3246670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -1148.000000) translate(0,12)">3524</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32468b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1197.000000 -840.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3246af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1221.000000 -906.000000) translate(0,12)">35217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3246fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 -1247.000000) translate(0,15)">35kV维的线</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-139803">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 921.000000 -659.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25064" ObjectName="SW-YR_WD.YR_WD_301BK"/>
     <cge:Meas_Ref ObjectId="139803"/>
    <cge:TPSR_Ref TObjectID="25064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139811">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 920.000000 -507.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25066" ObjectName="SW-YR_WD.YR_WD_001BK"/>
     <cge:Meas_Ref ObjectId="139811"/>
    <cge:TPSR_Ref TObjectID="25066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139852">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 -659.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25068" ObjectName="SW-YR_WD.YR_WD_302BK"/>
     <cge:Meas_Ref ObjectId="139852"/>
    <cge:TPSR_Ref TObjectID="25068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140061">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 -302.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25086" ObjectName="SW-YR_WD.YR_WD_035BK"/>
     <cge:Meas_Ref ObjectId="140061"/>
    <cge:TPSR_Ref TObjectID="25086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139989">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 823.000000 -304.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25080" ObjectName="SW-YR_WD.YR_WD_033BK"/>
     <cge:Meas_Ref ObjectId="139989"/>
    <cge:TPSR_Ref TObjectID="25080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139953">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 626.000000 -301.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25077" ObjectName="SW-YR_WD.YR_WD_032BK"/>
     <cge:Meas_Ref ObjectId="139953"/>
    <cge:TPSR_Ref TObjectID="25077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140025">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1024.000000 -304.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25083" ObjectName="SW-YR_WD.YR_WD_034BK"/>
     <cge:Meas_Ref ObjectId="140025"/>
    <cge:TPSR_Ref TObjectID="25083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-139857">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1510.000000 -508.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25070" ObjectName="SW-YR_WD.YR_WD_002BK"/>
     <cge:Meas_Ref ObjectId="139857"/>
    <cge:TPSR_Ref TObjectID="25070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284295">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 921.000000 -906.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44685" ObjectName="SW-YR_WD.YR_WD_351BK"/>
     <cge:Meas_Ref ObjectId="284295"/>
    <cge:TPSR_Ref TObjectID="44685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-284355">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1181.000000 -904.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44692" ObjectName="SW-YR_WD.YR_WD_352BK"/>
     <cge:Meas_Ref ObjectId="284355"/>
    <cge:TPSR_Ref TObjectID="44692"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YR" endPointId="0" endStationName="YR_WD" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_weidi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="930,-1186 930,-1215 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37837" ObjectName="AC-35kV.LN_weidi"/>
    <cge:TPSR_Ref TObjectID="37837_SS-192"/></metadata>
   <polyline fill="none" opacity="0" points="930,-1186 930,-1215 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_33445d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 -157.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b988d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 594.000000 -156.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_326de80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 992.000000 -159.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33b2750">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 676.000000 -454.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ac7e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 787.000000 -155.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33b5c50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 694.000000 -833.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c3150">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1546.000000 -959.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b58e60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 721.000000 -461.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_336eee0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 871.000000 -1101.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31f3300">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1143.000000 -1068.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3231670">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1180.000000 -1134.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3230970">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 -930.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33dd380">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1292.000000 -62.000000)" xlink:href="#lightningRod:shape197"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c4ca0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.000000 -168.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e7a270">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1312.500000 -1115.500000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e7aa30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 -1115.000000)" xlink:href="#lightningRod:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33b3550">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1062.000000 -1148.000000)" xlink:href="#lightningRod:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3233190">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1062.500000 -1148.500000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139712" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -717.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139712" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25064"/>
     <cge:Term_Ref ObjectID="35307"/>
    <cge:TPSR_Ref TObjectID="25064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139713" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -717.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139713" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25064"/>
     <cge:Term_Ref ObjectID="35307"/>
    <cge:TPSR_Ref TObjectID="25064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -717.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25064"/>
     <cge:Term_Ref ObjectID="35307"/>
    <cge:TPSR_Ref TObjectID="25064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139718" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1065.000000 -524.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139718" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25066"/>
     <cge:Term_Ref ObjectID="35311"/>
    <cge:TPSR_Ref TObjectID="25066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139719" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1065.000000 -524.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139719" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25066"/>
     <cge:Term_Ref ObjectID="35311"/>
    <cge:TPSR_Ref TObjectID="25066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139715" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1065.000000 -524.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139715" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25066"/>
     <cge:Term_Ref ObjectID="35311"/>
    <cge:TPSR_Ref TObjectID="25066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139726" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1687.000000 -710.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139726" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25068"/>
     <cge:Term_Ref ObjectID="35315"/>
    <cge:TPSR_Ref TObjectID="25068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139727" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1687.000000 -710.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139727" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25068"/>
     <cge:Term_Ref ObjectID="35315"/>
    <cge:TPSR_Ref TObjectID="25068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139723" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1687.000000 -710.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25068"/>
     <cge:Term_Ref ObjectID="35315"/>
    <cge:TPSR_Ref TObjectID="25068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139732" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1689.000000 -547.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139732" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25070"/>
     <cge:Term_Ref ObjectID="35319"/>
    <cge:TPSR_Ref TObjectID="25070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139733" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1689.000000 -547.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139733" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25070"/>
     <cge:Term_Ref ObjectID="35319"/>
    <cge:TPSR_Ref TObjectID="25070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139729" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1689.000000 -547.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139729" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25070"/>
     <cge:Term_Ref ObjectID="35319"/>
    <cge:TPSR_Ref TObjectID="25070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139756" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 638.000000 -83.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139756" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25077"/>
     <cge:Term_Ref ObjectID="35333"/>
    <cge:TPSR_Ref TObjectID="25077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139757" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 638.000000 -83.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25077"/>
     <cge:Term_Ref ObjectID="35333"/>
    <cge:TPSR_Ref TObjectID="25077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 638.000000 -83.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25077"/>
     <cge:Term_Ref ObjectID="35333"/>
    <cge:TPSR_Ref TObjectID="25077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139762" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 836.000000 -83.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139762" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25080"/>
     <cge:Term_Ref ObjectID="35339"/>
    <cge:TPSR_Ref TObjectID="25080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139763" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 836.000000 -83.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25080"/>
     <cge:Term_Ref ObjectID="35339"/>
    <cge:TPSR_Ref TObjectID="25080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139759" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 836.000000 -83.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139759" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25080"/>
     <cge:Term_Ref ObjectID="35339"/>
    <cge:TPSR_Ref TObjectID="25080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139768" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -86.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139768" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25083"/>
     <cge:Term_Ref ObjectID="35345"/>
    <cge:TPSR_Ref TObjectID="25083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139769" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -86.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139769" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25083"/>
     <cge:Term_Ref ObjectID="35345"/>
    <cge:TPSR_Ref TObjectID="25083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139765" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -86.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25083"/>
     <cge:Term_Ref ObjectID="35345"/>
    <cge:TPSR_Ref TObjectID="25083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-139774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1240.000000 -85.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25086"/>
     <cge:Term_Ref ObjectID="35351"/>
    <cge:TPSR_Ref TObjectID="25086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-139775" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1240.000000 -85.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139775" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25086"/>
     <cge:Term_Ref ObjectID="35351"/>
    <cge:TPSR_Ref TObjectID="25086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-139771" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1240.000000 -85.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139771" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25086"/>
     <cge:Term_Ref ObjectID="35351"/>
    <cge:TPSR_Ref TObjectID="25086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-139722" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 -667.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139722" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25089"/>
     <cge:Term_Ref ObjectID="35357"/>
    <cge:TPSR_Ref TObjectID="25089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-139721" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 -667.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139721" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25089"/>
     <cge:Term_Ref ObjectID="35357"/>
    <cge:TPSR_Ref TObjectID="25089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-139736" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1690.000000 -626.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139736" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25090"/>
     <cge:Term_Ref ObjectID="35361"/>
    <cge:TPSR_Ref TObjectID="25090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-139735" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1690.000000 -626.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139735" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25090"/>
     <cge:Term_Ref ObjectID="35361"/>
    <cge:TPSR_Ref TObjectID="25090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-139737" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 608.000000 -859.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139737" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25059"/>
     <cge:Term_Ref ObjectID="35299"/>
    <cge:TPSR_Ref TObjectID="25059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-139738" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 608.000000 -859.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139738" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25059"/>
     <cge:Term_Ref ObjectID="35299"/>
    <cge:TPSR_Ref TObjectID="25059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-139739" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 608.000000 -859.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139739" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25059"/>
     <cge:Term_Ref ObjectID="35299"/>
    <cge:TPSR_Ref TObjectID="25059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-139740" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 608.000000 -859.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139740" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25059"/>
     <cge:Term_Ref ObjectID="35299"/>
    <cge:TPSR_Ref TObjectID="25059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-139744" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 608.000000 -859.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139744" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25059"/>
     <cge:Term_Ref ObjectID="35299"/>
    <cge:TPSR_Ref TObjectID="25059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-139745" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 541.000000 -520.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139745" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25060"/>
     <cge:Term_Ref ObjectID="35300"/>
    <cge:TPSR_Ref TObjectID="25060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-139746" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 541.000000 -520.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139746" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25060"/>
     <cge:Term_Ref ObjectID="35300"/>
    <cge:TPSR_Ref TObjectID="25060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-139747" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 541.000000 -520.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139747" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25060"/>
     <cge:Term_Ref ObjectID="35300"/>
    <cge:TPSR_Ref TObjectID="25060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-139748" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 541.000000 -520.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139748" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25060"/>
     <cge:Term_Ref ObjectID="35300"/>
    <cge:TPSR_Ref TObjectID="25060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-139752" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 541.000000 -520.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139752" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25060"/>
     <cge:Term_Ref ObjectID="35300"/>
    <cge:TPSR_Ref TObjectID="25060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-284279" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -947.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="284279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44685"/>
     <cge:Term_Ref ObjectID="22195"/>
    <cge:TPSR_Ref TObjectID="44685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-284280" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -947.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="284280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44685"/>
     <cge:Term_Ref ObjectID="22195"/>
    <cge:TPSR_Ref TObjectID="44685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-284269" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -947.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="284269" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44685"/>
     <cge:Term_Ref ObjectID="22195"/>
    <cge:TPSR_Ref TObjectID="44685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-284292" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -940.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="284292" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44692"/>
     <cge:Term_Ref ObjectID="22209"/>
    <cge:TPSR_Ref TObjectID="44692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-284293" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -940.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="284293" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44692"/>
     <cge:Term_Ref ObjectID="22209"/>
    <cge:TPSR_Ref TObjectID="44692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-284282" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -940.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="284282" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44692"/>
     <cge:Term_Ref ObjectID="22209"/>
    <cge:TPSR_Ref TObjectID="44692"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="96" y="-1018"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="47" y="-1035"/></g>
   <g href="35kV维的变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="116" y="-638"/></g>
   <g href="35kV维的变YR_WD_032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="644" y="-330"/></g>
   <g href="35kV维的变YR_WD_033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="841" y="-333"/></g>
   <g href="35kV维的变YR_WD_034间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1042" y="-333"/></g>
   <g href="35kV维的变YR_WD_035间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1241" y="-332"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="264" y="-1028"/></g>
   <g href="cx_配调_配网接线图35_永仁.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="264" y="-995"/></g>
   <g href="35kV维的变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="969" y="-650"/></g>
   <g href="35kV维的变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1417" y="-639"/></g>
   <g href="35kV维的变YR_WD_351间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="945" y="-936"/></g>
   <g href="35kV维的变YR_WD_352间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1199" y="-933"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b96600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1008.000000 717.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3395d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 997.000000 702.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f2fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1022.000000 687.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32475b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1009.000000 524.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_323d2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 998.000000 509.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_323d4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1023.000000 494.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33749c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.000000 85.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31abb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 70.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31abd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 596.000000 55.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3249eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1632.000000 710.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3202b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1621.000000 695.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3202d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1646.000000 680.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3378520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1632.000000 548.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33fa780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1621.000000 533.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33fa980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1646.000000 518.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 802.000000 -634.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7bf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 15.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337ed80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 30.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b9110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 523.000000 813.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33dc190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 539.000000 797.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3202390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 531.000000 828.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_333e7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 531.000000 843.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e6f3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 531.000000 857.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3383760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.000000 476.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b58290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 493.000000 460.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_322fd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 485.000000 491.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_322e780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 485.000000 506.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33879d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 485.000000 520.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1618.000000 -592.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e2ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 15.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b574c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 30.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_337cc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 780.000000 84.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32409c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 769.000000 69.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3240c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 794.000000 54.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3350400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 985.000000 87.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b45d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 974.000000 72.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b47e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 999.000000 57.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3624ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1185.000000 86.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3360b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1174.000000 71.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3360d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.000000 56.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3430d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 799.000000 947.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3431050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 788.000000 932.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3431290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 813.000000 917.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3393510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1293.000000 939.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33937d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1282.000000 924.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3393a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1307.000000 909.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e6d7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1003.000000 1245.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="1010" cy="1238" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e6dda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1284.000000 1247.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="1291" cy="1240" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YR_WD.YR_WD_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35359"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.954545 -0.000000 0.000000 -0.882353 893.000000 -558.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.954545 -0.000000 0.000000 -0.882353 893.000000 -558.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25089" ObjectName="TF-YR_WD.YR_WD_1T"/>
    <cge:TPSR_Ref TObjectID="25089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YR_WD.YR_WD_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35363"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.924242 -0.000000 0.000000 -0.862745 1484.000000 -561.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.924242 -0.000000 0.000000 -0.862745 1484.000000 -561.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25090" ObjectName="TF-YR_WD.YR_WD_2T"/>
    <cge:TPSR_Ref TObjectID="25090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.000000 -922.000000)" xlink:href="#transformer2:shape41_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.000000 -922.000000)" xlink:href="#transformer2:shape41_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="96" y="-1018"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="96" y="-1018"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="47" y="-1035"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="47" y="-1035"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="116" y="-638"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="116" y="-638"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="644" y="-330"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="644" y="-330"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="841" y="-333"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="841" y="-333"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1042" y="-333"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1042" y="-333"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1241" y="-332"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1241" y="-332"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="264" y="-1028"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="264" y="-1028"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="264" y="-995"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="264" y="-995"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="969" y="-650"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="969" y="-650"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1417" y="-639"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1417" y="-639"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="945" y="-936"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="945" y="-936"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1199" y="-933"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1199" y="-933"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_33d56f0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1517.000000 -988.000000)" xlink:href="#voltageTransformer:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33cabf0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 741.000000 -524.000000)" xlink:href="#voltageTransformer:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 80.000000 -955.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217883" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 142.000000 -785.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217883" ObjectName="YR_WD:YR_WD_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-139712" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 142.000000 -864.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139712" ObjectName="YR_WD:YR_WD_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-139712" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 143.000000 -827.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="139712" ObjectName="YR_WD:YR_WD_301BK_P"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YR_WD"/>
</svg>