<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-334" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-845 -868 2072 1816">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape20">
    <rect fill="rgb(0,255,0)" fillStyle="1" height="18" stroke="rgb(0,255,0)" stroke-width="1" width="32" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape21">
    <rect fill="rgb(0,255,0)" fillStyle="1" height="18" stroke="rgb(255,0,0)" stroke-width="1" width="32" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape22">
    <rect fill="none" height="19" stroke="rgb(255,255,0)" stroke-width="1" width="33" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape23">
    <rect fill="none" height="19" stroke="rgb(255,255,0)" stroke-width="1" width="33" x="0" y="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape139">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27195" x1="7" x2="7" y1="6" y2="15"/>
    <rect height="25" stroke-width="1" width="12" x="1" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="48" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="46" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="reactance:shape1">
    <polyline points="13,13 11,13 9,14 8,14 6,15 5,16 3,17 2,19 1,21 1,22 0,24 0,26 0,28 1,30 1,31 2,33 3,34 5,36 6,37 8,38 9,38 11,39 13,39 15,39 17,38 18,38 20,37 21,36 23,34 24,33 25,31 25,30 26,28 26,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="13" x2="25" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="47" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="13" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape27_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape31_0">
    <circle cx="29" cy="42" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="102" y1="54" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="98" x2="103" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="103" x2="101" y1="87" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="24" x2="42" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="42" y1="54" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="24" y1="54" y2="38"/>
   </symbol>
   <symbol id="transformer:shape31_1">
    <circle cx="70" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="69" y1="31" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="62" y1="24" y2="31"/>
   </symbol>
   <symbol id="transformer:shape31-2">
    <circle cx="70" cy="64" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="78" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="69" y1="62" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="62" y1="69" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape74_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="50" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="52" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="54" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="49" y1="29" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="29" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 49,62 49,33 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="48" y2="6"/>
    <circle cx="15" cy="63" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="10" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="62" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape74_1">
    <circle cx="15" cy="85" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="91" y2="93"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="19,91 21,89 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,82 12,82 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="87" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="19" y1="87" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="87" y2="82"/>
   </symbol>
   <symbol id="voltageTransformer:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="22" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="18" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="26" x2="22" y1="23" y2="25"/>
    <circle cx="22" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="10" y2="12"/>
    <circle cx="35" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="46" x2="51" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="46" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="50" x2="51" y1="24" y2="28"/>
    <circle cx="48" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="27" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="33" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="35" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.229325" x1="18" x2="29" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.22074" x1="18" x2="29" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="41" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="34" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="21" x2="25" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="20" x2="27" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="18" x2="29" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.229325" x1="41" x2="41" y1="40" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.239135" x1="34" x2="34" y1="40" y2="51"/>
    <circle cx="24" cy="26" fillStyle="0" r="9" stroke-width="1"/>
    <circle cx="24" cy="10" fillStyle="0" r="9" stroke-width="1"/>
    <circle cx="10" cy="18" fillStyle="0" r="9" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_360bb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2df4890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_360db00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_360e6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_360f8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3610400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3610c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3611720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f74e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2f74e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3615270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3615270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3616f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3616f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3617e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3619a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_361a710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_361b4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_361bc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_361d4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_361e040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_361e8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_361f090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3620170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3620af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_36215e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3621fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3623610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3624040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3625240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3625eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_362c3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_362cec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3627490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3628a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1826" width="2082" x="-850" y="-873"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eae9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -116.000000 -828.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaf8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -127.000000 -843.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eb0450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -102.000000 -858.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e19880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 466.000000 -142.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e19ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 455.000000 -157.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e19cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 -172.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1a020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.000000 334.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1a280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.000000 319.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1a4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 487.000000 304.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1a7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 418.000000 616.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1aa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.000000 601.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1ac90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.000000 586.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1afc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1116.000000 609.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1b220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 594.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1b460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 579.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1c7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -56.000000 -176.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1d160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -50.000000 -192.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1d3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -66.000000 -207.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1d900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -56.000000 -146.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1db80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -50.000000 -223.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1e6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -56.000000 -161.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1ea50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 78.000000 514.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1ecd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 84.000000 498.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1ef10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 68.000000 483.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1f150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 78.000000 544.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1f390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 84.000000 467.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1f5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 78.000000 529.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="255,878 258,886 252,886 255,878 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="206,889 210,895 204,895 206,889 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-307792">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.860000 -275.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47771" ObjectName="SW-CX_STGF.CX_STGF_201BK"/>
     <cge:Meas_Ref ObjectId="307792"/>
    <cge:TPSR_Ref TObjectID="47771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307751">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1013.942857 -546.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47756" ObjectName="SW-CX_STGF.CX_STGF_291BK"/>
     <cge:Meas_Ref ObjectId="307751"/>
    <cge:TPSR_Ref TObjectID="47756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307769">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.942857 -548.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47762" ObjectName="SW-CX_STGF.CX_STGF_292BK"/>
     <cge:Meas_Ref ObjectId="307769"/>
    <cge:TPSR_Ref TObjectID="47762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307799">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 379.000000 181.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47778" ObjectName="SW-CX_STGF.CX_STGF_301BK"/>
     <cge:Meas_Ref ObjectId="307799"/>
    <cge:TPSR_Ref TObjectID="47778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307823">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 343.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47796" ObjectName="SW-CX_STGF.CX_STGF_369BK"/>
     <cge:Meas_Ref ObjectId="307823"/>
    <cge:TPSR_Ref TObjectID="47796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307828">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 219.000000 349.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47800" ObjectName="SW-CX_STGF.CX_STGF_371BK"/>
     <cge:Meas_Ref ObjectId="307828"/>
    <cge:TPSR_Ref TObjectID="47800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307803">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 690.000000 353.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47781" ObjectName="SW-CX_STGF.CX_STGF_361BK"/>
     <cge:Meas_Ref ObjectId="307803"/>
    <cge:TPSR_Ref TObjectID="47781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307808">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 850.000000 359.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47784" ObjectName="SW-CX_STGF.CX_STGF_362BK"/>
     <cge:Meas_Ref ObjectId="307808"/>
    <cge:TPSR_Ref TObjectID="47784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307813">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 983.000000 362.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47788" ObjectName="SW-CX_STGF.CX_STGF_363BK"/>
     <cge:Meas_Ref ObjectId="307813"/>
    <cge:TPSR_Ref TObjectID="47788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307818">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1131.000000 360.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47792" ObjectName="SW-CX_STGF.CX_STGF_364BK"/>
     <cge:Meas_Ref ObjectId="307818"/>
    <cge:TPSR_Ref TObjectID="47792"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_STGF.CX_STGF_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="45591"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.000000 -25.000000)" xlink:href="#transformer:shape31_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="45593"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.000000 -25.000000)" xlink:href="#transformer:shape31_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="45595"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.000000 -25.000000)" xlink:href="#transformer:shape31-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="47807" ObjectName="TF-CX_STGF.CX_STGF_1T"/>
    <cge:TPSR_Ref TObjectID="47807"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_STGF.CX_STGF_2IM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="148,-448 1160,-448 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47753" ObjectName="BS-CX_STGF.CX_STGF_2IM"/>
    <cge:TPSR_Ref TObjectID="47753"/></metadata>
   <polyline fill="none" opacity="0" points="148,-448 1160,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_STGF.CX_STGF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-52,252 1212,252 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47754" ObjectName="BS-CX_STGF.CX_STGF_3IM"/>
    <cge:TPSR_Ref TObjectID="47754"/></metadata>
   <polyline fill="none" opacity="0" points="-52,252 1212,252 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 15.000000 772.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 694.000000 552.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 854.000000 558.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 987.000000 561.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1135.000000 559.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2e94bf0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 495.990196 -22.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ed5170" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 339.009804 105.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e1f810" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 285.000000 -27.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_2ee2ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="818,-599 797,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="47770@0" ObjectIDZND0="47768@x" ObjectIDZND1="g_2f73dc0@0" Pin0InfoVect0LinkObjId="SW-307787_0" Pin0InfoVect1LinkObjId="g_2f73dc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307789_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="818,-599 797,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f12460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="797,-554 797,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="47768@1" ObjectIDZND0="47770@x" ObjectIDZND1="g_2f73dc0@0" Pin0InfoVect0LinkObjId="SW-307789_0" Pin0InfoVect1LinkObjId="g_2f73dc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307787_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="797,-554 797,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f7a3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="797,-599 797,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="47770@x" ObjectIDND1="47768@x" ObjectIDZND0="g_2f73dc0@0" Pin0InfoVect0LinkObjId="g_2f73dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307789_0" Pin1InfoVect1LinkObjId="SW-307787_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="797,-599 797,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fadd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="824,-490 797,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="47769@0" ObjectIDZND0="47753@0" ObjectIDZND1="47768@x" Pin0InfoVect0LinkObjId="g_2f51ed0_0" Pin0InfoVect1LinkObjId="SW-307787_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307788_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="824,-490 797,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f739e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="797,-448 797,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47753@0" ObjectIDZND0="47769@x" ObjectIDZND1="47768@x" Pin0InfoVect0LinkObjId="SW-307788_0" Pin0InfoVect1LinkObjId="SW-307787_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fadd70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="797,-448 797,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f73bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="797,-490 797,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="47769@x" ObjectIDND1="47753@0" ObjectIDZND0="47768@0" Pin0InfoVect0LinkObjId="SW-307787_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307788_0" Pin1InfoVect1LinkObjId="g_2fadd70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="797,-490 797,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f51900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="371,-255 388,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47775@0" ObjectIDZND0="47773@x" ObjectIDZND1="47771@x" Pin0InfoVect0LinkObjId="SW-307794_0" Pin0InfoVect1LinkObjId="SW-307792_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307796_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="371,-255 388,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f51af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-228 388,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47773@1" ObjectIDZND0="47775@x" ObjectIDZND1="47771@x" Pin0InfoVect0LinkObjId="SW-307796_0" Pin0InfoVect1LinkObjId="SW-307792_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307794_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="388,-228 388,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f51ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-255 388,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="47775@x" ObjectIDND1="47773@x" ObjectIDZND0="47771@0" Pin0InfoVect0LinkObjId="SW-307792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307796_0" Pin1InfoVect1LinkObjId="SW-307794_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,-255 388,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f51ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-424 388,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47772@1" ObjectIDZND0="47753@0" Pin0InfoVect0LinkObjId="g_2fadd70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307793_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,-424 388,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fc7450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1045,-530 1023,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47761@0" ObjectIDZND0="47758@x" ObjectIDZND1="47756@x" Pin0InfoVect0LinkObjId="SW-307753_0" Pin0InfoVect1LinkObjId="SW-307751_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307756_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1045,-530 1023,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fc7640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-504 1023,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47758@1" ObjectIDZND0="47761@x" ObjectIDZND1="47756@x" Pin0InfoVect0LinkObjId="SW-307756_0" Pin0InfoVect1LinkObjId="SW-307751_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307753_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-504 1023,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fc7830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-530 1023,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="47761@x" ObjectIDND1="47758@x" ObjectIDZND0="47756@0" Pin0InfoVect0LinkObjId="SW-307751_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307756_0" Pin1InfoVect1LinkObjId="SW-307753_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-530 1023,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fc9d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1003,-605 1023,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="47760@0" ObjectIDZND0="47756@x" ObjectIDZND1="47757@x" Pin0InfoVect0LinkObjId="SW-307751_0" Pin0InfoVect1LinkObjId="SW-307752_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307755_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1003,-605 1023,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fc9f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-581 1023,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47756@1" ObjectIDZND0="47760@x" ObjectIDZND1="47757@x" Pin0InfoVect0LinkObjId="SW-307755_0" Pin0InfoVect1LinkObjId="SW-307752_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307751_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-581 1023,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f19220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-605 1023,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="47760@x" ObjectIDND1="47756@x" ObjectIDZND0="47757@1" Pin0InfoVect0LinkObjId="SW-307752_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307755_0" Pin1InfoVect1LinkObjId="SW-307751_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-605 1023,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f1b4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1049,-690 1023,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="47759@0" ObjectIDZND0="47757@x" ObjectIDZND1="g_2e13910@0" ObjectIDZND2="g_2f1b8c0@0" Pin0InfoVect0LinkObjId="SW-307752_0" Pin0InfoVect1LinkObjId="g_2e13910_0" Pin0InfoVect2LinkObjId="g_2f1b8c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1049,-690 1023,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f1b6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-659 1023,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="47757@0" ObjectIDZND0="47759@x" ObjectIDZND1="g_2e13910@0" ObjectIDZND2="g_2f1b8c0@0" Pin0InfoVect0LinkObjId="SW-307754_0" Pin0InfoVect1LinkObjId="g_2e13910_0" Pin0InfoVect2LinkObjId="g_2f1b8c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307752_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-659 1023,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e10c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1044,-785 1023,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2f1b8c0@0" ObjectIDZND0="g_2e13910@0" ObjectIDZND1="47759@x" ObjectIDZND2="47757@x" Pin0InfoVect0LinkObjId="g_2e13910_0" Pin0InfoVect1LinkObjId="SW-307754_0" Pin0InfoVect2LinkObjId="SW-307752_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f1b8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1044,-785 1023,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e10e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-785 1023,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" ObjectIDND0="g_2f1b8c0@0" ObjectIDND1="g_2e13910@0" ObjectIDND2="47759@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f1b8c0_0" Pin1InfoVect1LinkObjId="g_2e13910_0" Pin1InfoVect2LinkObjId="SW-307754_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-785 1023,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e11050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="987,-735 1023,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2e13910@0" ObjectIDZND0="47759@x" ObjectIDZND1="47757@x" ObjectIDZND2="g_2f1b8c0@0" Pin0InfoVect0LinkObjId="SW-307754_0" Pin0InfoVect1LinkObjId="SW-307752_0" Pin0InfoVect2LinkObjId="g_2f1b8c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e13910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="987,-735 1023,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e11240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-690 1023,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="47759@x" ObjectIDND1="47757@x" ObjectIDZND0="g_2e13910@0" ObjectIDZND1="g_2f1b8c0@0" Pin0InfoVect0LinkObjId="g_2e13910_0" Pin0InfoVect1LinkObjId="g_2f1b8c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307754_0" Pin1InfoVect1LinkObjId="SW-307752_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-690 1023,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e11430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-735 1023,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2e13910@0" ObjectIDND1="47759@x" ObjectIDND2="47757@x" ObjectIDZND0="g_2f1b8c0@0" Pin0InfoVect0LinkObjId="g_2f1b8c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e13910_0" Pin1InfoVect1LinkObjId="SW-307754_0" Pin1InfoVect2LinkObjId="SW-307752_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-735 1023,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e13720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-468 1023,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47758@0" ObjectIDZND0="47753@0" Pin0InfoVect0LinkObjId="g_2fadd70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307753_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-468 1023,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e94a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="526,-79 526,-94 502,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47777@1" ObjectIDZND0="47807@x" ObjectIDZND1="g_2e94410@0" ObjectIDZND2="g_2f520c0@1" Pin0InfoVect0LinkObjId="g_2e96f00_0" Pin0InfoVect1LinkObjId="g_2e94410_0" Pin0InfoVect2LinkObjId="g_2f520c0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307798_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="526,-79 526,-94 502,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e96f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="502,-94 474,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="47777@x" ObjectIDND1="g_2f520c0@1" ObjectIDZND0="47807@x" ObjectIDZND1="g_2e94410@0" Pin0InfoVect0LinkObjId="g_2e94a00_0" Pin0InfoVect1LinkObjId="g_2e94410_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307798_0" Pin1InfoVect1LinkObjId="g_2f520c0_1" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="502,-94 474,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e970f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-94 387,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="47777@x" ObjectIDND1="g_2f520c0@0" ObjectIDND2="g_2e94410@0" ObjectIDZND0="47807@x" Pin0InfoVect0LinkObjId="g_2e94a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307798_0" Pin1InfoVect1LinkObjId="g_2f520c0_0" Pin1InfoVect2LinkObjId="g_2e94410_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="474,-94 387,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2edbfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-76 474,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="g_2e94410@1" ObjectIDZND0="47777@x" ObjectIDZND1="g_2f520c0@0" ObjectIDZND2="47807@x" Pin0InfoVect0LinkObjId="SW-307798_0" Pin0InfoVect1LinkObjId="g_2f520c0_0" Pin0InfoVect2LinkObjId="g_2e94a00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e94410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="474,-76 474,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2edc1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="502,-18 502,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_2e94bf0@0" ObjectIDZND0="g_2f520c0@1" Pin0InfoVect0LinkObjId="g_2f520c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e94bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="502,-18 502,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2edc3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="502,-74 502,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="g_2f520c0@0" ObjectIDZND0="47777@x" ObjectIDZND1="47807@x" ObjectIDZND2="g_2e94410@0" Pin0InfoVect0LinkObjId="SW-307798_0" Pin0InfoVect1LinkObjId="g_2e94a00_0" Pin0InfoVect2LinkObjId="g_2e94410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f520c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="502,-74 502,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2edc5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="526,-43 526,-24 474,-24 474,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="47777@0" ObjectIDZND0="g_2e94410@0" Pin0InfoVect0LinkObjId="g_2e94410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307798_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="526,-43 526,-24 474,-24 474,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ed4ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="334,-34 388,-49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" ObjectIDND0="g_2ed48b0@1" ObjectIDZND0="47807@x" Pin0InfoVect0LinkObjId="g_2e94a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ed48b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="334,-34 388,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f392f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-160 388,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="47776@0" ObjectIDZND0="47773@x" ObjectIDZND1="g_2f04be0@0" ObjectIDZND2="47807@x" Pin0InfoVect0LinkObjId="SW-307794_0" Pin0InfoVect1LinkObjId="g_2f04be0_0" Pin0InfoVect2LinkObjId="g_2e94a00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307797_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="367,-160 388,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f39510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-160 388,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="47776@x" ObjectIDND1="g_2f04be0@0" ObjectIDND2="47807@x" ObjectIDZND0="47773@0" Pin0InfoVect0LinkObjId="SW-307794_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307797_0" Pin1InfoVect1LinkObjId="g_2f04be0_0" Pin1InfoVect2LinkObjId="g_2e94a00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,-160 388,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f39730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="366,-353 388,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="47774@0" ObjectIDZND0="47771@x" ObjectIDZND1="47772@x" Pin0InfoVect0LinkObjId="SW-307792_0" Pin0InfoVect1LinkObjId="SW-307793_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307795_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="366,-353 388,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f047a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-310 388,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47771@1" ObjectIDZND0="47774@x" ObjectIDZND1="47772@x" Pin0InfoVect0LinkObjId="SW-307795_0" Pin0InfoVect1LinkObjId="SW-307793_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="388,-310 388,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f049c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-353 388,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="47774@x" ObjectIDND1="47771@x" ObjectIDZND0="47772@0" Pin0InfoVect0LinkObjId="SW-307793_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307795_0" Pin1InfoVect1LinkObjId="SW-307792_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,-353 388,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f05790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="426,-140 388,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer" ObjectIDND0="g_2f04be0@0" ObjectIDZND0="47773@x" ObjectIDZND1="47776@x" ObjectIDZND2="47807@x" Pin0InfoVect0LinkObjId="SW-307794_0" Pin0InfoVect1LinkObjId="SW-307797_0" Pin0InfoVect2LinkObjId="g_2e94a00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f04be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="426,-140 388,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f059b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-114 388,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="47807@2" ObjectIDZND0="g_2f04be0@0" ObjectIDZND1="47773@x" ObjectIDZND2="47776@x" Pin0InfoVect0LinkObjId="g_2f04be0_0" Pin0InfoVect1LinkObjId="SW-307794_0" Pin0InfoVect2LinkObjId="SW-307797_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e94a00_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="388,-114 388,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f05bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,-140 388,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2f04be0@0" ObjectIDND1="47807@x" ObjectIDZND0="47773@x" ObjectIDZND1="47776@x" Pin0InfoVect0LinkObjId="SW-307794_0" Pin0InfoVect1LinkObjId="SW-307797_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f04be0_0" Pin1InfoVect1LinkObjId="g_2e94a00_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="388,-140 388,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fbcfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="337,-532 315,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47767@0" ObjectIDZND0="47764@x" ObjectIDZND1="47762@x" Pin0InfoVect0LinkObjId="SW-307771_0" Pin0InfoVect1LinkObjId="SW-307769_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="337,-532 315,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fbd200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-506 315,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47764@1" ObjectIDZND0="47767@x" ObjectIDZND1="47762@x" Pin0InfoVect0LinkObjId="SW-307774_0" Pin0InfoVect1LinkObjId="SW-307769_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307771_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="315,-506 315,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fbd420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-532 315,-556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="47764@x" ObjectIDND1="47767@x" ObjectIDZND0="47762@0" Pin0InfoVect0LinkObjId="SW-307769_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307771_0" Pin1InfoVect1LinkObjId="SW-307774_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,-532 315,-556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f9e400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="295,-607 315,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="47766@0" ObjectIDZND0="47762@x" ObjectIDZND1="47763@x" Pin0InfoVect0LinkObjId="SW-307769_0" Pin0InfoVect1LinkObjId="SW-307770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="295,-607 315,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f9e620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-583 315,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47762@1" ObjectIDZND0="47766@x" ObjectIDZND1="47763@x" Pin0InfoVect0LinkObjId="SW-307773_0" Pin0InfoVect1LinkObjId="SW-307770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307769_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="315,-583 315,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2f9e840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-607 315,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="47762@x" ObjectIDND1="47766@x" ObjectIDZND0="47763@1" Pin0InfoVect0LinkObjId="SW-307770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307769_0" Pin1InfoVect1LinkObjId="SW-307773_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,-607 315,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fa1160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="341,-692 315,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="47765@0" ObjectIDZND0="47763@x" ObjectIDZND1="g_2efc010@0" ObjectIDZND2="g_2fa15a0@0" Pin0InfoVect0LinkObjId="SW-307770_0" Pin0InfoVect1LinkObjId="g_2efc010_0" Pin0InfoVect2LinkObjId="g_2fa15a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="341,-692 315,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2fa1380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-661 315,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="47763@0" ObjectIDZND0="47765@x" ObjectIDZND1="g_2efc010@0" ObjectIDZND2="g_2fa15a0@0" Pin0InfoVect0LinkObjId="SW-307772_0" Pin0InfoVect1LinkObjId="g_2efc010_0" Pin0InfoVect2LinkObjId="g_2fa15a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="315,-661 315,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2efb350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-787 315,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2fa15a0@0" ObjectIDZND0="47763@x" ObjectIDZND1="47765@x" ObjectIDZND2="g_2efc010@0" Pin0InfoVect0LinkObjId="SW-307770_0" Pin0InfoVect1LinkObjId="SW-307772_0" Pin0InfoVect2LinkObjId="g_2efc010_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fa15a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="336,-787 315,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2efb570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-787 315,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2fa15a0@0" ObjectIDND1="47763@x" ObjectIDND2="47765@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2fa15a0_0" Pin1InfoVect1LinkObjId="SW-307770_0" Pin1InfoVect2LinkObjId="SW-307772_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="315,-787 315,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2efb790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="279,-737 315,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2efc010@0" ObjectIDZND0="47763@x" ObjectIDZND1="47765@x" ObjectIDZND2="g_2fa15a0@0" Pin0InfoVect0LinkObjId="SW-307770_0" Pin0InfoVect1LinkObjId="SW-307772_0" Pin0InfoVect2LinkObjId="g_2fa15a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2efc010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="279,-737 315,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2efb9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-692 315,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="47763@x" ObjectIDND1="47765@x" ObjectIDZND0="g_2efc010@0" ObjectIDZND1="g_2fa15a0@0" Pin0InfoVect0LinkObjId="g_2efc010_0" Pin0InfoVect1LinkObjId="g_2fa15a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307770_0" Pin1InfoVect1LinkObjId="SW-307772_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="315,-692 315,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2efbbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-737 315,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="47763@x" ObjectIDND1="47765@x" ObjectIDND2="g_2efc010@0" ObjectIDZND0="g_2fa15a0@0" Pin0InfoVect0LinkObjId="g_2fa15a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307770_0" Pin1InfoVect1LinkObjId="SW-307772_0" Pin1InfoVect2LinkObjId="g_2efc010_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,-737 315,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2efbdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-470 315,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47764@0" ObjectIDZND0="47753@0" Pin0InfoVect0LinkObjId="g_2fadd70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307771_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,-470 315,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f39b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,145 388,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47778@1" ObjectIDZND0="47780@1" Pin0InfoVect0LinkObjId="SW-307800_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307799_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,145 388,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f3a1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,201 388,172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47779@1" ObjectIDZND0="47778@0" Pin0InfoVect0LinkObjId="SW-307799_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307800_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,201 388,172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f3ac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,307 20,287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47796@1" ObjectIDZND0="47797@1" Pin0InfoVect0LinkObjId="SW-307824_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307823_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,307 20,287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f3b260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,363 20,334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47798@1" ObjectIDZND0="47796@0" Pin0InfoVect0LinkObjId="SW-307823_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307824_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,363 20,334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f763a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="56,440 20,440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47799@0" ObjectIDZND0="47798@x" ObjectIDZND1="g_2f770f0@0" Pin0InfoVect0LinkObjId="SW-307824_0" Pin0InfoVect1LinkObjId="g_2f770f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307825_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="56,440 20,440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f76e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,440 20,380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="47799@x" ObjectIDND1="g_2f770f0@0" ObjectIDZND0="47798@0" Pin0InfoVect0LinkObjId="SW-307824_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307825_0" Pin1InfoVect1LinkObjId="g_2f770f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,440 20,380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f77d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,539 20,500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2f770f0@0" Pin0InfoVect0LinkObjId="g_2f770f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,539 20,500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f77f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,461 20,440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2f770f0@1" ObjectIDZND0="47799@x" ObjectIDZND1="47798@x" Pin0InfoVect0LinkObjId="SW-307825_0" Pin0InfoVect1LinkObjId="SW-307824_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f770f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="20,461 20,440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e829c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,634 20,678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,634 20,678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ec9f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,751 20,714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,751 20,714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f327f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,313 228,293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47800@1" ObjectIDZND0="47801@1" Pin0InfoVect0LinkObjId="SW-307829_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307828_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="228,313 228,293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f2cd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,369 228,340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47802@1" ObjectIDZND0="47800@0" Pin0InfoVect0LinkObjId="SW-307828_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307829_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="228,369 228,340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f2fd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="264,446 228,446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47803@0" ObjectIDZND0="47802@x" ObjectIDZND1="g_2f8d6b0@0" Pin0InfoVect0LinkObjId="SW-307829_0" Pin0InfoVect1LinkObjId="g_2f8d6b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="264,446 228,446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f2ffd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,446 228,386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="47803@x" ObjectIDND1="g_2f8d6b0@0" ObjectIDZND0="47802@0" Pin0InfoVect0LinkObjId="SW-307829_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307830_0" Pin1InfoVect1LinkObjId="g_2f8d6b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="228,446 228,386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8e180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,467 228,446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2f8d6b0@1" ObjectIDZND0="47802@x" ObjectIDZND1="47803@x" Pin0InfoVect0LinkObjId="SW-307829_0" Pin0InfoVect1LinkObjId="SW-307830_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8d6b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="228,467 228,446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e8fc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="699,317 699,297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47781@1" ObjectIDZND0="47782@1" Pin0InfoVect0LinkObjId="SW-307804_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307803_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="699,317 699,297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e92f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="699,373 699,344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47926@1" ObjectIDZND0="47781@0" Pin0InfoVect0LinkObjId="SW-307803_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307804_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="699,373 699,344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f0a2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="735,450 699,450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="47783@0" ObjectIDZND0="47926@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307804_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307805_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="735,450 699,450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f0a510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="699,390 699,450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="47926@0" ObjectIDZND0="47783@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307805_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="699,390 699,450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f0a770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="699,450 699,531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="47926@x" ObjectIDND1="47783@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307804_0" Pin1InfoVect1LinkObjId="SW-307805_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="699,450 699,531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8b8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="859,323 859,303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47784@1" ObjectIDZND0="47785@1" Pin0InfoVect0LinkObjId="SW-307809_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307808_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="859,323 859,303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e47380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="859,379 859,350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47786@1" ObjectIDZND0="47784@0" Pin0InfoVect0LinkObjId="SW-307808_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307809_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="859,379 859,350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e4af90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="895,456 859,456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="47787@0" ObjectIDZND0="47786@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307809_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="895,456 859,456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e4b1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="859,396 859,456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="47786@0" ObjectIDZND0="47787@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307810_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307809_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="859,396 859,456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e4b450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="859,456 859,537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="47786@x" ObjectIDND1="47787@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307809_0" Pin1InfoVect1LinkObjId="SW-307810_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="859,456 859,537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea37b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,326 992,306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47788@1" ObjectIDZND0="47789@1" Pin0InfoVect0LinkObjId="SW-307814_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307813_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,326 992,306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eece10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,382 992,353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47790@1" ObjectIDZND0="47788@0" Pin0InfoVect0LinkObjId="SW-307813_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307814_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,382 992,353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef0a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1028,459 992,459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="47791@0" ObjectIDZND0="47790@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307814_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307815_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1028,459 992,459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f407a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,399 992,459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="47790@0" ObjectIDZND0="47791@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307815_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307814_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="992,399 992,459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f40a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,459 992,540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="47790@x" ObjectIDND1="47791@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307814_0" Pin1InfoVect1LinkObjId="SW-307815_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,459 992,540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f462b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,324 1140,304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47792@1" ObjectIDZND0="47793@1" Pin0InfoVect0LinkObjId="SW-307819_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307818_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1140,324 1140,304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ecfc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,380 1140,351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47794@1" ObjectIDZND0="47792@0" Pin0InfoVect0LinkObjId="SW-307818_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307819_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1140,380 1140,351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ed3870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,457 1140,457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="47795@0" ObjectIDZND0="47794@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307819_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1176,457 1140,457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ed3ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,397 1140,457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="47794@0" ObjectIDZND0="47795@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307820_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307819_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1140,397 1140,457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ed3d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,457 1140,538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="47794@x" ObjectIDND1="47795@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307819_0" Pin1InfoVect1LinkObjId="SW-307820_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1140,457 1140,538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e9a170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,373 554,428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="47928@0" ObjectIDZND0="g_2e97600@0" Pin0InfoVect0LinkObjId="g_2e97600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308201_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,373 554,428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e746c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,530 181,530 181,551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="47804@x" ObjectIDND1="g_2f8d6b0@0" ObjectIDZND0="47806@0" Pin0InfoVect0LinkObjId="SW-307833_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307831_0" Pin1InfoVect1LinkObjId="g_2f8d6b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="228,530 181,530 181,551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e751b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,552 228,530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="47804@1" ObjectIDZND0="47806@x" ObjectIDZND1="g_2f8d6b0@0" Pin0InfoVect0LinkObjId="SW-307833_0" Pin0InfoVect1LinkObjId="g_2f8d6b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307831_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="228,552 228,530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e75410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,530 228,506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="47806@x" ObjectIDND1="47804@x" ObjectIDZND0="g_2f8d6b0@0" Pin0InfoVect0LinkObjId="g_2f8d6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307833_0" Pin1InfoVect1LinkObjId="SW-307831_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="228,530 228,506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e79920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,803 228,857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="228,803 228,857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e7b1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,270 20,252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47797@0" ObjectIDZND0="47754@0" Pin0InfoVect0LinkObjId="g_2e7b8c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307824_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,270 20,252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e7b8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,276 228,252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47801@0" ObjectIDZND0="47754@0" Pin0InfoVect0LinkObjId="g_2e7b1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="228,276 228,252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e7c060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,218 388,252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47779@0" ObjectIDZND0="47754@0" Pin0InfoVect0LinkObjId="g_2e7b1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,218 388,252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e7c890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,304 554,252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47927@0" ObjectIDZND0="47754@0" Pin0InfoVect0LinkObjId="g_2e7b1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308201_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,304 554,252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea4850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="699,280 699,252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47782@0" ObjectIDZND0="47754@0" Pin0InfoVect0LinkObjId="g_2e7b1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="699,280 699,252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea5050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="859,286 859,252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47785@0" ObjectIDZND0="47754@0" Pin0InfoVect0LinkObjId="g_2e7b1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307809_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="859,286 859,252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea5880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="992,289 992,252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47789@0" ObjectIDZND0="47754@0" Pin0InfoVect0LinkObjId="g_2e7b1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307814_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="992,289 992,252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea60b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,287 1140,252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47793@0" ObjectIDZND0="47754@0" Pin0InfoVect0LinkObjId="g_2e7b1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307819_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1140,287 1140,252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eadd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="334,6 334,64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2ed48b0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ed48b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="334,6 334,64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eadf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="333,110 333,92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_2ed5170@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ed5170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="333,110 333,92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e1ffe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="323,-67 291,-67 291,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="earth" ObjectIDND0="47807@0" ObjectIDZND0="g_2e1f810@0" Pin0InfoVect0LinkObjId="g_2e1f810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e94a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="323,-67 291,-67 291,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e20210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="180,692 180,716 228,716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="reactance" ObjectIDND0="g_2e75910@1" ObjectIDZND0="47805@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-307832_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e75910_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="180,692 180,716 228,716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e20d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,693 228,716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="reactance" ObjectIDND0="47805@0" ObjectIDZND0="g_2e75910@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2e75910_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307832_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="228,693 228,716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e20f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,716 228,761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="reactance" ObjectIDND0="g_2e75910@0" ObjectIDND1="47805@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e75910_0" Pin1InfoVect1LinkObjId="SW-307832_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="228,716 228,761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e211c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="180,658 180,638 228,638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2e75910@0" ObjectIDZND0="47804@x" ObjectIDZND1="47805@x" Pin0InfoVect0LinkObjId="SW-307831_0" Pin0InfoVect1LinkObjId="SW-307832_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e75910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="180,658 180,638 228,638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e21cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,588 228,638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47804@0" ObjectIDZND0="g_2e75910@0" ObjectIDZND1="47805@x" Pin0InfoVect0LinkObjId="g_2e75910_0" Pin0InfoVect1LinkObjId="SW-307832_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="228,588 228,638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e61940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,638 228,657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2e75910@0" ObjectIDND1="47804@x" ObjectIDZND0="47805@1" Pin0InfoVect0LinkObjId="SW-307832_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e75910_0" Pin1InfoVect1LinkObjId="SW-307831_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="228,638 228,657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e67960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,321 554,356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="47927@1" ObjectIDZND0="47928@1" Pin0InfoVect0LinkObjId="SW-308201_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308201_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,321 554,356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e681f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="421,65 388,65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="g_2f56570@0" ObjectIDZND0="47780@x" ObjectIDZND1="47807@x" Pin0InfoVect0LinkObjId="SW-307800_0" Pin0InfoVect1LinkObjId="g_2e94a00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f56570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="421,65 388,65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e68b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,108 388,65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="47780@0" ObjectIDZND0="g_2f56570@0" ObjectIDZND1="47807@x" Pin0InfoVect0LinkObjId="g_2f56570_0" Pin0InfoVect1LinkObjId="g_2e94a00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="388,108 388,65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e68d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="388,65 388,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="g_2f56570@0" ObjectIDND1="47780@x" ObjectIDZND0="47807@1" Pin0InfoVect0LinkObjId="g_2e94a00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f56570_0" Pin1InfoVect1LinkObjId="SW-307800_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="388,65 388,-30 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="47753" cx="797" cy="-448" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47753" cx="1023" cy="-448" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47753" cx="315" cy="-448" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47753" cx="388" cy="-448" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47754" cx="20" cy="252" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47754" cx="228" cy="252" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47754" cx="388" cy="252" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47754" cx="554" cy="252" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47754" cx="699" cy="252" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47754" cx="859" cy="252" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47754" cx="992" cy="252" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47754" cx="1140" cy="252" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-0" type="0">
    <use transform="matrix(0.000000 -0.848485 -0.736842 -0.000000 341.000000 92.000000)" xlink:href="#dynamicPoint:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-307584" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -512.000000 -518.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47741" ObjectName="DYN-CX_STGF"/>
     <cge:Meas_Ref ObjectId="307584"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3007f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -7.000000) translate(0,374)">联系方式：  0878 7721698</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d22590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -445.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d22590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -445.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d22590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -445.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d22590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -445.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d22590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -445.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d22590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -445.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d22590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -845.000000 -445.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2fe8240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -715.000000 -586.500000) translate(0,16)">石头光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f52850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 486.000000 -258.000000) translate(0,12)">#1号主变:110MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f55f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 260.000000 -868.000000) translate(0,12)">220kV英石线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e97440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 985.000000 -865.000000) translate(0,12)">220kV石水线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e79b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 933.000000) translate(0,12)">#1动态无功SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea68e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -558.000000) translate(0,12)">29217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea7330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.000000 -495.000000) translate(0,12)">2921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea7790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 324.000000 -577.000000) translate(0,12)">292</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea79d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 252.000000 -599.000000) translate(0,12)">29260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea7ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 323.000000 -652.000000) translate(0,12)">2926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea8170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -718.000000) translate(0,12)">29267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea83b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 827.000000 -516.000000) translate(0,12)">29010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea85f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 804.000000 -543.000000) translate(0,12)">2901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea8830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 821.000000 -625.000000) translate(0,12)">29017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea8a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 -493.000000) translate(0,12)">2911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea8cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1064.000000 -519.000000) translate(0,12)">29117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea8ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1037.000000 -579.000000) translate(0,12)">291</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea9130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -631.000000) translate(0,12)">29160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea9370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 -648.000000) translate(0,12)">2916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea95b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1052.000000 -716.000000) translate(0,12)">29167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea97f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 397.000000 -304.000000) translate(0,12)">201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea9a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 395.000000 -413.000000) translate(0,12)">2011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea9c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 323.000000 -379.000000) translate(0,12)">20117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ea9eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 328.000000 -281.000000) translate(0,12)">20160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaa0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 324.000000 -186.000000) translate(0,12)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaa330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 395.000000 -217.000000) translate(0,12)">2016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaa570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 533.000000 -68.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaa7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 29.000000 313.000000) translate(0,12)">369</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaacd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 58.000000 414.000000) translate(0,12)">36967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaaf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 319.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eab190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 266.000000 418.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eab3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 235.000000 562.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eab610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 127.000000 562.000000) translate(0,12)">37117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eab850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 235.000000 667.000000) translate(0,12)">3712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eaba90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 708.000000 323.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eabcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 738.000000 424.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eabf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 868.000000 329.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eac150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 897.000000 430.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eac390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 332.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eac5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 433.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eac810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1149.000000 330.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eacb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1179.000000 431.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eacfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 148.000000 -440.000000) translate(0,12)">220kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ead6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -61.000000 264.000000) translate(0,12)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eadb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 397.000000 151.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e13cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -34.000000 788.000000) translate(0,12)">#1站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e14580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 473.000000) translate(0,12)">35kVⅠ段压变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e15120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 670.000000 562.000000) translate(0,12)">集电线路1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e15cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 564.000000) translate(0,12)">集电线路2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e15f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 971.000000 564.000000) translate(0,12)">集电线路3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e16180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1114.000000 561.000000) translate(0,12)">集电线路4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1b6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 546.000000 85.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e1bf40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 273.000000 -116.000000) translate(0,12)">#1主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e67bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 571.000000 333.000000) translate(0,12)">3901</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="337" x2="337" y1="63" y2="63"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="224" x2="224" y1="572" y2="572"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="227" x2="180" y1="577" y2="577"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="274" x2="274" y1="858" y2="911"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="176" x2="176" y1="858" y2="911"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="176" x2="274" y1="911" y2="911"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="212" x2="255" y1="867" y2="867"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="255" x2="255" y1="867" y2="902"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="255" x2="213" y1="902" y2="902"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="258" x2="252" y1="879" y2="879"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="212" x2="212" y1="907" y2="896"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="212" x2="212" y1="862" y2="873"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="199" x2="199" y1="876" y2="895"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="192" x2="192" y1="881" y2="892"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="192" x2="184" y1="886" y2="886"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="212" x2="199" y1="896" y2="888"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="212" x2="199" y1="873" y2="880"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="176" x2="175" y1="885" y2="885"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="299" x2="274" y1="907" y2="907"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="274" x2="299" y1="863" y2="863"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="299" x2="299" y1="863" y2="878"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="299" x2="299" y1="891" y2="907"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="285" x2="323" y1="891" y2="891"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="176" x2="274" y1="858" y2="858"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-307787">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 788.448571 -513.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47768" ObjectName="SW-CX_STGF.CX_STGF_2901SW"/>
     <cge:Meas_Ref ObjectId="307787"/>
    <cge:TPSR_Ref TObjectID="47768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307789">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 813.448571 -592.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47770" ObjectName="SW-CX_STGF.CX_STGF_29017SW"/>
     <cge:Meas_Ref ObjectId="307789"/>
    <cge:TPSR_Ref TObjectID="47770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307788">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 819.448571 -483.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47769" ObjectName="SW-CX_STGF.CX_STGF_29010SW"/>
     <cge:Meas_Ref ObjectId="307788"/>
    <cge:TPSR_Ref TObjectID="47769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307793">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.860000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47772" ObjectName="SW-CX_STGF.CX_STGF_2011SW"/>
     <cge:Meas_Ref ObjectId="307793"/>
    <cge:TPSR_Ref TObjectID="47772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307794">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.860000 -187.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47773" ObjectName="SW-CX_STGF.CX_STGF_2016SW"/>
     <cge:Meas_Ref ObjectId="307794"/>
    <cge:TPSR_Ref TObjectID="47773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307796">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 319.860000 -248.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47775" ObjectName="SW-CX_STGF.CX_STGF_20160SW"/>
     <cge:Meas_Ref ObjectId="307796"/>
    <cge:TPSR_Ref TObjectID="47775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307752">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1013.942857 -618.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47757" ObjectName="SW-CX_STGF.CX_STGF_2916SW"/>
     <cge:Meas_Ref ObjectId="307752"/>
    <cge:TPSR_Ref TObjectID="47757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307753">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1013.942857 -463.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47758" ObjectName="SW-CX_STGF.CX_STGF_2911SW"/>
     <cge:Meas_Ref ObjectId="307753"/>
    <cge:TPSR_Ref TObjectID="47758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307756">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1039.942857 -523.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47761" ObjectName="SW-CX_STGF.CX_STGF_29117SW"/>
     <cge:Meas_Ref ObjectId="307756"/>
    <cge:TPSR_Ref TObjectID="47761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307755">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.942857 -598.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47760" ObjectName="SW-CX_STGF.CX_STGF_29160SW"/>
     <cge:Meas_Ref ObjectId="307755"/>
    <cge:TPSR_Ref TObjectID="47760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307754">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.942857 -683.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47759" ObjectName="SW-CX_STGF.CX_STGF_29167SW"/>
     <cge:Meas_Ref ObjectId="307754"/>
    <cge:TPSR_Ref TObjectID="47759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307798">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 517.098039 -38.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47777" ObjectName="SW-CX_STGF.CX_STGF_2010SW"/>
     <cge:Meas_Ref ObjectId="307798"/>
    <cge:TPSR_Ref TObjectID="47777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307795">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.860000 -346.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47774" ObjectName="SW-CX_STGF.CX_STGF_20117SW"/>
     <cge:Meas_Ref ObjectId="307795"/>
    <cge:TPSR_Ref TObjectID="47774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307797">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 315.860000 -153.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47776" ObjectName="SW-CX_STGF.CX_STGF_20167SW"/>
     <cge:Meas_Ref ObjectId="307797"/>
    <cge:TPSR_Ref TObjectID="47776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307770">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.942857 -620.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47763" ObjectName="SW-CX_STGF.CX_STGF_2926SW"/>
     <cge:Meas_Ref ObjectId="307770"/>
    <cge:TPSR_Ref TObjectID="47763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307771">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.942857 -465.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47764" ObjectName="SW-CX_STGF.CX_STGF_2921SW"/>
     <cge:Meas_Ref ObjectId="307771"/>
    <cge:TPSR_Ref TObjectID="47764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307774">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 331.942857 -525.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47767" ObjectName="SW-CX_STGF.CX_STGF_29217SW"/>
     <cge:Meas_Ref ObjectId="307774"/>
    <cge:TPSR_Ref TObjectID="47767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307773">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 243.942857 -600.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47766" ObjectName="SW-CX_STGF.CX_STGF_29260SW"/>
     <cge:Meas_Ref ObjectId="307773"/>
    <cge:TPSR_Ref TObjectID="47766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307772">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.942857 -685.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47765" ObjectName="SW-CX_STGF.CX_STGF_29267SW"/>
     <cge:Meas_Ref ObjectId="307772"/>
    <cge:TPSR_Ref TObjectID="47765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307800">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.000000 132.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47780" ObjectName="SW-CX_STGF.CX_STGF_301XC1"/>
     <cge:Meas_Ref ObjectId="307800"/>
    <cge:TPSR_Ref TObjectID="47780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307800">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.000000 225.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47779" ObjectName="SW-CX_STGF.CX_STGF_301XC"/>
     <cge:Meas_Ref ObjectId="307800"/>
    <cge:TPSR_Ref TObjectID="47779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307824">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 10.000000 294.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47797" ObjectName="SW-CX_STGF.CX_STGF_369XC"/>
     <cge:Meas_Ref ObjectId="307824"/>
    <cge:TPSR_Ref TObjectID="47797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307824">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 10.000000 387.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47798" ObjectName="SW-CX_STGF.CX_STGF_369XC1"/>
     <cge:Meas_Ref ObjectId="307824"/>
    <cge:TPSR_Ref TObjectID="47798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307825">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 51.000000 447.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47799" ObjectName="SW-CX_STGF.CX_STGF_36967SW"/>
     <cge:Meas_Ref ObjectId="307825"/>
    <cge:TPSR_Ref TObjectID="47799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 719.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307829">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 218.000000 300.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47801" ObjectName="SW-CX_STGF.CX_STGF_371XC"/>
     <cge:Meas_Ref ObjectId="307829"/>
    <cge:TPSR_Ref TObjectID="47801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307829">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 218.000000 393.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47802" ObjectName="SW-CX_STGF.CX_STGF_371XC1"/>
     <cge:Meas_Ref ObjectId="307829"/>
    <cge:TPSR_Ref TObjectID="47802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307830">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 259.000000 453.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47803" ObjectName="SW-CX_STGF.CX_STGF_37167SW"/>
     <cge:Meas_Ref ObjectId="307830"/>
    <cge:TPSR_Ref TObjectID="47803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307804">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 689.000000 304.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47782" ObjectName="SW-CX_STGF.CX_STGF_361XC"/>
     <cge:Meas_Ref ObjectId="307804"/>
    <cge:TPSR_Ref TObjectID="47782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307804">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 689.000000 397.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47926" ObjectName="SW-CX_STGF.CX_STGF_361XC1"/>
     <cge:Meas_Ref ObjectId="307804"/>
    <cge:TPSR_Ref TObjectID="47926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307805">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 730.000000 457.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47783" ObjectName="SW-CX_STGF.CX_STGF_36167SW"/>
     <cge:Meas_Ref ObjectId="307805"/>
    <cge:TPSR_Ref TObjectID="47783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307809">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 849.000000 310.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47785" ObjectName="SW-CX_STGF.CX_STGF_362XC"/>
     <cge:Meas_Ref ObjectId="307809"/>
    <cge:TPSR_Ref TObjectID="47785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307809">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 849.000000 403.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47786" ObjectName="SW-CX_STGF.CX_STGF_362XC1"/>
     <cge:Meas_Ref ObjectId="307809"/>
    <cge:TPSR_Ref TObjectID="47786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307810">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 890.000000 463.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47787" ObjectName="SW-CX_STGF.CX_STGF_36267SW"/>
     <cge:Meas_Ref ObjectId="307810"/>
    <cge:TPSR_Ref TObjectID="47787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307814">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 982.000000 313.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47789" ObjectName="SW-CX_STGF.CX_STGF_363XC"/>
     <cge:Meas_Ref ObjectId="307814"/>
    <cge:TPSR_Ref TObjectID="47789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307814">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 982.000000 406.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47790" ObjectName="SW-CX_STGF.CX_STGF_363XC1"/>
     <cge:Meas_Ref ObjectId="307814"/>
    <cge:TPSR_Ref TObjectID="47790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307815">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1023.000000 466.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47791" ObjectName="SW-CX_STGF.CX_STGF_36367SW"/>
     <cge:Meas_Ref ObjectId="307815"/>
    <cge:TPSR_Ref TObjectID="47791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307819">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 311.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47793" ObjectName="SW-CX_STGF.CX_STGF_364XC"/>
     <cge:Meas_Ref ObjectId="307819"/>
    <cge:TPSR_Ref TObjectID="47793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307819">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 404.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47794" ObjectName="SW-CX_STGF.CX_STGF_364XC1"/>
     <cge:Meas_Ref ObjectId="307819"/>
    <cge:TPSR_Ref TObjectID="47794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307820">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1171.000000 464.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47795" ObjectName="SW-CX_STGF.CX_STGF_36467SW"/>
     <cge:Meas_Ref ObjectId="307820"/>
    <cge:TPSR_Ref TObjectID="47795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307831">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 219.000000 593.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47804" ObjectName="SW-CX_STGF.CX_STGF_3711SW"/>
     <cge:Meas_Ref ObjectId="307831"/>
    <cge:TPSR_Ref TObjectID="47804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307832">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 219.000000 698.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47805" ObjectName="SW-CX_STGF.CX_STGF_3712SW"/>
     <cge:Meas_Ref ObjectId="307832"/>
    <cge:TPSR_Ref TObjectID="47805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307833">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 172.000000 602.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47806" ObjectName="SW-CX_STGF.CX_STGF_37117SW"/>
     <cge:Meas_Ref ObjectId="307833"/>
    <cge:TPSR_Ref TObjectID="47806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308201">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.000000 328.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47927" ObjectName="SW-CX_STGF.CX_STGF_3901XC"/>
     <cge:Meas_Ref ObjectId="308201"/>
    <cge:TPSR_Ref TObjectID="47927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308201">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.000000 380.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47928" ObjectName="SW-CX_STGF.CX_STGF_3901XC1"/>
     <cge:Meas_Ref ObjectId="308201"/>
    <cge:TPSR_Ref TObjectID="47928"/></metadata>
   </g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 241.000000 808.000000)" xlink:href="#reactance:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f520c0">
    <use class="BV-220KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 508.000000 -27.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f1b8c0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1039.942857 -777.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e94410">
    <use class="BV-220KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 469.000000 -31.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ed48b0">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 339.000000 11.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f04be0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 421.942857 -132.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fa15a0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 331.942857 -779.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f56570">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 416.942857 74.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f770f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 10.000000 505.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f8d6b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 218.000000 511.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e75910">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 173.000000 697.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-307657" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 8.000000 149.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47754"/>
     <cge:Term_Ref ObjectID="45473"/>
    <cge:TPSR_Ref TObjectID="47754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-307658" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 8.000000 149.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47754"/>
     <cge:Term_Ref ObjectID="45473"/>
    <cge:TPSR_Ref TObjectID="47754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-307659" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 8.000000 149.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47754"/>
     <cge:Term_Ref ObjectID="45473"/>
    <cge:TPSR_Ref TObjectID="47754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-307663" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 8.000000 149.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307663" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47754"/>
     <cge:Term_Ref ObjectID="45473"/>
    <cge:TPSR_Ref TObjectID="47754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-307660" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 8.000000 149.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307660" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47754"/>
     <cge:Term_Ref ObjectID="45473"/>
    <cge:TPSR_Ref TObjectID="47754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-307664" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 8.000000 149.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47754"/>
     <cge:Term_Ref ObjectID="45473"/>
    <cge:TPSR_Ref TObjectID="47754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-307621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -542.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47753"/>
     <cge:Term_Ref ObjectID="45472"/>
    <cge:TPSR_Ref TObjectID="47753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-307622" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -542.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307622" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47753"/>
     <cge:Term_Ref ObjectID="45472"/>
    <cge:TPSR_Ref TObjectID="47753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-307623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -542.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47753"/>
     <cge:Term_Ref ObjectID="45472"/>
    <cge:TPSR_Ref TObjectID="47753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-307627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -542.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47753"/>
     <cge:Term_Ref ObjectID="45472"/>
    <cge:TPSR_Ref TObjectID="47753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-307624" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -542.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47753"/>
     <cge:Term_Ref ObjectID="45472"/>
    <cge:TPSR_Ref TObjectID="47753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-307628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 140.000000 -542.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47753"/>
     <cge:Term_Ref ObjectID="45472"/>
    <cge:TPSR_Ref TObjectID="47753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307722" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -53.000000 829.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307722" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47796"/>
     <cge:Term_Ref ObjectID="45556"/>
    <cge:TPSR_Ref TObjectID="47796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307723" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -53.000000 829.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47796"/>
     <cge:Term_Ref ObjectID="45556"/>
    <cge:TPSR_Ref TObjectID="47796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307719" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -53.000000 829.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307719" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47796"/>
     <cge:Term_Ref ObjectID="45556"/>
    <cge:TPSR_Ref TObjectID="47796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307734" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 803.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307734" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47800"/>
     <cge:Term_Ref ObjectID="45576"/>
    <cge:TPSR_Ref TObjectID="47800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307735" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 803.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307735" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47800"/>
     <cge:Term_Ref ObjectID="45576"/>
    <cge:TPSR_Ref TObjectID="47800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307731" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 803.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307731" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47800"/>
     <cge:Term_Ref ObjectID="45576"/>
    <cge:TPSR_Ref TObjectID="47800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307674" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 584.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307674" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47781"/>
     <cge:Term_Ref ObjectID="45526"/>
    <cge:TPSR_Ref TObjectID="47781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307675" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 584.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307675" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47781"/>
     <cge:Term_Ref ObjectID="45526"/>
    <cge:TPSR_Ref TObjectID="47781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307671" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 584.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47781"/>
     <cge:Term_Ref ObjectID="45526"/>
    <cge:TPSR_Ref TObjectID="47781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307686" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 847.000000 585.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47784"/>
     <cge:Term_Ref ObjectID="45532"/>
    <cge:TPSR_Ref TObjectID="47784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307687" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 847.000000 585.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47784"/>
     <cge:Term_Ref ObjectID="45532"/>
    <cge:TPSR_Ref TObjectID="47784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307683" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 847.000000 585.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307683" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47784"/>
     <cge:Term_Ref ObjectID="45532"/>
    <cge:TPSR_Ref TObjectID="47784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307698" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 986.000000 582.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307698" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47788"/>
     <cge:Term_Ref ObjectID="45540"/>
    <cge:TPSR_Ref TObjectID="47788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307699" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 986.000000 582.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307699" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47788"/>
     <cge:Term_Ref ObjectID="45540"/>
    <cge:TPSR_Ref TObjectID="47788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307695" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 986.000000 582.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47788"/>
     <cge:Term_Ref ObjectID="45540"/>
    <cge:TPSR_Ref TObjectID="47788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307710" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1135.000000 584.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47792"/>
     <cge:Term_Ref ObjectID="45548"/>
    <cge:TPSR_Ref TObjectID="47792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307711" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1135.000000 584.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307711" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47792"/>
     <cge:Term_Ref ObjectID="45548"/>
    <cge:TPSR_Ref TObjectID="47792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307707" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1135.000000 584.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307707" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47792"/>
     <cge:Term_Ref ObjectID="45548"/>
    <cge:TPSR_Ref TObjectID="47792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307650" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 143.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307650" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47778"/>
     <cge:Term_Ref ObjectID="45520"/>
    <cge:TPSR_Ref TObjectID="47778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307651" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 143.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307651" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47778"/>
     <cge:Term_Ref ObjectID="45520"/>
    <cge:TPSR_Ref TObjectID="47778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307647" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 143.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307647" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47778"/>
     <cge:Term_Ref ObjectID="45520"/>
    <cge:TPSR_Ref TObjectID="47778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 530.000000 -332.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47771"/>
     <cge:Term_Ref ObjectID="45506"/>
    <cge:TPSR_Ref TObjectID="47771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 530.000000 -332.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47771"/>
     <cge:Term_Ref ObjectID="45506"/>
    <cge:TPSR_Ref TObjectID="47771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 530.000000 -332.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47771"/>
     <cge:Term_Ref ObjectID="45506"/>
    <cge:TPSR_Ref TObjectID="47771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307618" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 473.000000 -616.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47762"/>
     <cge:Term_Ref ObjectID="45488"/>
    <cge:TPSR_Ref TObjectID="47762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307619" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 473.000000 -616.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307619" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47762"/>
     <cge:Term_Ref ObjectID="45488"/>
    <cge:TPSR_Ref TObjectID="47762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307615" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 473.000000 -616.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47762"/>
     <cge:Term_Ref ObjectID="45488"/>
    <cge:TPSR_Ref TObjectID="47762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307606" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1170.000000 -610.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47756"/>
     <cge:Term_Ref ObjectID="45476"/>
    <cge:TPSR_Ref TObjectID="47756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307607" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1170.000000 -610.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307607" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47756"/>
     <cge:Term_Ref ObjectID="45476"/>
    <cge:TPSR_Ref TObjectID="47756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1170.000000 -610.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47756"/>
     <cge:Term_Ref ObjectID="45476"/>
    <cge:TPSR_Ref TObjectID="47756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-307653" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 87.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307653" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47807"/>
     <cge:Term_Ref ObjectID="45592"/>
    <cge:TPSR_Ref TObjectID="47807"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="175" x="-742" y="-597"/></g>
   <g href="cx_索引_接线图_省地共调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-791" y="-614"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="212" cy="902" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="212" cy="868" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f73dc0">
    <use class="BV-220KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 832.448571 -651.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e13910">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 935.000000 -690.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2efc010">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 227.000000 -692.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e97600">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 518.551429 423.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -754.000000 -538.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(60,120,255)" stroke-width="1" width="39" x="284" y="878"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="175" x="-742" y="-597"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="175" x="-742" y="-597"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-791" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-791" y="-614"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 639.000000)" xlink:href="#transformer2:shape74_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 639.000000)" xlink:href="#transformer2:shape74_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_STGF"/>
</svg>