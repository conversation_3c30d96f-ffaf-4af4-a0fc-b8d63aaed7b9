<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-287" aopId="1835526" id="thSvg" product="E8000V2" version="1.0" viewBox="16 -1169 2471 1388">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape45">
    <polyline arcFlag="1" points="19,100 17,100 15,99 14,99 12,98 11,97 9,96 8,94 7,92 7,91 6,89 6,87 6,85 7,83 7,82 8,80 9,79 11,77 12,76 14,75 15,75 17,74 19,74 21,74 23,75 24,75 26,76 27,77 29,79 30,80 31,82 31,83 32,85 32,87 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="32" x2="19" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="100" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="14" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="55" y2="47"/>
    <polyline arcFlag="1" points="36,14 37,14 38,14 38,14 39,14 39,15 40,15 40,16 41,16 41,17 41,17 41,18 42,19 42,19 42,20 41,21 41,21 41,22 41,22 40,23 40,23 39,24 39,24 38,24 38,25 37,25 36,25 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,36 37,36 38,36 38,37 39,37 39,37 40,38 40,38 41,39 41,39 41,40 41,40 42,41 42,42 42,42 41,43 41,44 41,44 41,45 40,45 40,46 39,46 39,47 38,47 38,47 37,47 36,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="19" x2="19" y1="87" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="19" x2="19" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="10" x2="26" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="21" y2="21"/>
    <polyline arcFlag="1" points="36,25 37,25 38,25 38,25 39,26 39,26 40,26 40,27 41,27 41,28 41,29 41,29 42,30 42,31 42,31 41,32 41,33 41,33 41,34 40,34 40,35 39,35 39,35 38,36 38,36 37,36 36,36 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape175">
    <polyline DF8003:Layer="PUBLIC" points="6,4 0,16 12,16 6,4 6,5 6,4 "/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="voltageTransformer:shape135">
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="6" y2="52"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
   </symbol>
   <symbol id="voltageTransformer:shape144">
    <circle cx="17" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="0" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="3" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="2" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="5" x2="10" y1="37" y2="37"/>
    <polyline points="5,7 5,37 " stroke-width="1"/>
    <circle cx="17" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="39" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="15" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="30" x2="30" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="34" x2="30" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="34" x2="30" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="27" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="33" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="41" x2="38" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="30" x2="27" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="39" y2="36"/>
    <circle cx="30" cy="23" r="7.5" stroke-width="0.804311"/>
    <circle cx="29" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="22" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="14" y1="25" y2="25"/>
    <circle cx="40" cy="30" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="voltageTransformer:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="22" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="18" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="26" x2="22" y1="23" y2="25"/>
    <circle cx="22" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="10" y2="12"/>
    <circle cx="35" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="46" x2="51" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="46" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="50" x2="51" y1="24" y2="28"/>
    <circle cx="48" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="27" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="33" y2="33"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8e690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8f050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a8f9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a906b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a918b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a924c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a93070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a93aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a950b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a950b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a96a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a96a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a98890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a98890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2a998a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9b530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a9c180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a9d060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a9d940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9f100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9f900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9fff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa0a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa1bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa2570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa3060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa3a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa4f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa5a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa6aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa76e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ab5eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa8d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2aa9f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2aab4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1398" width="2481" x="11" y="-1174"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-238725">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 -241.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39853" ObjectName="SW-CX_GYS.CX_GYS_001BK"/>
     <cge:Meas_Ref ObjectId="238725"/>
    <cge:TPSR_Ref TObjectID="39853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238766">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 677.000000 -111.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39874" ObjectName="SW-CX_GYS.CX_GYS_032BK"/>
     <cge:Meas_Ref ObjectId="238766"/>
    <cge:TPSR_Ref TObjectID="39874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238813">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1530.127932 -117.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39912" ObjectName="SW-CX_GYS.CX_GYS_012BK"/>
     <cge:Meas_Ref ObjectId="238813"/>
    <cge:TPSR_Ref TObjectID="39912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238781">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1624.118337 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39886" ObjectName="SW-CX_GYS.CX_GYS_041BK"/>
     <cge:Meas_Ref ObjectId="238781"/>
    <cge:TPSR_Ref TObjectID="39886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238791">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1913.286780 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39894" ObjectName="SW-CX_GYS.CX_GYS_043BK"/>
     <cge:Meas_Ref ObjectId="238791"/>
    <cge:TPSR_Ref TObjectID="39894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1775.689765 -110.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39890" ObjectName="SW-CX_GYS.CX_GYS_042BK"/>
     <cge:Meas_Ref ObjectId="238786"/>
    <cge:TPSR_Ref TObjectID="39890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238797">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2236.189765 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39898" ObjectName="SW-CX_GYS.CX_GYS_045BK"/>
     <cge:Meas_Ref ObjectId="238797"/>
    <cge:TPSR_Ref TObjectID="39898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238734">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.708955 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39859" ObjectName="SW-CX_GYS.CX_GYS_002BK"/>
     <cge:Meas_Ref ObjectId="238734"/>
    <cge:TPSR_Ref TObjectID="39859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238723">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39850" ObjectName="SW-CX_GYS.CX_GYS_301BK"/>
     <cge:Meas_Ref ObjectId="238723"/>
    <cge:TPSR_Ref TObjectID="39850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 -752.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39862" ObjectName="SW-CX_GYS.CX_GYS_371BK"/>
     <cge:Meas_Ref ObjectId="238741"/>
    <cge:TPSR_Ref TObjectID="39862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238732">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.708955 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39856" ObjectName="SW-CX_GYS.CX_GYS_302BK"/>
     <cge:Meas_Ref ObjectId="238732"/>
    <cge:TPSR_Ref TObjectID="39856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238803">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 -109.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39904" ObjectName="SW-CX_GYS.CX_GYS_036BK"/>
     <cge:Meas_Ref ObjectId="238803"/>
    <cge:TPSR_Ref TObjectID="39904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238760">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 524.000000 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39868" ObjectName="SW-CX_GYS.CX_GYS_031BK"/>
     <cge:Meas_Ref ObjectId="238760"/>
    <cge:TPSR_Ref TObjectID="39868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.000000 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39878" ObjectName="SW-CX_GYS.CX_GYS_033BK"/>
     <cge:Meas_Ref ObjectId="238771"/>
    <cge:TPSR_Ref TObjectID="39878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238776">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 980.000000 -113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39882" ObjectName="SW-CX_GYS.CX_GYS_034BK"/>
     <cge:Meas_Ref ObjectId="238776"/>
    <cge:TPSR_Ref TObjectID="39882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238808">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2074.286780 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39908" ObjectName="SW-CX_GYS.CX_GYS_044BK"/>
     <cge:Meas_Ref ObjectId="238808"/>
    <cge:TPSR_Ref TObjectID="39908"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_28821e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1376.000000 -862.000000)" xlink:href="#voltageTransformer:shape135"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28de140">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1424.000000 -463.000000)" xlink:href="#voltageTransformer:shape144"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_292fa00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1957.089552 -422.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296c600">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 731.089552 -440.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2226.189765 180.000000)" xlink:href="#capacitor:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 514.000000 180.000000)" xlink:href="#capacitor:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1137.000000 -23.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1137.000000 -23.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_GYS.CX_GYS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="60199"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1245.000000 -377.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1245.000000 -377.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="39927" ObjectName="TF-CX_GYS.CX_GYS_1T"/>
    <cge:TPSR_Ref TObjectID="39927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_GYS.CX_GYS_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="60203"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.708955 -376.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.708955 -376.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="39928" ObjectName="TF-CX_GYS.CX_GYS_2T"/>
    <cge:TPSR_Ref TObjectID="39928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2395.892324 61.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2395.892324 61.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2881720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1501.000000 -803.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2883710">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 -517.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2883df0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1385.000000 -564.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28625c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 681.000000 -8.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2866490">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 -27.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0cd40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1628.118337 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b10c10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1596.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a54720">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1917.286780 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a585f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1889.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a61ee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.689765 -7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a65db0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1749.000000 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a6f6a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2383.892324 -7.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a78870">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2240.189765 -9.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f1e10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1436.000000 -864.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2904060">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1236.000000 -25.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_290ce30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -25.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2914de0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2198.000000 81.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_292c7a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.000000 84.000000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_292d5a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 523.000000 -27.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_292e280">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1983.089552 -349.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_292ec50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2048.089552 -337.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2933190">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 -474.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2933bb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1628.000000 -476.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2948350">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1386.000000 -1005.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294a160">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 497.000000 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294ae10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294ec40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 836.000000 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2952b10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 801.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_295dca0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 -10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2961b70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 949.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296b1f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.089552 -367.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296b910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 822.089552 -355.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_297a8f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2078.286780 -9.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_297e7c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2050.000000 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b166b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2199.892324 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b171b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2275.892324 112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b17e60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2418.089552 -117.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b1f3e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1113.089552 -120.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b25a70">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 492.500000 177.500000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b26610">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2213.500000 177.500000)" xlink:href="#lightningRod:shape175"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b344e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 -314.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b350c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1628.000000 -309.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 683.000000 163.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39874"/>
     <cge:Term_Ref ObjectID="60091"/>
    <cge:TPSR_Ref TObjectID="39874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 683.000000 163.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39874"/>
     <cge:Term_Ref ObjectID="60091"/>
    <cge:TPSR_Ref TObjectID="39874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238942" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 683.000000 163.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238942" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39874"/>
     <cge:Term_Ref ObjectID="60091"/>
    <cge:TPSR_Ref TObjectID="39874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -782.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39862"/>
     <cge:Term_Ref ObjectID="60067"/>
    <cge:TPSR_Ref TObjectID="39862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -782.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39862"/>
     <cge:Term_Ref ObjectID="60067"/>
    <cge:TPSR_Ref TObjectID="39862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238903" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -782.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39862"/>
     <cge:Term_Ref ObjectID="60067"/>
    <cge:TPSR_Ref TObjectID="39862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238860" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1222.000000 -627.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238860" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39850"/>
     <cge:Term_Ref ObjectID="60043"/>
    <cge:TPSR_Ref TObjectID="39850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238861" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1222.000000 -627.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39850"/>
     <cge:Term_Ref ObjectID="60043"/>
    <cge:TPSR_Ref TObjectID="39850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238851" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1222.000000 -627.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238851" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39850"/>
     <cge:Term_Ref ObjectID="60043"/>
    <cge:TPSR_Ref TObjectID="39850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -620.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39856"/>
     <cge:Term_Ref ObjectID="60055"/>
    <cge:TPSR_Ref TObjectID="39856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -620.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39856"/>
     <cge:Term_Ref ObjectID="60055"/>
    <cge:TPSR_Ref TObjectID="39856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238877" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -620.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39856"/>
     <cge:Term_Ref ObjectID="60055"/>
    <cge:TPSR_Ref TObjectID="39856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238872" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1380.000000 -282.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39853"/>
     <cge:Term_Ref ObjectID="60049"/>
    <cge:TPSR_Ref TObjectID="39853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1380.000000 -282.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39853"/>
     <cge:Term_Ref ObjectID="60049"/>
    <cge:TPSR_Ref TObjectID="39853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238863" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1380.000000 -282.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238863" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39853"/>
     <cge:Term_Ref ObjectID="60049"/>
    <cge:TPSR_Ref TObjectID="39853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1754.000000 -280.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39859"/>
     <cge:Term_Ref ObjectID="60061"/>
    <cge:TPSR_Ref TObjectID="39859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1754.000000 -280.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39859"/>
     <cge:Term_Ref ObjectID="60061"/>
    <cge:TPSR_Ref TObjectID="39859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1754.000000 -280.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39859"/>
     <cge:Term_Ref ObjectID="60061"/>
    <cge:TPSR_Ref TObjectID="39859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-238902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1769.000000 -477.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39928"/>
     <cge:Term_Ref ObjectID="60201"/>
    <cge:TPSR_Ref TObjectID="39928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-238901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1769.000000 -477.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39928"/>
     <cge:Term_Ref ObjectID="60201"/>
    <cge:TPSR_Ref TObjectID="39928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-238876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1177.000000 -485.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39927"/>
     <cge:Term_Ref ObjectID="60200"/>
    <cge:TPSR_Ref TObjectID="39927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-238875" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1177.000000 -485.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39927"/>
     <cge:Term_Ref ObjectID="60200"/>
    <cge:TPSR_Ref TObjectID="39927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-238915" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 -796.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39931"/>
     <cge:Term_Ref ObjectID="60205"/>
    <cge:TPSR_Ref TObjectID="39931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-238916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 -796.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39931"/>
     <cge:Term_Ref ObjectID="60205"/>
    <cge:TPSR_Ref TObjectID="39931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-238917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 -796.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39931"/>
     <cge:Term_Ref ObjectID="60205"/>
    <cge:TPSR_Ref TObjectID="39931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-238921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 -796.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39931"/>
     <cge:Term_Ref ObjectID="60205"/>
    <cge:TPSR_Ref TObjectID="39931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-238918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 -796.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39931"/>
     <cge:Term_Ref ObjectID="60205"/>
    <cge:TPSR_Ref TObjectID="39931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-238922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -334.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39932"/>
     <cge:Term_Ref ObjectID="60206"/>
    <cge:TPSR_Ref TObjectID="39932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-238923" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -334.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238923" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39932"/>
     <cge:Term_Ref ObjectID="60206"/>
    <cge:TPSR_Ref TObjectID="39932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-238924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -334.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39932"/>
     <cge:Term_Ref ObjectID="60206"/>
    <cge:TPSR_Ref TObjectID="39932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-238928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -334.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39932"/>
     <cge:Term_Ref ObjectID="60206"/>
    <cge:TPSR_Ref TObjectID="39932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-238925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -334.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39932"/>
     <cge:Term_Ref ObjectID="60206"/>
    <cge:TPSR_Ref TObjectID="39932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-238929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2455.000000 -336.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39933"/>
     <cge:Term_Ref ObjectID="60207"/>
    <cge:TPSR_Ref TObjectID="39933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-238930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2455.000000 -336.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39933"/>
     <cge:Term_Ref ObjectID="60207"/>
    <cge:TPSR_Ref TObjectID="39933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-238931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2455.000000 -336.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39933"/>
     <cge:Term_Ref ObjectID="60207"/>
    <cge:TPSR_Ref TObjectID="39933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-238935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2455.000000 -336.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39933"/>
     <cge:Term_Ref ObjectID="60207"/>
    <cge:TPSR_Ref TObjectID="39933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-238932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2455.000000 -336.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39933"/>
     <cge:Term_Ref ObjectID="60207"/>
    <cge:TPSR_Ref TObjectID="39933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238951" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 164.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238951" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39878"/>
     <cge:Term_Ref ObjectID="60099"/>
    <cge:TPSR_Ref TObjectID="39878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 164.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39878"/>
     <cge:Term_Ref ObjectID="60099"/>
    <cge:TPSR_Ref TObjectID="39878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238948" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 164.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39878"/>
     <cge:Term_Ref ObjectID="60099"/>
    <cge:TPSR_Ref TObjectID="39878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 159.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39882"/>
     <cge:Term_Ref ObjectID="60107"/>
    <cge:TPSR_Ref TObjectID="39882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 159.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39882"/>
     <cge:Term_Ref ObjectID="60107"/>
    <cge:TPSR_Ref TObjectID="39882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 159.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39882"/>
     <cge:Term_Ref ObjectID="60107"/>
    <cge:TPSR_Ref TObjectID="39882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238991" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 157.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39904"/>
     <cge:Term_Ref ObjectID="60151"/>
    <cge:TPSR_Ref TObjectID="39904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238992" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 157.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238992" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39904"/>
     <cge:Term_Ref ObjectID="60151"/>
    <cge:TPSR_Ref TObjectID="39904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238988" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 157.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238988" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39904"/>
     <cge:Term_Ref ObjectID="60151"/>
    <cge:TPSR_Ref TObjectID="39904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1472.000000 -72.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238939" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39912"/>
     <cge:Term_Ref ObjectID="60167"/>
    <cge:TPSR_Ref TObjectID="39912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1472.000000 -72.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39912"/>
     <cge:Term_Ref ObjectID="60167"/>
    <cge:TPSR_Ref TObjectID="39912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1472.000000 -72.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39912"/>
     <cge:Term_Ref ObjectID="60167"/>
    <cge:TPSR_Ref TObjectID="39912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238963" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 156.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238963" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39886"/>
     <cge:Term_Ref ObjectID="60115"/>
    <cge:TPSR_Ref TObjectID="39886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238964" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 156.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238964" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39886"/>
     <cge:Term_Ref ObjectID="60115"/>
    <cge:TPSR_Ref TObjectID="39886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238960" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 156.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238960" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39886"/>
     <cge:Term_Ref ObjectID="60115"/>
    <cge:TPSR_Ref TObjectID="39886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238969" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1772.000000 156.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238969" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39890"/>
     <cge:Term_Ref ObjectID="60123"/>
    <cge:TPSR_Ref TObjectID="39890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238970" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1772.000000 156.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238970" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39890"/>
     <cge:Term_Ref ObjectID="60123"/>
    <cge:TPSR_Ref TObjectID="39890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1772.000000 156.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39890"/>
     <cge:Term_Ref ObjectID="60123"/>
    <cge:TPSR_Ref TObjectID="39890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1924.000000 155.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39894"/>
     <cge:Term_Ref ObjectID="60131"/>
    <cge:TPSR_Ref TObjectID="39894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1924.000000 155.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39894"/>
     <cge:Term_Ref ObjectID="60131"/>
    <cge:TPSR_Ref TObjectID="39894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1924.000000 155.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39894"/>
     <cge:Term_Ref ObjectID="60131"/>
    <cge:TPSR_Ref TObjectID="39894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-238997" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2074.000000 155.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39908"/>
     <cge:Term_Ref ObjectID="60159"/>
    <cge:TPSR_Ref TObjectID="39908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238998" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2074.000000 155.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238998" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39908"/>
     <cge:Term_Ref ObjectID="60159"/>
    <cge:TPSR_Ref TObjectID="39908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238994" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2074.000000 155.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39908"/>
     <cge:Term_Ref ObjectID="60159"/>
    <cge:TPSR_Ref TObjectID="39908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238986" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2338.000000 154.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39898"/>
     <cge:Term_Ref ObjectID="60139"/>
    <cge:TPSR_Ref TObjectID="39898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2338.000000 154.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39898"/>
     <cge:Term_Ref ObjectID="60139"/>
    <cge:TPSR_Ref TObjectID="39898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-238987" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2338.000000 154.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238987" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39898"/>
     <cge:Term_Ref ObjectID="60139"/>
    <cge:TPSR_Ref TObjectID="39898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-238981" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 463.000000 -150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238981" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39868"/>
     <cge:Term_Ref ObjectID="60079"/>
    <cge:TPSR_Ref TObjectID="39868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-238978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 463.000000 -150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39868"/>
     <cge:Term_Ref ObjectID="60079"/>
    <cge:TPSR_Ref TObjectID="39868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-238982" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 463.000000 -150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="238982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39868"/>
     <cge:Term_Ref ObjectID="60079"/>
    <cge:TPSR_Ref TObjectID="39868"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1453" y="-782"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1453" y="-782"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="1304" y="-429"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="1304" y="-429"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1661" y="-428"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1661" y="-428"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="542" y="-141"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="542" y="-141"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="695" y="-140"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="695" y="-140"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="850" y="-142"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="850" y="-142"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="998" y="-142"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="998" y="-142"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1285" y="-138"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1285" y="-138"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1549" y="-146"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1549" y="-146"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1642" y="-142"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1642" y="-142"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1794" y="-139"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1794" y="-139"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1932" y="-142"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1932" y="-142"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2093" y="-141"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2093" y="-141"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2255" y="-141"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2255" y="-141"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
   <g href="35kV观音山变CX_GYS_371间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1453" y="-782"/></g>
   <g href="35kV观音山变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="1304" y="-429"/></g>
   <g href="35kV观音山变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1661" y="-428"/></g>
   <g href="35kV观音山变CX_GYS_031间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="542" y="-141"/></g>
   <g href="35kV观音山变CX_GYS_032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="695" y="-140"/></g>
   <g href="35kV观音山变CX_GYS_033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="850" y="-142"/></g>
   <g href="35kV观音山变CX_GYS_034间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="998" y="-142"/></g>
   <g href="35kV观音山变CX_GYS_036间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1285" y="-138"/></g>
   <g href="35kV观音山变CX_GYS_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1549" y="-146"/></g>
   <g href="35kV观音山变CX_GYS_041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1642" y="-142"/></g>
   <g href="35kV观音山变CX_GYS_042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1794" y="-139"/></g>
   <g href="35kV观音山变CX_GYS_043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1932" y="-142"/></g>
   <g href="35kV观音山变CX_GYS_044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2093" y="-141"/></g>
   <g href="35kV观音山变CX_GYS_045间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2255" y="-141"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-238742">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 -980.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39863" ObjectName="SW-CX_GYS.CX_GYS_3716SW"/>
     <cge:Meas_Ref ObjectId="238742"/>
    <cge:TPSR_Ref TObjectID="39863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238745">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1454.000000 -1038.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39867" ObjectName="SW-CX_GYS.CX_GYS_37167SW"/>
     <cge:Meas_Ref ObjectId="238745"/>
    <cge:TPSR_Ref TObjectID="39867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238744">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1453.000000 -966.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39866" ObjectName="SW-CX_GYS.CX_GYS_37160SW"/>
     <cge:Meas_Ref ObjectId="238744"/>
    <cge:TPSR_Ref TObjectID="39866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238726">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -215.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39854" ObjectName="SW-CX_GYS.CX_GYS_001XC"/>
     <cge:Meas_Ref ObjectId="238726"/>
    <cge:TPSR_Ref TObjectID="39854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238726">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -280.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39855" ObjectName="SW-CX_GYS.CX_GYS_001XC1"/>
     <cge:Meas_Ref ObjectId="238726"/>
    <cge:TPSR_Ref TObjectID="39855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238768">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 -40.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39877" ObjectName="SW-CX_GYS.CX_GYS_03260SW"/>
     <cge:Meas_Ref ObjectId="238768"/>
    <cge:TPSR_Ref TObjectID="39877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238767">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 676.000000 -150.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39875" ObjectName="SW-CX_GYS.CX_GYS_032XC"/>
     <cge:Meas_Ref ObjectId="238767"/>
    <cge:TPSR_Ref TObjectID="39875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238767">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 676.000000 -85.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39876" ObjectName="SW-CX_GYS.CX_GYS_032XC1"/>
     <cge:Meas_Ref ObjectId="238767"/>
    <cge:TPSR_Ref TObjectID="39876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238814">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1529.127932 -91.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39914" ObjectName="SW-CX_GYS.CX_GYS_012XC1"/>
     <cge:Meas_Ref ObjectId="238814"/>
    <cge:TPSR_Ref TObjectID="39914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238815">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.127932 -149.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39915" ObjectName="SW-CX_GYS.CX_GYS_0121XC"/>
     <cge:Meas_Ref ObjectId="238815"/>
    <cge:TPSR_Ref TObjectID="39915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238815">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.127932 -98.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39916" ObjectName="SW-CX_GYS.CX_GYS_0121XC1"/>
     <cge:Meas_Ref ObjectId="238815"/>
    <cge:TPSR_Ref TObjectID="39916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238814">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1529.127932 -156.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39913" ObjectName="SW-CX_GYS.CX_GYS_012XC"/>
     <cge:Meas_Ref ObjectId="238814"/>
    <cge:TPSR_Ref TObjectID="39913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238783">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1661.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39889" ObjectName="SW-CX_GYS.CX_GYS_04160SW"/>
     <cge:Meas_Ref ObjectId="238783"/>
    <cge:TPSR_Ref TObjectID="39889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238782">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.118337 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39887" ObjectName="SW-CX_GYS.CX_GYS_041XC"/>
     <cge:Meas_Ref ObjectId="238782"/>
    <cge:TPSR_Ref TObjectID="39887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238782">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.118337 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39888" ObjectName="SW-CX_GYS.CX_GYS_041XC1"/>
     <cge:Meas_Ref ObjectId="238782"/>
    <cge:TPSR_Ref TObjectID="39888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238793">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39897" ObjectName="SW-CX_GYS.CX_GYS_04360SW"/>
     <cge:Meas_Ref ObjectId="238793"/>
    <cge:TPSR_Ref TObjectID="39897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1912.286780 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39895" ObjectName="SW-CX_GYS.CX_GYS_043XC"/>
     <cge:Meas_Ref ObjectId="238792"/>
    <cge:TPSR_Ref TObjectID="39895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1912.286780 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39896" ObjectName="SW-CX_GYS.CX_GYS_043XC1"/>
     <cge:Meas_Ref ObjectId="238792"/>
    <cge:TPSR_Ref TObjectID="39896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1814.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39893" ObjectName="SW-CX_GYS.CX_GYS_04260SW"/>
     <cge:Meas_Ref ObjectId="238788"/>
    <cge:TPSR_Ref TObjectID="39893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1774.689765 -149.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39891" ObjectName="SW-CX_GYS.CX_GYS_042XC"/>
     <cge:Meas_Ref ObjectId="238787"/>
    <cge:TPSR_Ref TObjectID="39891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1774.689765 -84.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39892" ObjectName="SW-CX_GYS.CX_GYS_042XC1"/>
     <cge:Meas_Ref ObjectId="238787"/>
    <cge:TPSR_Ref TObjectID="39892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238838">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2416.892324 -158.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39925" ObjectName="SW-CX_GYS.CX_GYS_0461XC"/>
     <cge:Meas_Ref ObjectId="238838"/>
    <cge:TPSR_Ref TObjectID="39925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238838">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2416.892324 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39926" ObjectName="SW-CX_GYS.CX_GYS_0461XC1"/>
     <cge:Meas_Ref ObjectId="238838"/>
    <cge:TPSR_Ref TObjectID="39926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238799">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2284.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39901" ObjectName="SW-CX_GYS.CX_GYS_04560SW"/>
     <cge:Meas_Ref ObjectId="238799"/>
    <cge:TPSR_Ref TObjectID="39901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238798">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2235.189765 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39899" ObjectName="SW-CX_GYS.CX_GYS_045XC"/>
     <cge:Meas_Ref ObjectId="238798"/>
    <cge:TPSR_Ref TObjectID="39899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238801">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2195.000000 55.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39903" ObjectName="SW-CX_GYS.CX_GYS_04567SW"/>
     <cge:Meas_Ref ObjectId="238801"/>
    <cge:TPSR_Ref TObjectID="39903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238800">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2236.189765 54.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39902" ObjectName="SW-CX_GYS.CX_GYS_0456SW"/>
     <cge:Meas_Ref ObjectId="238800"/>
    <cge:TPSR_Ref TObjectID="39902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238798">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2235.189765 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39900" ObjectName="SW-CX_GYS.CX_GYS_045XC1"/>
     <cge:Meas_Ref ObjectId="238798"/>
    <cge:TPSR_Ref TObjectID="39900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238735">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.708955 -214.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39860" ObjectName="SW-CX_GYS.CX_GYS_002XC"/>
     <cge:Meas_Ref ObjectId="238735"/>
    <cge:TPSR_Ref TObjectID="39860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238735">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.708955 -279.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39861" ObjectName="SW-CX_GYS.CX_GYS_002XC1"/>
     <cge:Meas_Ref ObjectId="238735"/>
    <cge:TPSR_Ref TObjectID="39861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -556.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39852" ObjectName="SW-CX_GYS.CX_GYS_301XC1"/>
     <cge:Meas_Ref ObjectId="238724"/>
    <cge:TPSR_Ref TObjectID="39852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -624.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39851" ObjectName="SW-CX_GYS.CX_GYS_301XC"/>
     <cge:Meas_Ref ObjectId="238724"/>
    <cge:TPSR_Ref TObjectID="39851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238743">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 -726.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39864" ObjectName="SW-CX_GYS.CX_GYS_371XC"/>
     <cge:Meas_Ref ObjectId="238743"/>
    <cge:TPSR_Ref TObjectID="39864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238743">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 -794.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39865" ObjectName="SW-CX_GYS.CX_GYS_371XC1"/>
     <cge:Meas_Ref ObjectId="238743"/>
    <cge:TPSR_Ref TObjectID="39865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238733">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.708955 -556.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39858" ObjectName="SW-CX_GYS.CX_GYS_302XC1"/>
     <cge:Meas_Ref ObjectId="238733"/>
    <cge:TPSR_Ref TObjectID="39858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238733">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.708955 -624.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39857" ObjectName="SW-CX_GYS.CX_GYS_302XC"/>
     <cge:Meas_Ref ObjectId="238733"/>
    <cge:TPSR_Ref TObjectID="39857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238805">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1301.000000 -38.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39907" ObjectName="SW-CX_GYS.CX_GYS_03660SW"/>
     <cge:Meas_Ref ObjectId="238805"/>
    <cge:TPSR_Ref TObjectID="39907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238804">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -148.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39905" ObjectName="SW-CX_GYS.CX_GYS_036XC"/>
     <cge:Meas_Ref ObjectId="238804"/>
    <cge:TPSR_Ref TObjectID="39905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238804">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.000000 -83.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39906" ObjectName="SW-CX_GYS.CX_GYS_036XC1"/>
     <cge:Meas_Ref ObjectId="238804"/>
    <cge:TPSR_Ref TObjectID="39906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238762">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39871" ObjectName="SW-CX_GYS.CX_GYS_03160SW"/>
     <cge:Meas_Ref ObjectId="238762"/>
    <cge:TPSR_Ref TObjectID="39871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238761">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 523.000000 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39869" ObjectName="SW-CX_GYS.CX_GYS_031XC"/>
     <cge:Meas_Ref ObjectId="238761"/>
    <cge:TPSR_Ref TObjectID="39869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238764">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 470.000000 55.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39873" ObjectName="SW-CX_GYS.CX_GYS_03167SW"/>
     <cge:Meas_Ref ObjectId="238764"/>
    <cge:TPSR_Ref TObjectID="39873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238763">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 524.000000 54.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39872" ObjectName="SW-CX_GYS.CX_GYS_0316SW"/>
     <cge:Meas_Ref ObjectId="238763"/>
    <cge:TPSR_Ref TObjectID="39872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238761">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 523.000000 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39870" ObjectName="SW-CX_GYS.CX_GYS_031XC1"/>
     <cge:Meas_Ref ObjectId="238761"/>
    <cge:TPSR_Ref TObjectID="39870"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238826">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1981.892324 -279.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39922" ObjectName="SW-CX_GYS.CX_GYS_0902XC1"/>
     <cge:Meas_Ref ObjectId="238826"/>
    <cge:TPSR_Ref TObjectID="39922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238826">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1981.892324 -232.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39921" ObjectName="SW-CX_GYS.CX_GYS_0902XC"/>
     <cge:Meas_Ref ObjectId="238826"/>
    <cge:TPSR_Ref TObjectID="39921"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238821">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.892324 -629.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39917" ObjectName="SW-CX_GYS.CX_GYS_3901XC"/>
     <cge:Meas_Ref ObjectId="238821"/>
    <cge:TPSR_Ref TObjectID="39917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238821">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.892324 -582.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39918" ObjectName="SW-CX_GYS.CX_GYS_3901XC1"/>
     <cge:Meas_Ref ObjectId="238821"/>
    <cge:TPSR_Ref TObjectID="39918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 866.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39881" ObjectName="SW-CX_GYS.CX_GYS_03360SW"/>
     <cge:Meas_Ref ObjectId="238773"/>
    <cge:TPSR_Ref TObjectID="39881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39879" ObjectName="SW-CX_GYS.CX_GYS_033XC"/>
     <cge:Meas_Ref ObjectId="238772"/>
    <cge:TPSR_Ref TObjectID="39879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39880" ObjectName="SW-CX_GYS.CX_GYS_033XC1"/>
     <cge:Meas_Ref ObjectId="238772"/>
    <cge:TPSR_Ref TObjectID="39880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238778">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1014.000000 -42.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39885" ObjectName="SW-CX_GYS.CX_GYS_03460SW"/>
     <cge:Meas_Ref ObjectId="238778"/>
    <cge:TPSR_Ref TObjectID="39885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238777">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 979.000000 -152.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39883" ObjectName="SW-CX_GYS.CX_GYS_034XC"/>
     <cge:Meas_Ref ObjectId="238777"/>
    <cge:TPSR_Ref TObjectID="39883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238777">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 979.000000 -87.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39884" ObjectName="SW-CX_GYS.CX_GYS_034XC1"/>
     <cge:Meas_Ref ObjectId="238777"/>
    <cge:TPSR_Ref TObjectID="39884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238823">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 755.892324 -297.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39920" ObjectName="SW-CX_GYS.CX_GYS_0901XC1"/>
     <cge:Meas_Ref ObjectId="238823"/>
    <cge:TPSR_Ref TObjectID="39920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238823">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 755.892324 -250.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39919" ObjectName="SW-CX_GYS.CX_GYS_0901XC"/>
     <cge:Meas_Ref ObjectId="238823"/>
    <cge:TPSR_Ref TObjectID="39919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238810">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2115.000000 -41.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39911" ObjectName="SW-CX_GYS.CX_GYS_04460SW"/>
     <cge:Meas_Ref ObjectId="238810"/>
    <cge:TPSR_Ref TObjectID="39911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238809">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2073.286780 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39909" ObjectName="SW-CX_GYS.CX_GYS_044XC"/>
     <cge:Meas_Ref ObjectId="238809"/>
    <cge:TPSR_Ref TObjectID="39909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238809">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2073.286780 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39910" ObjectName="SW-CX_GYS.CX_GYS_044XC1"/>
     <cge:Meas_Ref ObjectId="238809"/>
    <cge:TPSR_Ref TObjectID="39910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238837">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1111.892324 -161.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39923" ObjectName="SW-CX_GYS.CX_GYS_0351XC"/>
     <cge:Meas_Ref ObjectId="238837"/>
    <cge:TPSR_Ref TObjectID="39923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-238837">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1111.892324 -91.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39924" ObjectName="SW-CX_GYS.CX_GYS_0351XC1"/>
     <cge:Meas_Ref ObjectId="238837"/>
    <cge:TPSR_Ref TObjectID="39924"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1436.000000 -1111.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 681.000000 109.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 111.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 108.277778)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1628.118337 109.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.689765 114.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1917.286780 108.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2423.000000 113.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 836.000000 107.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 107.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2078.286780 109.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_287a5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-648 1276,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39851@0" ObjectIDZND0="39931@0" Pin0InfoVect0LinkObjId="g_2883520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238724_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-648 1276,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d1500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-971 1508,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39866@1" ObjectIDZND0="g_27d19c0@0" Pin0InfoVect0LinkObjId="g_27d19c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238744_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-971 1508,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d1760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1495,-1043 1508,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39867@1" ObjectIDZND0="g_28801d0@0" Pin0InfoVect0LinkObjId="g_28801d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238745_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1495,-1043 1508,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2883520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-653 1441,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39917@0" ObjectIDZND0="39931@0" Pin0InfoVect0LinkObjId="g_287a5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238821_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-653 1441,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2889e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-276 1276,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39853@1" ObjectIDZND0="39855@1" Pin0InfoVect0LinkObjId="SW-238726_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-276 1276,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288a0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-239 1276,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39854@1" ObjectIDZND0="39853@0" Pin0InfoVect0LinkObjId="SW-238725_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238726_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-239 1276,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288a320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-222 1276,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39854@0" ObjectIDZND0="39932@0" Pin0InfoVect0LinkObjId="g_28671c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-222 1276,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2862100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="686,-146 686,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39874@1" ObjectIDZND0="39875@1" Pin0InfoVect0LinkObjId="SW-238767_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238766_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="686,-146 686,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2862360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="686,-109 686,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39876@1" ObjectIDZND0="39874@0" Pin0InfoVect0LinkObjId="SW-238766_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238767_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="686,-109 686,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2866230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-36 720,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28657e0@0" ObjectIDZND0="39877@0" Pin0InfoVect0LinkObjId="SW-238768_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28657e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="720,-36 720,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28671c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="686,-174 686,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39875@0" ObjectIDZND0="39932@0" Pin0InfoVect0LinkObjId="g_288a320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="686,-174 686,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_286a210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="686,-92 686,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39876@0" ObjectIDZND0="g_28625c0@1" Pin0InfoVect0LinkObjId="g_28625c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="686,-92 686,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b02210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-152 1539,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39912@1" ObjectIDZND0="39913@1" Pin0InfoVect0LinkObjId="SW-238814_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238813_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-152 1539,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b02470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-115 1539,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39914@1" ObjectIDZND0="39912@0" Pin0InfoVect0LinkObjId="SW-238813_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238814_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-115 1539,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b026d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-180 1539,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39913@0" ObjectIDZND0="39933@0" Pin0InfoVect0LinkObjId="g_2a59320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238814_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-180 1539,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b02930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-173 1384,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39915@0" ObjectIDZND0="39932@0" Pin0InfoVect0LinkObjId="g_288a320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238815_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-173 1384,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b02b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-105 1384,-81 1539,-81 1539,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39916@0" ObjectIDZND0="39914@0" Pin0InfoVect0LinkObjId="SW-238814_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238815_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-105 1384,-81 1539,-81 1539,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b08d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-156 1384,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39915@1" ObjectIDZND0="39916@1" Pin0InfoVect0LinkObjId="SW-238815_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238815_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-156 1384,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0c880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-148 1633,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39886@1" ObjectIDZND0="39887@1" Pin0InfoVect0LinkObjId="SW-238782_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238781_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-148 1633,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0cae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-111 1633,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39888@1" ObjectIDZND0="39886@0" Pin0InfoVect0LinkObjId="SW-238781_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238782_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-111 1633,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b109b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1670,-38 1670,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2b0ff60@0" ObjectIDZND0="39889@0" Pin0InfoVect0LinkObjId="SW-238783_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0ff60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1670,-38 1670,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4f280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1603,-83 1670,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b10c10@0" ObjectIDZND0="39889@1" Pin0InfoVect0LinkObjId="SW-238783_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b10c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1603,-83 1670,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4f4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-94 1633,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39888@0" ObjectIDZND0="g_2b0cd40@1" Pin0InfoVect0LinkObjId="g_2b0cd40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238782_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-94 1633,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a58390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1963,-38 1963,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a57940@0" ObjectIDZND0="39897@0" Pin0InfoVect0LinkObjId="SW-238793_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a57940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1963,-38 1963,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a59320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-176 1922,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39895@0" ObjectIDZND0="39933@0" Pin0InfoVect0LinkObjId="g_2b026d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238792_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-176 1922,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a5c3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1896,-83 1963,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a585f0@0" ObjectIDZND0="39897@1" Pin0InfoVect0LinkObjId="SW-238793_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a585f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1896,-83 1963,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a5c620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-94 1922,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39896@0" ObjectIDZND0="g_2a54720@1" Pin0InfoVect0LinkObjId="g_2a54720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238792_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-94 1922,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a61a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-148 1922,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39894@1" ObjectIDZND0="39895@1" Pin0InfoVect0LinkObjId="SW-238792_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238791_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-148 1922,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a61c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-111 1922,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39896@1" ObjectIDZND0="39894@0" Pin0InfoVect0LinkObjId="SW-238791_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-111 1922,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a65b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1823,-35 1823,-43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a65100@0" ObjectIDZND0="39893@0" Pin0InfoVect0LinkObjId="SW-238788_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a65100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1823,-35 1823,-43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a66ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-173 1785,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39891@0" ObjectIDZND0="39933@0" Pin0InfoVect0LinkObjId="g_2b026d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238787_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-173 1785,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a69b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1756,-80 1823,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a65db0@0" ObjectIDZND0="39893@1" Pin0InfoVect0LinkObjId="SW-238788_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a65db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1756,-80 1823,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a69de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-91 1785,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39892@0" ObjectIDZND0="g_2a61ee0@1" Pin0InfoVect0LinkObjId="g_2a61ee0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238787_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-91 1785,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a6f1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-145 1785,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39890@1" ObjectIDZND0="39891@1" Pin0InfoVect0LinkObjId="SW-238787_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238786_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-145 1785,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a6f440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-108 1785,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39892@1" ObjectIDZND0="39890@0" Pin0InfoVect0LinkObjId="SW-238786_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238787_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-108 1785,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a783b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-147 2245,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39898@1" ObjectIDZND0="39899@1" Pin0InfoVect0LinkObjId="SW-238798_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238797_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-147 2245,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a78610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-110 2245,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39900@1" ObjectIDZND0="39898@0" Pin0InfoVect0LinkObjId="SW-238797_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238798_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-110 2245,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a7c4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2293,-38 2293,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a7ba90@0" ObjectIDZND0="39901@0" Pin0InfoVect0LinkObjId="SW-238799_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a7ba90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2293,-38 2293,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a7c740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-14 2245,13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a78870@0" ObjectIDZND0="39902@1" Pin0InfoVect0LinkObjId="SW-238800_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a78870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-14 2245,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a7c9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-175 2245,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39899@0" ObjectIDZND0="39933@0" Pin0InfoVect0LinkObjId="g_2b026d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238798_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-175 2245,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a859d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2228,5 2228,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a844b0@0" ObjectIDZND0="39903@1" Pin0InfoVect0LinkObjId="SW-238801_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a844b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2228,5 2228,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a88430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,-93 2245,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39900@0" ObjectIDZND0="g_2a78870@1" Pin0InfoVect0LinkObjId="g_2a78870_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238798_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,-93 2245,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28d0350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-275 1633,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39859@1" ObjectIDZND0="39861@1" Pin0InfoVect0LinkObjId="SW-238735_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238734_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-275 1633,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28d05b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-238 1633,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39860@1" ObjectIDZND0="39859@0" Pin0InfoVect0LinkObjId="SW-238734_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238735_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-238 1633,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28dd0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-732 1441,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39864@0" ObjectIDZND0="39931@0" Pin0InfoVect0LinkObjId="g_287a5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238743_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-732 1441,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28dd300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-857 1508,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_28f1e10@0" ObjectIDND1="g_28821e0@0" ObjectIDND2="39865@x" ObjectIDZND0="g_2881720@0" Pin0InfoVect0LinkObjId="g_2881720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28f1e10_0" Pin1InfoVect1LinkObjId="g_28821e0_0" Pin1InfoVect2LinkObjId="SW-238743_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-857 1508,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28dd560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-869 1441,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_28f1e10@0" ObjectIDZND0="g_2881720@0" ObjectIDZND1="g_28821e0@0" ObjectIDZND2="39865@x" Pin0InfoVect0LinkObjId="g_2881720_0" Pin0InfoVect1LinkObjId="g_28821e0_0" Pin0InfoVect2LinkObjId="SW-238743_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28f1e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-869 1441,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28dd7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1447,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1447,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28dda20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-572 1392,-572 1392,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="39918@x" ObjectIDND1="g_2883710@0" ObjectIDZND0="g_2883df0@0" Pin0InfoVect0LinkObjId="g_2883df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-238821_0" Pin1InfoVect1LinkObjId="g_2883710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-572 1392,-572 1392,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28ddc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-588 1441,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="39918@0" ObjectIDZND0="g_2883df0@0" ObjectIDZND1="g_2883710@0" Pin0InfoVect0LinkObjId="g_2883df0_0" Pin0InfoVect1LinkObjId="g_2883710_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238821_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-588 1441,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28ddee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-572 1441,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2883df0@0" ObjectIDND1="39918@x" ObjectIDZND0="g_2883710@0" Pin0InfoVect0LinkObjId="g_2883710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2883df0_0" Pin1InfoVect1LinkObjId="SW-238821_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-572 1441,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28e0dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-522 1441,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2883710@1" ObjectIDZND0="g_28de140@0" Pin0InfoVect0LinkObjId="g_28de140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2883710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-522 1441,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28e61b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-617 1276,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39850@1" ObjectIDZND0="39851@1" Pin0InfoVect0LinkObjId="SW-238724_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238723_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-617 1276,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28e6410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-580 1276,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39852@1" ObjectIDZND0="39850@0" Pin0InfoVect0LinkObjId="SW-238723_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-580 1276,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28ee690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-787 1441,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39862@1" ObjectIDZND0="39865@1" Pin0InfoVect0LinkObjId="SW-238743_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238741_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-787 1441,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28ee8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-750 1441,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39864@1" ObjectIDZND0="39862@0" Pin0InfoVect0LinkObjId="SW-238741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238743_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-750 1441,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28f1950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28f1bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1379,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1379,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28f2830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-971 1441,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="39863@x" ObjectIDND1="39866@x" ObjectIDZND0="g_28f1e10@1" Pin0InfoVect0LinkObjId="g_28f1e10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-238742_0" Pin1InfoVect1LinkObjId="SW-238744_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-971 1441,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28f2a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-985 1441,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="39863@0" ObjectIDZND0="g_28f1e10@0" ObjectIDZND1="39866@x" Pin0InfoVect0LinkObjId="g_28f1e10_0" Pin0InfoVect1LinkObjId="SW-238744_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238742_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-985 1441,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28f2cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-971 1458,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="39863@x" ObjectIDND1="g_28f1e10@0" ObjectIDZND0="39866@0" Pin0InfoVect0LinkObjId="SW-238744_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-238742_0" Pin1InfoVect1LinkObjId="g_28f1e10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-971 1458,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28f2f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-563 1276,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39852@0" ObjectIDZND0="g_2933190@1" Pin0InfoVect0LinkObjId="g_2933190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238724_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-563 1276,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28f31b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-479 1276,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2933190@0" ObjectIDZND0="39927@1" Pin0InfoVect0LinkObjId="g_2b34e60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2933190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-479 1276,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28f85a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-617 1633,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39856@1" ObjectIDZND0="39857@1" Pin0InfoVect0LinkObjId="SW-238733_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238732_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-617 1633,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28f8800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-580 1633,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39858@1" ObjectIDZND0="39856@0" Pin0InfoVect0LinkObjId="SW-238732_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238733_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-580 1633,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28fb8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1669,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1669,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28fbb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-680 1633,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39931@0" ObjectIDZND0="39857@0" Pin0InfoVect0LinkObjId="SW-238733_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_287a5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-680 1633,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28fbd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1592,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28fbfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-481 1633,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2933bb0@0" ObjectIDZND0="39928@1" Pin0InfoVect0LinkObjId="g_2b35c50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2933bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-481 1633,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28fc220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-563 1633,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39858@0" ObjectIDZND0="g_2933bb0@1" Pin0InfoVect0LinkObjId="g_2933bb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-563 1633,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fd090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="686,88 686,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_28625c0@0" Pin0InfoVect0LinkObjId="g_28625c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28821e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="686,88 686,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28fdf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1123,67 1123,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="load" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_28821e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1123,67 1123,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fe160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-186 1122,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39923@0" ObjectIDZND0="39932@0" Pin0InfoVect0LinkObjId="g_288a320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238837_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-186 1122,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fe3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="653,-81 720,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2866490@0" ObjectIDZND0="39877@1" Pin0InfoVect0LinkObjId="SW-238768_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2866490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="653,-81 720,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29006f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-144 1276,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39904@1" ObjectIDZND0="39905@1" Pin0InfoVect0LinkObjId="SW-238804_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238803_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-144 1276,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2900950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-107 1276,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39906@1" ObjectIDZND0="39904@0" Pin0InfoVect0LinkObjId="SW-238803_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238804_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-107 1276,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2903e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1310,-34 1310,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29033b0@0" ObjectIDZND0="39907@0" Pin0InfoVect0LinkObjId="SW-238805_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29033b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1310,-34 1310,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2904d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-172 1276,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39905@0" ObjectIDZND0="39932@0" Pin0InfoVect0LinkObjId="g_288a320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-172 1276,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2907e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-90 1276,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39906@0" ObjectIDZND0="g_290ce30@1" Pin0InfoVect0LinkObjId="g_290ce30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-90 1276,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290bd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1243,-79 1310,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2904060@0" ObjectIDZND0="39907@1" Pin0InfoVect0LinkObjId="SW-238805_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2904060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1243,-79 1310,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290cbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-15 1633,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2b0cd40@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_28821e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0cd40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-15 1633,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290d8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-30 1276,86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_290ce30@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_28821e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_290ce30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-30 1276,86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290e720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1785,-12 1785,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2a61ee0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_28821e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a61ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1785,-12 1785,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290f590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-15 1922,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2a54720@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_28821e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a54720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-15 1922,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2912090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2428,109 2428,61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2428,109 2428,61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29122f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2391,-61 2427,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_2a6f6a0@0" ObjectIDZND0="39926@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-238838_0" Pin0InfoVect1LinkObjId="g_28821e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a6f6a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2391,-61 2427,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2912550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2427,-95 2427,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="39926@0" ObjectIDZND0="g_2a6f6a0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2a6f6a0_0" Pin0InfoVect1LinkObjId="g_28821e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238838_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2427,-95 2427,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29127b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2427,-61 2427,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="39926@x" ObjectIDND1="g_2a6f6a0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_28821e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-238838_0" Pin1InfoVect1LinkObjId="g_2a6f6a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2427,-61 2427,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29154c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,49 2245,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="39902@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_28821e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,49 2245,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2915720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2283,58 2229,58 2228,50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b171b0@0" ObjectIDZND0="39903@0" Pin0InfoVect0LinkObjId="SW-238801_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b171b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2283,58 2229,58 2228,50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2915980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2204,50 2204,65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2914de0@0" Pin0InfoVect0LinkObjId="g_2914de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2204,50 2204,65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2915be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2207,-80 2293,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b166b0@0" ObjectIDZND0="39901@1" Pin0InfoVect0LinkObjId="SW-238799_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b166b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2207,-80 2293,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2917f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="533,-147 533,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39868@1" ObjectIDZND0="39869@1" Pin0InfoVect0LinkObjId="SW-238761_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238760_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="533,-147 533,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2918170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="533,-110 533,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39870@1" ObjectIDZND0="39868@0" Pin0InfoVect0LinkObjId="SW-238760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238761_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="533,-110 533,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_291b620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-38 568,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_291abd0@0" ObjectIDZND0="39871@0" Pin0InfoVect0LinkObjId="SW-238762_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_291abd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="568,-38 568,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_291b880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="533,-175 533,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39869@0" ObjectIDZND0="39932@0" Pin0InfoVect0LinkObjId="g_288a320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="533,-175 533,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29248b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="503,5 503,14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2923390@0" ObjectIDZND0="39873@1" Pin0InfoVect0LinkObjId="SW-238764_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2923390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="503,5 503,14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292ce80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="533,49 533,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="39872@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_28821e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="533,49 533,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292d0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="558,58 504,58 503,50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_294ae10@0" ObjectIDZND0="39873@0" Pin0InfoVect0LinkObjId="SW-238764_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_294ae10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="558,58 504,58 503,50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292d340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="504,-80 568,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_294a160@0" ObjectIDZND0="39871@1" Pin0InfoVect0LinkObjId="SW-238762_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_294a160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="504,-80 568,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292e020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="533,-93 533,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39870@0" ObjectIDZND0="g_292d5a0@1" Pin0InfoVect0LinkObjId="g_292d5a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="533,-93 533,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29325b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-427 1992,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_292fa00@0" ObjectIDZND0="g_292e280@0" Pin0InfoVect0LinkObjId="g_292e280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_292fa00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-427 1992,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2932810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-327 2055,-327 2055,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="39922@x" ObjectIDND1="g_292e280@0" ObjectIDZND0="g_292ec50@0" Pin0InfoVect0LinkObjId="g_292ec50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-238826_0" Pin1InfoVect1LinkObjId="g_292e280_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-327 2055,-327 2055,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2932a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-303 1992,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="39922@0" ObjectIDZND0="g_292ec50@0" ObjectIDZND1="g_292e280@0" Pin0InfoVect0LinkObjId="g_292ec50_0" Pin0InfoVect1LinkObjId="g_292e280_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238826_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-303 1992,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2932cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-327 1992,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_292ec50@0" ObjectIDND1="39922@x" ObjectIDZND0="g_292e280@1" Pin0InfoVect0LinkObjId="g_292e280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_292ec50_0" Pin1InfoVect1LinkObjId="SW-238826_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-327 1992,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2932f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-205 1992,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39933@0" ObjectIDZND0="39921@0" Pin0InfoVect0LinkObjId="SW-238826_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b026d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-205 1992,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29345d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-221 1633,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39860@0" ObjectIDZND0="39933@0" Pin0InfoVect0LinkObjId="g_2b026d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238735_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-221 1633,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2934830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-176 1633,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39887@0" ObjectIDZND0="39933@0" Pin0InfoVect0LinkObjId="g_2b026d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238782_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-176 1633,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2935780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1020 1441,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="39863@1" ObjectIDZND0="39867@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2948350@0" Pin0InfoVect0LinkObjId="SW-238745_0" Pin0InfoVect1LinkObjId="g_28821e0_0" Pin0InfoVect2LinkObjId="g_2948350_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238742_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1020 1441,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2935970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1043 1459,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="39863@x" ObjectIDND1="0@x" ObjectIDND2="g_2948350@0" ObjectIDZND0="39867@0" Pin0InfoVect0LinkObjId="SW-238745_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-238742_0" Pin1InfoVect1LinkObjId="g_28821e0_0" Pin1InfoVect2LinkObjId="g_2948350_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1043 1459,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29379a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="479,50 479,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_292c7a0@0" Pin0InfoVect0LinkObjId="g_292c7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="479,50 479,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29414e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1992,-256 1992,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39921@1" ObjectIDZND0="39922@1" Pin0InfoVect0LinkObjId="SW-238826_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238826_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1992,-256 1992,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2947640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-606 1441,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39918@1" ObjectIDZND0="39917@1" Pin0InfoVect0LinkObjId="SW-238821_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238821_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-606 1441,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2948d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1116 1441,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2948350@0" ObjectIDZND1="39867@x" ObjectIDZND2="39863@x" Pin0InfoVect0LinkObjId="g_2948350_0" Pin0InfoVect1LinkObjId="SW-238745_0" Pin0InfoVect2LinkObjId="SW-238742_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28821e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1116 1441,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2948fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1078 1441,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_2948350@0" ObjectIDZND0="39867@x" ObjectIDZND1="39863@x" Pin0InfoVect0LinkObjId="SW-238745_0" Pin0InfoVect1LinkObjId="SW-238742_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28821e0_0" Pin1InfoVect1LinkObjId="g_2948350_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1078 1441,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2949230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-1078 1393,-1078 1393,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="39867@x" ObjectIDND2="39863@x" ObjectIDZND0="g_2948350@0" Pin0InfoVect0LinkObjId="g_2948350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28821e0_0" Pin1InfoVect1LinkObjId="SW-238745_0" Pin1InfoVect2LinkObjId="SW-238742_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-1078 1393,-1078 1393,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2949490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-857 1441,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_28f1e10@0" ObjectIDND1="g_2881720@0" ObjectIDND2="g_28821e0@0" ObjectIDZND0="39865@0" Pin0InfoVect0LinkObjId="SW-238743_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28f1e10_0" Pin1InfoVect1LinkObjId="g_2881720_0" Pin1InfoVect2LinkObjId="g_28821e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-857 1441,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29496f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-857 1384,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_28f1e10@0" ObjectIDND1="g_2881720@0" ObjectIDND2="39865@x" ObjectIDZND0="g_28821e0@0" Pin0InfoVect0LinkObjId="g_28821e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28f1e10_0" Pin1InfoVect1LinkObjId="g_2881720_0" Pin1InfoVect2LinkObjId="SW-238743_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1441,-857 1384,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2949f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="533,13 533,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39872@1" ObjectIDZND0="g_292d5a0@0" Pin0InfoVect0LinkObjId="g_292d5a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238763_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="533,13 533,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_294e780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,-148 841,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39878@1" ObjectIDZND0="39879@1" Pin0InfoVect0LinkObjId="SW-238772_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238771_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="841,-148 841,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_294e9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,-111 841,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39880@1" ObjectIDZND0="39878@0" Pin0InfoVect0LinkObjId="SW-238771_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="841,-111 841,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29528b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="875,-38 875,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2951e60@0" ObjectIDZND0="39881@0" Pin0InfoVect0LinkObjId="SW-238773_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2951e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="875,-38 875,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2953840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,-176 841,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39879@0" ObjectIDZND0="39932@0" Pin0InfoVect0LinkObjId="g_288a320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="841,-176 841,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29568a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,-94 841,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39880@0" ObjectIDZND0="g_294ec40@1" Pin0InfoVect0LinkObjId="g_294ec40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="841,-94 841,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,86 841,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_294ec40@0" Pin0InfoVect0LinkObjId="g_294ec40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28821e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="841,86 841,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295aa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="808,-83 875,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2952b10@0" ObjectIDZND0="39881@1" Pin0InfoVect0LinkObjId="SW-238773_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2952b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="808,-83 875,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295d7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-148 989,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39882@1" ObjectIDZND0="39883@1" Pin0InfoVect0LinkObjId="SW-238777_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238776_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,-148 989,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295da40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-111 989,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39884@1" ObjectIDZND0="39882@0" Pin0InfoVect0LinkObjId="SW-238776_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238777_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,-111 989,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2961910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-38 1023,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2960ec0@0" ObjectIDZND0="39885@0" Pin0InfoVect0LinkObjId="SW-238778_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2960ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-38 1023,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29628a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-176 989,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39883@0" ObjectIDZND0="39932@0" Pin0InfoVect0LinkObjId="g_288a320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238777_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,-176 989,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2965900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-94 989,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39884@0" ObjectIDZND0="g_295dca0@1" Pin0InfoVect0LinkObjId="g_295dca0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238777_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,-94 989,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2969830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,86 989,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_295dca0@0" Pin0InfoVect0LinkObjId="g_295dca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28821e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="989,86 989,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2969a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="956,-83 1023,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2961b70@0" ObjectIDZND0="39885@1" Pin0InfoVect0LinkObjId="SW-238778_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2961b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="956,-83 1023,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296f140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-445 766,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_296c600@0" ObjectIDZND0="g_296b1f0@0" Pin0InfoVect0LinkObjId="g_296b1f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296c600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-445 766,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296f3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-344 829,-345 829,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="39920@x" ObjectIDND1="g_296b1f0@0" ObjectIDZND0="g_296b910@0" Pin0InfoVect0LinkObjId="g_296b910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-238823_0" Pin1InfoVect1LinkObjId="g_296b1f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-344 829,-345 829,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296f600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-321 766,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="39920@0" ObjectIDZND0="g_296b910@0" ObjectIDZND1="g_296b1f0@0" Pin0InfoVect0LinkObjId="g_296b910_0" Pin0InfoVect1LinkObjId="g_296b1f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238823_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="766,-321 766,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296f860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-344 766,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="39920@x" ObjectIDND1="g_296b910@0" ObjectIDZND0="g_296b1f0@1" Pin0InfoVect0LinkObjId="g_296b1f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-238823_0" Pin1InfoVect1LinkObjId="g_296b910_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-344 766,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29759c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-274 766,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39919@1" ObjectIDZND0="39920@1" Pin0InfoVect0LinkObjId="SW-238823_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238823_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-274 766,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29769d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-257 766,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39919@0" ObjectIDZND0="39932@0" Pin0InfoVect0LinkObjId="g_288a320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238823_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-257 766,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297e560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2124,-37 2124,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_297db10@0" ObjectIDZND0="39911@0" Pin0InfoVect0LinkObjId="SW-238810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_297db10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2124,-37 2124,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297f4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2083,-179 2083,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39909@0" ObjectIDZND0="39933@0" Pin0InfoVect0LinkObjId="g_2b026d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238809_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2083,-179 2083,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2982550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2057,-82 2124,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_297e7c0@0" ObjectIDZND0="39911@1" Pin0InfoVect0LinkObjId="SW-238810_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_297e7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2057,-82 2124,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29827b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2083,-93 2083,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39910@0" ObjectIDZND0="g_297a8f0@1" Pin0InfoVect0LinkObjId="g_297a8f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238809_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2083,-93 2083,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2987ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2083,-147 2083,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39908@1" ObjectIDZND0="39909@1" Pin0InfoVect0LinkObjId="SW-238809_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238808_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2083,-147 2083,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2987e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2083,-110 2083,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39910@1" ObjectIDZND0="39908@0" Pin0InfoVect0LinkObjId="SW-238808_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238809_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2083,-110 2083,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b15720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2083,-15 2083,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_297a8f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_28821e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_297a8f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2083,-15 2083,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b187f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2427,-165 2427,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39925@1" ObjectIDZND0="g_2b17e60@0" Pin0InfoVect0LinkObjId="g_2b17e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238838_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2427,-165 2427,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b18a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2427,-112 2427,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39926@1" ObjectIDZND0="g_2b17e60@1" Pin0InfoVect0LinkObjId="g_2b17e60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238838_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2427,-112 2427,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b18cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2427,-182 2427,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39925@0" ObjectIDZND0="39933@0" Pin0InfoVect0LinkObjId="g_2b026d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238838_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2427,-182 2427,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1fdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-168 1122,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39923@1" ObjectIDZND0="g_2b1f3e0@0" Pin0InfoVect0LinkObjId="g_2b1f3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238837_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-168 1122,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b20010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-115 1122,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39924@1" ObjectIDZND0="g_2b1f3e0@1" Pin0InfoVect0LinkObjId="g_2b1f3e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238837_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-115 1122,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b20840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1122,-18 1122,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="39924@0" Pin0InfoVect0LinkObjId="SW-238837_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28821e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-18 1122,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b26150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="533,177 533,183 508,183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2b25a70@0" Pin0InfoVect0LinkObjId="g_2b25a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="533,177 533,183 508,183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b263b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,183 479,183 479,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2b25a70@1" ObjectIDZND0="g_292c7a0@1" Pin0InfoVect0LinkObjId="g_292c7a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b25a70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,183 479,183 479,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b26cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2245,177 2245,183 2229,183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2b26610@0" Pin0InfoVect0LinkObjId="g_2b26610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2245,177 2245,183 2229,183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b26f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2218,183 2204,183 2204,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2b26610@1" ObjectIDZND0="g_2914de0@1" Pin0InfoVect0LinkObjId="g_2914de0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b26610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2218,183 2204,183 2204,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b34c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-304 1276,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39855@0" ObjectIDZND0="g_2b344e0@0" Pin0InfoVect0LinkObjId="g_2b344e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-304 1276,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b34e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-372 1276,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2b344e0@1" ObjectIDZND0="39927@0" Pin0InfoVect0LinkObjId="g_28f31b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b344e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-372 1276,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b359f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-303 1633,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39861@0" ObjectIDZND0="g_2b350c0@0" Pin0InfoVect0LinkObjId="g_2b350c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-238735_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-303 1633,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b35c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1633,-367 1633,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2b350c0@1" ObjectIDZND0="39928@0" Pin0InfoVect0LinkObjId="g_28fbfc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b350c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1633,-367 1633,-381 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-238704" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 288.500000 -911.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39832" ObjectName="DYN-CX_GYS"/>
     <cge:Meas_Ref ObjectId="238704"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287a8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 474.000000 289.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287ab20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 458.000000 274.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287ad60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 468.000000 335.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287afa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 468.000000 320.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287b1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 468.000000 305.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287b510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -163.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287b770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 639.000000 -193.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287b9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 615.000000 -178.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287bce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1168.000000 628.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287bf40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1182.000000 598.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287c180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1158.000000 613.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287c4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1698.000000 280.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287c710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1712.000000 250.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287c950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1688.000000 265.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287cc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1683.000000 621.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287cee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1697.000000 591.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287d120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1673.000000 606.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287d450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1501.000000 782.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287d6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1515.000000 752.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287d8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1491.000000 767.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287dc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2408.000000 293.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287de90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2392.000000 278.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287e0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2402.000000 339.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287e310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2402.000000 324.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287e550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2402.000000 309.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287e880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1162.000000 750.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287eaf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1146.000000 735.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287ed30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1156.000000 796.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1156.000000 781.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287f1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1156.000000 766.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287f4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1673.000000 461.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287f740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1673.000000 476.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b09050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1416.000000 71.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b096a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.000000 41.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b098e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1406.000000 56.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d9140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1326.000000 285.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d93a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 255.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d95e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1316.000000 270.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293a330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2273.000000 -153.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293a590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2298.000000 -168.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293a7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2304.000000 -183.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293b350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 394.000000 154.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293b5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 139.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293b830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 425.000000 124.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2ba50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1086.000000 471.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2bcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1086.000000 486.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2d750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 773.000000 -162.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2d9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 787.000000 -192.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2dbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 763.000000 -177.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2df00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 923.000000 -159.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 937.000000 -189.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 913.000000 -174.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.000000 -157.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2e930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1240.000000 -187.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2eb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.000000 -172.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b30510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1552.000000 -157.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b30770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1566.000000 -187.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b309b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1542.000000 -172.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b30ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1718.000000 -156.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b30f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1732.000000 -186.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1708.000000 -171.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b314b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1868.000000 -155.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1882.000000 -185.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1858.000000 -170.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2019.000000 -156.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2033.000000 -186.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b32120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2009.000000 -171.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_27d19c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1503.000000 -965.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28801d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1503.000000 -1037.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28657e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -18.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0ff60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1664.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a57940" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1957.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a65100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1817.000000 -17.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a7ba90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2287.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a844b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2222.000000 10.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a84f40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2198.000000 19.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29033b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -16.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291abd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2923390" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 497.000000 10.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2923e20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.000000 19.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2951e60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 869.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2960ec0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1017.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_297db10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2118.000000 -19.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ee1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a8d7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a8d7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a8d7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a8d7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a8d7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a8d7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a8d7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2a8da90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -1001.500000) translate(0,16)">观音山变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ee0c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -435.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ee0c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -435.000000) translate(0,33)">SZ11-6300/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ee0c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -435.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ee0c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -435.000000) translate(0,69)">Yd11 Uk%=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ee0fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -611.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2880c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -1169.000000) translate(0,15)">35kV果仁线及观音山T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2882f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1728.000000 -439.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2882f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1728.000000 -439.000000) translate(0,33)">SZ11-6300/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2882f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1728.000000 -439.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2882f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1728.000000 -439.000000) translate(0,69)">Yd11 Uk%=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2884ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.500000 -460.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a8b750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1956.000000 -483.000000) translate(0,12)">10kVⅡ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28d3610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1121.000000 -705.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28d3c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 460.000000 -241.000000) translate(0,15)">10kVIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d61d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2205.000000 201.000000) translate(0,12)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d6800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2403.000000 123.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d6a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1887.000000 126.000000) translate(0,12)">蔡家村支洞线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d7960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1724.000000 125.000000) translate(0,12)">蔡家村三号隧洞线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d7bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1573.000000 123.000000) translate(0,12)">龙潭隧洞出口线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d8150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.000000 127.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d8350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1262.000000 122.000000) translate(0,12)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d8590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 636.000000 124.000000) translate(0,12)">观音山倒虹吸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28d8e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 464.000000 204.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2934a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1453.000000 -782.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29350c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1466.000000 -993.000000) translate(0,12)">37160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2935300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1448.000000 -1013.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2935540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1456.000000 -1069.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2935b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -612.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2935e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -270.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29360c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1642.000000 -611.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2936300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1642.000000 -269.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2936540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1304.000000 -429.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2936780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1661.000000 -428.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29369c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2387.000000 -236.000000) translate(0,12)">10kVIIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2936e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 542.000000 -141.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29372e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 540.000000 23.000000) translate(0,12)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2937520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 575.000000 -69.000000) translate(0,12)">03160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2937760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.000000 27.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2937b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 695.000000 -140.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2937e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -70.000000) translate(0,12)">03260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2938080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -138.000000) translate(0,12)">036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29382c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1317.000000 -68.000000) translate(0,12)">03660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2938500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -146.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2938740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1549.000000 -146.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2938980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1642.000000 -142.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2938bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1677.000000 -72.000000) translate(0,12)">04160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2938e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1794.000000 -139.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2939040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1830.000000 -69.000000) translate(0,12)">04260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2939280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1932.000000 -142.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29394c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1970.000000 -72.000000) translate(0,12)">04360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2939700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2446.000000 -143.000000) translate(0,12)">0461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2939940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2255.000000 -141.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2939b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2300.000000 -69.000000) translate(0,12)">04560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2939dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2252.000000 23.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293a000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2151.000000 23.000000) translate(0,12)">04567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29478a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2000.000000 -278.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2947ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1448.000000 -630.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2948110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1135.000000 -146.000000) translate(0,12)">0351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_295ac90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 850.000000 -142.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_295b2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 882.000000 -70.000000) translate(0,12)">03360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2969cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 998.000000 -142.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_296a320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 -72.000000) translate(0,12)">03460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_296aa50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 125.000000) translate(0,12)">蔡家村隧洞线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_296ad70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 947.000000 125.000000) translate(0,12)">蔡家村四号隧洞线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_296afb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 730.000000 -501.000000) translate(0,12)">10kVI母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2975c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 774.000000 -296.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2988060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2070.000000 118.000000) translate(0,12)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b15950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2093.000000 -141.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b15f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2131.000000 -71.000000) translate(0,12)">04460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b35eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 141.000000 -67.000000) translate(0,17)">孟慧凯：15912027237</text>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_GYS.CX_GYS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1105,-680 1722,-680 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39931" ObjectName="BS-CX_GYS.CX_GYS_3IM"/>
    <cge:TPSR_Ref TObjectID="39931"/></metadata>
   <polyline fill="none" opacity="0" points="1105,-680 1722,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_GYS.CX_GYS_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="464,-204 1415,-204 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39932" ObjectName="BS-CX_GYS.CX_GYS_9IM"/>
    <cge:TPSR_Ref TObjectID="39932"/></metadata>
   <polyline fill="none" opacity="0" points="464,-204 1415,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_GYS.CX_GYS_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1510,-205 2471,-205 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39933" ObjectName="BS-CX_GYS.CX_GYS_9IIM"/>
    <cge:TPSR_Ref TObjectID="39933"/></metadata>
   <polyline fill="none" opacity="0" points="1510,-205 2471,-205 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="39932" cx="1276" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39932" cx="686" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39932" cx="533" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39932" cx="841" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39932" cx="989" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39932" cx="766" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39932" cx="1384" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39933" cx="1539" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39933" cx="1922" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39933" cx="1785" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39933" cx="2245" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39933" cx="1992" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39933" cx="1633" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39933" cx="2083" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39933" cx="2427" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39932" cx="1122" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39932" cx="1276" cy="-204" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39933" cx="1633" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39931" cx="1276" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39931" cx="1441" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39931" cx="1441" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39931" cx="1633" cy="-680" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_GYS"/>
</svg>