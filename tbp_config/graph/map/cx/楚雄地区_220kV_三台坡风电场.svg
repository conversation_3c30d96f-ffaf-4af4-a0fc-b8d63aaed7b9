<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-149" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="27 -1231 1522 1257">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="21" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="44" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="50" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="59" x2="59" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="62" x2="62" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="65" x2="65" y1="6" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_201dc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape46_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="61" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="31" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 40,62 40,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="5"/>
    <circle cx="16" cy="62" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape46_1">
    <circle cx="16" cy="84" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,93 10,81 22,81 16,93 16,92 16,93 "/>
   </symbol>
   <symbol id="transformer2:shape22_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,100 64,93 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer2:shape22_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="voltageTransformer:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="11" y2="9"/>
    <circle cx="15" cy="19" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="24" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="11" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="9" y2="7"/>
   </symbol>
   <symbol id="voltageTransformer:shape50">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
   </symbol>
   <symbol id="voltageTransformer:shape6">
    <circle cx="18" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="18" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape88">
    <circle cx="7" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="22" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="16" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="16" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198e740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198f120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_198fb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19902c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19912d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1991ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1992a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19934c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1993d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_19975e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19991b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1999da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_199ab60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199b4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199c520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199cd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199d410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19a0480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a0e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a4100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19a4d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19b3590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19b3dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a6f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a8560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1267" width="1532" x="22" y="-1236"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20929e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 425.000000 53.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1014b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 414.000000 38.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a04d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 439.000000 23.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20de050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 439.000000 541.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_173e4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 439.000000 556.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a61b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 439.000000 571.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1597e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 431.000000 526.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_efd450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 447.000000 511.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18de150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 883.000000 577.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a715c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 872.000000 562.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1870240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 897.000000 547.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c31e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 878.000000 945.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ac8520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 867.000000 930.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a55ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 892.000000 915.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="634,-106 640,-98 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="650,-106 642,-97 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="644,-107 608,-121 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-1105 1209,-1116 1219,-1116 1214,-1105 1214,-1106 1214,-1105 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-1105 1422,-1116 1432,-1116 1427,-1105 1427,-1106 1427,-1105 " stroke="rgb(60,120,255)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-116570">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 812.000000 -903.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21942" ObjectName="SW-CX_STP.CX_STP_271BK"/>
     <cge:Meas_Ref ObjectId="116570"/>
    <cge:TPSR_Ref TObjectID="21942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1204.000000 -873.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1417.000000 -873.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1364.000000 -873.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.000000 -728.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 812.000000 -526.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21949" ObjectName="SW-CX_STP.CX_STP_303BK"/>
     <cge:Meas_Ref ObjectId="116577"/>
    <cge:TPSR_Ref TObjectID="21949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116595">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 512.000000 -391.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21973" ObjectName="SW-CX_STP.CX_STP_371BK"/>
     <cge:Meas_Ref ObjectId="116595"/>
    <cge:TPSR_Ref TObjectID="21973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116598">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.000000 -385.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21977" ObjectName="SW-CX_STP.CX_STP_372BK"/>
     <cge:Meas_Ref ObjectId="116598"/>
    <cge:TPSR_Ref TObjectID="21977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116592">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 753.000000 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21969" ObjectName="SW-CX_STP.CX_STP_373BK"/>
     <cge:Meas_Ref ObjectId="116592"/>
    <cge:TPSR_Ref TObjectID="21969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116589">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 861.000000 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21965" ObjectName="SW-CX_STP.CX_STP_374BK"/>
     <cge:Meas_Ref ObjectId="116589"/>
    <cge:TPSR_Ref TObjectID="21965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116586">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 970.000000 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21961" ObjectName="SW-CX_STP.CX_STP_375BK"/>
     <cge:Meas_Ref ObjectId="116586"/>
    <cge:TPSR_Ref TObjectID="21961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116583">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1091.000000 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21957" ObjectName="SW-CX_STP.CX_STP_376BK"/>
     <cge:Meas_Ref ObjectId="116583"/>
    <cge:TPSR_Ref TObjectID="21957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116580">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1218.000000 -389.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21953" ObjectName="SW-CX_STP.CX_STP_377BK"/>
     <cge:Meas_Ref ObjectId="116580"/>
    <cge:TPSR_Ref TObjectID="21953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116601">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1349.000000 -384.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21981" ObjectName="SW-CX_STP.CX_STP_378BK"/>
     <cge:Meas_Ref ObjectId="116601"/>
    <cge:TPSR_Ref TObjectID="21981"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_19262d0">
    <use class="BV-35KV" transform="matrix(1.516129 -0.000000 0.000000 -1.461538 1420.000000 -628.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2228330">
    <use class="BV-35KV" transform="matrix(1.611111 -0.000000 0.000000 -1.444444 1345.347996 -152.000000)" xlink:href="#voltageTransformer:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_ec79a0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 840.000000 -792.000000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_efaa80">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 778.000000 -1069.000000)" xlink:href="#voltageTransformer:shape88"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_STP.CX_STP_3ⅢM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="435,-472 1486,-472 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21941" ObjectName="BS-CX_STP.CX_STP_3ⅢM"/>
    <cge:TPSR_Ref TObjectID="21941"/></metadata>
   <polyline fill="none" opacity="0" points="435,-472 1486,-472 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1198.000000 -991.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1198.000000 -991.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1411.000000 -991.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1411.000000 -991.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_STP.CX_STP_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="30811"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 783.000000 -672.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 783.000000 -672.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21987" ObjectName="TF-CX_STP.CX_STP_1T"/>
    <cge:TPSR_Ref TObjectID="21987"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20a1280">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1435.000000 -584.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f41cb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1409.000000 -565.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18fae90">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 844.000000 -1053.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1205990">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.236559 -245.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20dee00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 872.056696 -245.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a649d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 980.876833 -245.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ab3450">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1101.707722 -245.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f51fb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 478.871707 -256.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c43940">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.393939 -231.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2474cf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 504.393939 -173.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20d3f50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 631.405670 -200.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_ed2f10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 644.405670 -253.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_248e6c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 628.405670 -132.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17fdae0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.347996 -207.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_196e3c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1362.347996 -251.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18778f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1343.477768 -111.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f11440">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1228.707722 -247.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2027ae0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 782.871707 -549.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 78.000000 -1150.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-116532" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -717.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116532" ObjectName="CX_STP:CX_STP_1T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116525" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 134.000000 -1034.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116525" ObjectName="CX_STP:CX_STP_271BK_P_1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116526" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 134.000000 -952.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116526" ObjectName="CX_STP:CX_STP_271BK_Q_2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116537" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -1079.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116537" ObjectName="CX_STP:CX_STP_3ⅢM_Hz"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-116525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 934.000000 -944.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21942"/>
     <cge:Term_Ref ObjectID="20473"/>
    <cge:TPSR_Ref TObjectID="21942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-116526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 934.000000 -944.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21942"/>
     <cge:Term_Ref ObjectID="20473"/>
    <cge:TPSR_Ref TObjectID="21942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-116524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 934.000000 -944.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21942"/>
     <cge:Term_Ref ObjectID="20473"/>
    <cge:TPSR_Ref TObjectID="21942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-116529" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 940.000000 -577.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21949"/>
     <cge:Term_Ref ObjectID="26499"/>
    <cge:TPSR_Ref TObjectID="21949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-116530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 940.000000 -577.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21949"/>
     <cge:Term_Ref ObjectID="26499"/>
    <cge:TPSR_Ref TObjectID="21949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-116528" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 940.000000 -577.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116528" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21949"/>
     <cge:Term_Ref ObjectID="26499"/>
    <cge:TPSR_Ref TObjectID="21949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-116559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 494.000000 -52.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21973"/>
     <cge:Term_Ref ObjectID="30781"/>
    <cge:TPSR_Ref TObjectID="21973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-116560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 494.000000 -52.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21973"/>
     <cge:Term_Ref ObjectID="30781"/>
    <cge:TPSR_Ref TObjectID="21973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-116558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 494.000000 -52.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21973"/>
     <cge:Term_Ref ObjectID="30781"/>
    <cge:TPSR_Ref TObjectID="21973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-116563" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 618.000000 -52.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116563" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21977"/>
     <cge:Term_Ref ObjectID="30789"/>
    <cge:TPSR_Ref TObjectID="21977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-116564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 618.000000 -52.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21977"/>
     <cge:Term_Ref ObjectID="30789"/>
    <cge:TPSR_Ref TObjectID="21977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-116562" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 618.000000 -52.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21977"/>
     <cge:Term_Ref ObjectID="30789"/>
    <cge:TPSR_Ref TObjectID="21977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-116555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 739.000000 -19.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21969"/>
     <cge:Term_Ref ObjectID="30560"/>
    <cge:TPSR_Ref TObjectID="21969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-116556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 739.000000 -19.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21969"/>
     <cge:Term_Ref ObjectID="30560"/>
    <cge:TPSR_Ref TObjectID="21969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-116554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 739.000000 -19.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21969"/>
     <cge:Term_Ref ObjectID="30560"/>
    <cge:TPSR_Ref TObjectID="21969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-116551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 850.000000 -19.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21965"/>
     <cge:Term_Ref ObjectID="26531"/>
    <cge:TPSR_Ref TObjectID="21965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-116552" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 850.000000 -19.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21965"/>
     <cge:Term_Ref ObjectID="26531"/>
    <cge:TPSR_Ref TObjectID="21965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-116550" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 850.000000 -19.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116550" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21965"/>
     <cge:Term_Ref ObjectID="26531"/>
    <cge:TPSR_Ref TObjectID="21965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-116547" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 959.000000 -19.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21961"/>
     <cge:Term_Ref ObjectID="26523"/>
    <cge:TPSR_Ref TObjectID="21961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-116548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 959.000000 -19.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21961"/>
     <cge:Term_Ref ObjectID="26523"/>
    <cge:TPSR_Ref TObjectID="21961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-116546" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 959.000000 -19.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116546" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21961"/>
     <cge:Term_Ref ObjectID="26523"/>
    <cge:TPSR_Ref TObjectID="21961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-116543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -19.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21957"/>
     <cge:Term_Ref ObjectID="26515"/>
    <cge:TPSR_Ref TObjectID="21957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-116544" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -19.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116544" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21957"/>
     <cge:Term_Ref ObjectID="26515"/>
    <cge:TPSR_Ref TObjectID="21957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-116542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1087.000000 -19.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21957"/>
     <cge:Term_Ref ObjectID="26515"/>
    <cge:TPSR_Ref TObjectID="21957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-116539" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1212.000000 -19.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116539" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21953"/>
     <cge:Term_Ref ObjectID="26507"/>
    <cge:TPSR_Ref TObjectID="21953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-116540" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1212.000000 -19.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116540" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21953"/>
     <cge:Term_Ref ObjectID="26507"/>
    <cge:TPSR_Ref TObjectID="21953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-116538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1212.000000 -19.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21953"/>
     <cge:Term_Ref ObjectID="26507"/>
    <cge:TPSR_Ref TObjectID="21953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-116567" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 -52.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21981"/>
     <cge:Term_Ref ObjectID="30797"/>
    <cge:TPSR_Ref TObjectID="21981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-116568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 -52.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21981"/>
     <cge:Term_Ref ObjectID="30797"/>
    <cge:TPSR_Ref TObjectID="21981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-116566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 -52.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21981"/>
     <cge:Term_Ref ObjectID="30797"/>
    <cge:TPSR_Ref TObjectID="21981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-116533" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -569.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21941"/>
     <cge:Term_Ref ObjectID="20472"/>
    <cge:TPSR_Ref TObjectID="21941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-116534" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -569.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116534" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21941"/>
     <cge:Term_Ref ObjectID="20472"/>
    <cge:TPSR_Ref TObjectID="21941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-116535" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -569.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21941"/>
     <cge:Term_Ref ObjectID="20472"/>
    <cge:TPSR_Ref TObjectID="21941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-116536" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -569.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116536" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21941"/>
     <cge:Term_Ref ObjectID="20472"/>
    <cge:TPSR_Ref TObjectID="21941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-116537" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -569.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116537" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21941"/>
     <cge:Term_Ref ObjectID="20472"/>
    <cge:TPSR_Ref TObjectID="21941"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="151" x="90" y="-1209"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="151" x="90" y="-1209"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="41" y="-1226"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="41" y="-1226"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_风电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="151" x="90" y="-1209"/></g>
   <g href="cx_索引_接线图_省地共调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="41" y="-1226"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="0.423034" x1="753" x2="753" y1="-745" y2="-731"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="753" x2="753" y1="-690" y2="-722"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="773" x2="773" y1="-690" y2="-717"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="773" x2="773" y1="-745" y2="-721"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="733" x2="773" y1="-690" y2="-690"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="634" x2="642" y1="-151" y2="-143"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="642" x2="650" y1="-143" y2="-151"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.510204" x1="642" x2="642" y1="-135" y2="-143"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="2" x1="1197" x2="1282" y1="-845" y2="-845"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="2" x1="1359" x2="1465" y1="-845" y2="-845"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="642" x2="607" y1="-142" y2="-142"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="607" x2="607" y1="-141" y2="-104"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="643" x2="643" y1="-132" y2="-108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="635" x2="650" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="641" x2="641" y1="-98" y2="-73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="601" x2="614" y1="-103" y2="-103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="603" x2="612" y1="-100" y2="-100"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="604" x2="611" y1="-96" y2="-96"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="522" x2="471" y1="-191" y2="-191"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="471" x2="471" y1="-191" y2="-173"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="473" x2="473" y1="-149" y2="-130"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="468" x2="478" y1="-130" y2="-130"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="470" x2="477" y1="-127" y2="-127"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="471" x2="477" y1="-124" y2="-124"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="28" y="-1230"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="25" stroke="rgb(255,255,255)" stroke-width="1" width="12" x="747" y="-731"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="24" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="466" y="-173"/>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_STP.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 757.000000 -104.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43379" ObjectName="SM-CX_STP.P1"/>
    <cge:TPSR_Ref TObjectID="43379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_STP.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 865.000000 -104.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43380" ObjectName="SM-CX_STP.P2"/>
    <cge:TPSR_Ref TObjectID="43380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_STP.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 974.000000 -104.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43381" ObjectName="SM-CX_STP.P3"/>
    <cge:TPSR_Ref TObjectID="43381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_STP.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1095.000000 -104.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43382" ObjectName="SM-CX_STP.P4"/>
    <cge:TPSR_Ref TObjectID="43382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_STP.P5">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.000000 -106.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43383" ObjectName="SM-CX_STP.P5"/>
    <cge:TPSR_Ref TObjectID="43383"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_19d9a70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.000000 -1012.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20dda50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.000000 -944.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2264b00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 749.000000 -884.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18d33b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 727.000000 -672.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_229a660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 785.714327 -273.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2204ab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.534464 -274.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ea9f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1002.354601 -273.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_fc9a60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1123.185490 -273.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f3ffd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.871707 -268.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14ff7f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.883438 -271.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aa5570" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1382.825764 -271.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_f11d50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 749.000000 -813.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2212ce0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1250.185490 -275.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="21941" cx="641" cy="-472" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21941" cx="762" cy="-472" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21941" cx="870" cy="-472" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21941" cx="979" cy="-472" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21941" cx="1100" cy="-472" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21941" cx="1358" cy="-472" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21941" cx="521" cy="-472" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21941" cx="1227" cy="-472" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21941" cx="1444" cy="-472" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,17)">危险点说明</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2093c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -621.000000) translate(0,374)">联系方式</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22612a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,17)">频率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22612a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22612a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,59)">全站有功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22612a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22612a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,101)">风机出力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22612a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22612a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,143)">全站无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22612a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22612a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 36.000000 -1080.000000) translate(0,185)">并网联络点的电压和交换功率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2298510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -1198.500000) translate(0,16)">三台坡风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11e3140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 869.000000 -748.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15137b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 763.000000 -916.000000) translate(0,12)">27117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14ff5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 827.000000 -861.000000) translate(0,12)">2711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f60ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 -933.000000) translate(0,12)">271</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f41ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -975.000000) translate(0,12)">27160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2291d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -1040.000000) translate(0,12)">27167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21462b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 828.000000 -993.000000) translate(0,12)">2716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_1fb5170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -1217.500000) translate(0,16)">台阿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2417240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 391.000000 -462.000000) translate(0,16)">35kVⅢ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_f65920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1458.000000 -532.000000) translate(0,12)">3903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c14b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1065.000000 -87.000000) translate(0,12)">三台坡Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_22da010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 -62.000000) translate(0,12)">(84~87、89~94</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_22da010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 -62.000000) translate(0,26)">号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19eb630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 784.257519 -547.000000) translate(0,12)">303</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22628d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1169.000000 -1152.000000) translate(0,12)">35kVⅢ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2178c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1160.500000 -841.000000) translate(0,12)">0.4kVⅢ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2417960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1366.500000 -841.000000) translate(0,12)">0.4kVⅣ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17fa730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -758.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17fa730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -758.000000) translate(0,27)">SZ11-100000-230/37GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17fa730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -758.000000) translate(0,42)">230±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17fa730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -758.000000) translate(0,57)">100000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17fa730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -758.000000) translate(0,72)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17fa730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 958.000000 -758.000000) translate(0,87)">Ud%=13.47</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1013d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 -718.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19eb7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 687.000000 -725.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_fdbe40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 703.000000 -63.000000) translate(0,12)">(76~83、88、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_fdbe40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 703.000000 -63.000000) translate(0,26)">113号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f9fc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 -87.000000) translate(0,12)">三台坡Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1ab9260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -70.000000) translate(0,12)">(95~100、102、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1ab9260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -70.000000) translate(0,26)">103、105号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_1ab9260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -70.000000) translate(0,40)">发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2212750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 -87.000000) translate(0,12)">三台坡Ⅳ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_f9f380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1060.000000 -63.000000) translate(0,12)">(101、107~112、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_f9f380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1060.000000 -63.000000) translate(0,26)">114~116号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_174e870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 501.000000 -162.000000) translate(0,12)">35kV3号小</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_174e870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 501.000000 -162.000000) translate(0,27)">电阻接地</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_174e870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 501.000000 -162.000000) translate(0,42)">成套装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce61a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 584.000000 -70.000000) translate(0,12)">35kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19230a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 669.000000 -166.000000) translate(0,12)">400kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_229a2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1380.000000 -1152.000000) translate(0,12)">10kV花山村支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f7eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1443.500000 -1105.000000) translate(0,12)">10kV备用站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f7eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1443.500000 -1105.000000) translate(0,27)">（施工变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_168b170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.500000 -1073.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f90040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 699.000000 -87.000000) translate(0,12)">三台坡Ⅴ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_17fd8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1205.000000 -63.000000) translate(0,12)">(117~126</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" graphid="g_17fd8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1205.000000 -63.000000) translate(0,26)">号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab0760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1341.000000 -103.000000) translate(0,12)">3号动态无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab0760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1341.000000 -103.000000) translate(0,27)">补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab09a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -75.000000) translate(0,12)">±36MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a4510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1380.257519 -409.000000) translate(0,12)">378</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a4940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1403.000000 -698.000000) translate(0,12)">35kVⅢ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a4940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1403.000000 -698.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19dbd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 462.000000 -222.000000) translate(0,12)">600kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195f150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1141.000000 -1055.000000) translate(0,12)">200kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195f390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1460.000000 -1054.000000) translate(0,12)">200kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f78860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1404.257519 -322.000000) translate(0,12)">37867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_192a8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 762.000000 -843.000000) translate(0,12)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2509c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1196.000000 -87.000000) translate(0,12)">三台坡Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1860f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 534.257519 -408.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cf5220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 556.257519 -324.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1972ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.257519 -410.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11f5aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 678.257519 -326.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11f5ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.257519 -412.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2145c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 799.257519 -328.000000) translate(0,12)">37367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2145e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 884.257519 -413.000000) translate(0,12)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2146080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 906.257519 -329.000000) translate(0,12)">37467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 993.257519 -411.000000) translate(0,12)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e3f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1015.257519 -327.000000) translate(0,12)">37567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e41b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1112.257519 -411.000000) translate(0,12)">376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fe4ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1134.257519 -327.000000) translate(0,12)">37667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fe4cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1241.257519 -414.000000) translate(0,12)">377</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fe4f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.257519 -330.000000) translate(0,12)">37767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_22255e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 142.000000 -264.000000) translate(0,16)">4825661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_1a05810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 142.000000 -243.000000) translate(0,16)">4771</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="1214" cy="-845" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1214" cy="-984" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1374" cy="-845" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1427" cy="-845" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1269" cy="-845" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1427" cy="-984" fill="rgb(0,255,0)" fillStyle="1" r="2" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-116571">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 812.000000 -834.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21943" ObjectName="SW-CX_STP.CX_STP_2711SW"/>
     <cge:Meas_Ref ObjectId="116571"/>
    <cge:TPSR_Ref TObjectID="21943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116573">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 812.000000 -963.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21945" ObjectName="SW-CX_STP.CX_STP_2716SW"/>
     <cge:Meas_Ref ObjectId="116573"/>
    <cge:TPSR_Ref TObjectID="21945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116574">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 -945.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21946" ObjectName="SW-CX_STP.CX_STP_27160SW"/>
     <cge:Meas_Ref ObjectId="116574"/>
    <cge:TPSR_Ref TObjectID="21946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116575">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 -1013.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21947" ObjectName="SW-CX_STP.CX_STP_27167SW"/>
     <cge:Meas_Ref ObjectId="116575"/>
    <cge:TPSR_Ref TObjectID="21947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116572">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 -885.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21944" ObjectName="SW-CX_STP.CX_STP_27117SW"/>
     <cge:Meas_Ref ObjectId="116572"/>
    <cge:TPSR_Ref TObjectID="21944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116579">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 724.000000 -697.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21952" ObjectName="SW-CX_STP.CX_STP_2010SW"/>
     <cge:Meas_Ref ObjectId="116579"/>
    <cge:TPSR_Ref TObjectID="21952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116594">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 782.714327 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21972" ObjectName="SW-CX_STP.CX_STP_37367SW"/>
     <cge:Meas_Ref ObjectId="116594"/>
    <cge:TPSR_Ref TObjectID="21972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116591">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 890.534464 -297.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21968" ObjectName="SW-CX_STP.CX_STP_37467SW"/>
     <cge:Meas_Ref ObjectId="116591"/>
    <cge:TPSR_Ref TObjectID="21968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116588">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 999.354601 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21964" ObjectName="SW-CX_STP.CX_STP_37567SW"/>
     <cge:Meas_Ref ObjectId="116588"/>
    <cge:TPSR_Ref TObjectID="21964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116585">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1120.185490 -296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21960" ObjectName="SW-CX_STP.CX_STP_37667SW"/>
     <cge:Meas_Ref ObjectId="116585"/>
    <cge:TPSR_Ref TObjectID="21960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116597">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 541.871707 -295.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21976" ObjectName="SW-CX_STP.CX_STP_37167SW"/>
     <cge:Meas_Ref ObjectId="116597"/>
    <cge:TPSR_Ref TObjectID="21976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116600">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 661.883438 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21980" ObjectName="SW-CX_STP.CX_STP_37267SW"/>
     <cge:Meas_Ref ObjectId="116600"/>
    <cge:TPSR_Ref TObjectID="21980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116603">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1379.825764 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21984" ObjectName="SW-CX_STP.CX_STP_37867SW"/>
     <cge:Meas_Ref ObjectId="116603"/>
    <cge:TPSR_Ref TObjectID="21984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116576">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 -814.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21948" ObjectName="SW-CX_STP.CX_STP_20167SW"/>
     <cge:Meas_Ref ObjectId="116576"/>
    <cge:TPSR_Ref TObjectID="21948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116582">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1247.185490 -298.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21956" ObjectName="SW-CX_STP.CX_STP_37767SW"/>
     <cge:Meas_Ref ObjectId="116582"/>
    <cge:TPSR_Ref TObjectID="21956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.000000 -570.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21950" ObjectName="SW-CX_STP.CX_STP_303XC"/>
     <cge:Meas_Ref ObjectId="116578"/>
    <cge:TPSR_Ref TObjectID="21950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.000000 -491.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21951" ObjectName="SW-CX_STP.CX_STP_303XC1"/>
     <cge:Meas_Ref ObjectId="116578"/>
    <cge:TPSR_Ref TObjectID="21951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116596">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.000000 -435.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21974" ObjectName="SW-CX_STP.CX_STP_371XC"/>
     <cge:Meas_Ref ObjectId="116596"/>
    <cge:TPSR_Ref TObjectID="21974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116596">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.000000 -362.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21975" ObjectName="SW-CX_STP.CX_STP_371XC1"/>
     <cge:Meas_Ref ObjectId="116596"/>
    <cge:TPSR_Ref TObjectID="21975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116599">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 631.000000 -429.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21978" ObjectName="SW-CX_STP.CX_STP_372XC"/>
     <cge:Meas_Ref ObjectId="116599"/>
    <cge:TPSR_Ref TObjectID="21978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116599">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 631.000000 -350.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21979" ObjectName="SW-CX_STP.CX_STP_372XC1"/>
     <cge:Meas_Ref ObjectId="116599"/>
    <cge:TPSR_Ref TObjectID="21979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116593">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 -431.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21970" ObjectName="SW-CX_STP.CX_STP_373XC"/>
     <cge:Meas_Ref ObjectId="116593"/>
    <cge:TPSR_Ref TObjectID="21970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116593">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 -352.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21971" ObjectName="SW-CX_STP.CX_STP_373XC1"/>
     <cge:Meas_Ref ObjectId="116593"/>
    <cge:TPSR_Ref TObjectID="21971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116590">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 860.000000 -431.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21966" ObjectName="SW-CX_STP.CX_STP_374XC"/>
     <cge:Meas_Ref ObjectId="116590"/>
    <cge:TPSR_Ref TObjectID="21966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116590">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 860.000000 -352.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21967" ObjectName="SW-CX_STP.CX_STP_374XC1"/>
     <cge:Meas_Ref ObjectId="116590"/>
    <cge:TPSR_Ref TObjectID="21967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116587">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 969.000000 -431.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21962" ObjectName="SW-CX_STP.CX_STP_375XC"/>
     <cge:Meas_Ref ObjectId="116587"/>
    <cge:TPSR_Ref TObjectID="21962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116587">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 969.000000 -352.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21963" ObjectName="SW-CX_STP.CX_STP_375XC1"/>
     <cge:Meas_Ref ObjectId="116587"/>
    <cge:TPSR_Ref TObjectID="21963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116584">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1090.000000 -431.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21958" ObjectName="SW-CX_STP.CX_STP_376XC"/>
     <cge:Meas_Ref ObjectId="116584"/>
    <cge:TPSR_Ref TObjectID="21958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116584">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1090.000000 -352.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21959" ObjectName="SW-CX_STP.CX_STP_376XC1"/>
     <cge:Meas_Ref ObjectId="116584"/>
    <cge:TPSR_Ref TObjectID="21959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116581">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 -435.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21954" ObjectName="SW-CX_STP.CX_STP_377XC"/>
     <cge:Meas_Ref ObjectId="116581"/>
    <cge:TPSR_Ref TObjectID="21954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116581">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 -354.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21955" ObjectName="SW-CX_STP.CX_STP_377XC1"/>
     <cge:Meas_Ref ObjectId="116581"/>
    <cge:TPSR_Ref TObjectID="21955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116602">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -428.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21982" ObjectName="SW-CX_STP.CX_STP_378XC"/>
     <cge:Meas_Ref ObjectId="116602"/>
    <cge:TPSR_Ref TObjectID="21982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116602">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -349.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21983" ObjectName="SW-CX_STP.CX_STP_378XC1"/>
     <cge:Meas_Ref ObjectId="116602"/>
    <cge:TPSR_Ref TObjectID="21983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1434.000000 -485.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21986" ObjectName="SW-CX_STP.CX_STP_3903SW1"/>
     <cge:Meas_Ref ObjectId="116604"/>
    <cge:TPSR_Ref TObjectID="21986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-116604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1434.000000 -519.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21985" ObjectName="SW-CX_STP.CX_STP_3903SW"/>
     <cge:Meas_Ref ObjectId="116604"/>
    <cge:TPSR_Ref TObjectID="21985"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_16d45d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="775,-890 767,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21944@0" ObjectIDZND0="g_2264b00@0" Pin0InfoVect0LinkObjId="g_2264b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116572_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="775,-890 767,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1d51da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="775,-950 768,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21946@0" ObjectIDZND0="g_20dda50@0" Pin0InfoVect0LinkObjId="g_20dda50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116574_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="775,-950 768,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_f97fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-1018 811,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="21945@x" ObjectIDND1="g_18fae90@0" ObjectIDND2="25324@1" ObjectIDZND0="21947@1" Pin0InfoVect0LinkObjId="SW-116575_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116573_0" Pin1InfoVect1LinkObjId="g_18fae90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-1018 811,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_167ab80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="775,-1018 768,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21947@0" ObjectIDZND0="g_19d9a70@0" Pin0InfoVect0LinkObjId="g_19d9a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116575_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="775,-1018 768,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1d17230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-1018 821,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="21947@x" ObjectIDND1="g_18fae90@0" ObjectIDND2="25324@1" ObjectIDZND0="21945@1" Pin0InfoVect0LinkObjId="SW-116573_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116575_0" Pin1InfoVect1LinkObjId="g_18fae90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-1018 821,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f743e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-950 811,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21945@x" ObjectIDND1="21942@x" ObjectIDZND0="21946@1" Pin0InfoVect0LinkObjId="SW-116574_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-116573_0" Pin1InfoVect1LinkObjId="SW-116570_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-950 811,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_248fbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-1111 851,-1122 821,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_18fae90@0" ObjectIDZND0="21947@x" ObjectIDZND1="21945@x" ObjectIDZND2="g_efaa80@0" Pin0InfoVect0LinkObjId="SW-116575_0" Pin0InfoVect1LinkObjId="SW-116573_0" Pin0InfoVect2LinkObjId="g_efaa80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18fae90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="851,-1111 851,-1122 821,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_21aeae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-1104 821,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_18fae90@0" ObjectIDND1="25324@1" ObjectIDND2="g_efaa80@0" ObjectIDZND0="21947@x" ObjectIDZND1="21945@x" Pin0InfoVect0LinkObjId="SW-116575_0" Pin0InfoVect1LinkObjId="SW-116573_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18fae90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_efaa80_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="821,-1104 821,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_22de8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="811,-890 821,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21944@1" ObjectIDZND0="21943@x" ObjectIDZND1="21942@x" Pin0InfoVect0LinkObjId="SW-116571_0" Pin0InfoVect1LinkObjId="SW-116570_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116572_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="811,-890 821,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20056d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-911 821,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21942@0" ObjectIDZND0="21943@x" ObjectIDZND1="21944@x" Pin0InfoVect0LinkObjId="SW-116571_0" Pin0InfoVect1LinkObjId="SW-116572_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="821,-911 821,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20a8080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-890 821,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21942@x" ObjectIDND1="21944@x" ObjectIDZND0="21943@1" Pin0InfoVect0LinkObjId="SW-116571_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-116570_0" Pin1InfoVect1LinkObjId="SW-116572_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-890 821,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1cc00e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-968 821,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21945@0" ObjectIDZND0="21946@x" ObjectIDZND1="21942@x" Pin0InfoVect0LinkObjId="SW-116574_0" Pin0InfoVect1LinkObjId="SW-116570_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116573_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="821,-968 821,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1ca7fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-950 821,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21945@x" ObjectIDND1="21946@x" ObjectIDZND0="21942@1" Pin0InfoVect0LinkObjId="SW-116570_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-116573_0" Pin1InfoVect1LinkObjId="SW-116574_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-950 821,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1956720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1444,-620 1444,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_20a1280@0" ObjectIDZND0="g_19262d0@0" Pin0InfoVect0LinkObjId="g_19262d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20a1280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1444,-620 1444,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fc18a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-1104 821,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="21947@x" ObjectIDND1="21945@x" ObjectIDND2="g_efaa80@0" ObjectIDZND0="g_18fae90@0" ObjectIDZND1="25324@1" Pin0InfoVect0LinkObjId="g_18fae90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116575_0" Pin1InfoVect1LinkObjId="SW-116573_0" Pin1InfoVect2LinkObjId="g_efaa80_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="821,-1104 821,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1668540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-1162 821,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25324@1" ObjectIDZND0="g_18fae90@0" ObjectIDZND1="21947@x" ObjectIDZND2="21945@x" Pin0InfoVect0LinkObjId="g_18fae90_0" Pin0InfoVect1LinkObjId="SW-116575_0" Pin0InfoVect2LinkObjId="SW-116573_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="821,-1162 821,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_24fdf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="733,-738 733,-745 821,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="21952@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116579_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="733,-738 733,-745 821,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1884c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="733,-690 733,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_18d33b0@0" ObjectIDZND0="21952@0" Pin0InfoVect0LinkObjId="SW-116579_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18d33b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="733,-690 733,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18e5ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-301 791,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21972@0" ObjectIDZND0="g_229a660@0" Pin0InfoVect0LinkObjId="g_229a660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116594_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-301 791,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_226fcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-337 791,-346 762,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="21972@1" ObjectIDZND0="g_1205990@0" ObjectIDZND1="43379@x" ObjectIDZND2="21971@x" Pin0InfoVect0LinkObjId="g_1205990_0" Pin0InfoVect1LinkObjId="SM-CX_STP.P1_0" Pin0InfoVect2LinkObjId="SW-116593_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116594_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="791,-337 791,-346 762,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a4ef70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-452 762,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21970@0" ObjectIDZND0="21941@0" Pin0InfoVect0LinkObjId="g_21b0420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116593_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-452 762,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a4f540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-346 762,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="switch" ObjectIDND0="21972@x" ObjectIDND1="g_1205990@0" ObjectIDND2="43379@x" ObjectIDZND0="21971@0" Pin0InfoVect0LinkObjId="SW-116593_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116594_0" Pin1InfoVect1LinkObjId="g_1205990_0" Pin1InfoVect2LinkObjId="SM-CX_STP.P1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-346 762,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c04ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="769,-253 762,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_1205990@0" ObjectIDZND0="21972@x" ObjectIDZND1="21971@x" ObjectIDZND2="43379@x" Pin0InfoVect0LinkObjId="SW-116594_0" Pin0InfoVect1LinkObjId="SW-116593_0" Pin0InfoVect2LinkObjId="SM-CX_STP.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1205990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="769,-253 762,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf7c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-125 762,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43379@0" ObjectIDZND0="g_1205990@0" ObjectIDZND1="21972@x" ObjectIDZND2="21971@x" Pin0InfoVect0LinkObjId="g_1205990_0" Pin0InfoVect1LinkObjId="SW-116594_0" Pin0InfoVect2LinkObjId="SW-116593_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_STP.P1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="762,-125 762,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c01200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-253 762,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1205990@0" ObjectIDND1="43379@x" ObjectIDZND0="21972@x" ObjectIDZND1="21971@x" Pin0InfoVect0LinkObjId="SW-116594_0" Pin0InfoVect1LinkObjId="SW-116593_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1205990_0" Pin1InfoVect1LinkObjId="SM-CX_STP.P1_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="762,-253 762,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ab5760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="899,-302 899,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21968@0" ObjectIDZND0="g_2204ab0@0" Pin0InfoVect0LinkObjId="g_2204ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="899,-302 899,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_efd680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="899,-338 899,-347 870,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="21968@1" ObjectIDZND0="g_20dee00@0" ObjectIDZND1="43380@x" ObjectIDZND2="21967@x" Pin0InfoVect0LinkObjId="g_20dee00_0" Pin0InfoVect1LinkObjId="SM-CX_STP.P2_0" Pin0InfoVect2LinkObjId="SW-116590_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116591_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="899,-338 899,-347 870,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b0420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-453 870,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21966@0" ObjectIDZND0="21941@0" Pin0InfoVect0LinkObjId="g_1a4ef70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-453 870,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb8590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-347 870,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="switch" ObjectIDND0="21968@x" ObjectIDND1="g_20dee00@0" ObjectIDND2="43380@x" ObjectIDZND0="21967@0" Pin0InfoVect0LinkObjId="SW-116590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116591_0" Pin1InfoVect1LinkObjId="g_20dee00_0" Pin1InfoVect2LinkObjId="SM-CX_STP.P2_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-347 870,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f9dff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="877,-253 870,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_20dee00@0" ObjectIDZND0="21968@x" ObjectIDZND1="21967@x" ObjectIDZND2="43380@x" Pin0InfoVect0LinkObjId="SW-116591_0" Pin0InfoVect1LinkObjId="SW-116590_0" Pin0InfoVect2LinkObjId="SM-CX_STP.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20dee00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="877,-253 870,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2146480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-125 870,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43380@0" ObjectIDZND0="g_20dee00@0" ObjectIDZND1="21968@x" ObjectIDZND2="21967@x" Pin0InfoVect0LinkObjId="g_20dee00_0" Pin0InfoVect1LinkObjId="SW-116591_0" Pin0InfoVect2LinkObjId="SW-116590_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_STP.P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="870,-125 870,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_fdc2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-253 870,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_20dee00@0" ObjectIDND1="43380@x" ObjectIDZND0="21968@x" ObjectIDZND1="21967@x" Pin0InfoVect0LinkObjId="SW-116591_0" Pin0InfoVect1LinkObjId="SW-116590_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20dee00_0" Pin1InfoVect1LinkObjId="SM-CX_STP.P2_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="870,-253 870,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2134700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1008,-301 1008,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21964@0" ObjectIDZND0="g_23ea9f0@0" Pin0InfoVect0LinkObjId="g_23ea9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116588_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1008,-301 1008,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_220f050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1008,-337 1008,-346 979,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="21964@1" ObjectIDZND0="g_1a649d0@0" ObjectIDZND1="43381@x" ObjectIDZND2="21963@x" Pin0InfoVect0LinkObjId="g_1a649d0_0" Pin0InfoVect1LinkObjId="SM-CX_STP.P3_0" Pin0InfoVect2LinkObjId="SW-116587_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116588_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1008,-337 1008,-346 979,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_220e3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-452 979,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21962@0" ObjectIDZND0="21941@0" Pin0InfoVect0LinkObjId="g_1a4ef70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116587_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-452 979,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1974cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-346 979,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="switch" ObjectIDND0="21964@x" ObjectIDND1="g_1a649d0@0" ObjectIDND2="43381@x" ObjectIDZND0="21963@0" Pin0InfoVect0LinkObjId="SW-116587_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116588_0" Pin1InfoVect1LinkObjId="g_1a649d0_0" Pin1InfoVect2LinkObjId="SM-CX_STP.P3_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-346 979,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14fcba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="986,-253 979,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_1a649d0@0" ObjectIDZND0="21964@x" ObjectIDZND1="21963@x" ObjectIDZND2="43381@x" Pin0InfoVect0LinkObjId="SW-116588_0" Pin0InfoVect1LinkObjId="SW-116587_0" Pin0InfoVect2LinkObjId="SM-CX_STP.P3_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a649d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="986,-253 979,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d4540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-125 979,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43381@0" ObjectIDZND0="g_1a649d0@0" ObjectIDZND1="21964@x" ObjectIDZND2="21963@x" Pin0InfoVect0LinkObjId="g_1a649d0_0" Pin0InfoVect1LinkObjId="SW-116588_0" Pin0InfoVect2LinkObjId="SW-116587_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_STP.P3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="979,-125 979,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bde2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-253 979,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1a649d0@0" ObjectIDND1="43381@x" ObjectIDZND0="21964@x" ObjectIDZND1="21963@x" Pin0InfoVect0LinkObjId="SW-116588_0" Pin0InfoVect1LinkObjId="SW-116587_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a649d0_0" Pin1InfoVect1LinkObjId="SM-CX_STP.P3_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="979,-253 979,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a8950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1129,-301 1129,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21960@0" ObjectIDZND0="g_fc9a60@0" Pin0InfoVect0LinkObjId="g_fc9a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116585_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1129,-301 1129,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ab5400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1129,-337 1129,-346 1100,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="21960@1" ObjectIDZND0="g_1ab3450@0" ObjectIDZND1="43382@x" ObjectIDZND2="21959@x" Pin0InfoVect0LinkObjId="g_1ab3450_0" Pin0InfoVect1LinkObjId="SM-CX_STP.P4_0" Pin0InfoVect2LinkObjId="SW-116584_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116585_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1129,-337 1129,-346 1100,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c40830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1100,-452 1100,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21958@0" ObjectIDZND0="21941@0" Pin0InfoVect0LinkObjId="g_1a4ef70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116584_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1100,-452 1100,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2092450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1100,-346 1100,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="switch" ObjectIDND0="21960@x" ObjectIDND1="g_1ab3450@0" ObjectIDND2="43382@x" ObjectIDZND0="21959@0" Pin0InfoVect0LinkObjId="SW-116584_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116585_0" Pin1InfoVect1LinkObjId="g_1ab3450_0" Pin1InfoVect2LinkObjId="SM-CX_STP.P4_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1100,-346 1100,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d9730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1107,-253 1100,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_1ab3450@0" ObjectIDZND0="21960@x" ObjectIDZND1="21959@x" ObjectIDZND2="43382@x" Pin0InfoVect0LinkObjId="SW-116585_0" Pin0InfoVect1LinkObjId="SW-116584_0" Pin0InfoVect2LinkObjId="SM-CX_STP.P4_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ab3450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1107,-253 1100,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1957cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1100,-125 1100,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43382@0" ObjectIDZND0="g_1ab3450@0" ObjectIDZND1="21960@x" ObjectIDZND2="21959@x" Pin0InfoVect0LinkObjId="g_1ab3450_0" Pin0InfoVect1LinkObjId="SW-116585_0" Pin0InfoVect2LinkObjId="SW-116584_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_STP.P4_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1100,-125 1100,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22da6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1100,-253 1100,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1ab3450@0" ObjectIDND1="43382@x" ObjectIDZND0="21960@x" ObjectIDZND1="21959@x" Pin0InfoVect0LinkObjId="SW-116585_0" Pin0InfoVect1LinkObjId="SW-116584_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ab3450_0" Pin1InfoVect1LinkObjId="SM-CX_STP.P4_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1100,-253 1100,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe2990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="551,-300 551,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21976@0" ObjectIDZND0="g_f3ffd0@0" Pin0InfoVect0LinkObjId="g_f3ffd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116597_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="551,-300 551,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c320d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-459 521,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21974@0" ObjectIDZND0="21941@0" Pin0InfoVect0LinkObjId="g_1a4ef70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116596_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="521,-459 521,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_ef96a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-206 521,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2474cf0@0" ObjectIDZND0="g_1c43940@0" Pin0InfoVect0LinkObjId="g_1c43940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2474cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="521,-206 521,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f9ee20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="670,-299 670,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21980@0" ObjectIDZND0="g_14ff7f0@0" Pin0InfoVect0LinkObjId="g_14ff7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="670,-299 670,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ce5f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="670,-335 670,-344 641,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21980@1" ObjectIDZND0="g_20d3f50@0" ObjectIDZND1="g_ed2f10@0" ObjectIDZND2="21979@x" Pin0InfoVect0LinkObjId="g_20d3f50_0" Pin0InfoVect1LinkObjId="g_ed2f10_0" Pin0InfoVect2LinkObjId="SW-116599_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116600_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="670,-335 670,-344 641,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21acee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="642,-201 642,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_20d3f50@0" ObjectIDZND0="g_248e6c0@0" Pin0InfoVect0LinkObjId="g_248e6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20d3f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="642,-201 642,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_248fe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-344 641,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="21980@x" ObjectIDND1="g_20d3f50@0" ObjectIDND2="g_ed2f10@0" ObjectIDZND0="21979@0" Pin0InfoVect0LinkObjId="SW-116599_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116600_0" Pin1InfoVect1LinkObjId="g_20d3f50_0" Pin1InfoVect2LinkObjId="g_ed2f10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-344 641,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2298010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="649,-261 641,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_ed2f10@0" ObjectIDZND0="21980@x" ObjectIDZND1="21979@x" ObjectIDZND2="g_20d3f50@0" Pin0InfoVect0LinkObjId="SW-116600_0" Pin0InfoVect1LinkObjId="SW-116599_0" Pin0InfoVect2LinkObjId="g_20d3f50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_ed2f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="649,-261 641,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19deaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-450 641,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21978@0" ObjectIDZND0="21941@0" Pin0InfoVect0LinkObjId="g_1a4ef70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-450 641,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_229a080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-1130 1214,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-1130 1214,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_249cd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-880 1214,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-880 1214,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2142080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-1130 1427,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-1130 1427,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21422e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-880 1427,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-880 1427,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f9f760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-880 1374,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1374,-880 1374,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f9f9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-984 1374,-984 1374,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-984 1374,-984 1374,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1501100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-996 1214,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-996 1214,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1501360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1214,-984 1214,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1214,-984 1214,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18e3230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,-735 1269,-725 1511,-725 1511,-984 1427,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1269,-735 1269,-725 1511,-725 1511,-984 1427,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20a0830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1269,-845 1269,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1269,-845 1269,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bd8970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-996 1427,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-996 1427,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bd8bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-984 1427,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-984 1427,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2148790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="550,-336 550,-345 521,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21976@1" ObjectIDZND0="g_f51fb0@0" ObjectIDZND1="g_1c43940@0" ObjectIDZND2="21975@x" Pin0InfoVect0LinkObjId="g_f51fb0_0" Pin0InfoVect1LinkObjId="g_1c43940_0" Pin0InfoVect2LinkObjId="SW-116596_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116597_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="550,-336 550,-345 521,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b32c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-357 521,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_f51fb0@0" ObjectIDND1="g_1c43940@0" ObjectIDND2="21975@x" ObjectIDZND0="21976@x" ObjectIDZND1="g_f51fb0@0" ObjectIDZND2="g_1c43940@0" Pin0InfoVect0LinkObjId="SW-116597_0" Pin0InfoVect1LinkObjId="g_f51fb0_0" Pin0InfoVect2LinkObjId="g_1c43940_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_f51fb0_0" Pin1InfoVect1LinkObjId="g_1c43940_0" Pin1InfoVect2LinkObjId="SW-116596_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="521,-357 521,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b32e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="485,-314 485,-331 521,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_f51fb0@0" ObjectIDZND0="g_1c43940@0" ObjectIDZND1="21976@x" ObjectIDZND2="g_f51fb0@0" Pin0InfoVect0LinkObjId="g_1c43940_0" Pin0InfoVect1LinkObjId="SW-116597_0" Pin0InfoVect2LinkObjId="g_f51fb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f51fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="485,-314 485,-331 521,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16a3fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-270 521,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1c43940@1" ObjectIDZND0="g_f51fb0@0" ObjectIDZND1="21976@x" ObjectIDZND2="g_f51fb0@0" Pin0InfoVect0LinkObjId="g_f51fb0_0" Pin0InfoVect1LinkObjId="SW-116597_0" Pin0InfoVect2LinkObjId="g_f51fb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c43940_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="521,-270 521,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16a4200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-261 641,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_20d3f50@0" ObjectIDND1="g_ed2f10@0" ObjectIDZND0="21980@x" ObjectIDZND1="21979@x" Pin0InfoVect0LinkObjId="SW-116600_0" Pin0InfoVect1LinkObjId="SW-116599_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20d3f50_0" Pin1InfoVect1LinkObjId="g_ed2f10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="641,-261 641,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d39c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-239 641,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_20d3f50@1" ObjectIDZND0="21980@x" ObjectIDZND1="21979@x" ObjectIDZND2="g_ed2f10@0" Pin0InfoVect0LinkObjId="SW-116600_0" Pin0InfoVect1LinkObjId="SW-116599_0" Pin0InfoVect2LinkObjId="g_ed2f10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20d3f50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="641,-239 641,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f3990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-449 1358,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21982@0" ObjectIDZND0="21941@0" Pin0InfoVect0LinkObjId="g_1a4ef70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116602_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-449 1358,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f3bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1367,-259 1358,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_196e3c0@0" ObjectIDZND0="21984@x" ObjectIDZND1="21983@x" ObjectIDZND2="g_17fdae0@0" Pin0InfoVect0LinkObjId="SW-116603_0" Pin0InfoVect1LinkObjId="SW-116602_0" Pin0InfoVect2LinkObjId="g_17fdae0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_196e3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1367,-259 1358,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22280d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-212 1358,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_17fdae0@0" ObjectIDZND0="g_2228330@0" Pin0InfoVect0LinkObjId="g_2228330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17fdae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-212 1358,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16a4750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1360,-154 1360,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_18778f0@0" Pin0InfoVect0LinkObjId="g_18778f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1360,-154 1360,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aa5380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-246 1358,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_17fdae0@1" ObjectIDZND0="21984@x" ObjectIDZND1="21983@x" ObjectIDZND2="g_196e3c0@0" Pin0InfoVect0LinkObjId="SW-116603_0" Pin0InfoVect1LinkObjId="SW-116602_0" Pin0InfoVect2LinkObjId="g_196e3c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17fdae0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-246 1358,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b3b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1388,-299 1388,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21984@0" ObjectIDZND0="g_1aa5570@0" Pin0InfoVect0LinkObjId="g_1aa5570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116603_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1388,-299 1388,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18b3dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1388,-335 1388,-344 1359,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21984@1" ObjectIDZND0="g_17fdae0@0" ObjectIDZND1="g_196e3c0@0" ObjectIDZND2="21983@x" Pin0InfoVect0LinkObjId="g_17fdae0_0" Pin0InfoVect1LinkObjId="g_196e3c0_0" Pin0InfoVect2LinkObjId="SW-116602_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116603_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1388,-335 1388,-344 1359,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd9630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-357 1358,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21983@0" ObjectIDZND0="g_17fdae0@0" ObjectIDZND1="g_196e3c0@0" ObjectIDZND2="21984@x" Pin0InfoVect0LinkObjId="g_17fdae0_0" Pin0InfoVect1LinkObjId="g_196e3c0_0" Pin0InfoVect2LinkObjId="SW-116603_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116602_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-357 1358,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f78600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-341 1358,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21984@x" ObjectIDND1="21983@x" ObjectIDZND0="g_17fdae0@0" ObjectIDZND1="g_196e3c0@0" Pin0InfoVect0LinkObjId="g_17fdae0_0" Pin0InfoVect1LinkObjId="g_196e3c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-116603_0" Pin1InfoVect1LinkObjId="SW-116602_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-341 1358,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_192ab30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="775,-819 767,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21948@0" ObjectIDZND0="g_f11d50@0" Pin0InfoVect0LinkObjId="g_f11d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116576_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="775,-819 767,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f346f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1256,-303 1256,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21956@0" ObjectIDZND0="g_2212ce0@0" Pin0InfoVect0LinkObjId="g_2212ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116582_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1256,-303 1256,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f27f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1256,-339 1256,-348 1227,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="21956@1" ObjectIDZND0="g_f11440@0" ObjectIDZND1="43383@x" ObjectIDZND2="21955@x" Pin0InfoVect0LinkObjId="g_f11440_0" Pin0InfoVect1LinkObjId="SM-CX_STP.P5_0" Pin0InfoVect2LinkObjId="SW-116581_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116582_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1256,-339 1256,-348 1227,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f281a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,-456 1227,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21954@0" ObjectIDZND0="21941@0" Pin0InfoVect0LinkObjId="g_1a4ef70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116581_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,-456 1227,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11e3370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,-348 1227,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="generator" EndDevType0="switch" ObjectIDND0="21956@x" ObjectIDND1="g_f11440@0" ObjectIDND2="43383@x" ObjectIDZND0="21955@0" Pin0InfoVect0LinkObjId="SW-116581_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116582_0" Pin1InfoVect1LinkObjId="g_f11440_0" Pin1InfoVect2LinkObjId="SM-CX_STP.P5_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,-348 1227,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11e35d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1234,-255 1227,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="g_f11440@0" ObjectIDZND0="21956@x" ObjectIDZND1="21955@x" ObjectIDZND2="43383@x" Pin0InfoVect0LinkObjId="SW-116582_0" Pin0InfoVect1LinkObjId="SW-116581_0" Pin0InfoVect2LinkObjId="SM-CX_STP.P5_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_f11440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1234,-255 1227,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f10f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,-127 1227,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43383@0" ObjectIDZND0="g_f11440@0" ObjectIDZND1="21956@x" ObjectIDZND2="21955@x" Pin0InfoVect0LinkObjId="g_f11440_0" Pin0InfoVect1LinkObjId="SW-116582_0" Pin0InfoVect2LinkObjId="SW-116581_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_STP.P5_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1227,-127 1227,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f111e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,-255 1227,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_f11440@0" ObjectIDND1="43383@x" ObjectIDZND0="21956@x" ObjectIDZND1="21955@x" Pin0InfoVect0LinkObjId="SW-116582_0" Pin0InfoVect1LinkObjId="SW-116581_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_f11440_0" Pin1InfoVect1LinkObjId="SM-CX_STP.P5_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1227,-255 1227,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20d4bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-831 821,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="21948@x" ObjectIDND1="21987@x" ObjectIDND2="g_ec79a0@0" ObjectIDZND0="21943@0" Pin0InfoVect0LinkObjId="SW-116571_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116576_0" Pin1InfoVect1LinkObjId="g_ec7530_0" Pin1InfoVect2LinkObjId="g_ec79a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-831 821,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20d4de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="811,-819 821,-819 821,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" EndDevType2="voltageTransformer" ObjectIDND0="21948@1" ObjectIDZND0="21943@x" ObjectIDZND1="21987@x" ObjectIDZND2="g_ec79a0@0" Pin0InfoVect0LinkObjId="SW-116571_0" Pin0InfoVect1LinkObjId="g_ec7530_0" Pin0InfoVect2LinkObjId="g_ec79a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116576_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="811,-819 821,-819 821,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20d5040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-802 845,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="voltageTransformer" ObjectIDND0="21943@x" ObjectIDND1="21948@x" ObjectIDND2="21987@x" ObjectIDZND0="g_ec79a0@0" Pin0InfoVect0LinkObjId="g_ec79a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116571_0" Pin1InfoVect1LinkObjId="SW-116576_0" Pin1InfoVect2LinkObjId="g_ec7530_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-802 845,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_ec7530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-831 821,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" EndDevType1="voltageTransformer" ObjectIDND0="21943@x" ObjectIDND1="21948@x" ObjectIDZND0="21987@x" ObjectIDZND1="g_ec79a0@0" Pin0InfoVect0LinkObjId="g_ec7740_0" Pin0InfoVect1LinkObjId="g_ec79a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-116571_0" Pin1InfoVect1LinkObjId="SW-116576_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="821,-831 821,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_ec7740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-802 821,-764 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="transformer2" ObjectIDND0="21943@x" ObjectIDND1="21948@x" ObjectIDND2="g_ec79a0@0" ObjectIDZND0="21987@0" Pin0InfoVect0LinkObjId="g_ec7530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116571_0" Pin1InfoVect1LinkObjId="SW-116576_0" Pin1InfoVect2LinkObjId="g_ec79a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-802 821,-764 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1860d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-1089 790,-1104 821,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_efaa80@0" ObjectIDZND0="21947@x" ObjectIDZND1="21945@x" ObjectIDZND2="g_18fae90@0" Pin0InfoVect0LinkObjId="SW-116575_0" Pin0InfoVect1LinkObjId="SW-116573_0" Pin0InfoVect2LinkObjId="g_18fae90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_efaa80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="790,-1089 790,-1104 821,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19da7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1444,-589 1444,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_20a1280@1" ObjectIDZND0="g_f41cb0@0" ObjectIDZND1="21985@x" Pin0InfoVect0LinkObjId="g_f41cb0_0" Pin0InfoVect1LinkObjId="SW-116604_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20a1280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1444,-589 1444,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19da9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1444,-569 1416,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_20a1280@0" ObjectIDND1="21985@x" ObjectIDZND0="g_f41cb0@0" Pin0InfoVect0LinkObjId="g_f41cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20a1280_0" Pin1InfoVect1LinkObjId="SW-116604_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1444,-569 1416,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16a5ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-561 821,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21949@1" ObjectIDZND0="21950@1" Pin0InfoVect0LinkObjId="SW-116578_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116577_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-561 821,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a08db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-474 821,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21941@0" ObjectIDZND0="21951@0" Pin0InfoVect0LinkObjId="SW-116578_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a4ef70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-474 821,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a09010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-515 821,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21951@1" ObjectIDZND0="21949@0" Pin0InfoVect0LinkObjId="SW-116577_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116578_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-515 821,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_f455c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-426 521,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21973@1" ObjectIDZND0="21974@1" Pin0InfoVect0LinkObjId="SW-116596_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116595_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="521,-426 521,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ae1280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-386 521,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21975@1" ObjectIDZND0="21973@0" Pin0InfoVect0LinkObjId="SW-116595_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116596_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="521,-386 521,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1593f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-331 521,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_f51fb0@0" ObjectIDND1="g_1c43940@0" ObjectIDZND0="21976@x" ObjectIDZND1="g_f51fb0@0" ObjectIDZND2="g_1c43940@0" Pin0InfoVect0LinkObjId="SW-116597_0" Pin0InfoVect1LinkObjId="g_f51fb0_0" Pin0InfoVect2LinkObjId="g_1c43940_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_f51fb0_0" Pin1InfoVect1LinkObjId="g_1c43940_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="521,-331 521,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2225be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-339 521,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_f51fb0@0" ObjectIDND1="g_1c43940@0" ObjectIDND2="21976@x" ObjectIDZND0="21976@x" ObjectIDZND1="g_f51fb0@0" ObjectIDZND2="g_1c43940@0" Pin0InfoVect0LinkObjId="SW-116597_0" Pin0InfoVect1LinkObjId="g_f51fb0_0" Pin0InfoVect2LinkObjId="g_1c43940_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_f51fb0_0" Pin1InfoVect1LinkObjId="g_1c43940_0" Pin1InfoVect2LinkObjId="SW-116597_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="521,-339 521,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16939a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-339 521,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_f51fb0@0" ObjectIDND1="g_1c43940@0" ObjectIDND2="21976@x" ObjectIDZND0="21976@x" ObjectIDZND1="g_f51fb0@0" ObjectIDZND2="g_1c43940@0" Pin0InfoVect0LinkObjId="SW-116597_0" Pin0InfoVect1LinkObjId="g_f51fb0_0" Pin0InfoVect2LinkObjId="g_1c43940_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_f51fb0_0" Pin1InfoVect1LinkObjId="g_1c43940_0" Pin1InfoVect2LinkObjId="SW-116597_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="521,-339 521,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1693c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-357 521,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="21976@x" ObjectIDND1="g_f51fb0@0" ObjectIDND2="g_1c43940@0" ObjectIDZND0="21975@0" Pin0InfoVect0LinkObjId="SW-116596_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-116597_0" Pin1InfoVect1LinkObjId="g_f51fb0_0" Pin1InfoVect2LinkObjId="g_1c43940_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="521,-357 521,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c14d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-420 641,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21977@1" ObjectIDZND0="21978@1" Pin0InfoVect0LinkObjId="SW-116599_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116598_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-420 641,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c14fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-374 641,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21979@1" ObjectIDZND0="21977@0" Pin0InfoVect0LinkObjId="SW-116598_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116599_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-374 641,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20a6cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-422 762,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21969@1" ObjectIDZND0="21970@1" Pin0InfoVect0LinkObjId="SW-116593_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116592_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-422 762,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_efc390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-376 762,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21971@1" ObjectIDZND0="21969@0" Pin0InfoVect0LinkObjId="SW-116592_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116593_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-376 762,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21419c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-422 870,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21965@1" ObjectIDZND0="21966@1" Pin0InfoVect0LinkObjId="SW-116590_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116589_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-422 870,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18d6780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-376 870,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21967@1" ObjectIDZND0="21965@0" Pin0InfoVect0LinkObjId="SW-116589_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-376 870,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_290a450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-422 979,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21961@1" ObjectIDZND0="21962@1" Pin0InfoVect0LinkObjId="SW-116587_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116586_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-422 979,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_290a6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="979,-376 979,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21963@1" ObjectIDZND0="21961@0" Pin0InfoVect0LinkObjId="SW-116586_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116587_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="979,-376 979,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_fa4ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1100,-422 1100,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21957@1" ObjectIDZND0="21958@1" Pin0InfoVect0LinkObjId="SW-116584_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116583_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1100,-422 1100,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1734010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1100,-376 1100,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21959@1" ObjectIDZND0="21957@0" Pin0InfoVect0LinkObjId="SW-116583_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116584_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1100,-376 1100,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0c390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,-424 1227,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21953@1" ObjectIDZND0="21954@1" Pin0InfoVect0LinkObjId="SW-116581_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,-424 1227,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0c5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1227,-378 1227,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21955@1" ObjectIDZND0="21953@0" Pin0InfoVect0LinkObjId="SW-116580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116581_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1227,-378 1227,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_198cbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-419 1358,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21981@1" ObjectIDZND0="21982@1" Pin0InfoVect0LinkObjId="SW-116602_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116601_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-419 1358,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_198ce40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-373 1358,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21983@1" ObjectIDZND0="21981@0" Pin0InfoVect0LinkObjId="SW-116601_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116602_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-373 1358,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c66e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-618 821,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_2027ae0@0" ObjectIDND1="21950@x" ObjectIDZND0="21987@1" Pin0InfoVect0LinkObjId="g_ec7530_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2027ae0_0" Pin1InfoVect1LinkObjId="SW-116578_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-618 821,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15574b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-607 790,-618 821,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_2027ae0@0" ObjectIDZND0="21987@x" ObjectIDZND1="21950@x" Pin0InfoVect0LinkObjId="g_ec7530_0" Pin0InfoVect1LinkObjId="SW-116578_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2027ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="790,-607 790,-618 821,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1557710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-618 821,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="21987@x" ObjectIDND1="g_2027ae0@0" ObjectIDZND0="21950@0" Pin0InfoVect0LinkObjId="SW-116578_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_ec7530_0" Pin1InfoVect1LinkObjId="g_2027ae0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-618 821,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cc6a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1444,-543 1444,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21985@0" ObjectIDZND0="g_20a1280@0" ObjectIDZND1="g_f41cb0@0" Pin0InfoVect0LinkObjId="g_20a1280_0" Pin0InfoVect1LinkObjId="g_f41cb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1444,-543 1444,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cc6c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1444,-492 1444,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21986@0" ObjectIDZND0="21941@0" Pin0InfoVect0LinkObjId="g_1a4ef70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1444,-492 1444,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224a130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1444,-526 1444,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="21985@1" ObjectIDZND0="21986@1" Pin0InfoVect0LinkObjId="SW-116604_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-116604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1444,-526 1444,-509 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="651,-173 642,-161 633,-173 651,-173 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-93898" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 262.000000 -1118.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19750" ObjectName="DYN-CX_STP"/>
     <cge:Meas_Ref ObjectId="93898"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_AP" endPointId="0" endStationName="CX_STP" flowDrawDirect="1" flowShape="0" id="AC-220kV.taia_line" runFlow="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="821,-1162 821,-1187 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25324" ObjectName="AC-220kV.taia_line"/>
    <cge:TPSR_Ref TObjectID="25324_SS-149"/></metadata>
   <polyline fill="none" opacity="0" points="821,-1162 821,-1187 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_STP"/>
</svg>