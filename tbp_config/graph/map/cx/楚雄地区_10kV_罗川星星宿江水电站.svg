<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-275" aopId="787198" id="thSvg" product="E8000V2" version="1.0" viewBox="-2876 -2270 3119 1783">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape9">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="21" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="51" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="14" x2="22" y1="33" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="8" x2="18" y1="29" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="18" y1="36" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="0" y1="22" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="36" x2="28" y1="33" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="42" x2="32" y1="29" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="32" y1="36" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="51" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="21" x2="21" y1="13" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="28" x2="28" y1="13" y2="0"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape194">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="10" y1="8" y2="8"/>
    <polyline DF8003:Layer="PUBLIC" points="22,1 22,16 10,8 22,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="36" x2="22" y1="9" y2="9"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="nonConstantLoad:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="10" x2="10" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="18,19 10,31 2,19 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape2_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape2_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="20" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="20" y2="12"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="voltageTransformer:shape33">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="6" y2="6"/>
   </symbol>
   <symbol id="voltageTransformer:shape137">
    <ellipse cx="22" cy="27" fillStyle="0" rx="8" ry="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="13" y1="11" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="25" x2="23" y1="25" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="23" x2="20" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="25" x2="23" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="10" x2="8" y1="25" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="8" x2="5" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="10" x2="8" y1="29" y2="27"/>
    <ellipse cx="16" cy="15" fillStyle="0" rx="8" ry="8.5" stroke-width="1"/>
    <ellipse cx="9" cy="27" fillStyle="0" rx="8" ry="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_229fd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22a0d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22a1780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22a2300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22a3580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22a40a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22a4900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22a5380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22a5da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22a6730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22a7260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_22a7b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22a9750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22aa370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22aabc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22ab5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22acbc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c00320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1be1100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22af160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b0340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b0cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c2e370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22b6f00" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b79f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22b80f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22b4df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22b5890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_22c48b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d21800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1793" width="3129" x="-2881" y="-2275"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="-1790,-1132 -1804,-1132 -1797,-1120 -1790,-1132 " stroke="rgb(0,205,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="-1601,-1500 -1615,-1500 -1608,-1488 -1601,-1500 " stroke="rgb(0,205,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="-828,-1172 -842,-1172 -835,-1184 -828,-1172 " stroke="rgb(0,205,0)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-228651">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1532.000000 -1849.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37978" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_431"/>
     <cge:Meas_Ref ObjectId="228651"/>
    <cge:TPSR_Ref TObjectID="37978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228654">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2329.000000 -1602.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37989" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_434"/>
     <cge:Meas_Ref ObjectId="228654"/>
    <cge:TPSR_Ref TObjectID="37989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2411.000000 -1336.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228657">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2122.000000 -1602.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37988" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_433"/>
     <cge:Meas_Ref ObjectId="228657"/>
    <cge:TPSR_Ref TObjectID="37988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228661">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1618.000000 -1612.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37987" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_432"/>
     <cge:Meas_Ref ObjectId="228661"/>
    <cge:TPSR_Ref TObjectID="37987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228660">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1130.000000 -1609.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37995" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_435"/>
     <cge:Meas_Ref ObjectId="228660"/>
    <cge:TPSR_Ref TObjectID="37995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2020.000000 -1175.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228674">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1605.000000 -1175.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38001" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_612"/>
     <cge:Meas_Ref ObjectId="228674"/>
    <cge:TPSR_Ref TObjectID="38001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228671">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1421.000000 -1178.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38002" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_611"/>
     <cge:Meas_Ref ObjectId="228671"/>
    <cge:TPSR_Ref TObjectID="38002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228666">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1199.000000 -1324.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37997" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_436"/>
     <cge:Meas_Ref ObjectId="228666"/>
    <cge:TPSR_Ref TObjectID="37997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228667">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1115.000000 -1095.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37999" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_437"/>
     <cge:Meas_Ref ObjectId="228667"/>
    <cge:TPSR_Ref TObjectID="37999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228672">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -761.000000 -1192.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38003" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_211"/>
     <cge:Meas_Ref ObjectId="228672"/>
    <cge:TPSR_Ref TObjectID="38003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228673">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -587.000000 -1192.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38004" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_212"/>
     <cge:Meas_Ref ObjectId="228673"/>
    <cge:TPSR_Ref TObjectID="38004"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1cd93a0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -1433.000000 -1996.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1be6820">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 -2367.000000 -1248.000000)" xlink:href="#voltageTransformer:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bfb530">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 -1885.000000 -1538.000000)" xlink:href="#voltageTransformer:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd53e0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 -1683.000000 -1086.000000)" xlink:href="#voltageTransformer:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bdd7e0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1527.000000 -1047.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb6d70">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1343.000000 -1050.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bbd520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1146.000000 -1375.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22cba70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1039.000000 -1433.000000)" xlink:href="#voltageTransformer:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22dcd20">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -682.000000 -1047.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e5420">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -508.000000 -1047.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2426.000000 -1194.000000)" xlink:href="#capacitor:shape9"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1791.000000 -1510.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1791.000000 -1510.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1621.000000 -1507.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1621.000000 -1507.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1889.000000 -965.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1889.000000 -965.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -860.000000 -1073.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -860.000000 -1073.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1619.000000 -953.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1435.000000 -956.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -775.000000 -953.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -601.000000 -953.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_1d21990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="241,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="241,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c46b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1522,-2141 -1522,-2089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="nonConstantLoad" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="37977@1" Pin0InfoVect0LinkObjId="SW-228653_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1522,-2141 -1522,-2089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a7f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1522,-1857 -1522,-1812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37978@0" ObjectIDZND0="37976@1" Pin0InfoVect0LinkObjId="SW-228652_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228651_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1522,-1857 -1522,-1812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a81d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1522,-2004 -1499,-2004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="37978@x" ObjectIDND1="g_22a9d00@0" ObjectIDND2="37977@x" ObjectIDZND0="g_1cd93a0@0" Pin0InfoVect0LinkObjId="g_1cd93a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-228651_0" Pin1InfoVect1LinkObjId="g_22a9d00_0" Pin1InfoVect2LinkObjId="SW-228653_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1522,-2004 -1499,-2004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2225b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1522,-2004 -1522,-1884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_1cd93a0@0" ObjectIDND1="g_22a9d00@0" ObjectIDND2="37977@x" ObjectIDZND0="37978@1" Pin0InfoVect0LinkObjId="SW-228651_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1cd93a0_0" Pin1InfoVect1LinkObjId="g_22a9d00_0" Pin1InfoVect2LinkObjId="SW-228653_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1522,-2004 -1522,-1884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2225d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1522,-2039 -1585,-2039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="37977@x" ObjectIDND1="g_1cd93a0@0" ObjectIDND2="37978@x" ObjectIDZND0="g_22a9d00@0" Pin0InfoVect0LinkObjId="g_22a9d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-228653_0" Pin1InfoVect1LinkObjId="g_1cd93a0_0" Pin1InfoVect2LinkObjId="SW-228651_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1522,-2039 -1585,-2039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_222a2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1522,-2053 -1522,-2039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="breaker" ObjectIDND0="37977@0" ObjectIDZND0="g_22a9d00@0" ObjectIDZND1="g_1cd93a0@0" ObjectIDZND2="37978@x" Pin0InfoVect0LinkObjId="g_22a9d00_0" Pin0InfoVect1LinkObjId="g_1cd93a0_0" Pin0InfoVect2LinkObjId="SW-228651_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228653_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-1522,-2053 -1522,-2039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4c480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1522,-2039 -1522,-2004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="breaker" ObjectIDND0="g_22a9d00@0" ObjectIDND1="37977@x" ObjectIDZND0="g_1cd93a0@0" ObjectIDZND1="37978@x" Pin0InfoVect0LinkObjId="g_1cd93a0_0" Pin0InfoVect1LinkObjId="SW-228651_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22a9d00_0" Pin1InfoVect1LinkObjId="SW-228653_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-1522,-2039 -1522,-2004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be8e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2319,-1666 -2319,-1637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37980@0" ObjectIDZND0="37989@1" Pin0InfoVect0LinkObjId="SW-228654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2319,-1666 -2319,-1637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be90c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2319,-1594 -2272,-1594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="37989@x" ObjectIDND1="37981@x" ObjectIDZND0="g_1cd67c0@0" Pin0InfoVect0LinkObjId="g_1cd67c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-228654_0" Pin1InfoVect1LinkObjId="SW-228656_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2319,-1594 -2272,-1594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be9bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2319,-1610 -2319,-1594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37989@0" ObjectIDZND0="g_1cd67c0@0" ObjectIDZND1="37981@x" Pin0InfoVect0LinkObjId="g_1cd67c0_0" Pin0InfoVect1LinkObjId="SW-228656_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-2319,-1610 -2319,-1594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be9e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2319,-1594 -2319,-1573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_1cd67c0@0" ObjectIDND1="37989@x" ObjectIDZND0="37981@1" Pin0InfoVect0LinkObjId="SW-228656_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1cd67c0_0" Pin1InfoVect1LinkObjId="SW-228654_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2319,-1594 -2319,-1573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bea070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2257,-1448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="-2257,-1448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bea2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2319,-1516 -2401,-1516 -2401,-1496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="37981@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-228656_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2319,-1516 -2401,-1516 -2401,-1496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1beadc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2319,-1537 -2319,-1516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="37981@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-2319,-1537 -2319,-1516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1beb020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2319,-1516 -2319,-1444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="37981@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-228656_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2319,-1516 -2319,-1444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1beb280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2401,-1407 -2401,-1371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1c55480@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c55480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2401,-1407 -2401,-1371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1beb4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2401,-1344 -2401,-1317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1be4790@0" Pin0InfoVect0LinkObjId="g_1be4790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2401,-1344 -2401,-1317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1beb740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2401,-1264 -2362,-1264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" EndDevType0="voltageTransformer" ObjectIDND0="g_1be4790@0" ObjectIDND1="0@x" ObjectIDZND0="g_1be6820@0" Pin0InfoVect0LinkObjId="g_1be6820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1be4790_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2401,-1264 -2362,-1264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c54fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2401,-1285 -2401,-1264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="capacitor" ObjectIDND0="g_1be4790@1" ObjectIDZND0="g_1be6820@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1be6820_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1be4790_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-2401,-1285 -2401,-1264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c55220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2401,-1264 -2401,-1229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1be6820@0" ObjectIDND1="g_1be4790@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1be6820_0" Pin1InfoVect1LinkObjId="g_1be4790_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2401,-1264 -2401,-1229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c58480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2401,-1460 -2401,-1439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1c55480@0" Pin0InfoVect0LinkObjId="g_1c55480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2401,-1460 -2401,-1439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2298750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2112,-1666 -2112,-1637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37982@0" ObjectIDZND0="37988@1" Pin0InfoVect0LinkObjId="SW-228657_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228658_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2112,-1666 -2112,-1637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22989b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2112,-1594 -2067,-1594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="37988@x" ObjectIDND1="37986@x" ObjectIDZND0="g_2294650@0" Pin0InfoVect0LinkObjId="g_2294650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-228657_0" Pin1InfoVect1LinkObjId="SW-228659_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2112,-1594 -2067,-1594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2298c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2112,-1610 -2112,-1594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37988@0" ObjectIDZND0="g_2294650@0" ObjectIDZND1="37986@x" Pin0InfoVect0LinkObjId="g_2294650_0" Pin0InfoVect1LinkObjId="SW-228659_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228657_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-2112,-1610 -2112,-1594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2298e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2112,-1594 -2112,-1573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2294650@0" ObjectIDND1="37988@x" ObjectIDZND0="37986@1" Pin0InfoVect0LinkObjId="SW-228659_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2294650_0" Pin1InfoVect1LinkObjId="SW-228657_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2112,-1594 -2112,-1573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_22990d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2052,-1448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="-2052,-1448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2299bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2112,-1444 -2112,-1537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="37986@0" Pin0InfoVect0LinkObjId="SW-228659_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2112,-1444 -2112,-1537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bfcf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1901,-1576 -1901,-1533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1bfab60@1" ObjectIDZND0="g_1bfb530@0" Pin0InfoVect0LinkObjId="g_1bfb530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bfab60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1901,-1576 -1901,-1533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bfd1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1901,-1641 -1962,-1641 -1962,-1621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="37983@x" ObjectIDND1="g_1bfab60@0" ObjectIDZND0="g_1bf9e30@0" Pin0InfoVect0LinkObjId="g_1bf9e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-228665_0" Pin1InfoVect1LinkObjId="g_1bfab60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1901,-1641 -1962,-1641 -1962,-1621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bfdce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1901,-1664 -1901,-1641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="37983@0" ObjectIDZND0="g_1bf9e30@0" ObjectIDZND1="g_1bfab60@0" Pin0InfoVect0LinkObjId="g_1bf9e30_0" Pin0InfoVect1LinkObjId="g_1bfab60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-1901,-1664 -1901,-1641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bfdf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1901,-1641 -1901,-1608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1bf9e30@0" ObjectIDND1="37983@x" ObjectIDZND0="g_1bfab60@0" Pin0InfoVect0LinkObjId="g_1bfab60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bf9e30_0" Pin1InfoVect1LinkObjId="SW-228665_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1901,-1641 -1901,-1608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bfe1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1947,-1602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="-1947,-1602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_221eb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1778,-1664 -1778,-1636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="37985@0" ObjectIDZND0="g_221cf20@0" Pin0InfoVect0LinkObjId="g_221cf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228664_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1778,-1664 -1778,-1636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_221ed70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1778,-1604 -1778,-1557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_221cf20@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_221cf20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1778,-1604 -1778,-1557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ce4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1609,-1664 -1608,-1647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37984@0" ObjectIDZND0="37987@1" Pin0InfoVect0LinkObjId="SW-228661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1609,-1664 -1608,-1647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ce750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1608,-1620 -1608,-1554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="37987@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1608,-1620 -1608,-1554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d3480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1522,-1776 -1522,-1733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37976@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228652_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1522,-1776 -1522,-1733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d36e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1609,-1733 -1609,-1700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="37984@1" Pin0InfoVect0LinkObjId="SW-228662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1609,-1733 -1609,-1700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d3940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1778,-1733 -1778,-1700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="37985@1" Pin0InfoVect0LinkObjId="SW-228664_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1778,-1733 -1778,-1700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d3ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1901,-1733 -1901,-1700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="37983@1" Pin0InfoVect0LinkObjId="SW-228665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1901,-1733 -1901,-1700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d3e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2112,-1733 -2112,-1702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="37982@1" Pin0InfoVect0LinkObjId="SW-228658_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2112,-1733 -2112,-1702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21d4060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2319,-1733 -2319,-1702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="37980@1" Pin0InfoVect0LinkObjId="SW-228655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2319,-1733 -2319,-1702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bcc690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2010,-1248 -2010,-1210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2010,-1248 -2010,-1210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bcc8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1876,-1227 -1929,-1227 -1929,-1191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38005@x" ObjectIDND1="g_21db2e0@0" ObjectIDZND0="g_21dbcb0@0" Pin0InfoVect0LinkObjId="g_21dbcb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-228682_0" Pin1InfoVect1LinkObjId="g_21db2e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1876,-1227 -1929,-1227 -1929,-1191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bcd3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1876,-1247 -1876,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="38005@0" ObjectIDZND0="g_21dbcb0@0" ObjectIDZND1="g_21db2e0@0" Pin0InfoVect0LinkObjId="g_21dbcb0_0" Pin0InfoVect1LinkObjId="g_21db2e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-1876,-1247 -1876,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bcd610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1876,-1227 -1876,-1204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_21dbcb0@0" ObjectIDND1="38005@x" ObjectIDZND0="g_21db2e0@0" Pin0InfoVect0LinkObjId="g_21db2e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21dbcb0_0" Pin1InfoVect1LinkObjId="SW-228682_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1876,-1227 -1876,-1204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bcd870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1876,-1172 -1876,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_21db2e0@1" ObjectIDZND0="g_21dc9e0@1" Pin0InfoVect0LinkObjId="g_21dc9e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21db2e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1876,-1172 -1876,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bcdad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1876,-1057 -1876,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_21dc9e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21dc9e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1876,-1057 -1876,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bd4430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1699,-1222 -1752,-1222 -1752,-1186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38007@x" ObjectIDND1="g_1bd2d30@0" ObjectIDZND0="g_1bd3700@0" Pin0InfoVect0LinkObjId="g_1bd3700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-228679_0" Pin1InfoVect1LinkObjId="g_1bd2d30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1699,-1222 -1752,-1222 -1752,-1186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bd4690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1699,-1242 -1699,-1222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="38007@0" ObjectIDZND0="g_1bd3700@0" ObjectIDZND1="g_1bd2d30@0" Pin0InfoVect0LinkObjId="g_1bd3700_0" Pin0InfoVect1LinkObjId="g_1bd2d30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-1699,-1242 -1699,-1222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bd48f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1699,-1222 -1699,-1199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1bd3700@0" ObjectIDND1="38007@x" ObjectIDZND0="g_1bd2d30@0" Pin0InfoVect0LinkObjId="g_1bd2d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bd3700_0" Pin1InfoVect1LinkObjId="SW-228679_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1699,-1222 -1699,-1199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bd6e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1699,-1167 -1699,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_1bd2d30@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bd2d30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="-1699,-1167 -1699,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bdb970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2010,-1183 -2010,-1057 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="-2010,-1183 -2010,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bde590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1595,-1244 -1595,-1210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38012@0" ObjectIDZND0="38001@1" Pin0InfoVect0LinkObjId="SW-228674_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1595,-1244 -1595,-1210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bde7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1595,-1042 -1595,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_1bdbbd0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bdbbd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1595,-1042 -1595,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bdea50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1595,-1155 -1519,-1155 -1519,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="38001@x" ObjectIDND1="g_1bdbbd0@0" ObjectIDZND0="g_1bdd7e0@0" Pin0InfoVect0LinkObjId="g_1bdd7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-228674_0" Pin1InfoVect1LinkObjId="g_1bdbbd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1595,-1155 -1519,-1155 -1519,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bdf540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1595,-1183 -1595,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="38001@0" ObjectIDZND0="g_1bdd7e0@0" ObjectIDZND1="g_1bdbbd0@0" Pin0InfoVect0LinkObjId="g_1bdd7e0_0" Pin0InfoVect1LinkObjId="g_1bdbbd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228674_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-1595,-1183 -1595,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bdf7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1595,-1155 -1595,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_1bdd7e0@0" ObjectIDND1="38001@x" ObjectIDZND0="g_1bdbbd0@1" Pin0InfoVect0LinkObjId="g_1bdbbd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bdd7e0_0" Pin1InfoVect1LinkObjId="SW-228674_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1595,-1155 -1595,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bb7b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1411,-1247 -1411,-1213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38011@0" ObjectIDZND0="38002@1" Pin0InfoVect0LinkObjId="SW-228671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1411,-1247 -1411,-1213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bb7d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1411,-1045 -1411,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_1bb5160@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bb5160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1411,-1045 -1411,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bb7fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1411,-1158 -1335,-1158 -1335,-1116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="38002@x" ObjectIDND1="g_1bb5160@0" ObjectIDZND0="g_1bb6d70@0" Pin0InfoVect0LinkObjId="g_1bb6d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-228671_0" Pin1InfoVect1LinkObjId="g_1bb5160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1411,-1158 -1335,-1158 -1335,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bb8240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1411,-1186 -1411,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="38002@0" ObjectIDZND0="g_1bb6d70@0" ObjectIDZND1="g_1bb5160@0" Pin0InfoVect0LinkObjId="g_1bb6d70_0" Pin0InfoVect1LinkObjId="g_1bb5160_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-1411,-1186 -1411,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bb84a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1411,-1158 -1411,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_1bb6d70@0" ObjectIDND1="38002@x" ObjectIDZND0="g_1bb5160@1" Pin0InfoVect0LinkObjId="g_1bb5160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bb6d70_0" Pin1InfoVect1LinkObjId="SW-228671_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1411,-1158 -1411,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bb9190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2010,-1315 -2010,-1284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-2010,-1315 -2010,-1284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bb93f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1877,-1315 -1877,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="38005@1" Pin0InfoVect0LinkObjId="SW-228682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1877,-1315 -1877,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bb9650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1797,-1315 -1797,-1282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="38006@1" Pin0InfoVect0LinkObjId="SW-228678_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1797,-1315 -1797,-1282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bb98b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1699,-1315 -1699,-1279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="38007@1" Pin0InfoVect0LinkObjId="SW-228679_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1699,-1315 -1699,-1279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bb9b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1595,-1315 -1595,-1281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="38012@1" Pin0InfoVect0LinkObjId="SW-228681_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1595,-1315 -1595,-1281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bb9d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1411,-1315 -1411,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="38011@1" Pin0InfoVect0LinkObjId="SW-228675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1411,-1315 -1411,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc2ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1120,-1617 -1120,-1567 -1189,-1567 -1189,-1528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37995@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1120,-1617 -1120,-1567 -1189,-1567 -1189,-1528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc2e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1189,-1332 -1189,-1294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37997@0" ObjectIDZND0="37996@1" Pin0InfoVect0LinkObjId="SW-228668_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1189,-1332 -1189,-1294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc3070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1189,-1466 -1240,-1466 -1240,-1497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1bbd520@0" ObjectIDND2="37997@x" ObjectIDZND0="g_1bb9fd0@0" Pin0InfoVect0LinkObjId="g_1bb9fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1bbd520_0" Pin1InfoVect2LinkObjId="SW-228666_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1189,-1466 -1240,-1466 -1240,-1497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc3b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1189,-1492 -1189,-1466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_1bb9fd0@0" ObjectIDZND1="g_1bbd520@0" ObjectIDZND2="37997@x" Pin0InfoVect0LinkObjId="g_1bb9fd0_0" Pin0InfoVect1LinkObjId="g_1bbd520_0" Pin0InfoVect2LinkObjId="SW-228666_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-1189,-1492 -1189,-1466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc3dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1189,-1453 -1138,-1453 -1138,-1441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="voltageTransformer" ObjectIDND0="g_1bb9fd0@0" ObjectIDND1="0@x" ObjectIDND2="37997@x" ObjectIDZND0="g_1bbd520@0" Pin0InfoVect0LinkObjId="g_1bbd520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bb9fd0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-228666_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1189,-1453 -1138,-1453 -1138,-1441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc48b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1189,-1466 -1189,-1453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="breaker" ObjectIDND0="g_1bb9fd0@0" ObjectIDND1="0@x" ObjectIDZND0="g_1bbd520@0" ObjectIDZND1="37997@x" Pin0InfoVect0LinkObjId="g_1bbd520_0" Pin0InfoVect1LinkObjId="SW-228666_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bb9fd0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-1189,-1466 -1189,-1453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc4b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1189,-1453 -1189,-1359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_1bbd520@0" ObjectIDND1="g_1bb9fd0@0" ObjectIDND2="0@x" ObjectIDZND0="37997@1" Pin0InfoVect0LinkObjId="SW-228666_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bbd520_0" Pin1InfoVect1LinkObjId="g_1bb9fd0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1189,-1453 -1189,-1359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cdea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1023,-1438 -1023,-1389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_22cba70@0" ObjectIDZND0="g_22cd4d0@0" Pin0InfoVect0LinkObjId="g_22cd4d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22cba70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1023,-1438 -1023,-1389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ce100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1023,-1331 -939,-1331 -939,-1353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_22cd4d0@0" ObjectIDND1="37998@x" ObjectIDZND0="g_22cad80@0" Pin0InfoVect0LinkObjId="g_22cad80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22cd4d0_0" Pin1InfoVect1LinkObjId="SW-228669_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1023,-1331 -939,-1331 -939,-1353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cebf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1023,-1357 -1023,-1331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_22cd4d0@1" ObjectIDZND0="g_22cad80@0" ObjectIDZND1="37998@x" Pin0InfoVect0LinkObjId="g_22cad80_0" Pin0InfoVect1LinkObjId="SW-228669_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22cd4d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-1023,-1357 -1023,-1331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cee50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1023,-1331 -1023,-1297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_22cad80@0" ObjectIDND1="g_22cd4d0@0" ObjectIDZND0="37998@1" Pin0InfoVect0LinkObjId="SW-228669_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22cad80_0" Pin1InfoVect1LinkObjId="g_22cd4d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1023,-1331 -1023,-1297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cf2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1189,-1258 -1189,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37996@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228668_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1189,-1258 -1189,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22cf510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1023,-1261 -1023,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37998@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228669_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1023,-1261 -1023,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22ddad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-751,-1042 -751,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_22db110@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22db110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-751,-1042 -751,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22ddd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-751,-1265 -751,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38009@0" ObjectIDZND0="38003@1" Pin0InfoVect0LinkObjId="SW-228672_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-751,-1265 -751,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22ddf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-751,-1162 -674,-1162 -674,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="38003@x" ObjectIDND1="g_22db110@0" ObjectIDZND0="g_22dcd20@0" Pin0InfoVect0LinkObjId="g_22dcd20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-228672_0" Pin1InfoVect1LinkObjId="g_22db110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-751,-1162 -674,-1162 -674,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22dea80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-751,-1200 -751,-1162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="38003@0" ObjectIDZND0="g_22dcd20@0" ObjectIDZND1="g_22db110@0" Pin0InfoVect0LinkObjId="g_22dcd20_0" Pin0InfoVect1LinkObjId="g_22db110_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-751,-1200 -751,-1162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22dece0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-751,-1162 -751,-1155 -751,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_22dcd20@0" ObjectIDND1="38003@x" ObjectIDZND0="g_22db110@1" Pin0InfoVect0LinkObjId="g_22db110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22dcd20_0" Pin1InfoVect1LinkObjId="SW-228672_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-751,-1162 -751,-1155 -751,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e61d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-577,-1042 -577,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_22e3810@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22e3810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-577,-1042 -577,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e6430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-577,-1265 -577,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38010@0" ObjectIDZND0="38004@1" Pin0InfoVect0LinkObjId="SW-228673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228677_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-577,-1265 -577,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e6690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-577,-1162 -500,-1162 -500,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="38004@x" ObjectIDND1="g_22e3810@0" ObjectIDZND0="g_22e5420@0" Pin0InfoVect0LinkObjId="g_22e5420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-228673_0" Pin1InfoVect1LinkObjId="g_22e3810_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-577,-1162 -500,-1162 -500,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e68f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-577,-1200 -577,-1162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="38004@0" ObjectIDZND0="g_22e5420@0" ObjectIDZND1="g_22e3810@0" Pin0InfoVect0LinkObjId="g_22e5420_0" Pin0InfoVect1LinkObjId="g_22e3810_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-577,-1200 -577,-1162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e6b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-577,-1162 -577,-1155 -577,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_22e5420@0" ObjectIDND1="38004@x" ObjectIDZND0="g_22e3810@1" Pin0InfoVect0LinkObjId="g_22e3810_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22e5420_0" Pin1InfoVect1LinkObjId="SW-228673_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-577,-1162 -577,-1155 -577,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e7840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-836,-1301 -836,-1343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38008@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-836,-1301 -836,-1343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e7aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-751,-1301 -751,-1343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38009@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228676_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-751,-1301 -751,-1343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e7d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-577,-1301 -577,-1343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38010@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228677_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-577,-1301 -577,-1343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e9c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-836,-1265 -836,-1229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38008@0" ObjectIDZND0="g_22e9200@1" Pin0InfoVect0LinkObjId="g_22e9200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-836,-1265 -836,-1229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22e9ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-835,-1200 -835,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_22e9200@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22e9200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-835,-1200 -835,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ea100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1105,-1195 -1105,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38000@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1105,-1195 -1105,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ea360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1105,-1159 -1105,-1130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38000@0" ObjectIDZND0="37999@1" Pin0InfoVect0LinkObjId="SW-228667_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1105,-1159 -1105,-1130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ea5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1105,-1103 -1105,-1035 -835,-1035 -835,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="37999@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228667_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1105,-1103 -1105,-1035 -835,-1035 -835,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22ea830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1608,-1511 -1608,-1372 -2146,-1372 -2146,-866 -1797,-866 -1797,-1246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="38006@0" Pin0InfoVect0LinkObjId="SW-228678_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1608,-1511 -1608,-1372 -2146,-1372 -2146,-866 -1797,-866 -1797,-1246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f1f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1120,-1644 -1120,-1665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37995@1" ObjectIDZND0="37994@0" Pin0InfoVect0LinkObjId="SW-228663_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228660_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1120,-1644 -1120,-1665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f2180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1120,-1701 -1120,-1733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37994@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228663_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1120,-1701 -1120,-1733 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1522" cy="-1733" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1609" cy="-1733" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1778" cy="-1733" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1901" cy="-1733" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-2112" cy="-1733" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-2319" cy="-1733" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-2010" cy="-1315" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1877" cy="-1315" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1797" cy="-1315" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1699" cy="-1315" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1595" cy="-1315" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1411" cy="-1315" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1189" cy="-1226" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1023" cy="-1226" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-836" cy="-1343" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-751" cy="-1343" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-577" cy="-1343" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1105" cy="-1226" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-1120" cy="-1733" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22a5050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1898.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22a5050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1898.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22a5050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1898.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22a5050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1898.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22a5050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1898.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22a5050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1898.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22a5050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1898.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2837.000000 -1460.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="22" graphid="g_1bfffc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2708.000000 -2241.500000) translate(0,18)">星宿江水电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22eaab0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -602.000000 -939.458333) translate(0,15)">250kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22ebf40" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -777.000000 -943.458333) translate(0,15)">250kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22ec3d0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -703.000000 -908.458333) translate(0,15)">Ⅱ级发电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22ed3e0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -561.000000 -1224.875000) translate(0,15)">212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22edaa0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -566.000000 -1290.875000) translate(0,15)">2121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22edcd0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -735.000000 -1223.875000) translate(0,15)">211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22edf10" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -741.000000 -1292.875000) translate(0,15)">2111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22ee150" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -826.000000 -1290.875000) translate(0,15)">2131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22ee690" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -954.000000 -1156.458333) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22ee690" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -954.000000 -1156.458333) translate(0,33)">S7-630/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f0230" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1091.000000 -1125.875000) translate(0,15)">437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f0780" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1094.000000 -1184.875000) translate(0,15)">4371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f09d0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1014.000000 -1283.875000) translate(0,15)">4381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f0f10" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1178.000000 -1283.875000) translate(0,15)">4361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f1180" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1176.000000 -1353.875000) translate(0,15)">436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f13c0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1177.000000 -1521.875000) translate(0,15)">#1杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f1b10" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1103.000000 -1639.875000) translate(0,15)">435</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f2370" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1108.000000 -1689.875000) translate(0,15)">4351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f2690" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1439.000000 -944.458333) translate(0,15)">400kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f28d0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1620.000000 -941.458333) translate(0,15)">400kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f2b10" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1394.000000 -1209.875000) translate(0,15)">611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f2d50" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1398.000000 -1271.875000) translate(0,15)">6111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f2f90" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1578.000000 -1205.875000) translate(0,15)">612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f31d0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1582.000000 -1271.875000) translate(0,15)">6121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f3410" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1687.000000 -1271.875000) translate(0,15)">6211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f3650" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1788.000000 -1270.875000) translate(0,15)">6212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f3890" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1868.000000 -1270.875000) translate(0,15)">4381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f3ad0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1904.000000 -958.458333) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f3f20" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2023.000000 -1044.458333) translate(0,15)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f4480" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2036.000000 -1340.458333) translate(0,15)">6.3kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f52a0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -807.000000 -1375.458333) translate(0,15)">0.4kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f5700" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1584.000000 -1550.458333) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f5700" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1584.000000 -1550.458333) translate(0,33)">S7-1250/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f5970" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1544.000000 -907.458333) translate(0,15)">Ⅰ级发电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f5e10" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1799.000000 -1501.458333) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f6060" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1982.000000 -1492.458333) translate(0,15)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f6d40" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1594.000000 -1643.875000) translate(0,15)">432</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f7180" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1597.000000 -1688.875000) translate(0,15)">4321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f73c0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1767.000000 -1689.875000) translate(0,15)">4381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f7600" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1892.000000 -1689.875000) translate(0,15)">4371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f7840" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2099.000000 -1688.875000) translate(0,15)">4331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f7a80" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2098.000000 -1632.875000) translate(0,15)">433</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f7cc0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2172.000000 -1539.875000) translate(0,15)">#61杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f7f00" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2171.000000 -1572.875000) translate(0,15)">4336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f8140" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2365.000000 -1508.875000) translate(0,15)">#1杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f8380" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2142.000000 -1416.458333) translate(0,15)">罗川线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f8b20" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2353.000000 -1413.458333) translate(0,15)">水泥厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f96b0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2425.000000 -1180.458333) translate(0,15)">电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f9c30" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2312.000000 -1691.875000) translate(0,15)">4341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f9f00" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2378.000000 -1573.875000) translate(0,15)">4346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22fa140" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2308.000000 -1633.875000) translate(0,15)">434</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22fa380" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1514.000000 -2079.875000) translate(0,15)">4313</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22fa5c0" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1508.000000 -1880.875000) translate(0,15)">431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22fa800" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1513.000000 -1799.875000) translate(0,15)">4311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2301d60" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -1569.000000 -2192.875000) translate(0,15)">10kV白星线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2302d30" transform="matrix(1.000000 0.000000 -0.000000 1.041667 -2432.000000 -1767.458333) translate(0,15)">10kV母线</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-2865" y="-2246"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-2875" y="-1954"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-2865" y="-1470"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2468,-1733 -1016,-1733 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="-2468,-1733 -1016,-1733 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-2078,-1315 -1294,-1315 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="-2078,-1315 -1294,-1315 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1240,-1226 -973,-1226 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="-1240,-1226 -973,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-856,-1343 -515,-1343 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="-856,-1343 -515,-1343 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="NonConstantLoad_Layer">
   <g DF8003:Layer="PUBLIC">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1532.000000 -2136.000000)" xlink:href="#nonConstantLoad:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -2746.000000 -2167.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图10.svg" style="fill-opacity:0"><rect height="65" qtmmishow="hidden" width="259" x="-2793" y="-2264"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-2797" y="-2270"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="-1648" x2="-433" y1="-1608" y2="-1608"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="-432" x2="-432" y1="-1607" y2="-828"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="-2177" x2="-432" y1="-828" y2="-828"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="-2178" x2="-2178" y1="-1382" y2="-829"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="-2178" x2="-1650" y1="-1383" y2="-1383"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="-1649" x2="-1649" y1="-1607" y2="-1383"/>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2324.000000 -1423.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2117.000000 -1423.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="65" qtmmishow="hidden" width="259" x="-2793" y="-2264"/>
    </a>
   <metadata/><rect fill="white" height="65" opacity="0" stroke="white" transform="" width="259" x="-2793" y="-2264"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-2797" y="-2270"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-2797" y="-2270"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-228652">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1531.000000 -1771.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37976" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4311"/>
     <cge:Meas_Ref ObjectId="228652"/>
    <cge:TPSR_Ref TObjectID="37976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228653">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1531.000000 -2048.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37977" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4313"/>
     <cge:Meas_Ref ObjectId="228653"/>
    <cge:TPSR_Ref TObjectID="37977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228655">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2328.000000 -1661.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37980" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4341"/>
     <cge:Meas_Ref ObjectId="228655"/>
    <cge:TPSR_Ref TObjectID="37980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228656">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2328.000000 -1532.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37981" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4346"/>
     <cge:Meas_Ref ObjectId="228656"/>
    <cge:TPSR_Ref TObjectID="37981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2410.000000 -1455.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228658">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2121.000000 -1661.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37982" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4331"/>
     <cge:Meas_Ref ObjectId="228658"/>
    <cge:TPSR_Ref TObjectID="37982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228659">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2121.000000 -1532.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37986" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4336"/>
     <cge:Meas_Ref ObjectId="228659"/>
    <cge:TPSR_Ref TObjectID="37986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1910.000000 -1659.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37983" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4371hg"/>
     <cge:Meas_Ref ObjectId="228665"/>
    <cge:TPSR_Ref TObjectID="37983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228664">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1787.000000 -1659.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37985" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4381zy"/>
     <cge:Meas_Ref ObjectId="228664"/>
    <cge:TPSR_Ref TObjectID="37985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228662">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1618.000000 -1659.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37984" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4321"/>
     <cge:Meas_Ref ObjectId="228662"/>
    <cge:TPSR_Ref TObjectID="37984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228663">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1129.000000 -1660.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37994" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4351"/>
     <cge:Meas_Ref ObjectId="228663"/>
    <cge:TPSR_Ref TObjectID="37994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2019.000000 -1243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228682">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1886.000000 -1242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38005" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4381zy6"/>
     <cge:Meas_Ref ObjectId="228682"/>
    <cge:TPSR_Ref TObjectID="38005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228678">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1806.000000 -1241.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38006" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_6212"/>
     <cge:Meas_Ref ObjectId="228678"/>
    <cge:TPSR_Ref TObjectID="38006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228679">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1708.000000 -1238.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38007" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_6211"/>
     <cge:Meas_Ref ObjectId="228679"/>
    <cge:TPSR_Ref TObjectID="38007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228681">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1604.000000 -1240.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38012" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_6121"/>
     <cge:Meas_Ref ObjectId="228681"/>
    <cge:TPSR_Ref TObjectID="38012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228675">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1420.000000 -1242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38011" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_6111"/>
     <cge:Meas_Ref ObjectId="228675"/>
    <cge:TPSR_Ref TObjectID="38011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1198.000000 -1487.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228668">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1198.000000 -1253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37996" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4361"/>
     <cge:Meas_Ref ObjectId="228668"/>
    <cge:TPSR_Ref TObjectID="37996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228669">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1032.000000 -1256.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37998" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4381"/>
     <cge:Meas_Ref ObjectId="228669"/>
    <cge:TPSR_Ref TObjectID="37998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228670">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1114.000000 -1154.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38000" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_4371"/>
     <cge:Meas_Ref ObjectId="228670"/>
    <cge:TPSR_Ref TObjectID="38000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228676">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -760.000000 -1260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38009" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_2111"/>
     <cge:Meas_Ref ObjectId="228676"/>
    <cge:TPSR_Ref TObjectID="38009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228680">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -845.000000 -1260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38008" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_2131"/>
     <cge:Meas_Ref ObjectId="228680"/>
    <cge:TPSR_Ref TObjectID="38008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228677">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -586.000000 -1260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38010" ObjectName="SW-CX_SDZ.CX_SDZ_XSJ_2121"/>
     <cge:Meas_Ref ObjectId="228677"/>
    <cge:TPSR_Ref TObjectID="38010"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_22a9d00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1592.000000 -2035.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cd67c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2279.000000 -1540.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1be4790">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2410.000000 -1280.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c55480">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2410.000000 -1402.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2294650">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -2074.000000 -1540.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf9e30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1969.000000 -1567.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bfab60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1910.000000 -1571.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_221cf20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1787.000000 -1599.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21db2e0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1885.000000 -1167.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21dbcb0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1936.000000 -1137.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21dc9e0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1881.000000 -1052.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd2d30">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1708.000000 -1162.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd3700">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1759.000000 -1132.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bdbbd0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1600.000000 -1037.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb5160">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1416.000000 -1040.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb9fd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1247.000000 -1493.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22cad80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -946.000000 -1349.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22cd4d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1032.000000 -1352.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22db110">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -756.000000 -1037.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e3810">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -582.000000 -1037.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22e9200">
    <use class="BV-6KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 -844.000000 -1234.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>