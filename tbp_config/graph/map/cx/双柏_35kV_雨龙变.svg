<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-14" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-269 -1181 2114 1222">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape186">
    <circle cx="15" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="41" x2="39" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="43" x2="37" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="34" x2="46" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="19" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="57" y2="53"/>
    <circle cx="15" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="43" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,16 21,28 9,28 15,16 15,16 15,16 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,59 40,59 40,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="53" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="10" y1="87" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="18" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="14" y1="81" y2="87"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape192">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="26" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="5,19 17,9 5,0 5,19 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,1 26,10 38,19 38,1 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape208">
    <circle cx="7" cy="21" fillStyle="0" r="6" stroke-width="0.431185"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="3,17 11,25 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="2,24 11,17 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.29293" x1="7" x2="7" y1="27" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.895105" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape57_0">
    <circle cx="16" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="50" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="16,12 22,25 10,25 16,12 16,13 16,12 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="43" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="79" y2="74"/>
   </symbol>
   <symbol id="transformer2:shape57_1">
    <circle cx="16" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,55 41,55 41,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="55" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="55" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="55" y2="60"/>
   </symbol>
   <symbol id="voltageTransformer:shape64">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="38" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="8" y1="16" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="17" y1="24" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="10" y1="17" y2="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape85">
    <circle cx="20" cy="32" fillStyle="0" r="8" stroke-width="0.570276"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="11" x2="13" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="13" x2="16" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="11" x2="13" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="25" x2="27" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="27" x2="30" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="25" x2="27" y1="16" y2="20"/>
    <circle cx="13" cy="20" fillStyle="0" r="8" stroke-width="0.570276"/>
    <circle cx="27" cy="20" fillStyle="0" r="8" stroke-width="0.570276"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="17" x2="19" y1="37" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.960801" x1="21" x2="21" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.989747" x1="18" x2="21" y1="5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.992459" x1="18" x2="21" y1="10" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="19" x2="22" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="17" x2="19" y1="30" y2="34"/>
    <circle cx="20" cy="8" fillStyle="0" r="8" stroke-width="0.536731"/>
   </symbol>
   <symbol id="voltageTransformer:shape65">
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="46" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="18" y2="20"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="5" x2="9" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="5" x2="9" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="9" x2="9" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="41" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="38"/>
    <rect height="13" stroke-width="1" width="7" x="32" y="17"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="27,8 35,8 35,18 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="38" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="40" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37689c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37691e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3769be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_376aac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_376bde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_376c9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376d430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376dc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376e500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376ee90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376ee90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37704d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37704d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3771010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3772cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3773900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3774150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3774b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3776150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3776940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3776fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_37779c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3778ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3779520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_377a010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_377f460" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3780140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_377bd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_377d2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_377e030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3781530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3781b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1232" width="2124" x="-274" y="-1186"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1615" x2="1615" y1="-708" y2="-611"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1721" x2="1615" y1="-650" y2="-650"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1721" x2="1721" y1="-650" y2="-606"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1778" x2="1778" y1="-706" y2="-609"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1666" x2="1778" y1="-622" y2="-622"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1666" x2="1666" y1="-622" y2="-609"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1615" x2="1615" y1="-584" y2="-569"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.980769" x1="1615" x2="1666" y1="-569" y2="-569"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1666" x2="1666" y1="-569" y2="-582"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1721" x2="1778" y1="-568" y2="-568"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1778" x2="1778" y1="-568" y2="-582"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1721" x2="1721" y1="-579" y2="-568"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1641" x2="1641" y1="-569" y2="-532"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1752" x2="1752" y1="-568" y2="-529"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="6" x1="1558" x2="1697" y1="-529" y2="-529"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="6" x1="1725" x2="1844" y1="-529" y2="-529"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-159729">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.985640 -303.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26522" ObjectName="SW-SB_YL.SB_YL_081BK"/>
     <cge:Meas_Ref ObjectId="159729"/>
    <cge:TPSR_Ref TObjectID="26522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159568">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 574.556647 -885.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26496" ObjectName="SW-SB_YL.SB_YL_382BK"/>
     <cge:Meas_Ref ObjectId="159568"/>
    <cge:TPSR_Ref TObjectID="26496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159509">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.556647 -884.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26490" ObjectName="SW-SB_YL.SB_YL_381BK"/>
     <cge:Meas_Ref ObjectId="159509"/>
    <cge:TPSR_Ref TObjectID="26490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159783">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1045.000000 -477.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26527" ObjectName="SW-SB_YL.SB_YL_012BK"/>
     <cge:Meas_Ref ObjectId="159783"/>
    <cge:TPSR_Ref TObjectID="26527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159386">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 -708.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26484" ObjectName="SW-SB_YL.SB_YL_301BK"/>
     <cge:Meas_Ref ObjectId="159386"/>
    <cge:TPSR_Ref TObjectID="26484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159419">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 -469.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26486" ObjectName="SW-SB_YL.SB_YL_001BK"/>
     <cge:Meas_Ref ObjectId="159419"/>
    <cge:TPSR_Ref TObjectID="26486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159700">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 505.985640 -303.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26517" ObjectName="SW-SB_YL.SB_YL_082BK"/>
     <cge:Meas_Ref ObjectId="159700"/>
    <cge:TPSR_Ref TObjectID="26517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159613">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.985640 -302.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26502" ObjectName="SW-SB_YL.SB_YL_083BK"/>
     <cge:Meas_Ref ObjectId="159613"/>
    <cge:TPSR_Ref TObjectID="26502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159671">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.985640 -302.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26512" ObjectName="SW-SB_YL.SB_YL_091BK"/>
     <cge:Meas_Ref ObjectId="159671"/>
    <cge:TPSR_Ref TObjectID="26512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159642">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1406.985640 -303.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26507" ObjectName="SW-SB_YL.SB_YL_092BK"/>
     <cge:Meas_Ref ObjectId="159642"/>
    <cge:TPSR_Ref TObjectID="26507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160364">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1769.000000 -573.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26688" ObjectName="SW-SB_YL.SB_YL_Zyb2BK_S"/>
     <cge:Meas_Ref ObjectId="160364"/>
    <cge:TPSR_Ref TObjectID="26688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160363">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1606.000000 -575.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26689" ObjectName="SW-SB_YL.SB_YL_Zyb1BK_S"/>
     <cge:Meas_Ref ObjectId="160363"/>
    <cge:TPSR_Ref TObjectID="26689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160362">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1657.000000 -573.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26687" ObjectName="SW-SB_YL.SB_YL_Zyb2BK"/>
     <cge:Meas_Ref ObjectId="160362"/>
    <cge:TPSR_Ref TObjectID="26687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160365">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1712.000000 -570.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26690" ObjectName="SW-SB_YL.SB_YL_Zyb1BK"/>
     <cge:Meas_Ref ObjectId="160365"/>
    <cge:TPSR_Ref TObjectID="26690"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_4148ee0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 530.000000 -1052.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41885a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1285.000000 -1072.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33fb6a0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1019.500000 -1012.500000)" xlink:href="#voltageTransformer:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_340a0d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 288.000000 -581.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_412ac50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1468.000000 -580.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_YL" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_damaidiTyl" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="584,-1096 584,-1144 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37759" ObjectName="AC-35kV.LN_damaidiTyl"/>
    <cge:TPSR_Ref TObjectID="37759_SS-14"/></metadata>
   <polyline fill="none" opacity="0" points="584,-1096 584,-1144 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="SB_YL" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yulong_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1338,-1099 1338,-1133 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37757" ObjectName="AC-35kV.LN_yulong_line"/>
    <cge:TPSR_Ref TObjectID="37757_SS-14"/></metadata>
   <polyline fill="none" opacity="0" points="1338,-1099 1338,-1133 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-SB_YL.SB_YL_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="37542"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 530.000000 -619.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 530.000000 -619.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26530" ObjectName="TF-SB_YL.SB_YL_1T"/>
    <cge:TPSR_Ref TObjectID="26530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_YL.SB_YL_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="37670"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 -865.000000)" xlink:href="#transformer2:shape57_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 -865.000000)" xlink:href="#transformer2:shape57_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26607" ObjectName="TF-SB_YL.SB_YL_Zyb1"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_4135b60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 608.000000 -1089.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_412b5f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1363.000000 -1088.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_340b730">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 992.000000 -943.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_418d150">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1025.000000 -949.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3418780">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 301.000000 -520.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_341f000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.000000 -439.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40d7330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 337.000000 -57.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40cb280">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1481.000000 -519.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40ec920">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1532.000000 -438.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4161c70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 605.000000 -495.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4151770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 346.000000 -173.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4138700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 476.000000 -56.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4125370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 739.000000 -57.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41296e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1185.000000 -67.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_442dc90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1377.000000 -58.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4497760">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1134.000000 22.000000)" xlink:href="#lightningRod:shape186"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4499510">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1763.000000 -663.000000)" xlink:href="#lightningRod:shape186"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44a3960">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1206.000000 -974.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44a47a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 558.000000 -568.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44a57e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.000000 -179.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44a97d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 505.000000 -181.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44ab600">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 485.000000 -173.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44ae030">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 -180.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44af110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 748.000000 -172.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44b0b90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1406.000000 -181.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44b2c20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1121.000000 -83.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44b4440">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1158.000000 -144.000000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44b55e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1194.000000 -172.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44b72c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1387.000000 -173.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44b7ff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 425.000000 -181.000000)" xlink:href="#lightningRod:shape208"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44b91e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 567.000000 -181.000000)" xlink:href="#lightningRod:shape208"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44ba3d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 -180.000000)" xlink:href="#lightningRod:shape208"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44bb5c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1290.000000 -180.000000)" xlink:href="#lightningRod:shape208"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44bc7b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1468.000000 -181.000000)" xlink:href="#lightningRod:shape208"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44c2110">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1600.000000 -665.000000)" xlink:href="#lightningRod:shape186"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -161.000000 -1001.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-159215" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -131.000000 -822.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159215" ObjectName="SB_YL:SB_YL_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-159216" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -131.000000 -778.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159216" ObjectName="SB_YL:SB_YL_301BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-159215" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -134.000000 -905.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159215" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-159215" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -133.000000 -864.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159215" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 709.000000 -959.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26496"/>
     <cge:Term_Ref ObjectID="37472"/>
    <cge:TPSR_Ref TObjectID="26496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 709.000000 -959.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26496"/>
     <cge:Term_Ref ObjectID="37472"/>
    <cge:TPSR_Ref TObjectID="26496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159232" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 709.000000 -959.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26496"/>
     <cge:Term_Ref ObjectID="37472"/>
    <cge:TPSR_Ref TObjectID="26496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159228" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.047814 -0.000000 -0.000000 1.000000 1471.916667 -962.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26490"/>
     <cge:Term_Ref ObjectID="37460"/>
    <cge:TPSR_Ref TObjectID="26490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159229" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.047814 -0.000000 -0.000000 1.000000 1471.916667 -962.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159229" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26490"/>
     <cge:Term_Ref ObjectID="37460"/>
    <cge:TPSR_Ref TObjectID="26490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159225" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.047814 -0.000000 -0.000000 1.000000 1471.916667 -962.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159225" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26490"/>
     <cge:Term_Ref ObjectID="37460"/>
    <cge:TPSR_Ref TObjectID="26490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159266" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159266" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26522"/>
     <cge:Term_Ref ObjectID="37524"/>
    <cge:TPSR_Ref TObjectID="26522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159267" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159267" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26522"/>
     <cge:Term_Ref ObjectID="37524"/>
    <cge:TPSR_Ref TObjectID="26522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 382.000000 -23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26522"/>
     <cge:Term_Ref ObjectID="37524"/>
    <cge:TPSR_Ref TObjectID="26522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 526.000000 -23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26517"/>
     <cge:Term_Ref ObjectID="37514"/>
    <cge:TPSR_Ref TObjectID="26517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159261" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 526.000000 -23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26517"/>
     <cge:Term_Ref ObjectID="37514"/>
    <cge:TPSR_Ref TObjectID="26517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159257" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 526.000000 -23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159257" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26517"/>
     <cge:Term_Ref ObjectID="37514"/>
    <cge:TPSR_Ref TObjectID="26517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159242" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 -23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159242" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26502"/>
     <cge:Term_Ref ObjectID="37484"/>
    <cge:TPSR_Ref TObjectID="26502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159243" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 -23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159243" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26502"/>
     <cge:Term_Ref ObjectID="37484"/>
    <cge:TPSR_Ref TObjectID="26502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159239" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 -23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26502"/>
     <cge:Term_Ref ObjectID="37484"/>
    <cge:TPSR_Ref TObjectID="26502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159254" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1253.000000 -23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159254" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26512"/>
     <cge:Term_Ref ObjectID="37504"/>
    <cge:TPSR_Ref TObjectID="26512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1253.000000 -23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26512"/>
     <cge:Term_Ref ObjectID="37504"/>
    <cge:TPSR_Ref TObjectID="26512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1253.000000 -23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26512"/>
     <cge:Term_Ref ObjectID="37504"/>
    <cge:TPSR_Ref TObjectID="26512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1451.000000 -23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26507"/>
     <cge:Term_Ref ObjectID="37494"/>
    <cge:TPSR_Ref TObjectID="26507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159249" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1451.000000 -23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26507"/>
     <cge:Term_Ref ObjectID="37494"/>
    <cge:TPSR_Ref TObjectID="26507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159245" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1451.000000 -23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159245" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26507"/>
     <cge:Term_Ref ObjectID="37494"/>
    <cge:TPSR_Ref TObjectID="26507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159272" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 -560.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159272" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26527"/>
     <cge:Term_Ref ObjectID="37534"/>
    <cge:TPSR_Ref TObjectID="26527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159273" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 -560.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159273" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26527"/>
     <cge:Term_Ref ObjectID="37534"/>
    <cge:TPSR_Ref TObjectID="26527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159269" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 -560.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159269" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26527"/>
     <cge:Term_Ref ObjectID="37534"/>
    <cge:TPSR_Ref TObjectID="26527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 690.000000 -752.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26484"/>
     <cge:Term_Ref ObjectID="37448"/>
    <cge:TPSR_Ref TObjectID="26484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159216" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 690.000000 -752.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26484"/>
     <cge:Term_Ref ObjectID="37448"/>
    <cge:TPSR_Ref TObjectID="26484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159212" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 690.000000 -752.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159212" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26484"/>
     <cge:Term_Ref ObjectID="37448"/>
    <cge:TPSR_Ref TObjectID="26484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159221" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 705.000000 -504.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159221" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26486"/>
     <cge:Term_Ref ObjectID="37452"/>
    <cge:TPSR_Ref TObjectID="26486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159222" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 705.000000 -504.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159222" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26486"/>
     <cge:Term_Ref ObjectID="37452"/>
    <cge:TPSR_Ref TObjectID="26486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159218" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 705.000000 -504.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159218" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26486"/>
     <cge:Term_Ref ObjectID="37452"/>
    <cge:TPSR_Ref TObjectID="26486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-159188" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 -896.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159188" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26475"/>
     <cge:Term_Ref ObjectID="37433"/>
    <cge:TPSR_Ref TObjectID="26475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-159189" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 -896.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159189" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26475"/>
     <cge:Term_Ref ObjectID="37433"/>
    <cge:TPSR_Ref TObjectID="26475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-159190" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 -896.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159190" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26475"/>
     <cge:Term_Ref ObjectID="37433"/>
    <cge:TPSR_Ref TObjectID="26475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-159194" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 -896.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159194" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26475"/>
     <cge:Term_Ref ObjectID="37433"/>
    <cge:TPSR_Ref TObjectID="26475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-159191" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 -896.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159191" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26475"/>
     <cge:Term_Ref ObjectID="37433"/>
    <cge:TPSR_Ref TObjectID="26475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-159196" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 173.000000 -508.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159196" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26476"/>
     <cge:Term_Ref ObjectID="37434"/>
    <cge:TPSR_Ref TObjectID="26476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-159197" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 173.000000 -508.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159197" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26476"/>
     <cge:Term_Ref ObjectID="37434"/>
    <cge:TPSR_Ref TObjectID="26476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-159198" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 173.000000 -508.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159198" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26476"/>
     <cge:Term_Ref ObjectID="37434"/>
    <cge:TPSR_Ref TObjectID="26476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-159202" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 173.000000 -508.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159202" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26476"/>
     <cge:Term_Ref ObjectID="37434"/>
    <cge:TPSR_Ref TObjectID="26476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-159199" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 173.000000 -508.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159199" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26476"/>
     <cge:Term_Ref ObjectID="37434"/>
    <cge:TPSR_Ref TObjectID="26476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-159204" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1626.000000 -392.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159204" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26477"/>
     <cge:Term_Ref ObjectID="37435"/>
    <cge:TPSR_Ref TObjectID="26477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-159205" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1626.000000 -392.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159205" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26477"/>
     <cge:Term_Ref ObjectID="37435"/>
    <cge:TPSR_Ref TObjectID="26477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-159206" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1626.000000 -392.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159206" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26477"/>
     <cge:Term_Ref ObjectID="37435"/>
    <cge:TPSR_Ref TObjectID="26477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-159210" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1626.000000 -392.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159210" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26477"/>
     <cge:Term_Ref ObjectID="37435"/>
    <cge:TPSR_Ref TObjectID="26477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-159207" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1626.000000 -392.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159207" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26477"/>
     <cge:Term_Ref ObjectID="37435"/>
    <cge:TPSR_Ref TObjectID="26477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-160333" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1133.000000 -941.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160333" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26607"/>
     <cge:Term_Ref ObjectID="37668"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-160334" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1133.000000 -941.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160334" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26607"/>
     <cge:Term_Ref ObjectID="37668"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-160324" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1133.000000 -941.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160324" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26607"/>
     <cge:Term_Ref ObjectID="37668"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-160321" prefix="Ua " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1133.000000 -941.000000) translate(0,57)">Ua  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160321" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26607"/>
     <cge:Term_Ref ObjectID="37668"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-160338" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -119.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160338" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26608"/>
     <cge:Term_Ref ObjectID="37672"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-160339" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -119.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160339" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26608"/>
     <cge:Term_Ref ObjectID="37672"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-160330" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -119.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160330" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26608"/>
     <cge:Term_Ref ObjectID="37672"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-160321" prefix="Ua " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -119.000000) translate(0,57)">Ua  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160321" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26608"/>
     <cge:Term_Ref ObjectID="37672"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-196156" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 -669.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26530"/>
     <cge:Term_Ref ObjectID="37540"/>
    <cge:TPSR_Ref TObjectID="26530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-159224" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 -669.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159224" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26530"/>
     <cge:Term_Ref ObjectID="37540"/>
    <cge:TPSR_Ref TObjectID="26530"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-149" y="-1060"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-149" y="-1060"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-198" y="-1077"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="593" y="-914"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="593" y="-914"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="385" y="-332"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="385" y="-332"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="524" y="-332"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="524" y="-332"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="787" y="-331"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="787" y="-331"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1425" y="-332"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1425" y="-332"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1233" y="-331"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1233" y="-331"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1348" y="-913"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1348" y="-913"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1011" y="-510"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1011" y="-510"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="602" y="-671"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="602" y="-671"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="65" x="-250" y="-636"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="65" x="-250" y="-636"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="84" y="-1083"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="84" y="-1083"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="86" y="-1041"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="86" y="-1041"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="96" y="-981"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="96" y="-981"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-149" y="-1060"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/></g>
   <g href="35kV雨龙变SB_YL_382间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="593" y="-914"/></g>
   <g href="35kV雨龙变SB_YL_081间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="385" y="-332"/></g>
   <g href="35kV雨龙变SB_YL_082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="524" y="-332"/></g>
   <g href="35kV雨龙变SB_YL_083间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="787" y="-331"/></g>
   <g href="35kV雨龙变SB_YL_092间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1425" y="-332"/></g>
   <g href="35kV雨龙变SB_YL_091间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1233" y="-331"/></g>
   <g href="35kV雨龙变SB_YL_381间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1348" y="-913"/></g>
   <g href="35kV雨龙变SB_YL_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1011" y="-510"/></g>
   <g href="35kV雨龙变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="602" y="-671"/></g>
   <g href="35kV雨龙变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="65" x="-250" y="-636"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="84" y="-1083"/></g>
   <g href="cx_配调_配网接线图35_双柏.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="86" y="-1041"/></g>
   <g href="AVC雨龙站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="96" y="-981"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="97" y="-980"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-SB_YL.SB_YL_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="301,-818 1501,-818 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26475" ObjectName="BS-SB_YL.SB_YL_3IM"/>
    <cge:TPSR_Ref TObjectID="26475"/></metadata>
   <polyline fill="none" opacity="0" points="301,-818 1501,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_YL.SB_YL_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="177,-417 964,-417 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26476" ObjectName="BS-SB_YL.SB_YL_9IM"/>
    <cge:TPSR_Ref TObjectID="26476"/></metadata>
   <polyline fill="none" opacity="0" points="177,-417 964,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_YL.SB_YL_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1085,-417 1666,-417 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26477" ObjectName="BS-SB_YL.SB_YL_9IIM"/>
    <cge:TPSR_Ref TObjectID="26477"/></metadata>
   <polyline fill="none" opacity="0" points="1085,-417 1666,-417 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_41597d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 512.000000 -949.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4157970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -1002.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e335c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1282.000000 -958.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41589f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 -1001.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d65da0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 936.000000 -836.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40f2980" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 227.000000 -493.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4120a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1407.000000 -492.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_414e280" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 487.000000 -752.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40de470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 398.000000 -162.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_442aae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 538.000000 -162.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41652c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 803.000000 -161.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_416da10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 -161.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40e88f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1439.000000 -162.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d1e7d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.000000 -870.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4477e50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1414.000000 -873.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="26475" cx="584" cy="-818" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26475" cx="1339" cy="-818" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26475" cx="999" cy="-818" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26476" cx="945" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26476" cx="376" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26476" cx="308" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26476" cx="515" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26476" cx="778" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26477" cx="1112" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26477" cx="1488" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26477" cx="1224" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26477" cx="1416" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26476" cx="568" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26475" cx="568" cy="-818" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_412be10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_412be10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_412be10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_412be10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_412be10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_412be10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_412be10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_412be10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_412be10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_40f4010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_40f53b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -114.000000 -1049.500000) translate(0,16)">雨龙变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_414ad60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 526.000000 -1180.000000) translate(0,18)">35kV大麦地线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_40d60f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1289.000000 -1180.000000) translate(0,18)">35kV雨龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_41674f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 290.000000 -796.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33fd790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 173.000000 -402.000000) translate(0,15)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_4139c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 342.000000 -51.000000) translate(0,18)">帮三线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_4189320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 459.000000 -1157.000000) translate(0,18)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_4189320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 459.000000 -1157.000000) translate(0,40)">A B相</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_4187b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 972.000000 -1073.000000) translate(0,18)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_48807d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1221.000000 -1157.000000) translate(0,18)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_48807d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1221.000000 -1157.000000) translate(0,40)">A B相</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_415a670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 -576.000000) translate(0,15)">10kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4186ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 323.000000 -578.000000) translate(0,15)">10kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_411de20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 474.000000 -51.000000) translate(0,18)">法甸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_41600f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 742.000000 -51.000000) translate(0,18)">野牛线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_40c92a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1205.000000 -51.000000) translate(0,18)">法脿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_40c94e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1388.000000 -51.000000) translate(0,18)">新会线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e57fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1578.000000 -435.000000) translate(0,12)">10kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e58890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 383.000000 -383.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e58f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 385.000000 -332.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e591b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 383.000000 -278.000000) translate(0,12)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e593f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 332.000000 -159.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e59630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -162.000000) translate(0,12)">08127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41130e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1011.000000 -510.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41134d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1119.000000 -456.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4113710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -457.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4113950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1231.000000 -382.000000) translate(0,12)">0911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4113b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -331.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4113dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1231.000000 -277.000000) translate(0,12)">0912</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4114010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1225.000000 -194.000000) translate(0,12)">0916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4114250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -158.000000) translate(0,12)">09127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4114490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 524.000000 -332.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41146d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 522.000000 -383.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4114910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 533.000000 -160.000000) translate(0,12)">08227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4114b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 473.000000 -160.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4114d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 522.000000 -278.000000) translate(0,12)">0822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4114fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 315.000000 -462.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4115210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 251.000000 -525.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4115450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1495.000000 -461.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4115690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1430.000000 -524.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41158d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1423.000000 -383.000000) translate(0,12)">0921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4115b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1425.000000 -332.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4115d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1423.000000 -278.000000) translate(0,12)">0922</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4115f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1372.000000 -159.000000) translate(0,12)">0926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41161d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -157.000000) translate(0,12)">09227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4116410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 787.000000 -331.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4116650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 785.000000 -382.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4116890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 785.000000 -277.000000) translate(0,12)">0832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41025b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -159.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41027f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 800.000000 -160.000000) translate(0,12)">08327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4102a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 575.000000 -796.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4102c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 575.000000 -453.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4102eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 571.000000 -539.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41030f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 -498.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4103330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 -737.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4103570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 517.000000 -784.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41037b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 591.000000 -855.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41039f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 638.000000 -982.000000) translate(0,12)">38260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4103c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 525.000000 -1004.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4103e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 591.000000 -997.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41040b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -914.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41042f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1006.000000 -863.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4104530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 949.000000 -889.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4104770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -913.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41049b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1346.000000 -996.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4104bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1291.000000 -1013.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4104e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1393.000000 -981.000000) translate(0,12)">38160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4105070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1346.000000 -854.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4479af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 628.000000 -869.000000) translate(0,12)">38217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447a120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -870.000000) translate(0,12)">38117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447df00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 693.000000 -654.000000) translate(0,12)">油温</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_447e290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -141.000000 -115.000000) translate(0,17)">7812029</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447e940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 993.000000 -470.000000) translate(0,12)">10kV分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447f020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 9.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4489e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1557.000000 -549.000000) translate(0,15)">380VI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_448a810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1784.000000 -548.000000) translate(0,12)">380VII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_448aa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1642.000000 -556.000000) translate(0,12)">1ATS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_448b010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1754.000000 -560.000000) translate(0,12)">2ATS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_448b290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1719.000000 -781.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_448b4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1567.000000 -781.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_448b720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1624.000000 -604.000000) translate(0,12)">2QF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_448ba30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1675.000000 -602.000000) translate(0,12)">1QF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_448bec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1730.000000 -599.000000) translate(0,12)">4QF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_448c100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1787.000000 -602.000000) translate(0,12)">3QF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_449c010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 690.000000 -670.000000) translate(0,12)">档位</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_449cbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.000000 -641.000000) translate(0,15)">1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_449cbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.000000 -641.000000) translate(0,33)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_449e1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 323.000000 25.000000) translate(0,12)">TA变比 100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_449e3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 25.000000) translate(0,12)">TA变比 150/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_449e620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 733.000000 25.000000) translate(0,12)">TA变比 100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_449e860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1193.000000 25.000000) translate(0,12)">TA变比 300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_449eaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1391.000000 25.000000) translate(0,12)">TA变比 150/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_449ece0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 637.000000 -896.000000) translate(0,12)">TA变比 300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_449ef20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 -902.000000) translate(0,12)">TA变比 300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_449f160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -635.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_449fd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 95.000000 -1075.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_44a02f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 94.000000 -1034.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44a1aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 602.000000 -670.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_44bf470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -269.000000 -73.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_44bf470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -269.000000 -73.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_44c0d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -142.000000 -83.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_44c0d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -142.000000 -83.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_44c0d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -142.000000 -83.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_44c57e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1199.000000 -850.000000) translate(0,13)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_44c79d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 113.500000 -969.000000) translate(0,16)">AVC</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4105620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1568.000000 393.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4105810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1568.000000 379.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4105c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1568.000000 365.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41061f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1574.000000 350.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4106470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1560.000000 334.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_487b210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 321.000000 23.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_487be40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 8.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_487c720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.000000 -7.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_487f410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 463.000000 23.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_487f640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 452.000000 8.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_487f880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.000000 -7.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_487fbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 723.000000 23.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_487fe10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 712.000000 8.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4880050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 737.000000 -7.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4880380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 23.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_48805e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1180.000000 8.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d19680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1205.000000 -7.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d199b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1386.000000 23.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d19c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 8.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d19e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1400.000000 -7.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1a180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 644.000000 504.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1a3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 633.000000 489.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1a620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 658.000000 474.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1a950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 752.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1abb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 616.000000 737.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1adf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 641.000000 722.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1b120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 968.000000 560.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1b380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.000000 545.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1b5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 982.000000 530.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 8.000000 -48.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447bae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 385.000000 847.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447bd70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 385.000000 833.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447bfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 385.000000 819.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447c1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 391.000000 804.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447c430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 377.000000 788.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -655.000000 138.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447cda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 767.000000 646.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447cff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 767.000000 632.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447d230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 767.000000 618.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447d470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 773.000000 603.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_447d6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 587.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44c1280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 636.000000 944.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44c14f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 661.000000 929.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44c1700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 647.000000 959.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44c1a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1400.000000 947.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44c1c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1425.000000 932.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44c1ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1411.000000 962.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-SB_YL.081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 370.985640 -57.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34358" ObjectName="EC-SB_YL.081Ld"/>
    <cge:TPSR_Ref TObjectID="34358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_YL.082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 509.985640 -57.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34359" ObjectName="EC-SB_YL.082Ld"/>
    <cge:TPSR_Ref TObjectID="34359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_YL.083Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.985640 -56.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34360" ObjectName="EC-SB_YL.083Ld"/>
    <cge:TPSR_Ref TObjectID="34360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_YL.091Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1218.985640 -56.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34361" ObjectName="EC-SB_YL.091Ld"/>
    <cge:TPSR_Ref TObjectID="34361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_YL.092Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1410.985640 -57.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34362" ObjectName="EC-SB_YL.092Ld"/>
    <cge:TPSR_Ref TObjectID="34362"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-14" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 19.000000 -968.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="14" ObjectName="DYN-SB_YL"/>
     <cge:Meas_Ref ObjectId="14"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-159730">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.985640 -353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26523" ObjectName="SW-SB_YL.SB_YL_0811SW"/>
     <cge:Meas_Ref ObjectId="159730"/>
    <cge:TPSR_Ref TObjectID="26523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159731">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.985640 -248.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26524" ObjectName="SW-SB_YL.SB_YL_0812SW"/>
     <cge:Meas_Ref ObjectId="159731"/>
    <cge:TPSR_Ref TObjectID="26524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159574">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 574.556647 -825.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26497" ObjectName="SW-SB_YL.SB_YL_3821SW"/>
     <cge:Meas_Ref ObjectId="159574"/>
    <cge:TPSR_Ref TObjectID="26497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159581">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 574.556647 -967.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26498" ObjectName="SW-SB_YL.SB_YL_3826SW"/>
     <cge:Meas_Ref ObjectId="159581"/>
    <cge:TPSR_Ref TObjectID="26498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159588">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.000000 -952.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26500" ObjectName="SW-SB_YL.SB_YL_38260SW"/>
     <cge:Meas_Ref ObjectId="159588"/>
    <cge:TPSR_Ref TObjectID="26500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159589">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 509.000000 -974.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26501" ObjectName="SW-SB_YL.SB_YL_38267SW"/>
     <cge:Meas_Ref ObjectId="159589"/>
    <cge:TPSR_Ref TObjectID="26501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159515">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.556647 -824.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26491" ObjectName="SW-SB_YL.SB_YL_3811SW"/>
     <cge:Meas_Ref ObjectId="159515"/>
    <cge:TPSR_Ref TObjectID="26491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159522">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.556647 -966.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26492" ObjectName="SW-SB_YL.SB_YL_3816SW"/>
     <cge:Meas_Ref ObjectId="159522"/>
    <cge:TPSR_Ref TObjectID="26492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159529">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1377.000000 -951.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26494" ObjectName="SW-SB_YL.SB_YL_38160SW"/>
     <cge:Meas_Ref ObjectId="159529"/>
    <cge:TPSR_Ref TObjectID="26494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159530">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1279.000000 -983.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26495" ObjectName="SW-SB_YL.SB_YL_38167SW"/>
     <cge:Meas_Ref ObjectId="159530"/>
    <cge:TPSR_Ref TObjectID="26495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159823">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.000000 -833.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26531" ObjectName="SW-SB_YL.SB_YL_3901SW"/>
     <cge:Meas_Ref ObjectId="159823"/>
    <cge:TPSR_Ref TObjectID="26531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159352">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 933.000000 -859.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26483" ObjectName="SW-SB_YL.SB_YL_39017SW"/>
     <cge:Meas_Ref ObjectId="159352"/>
    <cge:TPSR_Ref TObjectID="26483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 936.000000 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26529" ObjectName="SW-SB_YL.SB_YL_0121SW"/>
     <cge:Meas_Ref ObjectId="159786"/>
    <cge:TPSR_Ref TObjectID="26529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159784">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1103.000000 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26528" ObjectName="SW-SB_YL.SB_YL_0122SW"/>
     <cge:Meas_Ref ObjectId="159784"/>
    <cge:TPSR_Ref TObjectID="26528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159303">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.000000 -432.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26479" ObjectName="SW-SB_YL.SB_YL_0901SW"/>
     <cge:Meas_Ref ObjectId="159303"/>
    <cge:TPSR_Ref TObjectID="26479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159394">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 514.000000 -753.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26485" ObjectName="SW-SB_YL.SB_YL_30117SW"/>
     <cge:Meas_Ref ObjectId="159394"/>
    <cge:TPSR_Ref TObjectID="26485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159304">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 248.000000 -494.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26480" ObjectName="SW-SB_YL.SB_YL_09017SW"/>
     <cge:Meas_Ref ObjectId="159304"/>
    <cge:TPSR_Ref TObjectID="26480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159307">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1479.000000 -431.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26481" ObjectName="SW-SB_YL.SB_YL_0902SW"/>
     <cge:Meas_Ref ObjectId="159307"/>
    <cge:TPSR_Ref TObjectID="26481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159308">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1428.000000 -493.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26482" ObjectName="SW-SB_YL.SB_YL_09027SW"/>
     <cge:Meas_Ref ObjectId="159308"/>
    <cge:TPSR_Ref TObjectID="26482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159733">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 367.000000 -128.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26525" ObjectName="SW-SB_YL.SB_YL_0816SW"/>
     <cge:Meas_Ref ObjectId="159733"/>
    <cge:TPSR_Ref TObjectID="26525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159436">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 -766.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26489" ObjectName="SW-SB_YL.SB_YL_3011SW"/>
     <cge:Meas_Ref ObjectId="159436"/>
    <cge:TPSR_Ref TObjectID="26489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159422">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 -511.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26488" ObjectName="SW-SB_YL.SB_YL_0016SW"/>
     <cge:Meas_Ref ObjectId="159422"/>
    <cge:TPSR_Ref TObjectID="26488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159421">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 -423.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26487" ObjectName="SW-SB_YL.SB_YL_0011SW"/>
     <cge:Meas_Ref ObjectId="159421"/>
    <cge:TPSR_Ref TObjectID="26487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159734">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 394.985640 -186.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26526" ObjectName="SW-SB_YL.SB_YL_08127SW"/>
     <cge:Meas_Ref ObjectId="159734"/>
    <cge:TPSR_Ref TObjectID="26526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159701">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 505.985640 -353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26518" ObjectName="SW-SB_YL.SB_YL_0821SW"/>
     <cge:Meas_Ref ObjectId="159701"/>
    <cge:TPSR_Ref TObjectID="26518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159702">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 505.985640 -248.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26519" ObjectName="SW-SB_YL.SB_YL_0822SW"/>
     <cge:Meas_Ref ObjectId="159702"/>
    <cge:TPSR_Ref TObjectID="26519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159704">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 506.000000 -129.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26520" ObjectName="SW-SB_YL.SB_YL_0826SW"/>
     <cge:Meas_Ref ObjectId="159704"/>
    <cge:TPSR_Ref TObjectID="26520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159705">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 534.985640 -186.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26521" ObjectName="SW-SB_YL.SB_YL_08227SW"/>
     <cge:Meas_Ref ObjectId="159705"/>
    <cge:TPSR_Ref TObjectID="26521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159614">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.985640 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26503" ObjectName="SW-SB_YL.SB_YL_0831SW"/>
     <cge:Meas_Ref ObjectId="159614"/>
    <cge:TPSR_Ref TObjectID="26503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159615">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.985640 -247.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26504" ObjectName="SW-SB_YL.SB_YL_0832SW"/>
     <cge:Meas_Ref ObjectId="159615"/>
    <cge:TPSR_Ref TObjectID="26504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159617">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 769.000000 -128.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26505" ObjectName="SW-SB_YL.SB_YL_0836SW"/>
     <cge:Meas_Ref ObjectId="159617"/>
    <cge:TPSR_Ref TObjectID="26505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159618">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 799.985640 -185.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26506" ObjectName="SW-SB_YL.SB_YL_08327SW"/>
     <cge:Meas_Ref ObjectId="159618"/>
    <cge:TPSR_Ref TObjectID="26506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159672">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.985640 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26513" ObjectName="SW-SB_YL.SB_YL_0911SW"/>
     <cge:Meas_Ref ObjectId="159672"/>
    <cge:TPSR_Ref TObjectID="26513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159673">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.985640 -247.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26514" ObjectName="SW-SB_YL.SB_YL_0912SW"/>
     <cge:Meas_Ref ObjectId="159673"/>
    <cge:TPSR_Ref TObjectID="26514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.000000 -163.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26515" ObjectName="SW-SB_YL.SB_YL_0916SW"/>
     <cge:Meas_Ref ObjectId="159675"/>
    <cge:TPSR_Ref TObjectID="26515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159676">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1260.985640 -185.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26516" ObjectName="SW-SB_YL.SB_YL_09127SW"/>
     <cge:Meas_Ref ObjectId="159676"/>
    <cge:TPSR_Ref TObjectID="26516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159643">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1406.985640 -353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26508" ObjectName="SW-SB_YL.SB_YL_0921SW"/>
     <cge:Meas_Ref ObjectId="159643"/>
    <cge:TPSR_Ref TObjectID="26508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159644">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1406.985640 -248.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26509" ObjectName="SW-SB_YL.SB_YL_0922SW"/>
     <cge:Meas_Ref ObjectId="159644"/>
    <cge:TPSR_Ref TObjectID="26509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159646">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1407.000000 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26510" ObjectName="SW-SB_YL.SB_YL_0926SW"/>
     <cge:Meas_Ref ObjectId="159646"/>
    <cge:TPSR_Ref TObjectID="26510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159647">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1435.985640 -186.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26511" ObjectName="SW-SB_YL.SB_YL_09227SW"/>
     <cge:Meas_Ref ObjectId="159647"/>
    <cge:TPSR_Ref TObjectID="26511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1143.000000 -87.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159587">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 612.000000 -871.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26499" ObjectName="SW-SB_YL.SB_YL_38217SW"/>
     <cge:Meas_Ref ObjectId="159587"/>
    <cge:TPSR_Ref TObjectID="26499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159528">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1362.000000 -874.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26493" ObjectName="SW-SB_YL.SB_YL_38117SW"/>
     <cge:Meas_Ref ObjectId="159528"/>
    <cge:TPSR_Ref TObjectID="26493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1227.000000 -978.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_40f19e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-818 584,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26475@0" ObjectIDZND0="26497@0" Pin0InfoVect0LinkObjId="SW-159574_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-818 584,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4141130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-920 584,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26496@1" ObjectIDZND0="26498@x" ObjectIDZND1="26500@x" Pin0InfoVect0LinkObjId="SW-159581_0" Pin0InfoVect1LinkObjId="SW-159588_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159568_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="584,-920 584,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_340a5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-949 584,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26496@x" ObjectIDND1="26500@x" ObjectIDZND0="26498@0" Pin0InfoVect0LinkObjId="SW-159581_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159568_0" Pin1InfoVect1LinkObjId="SW-159588_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-949 584,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3423720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-1007 631,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4157970@0" ObjectIDZND0="26500@1" Pin0InfoVect0LinkObjId="SW-159588_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4157970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="631,-1007 631,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4102180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="631,-957 631,-949 584,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26500@0" ObjectIDZND0="26496@x" ObjectIDZND1="26498@x" Pin0InfoVect0LinkObjId="SW-159568_0" Pin0InfoVect1LinkObjId="SW-159581_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159588_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="631,-957 631,-949 584,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4118e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-1008 584,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="26498@1" ObjectIDZND0="26501@x" ObjectIDZND1="g_4148ee0@0" ObjectIDZND2="g_4135b60@0" Pin0InfoVect0LinkObjId="SW-159589_0" Pin0InfoVect1LinkObjId="g_4148ee0_0" Pin0InfoVect2LinkObjId="g_4135b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159581_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="584,-1008 584,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33fbb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-1024 518,-1024 518,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26498@x" ObjectIDND1="g_4148ee0@0" ObjectIDND2="g_4135b60@0" ObjectIDZND0="26501@1" Pin0InfoVect0LinkObjId="SW-159589_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159581_0" Pin1InfoVect1LinkObjId="g_4148ee0_0" Pin1InfoVect2LinkObjId="g_4135b60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-1024 518,-1024 518,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_415b3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-979 518,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26501@0" ObjectIDZND0="g_41597d0@0" Pin0InfoVect0LinkObjId="g_41597d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159589_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-979 518,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4195df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-1044 538,-1044 538,-1057 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="26498@x" ObjectIDND1="26501@x" ObjectIDND2="g_4135b60@0" ObjectIDZND0="g_4148ee0@0" Pin0InfoVect0LinkObjId="g_4148ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159581_0" Pin1InfoVect1LinkObjId="SW-159589_0" Pin1InfoVect2LinkObjId="g_4135b60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-1044 538,-1044 538,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40ed380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-1024 584,-1044 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="26498@x" ObjectIDND1="26501@x" ObjectIDZND0="g_4148ee0@0" ObjectIDZND1="g_4135b60@0" ObjectIDZND2="37759@1" Pin0InfoVect0LinkObjId="g_4148ee0_0" Pin0InfoVect1LinkObjId="g_4135b60_0" Pin0InfoVect2LinkObjId="g_3e151f0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159581_0" Pin1InfoVect1LinkObjId="SW-159589_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="584,-1024 584,-1044 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4134bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-1081 615,-1081 615,-1093 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_4148ee0@0" ObjectIDND1="26498@x" ObjectIDND2="26501@x" ObjectIDZND0="g_4135b60@0" Pin0InfoVect0LinkObjId="g_4135b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4148ee0_0" Pin1InfoVect1LinkObjId="SW-159581_0" Pin1InfoVect2LinkObjId="SW-159589_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-1081 615,-1081 615,-1093 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_415c560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-1044 584,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_4148ee0@0" ObjectIDND1="26498@x" ObjectIDND2="26501@x" ObjectIDZND0="g_4135b60@0" ObjectIDZND1="37759@1" Pin0InfoVect0LinkObjId="g_4135b60_0" Pin0InfoVect1LinkObjId="g_3e151f0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4148ee0_0" Pin1InfoVect1LinkObjId="SW-159581_0" Pin1InfoVect2LinkObjId="SW-159589_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="584,-1044 584,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e151f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-1081 584,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_4135b60@0" ObjectIDND1="g_4148ee0@0" ObjectIDND2="26498@x" ObjectIDZND0="37759@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4135b60_0" Pin1InfoVect1LinkObjId="g_4148ee0_0" Pin1InfoVect2LinkObjId="SW-159581_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-1081 584,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca3290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-818 1339,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26475@0" ObjectIDZND0="26491@0" Pin0InfoVect0LinkObjId="SW-159515_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-818 1339,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41722b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-919 1339,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26490@1" ObjectIDZND0="26492@x" ObjectIDZND1="26494@x" Pin0InfoVect0LinkObjId="SW-159522_0" Pin0InfoVect1LinkObjId="SW-159529_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-919 1339,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e1f490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-948 1339,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26490@x" ObjectIDND1="26494@x" ObjectIDZND0="26492@0" Pin0InfoVect0LinkObjId="SW-159522_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159509_0" Pin1InfoVect1LinkObjId="SW-159529_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-948 1339,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40dde90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1386,-1006 1386,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_41589f0@0" ObjectIDZND0="26494@1" Pin0InfoVect0LinkObjId="SW-159529_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41589f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1386,-1006 1386,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41333a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1386,-956 1386,-948 1339,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26494@0" ObjectIDZND0="26492@x" ObjectIDZND1="26490@x" Pin0InfoVect0LinkObjId="SW-159522_0" Pin0InfoVect1LinkObjId="SW-159509_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159529_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1386,-956 1386,-948 1339,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e1c3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1288,-988 1288,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26495@0" ObjectIDZND0="g_3e335c0@0" Pin0InfoVect0LinkObjId="g_3e335c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1288,-988 1288,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc83c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-1062 1293,-1062 1293,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_412b5f0@0" ObjectIDND1="37757@1" ObjectIDND2="26495@x" ObjectIDZND0="g_41885a0@0" Pin0InfoVect0LinkObjId="g_41885a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_412b5f0_0" Pin1InfoVect1LinkObjId="g_3d55960_1" Pin1InfoVect2LinkObjId="SW-159530_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-1062 1293,-1062 1293,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e32610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-1080 1370,-1080 1370,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_41885a0@0" ObjectIDND1="26495@x" ObjectIDND2="26492@x" ObjectIDZND0="g_412b5f0@0" Pin0InfoVect0LinkObjId="g_412b5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_41885a0_0" Pin1InfoVect1LinkObjId="SW-159530_0" Pin1InfoVect2LinkObjId="SW-159522_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-1080 1370,-1080 1370,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d55960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-1080 1339,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_412b5f0@0" ObjectIDND1="g_41885a0@0" ObjectIDND2="26495@x" ObjectIDZND0="37757@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_412b5f0_0" Pin1InfoVect1LinkObjId="g_41885a0_0" Pin1InfoVect2LinkObjId="SW-159530_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-1080 1339,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_413a3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-1062 1339,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_41885a0@0" ObjectIDND1="26495@x" ObjectIDND2="26492@x" ObjectIDZND0="g_412b5f0@0" ObjectIDZND1="37757@1" Pin0InfoVect0LinkObjId="g_412b5f0_0" Pin0InfoVect1LinkObjId="g_3d55960_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_41885a0_0" Pin1InfoVect1LinkObjId="SW-159530_0" Pin1InfoVect2LinkObjId="SW-159522_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-1062 1339,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_414b3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-1024 1288,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26492@x" ObjectIDND1="g_41885a0@0" ObjectIDND2="g_412b5f0@0" ObjectIDZND0="26495@1" Pin0InfoVect0LinkObjId="SW-159530_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159522_0" Pin1InfoVect1LinkObjId="g_41885a0_0" Pin1InfoVect2LinkObjId="g_412b5f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-1024 1288,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4108c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-1007 1339,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="26492@1" ObjectIDZND0="26495@x" ObjectIDZND1="g_41885a0@0" ObjectIDZND2="g_412b5f0@0" Pin0InfoVect0LinkObjId="SW-159530_0" Pin0InfoVect1LinkObjId="g_41885a0_0" Pin0InfoVect2LinkObjId="g_412b5f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159522_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-1007 1339,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d064f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-818 999,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26475@0" ObjectIDZND0="26531@0" Pin0InfoVect0LinkObjId="SW-159823_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,-818 999,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40d6d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-874 999,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26531@1" ObjectIDZND0="26483@x" ObjectIDZND1="g_418d150@0" ObjectIDZND2="g_340b730@0" Pin0InfoVect0LinkObjId="SW-159352_0" Pin0InfoVect1LinkObjId="g_418d150_0" Pin0InfoVect2LinkObjId="g_340b730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159823_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="999,-874 999,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4137940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-909 942,-909 942,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26531@x" ObjectIDND1="g_418d150@0" ObjectIDND2="g_340b730@0" ObjectIDZND0="26483@1" Pin0InfoVect0LinkObjId="SW-159352_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159823_0" Pin1InfoVect1LinkObjId="g_418d150_0" Pin1InfoVect2LinkObjId="g_340b730_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,-909 942,-909 942,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41323c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,-864 942,-854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26483@0" ObjectIDZND0="g_2d65da0@0" Pin0InfoVect0LinkObjId="g_2d65da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159352_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="942,-864 942,-854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_415ad90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-928 1032,-928 1032,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26531@x" ObjectIDND1="26483@x" ObjectIDND2="g_340b730@0" ObjectIDZND0="g_418d150@0" Pin0InfoVect0LinkObjId="g_418d150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159823_0" Pin1InfoVect1LinkObjId="SW-159352_0" Pin1InfoVect2LinkObjId="g_340b730_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,-928 1032,-928 1032,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41662f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-909 999,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26531@x" ObjectIDND1="26483@x" ObjectIDZND0="g_418d150@0" ObjectIDZND1="g_340b730@0" Pin0InfoVect0LinkObjId="g_418d150_0" Pin0InfoVect1LinkObjId="g_340b730_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159823_0" Pin1InfoVect1LinkObjId="SW-159352_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="999,-909 999,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40edfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-928 999,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26531@x" ObjectIDND1="26483@x" ObjectIDND2="g_418d150@0" ObjectIDZND0="g_340b730@0" Pin0InfoVect0LinkObjId="g_340b730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159823_0" Pin1InfoVect1LinkObjId="SW-159352_0" Pin1InfoVect2LinkObjId="g_418d150_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,-928 999,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_340aa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-992 999,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_340b730@1" ObjectIDZND0="g_33fb6a0@0" Pin0InfoVect0LinkObjId="g_33fb6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_340b730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,-992 999,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40f3700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1112,-417 1112,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26477@0" ObjectIDZND0="26528@0" Pin0InfoVect0LinkObjId="SW-159784_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1112,-417 1112,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40f5fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1112,-467 1112,-486 1036,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26528@1" ObjectIDZND0="26527@0" Pin0InfoVect0LinkObjId="SW-159783_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159784_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1112,-467 1112,-486 1036,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4133640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1009,-486 945,-486 945,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26527@1" ObjectIDZND0="26529@1" Pin0InfoVect0LinkObjId="SW-159786_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159783_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1009,-486 945,-486 945,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4187740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="945,-432 945,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26529@0" ObjectIDZND0="26476@0" Pin0InfoVect0LinkObjId="g_44930e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159786_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="945,-432 945,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3416040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-417 376,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26476@0" ObjectIDZND0="26523@1" Pin0InfoVect0LinkObjId="SW-159730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4187740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-417 376,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4131a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="308,-417 308,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26476@0" ObjectIDZND0="26479@0" Pin0InfoVect0LinkObjId="SW-159303_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4187740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="308,-417 308,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_412f7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="308,-473 308,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26479@1" ObjectIDZND0="26480@x" ObjectIDZND1="g_341f000@0" ObjectIDZND2="g_3418780@0" Pin0InfoVect0LinkObjId="SW-159304_0" Pin0InfoVect1LinkObjId="g_341f000_0" Pin0InfoVect2LinkObjId="g_3418780_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159303_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="308,-473 308,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_416ed00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="308,-499 289,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26479@x" ObjectIDND1="g_341f000@0" ObjectIDND2="g_3418780@0" ObjectIDZND0="26480@1" Pin0InfoVect0LinkObjId="SW-159304_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159303_0" Pin1InfoVect1LinkObjId="g_341f000_0" Pin1InfoVect2LinkObjId="g_3418780_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="308,-499 289,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c6aa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="253,-499 245,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26480@0" ObjectIDZND0="g_40f2980@0" Pin0InfoVect0LinkObjId="g_40f2980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159304_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="253,-499 245,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c6ac70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-510 359,-510 359,-493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26479@x" ObjectIDND1="26480@x" ObjectIDND2="g_3418780@0" ObjectIDZND0="g_341f000@0" Pin0InfoVect0LinkObjId="g_341f000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159303_0" Pin1InfoVect1LinkObjId="SW-159304_0" Pin1InfoVect2LinkObjId="g_3418780_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="306,-510 359,-510 359,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40caea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="308,-569 308,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3418780@1" ObjectIDZND0="g_340a0d0@0" Pin0InfoVect0LinkObjId="g_340a0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3418780_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="308,-569 308,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e5a1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="308,-499 308,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26479@x" ObjectIDND1="26480@x" ObjectIDZND0="g_341f000@0" ObjectIDZND1="g_3418780@0" Pin0InfoVect0LinkObjId="g_341f000_0" Pin0InfoVect1LinkObjId="g_3418780_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159303_0" Pin1InfoVect1LinkObjId="SW-159304_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="308,-499 308,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_415a410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="308,-525 308,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3418780@0" ObjectIDZND0="g_341f000@0" ObjectIDZND1="26479@x" ObjectIDZND2="26480@x" Pin0InfoVect0LinkObjId="g_341f000_0" Pin0InfoVect1LinkObjId="SW-159303_0" Pin0InfoVect2LinkObjId="SW-159304_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3418780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="308,-525 308,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4120550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-417 1488,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26477@0" ObjectIDZND0="26481@0" Pin0InfoVect0LinkObjId="SW-159307_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-417 1488,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41207b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-472 1488,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26481@1" ObjectIDZND0="26482@x" ObjectIDZND1="g_40ec920@0" ObjectIDZND2="g_40cb280@0" Pin0InfoVect0LinkObjId="SW-159308_0" Pin0InfoVect1LinkObjId="g_40ec920_0" Pin0InfoVect2LinkObjId="g_40cb280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159307_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-472 1488,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_416c520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-498 1469,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="26481@x" ObjectIDND1="g_40ec920@0" ObjectIDND2="g_40cb280@0" ObjectIDZND0="26482@1" Pin0InfoVect0LinkObjId="SW-159308_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159307_0" Pin1InfoVect1LinkObjId="g_40ec920_0" Pin1InfoVect2LinkObjId="g_40cb280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-498 1469,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40c8b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1433,-498 1425,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26482@0" ObjectIDZND0="g_4120a10@0" Pin0InfoVect0LinkObjId="g_4120a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1433,-498 1425,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40c8d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-509 1539,-509 1539,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26481@x" ObjectIDND1="26482@x" ObjectIDND2="g_40cb280@0" ObjectIDZND0="g_40ec920@0" Pin0InfoVect0LinkObjId="g_40ec920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159307_0" Pin1InfoVect1LinkObjId="SW-159308_0" Pin1InfoVect2LinkObjId="g_40cb280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-509 1539,-509 1539,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40c8ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-568 1488,-582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_40cb280@1" ObjectIDZND0="g_412ac50@0" Pin0InfoVect0LinkObjId="g_412ac50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40cb280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-568 1488,-582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4187390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-358 376,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26523@0" ObjectIDZND0="26522@1" Pin0InfoVect0LinkObjId="SW-159729_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-358 376,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4161a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-311 376,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26522@0" ObjectIDZND0="26524@1" Pin0InfoVect0LinkObjId="SW-159731_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159729_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-311 376,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4122430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-819 568,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26475@0" ObjectIDZND0="26489@1" Pin0InfoVect0LinkObjId="SW-159436_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="568,-819 568,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40f16d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-771 568,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26489@0" ObjectIDZND0="26485@x" ObjectIDZND1="26484@x" Pin0InfoVect0LinkObjId="SW-159394_0" Pin0InfoVect1LinkObjId="SW-159386_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159436_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="568,-771 568,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_414ddc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-758 555,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26489@x" ObjectIDND1="26484@x" ObjectIDZND0="26485@1" Pin0InfoVect0LinkObjId="SW-159394_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159436_0" Pin1InfoVect1LinkObjId="SW-159386_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="568,-758 555,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_414e020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="519,-758 505,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26485@0" ObjectIDZND0="g_414e280@0" Pin0InfoVect0LinkObjId="g_414e280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159394_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="519,-758 505,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4121140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-758 568,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26489@x" ObjectIDND1="26485@x" ObjectIDZND0="26484@1" Pin0InfoVect0LinkObjId="SW-159386_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159436_0" Pin1InfoVect1LinkObjId="SW-159394_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="568,-758 568,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_415fc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-561 568,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_4161c70@0" ObjectIDND1="g_44a47a0@0" ObjectIDZND0="26488@1" Pin0InfoVect0LinkObjId="SW-159422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4161c70_0" Pin1InfoVect1LinkObjId="g_44a47a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="568,-561 568,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_415fe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-549 612,-561 568,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_4161c70@0" ObjectIDZND0="26488@x" ObjectIDZND1="g_44a47a0@0" Pin0InfoVect0LinkObjId="SW-159422_0" Pin0InfoVect1LinkObjId="g_44a47a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4161c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="612,-549 612,-561 568,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40de210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="404,-191 404,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26526@0" ObjectIDZND0="g_40de470@0" Pin0InfoVect0LinkObjId="g_40de470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159734_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="404,-191 404,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4141d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-253 376,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26524@0" ObjectIDZND0="g_44a57e0@0" ObjectIDZND1="g_44b7ff0@0" ObjectIDZND2="26526@x" Pin0InfoVect0LinkObjId="g_44a57e0_0" Pin0InfoVect1LinkObjId="g_44b7ff0_0" Pin0InfoVect2LinkObjId="SW-159734_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159731_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="376,-253 376,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4141f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-124 344,-124 344,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="26525@x" ObjectIDND1="34358@x" ObjectIDZND0="g_40d7330@0" Pin0InfoVect0LinkObjId="g_40d7330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159733_0" Pin1InfoVect1LinkObjId="EC-SB_YL.081Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-124 344,-124 344,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41384a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-417 515,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26476@0" ObjectIDZND0="26518@1" Pin0InfoVect0LinkObjId="SW-159701_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4187740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,-417 515,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4101420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-358 515,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26518@0" ObjectIDZND0="26517@1" Pin0InfoVect0LinkObjId="SW-159700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,-358 515,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4101680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-311 515,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26517@0" ObjectIDZND0="26519@1" Pin0InfoVect0LinkObjId="SW-159702_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,-311 515,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_442a880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="544,-191 544,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26521@0" ObjectIDZND0="g_442aae0@0" Pin0InfoVect0LinkObjId="g_442aae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="544,-191 544,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40eb670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-253 515,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26519@0" ObjectIDZND0="g_44a97d0@0" ObjectIDZND1="g_44b91e0@0" ObjectIDZND2="26521@x" Pin0InfoVect0LinkObjId="g_44a97d0_0" Pin0InfoVect1LinkObjId="g_44b91e0_0" Pin0InfoVect2LinkObjId="SW-159705_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159702_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="515,-253 515,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4125110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-417 778,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26476@0" ObjectIDZND0="26503@1" Pin0InfoVect0LinkObjId="SW-159614_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4187740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,-417 778,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4126020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-357 778,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26503@0" ObjectIDZND0="26502@1" Pin0InfoVect0LinkObjId="SW-159613_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159614_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,-357 778,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4126280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-310 778,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26502@0" ObjectIDZND0="26504@1" Pin0InfoVect0LinkObjId="SW-159615_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,-310 778,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4165060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="809,-190 809,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26506@0" ObjectIDZND0="g_41652c0@0" Pin0InfoVect0LinkObjId="g_41652c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159618_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="809,-190 809,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4165c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-252 778,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="26504@0" ObjectIDZND0="g_44ba3d0@0" ObjectIDZND1="26506@x" ObjectIDZND2="g_44ae030@0" Pin0InfoVect0LinkObjId="g_44ba3d0_0" Pin0InfoVect1LinkObjId="SW-159618_0" Pin0InfoVect2LinkObjId="g_44ae030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159615_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="778,-252 778,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4165ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-124 746,-124 746,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34360@x" ObjectIDND1="26505@x" ObjectIDZND0="g_4125370@0" Pin0InfoVect0LinkObjId="g_4125370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-SB_YL.083Ld_0" Pin1InfoVect1LinkObjId="SW-159617_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,-124 746,-124 746,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4129480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-417 1224,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26477@0" ObjectIDZND0="26513@1" Pin0InfoVect0LinkObjId="SW-159672_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-417 1224,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_412a3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-357 1224,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26513@0" ObjectIDZND0="26512@1" Pin0InfoVect0LinkObjId="SW-159671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-357 1224,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_412a5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-310 1224,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26512@0" ObjectIDZND0="26514@1" Pin0InfoVect0LinkObjId="SW-159673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-310 1224,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_416d7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-190 1270,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26516@0" ObjectIDZND0="g_416da10@0" Pin0InfoVect0LinkObjId="g_416da10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-190 1270,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_416e460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-252 1224,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26514@0" ObjectIDZND0="26515@x" ObjectIDZND1="g_44b55e0@0" ObjectIDZND2="26516@x" Pin0InfoVect0LinkObjId="SW-159675_0" Pin0InfoVect1LinkObjId="g_44b55e0_0" Pin0InfoVect2LinkObjId="SW-159676_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-252 1224,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_416e6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-134 1192,-134 1192,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="34361@x" ObjectIDND1="26515@x" ObjectIDND2="g_44b4440@0" ObjectIDZND0="g_41296e0@0" Pin0InfoVect0LinkObjId="g_41296e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-SB_YL.091Ld_0" Pin1InfoVect1LinkObjId="SW-159675_0" Pin1InfoVect2LinkObjId="g_44b4440_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-134 1192,-134 1192,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_442da30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1416,-417 1416,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26477@0" ObjectIDZND0="26508@1" Pin0InfoVect0LinkObjId="SW-159643_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1416,-417 1416,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_442e960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1416,-358 1416,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26508@0" ObjectIDZND0="26507@1" Pin0InfoVect0LinkObjId="SW-159642_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159643_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1416,-358 1416,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_442ebc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1416,-311 1416,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26507@0" ObjectIDZND0="26509@1" Pin0InfoVect0LinkObjId="SW-159644_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159642_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1416,-311 1416,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40e8690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1445,-191 1445,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26511@0" ObjectIDZND0="g_40e88f0@0" Pin0InfoVect0LinkObjId="g_40e88f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1445,-191 1445,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40e9340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1416,-253 1416,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26509@0" ObjectIDZND0="26511@x" ObjectIDZND1="g_44bc7b0@0" ObjectIDZND2="g_44b0b90@0" Pin0InfoVect0LinkObjId="SW-159647_0" Pin0InfoVect1LinkObjId="g_44bc7b0_0" Pin0InfoVect2LinkObjId="g_44b0b90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159644_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1416,-253 1416,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40e95a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1416,-125 1384,-125 1384,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34362@x" ObjectIDND1="26510@x" ObjectIDZND0="g_442dc90@0" Pin0InfoVect0LinkObjId="g_442dc90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-SB_YL.092Ld_0" Pin1InfoVect1LinkObjId="SW-159646_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1416,-125 1384,-125 1384,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40ea2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1148,-85 1148,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_4497760@1" Pin0InfoVect0LinkObjId="g_4497760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4148ee0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1148,-85 1148,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41126b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-237 424,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="26524@x" ObjectIDND1="g_44a57e0@0" ObjectIDND2="g_4151770@0" ObjectIDZND0="g_44b7ff0@0" ObjectIDZND1="26526@x" Pin0InfoVect0LinkObjId="g_44b7ff0_0" Pin0InfoVect1LinkObjId="SW-159734_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159731_0" Pin1InfoVect1LinkObjId="g_44a57e0_0" Pin1InfoVect2LinkObjId="g_4151770_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="376,-237 424,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41128a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-237 561,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="26519@x" ObjectIDND1="g_44a97d0@0" ObjectIDND2="g_44ab600@0" ObjectIDZND0="g_44b91e0@0" ObjectIDZND1="26521@x" Pin0InfoVect0LinkObjId="g_44b91e0_0" Pin0InfoVect1LinkObjId="SW-159705_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159702_0" Pin1InfoVect1LinkObjId="g_44a97d0_0" Pin1InfoVect2LinkObjId="g_44ab600_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="515,-237 561,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4112a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-236 825,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="26504@x" ObjectIDND1="g_44ae030@0" ObjectIDND2="g_44af110@0" ObjectIDZND0="g_44ba3d0@0" ObjectIDZND1="26506@x" Pin0InfoVect0LinkObjId="g_44ba3d0_0" Pin0InfoVect1LinkObjId="SW-159618_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159615_0" Pin1InfoVect1LinkObjId="g_44ae030_0" Pin1InfoVect2LinkObjId="g_44af110_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="778,-236 825,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4112c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-236 1273,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26514@x" ObjectIDND1="26515@x" ObjectIDND2="g_44b55e0@0" ObjectIDZND0="26516@x" ObjectIDZND1="g_44bb5c0@0" Pin0InfoVect0LinkObjId="SW-159676_0" Pin0InfoVect1LinkObjId="g_44bb5c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159673_0" Pin1InfoVect1LinkObjId="SW-159675_0" Pin1InfoVect2LinkObjId="g_44b55e0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-236 1273,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4112eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1416,-237 1462,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26509@x" ObjectIDND1="g_44b0b90@0" ObjectIDND2="g_44b72c0@0" ObjectIDZND0="26511@x" ObjectIDZND1="g_44bc7b0@0" Pin0InfoVect0LinkObjId="SW-159647_0" Pin0InfoVect1LinkObjId="g_44bc7b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159644_0" Pin1InfoVect1LinkObjId="g_44b0b90_0" Pin1InfoVect2LinkObjId="g_44b72c0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1416,-237 1462,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1bf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-866 584,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26497@1" ObjectIDZND0="26496@x" ObjectIDZND1="26499@x" Pin0InfoVect0LinkObjId="SW-159568_0" Pin0InfoVect1LinkObjId="SW-159587_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159574_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="584,-866 584,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1c170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-876 584,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26497@x" ObjectIDND1="26499@x" ObjectIDZND0="26496@0" Pin0InfoVect0LinkObjId="SW-159568_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159574_0" Pin1InfoVect1LinkObjId="SW-159587_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-876 584,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1f260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-876 617,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26497@x" ObjectIDND1="26496@x" ObjectIDZND0="26499@0" Pin0InfoVect0LinkObjId="SW-159587_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159574_0" Pin1InfoVect1LinkObjId="SW-159568_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-876 617,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1f4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="653,-876 668,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26499@1" ObjectIDZND0="g_2d1e7d0@0" Pin0InfoVect0LinkObjId="g_2d1e7d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159587_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="653,-876 668,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44788e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1403,-879 1418,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26493@1" ObjectIDZND0="g_4477e50@0" Pin0InfoVect0LinkObjId="g_4477e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159528_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1403,-879 1418,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4478b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-879 1367,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26490@x" ObjectIDND1="26491@x" ObjectIDZND0="26493@0" Pin0InfoVect0LinkObjId="SW-159528_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159509_0" Pin1InfoVect1LinkObjId="SW-159515_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-879 1367,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4479630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-892 1339,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26490@0" ObjectIDZND0="26491@x" ObjectIDZND1="26493@x" Pin0InfoVect0LinkObjId="SW-159515_0" Pin0InfoVect1LinkObjId="SW-159528_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159509_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-892 1339,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4479890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-879 1339,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26490@x" ObjectIDND1="26493@x" ObjectIDZND0="26491@1" Pin0InfoVect0LinkObjId="SW-159515_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159509_0" Pin1InfoVect1LinkObjId="SW-159528_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-879 1339,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_448cac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-131 1224,-77 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="g_41296e0@0" ObjectIDND1="26515@x" ObjectIDND2="g_44b4440@0" ObjectIDZND0="34361@0" Pin0InfoVect0LinkObjId="EC-SB_YL.091Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_41296e0_0" Pin1InfoVect1LinkObjId="SW-159675_0" Pin1InfoVect2LinkObjId="g_44b4440_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-131 1224,-77 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_448ccb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-236 1224,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="26514@x" ObjectIDND1="g_44b55e0@0" ObjectIDND2="26516@x" ObjectIDZND0="26515@1" Pin0InfoVect0LinkObjId="SW-159675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159673_0" Pin1InfoVect1LinkObjId="g_44b55e0_0" Pin1InfoVect2LinkObjId="SW-159676_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-236 1224,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_448d6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-131 1224,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_41296e0@0" ObjectIDND1="34361@x" ObjectIDZND0="26515@1" ObjectIDZND1="g_44b4440@0" Pin0InfoVect0LinkObjId="SW-159675_1" Pin0InfoVect1LinkObjId="g_44b4440_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41296e0_0" Pin1InfoVect1LinkObjId="EC-SB_YL.091Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-131 1224,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_448d910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-168 1224,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="26515@0" ObjectIDZND0="g_41296e0@0" ObjectIDZND1="34361@x" ObjectIDZND2="g_44b4440@0" Pin0InfoVect0LinkObjId="g_41296e0_0" Pin0InfoVect1LinkObjId="EC-SB_YL.091Ld_0" Pin0InfoVect2LinkObjId="g_44b4440_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-168 1224,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4492c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-498 1488,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26481@x" ObjectIDND1="26482@x" ObjectIDZND0="g_40ec920@0" ObjectIDZND1="g_40cb280@0" Pin0InfoVect0LinkObjId="g_40ec920_0" Pin0InfoVect1LinkObjId="g_40cb280_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159307_0" Pin1InfoVect1LinkObjId="SW-159308_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-498 1488,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4492e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-509 1488,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_40ec920@0" ObjectIDND1="26481@x" ObjectIDND2="26482@x" ObjectIDZND0="g_40cb280@0" Pin0InfoVect0LinkObjId="g_40cb280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40ec920_0" Pin1InfoVect1LinkObjId="SW-159307_0" Pin1InfoVect2LinkObjId="SW-159308_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-509 1488,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44930e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-428 568,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26487@0" ObjectIDZND0="26476@0" Pin0InfoVect0LinkObjId="g_4187740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="568,-428 568,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4493340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-516 568,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26488@0" ObjectIDZND0="26486@1" Pin0InfoVect0LinkObjId="SW-159419_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="568,-516 568,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44935a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-477 568,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26486@0" ObjectIDZND0="26487@1" Pin0InfoVect0LinkObjId="SW-159421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159419_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="568,-477 568,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_44942f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-979 1232,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="26607@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4148ee0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-979 1232,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_449bb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-1024 1339,-1044 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="26495@x" ObjectIDND1="26492@x" ObjectIDZND0="g_41885a0@0" ObjectIDZND1="g_412b5f0@0" ObjectIDZND2="37757@1" Pin0InfoVect0LinkObjId="g_41885a0_0" Pin0InfoVect1LinkObjId="g_412b5f0_0" Pin0InfoVect2LinkObjId="g_3d55960_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159530_0" Pin1InfoVect1LinkObjId="SW-159522_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-1024 1339,-1044 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_449bdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-1044 1339,-1062 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="26495@x" ObjectIDND1="26492@x" ObjectIDND2="0@x" ObjectIDZND0="g_41885a0@0" ObjectIDZND1="g_412b5f0@0" ObjectIDZND2="37757@1" Pin0InfoVect0LinkObjId="g_41885a0_0" Pin0InfoVect1LinkObjId="g_412b5f0_0" Pin0InfoVect2LinkObjId="g_3d55960_1" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159530_0" Pin1InfoVect1LinkObjId="SW-159522_0" Pin1InfoVect2LinkObjId="g_4148ee0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-1044 1339,-1062 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44a3370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-1038 1232,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="26495@x" ObjectIDND1="26492@x" ObjectIDND2="g_41885a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_4148ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159530_0" Pin1InfoVect1LinkObjId="SW-159522_0" Pin1InfoVect2LinkObjId="g_41885a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-1038 1232,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44a3560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1339,-1044 1232,-1044 1232,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26495@x" ObjectIDND1="26492@x" ObjectIDND2="g_41885a0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_44a3960@0" Pin0InfoVect0LinkObjId="g_4148ee0_0" Pin0InfoVect1LinkObjId="g_44a3960_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159530_0" Pin1InfoVect1LinkObjId="SW-159522_0" Pin1InfoVect2LinkObjId="g_41885a0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1339,-1044 1232,-1044 1232,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44a3750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1213,-1028 1213,-1038 1232,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_44a3960@0" ObjectIDZND0="0@x" ObjectIDZND1="26495@x" ObjectIDZND2="26492@x" Pin0InfoVect0LinkObjId="g_4148ee0_0" Pin0InfoVect1LinkObjId="SW-159530_0" Pin0InfoVect2LinkObjId="SW-159522_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44a3960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1213,-1028 1213,-1038 1232,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44a4570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-716 568,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="26484@0" ObjectIDZND0="26530@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="568,-716 568,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a5320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-624 568,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="26530@1" ObjectIDZND0="g_44a47a0@1" Pin0InfoVect0LinkObjId="g_44a47a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44a4570_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="568,-624 568,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a5580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="568,-573 568,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_44a47a0@0" ObjectIDZND0="26488@x" ObjectIDZND1="g_4161c70@0" Pin0InfoVect0LinkObjId="SW-159422_0" Pin0InfoVect1LinkObjId="g_4161c70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44a47a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="568,-573 568,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a6400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-237 376,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="26524@x" ObjectIDND1="g_44b7ff0@0" ObjectIDND2="26526@x" ObjectIDZND0="g_44a57e0@1" Pin0InfoVect0LinkObjId="g_44a57e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159731_0" Pin1InfoVect1LinkObjId="g_44b7ff0_0" Pin1InfoVect2LinkObjId="SW-159734_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-237 376,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a6660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-169 376,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="26525@1" ObjectIDZND0="g_44a57e0@0" Pin0InfoVect0LinkObjId="g_44a57e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159733_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-169 376,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a7150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-124 376,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_40d7330@0" ObjectIDND1="34358@x" ObjectIDZND0="26525@0" Pin0InfoVect0LinkObjId="SW-159733_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_40d7330_0" Pin1InfoVect1LinkObjId="EC-SB_YL.081Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-124 376,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a73b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-78 376,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34358@0" ObjectIDZND0="g_40d7330@0" ObjectIDZND1="26525@x" Pin0InfoVect0LinkObjId="g_40d7330_0" Pin0InfoVect1LinkObjId="SW-159733_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-SB_YL.081Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="376,-78 376,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a7610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="376,-237 353,-237 353,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26524@x" ObjectIDND1="g_44a57e0@0" ObjectIDND2="g_44b7ff0@0" ObjectIDZND0="g_4151770@0" Pin0InfoVect0LinkObjId="g_4151770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159731_0" Pin1InfoVect1LinkObjId="g_44a57e0_0" Pin1InfoVect2LinkObjId="g_44b7ff0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="376,-237 353,-237 353,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a7870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-237 492,-237 492,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26519@x" ObjectIDND1="g_44a97d0@0" ObjectIDND2="g_44b91e0@0" ObjectIDZND0="g_44ab600@0" Pin0InfoVect0LinkObjId="g_44ab600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159702_0" Pin1InfoVect1LinkObjId="g_44a97d0_0" Pin1InfoVect2LinkObjId="g_44b91e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,-237 492,-237 492,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a8360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="432,-219 432,-237 424,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_44b7ff0@0" ObjectIDZND0="26524@x" ObjectIDZND1="g_44a57e0@0" ObjectIDZND2="g_4151770@0" Pin0InfoVect0LinkObjId="SW-159731_0" Pin0InfoVect1LinkObjId="g_44a57e0_0" Pin0InfoVect2LinkObjId="g_4151770_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44b7ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="432,-219 432,-237 424,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a85c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="404,-227 404,-237 424,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26526@1" ObjectIDZND0="26524@x" ObjectIDZND1="g_44a57e0@0" ObjectIDZND2="g_4151770@0" Pin0InfoVect0LinkObjId="SW-159731_0" Pin0InfoVect1LinkObjId="g_44a57e0_0" Pin0InfoVect2LinkObjId="g_4151770_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159734_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="404,-227 404,-237 424,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a90b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,-110 483,-123 515,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_4138700@0" ObjectIDZND0="34359@x" ObjectIDZND1="26520@x" Pin0InfoVect0LinkObjId="EC-SB_YL.082Ld_0" Pin0InfoVect1LinkObjId="SW-159704_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4138700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="483,-110 483,-123 515,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a9310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-123 515,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_4138700@0" ObjectIDND1="26520@x" ObjectIDZND0="34359@0" Pin0InfoVect0LinkObjId="EC-SB_YL.082Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4138700_0" Pin1InfoVect1LinkObjId="SW-159704_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,-123 515,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44a9570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-123 515,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_4138700@0" ObjectIDND1="34359@x" ObjectIDZND0="26520@0" Pin0InfoVect0LinkObjId="SW-159704_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4138700_0" Pin1InfoVect1LinkObjId="EC-SB_YL.082Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,-123 515,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44aa3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-170 515,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="26520@1" ObjectIDZND0="g_44a97d0@0" Pin0InfoVect0LinkObjId="g_44a97d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159704_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="515,-170 515,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44aa650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,-225 515,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_44a97d0@1" ObjectIDZND0="26519@x" ObjectIDZND1="g_44b91e0@0" ObjectIDZND2="26521@x" Pin0InfoVect0LinkObjId="SW-159702_0" Pin0InfoVect1LinkObjId="g_44b91e0_0" Pin0InfoVect2LinkObjId="SW-159705_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44a97d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="515,-225 515,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44ab140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,-219 574,-237 561,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_44b91e0@0" ObjectIDZND0="26519@x" ObjectIDZND1="g_44a97d0@0" ObjectIDZND2="g_44ab600@0" Pin0InfoVect0LinkObjId="SW-159702_0" Pin0InfoVect1LinkObjId="g_44a97d0_0" Pin0InfoVect2LinkObjId="g_44ab600_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44b91e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="574,-219 574,-237 561,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44ab3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="544,-227 544,-237 561,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26521@1" ObjectIDZND0="26519@x" ObjectIDZND1="g_44a97d0@0" ObjectIDZND2="g_44ab600@0" Pin0InfoVect0LinkObjId="SW-159702_0" Pin0InfoVect1LinkObjId="g_44a97d0_0" Pin0InfoVect2LinkObjId="g_44ab600_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159705_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="544,-227 544,-237 561,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44acbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-124 778,-77 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_4125370@0" ObjectIDND1="26505@x" ObjectIDZND0="34360@0" Pin0InfoVect0LinkObjId="EC-SB_YL.083Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4125370_0" Pin1InfoVect1LinkObjId="SW-159617_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,-124 778,-77 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44ace20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-236 755,-236 755,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="26504@x" ObjectIDND1="g_44ba3d0@0" ObjectIDND2="26506@x" ObjectIDZND0="g_44af110@0" Pin0InfoVect0LinkObjId="g_44af110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159615_0" Pin1InfoVect1LinkObjId="g_44ba3d0_0" Pin1InfoVect2LinkObjId="SW-159618_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,-236 755,-236 755,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44ad910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="838,-218 838,-236 825,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_44ba3d0@0" ObjectIDZND0="26504@x" ObjectIDZND1="g_44ae030@0" ObjectIDZND2="g_44af110@0" Pin0InfoVect0LinkObjId="SW-159615_0" Pin0InfoVect1LinkObjId="g_44ae030_0" Pin0InfoVect2LinkObjId="g_44af110_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44ba3d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="838,-218 838,-236 825,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44adb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="809,-226 809,-236 825,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26506@1" ObjectIDZND0="26504@x" ObjectIDZND1="g_44ae030@0" ObjectIDZND2="g_44af110@0" Pin0InfoVect0LinkObjId="SW-159615_0" Pin0InfoVect1LinkObjId="g_44ae030_0" Pin0InfoVect2LinkObjId="g_44af110_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159618_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="809,-226 809,-236 825,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44addd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-124 778,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_4125370@0" ObjectIDND1="34360@x" ObjectIDZND0="26505@0" Pin0InfoVect0LinkObjId="SW-159617_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4125370_0" Pin1InfoVect1LinkObjId="EC-SB_YL.083Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,-124 778,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44aec50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-169 778,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="26505@1" ObjectIDZND0="g_44ae030@0" Pin0InfoVect0LinkObjId="g_44ae030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159617_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="778,-169 778,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44aeeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="778,-224 778,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_44ae030@1" ObjectIDZND0="26504@x" ObjectIDZND1="g_44ba3d0@0" ObjectIDZND2="26506@x" Pin0InfoVect0LinkObjId="SW-159615_0" Pin0InfoVect1LinkObjId="g_44ba3d0_0" Pin0InfoVect2LinkObjId="SW-159618_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44ae030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="778,-224 778,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b06d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1416,-125 1416,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_442dc90@0" ObjectIDND1="26510@x" ObjectIDZND0="34362@0" Pin0InfoVect0LinkObjId="EC-SB_YL.092Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_442dc90_0" Pin1InfoVect1LinkObjId="SW-159646_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1416,-125 1416,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b0930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1416,-135 1416,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26510@0" ObjectIDZND0="g_442dc90@0" ObjectIDZND1="34362@x" Pin0InfoVect0LinkObjId="g_442dc90_0" Pin0InfoVect1LinkObjId="EC-SB_YL.092Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159646_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1416,-135 1416,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b2040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-219 1475,-237 1462,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_44bc7b0@0" ObjectIDZND0="26509@x" ObjectIDZND1="g_44b0b90@0" ObjectIDZND2="g_44b72c0@0" Pin0InfoVect0LinkObjId="SW-159644_0" Pin0InfoVect1LinkObjId="g_44b0b90_0" Pin0InfoVect2LinkObjId="g_44b72c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44bc7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-219 1475,-237 1462,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b22a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1445,-227 1445,-237 1462,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26511@1" ObjectIDZND0="26509@x" ObjectIDZND1="g_44b0b90@0" ObjectIDZND2="g_44b72c0@0" Pin0InfoVect0LinkObjId="SW-159644_0" Pin0InfoVect1LinkObjId="g_44b0b90_0" Pin0InfoVect2LinkObjId="g_44b72c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159647_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1445,-227 1445,-237 1462,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b2500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1416,-237 1416,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26509@x" ObjectIDND1="26511@x" ObjectIDND2="g_44bc7b0@0" ObjectIDZND0="g_44b0b90@1" Pin0InfoVect0LinkObjId="g_44b0b90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159644_0" Pin1InfoVect1LinkObjId="SW-159647_0" Pin1InfoVect2LinkObjId="g_44bc7b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1416,-237 1416,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b2760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1416,-186 1416,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_44b0b90@0" ObjectIDZND0="26510@1" Pin0InfoVect0LinkObjId="SW-159646_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44b0b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1416,-186 1416,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b29c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1148,-150 1128,-150 1128,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_44b4440@0" ObjectIDZND0="g_44b2c20@0" Pin0InfoVect0LinkObjId="g_44b2c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4148ee0_0" Pin1InfoVect1LinkObjId="g_44b4440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1148,-150 1128,-150 1128,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b41e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1148,-154 1148,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_44b2c20@0" ObjectIDND1="g_44b4440@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_4148ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_44b2c20_0" Pin1InfoVect1LinkObjId="g_44b4440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1148,-154 1148,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b4ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1223,-154 1195,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_41296e0@0" ObjectIDND1="34361@x" ObjectIDND2="26515@1" ObjectIDZND0="g_44b4440@0" Pin0InfoVect0LinkObjId="g_44b4440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_41296e0_0" Pin1InfoVect1LinkObjId="EC-SB_YL.091Ld_0" Pin1InfoVect2LinkObjId="SW-159675_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1223,-154 1195,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b5120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,-154 1148,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_44b4440@1" ObjectIDZND0="g_44b2c20@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_44b2c20_0" Pin0InfoVect1LinkObjId="g_4148ee0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44b4440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1162,-154 1148,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b5380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-236 1201,-236 1201,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="26514@x" ObjectIDND1="26515@x" ObjectIDND2="26516@x" ObjectIDZND0="g_44b55e0@0" Pin0InfoVect0LinkObjId="g_44b55e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159673_0" Pin1InfoVect1LinkObjId="SW-159675_0" Pin1InfoVect2LinkObjId="SW-159676_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-236 1201,-236 1201,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b6ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-226 1270,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="26516@1" ObjectIDZND0="26514@x" ObjectIDZND1="26515@x" ObjectIDZND2="g_44b55e0@0" Pin0InfoVect0LinkObjId="SW-159673_0" Pin0InfoVect1LinkObjId="SW-159675_0" Pin0InfoVect2LinkObjId="g_44b55e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159676_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1270,-226 1270,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b6e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1297,-218 1297,-236 1270,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_44bb5c0@0" ObjectIDZND0="26514@x" ObjectIDZND1="26515@x" ObjectIDZND2="g_44b55e0@0" Pin0InfoVect0LinkObjId="SW-159673_0" Pin0InfoVect1LinkObjId="SW-159675_0" Pin0InfoVect2LinkObjId="g_44b55e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44bb5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1297,-218 1297,-236 1270,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b7060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-237 1394,-237 1394,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26509@x" ObjectIDND1="26511@x" ObjectIDND2="g_44bc7b0@0" ObjectIDZND0="g_44b72c0@0" Pin0InfoVect0LinkObjId="g_44b72c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-159644_0" Pin1InfoVect1LinkObjId="SW-159647_0" Pin1InfoVect2LinkObjId="g_44bc7b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-237 1394,-237 1394,-227 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="SB_YL"/>
</svg>