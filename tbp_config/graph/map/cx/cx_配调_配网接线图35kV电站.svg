<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="106 -991 1683 938">
 
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d87850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d87e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d62930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1de3480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cd2090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d43eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1de99d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dea210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d9dda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf7160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf7160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf6f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf6f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1d04510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1de06e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cfb290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cfba30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cf96f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cd1590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dfd100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dfd6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dfdfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d8bb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc8570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc8f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d30a50" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dd6f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1da7e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e0fbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d07230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1e23260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d89910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="948" width="1693" x="101" y="-996"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="36" qtmmishow="hidden" width="208" x="186" y="-768"/>
    </a>
   <metadata/><rect fill="white" height="36" opacity="0" stroke="white" transform="" width="208" x="186" y="-768"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="39" qtmmishow="hidden" width="206" x="187" y="-703"/>
    </a>
   <metadata/><rect fill="white" height="39" opacity="0" stroke="white" transform="" width="206" x="187" y="-703"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="39" qtmmishow="hidden" width="207" x="186" y="-634"/>
    </a>
   <metadata/><rect fill="white" height="39" opacity="0" stroke="white" transform="" width="207" x="186" y="-634"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="39" qtmmishow="hidden" width="208" x="187" y="-500"/>
    </a>
   <metadata/><rect fill="white" height="39" opacity="0" stroke="white" transform="" width="208" x="187" y="-500"/></g>
   <g DF8003:Layer="PUBLIC" DownImage="image/btnd_04.png" ImageFlag="1" OverImage="image/btnd_04.png" UpImage="image/btnd_03.png" imageHeight="42" imageWidth="165">
    <a>
     
     <rect fill="none" height="44" qtmmishow="hidden" width="159" x="481" y="-749"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="159" x="481" y="-749"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/fhzy.png" imageHeight="83" imageWidth="84">
    <a>
     
     <rect fill="none" height="36" qtmmishow="hidden" width="42" x="1693" y="-966"/>
    </a>
   <metadata/><rect fill="white" height="36" opacity="0" stroke="white" transform="" width="42" x="1693" y="-966"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/jt3.png" imageHeight="128" imageWidth="128">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="42" x="1698" y="-842"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="42" x="1698" y="-842"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="39" qtmmishow="hidden" width="205" x="187" y="-431"/>
    </a>
   <metadata/><rect fill="white" height="39" opacity="0" stroke="white" transform="" width="205" x="187" y="-431"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="39" qtmmishow="hidden" width="206" x="188" y="-567"/>
    </a>
   <metadata/><rect fill="white" height="39" opacity="0" stroke="white" transform="" width="206" x="188" y="-567"/></g>
   <g DF8003:Layer="PUBLIC" DownImage="image/btnd_04.png" ImageFlag="1" OverImage="image/btnd_04.png" UpImage="image/btnd_03.png" imageHeight="42" imageWidth="165">
    <a>
     
     <rect fill="none" height="44" qtmmishow="hidden" width="162" x="481" y="-267"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="162" x="481" y="-267"/></g>
   <g DF8003:Layer="PUBLIC" DownImage="image/btnd_04.png" ImageFlag="1" OverImage="image/btnd_04.png" UpImage="image/btnd_03.png" imageHeight="42" imageWidth="165">
    <a>
     
     <rect fill="none" height="44" qtmmishow="hidden" width="162" x="481" y="-214"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="162" x="481" y="-214"/></g>
   <g DF8003:Layer="PUBLIC" DownImage="image/btnd_04.png" ImageFlag="1" OverImage="image/btnd_04.png" UpImage="image/btnd_03.png" imageHeight="42" imageWidth="165">
    <a>
     
     <rect fill="none" height="44" qtmmishow="hidden" width="159" x="481" y="-695"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="159" x="481" y="-695"/></g>
   <g DF8003:Layer="PUBLIC" DownImage="image/btnd_04.png" ImageFlag="1" OverImage="image/btnd_04.png" UpImage="image/btnd_03.png" imageHeight="42" imageWidth="165">
    <a>
     
     <rect fill="none" height="44" qtmmishow="hidden" width="159" x="481" y="-642"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="159" x="481" y="-642"/></g>
   <g DF8003:Layer="PUBLIC" DownImage="image/btnd_04.png" ImageFlag="1" OverImage="image/btnd_04.png" UpImage="image/btnd_03.png" imageHeight="42" imageWidth="165">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="164" x="482" y="-585"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="164" x="482" y="-585"/></g>
   <g DF8003:Layer="PUBLIC" DownImage="image/btnd_04.png" ImageFlag="1" OverImage="image/btnd_04.png" UpImage="image/btnd_03.png" imageHeight="42" imageWidth="165">
    <a>
     
     <rect fill="none" height="44" qtmmishow="hidden" width="159" x="481" y="-535"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="159" x="481" y="-535"/></g>
   <g DF8003:Layer="PUBLIC" DownImage="image/btnd_04.png" ImageFlag="1" OverImage="image/btnd_04.png" UpImage="image/btnd_03.png" imageHeight="42" imageWidth="165">
    <a>
     
     <rect fill="none" height="44" qtmmishow="hidden" width="162" x="481" y="-481"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="162" x="481" y="-481"/></g>
   <g DF8003:Layer="PUBLIC" DownImage="image/btnd_04.png" ImageFlag="1" OverImage="image/btnd_04.png" UpImage="image/btnd_03.png" imageHeight="42" imageWidth="165">
    <a>
     
     <rect fill="none" height="44" qtmmishow="hidden" width="162" x="481" y="-428"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="162" x="481" y="-428"/></g>
   <g DF8003:Layer="PUBLIC" DownImage="image/btnd_04.png" ImageFlag="1" OverImage="image/btnd_04.png" UpImage="image/btnd_03.png" imageHeight="42" imageWidth="165">
    <a>
     
     <rect fill="none" height="44" qtmmishow="hidden" width="162" x="481" y="-374"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="162" x="481" y="-374"/></g>
   <g DF8003:Layer="PUBLIC" DownImage="image/btnd_04.png" ImageFlag="1" OverImage="image/btnd_04.png" UpImage="image/btnd_03.png" imageHeight="42" imageWidth="165">
    <a>
     
     <rect fill="none" height="44" qtmmishow="hidden" width="162" x="481" y="-321"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="162" x="481" y="-321"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图.svg" style="fill-opacity:0"><rect height="36" qtmmishow="hidden" width="208" x="186" y="-768"/></g>
   <g href="cx_配调_配网接线图110.svg" style="fill-opacity:0"><rect height="39" qtmmishow="hidden" width="206" x="187" y="-703"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="39" qtmmishow="hidden" width="207" x="186" y="-634"/></g>
   <g href="cx_配调_配网接线图10.svg" style="fill-opacity:0"><rect height="39" qtmmishow="hidden" width="208" x="187" y="-500"/></g>
   <g href="楚雄地区_35kV_青山嘴坝后电站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="159" x="481" y="-749"/></g>
   <g href="cx_index.svg" style="fill-opacity:0"><rect height="36" qtmmishow="hidden" width="42" x="1693" y="-966"/></g>
   <g href="cx_配调_监视中心.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="42" x="1698" y="-842"/></g>
   <g href="cx_配调_配网接线图开闭所.svg" style="fill-opacity:0"><rect height="39" qtmmishow="hidden" width="205" x="187" y="-431"/></g>
   <g href="cx_配调_配网接线图35kV电站.svg" style="fill-opacity:0"><rect height="39" qtmmishow="hidden" width="206" x="188" y="-567"/></g>
   <g href="武定_35kV_大响水.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="162" x="481" y="-267"/></g>
   <g href="武定_35kV_永厂河电站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="162" x="481" y="-214"/></g>
   <g href="楚雄地区_35kV_大海波水电站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="159" x="481" y="-695"/></g>
   <g href="楚雄地区_35kV_花桥水电站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="159" x="481" y="-642"/></g>
   <g href="楚雄地区_35kV_妥安水电站.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="164" x="482" y="-585"/></g>
   <g href="禄丰_10kV_滚水变.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="159" x="481" y="-535"/></g>
   <g href="大姚_35kV_天生桥一级电站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="162" x="481" y="-481"/></g>
   <g href="大姚_35kV_天生桥二级水电站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="162" x="481" y="-428"/></g>
   <g href="大姚_35kV_三岔河一级电站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="162" x="481" y="-374"/></g>
   <g href="大姚_35kV_碧么变.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="162" x="481" y="-321"/></g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimHei" font-size="22" graphid="g_3153280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.500000 -761.000000) translate(0,18)">区域电网图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimHei" font-size="22" graphid="g_1cfd2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.000000 -421.366667) translate(0,18)">10kV开闭所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="22" graphid="g_1d44280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.500000 -557.900000) translate(0,18)">35kV电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimHei" font-size="22" graphid="g_1e2b200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.500000 -625.933333) translate(0,18)">35kV变电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimHei" font-size="22" graphid="g_2d0aba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.000000 -490.400000) translate(0,18)">10kV电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="18" graphid="g_1e2b900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 507.200000 -736.500000) translate(0,15)">青山嘴坝后电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="16" graphid="g_322c9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1691.700000 -926.000000) translate(0,13)">返回主页</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="16" graphid="g_1d0ade0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1694.700000 -805.000000) translate(0,13)">返回监视</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimHei" font-size="22" graphid="g_312d3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.500000 -695.933333) translate(0,18)">110kV及以上</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="22" graphid="g_3060c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 463.500000 -813.000000) translate(0,18)">配网变电站一次接线图监控索引</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="17" graphid="g_22a60a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.700000 -252.463889) translate(0,14)">大响水电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="17" graphid="g_3080970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.700000 -199.063889) translate(0,14)">永厂河电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="20" graphid="g_2d68810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.200000 -683.100000) translate(0,16)">大海波电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="20" graphid="g_3013340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 530.200000 -629.700000) translate(0,16)">花桥电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="20" graphid="g_2fd5b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 530.200000 -576.300000) translate(0,16)">妥安电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="20" graphid="g_1dbf460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 530.200000 -522.900000) translate(0,16)">滚水坝变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="17" graphid="g_1d38f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 510.700000 -466.063889) translate(0,14)">天生桥一级电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="17" graphid="g_2c83020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 510.700000 -412.663889) translate(0,14)">天生桥二级电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="17" graphid="g_1d3a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 510.700000 -359.263889) translate(0,14)">三岔河一级电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="17" graphid="g_2daf3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.700000 -305.863889) translate(0,14)">碧么水电站</text>
  </g><g id="Image_Layer">
   
   
   
   
   
   
   
   
  </g><g areaN="0" fileType="0" fixScaleFlag="0" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>