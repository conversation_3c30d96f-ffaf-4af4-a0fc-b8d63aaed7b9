<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-207" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-730 -1270 2063 1131">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape189">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="13" y1="21" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="13,64 38,64 38,35 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="13" y1="58" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="32" x2="44" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="41" x2="35" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="39" x2="37" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="13" y1="21" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="53" y2="0"/>
    <circle cx="13" cy="66" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="80" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="84" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="88" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="60" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="64" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="68" y2="64"/>
    <ellipse cx="13" cy="82" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="lightningRod:shape204">
    <rect height="31" stroke-width="0.5" width="16" x="12" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="30" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="2" y1="30" y2="10"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="voltageTransformer:shape130">
    <ellipse cx="16" cy="8" fillStyle="0" rx="7" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="67" x2="23" y1="9" y2="9"/>
    <rect height="14" stroke-width="0.379884" width="24" x="30" y="2"/>
   </symbol>
   <symbol id="voltageTransformer:shape85">
    <circle cx="20" cy="32" fillStyle="0" r="8" stroke-width="0.570276"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="11" x2="13" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="13" x2="16" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="11" x2="13" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="25" x2="27" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="27" x2="30" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="25" x2="27" y1="16" y2="20"/>
    <circle cx="13" cy="20" fillStyle="0" r="8" stroke-width="0.570276"/>
    <circle cx="27" cy="20" fillStyle="0" r="8" stroke-width="0.570276"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="17" x2="19" y1="37" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.960801" x1="21" x2="21" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.989747" x1="18" x2="21" y1="5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.992459" x1="18" x2="21" y1="10" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="19" x2="22" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="17" x2="19" y1="30" y2="34"/>
    <circle cx="20" cy="8" fillStyle="0" r="8" stroke-width="0.536731"/>
   </symbol>
   <symbol id="voltageTransformer:shape89">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.992459" x1="27" x2="28" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.989747" x1="22" x2="21" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.960801" x1="21" x2="28" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
    <circle cx="23" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="23" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="27" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="7" y2="11"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2db2d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db3a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2db42a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2db4f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2db5df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2db6a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db7600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2db8000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1f1d330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1f1d330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dbbb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dbbb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dbd2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dbd2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2dbdf70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dbfcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dc08a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dc16c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dc2000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dc36b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dc43a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dc4c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dc5420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dc6500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dc6e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dc7970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dc8330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dc9760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dca370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dcb350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dcc010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dda810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dcd6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2dce380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2dcf8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1141" width="2073" x="-735" y="-1275"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1324" x2="1333" y1="-563" y2="-563"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="547" x2="547" y1="-905" y2="-905"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="492" x2="492" y1="-141" y2="-141"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1325" x2="1325" y1="-899" y2="-899"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="491" x2="491" y1="-583" y2="-583"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="491" x2="491" y1="-582" y2="-582"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="23" stroke="rgb(255,255,0)" stroke-width="0.350829" width="12" x="361" y="-892"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="23" stroke="rgb(0,255,0)" stroke-width="0.350829" width="12" x="-115" y="-694"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-364" y="-1191"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-136709">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 229.000000 -1003.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24709" ObjectName="SW-MD_LP.MD_LP_341XC1"/>
     <cge:Meas_Ref ObjectId="136709"/>
    <cge:TPSR_Ref TObjectID="24709"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136709">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 229.000000 -941.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24708" ObjectName="SW-MD_LP.MD_LP_341XC"/>
     <cge:Meas_Ref ObjectId="136709"/>
    <cge:TPSR_Ref TObjectID="24708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136728">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.000000 -1001.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24712" ObjectName="SW-MD_LP.MD_LP_342XC1"/>
     <cge:Meas_Ref ObjectId="136728"/>
    <cge:TPSR_Ref TObjectID="24712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136728">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.000000 -939.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24711" ObjectName="SW-MD_LP.MD_LP_342XC"/>
     <cge:Meas_Ref ObjectId="136728"/>
    <cge:TPSR_Ref TObjectID="24711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136969">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.000000 -952.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24742" ObjectName="SW-MD_LP.MD_LP_3901XC"/>
     <cge:Meas_Ref ObjectId="136969"/>
    <cge:TPSR_Ref TObjectID="24742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136749">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.156028 -874.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24714" ObjectName="SW-MD_LP.MD_LP_301XC"/>
     <cge:Meas_Ref ObjectId="136749"/>
    <cge:TPSR_Ref TObjectID="24714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136749">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.156028 -809.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24715" ObjectName="SW-MD_LP.MD_LP_301XC1"/>
     <cge:Meas_Ref ObjectId="136749"/>
    <cge:TPSR_Ref TObjectID="24715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136757">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.156028 -585.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24717" ObjectName="SW-MD_LP.MD_LP_0011SW"/>
     <cge:Meas_Ref ObjectId="136757"/>
    <cge:TPSR_Ref TObjectID="24717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136967">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 561.243972 -580.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24741" ObjectName="SW-MD_LP.MD_LP_0122SW"/>
     <cge:Meas_Ref ObjectId="136967"/>
    <cge:TPSR_Ref TObjectID="24741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136966">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.243972 -587.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24740" ObjectName="SW-MD_LP.MD_LP_0121SW"/>
     <cge:Meas_Ref ObjectId="136966"/>
    <cge:TPSR_Ref TObjectID="24740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297943">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 357.000000 -893.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46173" ObjectName="SW-MD_LP.MD_LP_3401XC"/>
     <cge:Meas_Ref ObjectId="297943"/>
    <cge:TPSR_Ref TObjectID="46173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-297943">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 357.000000 -837.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46174" ObjectName="SW-MD_LP.MD_LP_3401XC1"/>
     <cge:Meas_Ref ObjectId="297943"/>
    <cge:TPSR_Ref TObjectID="46174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136806">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 -871.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24752" ObjectName="SW-MD_LP.MD_LP_302XC"/>
     <cge:Meas_Ref ObjectId="136806"/>
    <cge:TPSR_Ref TObjectID="24752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136806">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 -809.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24753" ObjectName="SW-MD_LP.MD_LP_302XC1"/>
     <cge:Meas_Ref ObjectId="136806"/>
    <cge:TPSR_Ref TObjectID="24753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136812">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 710.000000 -585.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24720" ObjectName="SW-MD_LP.MD_LP_0022SW"/>
     <cge:Meas_Ref ObjectId="136812"/>
    <cge:TPSR_Ref TObjectID="24720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136973">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -118.175887 -589.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24744" ObjectName="SW-MD_LP.MD_LP_0901SW"/>
     <cge:Meas_Ref ObjectId="136973"/>
    <cge:TPSR_Ref TObjectID="24744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136975">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 991.000000 -583.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24745" ObjectName="SW-MD_LP.MD_LP_0902SW"/>
     <cge:Meas_Ref ObjectId="136975"/>
    <cge:TPSR_Ref TObjectID="24745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136858">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -217.000000 -501.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24722" ObjectName="SW-MD_LP.MD_LP_0411SW"/>
     <cge:Meas_Ref ObjectId="136858"/>
    <cge:TPSR_Ref TObjectID="24722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136859">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -217.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24723" ObjectName="SW-MD_LP.MD_LP_0416SW"/>
     <cge:Meas_Ref ObjectId="136859"/>
    <cge:TPSR_Ref TObjectID="24723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136876">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -46.333333 -501.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24725" ObjectName="SW-MD_LP.MD_LP_0421SW"/>
     <cge:Meas_Ref ObjectId="136876"/>
    <cge:TPSR_Ref TObjectID="24725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136877">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -46.333333 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24726" ObjectName="SW-MD_LP.MD_LP_0426SW"/>
     <cge:Meas_Ref ObjectId="136877"/>
    <cge:TPSR_Ref TObjectID="24726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136896">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 124.333333 -501.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24728" ObjectName="SW-MD_LP.MD_LP_0431SW"/>
     <cge:Meas_Ref ObjectId="136896"/>
    <cge:TPSR_Ref TObjectID="24728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136897">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 124.333333 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24729" ObjectName="SW-MD_LP.MD_LP_0436SW"/>
     <cge:Meas_Ref ObjectId="136897"/>
    <cge:TPSR_Ref TObjectID="24729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136912">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 -501.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24731" ObjectName="SW-MD_LP.MD_LP_0441SW"/>
     <cge:Meas_Ref ObjectId="136912"/>
    <cge:TPSR_Ref TObjectID="24731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136913">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24732" ObjectName="SW-MD_LP.MD_LP_0446SW"/>
     <cge:Meas_Ref ObjectId="136913"/>
    <cge:TPSR_Ref TObjectID="24732"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136930">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 660.000000 -500.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24734" ObjectName="SW-MD_LP.MD_LP_0451SW"/>
     <cge:Meas_Ref ObjectId="136930"/>
    <cge:TPSR_Ref TObjectID="24734"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136931">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 660.000000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24735" ObjectName="SW-MD_LP.MD_LP_0456SW"/>
     <cge:Meas_Ref ObjectId="136931"/>
    <cge:TPSR_Ref TObjectID="24735"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136948">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.000000 -500.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24737" ObjectName="SW-MD_LP.MD_LP_0461SW"/>
     <cge:Meas_Ref ObjectId="136948"/>
    <cge:TPSR_Ref TObjectID="24737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.000000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24738" ObjectName="SW-MD_LP.MD_LP_0466SW"/>
     <cge:Meas_Ref ObjectId="136949"/>
    <cge:TPSR_Ref TObjectID="24738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1127.000000 -414.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296736">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.869565 -0.000000 0.000000 -1.071429 385.000000 -631.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46061" ObjectName="SW-MD_LP.MD_LP_01217SW"/>
     <cge:Meas_Ref ObjectId="296736"/>
    <cge:TPSR_Ref TObjectID="46061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-288153">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.869565 -0.000000 0.000000 -1.071429 583.000000 -635.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45018" ObjectName="SW-MD_LP.MD_LP_01227SW"/>
     <cge:Meas_Ref ObjectId="288153"/>
    <cge:TPSR_Ref TObjectID="45018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-287982">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 753.000000 -630.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45017" ObjectName="SW-MD_LP.MD_LP_00227SW"/>
     <cge:Meas_Ref ObjectId="287982"/>
    <cge:TPSR_Ref TObjectID="45017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-288150">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 701.000000 -483.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45019" ObjectName="SW-MD_LP.MD_LP_04527SW"/>
     <cge:Meas_Ref ObjectId="288150"/>
    <cge:TPSR_Ref TObjectID="45019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-288149">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 700.000000 -344.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45020" ObjectName="SW-MD_LP.MD_LP_04567SW"/>
     <cge:Meas_Ref ObjectId="288149"/>
    <cge:TPSR_Ref TObjectID="45020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-288152">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 945.000000 -481.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45022" ObjectName="SW-MD_LP.MD_LP_04627SW"/>
     <cge:Meas_Ref ObjectId="288152"/>
    <cge:TPSR_Ref TObjectID="45022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-288151">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 932.000000 -342.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45023" ObjectName="SW-MD_LP.MD_LP_04667SW"/>
     <cge:Meas_Ref ObjectId="288151"/>
    <cge:TPSR_Ref TObjectID="45023"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-288148">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1036.000000 -631.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="45021" ObjectName="SW-MD_LP.MD_LP_09027SW"/>
     <cge:Meas_Ref ObjectId="288148"/>
    <cge:TPSR_Ref TObjectID="45021"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 995.000000 -654.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296956">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 337.000000 -483.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46066" ObjectName="SW-MD_LP.MD_LP_04417SW"/>
     <cge:Meas_Ref ObjectId="296956"/>
    <cge:TPSR_Ref TObjectID="46066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296957">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 362.000000 -351.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46068" ObjectName="SW-MD_LP.MD_LP_04467SW"/>
     <cge:Meas_Ref ObjectId="296957"/>
    <cge:TPSR_Ref TObjectID="46068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296915">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 166.000000 -483.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46065" ObjectName="SW-MD_LP.MD_LP_04317SW"/>
     <cge:Meas_Ref ObjectId="296915"/>
    <cge:TPSR_Ref TObjectID="46065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296874">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -4.000000 -483.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46064" ObjectName="SW-MD_LP.MD_LP_04217SW"/>
     <cge:Meas_Ref ObjectId="296874"/>
    <cge:TPSR_Ref TObjectID="46064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296813">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -176.000000 -486.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46063" ObjectName="SW-MD_LP.MD_LP_04117SW"/>
     <cge:Meas_Ref ObjectId="296813"/>
    <cge:TPSR_Ref TObjectID="46063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 192.000000 -348.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46067" ObjectName="SW-MD_LP.MD_LP_04367SW"/>
     <cge:Meas_Ref ObjectId="296916"/>
    <cge:TPSR_Ref TObjectID="46067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296875">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 21.000000 -348.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46070" ObjectName="SW-MD_LP.MD_LP_04267SW"/>
     <cge:Meas_Ref ObjectId="296875"/>
    <cge:TPSR_Ref TObjectID="46070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -151.000000 -348.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46069" ObjectName="SW-MD_LP.MD_LP_04167SW"/>
     <cge:Meas_Ref ObjectId="296834"/>
    <cge:TPSR_Ref TObjectID="46069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233644">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.869565 -0.000000 0.000000 -1.071429 -96.000000 -633.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46060" ObjectName="SW-MD_LP.MD_LP_09017SW"/>
     <cge:Meas_Ref ObjectId="233644"/>
    <cge:TPSR_Ref TObjectID="46060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296753">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 136.000000 -633.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46062" ObjectName="SW-MD_LP.MD_LP_00117SW"/>
     <cge:Meas_Ref ObjectId="296753"/>
    <cge:TPSR_Ref TObjectID="46062"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-MD_LP.MD_LP_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-76,-929 960,-929 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24703" ObjectName="BS-MD_LP.MD_LP_3IM"/>
    <cge:TPSR_Ref TObjectID="24703"/></metadata>
   <polyline fill="none" opacity="0" points="-76,-929 960,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_LP.MD_LP_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-318,-569 386,-569 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24704" ObjectName="BS-MD_LP.MD_LP_9IM"/>
    <cge:TPSR_Ref TObjectID="24704"/></metadata>
   <polyline fill="none" opacity="0" points="-318,-569 386,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_LP.MD_LP_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="542,-569 1294,-569 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24705" ObjectName="BS-MD_LP.MD_LP_9IIM"/>
    <cge:TPSR_Ref TObjectID="24705"/></metadata>
   <polyline fill="none" opacity="0" points="542,-569 1294,-569 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-MD_LP.041Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -217.000000 -292.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34295" ObjectName="EC-MD_LP.041Ld"/>
    <cge:TPSR_Ref TObjectID="34295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_LP.042Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -46.000000 -291.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46123" ObjectName="EC-MD_LP.042Ld"/>
    <cge:TPSR_Ref TObjectID="46123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_LP.043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 124.000000 -292.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38965" ObjectName="EC-MD_LP.043Ld"/>
    <cge:TPSR_Ref TObjectID="38965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_LP.044Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 -290.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34296" ObjectName="EC-MD_LP.044Ld"/>
    <cge:TPSR_Ref TObjectID="34296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_LP.045Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 660.000000 -289.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34297" ObjectName="EC-MD_LP.045Ld"/>
    <cge:TPSR_Ref TObjectID="34297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_LP.046Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.000000 -292.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34298" ObjectName="EC-MD_LP.046Ld"/>
    <cge:TPSR_Ref TObjectID="34298"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2e0bf10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 428.000000 -609.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e1e870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 626.000000 -613.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f7a830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 803.000000 -629.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28436b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.000000 -482.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_312a900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.000000 -343.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fe6770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 995.000000 -480.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e93700" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 982.000000 -341.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2842070" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1086.000000 -630.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31f7b40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 434.000000 -1100.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_334ead0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.000000 -482.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e56d50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 412.000000 -350.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3307cd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 216.000000 -482.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_304c9e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 46.000000 -482.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e1c7a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -126.000000 -485.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2841350" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 242.000000 -347.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc1500" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 71.000000 -347.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_307a450" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -101.000000 -347.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e8f040" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -52.000000 -611.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2161f20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 186.000000 -632.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_33600f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1000 239,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24707@1" ObjectIDZND0="24709@1" Pin0InfoVect0LinkObjId="SW-136709_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136704_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1000 239,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e1ecb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-965 239,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24708@1" ObjectIDZND0="24707@0" Pin0InfoVect0LinkObjId="SW-136704_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136709_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-965 239,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335c360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-998 760,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24710@1" ObjectIDZND0="24712@1" Pin0InfoVect0LinkObjId="SW-136728_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136723_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-998 760,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e273d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-963 760,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24711@1" ObjectIDZND0="24710@0" Pin0InfoVect0LinkObjId="SW-136723_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136728_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-963 760,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e67e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="123,-870 123,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24713@1" ObjectIDZND0="24714@1" Pin0InfoVect0LinkObjId="SW-136749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136747_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="123,-870 123,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2840ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="123,-833 123,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24715@1" ObjectIDZND0="24713@0" Pin0InfoVect0LinkObjId="SW-136747_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136749_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="123,-833 123,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_283c090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="123,-898 123,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24714@0" ObjectIDZND0="24703@0" Pin0InfoVect0LinkObjId="g_2e57df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="123,-898 123,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e1c590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="123,-816 123,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="24715@0" ObjectIDZND0="24746@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="123,-816 123,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e18b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="122,-702 122,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="24746@0" ObjectIDZND0="24716@1" Pin0InfoVect0LinkObjId="SW-136755_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e1c590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="122,-702 122,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e7e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="122,-590 122,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24717@0" ObjectIDZND0="24704@0" Pin0InfoVect0LinkObjId="g_1ecd260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136757_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="122,-590 122,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e57df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-946 760,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24711@0" ObjectIDZND0="24703@0" Pin0InfoVect0LinkObjId="g_283c090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-946 760,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ecd260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="370,-592 370,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24740@0" ObjectIDZND0="24704@0" Pin0InfoVect0LinkObjId="g_27e7e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136966_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="370,-592 370,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3349460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-109,-594 -109,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24744@0" ObjectIDZND0="24704@0" Pin0InfoVect0LinkObjId="g_27e7e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136973_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-109,-594 -109,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31a5fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-208,-542 -208,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24722@1" ObjectIDZND0="24704@0" Pin0InfoVect0LinkObjId="g_27e7e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136858_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-208,-542 -208,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fa84c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-208,-439 -208,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24721@0" ObjectIDZND0="24723@1" Pin0InfoVect0LinkObjId="SW-136859_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136856_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-208,-439 -208,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2173270">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-37,-542 -37,-569 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24725@1" ObjectIDZND0="24704@0" Pin0InfoVect0LinkObjId="g_27e7e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136876_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-37,-542 -37,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32f6130">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-37,-439 -37,-400 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24724@0" ObjectIDZND0="24726@1" Pin0InfoVect0LinkObjId="SW-136877_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136874_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-37,-439 -37,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30afbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="133,-542 133,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24728@1" ObjectIDZND0="24704@0" Pin0InfoVect0LinkObjId="g_27e7e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136896_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="133,-542 133,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a48b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="133,-439 133,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24727@0" ObjectIDZND0="24729@1" Pin0InfoVect0LinkObjId="SW-136897_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136892_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="133,-439 133,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30bdd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-542 304,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24731@1" ObjectIDZND0="24704@0" Pin0InfoVect0LinkObjId="g_27e7e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136912_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-542 304,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3a0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-439 304,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24730@0" ObjectIDZND0="24732@1" Pin0InfoVect0LinkObjId="SW-136913_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-439 304,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ea3410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="669,-541 669,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24734@1" ObjectIDZND0="24705@0" Pin0InfoVect0LinkObjId="g_285d460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136930_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="669,-541 669,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e30a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="669,-438 669,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24733@0" ObjectIDZND0="24735@1" Pin0InfoVect0LinkObjId="SW-136931_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136928_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="669,-438 669,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_285d460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-541 918,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24737@1" ObjectIDZND0="24705@0" Pin0InfoVect0LinkObjId="g_1ea3410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136948_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-541 918,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30919a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-438 918,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24736@0" ObjectIDZND0="24738@1" Pin0InfoVect0LinkObjId="SW-136949_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136946_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-438 918,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3147220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1132,-419 1132,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3148ae0@0" Pin0InfoVect0LinkObjId="g_3148ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1132,-419 1132,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_321ed30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-569 719,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24705@0" ObjectIDZND0="24720@0" Pin0InfoVect0LinkObjId="SW-136812_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ea3410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-569 719,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3313090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-680 719,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24719@1" ObjectIDZND0="24747@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136810_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-680 719,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3313580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-794 719,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="24747@1" ObjectIDZND0="24753@0" Pin0InfoVect0LinkObjId="SW-136806_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3313090_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-794 719,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_301db30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-833 719,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24753@1" ObjectIDZND0="24718@0" Pin0InfoVect0LinkObjId="SW-136802_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136806_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-833 719,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_318d510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-929 719,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24703@0" ObjectIDZND0="24752@0" Pin0InfoVect0LinkObjId="SW-136806_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_283c090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-929 719,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b3d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-878 719,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24752@1" ObjectIDZND0="24718@1" Pin0InfoVect0LinkObjId="SW-136802_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136806_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-878 719,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32f6980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-172,-330 -172,-346 -208,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2e582a0@0" ObjectIDZND0="34295@x" ObjectIDZND1="46069@x" ObjectIDZND2="24723@x" Pin0InfoVect0LinkObjId="EC-MD_LP.041Ld_0" Pin0InfoVect1LinkObjId="SW-296834_0" Pin0InfoVect2LinkObjId="SW-136859_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e582a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-172,-330 -172,-346 -208,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_335bab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-208,-346 -208,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2e582a0@0" ObjectIDND1="46069@x" ObjectIDND2="24723@x" ObjectIDZND0="34295@0" Pin0InfoVect0LinkObjId="EC-MD_LP.041Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e582a0_0" Pin1InfoVect1LinkObjId="SW-296834_0" Pin1InfoVect2LinkObjId="SW-136859_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-208,-346 -208,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e77dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-1,-330 -1,-346 -37,-346 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_285ea60@0" ObjectIDZND0="46123@x" ObjectIDZND1="24726@x" ObjectIDZND2="46070@x" Pin0InfoVect0LinkObjId="EC-MD_LP.042Ld_0" Pin0InfoVect1LinkObjId="SW-136877_0" Pin0InfoVect2LinkObjId="SW-296875_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_285ea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-1,-330 -1,-346 -37,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3353790">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-37,-346 -37,-318 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_285ea60@0" ObjectIDND1="24726@x" ObjectIDND2="46070@x" ObjectIDZND0="46123@0" Pin0InfoVect0LinkObjId="EC-MD_LP.042Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_285ea60_0" Pin1InfoVect1LinkObjId="SW-136877_0" Pin1InfoVect2LinkObjId="SW-296875_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-37,-346 -37,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e75d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="169,-330 169,-346 133,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_283bec0@0" ObjectIDZND0="38965@x" ObjectIDZND1="46067@x" ObjectIDZND2="24729@x" Pin0InfoVect0LinkObjId="EC-MD_LP.043Ld_0" Pin0InfoVect1LinkObjId="SW-296916_0" Pin0InfoVect2LinkObjId="SW-136897_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_283bec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="169,-330 169,-346 133,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31f9190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="133,-346 133,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_283bec0@0" ObjectIDND1="46067@x" ObjectIDND2="24729@x" ObjectIDZND0="38965@0" Pin0InfoVect0LinkObjId="EC-MD_LP.043Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_283bec0_0" Pin1InfoVect1LinkObjId="SW-296916_0" Pin1InfoVect2LinkObjId="SW-136897_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="133,-346 133,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e6d4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="340,-330 340,-348 304,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_281ff30@0" ObjectIDZND0="34296@x" ObjectIDZND1="24732@x" ObjectIDZND2="46068@x" Pin0InfoVect0LinkObjId="EC-MD_LP.044Ld_0" Pin0InfoVect1LinkObjId="SW-136913_0" Pin0InfoVect2LinkObjId="SW-296957_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_281ff30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="340,-330 340,-348 304,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e6eb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-348 304,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_281ff30@0" ObjectIDND1="24732@x" ObjectIDND2="46068@x" ObjectIDZND0="34296@0" Pin0InfoVect0LinkObjId="EC-MD_LP.044Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_281ff30_0" Pin1InfoVect1LinkObjId="SW-136913_0" Pin1InfoVect2LinkObjId="SW-296957_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-348 304,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e71f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="523,-996 523,-985 488,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_333b3f0@0" ObjectIDZND0="24742@x" ObjectIDZND1="g_309a510@0" Pin0InfoVect0LinkObjId="SW-136969_0" Pin0InfoVect1LinkObjId="g_309a510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_333b3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="523,-996 523,-985 488,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304a200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="487,-985 487,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_333b3f0@0" ObjectIDND1="g_309a510@0" ObjectIDZND0="24742@1" Pin0InfoVect0LinkObjId="SW-136969_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_333b3f0_0" Pin1InfoVect1LinkObjId="g_309a510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="487,-985 487,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e30f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1027 239,-1044 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24709@0" ObjectIDZND0="g_3130620@0" Pin0InfoVect0LinkObjId="g_3130620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136709_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1027 239,-1044 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31ebe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="186,-1111 186,-1102 239,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_32b1540@0" ObjectIDZND0="g_3130620@0" ObjectIDZND1="g_31ff210@0" ObjectIDZND2="37787@1" Pin0InfoVect0LinkObjId="g_3130620_0" Pin0InfoVect1LinkObjId="g_31ff210_0" Pin0InfoVect2LinkObjId="g_307c580_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b1540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="186,-1111 186,-1102 239,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3207a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1083 239,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="voltageTransformer" ObjectIDND0="g_3130620@1" ObjectIDZND0="g_31ff210@0" ObjectIDZND1="37787@1" ObjectIDZND2="g_32b1540@0" Pin0InfoVect0LinkObjId="g_31ff210_0" Pin0InfoVect1LinkObjId="g_307c580_1" Pin0InfoVect2LinkObjId="g_32b1540_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3130620_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1083 239,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3078310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="295,-1129 295,-1117 239,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="g_31ff210@0" ObjectIDZND0="g_3130620@0" ObjectIDZND1="g_32b1540@0" ObjectIDZND2="37787@1" Pin0InfoVect0LinkObjId="g_3130620_0" Pin0InfoVect1LinkObjId="g_32b1540_0" Pin0InfoVect2LinkObjId="g_307c580_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31ff210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="295,-1129 295,-1117 239,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eada40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1102 239,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_3130620@0" ObjectIDND1="g_32b1540@0" ObjectIDZND0="g_31ff210@0" ObjectIDZND1="37787@1" Pin0InfoVect0LinkObjId="g_31ff210_0" Pin0InfoVect1LinkObjId="g_307c580_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3130620_0" Pin1InfoVect1LinkObjId="g_32b1540_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1102 239,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307c580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-1117 239,-1153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="g_31ff210@0" ObjectIDND1="g_3130620@0" ObjectIDND2="g_32b1540@0" ObjectIDZND0="37787@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_31ff210_0" Pin1InfoVect1LinkObjId="g_3130620_0" Pin1InfoVect2LinkObjId="g_32b1540_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-1117 239,-1153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eacf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-1025 760,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24712@0" ObjectIDZND0="g_1dfd340@0" Pin0InfoVect0LinkObjId="g_1dfd340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-1025 760,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32a2c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-1112 706,-1104 760,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_3160120@0" ObjectIDZND0="g_1dfd340@0" ObjectIDZND1="g_33488a0@0" ObjectIDZND2="37788@1" Pin0InfoVect0LinkObjId="g_1dfd340_0" Pin0InfoVect1LinkObjId="g_33488a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3160120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="706,-1112 706,-1104 760,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_216ba10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-1104 760,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_3160120@0" ObjectIDND1="g_33488a0@0" ObjectIDND2="37788@1" ObjectIDZND0="g_1dfd340@1" Pin0InfoVect0LinkObjId="g_1dfd340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3160120_0" Pin1InfoVect1LinkObjId="g_33488a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="760,-1104 760,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e9cf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="815,-1130 815,-1121 760,-1121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_33488a0@0" ObjectIDZND0="g_3160120@0" ObjectIDZND1="g_1dfd340@0" ObjectIDZND2="37788@1" Pin0InfoVect0LinkObjId="g_3160120_0" Pin0InfoVect1LinkObjId="g_1dfd340_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33488a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="815,-1130 815,-1121 760,-1121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e712b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-1153 760,-1121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="37788@1" ObjectIDZND0="g_33488a0@0" ObjectIDZND1="g_3160120@0" ObjectIDZND2="g_1dfd340@0" Pin0InfoVect0LinkObjId="g_33488a0_0" Pin0InfoVect1LinkObjId="g_3160120_0" Pin0InfoVect2LinkObjId="g_1dfd340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="760,-1153 760,-1121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2177400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-1121 760,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="g_33488a0@0" ObjectIDND1="37788@1" ObjectIDZND0="g_3160120@0" ObjectIDZND1="g_1dfd340@0" Pin0InfoVect0LinkObjId="g_3160120_0" Pin0InfoVect1LinkObjId="g_1dfd340_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33488a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="760,-1121 760,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2db1430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="239,-948 239,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24708@0" ObjectIDZND0="24703@0" Pin0InfoVect0LinkObjId="g_283c090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136709_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-948 239,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dbad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-917 367,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46173@0" ObjectIDZND0="24703@0" Pin0InfoVect0LinkObjId="g_283c090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297943_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-917 367,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e78110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-900 367,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="46173@1" ObjectIDZND0="46174@1" Pin0InfoVect0LinkObjId="SW-297943_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297943_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-900 367,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e71850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-588 1000,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24745@0" ObjectIDZND0="24705@0" Pin0InfoVect0LinkObjId="g_1ea3410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136975_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-588 1000,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33440b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="487,-959 487,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24742@0" ObjectIDZND0="24703@0" Pin0InfoVect0LinkObjId="g_283c090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136969_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="487,-959 487,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30cb860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="487,-1053 487,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_318ed10@0" ObjectIDZND0="g_309a510@0" Pin0InfoVect0LinkObjId="g_309a510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_318ed10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="487,-1053 487,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30d4080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="487,-1009 487,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_309a510@1" ObjectIDZND0="g_333b3f0@0" ObjectIDZND1="24742@x" Pin0InfoVect0LinkObjId="g_333b3f0_0" Pin0InfoVect1LinkObjId="SW-136969_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_309a510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="487,-1009 487,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32e19e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-844 367,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="46174@0" ObjectIDZND0="g_30d37f0@0" Pin0InfoVect0LinkObjId="g_30d37f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-297943_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="367,-844 367,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2800830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="434,-626 434,-636 420,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e0bf10@0" ObjectIDZND0="46061@1" Pin0InfoVect0LinkObjId="SW-296736_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e0bf10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="434,-626 434,-636 420,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e51650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="389,-636 370,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46061@0" ObjectIDZND0="24740@x" ObjectIDZND1="24739@x" Pin0InfoVect0LinkObjId="SW-136966_0" Pin0InfoVect1LinkObjId="SW-136964_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="389,-636 370,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fe3960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="370,-628 370,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24740@1" ObjectIDZND0="46061@x" ObjectIDZND1="24739@x" Pin0InfoVect0LinkObjId="SW-296736_0" Pin0InfoVect1LinkObjId="SW-136964_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136966_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="370,-628 370,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eeaa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="370,-636 370,-653 456,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46061@x" ObjectIDND1="24740@x" ObjectIDZND0="24739@1" Pin0InfoVect0LinkObjId="SW-136964_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-296736_0" Pin1InfoVect1LinkObjId="SW-136966_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="370,-636 370,-653 456,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f1ea40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-587 571,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24741@0" ObjectIDZND0="24705@0" Pin0InfoVect0LinkObjId="g_1ea3410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136967_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-587 571,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33645a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-630 632,-640 618,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1e1e870@0" ObjectIDZND0="45018@1" Pin0InfoVect0LinkObjId="SW-288153_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e1e870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-630 632,-640 618,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e9c560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="587,-640 570,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="45018@0" ObjectIDZND0="24739@x" ObjectIDZND1="24741@x" Pin0InfoVect0LinkObjId="SW-136964_0" Pin0InfoVect1LinkObjId="SW-136967_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-288153_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="587,-640 570,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e00620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="484,-653 570,-653 570,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24739@0" ObjectIDZND0="45018@x" ObjectIDZND1="24741@x" Pin0InfoVect0LinkObjId="SW-288153_0" Pin0InfoVect1LinkObjId="SW-136967_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136964_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="484,-653 570,-653 570,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30931c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="570,-640 570,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="45018@x" ObjectIDND1="24739@x" ObjectIDZND0="24741@1" Pin0InfoVect0LinkObjId="SW-136967_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-288153_0" Pin1InfoVect1LinkObjId="SW-136964_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="570,-640 570,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f7dd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="794,-635 808,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45017@1" ObjectIDZND0="g_2f7a830@0" Pin0InfoVect0LinkObjId="g_2f7a830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287982_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="794,-635 808,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e66f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="758,-635 719,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="45017@0" ObjectIDZND0="24720@x" ObjectIDZND1="24719@x" Pin0InfoVect0LinkObjId="SW-136812_0" Pin0InfoVect1LinkObjId="SW-136810_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-287982_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="758,-635 719,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30b0010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-626 719,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24720@1" ObjectIDZND0="45017@x" ObjectIDZND1="24719@x" Pin0InfoVect0LinkObjId="SW-287982_0" Pin0InfoVect1LinkObjId="SW-136810_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136812_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-626 719,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ba7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-635 719,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="45017@x" ObjectIDND1="24720@x" ObjectIDZND0="24719@0" Pin0InfoVect0LinkObjId="SW-136810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-287982_0" Pin1InfoVect1LinkObjId="SW-136812_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-635 719,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3048590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="742,-488 756,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45019@1" ObjectIDZND0="g_28436b0@0" Pin0InfoVect0LinkObjId="g_28436b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-288150_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="742,-488 756,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21b0f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-488 669,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="45019@0" ObjectIDZND0="24734@x" ObjectIDZND1="24733@1" Pin0InfoVect0LinkObjId="SW-136930_0" Pin0InfoVect1LinkObjId="SW-136928_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-288150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="706,-488 669,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e5ac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="669,-505 669,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24734@0" ObjectIDZND0="45019@x" ObjectIDZND1="24733@1" Pin0InfoVect0LinkObjId="SW-288150_0" Pin0InfoVect1LinkObjId="SW-136928_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="669,-505 669,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e6b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="669,-488 669,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="45019@x" ObjectIDND1="24734@x" ObjectIDZND0="24733@1" Pin0InfoVect0LinkObjId="SW-136928_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-288150_0" Pin1InfoVect1LinkObjId="SW-136930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="669,-488 669,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e36aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="741,-349 755,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45020@1" ObjectIDZND0="g_312a900@0" Pin0InfoVect0LinkObjId="g_312a900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-288149_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="741,-349 755,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e24440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-319 705,-330 669,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1e7e9b0@0" ObjectIDZND0="34297@x" ObjectIDZND1="45020@x" ObjectIDZND2="24735@x" Pin0InfoVect0LinkObjId="EC-MD_LP.045Ld_0" Pin0InfoVect1LinkObjId="SW-288149_0" Pin0InfoVect2LinkObjId="SW-136931_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e7e9b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="705,-319 705,-330 669,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e6b770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="669,-330 669,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1e7e9b0@0" ObjectIDND1="45020@x" ObjectIDND2="24735@x" ObjectIDZND0="34297@0" Pin0InfoVect0LinkObjId="EC-MD_LP.045Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e7e9b0_0" Pin1InfoVect1LinkObjId="SW-288149_0" Pin1InfoVect2LinkObjId="SW-136931_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="669,-330 669,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2187290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-349 669,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="45020@0" ObjectIDZND0="24735@x" ObjectIDZND1="g_1e7e9b0@0" ObjectIDZND2="34297@x" Pin0InfoVect0LinkObjId="SW-136931_0" Pin0InfoVect1LinkObjId="g_1e7e9b0_0" Pin0InfoVect2LinkObjId="EC-MD_LP.045Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-288149_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="705,-349 669,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33649b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="669,-363 669,-349 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="24735@0" ObjectIDZND0="45020@x" ObjectIDZND1="g_1e7e9b0@0" ObjectIDZND2="34297@x" Pin0InfoVect0LinkObjId="SW-288149_0" Pin0InfoVect1LinkObjId="g_1e7e9b0_0" Pin0InfoVect2LinkObjId="EC-MD_LP.045Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136931_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="669,-363 669,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3016310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="669,-349 669,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="45020@x" ObjectIDND1="24735@x" ObjectIDZND0="g_1e7e9b0@0" ObjectIDZND1="34297@x" Pin0InfoVect0LinkObjId="g_1e7e9b0_0" Pin0InfoVect1LinkObjId="EC-MD_LP.045Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-288149_0" Pin1InfoVect1LinkObjId="SW-136931_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="669,-349 669,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ea8960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="986,-486 1000,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45022@1" ObjectIDZND0="g_2fe6770@0" Pin0InfoVect0LinkObjId="g_2fe6770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-288152_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="986,-486 1000,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dfa240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="950,-486 918,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="45022@0" ObjectIDZND0="24737@x" ObjectIDZND1="24736@x" Pin0InfoVect0LinkObjId="SW-136948_0" Pin0InfoVect1LinkObjId="SW-136946_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-288152_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="950,-486 918,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f58aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-505 918,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24737@0" ObjectIDZND0="45022@x" ObjectIDZND1="24736@x" Pin0InfoVect0LinkObjId="SW-288152_0" Pin0InfoVect1LinkObjId="SW-136946_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136948_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="918,-505 918,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_285d0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-486 918,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="45022@x" ObjectIDND1="24737@x" ObjectIDZND0="24736@1" Pin0InfoVect0LinkObjId="SW-136946_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-288152_0" Pin1InfoVect1LinkObjId="SW-136948_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-486 918,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e30c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="954,-319 954,-329 918,-329 918,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1e8b330@0" ObjectIDZND0="34298@x" ObjectIDZND1="45023@x" ObjectIDZND2="24738@1" Pin0InfoVect0LinkObjId="EC-MD_LP.046Ld_0" Pin0InfoVect1LinkObjId="SW-288151_0" Pin0InfoVect2LinkObjId="SW-136949_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e8b330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="954,-319 954,-329 918,-329 918,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e3df00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-319 918,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34298@0" ObjectIDZND0="g_1e8b330@0" ObjectIDZND1="45023@x" ObjectIDZND2="24738@1" Pin0InfoVect0LinkObjId="g_1e8b330_0" Pin0InfoVect1LinkObjId="SW-288151_0" Pin0InfoVect2LinkObjId="SW-136949_1" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-MD_LP.046Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="918,-319 918,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dfce10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="973,-347 987,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45023@1" ObjectIDZND0="g_1e93700@0" Pin0InfoVect0LinkObjId="g_1e93700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-288151_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="973,-347 987,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e62550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-347 918,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="45023@0" ObjectIDZND0="g_1e8b330@0" ObjectIDZND1="34298@x" ObjectIDZND2="24738@x" Pin0InfoVect0LinkObjId="g_1e8b330_0" Pin0InfoVect1LinkObjId="EC-MD_LP.046Ld_0" Pin0InfoVect2LinkObjId="SW-136949_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-288151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="937,-347 918,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_320e1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-327 918,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1e8b330@0" ObjectIDND1="34298@x" ObjectIDZND0="45023@x" ObjectIDZND1="24738@x" Pin0InfoVect0LinkObjId="SW-288151_0" Pin0InfoVect1LinkObjId="SW-136949_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e8b330_0" Pin1InfoVect1LinkObjId="EC-MD_LP.046Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="918,-327 918,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e310f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-347 918,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="45023@x" ObjectIDND1="g_1e8b330@0" ObjectIDND2="34298@x" ObjectIDZND0="24738@0" Pin0InfoVect0LinkObjId="SW-136949_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-288151_0" Pin1InfoVect1LinkObjId="g_1e8b330_0" Pin1InfoVect2LinkObjId="EC-MD_LP.046Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-347 918,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28004b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1132,-464 1132,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="24705@0" Pin0InfoVect0LinkObjId="g_1ea3410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1132,-464 1132,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3293fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1077,-636 1091,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="45021@1" ObjectIDZND0="g_2842070@0" Pin0InfoVect0LinkObjId="g_2842070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-288148_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1077,-636 1091,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e90eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-636 1000,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="45021@0" ObjectIDZND0="24745@x" ObjectIDZND1="g_3180da0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-136975_0" Pin0InfoVect1LinkObjId="g_3180da0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-288148_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-636 1000,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ebdcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-624 1000,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="24745@1" ObjectIDZND0="45021@x" ObjectIDZND1="g_3180da0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-288148_0" Pin0InfoVect1LinkObjId="g_3180da0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136975_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-624 1000,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e85480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="965,-653 965,-645 1000,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3180da0@0" ObjectIDZND0="45021@x" ObjectIDZND1="24745@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-288148_0" Pin0InfoVect1LinkObjId="SW-136975_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3180da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="965,-653 965,-645 1000,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31f1460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-645 1000,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3180da0@0" ObjectIDND1="0@x" ObjectIDZND0="45021@x" ObjectIDZND1="24745@x" Pin0InfoVect0LinkObjId="SW-288148_0" Pin0InfoVect1LinkObjId="SW-136975_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3180da0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-645 1000,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e97140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="440,-1105 441,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_31f7b40@0" ObjectIDZND0="g_27e95f0@1" Pin0InfoVect0LinkObjId="g_27e95f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31f7b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="440,-1105 441,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2824490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="441,-1076 441,-1067 478,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="441,-1076 441,-1067 478,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_304e070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-735 1000,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_3011780@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3011780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-735 1000,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc45b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-659 1000,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3180da0@0" ObjectIDZND1="45021@x" ObjectIDZND2="24745@x" Pin0InfoVect0LinkObjId="g_3180da0_0" Pin0InfoVect1LinkObjId="SW-288148_0" Pin0InfoVect2LinkObjId="SW-136975_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-659 1000,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33490b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,-488 392,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46066@1" ObjectIDZND0="g_334ead0@0" Pin0InfoVect0LinkObjId="g_334ead0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296956_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,-488 392,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2807c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="342,-488 305,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46066@0" ObjectIDZND0="24731@x" ObjectIDZND1="24730@x" Pin0InfoVect0LinkObjId="SW-136912_0" Pin0InfoVect1LinkObjId="SW-136910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296956_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="342,-488 305,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f7ab00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-506 304,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24731@0" ObjectIDZND0="46066@x" ObjectIDZND1="24730@x" Pin0InfoVect0LinkObjId="SW-296956_0" Pin0InfoVect1LinkObjId="SW-136910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136912_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="304,-506 304,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e60cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-488 304,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46066@x" ObjectIDND1="24731@x" ObjectIDZND0="24730@1" Pin0InfoVect0LinkObjId="SW-136910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-296956_0" Pin1InfoVect1LinkObjId="SW-136912_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-488 304,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3365450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="403,-356 417,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46068@1" ObjectIDZND0="g_2e56d50@0" Pin0InfoVect0LinkObjId="g_2e56d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296957_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="403,-356 417,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32fc800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-356 305,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="46068@0" ObjectIDZND0="24732@x" ObjectIDZND1="g_281ff30@0" ObjectIDZND2="34296@x" Pin0InfoVect0LinkObjId="SW-136913_0" Pin0InfoVect1LinkObjId="g_281ff30_0" Pin0InfoVect2LinkObjId="EC-MD_LP.044Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296957_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="367,-356 305,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32fb4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-364 304,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="24732@0" ObjectIDZND0="g_281ff30@0" ObjectIDZND1="34296@x" ObjectIDZND2="46068@x" Pin0InfoVect0LinkObjId="g_281ff30_0" Pin0InfoVect1LinkObjId="EC-MD_LP.044Ld_0" Pin0InfoVect2LinkObjId="SW-296957_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136913_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="304,-364 304,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32fb6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-356 304,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24732@x" ObjectIDND1="46068@x" ObjectIDZND0="g_281ff30@0" ObjectIDZND1="34296@x" Pin0InfoVect0LinkObjId="g_281ff30_0" Pin0InfoVect1LinkObjId="EC-MD_LP.044Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136913_0" Pin1InfoVect1LinkObjId="SW-296957_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="304,-356 304,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a2420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="207,-488 221,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46065@1" ObjectIDZND0="g_3307cd0@0" Pin0InfoVect0LinkObjId="g_3307cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="207,-488 221,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32fc160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="171,-488 134,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46065@0" ObjectIDZND0="24728@x" ObjectIDZND1="24727@x" Pin0InfoVect0LinkObjId="SW-136896_0" Pin0InfoVect1LinkObjId="SW-136892_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296915_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="171,-488 134,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eedc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="133,-506 133,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24728@0" ObjectIDZND0="46065@x" ObjectIDZND1="24727@x" Pin0InfoVect0LinkObjId="SW-296915_0" Pin0InfoVect1LinkObjId="SW-136892_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136896_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="133,-506 133,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eede60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="133,-488 133,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46065@x" ObjectIDND1="24728@x" ObjectIDZND0="24727@1" Pin0InfoVect0LinkObjId="SW-136892_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-296915_0" Pin1InfoVect1LinkObjId="SW-136896_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="133,-488 133,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e81f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="37,-488 51,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46064@1" ObjectIDZND0="g_304c9e0@0" Pin0InfoVect0LinkObjId="g_304c9e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296874_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="37,-488 51,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_335a640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1,-488 -36,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46064@0" ObjectIDZND0="24725@x" ObjectIDZND1="24724@x" Pin0InfoVect0LinkObjId="SW-136876_0" Pin0InfoVect1LinkObjId="SW-136874_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296874_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1,-488 -36,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e15240">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-37,-506 -37,-488 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24725@0" ObjectIDZND0="46064@x" ObjectIDZND1="24724@x" Pin0InfoVect0LinkObjId="SW-296874_0" Pin0InfoVect1LinkObjId="SW-136874_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136876_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-37,-506 -37,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e15430">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-37,-488 -37,-466 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46064@x" ObjectIDND1="24725@x" ObjectIDZND0="24724@1" Pin0InfoVect0LinkObjId="SW-136874_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-296874_0" Pin1InfoVect1LinkObjId="SW-136876_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-37,-488 -37,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_318df10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-135,-491 -121,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46063@1" ObjectIDZND0="g_1e1c7a0@0" Pin0InfoVect0LinkObjId="g_1e1c7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296813_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-135,-491 -121,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e90b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-171,-491 -208,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="46063@0" ObjectIDZND0="24722@x" ObjectIDZND1="24721@x" Pin0InfoVect0LinkObjId="SW-136858_0" Pin0InfoVect1LinkObjId="SW-136856_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296813_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-171,-491 -208,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_315eab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-208,-506 -208,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24722@0" ObjectIDZND0="46063@x" ObjectIDZND1="24721@x" Pin0InfoVect0LinkObjId="SW-296813_0" Pin0InfoVect1LinkObjId="SW-136856_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136858_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-208,-506 -208,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_315eca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-208,-491 -208,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="46063@x" ObjectIDND1="24722@x" ObjectIDZND0="24721@1" Pin0InfoVect0LinkObjId="SW-136856_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-296813_0" Pin1InfoVect1LinkObjId="SW-136858_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-208,-491 -208,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31d2cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="233,-353 247,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46067@1" ObjectIDZND0="g_2841350@0" Pin0InfoVect0LinkObjId="g_2841350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="233,-353 247,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2839900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="197,-353 135,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="46067@0" ObjectIDZND0="24729@x" ObjectIDZND1="g_283bec0@0" ObjectIDZND2="38965@x" Pin0InfoVect0LinkObjId="SW-136897_0" Pin0InfoVect1LinkObjId="g_283bec0_0" Pin0InfoVect2LinkObjId="EC-MD_LP.043Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="197,-353 135,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f9c870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="133,-364 133,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="24729@0" ObjectIDZND0="46067@x" ObjectIDZND1="g_283bec0@0" ObjectIDZND2="38965@x" Pin0InfoVect0LinkObjId="SW-296916_0" Pin0InfoVect1LinkObjId="g_283bec0_0" Pin0InfoVect2LinkObjId="EC-MD_LP.043Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136897_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="133,-364 133,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f9ca60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="133,-353 133,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="46067@x" ObjectIDND1="24729@x" ObjectIDZND0="g_283bec0@0" ObjectIDZND1="38965@x" Pin0InfoVect0LinkObjId="g_283bec0_0" Pin0InfoVect1LinkObjId="EC-MD_LP.043Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-296916_0" Pin1InfoVect1LinkObjId="SW-136897_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="133,-353 133,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e96740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="62,-353 76,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46070@1" ObjectIDZND0="g_2fc1500@0" Pin0InfoVect0LinkObjId="g_2fc1500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296875_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="62,-353 76,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fd67e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="26,-353 -36,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="46070@0" ObjectIDZND0="24726@x" ObjectIDZND1="g_285ea60@0" ObjectIDZND2="46123@x" Pin0InfoVect0LinkObjId="SW-136877_0" Pin0InfoVect1LinkObjId="g_285ea60_0" Pin0InfoVect2LinkObjId="EC-MD_LP.042Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="26,-353 -36,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329ade0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-37,-364 -37,-356 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="24726@0" ObjectIDZND0="g_285ea60@0" ObjectIDZND1="46123@x" ObjectIDZND2="46070@x" Pin0InfoVect0LinkObjId="g_285ea60_0" Pin0InfoVect1LinkObjId="EC-MD_LP.042Ld_0" Pin0InfoVect2LinkObjId="SW-296875_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136877_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-37,-364 -37,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329afd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-37,-356 -37,-346 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24726@x" ObjectIDND1="46070@x" ObjectIDZND0="g_285ea60@0" ObjectIDZND1="46123@x" Pin0InfoVect0LinkObjId="g_285ea60_0" Pin0InfoVect1LinkObjId="EC-MD_LP.042Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136877_0" Pin1InfoVect1LinkObjId="SW-296875_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-37,-356 -37,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329a690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-110,-353 -96,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46069@1" ObjectIDZND0="g_307a450@0" Pin0InfoVect0LinkObjId="g_307a450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296834_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-110,-353 -96,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_319f6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-146,-353 -208,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="46069@0" ObjectIDZND0="24723@x" ObjectIDZND1="g_2e582a0@0" ObjectIDZND2="34295@x" Pin0InfoVect0LinkObjId="SW-136859_0" Pin0InfoVect1LinkObjId="g_2e582a0_0" Pin0InfoVect2LinkObjId="EC-MD_LP.041Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296834_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-146,-353 -208,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31484e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-208,-364 -208,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="24723@0" ObjectIDZND0="46069@x" ObjectIDZND1="g_2e582a0@0" ObjectIDZND2="34295@x" Pin0InfoVect0LinkObjId="SW-296834_0" Pin0InfoVect1LinkObjId="g_2e582a0_0" Pin0InfoVect2LinkObjId="EC-MD_LP.041Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136859_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-208,-364 -208,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31486d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-208,-353 -208,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="46069@x" ObjectIDND1="24723@x" ObjectIDZND0="g_2e582a0@0" ObjectIDZND1="34295@x" Pin0InfoVect0LinkObjId="g_2e582a0_0" Pin0InfoVect1LinkObjId="EC-MD_LP.041Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-296834_0" Pin1InfoVect1LinkObjId="SW-136859_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-208,-353 -208,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329dbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-63,-661 -63,-651 -109,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1e35310@0" ObjectIDZND0="g_21a5ff0@0" ObjectIDZND1="46060@x" ObjectIDZND2="24744@x" Pin0InfoVect0LinkObjId="g_21a5ff0_0" Pin0InfoVect1LinkObjId="SW-233644_0" Pin0InfoVect2LinkObjId="SW-136973_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e35310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-63,-661 -63,-651 -109,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329c4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-109,-720 -109,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_21a5ff0@0" ObjectIDZND0="g_1e35310@0" ObjectIDZND1="46060@x" ObjectIDZND2="24744@x" Pin0InfoVect0LinkObjId="g_1e35310_0" Pin0InfoVect1LinkObjId="SW-233644_0" Pin0InfoVect2LinkObjId="SW-136973_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21a5ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-109,-720 -109,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e8ede0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-46,-628 -46,-638 -60,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1e8f040@0" ObjectIDZND0="46060@1" Pin0InfoVect0LinkObjId="SW-233644_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e8f040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-46,-628 -46,-638 -60,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3015fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-91,-638 -109,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="46060@0" ObjectIDZND0="g_1e35310@0" ObjectIDZND1="g_21a5ff0@0" ObjectIDZND2="24744@x" Pin0InfoVect0LinkObjId="g_1e35310_0" Pin0InfoVect1LinkObjId="g_21a5ff0_0" Pin0InfoVect2LinkObjId="SW-136973_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233644_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-91,-638 -109,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e24740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-109,-651 -109,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1e35310@0" ObjectIDND1="g_21a5ff0@0" ObjectIDZND0="46060@x" ObjectIDZND1="24744@x" Pin0InfoVect0LinkObjId="SW-233644_0" Pin0InfoVect1LinkObjId="SW-136973_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e35310_0" Pin1InfoVect1LinkObjId="g_21a5ff0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-109,-651 -109,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e24970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-109,-638 -109,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="46060@x" ObjectIDND1="g_1e35310@0" ObjectIDND2="g_21a5ff0@0" ObjectIDZND0="24744@1" Pin0InfoVect0LinkObjId="SW-136973_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-233644_0" Pin1InfoVect1LinkObjId="g_1e35310_0" Pin1InfoVect2LinkObjId="g_21a5ff0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-109,-638 -109,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_335f1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="177,-638 191,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="46062@1" ObjectIDZND0="g_2161f20@0" Pin0InfoVect0LinkObjId="g_2161f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296753_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="177,-638 191,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31d7d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="141,-638 122,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="46062@0" ObjectIDZND0="24716@x" ObjectIDZND1="24717@x" Pin0InfoVect0LinkObjId="SW-136755_0" Pin0InfoVect1LinkObjId="SW-136757_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296753_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="141,-638 122,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f99970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="122,-653 122,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24716@0" ObjectIDZND0="46062@x" ObjectIDZND1="24717@x" Pin0InfoVect0LinkObjId="SW-296753_0" Pin0InfoVect1LinkObjId="SW-136757_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136755_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="122,-653 122,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f99bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="122,-638 122,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="46062@x" ObjectIDND1="24716@x" ObjectIDZND0="24717@1" Pin0InfoVect0LinkObjId="SW-136757_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-296753_0" Pin1InfoVect1LinkObjId="SW-136755_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="122,-638 122,-626 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24704" cx="122" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24704" cx="370" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24704" cx="-109" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24704" cx="-208" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24704" cx="-37" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24704" cx="133" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24704" cx="304" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24705" cx="669" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24705" cx="918" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24705" cx="719" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24703" cx="761" cy="-929" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24703" cx="240" cy="-929" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24703" cx="367" cy="-929" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24705" cx="1000" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24703" cx="487" cy="-929" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24703" cx="123" cy="-929" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24703" cx="719" cy="-929" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24705" cx="571" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24705" cx="1132" cy="-569" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-136548" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -423.000000 -1162.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24684" ObjectName="DYN-MD_LP"/>
     <cge:Meas_Ref ObjectId="136548"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_3354010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -567.000000 -1239.500000) translate(0,16)">龙排变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_30e65c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_30e65c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_30e65c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_30e65c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_30e65c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_30e65c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_30e65c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_30e65c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_30e65c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：5351153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3294610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,395)">               2257</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5f7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -197.000000 -531.000000) translate(0,12)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e734b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -197.000000 -390.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e69570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -24.000000 -531.000000) translate(0,12)">0421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e70f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -24.000000 -383.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e66170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 -535.000000) translate(0,12)">0431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e67830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 -388.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e741b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 -533.000000) translate(0,12)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e744f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 -389.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e60330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 682.000000 -532.000000) translate(0,12)">0452</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e70730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 682.000000 -392.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e6e370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 933.000000 -535.000000) translate(0,12)">0462</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e6f070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 933.000000 -385.000000) translate(0,12)">0466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e74350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -101.000000 -613.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e71290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 136.000000 -607.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e61510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1011.000000 -615.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e73170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 327.000000 -626.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e722d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -605.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e6d9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -177.000000 -789.000000) translate(0,12)">10kV  I母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 949.000000 -783.000000) translate(0,12)">10kV  II母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e72af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 390.000000 -804.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e73310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1115.000000 -269.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e61d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -238.000000 -263.000000) translate(0,12)">10kV米村线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e61370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -59.000000 -263.000000) translate(0,12)">10kV备用线二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5f970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 280.000000 -263.000000) translate(0,12)">10kV柜山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5fb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 647.000000 -263.000000) translate(0,12)">10kV矿山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e6b790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.000000 -263.000000) translate(0,12)">10kV江坡线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df8b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 198.000000 -1214.000000) translate(0,12)">35kV龙排I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e66cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 708.000000 -1214.000000) translate(0,12)">35kV龙排II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e6fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -31.000000 -741.000000) translate(0,12)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e6fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -31.000000 -741.000000) translate(0,27)">35±3*2.5%10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e6fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -31.000000 -741.000000) translate(0,42)">yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e679d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 567.000000 -739.000000) translate(0,12)">SZ11-2000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e679d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 567.000000 -739.000000) translate(0,27)">35±3*2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e679d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 567.000000 -739.000000) translate(0,42)">yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e65130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -104.000000 -922.000000) translate(0,12)">35kV  I母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e681f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -561.000000) translate(0,12)">10kV   I段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e63f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1226.000000 -562.000000) translate(0,12)">10kV  II段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a67c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 248.000000 -994.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_309a8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 769.000000 -992.000000) translate(0,12)">342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329e450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 133.000000 -864.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30cbb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 728.000000 -862.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a5650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 132.000000 -674.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a3a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 -642.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ea3060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 728.000000 -674.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a45b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -460.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e5700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -29.000000 -460.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a7360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 143.000000 -460.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec1a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -460.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f4e140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 678.000000 -459.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d9c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 38.000000 -760.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3313d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 632.000000 -761.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e31c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -459.000000) translate(0,12)">046</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_329f310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -444.000000 -1227.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_318e4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -444.000000 -1262.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e01b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 95.000000 -263.000000) translate(0,12)">10kV高家线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_215eae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -730.000000 -251.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_215eae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -730.000000 -251.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_31d3c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -607.000000 -261.500000) translate(0,17)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_31d3c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -607.000000 -261.500000) translate(0,38)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_31d3c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -607.000000 -261.500000) translate(0,59)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_31eb280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -678.000000 -897.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32f41d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -979.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1e14ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -347.500000 -1180.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_302e610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -615.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27fb230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 755.000000 -661.000000) translate(0,12)">00227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3308700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1038.000000 -662.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_304d0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 715.000000 -514.000000) translate(0,12)">04527</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_304d280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 714.000000 -375.000000) translate(0,12)">04567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb3e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 959.000000 -512.000000) translate(0,12)">04627</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f5da90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 953.000000 -370.000000) translate(0,12)">04667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0bed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 -662.000000) translate(0,12)">01227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2160420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 -514.000000) translate(0,12)">04417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32fca60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 376.000000 -382.000000) translate(0,12)">04467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_333f740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 180.000000 -514.000000) translate(0,12)">04317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_320d790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 10.000000 -514.000000) translate(0,12)">04217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32d7d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -517.000000) translate(0,12)">04117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2839b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 206.000000 -379.000000) translate(0,12)">04367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eeb140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 35.000000 -379.000000) translate(0,12)">04267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_312f390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -379.000000) translate(0,12)">04167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e33db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -93.000000 -633.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_333ed80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 384.000000 -627.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe9ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 158.000000 -661.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0f980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 384.000000 -884.000000) translate(0,12)">3401</text>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="361,-817 367,-824 372,-817 361,-817 " stroke="rgb(60,120,255)" stroke-width="1.22449"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="372,-836 367,-829 361,-836 372,-836 " stroke="rgb(60,120,255)" stroke-width="1.22449"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_21a5ff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -128.175887 -715.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32b1540">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 177.000000 -1178.000000)" xlink:href="#voltageTransformer:shape130"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3160120">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 697.000000 -1179.000000)" xlink:href="#voltageTransformer:shape130"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_318ed10">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 507.500000 -1048.500000)" xlink:href="#voltageTransformer:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3011780">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1009.000000 -766.000000)" xlink:href="#voltageTransformer:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-MD_LP.MD_LP_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34917"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 91.000000 -697.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 91.000000 -697.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24746" ObjectName="TF-MD_LP.MD_LP_1T"/>
    <cge:TPSR_Ref TObjectID="24746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-MD_LP.MD_LP_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34921"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 688.000000 -709.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 688.000000 -709.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24747" ObjectName="TF-MD_LP.MD_LP_2T"/>
    <cge:TPSR_Ref TObjectID="24747"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215174" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -599.000000 -1096.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215174" ObjectName="MD_LP:MD_LP_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215174" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -599.000000 -1056.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215174" ObjectName="MD_LP:MD_LP_sumP"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-606" y="-1252"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-606" y="-1252"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-200" y="-460"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-200" y="-460"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-29" y="-460"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-29" y="-460"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="143" y="-460"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="143" y="-460"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="313" y="-460"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="313" y="-460"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="678" y="-459"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="678" y="-459"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="248" y="-994"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="248" y="-994"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="769" y="-992"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="769" y="-992"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="38" y="-760"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="38" y="-760"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="632" y="-761"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="632" y="-761"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="927" y="-459"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="927" y="-459"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="457" y="-642"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="457" y="-642"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-455" y="-1235"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-455" y="-1235"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-455" y="-1270"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-455" y="-1270"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="95" x="-689" y="-899"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="95" x="-689" y="-899"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-364" y="-1192"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-364" y="-1192"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fcc970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 199.000000 1265.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2160be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.000000 1250.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe4460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 213.000000 1235.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e5d810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 721.000000 1261.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa75c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 710.000000 1246.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e24530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 735.000000 1231.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30d3bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 162.000000 754.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec8440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 162.000000 769.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b7280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.000000 754.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f4cf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.000000 769.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3011b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -90.000000 998.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec39f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -90.000000 1013.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e51320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -90.000000 1029.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31af1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -84.000000 983.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e91270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -85.000000 952.000000) translate(0,12)">F(HZ):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e76190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -98.000000 968.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec2300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -318.000000 634.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e9c9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -318.000000 649.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f941c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -318.000000 665.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e32590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -312.000000 619.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fcdcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -313.000000 588.000000) translate(0,12)">F(HZ):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dfd510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -326.000000 604.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32f63b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.000000 635.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285ff10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.000000 650.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a5990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.000000 666.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e714e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1205.000000 620.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329ed40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1204.000000 589.000000) translate(0,12)">F(HZ):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2184f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 605.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd2380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 203.000000 830.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3347520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 199.000000 848.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e8e430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 185.000000 878.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e017d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.000000 863.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e6edb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 217.000000 653.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2195190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 213.000000 671.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd1490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 199.000000 701.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e27d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.000000 686.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_304de00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.000000 830.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e85d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 786.000000 848.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b76e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 878.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f57d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 761.000000 863.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eae7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 792.000000 672.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc38b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 788.000000 690.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e6b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 774.000000 720.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e18cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 763.000000 705.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.967213 -0.000000 0.000000 -0.925000 80.639344 -19.475000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f44ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -353.000000 185.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f25460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -352.000000 203.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c7d0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -377.000000 218.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e67c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -366.000000 233.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335a940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -354.000000 168.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.967213 -0.000000 0.000000 -0.925000 271.639344 -19.475000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e8330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -353.000000 185.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef12b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -352.000000 203.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e0c950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -377.000000 218.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_218cd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -366.000000 233.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32fc410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -354.000000 168.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.967213 -0.000000 0.000000 -0.925000 444.639344 -19.475000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f9d440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -353.000000 185.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f9d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -352.000000 203.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fba4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -377.000000 218.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eee480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -366.000000 233.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ed2a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -354.000000 168.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.967213 -0.000000 0.000000 -0.925000 616.639344 -19.475000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33432f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -353.000000 185.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21a8f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -352.000000 203.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3387e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -377.000000 218.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fcf680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -366.000000 233.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_304c160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -354.000000 168.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.967213 -0.000000 0.000000 -0.925000 980.639344 -18.475000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335bce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -353.000000 185.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_284bb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -352.000000 203.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e2dd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -377.000000 218.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3309710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -366.000000 233.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f9ccd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -354.000000 168.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.967213 -0.000000 0.000000 -0.925000 1261.639344 -17.475000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28363d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -353.000000 185.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f943a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -352.000000 203.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f77de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -377.000000 218.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f7bab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -366.000000 233.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fcb120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -354.000000 168.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31f7ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -154.000000 263.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="-146" cy="256" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1df9700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 36.000000 263.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="43" cy="256" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31d6290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 180.000000 263.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="187" cy="256" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_333f3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 367.000000 261.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="374" cy="254" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3351350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.000000 259.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="741" cy="252" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_281f550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 989.000000 260.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="996" cy="253" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_MD" endPointId="0" endStationName="MD_LP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_longpai1" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="239,-1195 239,-1151 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37787" ObjectName="AC-35kV.LN_longpai1"/>
    <cge:TPSR_Ref TObjectID="37787_SS-207"/></metadata>
   <polyline fill="none" opacity="0" points="239,-1195 239,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_MD" endPointId="0" endStationName="MD_LP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_longpai2" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="760,-1149 760,-1196 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37788" ObjectName="AC-35kV.LN_longpai2"/>
    <cge:TPSR_Ref TObjectID="37788_SS-207"/></metadata>
   <polyline fill="none" opacity="0" points="760,-1149 760,-1196 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 256.000000 -1263.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24707"/>
     <cge:Term_Ref ObjectID="34837"/>
    <cge:TPSR_Ref TObjectID="24707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 256.000000 -1263.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24707"/>
     <cge:Term_Ref ObjectID="34837"/>
    <cge:TPSR_Ref TObjectID="24707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 256.000000 -1263.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24707"/>
     <cge:Term_Ref ObjectID="34837"/>
    <cge:TPSR_Ref TObjectID="24707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136577" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -1259.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136577" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24710"/>
     <cge:Term_Ref ObjectID="34843"/>
    <cge:TPSR_Ref TObjectID="24710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -1259.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24710"/>
     <cge:Term_Ref ObjectID="34843"/>
    <cge:TPSR_Ref TObjectID="24710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -1259.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24710"/>
     <cge:Term_Ref ObjectID="34843"/>
    <cge:TPSR_Ref TObjectID="24710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 239.000000 -875.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24713"/>
     <cge:Term_Ref ObjectID="34849"/>
    <cge:TPSR_Ref TObjectID="24713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 239.000000 -875.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24713"/>
     <cge:Term_Ref ObjectID="34849"/>
    <cge:TPSR_Ref TObjectID="24713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 239.000000 -875.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136580" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24713"/>
     <cge:Term_Ref ObjectID="34849"/>
    <cge:TPSR_Ref TObjectID="24713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-136585" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 239.000000 -875.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24713"/>
     <cge:Term_Ref ObjectID="34849"/>
    <cge:TPSR_Ref TObjectID="24713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 258.000000 -700.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24716"/>
     <cge:Term_Ref ObjectID="34855"/>
    <cge:TPSR_Ref TObjectID="24716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136590" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 258.000000 -700.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136590" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24716"/>
     <cge:Term_Ref ObjectID="34855"/>
    <cge:TPSR_Ref TObjectID="24716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136586" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 258.000000 -700.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136586" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24716"/>
     <cge:Term_Ref ObjectID="34855"/>
    <cge:TPSR_Ref TObjectID="24716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-136591" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 258.000000 -700.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136591" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24716"/>
     <cge:Term_Ref ObjectID="34855"/>
    <cge:TPSR_Ref TObjectID="24716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 833.000000 -722.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24719"/>
     <cge:Term_Ref ObjectID="34861"/>
    <cge:TPSR_Ref TObjectID="24719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136604" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 833.000000 -722.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24719"/>
     <cge:Term_Ref ObjectID="34861"/>
    <cge:TPSR_Ref TObjectID="24719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136599" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 833.000000 -722.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24719"/>
     <cge:Term_Ref ObjectID="34861"/>
    <cge:TPSR_Ref TObjectID="24719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-136605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 833.000000 -722.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24719"/>
     <cge:Term_Ref ObjectID="34861"/>
    <cge:TPSR_Ref TObjectID="24719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -218.000000 -233.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24721"/>
     <cge:Term_Ref ObjectID="34865"/>
    <cge:TPSR_Ref TObjectID="24721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -218.000000 -233.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24721"/>
     <cge:Term_Ref ObjectID="34865"/>
    <cge:TPSR_Ref TObjectID="24721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136631" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -218.000000 -233.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24721"/>
     <cge:Term_Ref ObjectID="34865"/>
    <cge:TPSR_Ref TObjectID="24721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-136632" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -218.000000 -233.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24721"/>
     <cge:Term_Ref ObjectID="34865"/>
    <cge:TPSR_Ref TObjectID="24721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-136633" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -218.000000 -233.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24721"/>
     <cge:Term_Ref ObjectID="34865"/>
    <cge:TPSR_Ref TObjectID="24721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -234.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24724"/>
     <cge:Term_Ref ObjectID="34871"/>
    <cge:TPSR_Ref TObjectID="24724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -234.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24724"/>
     <cge:Term_Ref ObjectID="34871"/>
    <cge:TPSR_Ref TObjectID="24724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -234.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24724"/>
     <cge:Term_Ref ObjectID="34871"/>
    <cge:TPSR_Ref TObjectID="24724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-136638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -234.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24724"/>
     <cge:Term_Ref ObjectID="34871"/>
    <cge:TPSR_Ref TObjectID="24724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-136639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -32.000000 -234.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24724"/>
     <cge:Term_Ref ObjectID="34871"/>
    <cge:TPSR_Ref TObjectID="24724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136646" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 142.000000 -233.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24727"/>
     <cge:Term_Ref ObjectID="34877"/>
    <cge:TPSR_Ref TObjectID="24727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136647" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 142.000000 -233.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136647" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24727"/>
     <cge:Term_Ref ObjectID="34877"/>
    <cge:TPSR_Ref TObjectID="24727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136643" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 142.000000 -233.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136643" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24727"/>
     <cge:Term_Ref ObjectID="34877"/>
    <cge:TPSR_Ref TObjectID="24727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-136644" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 142.000000 -233.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136644" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24727"/>
     <cge:Term_Ref ObjectID="34877"/>
    <cge:TPSR_Ref TObjectID="24727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-136645" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 142.000000 -233.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24727"/>
     <cge:Term_Ref ObjectID="34877"/>
    <cge:TPSR_Ref TObjectID="24727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136652" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 311.000000 -233.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136652" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24730"/>
     <cge:Term_Ref ObjectID="34883"/>
    <cge:TPSR_Ref TObjectID="24730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136653" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 311.000000 -233.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136653" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24730"/>
     <cge:Term_Ref ObjectID="34883"/>
    <cge:TPSR_Ref TObjectID="24730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136649" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 311.000000 -233.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136649" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24730"/>
     <cge:Term_Ref ObjectID="34883"/>
    <cge:TPSR_Ref TObjectID="24730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-136650" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 311.000000 -233.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136650" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24730"/>
     <cge:Term_Ref ObjectID="34883"/>
    <cge:TPSR_Ref TObjectID="24730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-136651" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 311.000000 -233.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136651" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24730"/>
     <cge:Term_Ref ObjectID="34883"/>
    <cge:TPSR_Ref TObjectID="24730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136658" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 677.000000 -234.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24733"/>
     <cge:Term_Ref ObjectID="34889"/>
    <cge:TPSR_Ref TObjectID="24733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136659" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 677.000000 -234.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24733"/>
     <cge:Term_Ref ObjectID="34889"/>
    <cge:TPSR_Ref TObjectID="24733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136655" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 677.000000 -234.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136655" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24733"/>
     <cge:Term_Ref ObjectID="34889"/>
    <cge:TPSR_Ref TObjectID="24733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-136656" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 677.000000 -234.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136656" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24733"/>
     <cge:Term_Ref ObjectID="34889"/>
    <cge:TPSR_Ref TObjectID="24733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-136657" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 677.000000 -234.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24733"/>
     <cge:Term_Ref ObjectID="34889"/>
    <cge:TPSR_Ref TObjectID="24733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136664" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -231.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24736"/>
     <cge:Term_Ref ObjectID="34895"/>
    <cge:TPSR_Ref TObjectID="24736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136665" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -231.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136665" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24736"/>
     <cge:Term_Ref ObjectID="34895"/>
    <cge:TPSR_Ref TObjectID="24736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136661" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -231.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136661" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24736"/>
     <cge:Term_Ref ObjectID="34895"/>
    <cge:TPSR_Ref TObjectID="24736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-136662" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -231.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136662" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24736"/>
     <cge:Term_Ref ObjectID="34895"/>
    <cge:TPSR_Ref TObjectID="24736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-136663" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -231.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136663" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24736"/>
     <cge:Term_Ref ObjectID="34895"/>
    <cge:TPSR_Ref TObjectID="24736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136669" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 -705.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24739"/>
     <cge:Term_Ref ObjectID="34901"/>
    <cge:TPSR_Ref TObjectID="24739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136670" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 -705.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136670" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24739"/>
     <cge:Term_Ref ObjectID="34901"/>
    <cge:TPSR_Ref TObjectID="24739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136667" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 -705.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136667" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24739"/>
     <cge:Term_Ref ObjectID="34901"/>
    <cge:TPSR_Ref TObjectID="24739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-136607" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -1029.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136607" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24703"/>
     <cge:Term_Ref ObjectID="34832"/>
    <cge:TPSR_Ref TObjectID="24703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-136608" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -1029.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136608" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24703"/>
     <cge:Term_Ref ObjectID="34832"/>
    <cge:TPSR_Ref TObjectID="24703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-136609" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -1029.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24703"/>
     <cge:Term_Ref ObjectID="34832"/>
    <cge:TPSR_Ref TObjectID="24703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-136613" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -1029.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24703"/>
     <cge:Term_Ref ObjectID="34832"/>
    <cge:TPSR_Ref TObjectID="24703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-136610" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -1029.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136610" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24703"/>
     <cge:Term_Ref ObjectID="34832"/>
    <cge:TPSR_Ref TObjectID="24703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-136614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -28.000000 -1029.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24703"/>
     <cge:Term_Ref ObjectID="34832"/>
    <cge:TPSR_Ref TObjectID="24703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-136615" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -256.000000 -663.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24704"/>
     <cge:Term_Ref ObjectID="34833"/>
    <cge:TPSR_Ref TObjectID="24704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-136616" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -256.000000 -663.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136616" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24704"/>
     <cge:Term_Ref ObjectID="34833"/>
    <cge:TPSR_Ref TObjectID="24704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-136617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -256.000000 -663.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24704"/>
     <cge:Term_Ref ObjectID="34833"/>
    <cge:TPSR_Ref TObjectID="24704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-136621" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -256.000000 -663.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136621" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24704"/>
     <cge:Term_Ref ObjectID="34833"/>
    <cge:TPSR_Ref TObjectID="24704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-136618" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -256.000000 -663.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24704"/>
     <cge:Term_Ref ObjectID="34833"/>
    <cge:TPSR_Ref TObjectID="24704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-136622" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -256.000000 -663.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136622" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24704"/>
     <cge:Term_Ref ObjectID="34833"/>
    <cge:TPSR_Ref TObjectID="24704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-136623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1261.000000 -664.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24705"/>
     <cge:Term_Ref ObjectID="34834"/>
    <cge:TPSR_Ref TObjectID="24705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-136624" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1261.000000 -664.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24705"/>
     <cge:Term_Ref ObjectID="34834"/>
    <cge:TPSR_Ref TObjectID="24705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-136625" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1261.000000 -664.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136625" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24705"/>
     <cge:Term_Ref ObjectID="34834"/>
    <cge:TPSR_Ref TObjectID="24705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-136629" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1261.000000 -664.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136629" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24705"/>
     <cge:Term_Ref ObjectID="34834"/>
    <cge:TPSR_Ref TObjectID="24705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-136626" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1261.000000 -664.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136626" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24705"/>
     <cge:Term_Ref ObjectID="34834"/>
    <cge:TPSR_Ref TObjectID="24705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-136630" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1261.000000 -664.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24705"/>
     <cge:Term_Ref ObjectID="34834"/>
    <cge:TPSR_Ref TObjectID="24705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-136596" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 -876.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136596" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24718"/>
     <cge:Term_Ref ObjectID="34859"/>
    <cge:TPSR_Ref TObjectID="24718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-136597" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 -876.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24718"/>
     <cge:Term_Ref ObjectID="34859"/>
    <cge:TPSR_Ref TObjectID="24718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-136592" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 -876.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136592" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24718"/>
     <cge:Term_Ref ObjectID="34859"/>
    <cge:TPSR_Ref TObjectID="24718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-136598" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 -876.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136598" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24718"/>
     <cge:Term_Ref ObjectID="34859"/>
    <cge:TPSR_Ref TObjectID="24718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-287835" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 -771.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="287835" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24747"/>
     <cge:Term_Ref ObjectID="34922"/>
    <cge:TPSR_Ref TObjectID="24747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-137031" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 -771.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="137031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24747"/>
     <cge:Term_Ref ObjectID="34922"/>
    <cge:TPSR_Ref TObjectID="24747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-296709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 230.000000 -767.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="296709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24746"/>
     <cge:Term_Ref ObjectID="34915"/>
    <cge:TPSR_Ref TObjectID="24746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-136606" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 230.000000 -767.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="136606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24746"/>
     <cge:Term_Ref ObjectID="34915"/>
    <cge:TPSR_Ref TObjectID="24746"/></metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-136755">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.156028 -645.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24716" ObjectName="SW-MD_LP.MD_LP_001BK"/>
     <cge:Meas_Ref ObjectId="136755"/>
    <cge:TPSR_Ref TObjectID="24716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136964">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 492.243972 -644.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24739" ObjectName="SW-MD_LP.MD_LP_012BK"/>
     <cge:Meas_Ref ObjectId="136964"/>
    <cge:TPSR_Ref TObjectID="24739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136704">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 230.000000 -965.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24707" ObjectName="SW-MD_LP.MD_LP_341BK"/>
     <cge:Meas_Ref ObjectId="136704"/>
    <cge:TPSR_Ref TObjectID="24707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136723">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.000000 -963.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24710" ObjectName="SW-MD_LP.MD_LP_342BK"/>
     <cge:Meas_Ref ObjectId="136723"/>
    <cge:TPSR_Ref TObjectID="24710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136747">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 114.156028 -835.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24713" ObjectName="SW-MD_LP.MD_LP_301BK"/>
     <cge:Meas_Ref ObjectId="136747"/>
    <cge:TPSR_Ref TObjectID="24713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136810">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 710.000000 -645.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24719" ObjectName="SW-MD_LP.MD_LP_002BK"/>
     <cge:Meas_Ref ObjectId="136810"/>
    <cge:TPSR_Ref TObjectID="24719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136802">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 710.000000 -833.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24718" ObjectName="SW-MD_LP.MD_LP_302BK"/>
     <cge:Meas_Ref ObjectId="136802"/>
    <cge:TPSR_Ref TObjectID="24718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136856">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -218.000000 -431.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24721" ObjectName="SW-MD_LP.MD_LP_041BK"/>
     <cge:Meas_Ref ObjectId="136856"/>
    <cge:TPSR_Ref TObjectID="24721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136874">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -47.333333 -431.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24724" ObjectName="SW-MD_LP.MD_LP_042BK"/>
     <cge:Meas_Ref ObjectId="136874"/>
    <cge:TPSR_Ref TObjectID="24724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136892">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 124.333333 -431.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24727" ObjectName="SW-MD_LP.MD_LP_043BK"/>
     <cge:Meas_Ref ObjectId="136892"/>
    <cge:TPSR_Ref TObjectID="24727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136910">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 295.000000 -431.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24730" ObjectName="SW-MD_LP.MD_LP_044BK"/>
     <cge:Meas_Ref ObjectId="136910"/>
    <cge:TPSR_Ref TObjectID="24730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136928">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 660.000000 -430.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24733" ObjectName="SW-MD_LP.MD_LP_045BK"/>
     <cge:Meas_Ref ObjectId="136928"/>
    <cge:TPSR_Ref TObjectID="24733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-136946">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.000000 -430.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24736" ObjectName="SW-MD_LP.MD_LP_046BK"/>
     <cge:Meas_Ref ObjectId="136946"/>
    <cge:TPSR_Ref TObjectID="24736"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-606" y="-1252"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="35kV龙排变10kV米村线041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-200" y="-460"/></g>
   <g href="35kV龙排变10kV备用二线042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-29" y="-460"/></g>
   <g href="35kV龙排变10kV备用三线043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="143" y="-460"/></g>
   <g href="35kV龙排变10kV柜山线044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="313" y="-460"/></g>
   <g href="35kV龙排变10kV矿山线045间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="678" y="-459"/></g>
   <g href="35kV龙排变35kV龙排Ⅰ回线341间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="248" y="-994"/></g>
   <g href="35kV龙排变35kV龙排Ⅱ回线342间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="769" y="-992"/></g>
   <g href="35kV龙排变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="38" y="-760"/></g>
   <g href="35kV龙排变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="632" y="-761"/></g>
   <g href="35kV龙排变10kV江坡线046间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="927" y="-459"/></g>
   <g href="35kV龙排变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="457" y="-642"/></g>
   <g href="cx_配调_配网接线图35_牟定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-455" y="-1235"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-455" y="-1270"/></g>
   <g href="35kV龙排变GG虚设备间隔间隔接线图（新）_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="95" x="-689" y="-899"/></g>
   <g href="AVC龙排站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-364" y="-1192"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3130620">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.477778 234.000000 -1042.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31ff210">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 302.000000 -1183.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dfd340">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.476190 755.000000 -1040.571429)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33488a0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 822.000000 -1184.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_333b3f0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 530.000000 -1050.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e35310">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 -55.915152 -715.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3180da0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 972.084848 -707.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e582a0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -179.084848 -276.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_285ea60">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -8.418182 -276.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_283bec0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 162.248485 -276.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_281ff30">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 332.915152 -276.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e7e9b0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 697.915152 -265.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e8b330">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 946.915152 -265.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_309a510">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 478.000000 -1004.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30d37f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 354.000000 -714.000000)" xlink:href="#lightningRod:shape189"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3148ae0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1119.000000 -307.000000)" xlink:href="#lightningRod:shape189"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27e95f0">
    <use class="BV-0KV" transform="matrix(0.736842 -0.000000 0.000000 0.682927 426.000000 -1101.000000)" xlink:href="#lightningRod:shape204"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="MD_LP"/>
</svg>