<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-56" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3098 -1385 3481 1386">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape14">
    <polyline arcFlag="1" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 " stroke-width="0.0972"/>
    <rect height="23" stroke-width="0.945274" width="11" x="41" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="47" x2="45" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="49" x2="47" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07143" x1="47" x2="47" y1="54" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="47" x2="47" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="54" x2="40" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="50" x2="44" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.309343" x1="49" x2="45" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="6" x2="6" y1="17" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.337605" x1="7" x2="7" y1="47" y2="36"/>
    <polyline arcFlag="1" points="7,24 7,24 7,24 7,24 8,24 8,24 8,25 8,25 9,25 9,25 9,26 9,26 9,26 9,27 9,27 9,28 9,28 9,28 9,29 8,29 8,29 8,29 8,30 7,30 7,30 7,30 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="6,18 7,18 7,18 7,18 7,18 8,18 8,18 8,19 8,19 8,19 8,20 9,20 9,20 9,21 9,21 9,21 8,22 8,22 8,22 8,23 8,23 8,23 7,23 7,24 7,24 7,24 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="7,30 7,30 7,30 7,30 8,30 8,31 8,31 8,31 9,31 9,32 9,32 9,32 9,33 9,33 9,33 9,34 9,34 9,35 9,35 8,35 8,35 8,36 8,36 7,36 7,36 7,36 " stroke-width="0.0170053"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.266312" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="4" x2="9" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="0" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape89">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="47" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="38" x2="20" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="20" x2="20" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="13" x2="13" y1="48" y2="31"/>
    <circle cx="34" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="24" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="4" x2="13" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape131">
    <ellipse cx="32" cy="19" rx="7.5" ry="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="8" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="32" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="16" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="14" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="14" y2="16"/>
    <ellipse cx="21" cy="15" rx="7.5" ry="6.5" stroke-width="1"/>
    <ellipse cx="43" cy="19" rx="7.5" ry="6.5" stroke-width="1"/>
    <ellipse cx="32" cy="9" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="43" cy="9" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="2" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="3" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="32" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="32" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="43" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="43" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="41" y1="17" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="46" y1="17" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="41" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="32" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="32" y1="18" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_2473440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape16">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,48 5,1 " stroke-width="4.41667"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape29_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape29_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="34"/>
   </symbol>
   <symbol id="switch2:shape29-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape29-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="34"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="11" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="19" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="33" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30ef060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30efcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30f0580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30f1220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30f2480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30f30a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30f3b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30f45c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30f5b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30f5b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30f7540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30f7540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30f8d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30f8d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_30f99f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30fb6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_30fc2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_30fd1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30fdab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30fef50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30ffc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3100510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3100cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3101db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3102730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3103220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3103be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3105060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3105c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3106c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3107870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3116040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3109160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_310a1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_310b1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1396" width="3491" x="3093" y="-1390"/>
  </g><g id="Line_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5008,-871 5025,-871 5025,-893 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.222222" x1="5025" x2="5025" y1="-907" y2="-911"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="5025" x2="5025" y1="-950" y2="-929"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="18" stroke="rgb(255,255,255)" stroke-width="1" width="43" x="4371" y="-174"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-48011">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5002.000000 -1009.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8795" ObjectName="SW-CX_LYS.CX_LYS_1011SW"/>
     <cge:Meas_Ref ObjectId="48011"/>
    <cge:TPSR_Ref TObjectID="8795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48012">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5002.000000 -1143.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8796" ObjectName="SW-CX_LYS.CX_LYS_1016SW"/>
     <cge:Meas_Ref ObjectId="48012"/>
    <cge:TPSR_Ref TObjectID="8796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48015">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4971.000000 -1191.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8799" ObjectName="SW-CX_LYS.CX_LYS_10167SW"/>
     <cge:Meas_Ref ObjectId="48015"/>
    <cge:TPSR_Ref TObjectID="8799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48013">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4972.000000 -1062.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8797" ObjectName="SW-CX_LYS.CX_LYS_10117SW"/>
     <cge:Meas_Ref ObjectId="48013"/>
    <cge:TPSR_Ref TObjectID="8797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48032">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4248.000000 -685.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8803" ObjectName="SW-CX_LYS.CX_LYS_3016SW"/>
     <cge:Meas_Ref ObjectId="48032"/>
    <cge:TPSR_Ref TObjectID="8803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48031">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4248.000000 -565.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8802" ObjectName="SW-CX_LYS.CX_LYS_3011SW"/>
     <cge:Meas_Ref ObjectId="48031"/>
    <cge:TPSR_Ref TObjectID="8802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48033">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4217.000000 -604.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8804" ObjectName="SW-CX_LYS.CX_LYS_30117SW"/>
     <cge:Meas_Ref ObjectId="48033"/>
    <cge:TPSR_Ref TObjectID="8804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48095">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3601.000000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8830" ObjectName="SW-CX_LYS.CX_LYS_38117SW"/>
     <cge:Meas_Ref ObjectId="48095"/>
    <cge:TPSR_Ref TObjectID="8830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48096">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3600.000000 -333.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8831" ObjectName="SW-CX_LYS.CX_LYS_38167SW"/>
     <cge:Meas_Ref ObjectId="48096"/>
    <cge:TPSR_Ref TObjectID="8831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48093">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3631.000000 -487.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8828" ObjectName="SW-CX_LYS.CX_LYS_3811SW"/>
     <cge:Meas_Ref ObjectId="48093"/>
    <cge:TPSR_Ref TObjectID="8828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48094">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3631.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8829" ObjectName="SW-CX_LYS.CX_LYS_3816SW"/>
     <cge:Meas_Ref ObjectId="48094"/>
    <cge:TPSR_Ref TObjectID="8829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48089">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3788.250000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8825" ObjectName="SW-CX_LYS.CX_LYS_38217SW"/>
     <cge:Meas_Ref ObjectId="48089"/>
    <cge:TPSR_Ref TObjectID="8825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48090">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3787.250000 -333.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8826" ObjectName="SW-CX_LYS.CX_LYS_38267SW"/>
     <cge:Meas_Ref ObjectId="48090"/>
    <cge:TPSR_Ref TObjectID="8826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48087">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.250000 -487.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8823" ObjectName="SW-CX_LYS.CX_LYS_3821SW"/>
     <cge:Meas_Ref ObjectId="48087"/>
    <cge:TPSR_Ref TObjectID="8823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48088">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.250000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8824" ObjectName="SW-CX_LYS.CX_LYS_3826SW"/>
     <cge:Meas_Ref ObjectId="48088"/>
    <cge:TPSR_Ref TObjectID="8824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48082">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.500000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8820" ObjectName="SW-CX_LYS.CX_LYS_38317SW"/>
     <cge:Meas_Ref ObjectId="48082"/>
    <cge:TPSR_Ref TObjectID="8820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58241">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3975.500000 -333.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10875" ObjectName="SW-CX_LYS.CX_LYS_38337SW"/>
     <cge:Meas_Ref ObjectId="58241"/>
    <cge:TPSR_Ref TObjectID="10875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48080">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.500000 -487.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8818" ObjectName="SW-CX_LYS.CX_LYS_3831SW"/>
     <cge:Meas_Ref ObjectId="48080"/>
    <cge:TPSR_Ref TObjectID="8818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48081">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.500000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10556" ObjectName="SW-CX_LYS.CX_LYS_3833SW"/>
     <cge:Meas_Ref ObjectId="48081"/>
    <cge:TPSR_Ref TObjectID="10556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48075">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4161.750000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8815" ObjectName="SW-CX_LYS.CX_LYS_38417SW"/>
     <cge:Meas_Ref ObjectId="48075"/>
    <cge:TPSR_Ref TObjectID="8815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48076">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4160.750000 -333.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8816" ObjectName="SW-CX_LYS.CX_LYS_38467SW"/>
     <cge:Meas_Ref ObjectId="48076"/>
    <cge:TPSR_Ref TObjectID="8816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48073">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4191.750000 -487.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8813" ObjectName="SW-CX_LYS.CX_LYS_3841SW"/>
     <cge:Meas_Ref ObjectId="48073"/>
    <cge:TPSR_Ref TObjectID="8813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48074">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4191.750000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8814" ObjectName="SW-CX_LYS.CX_LYS_3846SW"/>
     <cge:Meas_Ref ObjectId="48074"/>
    <cge:TPSR_Ref TObjectID="8814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48069">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4348.000000 -333.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8811" ObjectName="SW-CX_LYS.CX_LYS_38537SW"/>
     <cge:Meas_Ref ObjectId="48069"/>
    <cge:TPSR_Ref TObjectID="8811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 -487.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8808" ObjectName="SW-CX_LYS.CX_LYS_3851SW"/>
     <cge:Meas_Ref ObjectId="48066"/>
    <cge:TPSR_Ref TObjectID="8808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48067">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8809" ObjectName="SW-CX_LYS.CX_LYS_3853SW"/>
     <cge:Meas_Ref ObjectId="48067"/>
    <cge:TPSR_Ref TObjectID="8809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58240">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.250000 -202.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8819" ObjectName="SW-CX_LYS.CX_LYS_3836SW"/>
     <cge:Meas_Ref ObjectId="58240"/>
    <cge:TPSR_Ref TObjectID="8819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48083">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4004.000000 -20.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10874" ObjectName="SW-CX_LYS.CX_LYS_38367SW"/>
     <cge:Meas_Ref ObjectId="48083"/>
    <cge:TPSR_Ref TObjectID="10874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48000">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4633.000000 -1011.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8785" ObjectName="SW-CX_LYS.CX_LYS_1811SW"/>
     <cge:Meas_Ref ObjectId="48000"/>
    <cge:TPSR_Ref TObjectID="8785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48001">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4633.000000 -1147.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8786" ObjectName="SW-CX_LYS.CX_LYS_1816SW"/>
     <cge:Meas_Ref ObjectId="48001"/>
    <cge:TPSR_Ref TObjectID="8786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48004">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4602.000000 -1193.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8789" ObjectName="SW-CX_LYS.CX_LYS_18167SW"/>
     <cge:Meas_Ref ObjectId="48004"/>
    <cge:TPSR_Ref TObjectID="8789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48002">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.000000 -1064.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8787" ObjectName="SW-CX_LYS.CX_LYS_18117SW"/>
     <cge:Meas_Ref ObjectId="48002"/>
    <cge:TPSR_Ref TObjectID="8787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48068">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4349.000000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8810" ObjectName="SW-CX_LYS.CX_LYS_38517SW"/>
     <cge:Meas_Ref ObjectId="48068"/>
    <cge:TPSR_Ref TObjectID="8810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48099">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4378.000000 -196.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8832" ObjectName="SW-CX_LYS.CX_LYS_3856SW"/>
     <cge:Meas_Ref ObjectId="48099"/>
    <cge:TPSR_Ref TObjectID="8832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48009">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -1007.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8793" ObjectName="SW-CX_LYS.CX_LYS_19010SW"/>
     <cge:Meas_Ref ObjectId="48009"/>
    <cge:TPSR_Ref TObjectID="8793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48008">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -1083.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8792" ObjectName="SW-CX_LYS.CX_LYS_19017SW"/>
     <cge:Meas_Ref ObjectId="48008"/>
    <cge:TPSR_Ref TObjectID="8792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48007">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4811.000000 -1034.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8791" ObjectName="SW-CX_LYS.CX_LYS_1901SW"/>
     <cge:Meas_Ref ObjectId="48007"/>
    <cge:TPSR_Ref TObjectID="8791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48014">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4972.000000 -1125.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8798" ObjectName="SW-CX_LYS.CX_LYS_10160SW"/>
     <cge:Meas_Ref ObjectId="48014"/>
    <cge:TPSR_Ref TObjectID="8798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48003">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4604.000000 -1126.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8788" ObjectName="SW-CX_LYS.CX_LYS_18160SW"/>
     <cge:Meas_Ref ObjectId="48003"/>
    <cge:TPSR_Ref TObjectID="8788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48098">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3974.500000 -186.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8821" ObjectName="SW-CX_LYS.CX_LYS_38360SW"/>
     <cge:Meas_Ref ObjectId="48098"/>
    <cge:TPSR_Ref TObjectID="8821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57008">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.500000 -463.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10540" ObjectName="SW-CX_LYS.CX_LYS_38617SW"/>
     <cge:Meas_Ref ObjectId="57008"/>
    <cge:TPSR_Ref TObjectID="10540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57009">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4528.500000 -334.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10539" ObjectName="SW-CX_LYS.CX_LYS_38637SW"/>
     <cge:Meas_Ref ObjectId="57009"/>
    <cge:TPSR_Ref TObjectID="10539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57005">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.500000 -488.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10525" ObjectName="SW-CX_LYS.CX_LYS_3861SW"/>
     <cge:Meas_Ref ObjectId="57005"/>
    <cge:TPSR_Ref TObjectID="10525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57007">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.500000 -353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10536" ObjectName="SW-CX_LYS.CX_LYS_3863SW"/>
     <cge:Meas_Ref ObjectId="57007"/>
    <cge:TPSR_Ref TObjectID="10536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57006">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.250000 -203.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10535" ObjectName="SW-CX_LYS.CX_LYS_3866SW"/>
     <cge:Meas_Ref ObjectId="57006"/>
    <cge:TPSR_Ref TObjectID="10535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57011">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4560.000000 -27.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10537" ObjectName="SW-CX_LYS.CX_LYS_38667SW"/>
     <cge:Meas_Ref ObjectId="57011"/>
    <cge:TPSR_Ref TObjectID="10537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57010">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.500000 -187.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10538" ObjectName="SW-CX_LYS.CX_LYS_38660SW"/>
     <cge:Meas_Ref ObjectId="57010"/>
    <cge:TPSR_Ref TObjectID="10538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48057">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3802.000000 -632.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8806" ObjectName="SW-CX_LYS.CX_LYS_39017SW"/>
     <cge:Meas_Ref ObjectId="48057"/>
    <cge:TPSR_Ref TObjectID="8806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48056">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 -576.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8805" ObjectName="SW-CX_LYS.CX_LYS_3901SW"/>
     <cge:Meas_Ref ObjectId="48056"/>
    <cge:TPSR_Ref TObjectID="8805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56988">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4693.250000 -461.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10534" ObjectName="SW-CX_LYS.CX_LYS_38717SW"/>
     <cge:Meas_Ref ObjectId="56988"/>
    <cge:TPSR_Ref TObjectID="10534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56989">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.250000 -332.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10533" ObjectName="SW-CX_LYS.CX_LYS_38767SW"/>
     <cge:Meas_Ref ObjectId="56989"/>
    <cge:TPSR_Ref TObjectID="10533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56986">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.250000 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10532" ObjectName="SW-CX_LYS.CX_LYS_3871SW"/>
     <cge:Meas_Ref ObjectId="56986"/>
    <cge:TPSR_Ref TObjectID="10532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56987">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.250000 -351.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10531" ObjectName="SW-CX_LYS.CX_LYS_3876SW"/>
     <cge:Meas_Ref ObjectId="56987"/>
    <cge:TPSR_Ref TObjectID="10531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56994">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4852.250000 -461.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10541" ObjectName="SW-CX_LYS.CX_LYS_38817SW"/>
     <cge:Meas_Ref ObjectId="56994"/>
    <cge:TPSR_Ref TObjectID="10541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56995">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4851.250000 -332.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10542" ObjectName="SW-CX_LYS.CX_LYS_38867SW"/>
     <cge:Meas_Ref ObjectId="56995"/>
    <cge:TPSR_Ref TObjectID="10542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56992">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.250000 -486.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10557" ObjectName="SW-CX_LYS.CX_LYS_3881SW"/>
     <cge:Meas_Ref ObjectId="56992"/>
    <cge:TPSR_Ref TObjectID="10557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56993">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.250000 -351.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10543" ObjectName="SW-CX_LYS.CX_LYS_3886SW"/>
     <cge:Meas_Ref ObjectId="56993"/>
    <cge:TPSR_Ref TObjectID="10543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319585">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6407.000000 -338.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49674" ObjectName="SW-CX_LYS.CX_LYS_36537SW"/>
     <cge:Meas_Ref ObjectId="319585"/>
    <cge:TPSR_Ref TObjectID="49674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319582">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6438.000000 -492.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49671" ObjectName="SW-CX_LYS.CX_LYS_3651SW"/>
     <cge:Meas_Ref ObjectId="319582"/>
    <cge:TPSR_Ref TObjectID="49671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319584">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6438.000000 -357.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49673" ObjectName="SW-CX_LYS.CX_LYS_3653SW"/>
     <cge:Meas_Ref ObjectId="319584"/>
    <cge:TPSR_Ref TObjectID="49673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319583">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6408.000000 -467.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49672" ObjectName="SW-CX_LYS.CX_LYS_36517SW"/>
     <cge:Meas_Ref ObjectId="319583"/>
    <cge:TPSR_Ref TObjectID="49672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319586">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6438.000000 -207.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49675" ObjectName="SW-CX_LYS.CX_LYS_3656SW"/>
     <cge:Meas_Ref ObjectId="319586"/>
    <cge:TPSR_Ref TObjectID="49675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48016">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4978.000000 -868.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8800" ObjectName="SW-CX_LYS.CX_LYS_1010SW"/>
     <cge:Meas_Ref ObjectId="48016"/>
    <cge:TPSR_Ref TObjectID="8800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319813">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5530.250000 -469.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49688" ObjectName="SW-CX_LYS.CX_LYS_36117SW"/>
     <cge:Meas_Ref ObjectId="319813"/>
    <cge:TPSR_Ref TObjectID="49688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319815">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5529.250000 -340.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49690" ObjectName="SW-CX_LYS.CX_LYS_36167SW"/>
     <cge:Meas_Ref ObjectId="319815"/>
    <cge:TPSR_Ref TObjectID="49690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319812">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5560.250000 -494.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49686" ObjectName="SW-CX_LYS.CX_LYS_3611SW"/>
     <cge:Meas_Ref ObjectId="319812"/>
    <cge:TPSR_Ref TObjectID="49686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319814">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5560.250000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49689" ObjectName="SW-CX_LYS.CX_LYS_3616SW"/>
     <cge:Meas_Ref ObjectId="319814"/>
    <cge:TPSR_Ref TObjectID="49689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319560">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5738.250000 -468.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49657" ObjectName="SW-CX_LYS.CX_LYS_36217SW"/>
     <cge:Meas_Ref ObjectId="319560"/>
    <cge:TPSR_Ref TObjectID="49657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319562">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5737.250000 -339.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49659" ObjectName="SW-CX_LYS.CX_LYS_36267SW"/>
     <cge:Meas_Ref ObjectId="319562"/>
    <cge:TPSR_Ref TObjectID="49659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319559">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5768.250000 -493.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49656" ObjectName="SW-CX_LYS.CX_LYS_3621SW"/>
     <cge:Meas_Ref ObjectId="319559"/>
    <cge:TPSR_Ref TObjectID="49656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319561">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5768.250000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49658" ObjectName="SW-CX_LYS.CX_LYS_3626SW"/>
     <cge:Meas_Ref ObjectId="319561"/>
    <cge:TPSR_Ref TObjectID="49658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319568">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5937.250000 -463.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49662" ObjectName="SW-CX_LYS.CX_LYS_36317SW"/>
     <cge:Meas_Ref ObjectId="319568"/>
    <cge:TPSR_Ref TObjectID="49662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319570">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5936.250000 -334.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49664" ObjectName="SW-CX_LYS.CX_LYS_36367SW"/>
     <cge:Meas_Ref ObjectId="319570"/>
    <cge:TPSR_Ref TObjectID="49664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319567">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5967.250000 -488.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49661" ObjectName="SW-CX_LYS.CX_LYS_3631SW"/>
     <cge:Meas_Ref ObjectId="319567"/>
    <cge:TPSR_Ref TObjectID="49661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319569">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5967.250000 -353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49663" ObjectName="SW-CX_LYS.CX_LYS_3636SW"/>
     <cge:Meas_Ref ObjectId="319569"/>
    <cge:TPSR_Ref TObjectID="49663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319576">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6187.000000 -475.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49667" ObjectName="SW-CX_LYS.CX_LYS_36417SW"/>
     <cge:Meas_Ref ObjectId="319576"/>
    <cge:TPSR_Ref TObjectID="49667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6186.000000 -346.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49669" ObjectName="SW-CX_LYS.CX_LYS_36467SW"/>
     <cge:Meas_Ref ObjectId="319578"/>
    <cge:TPSR_Ref TObjectID="49669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319575">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6217.000000 -500.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49666" ObjectName="SW-CX_LYS.CX_LYS_3641SW"/>
     <cge:Meas_Ref ObjectId="319575"/>
    <cge:TPSR_Ref TObjectID="49666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6217.000000 -365.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49668" ObjectName="SW-CX_LYS.CX_LYS_3646SW"/>
     <cge:Meas_Ref ObjectId="319577"/>
    <cge:TPSR_Ref TObjectID="49668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57001">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5033.250000 -459.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10545" ObjectName="SW-CX_LYS.CX_LYS_38917SW"/>
     <cge:Meas_Ref ObjectId="57001"/>
    <cge:TPSR_Ref TObjectID="10545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57002">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.250000 -330.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10546" ObjectName="SW-CX_LYS.CX_LYS_38967SW"/>
     <cge:Meas_Ref ObjectId="57002"/>
    <cge:TPSR_Ref TObjectID="10546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56998">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5063.250000 -484.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10547" ObjectName="SW-CX_LYS.CX_LYS_3891SW"/>
     <cge:Meas_Ref ObjectId="56998"/>
    <cge:TPSR_Ref TObjectID="10547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57000">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5063.250000 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10549" ObjectName="SW-CX_LYS.CX_LYS_3896SW"/>
     <cge:Meas_Ref ObjectId="57000"/>
    <cge:TPSR_Ref TObjectID="10549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319370">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5312.250000 -668.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49515" ObjectName="SW-CX_LYS.CX_LYS_33117SW"/>
     <cge:Meas_Ref ObjectId="319370"/>
    <cge:TPSR_Ref TObjectID="49515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319371">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5311.250000 -539.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49516" ObjectName="SW-CX_LYS.CX_LYS_33167SW"/>
     <cge:Meas_Ref ObjectId="319371"/>
    <cge:TPSR_Ref TObjectID="49516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319368">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5342.250000 -693.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49513" ObjectName="SW-CX_LYS.CX_LYS_3311SW"/>
     <cge:Meas_Ref ObjectId="319368"/>
    <cge:TPSR_Ref TObjectID="49513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319369">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5342.250000 -558.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49514" ObjectName="SW-CX_LYS.CX_LYS_3316SW"/>
     <cge:Meas_Ref ObjectId="319369"/>
    <cge:TPSR_Ref TObjectID="49514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319592">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6100.000000 -690.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49679" ObjectName="SW-CX_LYS.CX_LYS_3026SW"/>
     <cge:Meas_Ref ObjectId="319592"/>
    <cge:TPSR_Ref TObjectID="49679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319591">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6100.000000 -566.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49678" ObjectName="SW-CX_LYS.CX_LYS_3021SW"/>
     <cge:Meas_Ref ObjectId="319591"/>
    <cge:TPSR_Ref TObjectID="49678"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319593">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6069.000000 -609.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49680" ObjectName="SW-CX_LYS.CX_LYS_30217SW"/>
     <cge:Meas_Ref ObjectId="319593"/>
    <cge:TPSR_Ref TObjectID="49680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319601">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6334.000000 -628.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49682" ObjectName="SW-CX_LYS.CX_LYS_39027SW"/>
     <cge:Meas_Ref ObjectId="319601"/>
    <cge:TPSR_Ref TObjectID="49682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319600">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6365.000000 -572.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49681" ObjectName="SW-CX_LYS.CX_LYS_3902SW"/>
     <cge:Meas_Ref ObjectId="319600"/>
    <cge:TPSR_Ref TObjectID="49681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319587">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6406.000000 -183.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49676" ObjectName="SW-CX_LYS.CX_LYS_36567SW"/>
     <cge:Meas_Ref ObjectId="319587"/>
    <cge:TPSR_Ref TObjectID="49676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319372">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5398.000000 -391.000000)" xlink:href="#switch2:shape29_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49517" ObjectName="SW-CX_LYS.CX_LYS_3310SW"/>
     <cge:Meas_Ref ObjectId="319372"/>
    <cge:TPSR_Ref TObjectID="49517"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_LYS.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3828.000000 -291.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43302" ObjectName="SM-CX_LYS.P1"/>
    <cge:TPSR_Ref TObjectID="43302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LYS.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4202.000000 -291.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43303" ObjectName="SM-CX_LYS.P2"/>
    <cge:TPSR_Ref TObjectID="43303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LYS.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4733.000000 -290.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43304" ObjectName="SM-CX_LYS.P3"/>
    <cge:TPSR_Ref TObjectID="43304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LYS.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4892.000000 -290.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43305" ObjectName="SM-CX_LYS.P4"/>
    <cge:TPSR_Ref TObjectID="43305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5570.000000 -298.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5778.000000 -297.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5977.000000 -292.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5073.000000 -288.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YM" endPointId="0" endStationName="CX_LYS" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuanlei_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4648,-1335 4648,-1301 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11551" ObjectName="AC-110kV.yuanlei_line"/>
    <cge:TPSR_Ref TObjectID="11551_SS-56"/></metadata>
   <polyline fill="none" opacity="0" points="4648,-1335 4648,-1301 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_LYS.CX_LYS_3856LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.679245 4388.000000 -173.000000)" xlink:href="#load:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22108" ObjectName="EC-CX_LYS.CX_LYS_3856LD"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1f1ab30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6426.000000 -1059.413793)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2237990" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4957.500000 -1227.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22398f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4958.500000 -1098.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_224a9a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4203.500000 -640.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223aef0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3587.500000 -477.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223d4a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3586.500000 -348.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_221b2f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3773.750000 -477.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_221d860" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3772.750000 -348.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2278090" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3962.000000 -477.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_227aeb0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3961.000000 -348.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b6360" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4148.250000 -477.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20b90f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4147.250000 -348.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2217690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -2.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2283c70" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4588.500000 -1229.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2264270" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4589.500000 -1100.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22ab970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4998.000000 -831.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22ac3c0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4335.500000 -477.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2221680" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4766.500000 -1043.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2227ff0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4766.500000 -1120.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f8370" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4958.500000 -1161.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21fc230" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4590.500000 -1162.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2148440" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3960.000000 -201.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21c4820" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4515.000000 -478.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20fabe0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4514.000000 -350.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c1d60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4564.000000 -1.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20caab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3805.000000 -774.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2164b30" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3788.500000 -668.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21655c0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4678.750000 -476.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2168a20" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4677.750000 -347.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2328130" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4837.750000 -476.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_232b450" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4836.750000 -347.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21c9950" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 6393.500000 -353.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d2250" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 6394.500000 -482.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21219c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4474.000000 -204.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2122170" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4334.500000 -348.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_212d6f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5515.750000 -484.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21308b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5514.750000 -355.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_213ee20" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5723.750000 -483.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2142280" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5722.750000 -354.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23fe0f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5922.750000 -478.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2401550" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5921.750000 -349.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2410100" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 6173.500000 -490.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2413170" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 6172.500000 -361.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24243a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5018.750000 -474.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2427800" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5017.750000 -345.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24381a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5297.750000 -683.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_243b600" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5296.750000 -554.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_244f750" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 6055.500000 -645.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24571d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6337.000000 -770.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_245f3f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 6320.500000 -664.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2471fa0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 6392.500000 -198.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_22cefe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6436,-1131 6436,-1085 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" BeginDevType1="earth" BeginDevType2="earth" EndDevType0="earth" ObjectIDND0="g_1f1ab30@0" ObjectIDND1="g_1f1ab30@0" ObjectIDND2="g_1f1ab30@0" ObjectIDZND0="g_1f1ab30@0" Pin0InfoVect0LinkObjId="g_1f1ab30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f1ab30_0" Pin1InfoVect1LinkObjId="g_1f1ab30_0" Pin1InfoVect2LinkObjId="g_1f1ab30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6436,-1131 6436,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22c3090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5017,-1004 5017,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8783@0" ObjectIDZND0="8795@0" Pin0InfoVect0LinkObjId="SW-48011_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5017,-1004 5017,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22c3280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5017,-1067 5017,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="8795@1" ObjectIDZND0="8794@x" ObjectIDZND1="8797@x" Pin0InfoVect0LinkObjId="SW-48010_0" Pin0InfoVect1LinkObjId="SW-48013_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48011_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5017,-1067 5017,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22c4670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5017,-1088 5017,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8795@x" ObjectIDND1="8797@x" ObjectIDZND0="8794@0" Pin0InfoVect0LinkObjId="SW-48010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48011_0" Pin1InfoVect1LinkObjId="SW-48013_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5017,-1088 5017,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2237fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4962,-1217 4952,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8799@0" ObjectIDZND0="g_2237990@0" Pin0InfoVect0LinkObjId="g_2237990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48015_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4962,-1217 4952,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2239f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5017,-1088 4999,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8795@x" ObjectIDND1="8794@x" ObjectIDZND0="8797@1" Pin0InfoVect0LinkObjId="SW-48013_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48011_0" Pin1InfoVect1LinkObjId="SW-48010_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5017,-1088 4999,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_223a110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4963,-1088 4953,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8797@0" ObjectIDZND0="g_22398f0@0" Pin0InfoVect0LinkObjId="g_22398f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48013_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4963,-1088 4953,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224b150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-630 4244,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8801@x" ObjectIDND1="8802@x" ObjectIDZND0="8804@1" Pin0InfoVect0LinkObjId="SW-48033_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48030_0" Pin1InfoVect1LinkObjId="SW-48031_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-630 4244,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224b340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-630 4198,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8804@0" ObjectIDZND0="g_224a9a0@0" Pin0InfoVect0LinkObjId="g_224a9a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48033_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-630 4198,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223a920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4263,-619 4263,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="8802@1" ObjectIDZND0="8801@x" ObjectIDZND1="8804@x" Pin0InfoVect0LinkObjId="SW-48030_0" Pin0InfoVect1LinkObjId="SW-48033_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48031_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4263,-619 4263,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223ab10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4263,-630 4263,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8802@x" ObjectIDND1="8804@x" ObjectIDZND0="8801@0" Pin0InfoVect0LinkObjId="SW-48030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48031_0" Pin1InfoVect1LinkObjId="SW-48033_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4263,-630 4263,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223ad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3646,-571 3646,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10517@0" ObjectIDZND0="8828@1" Pin0InfoVect0LinkObjId="SW-48093_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3646,-571 3646,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223b6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3646,-488 3628,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8828@x" ObjectIDND1="8827@x" ObjectIDZND0="8830@1" Pin0InfoVect0LinkObjId="SW-48095_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48093_0" Pin1InfoVect1LinkObjId="SW-48092_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3646,-488 3628,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223b890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3592,-488 3582,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8830@0" ObjectIDZND0="g_223aef0@0" Pin0InfoVect0LinkObjId="g_223aef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48095_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3592,-488 3582,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223ba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3646,-509 3646,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8828@0" ObjectIDZND0="8830@x" ObjectIDZND1="8827@x" Pin0InfoVect0LinkObjId="SW-48095_0" Pin0InfoVect1LinkObjId="SW-48092_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48093_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3646,-509 3646,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223d2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3646,-488 3646,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8828@x" ObjectIDND1="8830@x" ObjectIDZND0="8827@0" Pin0InfoVect0LinkObjId="SW-48092_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48093_0" Pin1InfoVect1LinkObjId="SW-48095_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3646,-488 3646,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_229df30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-359 3627,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8829@x" ObjectIDND1="g_21b2810@0" ObjectIDZND0="8831@1" Pin0InfoVect0LinkObjId="SW-48096_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48094_0" Pin1InfoVect1LinkObjId="g_21b2810_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3645,-359 3627,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_229e120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3591,-359 3581,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8831@0" ObjectIDZND0="g_223d4a0@0" Pin0InfoVect0LinkObjId="g_223d4a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48096_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3591,-359 3581,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_229e310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3646,-374 3646,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8829@0" ObjectIDZND0="8831@x" ObjectIDZND1="g_21b2810@0" Pin0InfoVect0LinkObjId="SW-48096_0" Pin0InfoVect1LinkObjId="g_21b2810_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48094_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3646,-374 3646,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_221ad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3646,-442 3646,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8827@1" ObjectIDZND0="8829@x" ObjectIDZND1="g_21f7360@0" Pin0InfoVect0LinkObjId="SW-48094_0" Pin0InfoVect1LinkObjId="g_21f7360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48092_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3646,-442 3646,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_221af10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3646,-423 3646,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8827@x" ObjectIDND1="g_21f7360@0" ObjectIDZND0="8829@1" Pin0InfoVect0LinkObjId="SW-48094_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48092_0" Pin1InfoVect1LinkObjId="g_21f7360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3646,-423 3646,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_221b100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3833,-571 3833,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10517@0" ObjectIDZND0="8823@1" Pin0InfoVect0LinkObjId="SW-48087_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3833,-571 3833,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_221ba60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3833,-488 3815,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8823@x" ObjectIDND1="8822@x" ObjectIDZND0="8825@1" Pin0InfoVect0LinkObjId="SW-48089_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48087_0" Pin1InfoVect1LinkObjId="SW-48086_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3833,-488 3815,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_221bc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-488 3769,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8825@0" ObjectIDZND0="g_221b2f0@0" Pin0InfoVect0LinkObjId="g_221b2f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48089_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-488 3769,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_221be40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3833,-509 3833,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8823@0" ObjectIDZND0="8825@x" ObjectIDZND1="8822@x" Pin0InfoVect0LinkObjId="SW-48089_0" Pin0InfoVect1LinkObjId="SW-48086_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48087_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3833,-509 3833,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_221d670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3833,-488 3833,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8823@x" ObjectIDND1="8825@x" ObjectIDZND0="8822@0" Pin0InfoVect0LinkObjId="SW-48086_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48087_0" Pin1InfoVect1LinkObjId="SW-48089_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3833,-488 3833,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22055c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3833,-359 3814,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="generator" EndDevType0="switch" ObjectIDND0="8824@x" ObjectIDND1="43302@x" ObjectIDZND0="8826@1" Pin0InfoVect0LinkObjId="SW-48090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48088_0" Pin1InfoVect1LinkObjId="SM-CX_LYS.P1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3833,-359 3814,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22057b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3778,-359 3768,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8826@0" ObjectIDZND0="g_221d860@0" Pin0InfoVect0LinkObjId="g_221d860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3778,-359 3768,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22059a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3833,-374 3833,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="8824@0" ObjectIDZND0="8826@x" ObjectIDZND1="43302@x" Pin0InfoVect0LinkObjId="SW-48090_0" Pin0InfoVect1LinkObjId="SM-CX_LYS.P1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3833,-374 3833,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2205b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3833,-359 3833,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="8824@x" ObjectIDND1="8826@x" ObjectIDZND0="43302@0" Pin0InfoVect0LinkObjId="SM-CX_LYS.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48088_0" Pin1InfoVect1LinkObjId="SW-48090_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3833,-359 3833,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2277c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3833,-442 3833,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8822@1" ObjectIDZND0="8824@x" ObjectIDZND1="g_21f3ca0@0" Pin0InfoVect0LinkObjId="SW-48088_0" Pin0InfoVect1LinkObjId="g_21f3ca0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48086_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3833,-442 3833,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2277e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-571 4021,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10517@0" ObjectIDZND0="8818@1" Pin0InfoVect0LinkObjId="SW-48080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-571 4021,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22789c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-488 4003,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8818@x" ObjectIDND1="8817@x" ObjectIDZND0="8820@1" Pin0InfoVect0LinkObjId="SW-48082_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48080_0" Pin1InfoVect1LinkObjId="SW-48079_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-488 4003,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2278be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-488 3957,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8820@0" ObjectIDZND0="g_2278090@0" Pin0InfoVect0LinkObjId="g_2278090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48082_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-488 3957,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2278e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-509 4021,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8818@0" ObjectIDZND0="8820@x" ObjectIDZND1="8817@x" Pin0InfoVect0LinkObjId="SW-48082_0" Pin0InfoVect1LinkObjId="SW-48079_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-509 4021,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_227ac90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-488 4021,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8818@x" ObjectIDND1="8820@x" ObjectIDZND0="8817@0" Pin0InfoVect0LinkObjId="SW-48079_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48080_0" Pin1InfoVect1LinkObjId="SW-48082_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-488 4021,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21e89f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-359 3956,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10875@0" ObjectIDZND0="g_227aeb0@0" Pin0InfoVect0LinkObjId="g_227aeb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58241_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-359 3956,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b5d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-442 4021,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8817@1" ObjectIDZND0="10556@x" ObjectIDZND1="g_21f4a50@0" Pin0InfoVect0LinkObjId="SW-48081_0" Pin0InfoVect1LinkObjId="g_21f4a50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-442 4021,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b5f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-423 4021,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8817@x" ObjectIDND1="g_21f4a50@0" ObjectIDZND0="10556@1" Pin0InfoVect0LinkObjId="SW-48081_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48079_0" Pin1InfoVect1LinkObjId="g_21f4a50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-423 4021,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b6140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-571 4207,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10517@0" ObjectIDZND0="8813@1" Pin0InfoVect0LinkObjId="SW-48073_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-571 4207,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b6c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-488 4189,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8813@x" ObjectIDND1="8812@x" ObjectIDZND0="8815@1" Pin0InfoVect0LinkObjId="SW-48075_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48073_0" Pin1InfoVect1LinkObjId="SW-48072_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-488 4189,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b6eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-488 4143,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8815@0" ObjectIDZND0="g_20b6360@0" Pin0InfoVect0LinkObjId="g_20b6360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48075_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-488 4143,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b70d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-509 4207,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8813@0" ObjectIDZND0="8815@x" ObjectIDZND1="8812@x" Pin0InfoVect0LinkObjId="SW-48075_0" Pin0InfoVect1LinkObjId="SW-48072_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48073_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-509 4207,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b8ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-488 4207,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8813@x" ObjectIDND1="8815@x" ObjectIDZND0="8812@0" Pin0InfoVect0LinkObjId="SW-48072_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48073_0" Pin1InfoVect1LinkObjId="SW-48075_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-488 4207,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b9a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4206,-359 4188,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="generator" EndDevType0="switch" ObjectIDND0="8814@x" ObjectIDND1="43303@x" ObjectIDZND0="8816@1" Pin0InfoVect0LinkObjId="SW-48076_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48074_0" Pin1InfoVect1LinkObjId="SM-CX_LYS.P2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4206,-359 4188,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b9c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-359 4142,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8816@0" ObjectIDZND0="g_20b90f0@0" Pin0InfoVect0LinkObjId="g_20b90f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-359 4142,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b9e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-374 4207,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="8814@0" ObjectIDZND0="8816@x" ObjectIDZND1="43303@x" Pin0InfoVect0LinkObjId="SW-48076_0" Pin0InfoVect1LinkObjId="SM-CX_LYS.P2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-374 4207,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ba080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-359 4207,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="8814@x" ObjectIDND1="8816@x" ObjectIDZND0="43303@0" Pin0InfoVect0LinkObjId="SM-CX_LYS.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48074_0" Pin1InfoVect1LinkObjId="SW-48076_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-359 4207,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2251990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-442 4207,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8812@1" ObjectIDZND0="8814@x" ObjectIDZND1="g_21f5800@0" Pin0InfoVect0LinkObjId="SW-48074_0" Pin0InfoVect1LinkObjId="g_21f5800_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48072_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-442 4207,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2251bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-423 4207,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8812@x" ObjectIDND1="g_21f5800@0" ObjectIDZND0="8814@1" Pin0InfoVect0LinkObjId="SW-48074_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48072_0" Pin1InfoVect1LinkObjId="g_21f5800_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-423 4207,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2251e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-571 4394,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10517@0" ObjectIDZND0="8808@1" Pin0InfoVect0LinkObjId="SW-48066_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-571 4394,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22520b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-509 4394,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="8808@0" ObjectIDZND0="8807@x" ObjectIDZND1="8810@x" Pin0InfoVect0LinkObjId="SW-48065_0" Pin0InfoVect1LinkObjId="SW-48068_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48066_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-509 4394,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2252710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-488 4394,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8808@x" ObjectIDND1="8810@x" ObjectIDZND0="8807@0" Pin0InfoVect0LinkObjId="SW-48065_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48066_0" Pin1InfoVect1LinkObjId="SW-48068_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-488 4394,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2252970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-359 4375,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8809@x" ObjectIDND1="g_21b49d0@0" ObjectIDZND0="8811@1" Pin0InfoVect0LinkObjId="SW-48069_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48067_0" Pin1InfoVect1LinkObjId="g_21b49d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-359 4375,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2252bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-359 4329,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8811@0" ObjectIDZND0="g_2122170@0" Pin0InfoVect0LinkObjId="g_2122170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48069_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-359 4329,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2252e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-374 4394,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8809@0" ObjectIDZND0="8811@x" ObjectIDZND1="g_21b49d0@0" Pin0InfoVect0LinkObjId="SW-48069_0" Pin0InfoVect1LinkObjId="g_21b49d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48067_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-374 4394,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2215fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-442 4394,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8807@1" ObjectIDZND0="8809@x" ObjectIDZND1="g_21f65b0@0" Pin0InfoVect0LinkObjId="SW-48067_0" Pin0InfoVect1LinkObjId="g_21f65b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48065_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-442 4394,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22161e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-423 4394,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8807@x" ObjectIDND1="g_21f65b0@0" ObjectIDZND0="8809@1" Pin0InfoVect0LinkObjId="SW-48067_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48065_0" Pin1InfoVect1LinkObjId="g_21f65b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-423 4394,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2217460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4019,-42 4021,-28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10874@0" ObjectIDZND0="g_2217690@0" Pin0InfoVect0LinkObjId="g_2217690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48083_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4019,-42 4021,-28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_221a980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-1004 4648,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8783@0" ObjectIDZND0="8785@0" Pin0InfoVect0LinkObjId="SW-48000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-1004 4648,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_227efb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-1069 4648,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="8785@1" ObjectIDZND0="8784@x" ObjectIDZND1="8787@x" Pin0InfoVect0LinkObjId="SW-47999_0" Pin0InfoVect1LinkObjId="SW-48002_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-1069 4648,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2281160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-1090 4648,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8785@x" ObjectIDND1="8787@x" ObjectIDZND0="8784@0" Pin0InfoVect0LinkObjId="SW-47999_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48000_0" Pin1InfoVect1LinkObjId="SW-48002_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-1090 4648,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2261560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-1219 4583,-1219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8789@0" ObjectIDZND0="g_2283c70@0" Pin0InfoVect0LinkObjId="g_2283c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-1219 4583,-1219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2264d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-1090 4630,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8785@x" ObjectIDND1="8784@x" ObjectIDZND0="8787@1" Pin0InfoVect0LinkObjId="SW-48002_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48000_0" Pin1InfoVect1LinkObjId="SW-47999_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-1090 4630,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2264f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-1090 4584,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8787@0" ObjectIDZND0="g_2264270@0" Pin0InfoVect0LinkObjId="g_2264270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48002_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-1090 4584,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22ab4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5050,-950 5050,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8833@x" ObjectIDND1="8800@x" ObjectIDZND0="g_21ad660@0" Pin0InfoVect0LinkObjId="g_21ad660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22ab710_0" Pin1InfoVect1LinkObjId="SW-48016_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5050,-950 5050,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22ab710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5050,-950 5105,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_21ad660@0" ObjectIDND1="8800@x" ObjectIDZND0="8833@x" Pin0InfoVect0LinkObjId="g_2176420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21ad660_0" Pin1InfoVect1LinkObjId="SW-48016_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5050,-950 5105,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22ace50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4340,-488 4330,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8810@0" ObjectIDZND0="g_22ac3c0@0" Pin0InfoVect0LinkObjId="g_22ac3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48068_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4340,-488 4330,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2159660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-488 4376,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8807@x" ObjectIDND1="8808@x" ObjectIDZND0="8810@1" Pin0InfoVect0LinkObjId="SW-48068_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48065_0" Pin1InfoVect1LinkObjId="SW-48066_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-488 4376,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2220840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6389,-1168 6389,-1176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_221fc90@1" Pin0InfoVect0LinkObjId="g_221fc90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6389,-1168 6389,-1176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2220aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6388,-1221 6388,-1234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_221fc90@0" ObjectIDZND0="g_215da20@1" Pin0InfoVect0LinkObjId="g_215da20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_221fc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6388,-1221 6388,-1234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2220d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6387,-1279 6387,-1336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_215da20@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_215da20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="6387,-1279 6387,-1336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2220f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5017,-1217 4998,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="8796@x" ObjectIDND1="g_2175780@0" ObjectIDND2="8833@x" ObjectIDZND0="8799@1" Pin0InfoVect0LinkObjId="SW-48015_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-48012_0" Pin1InfoVect1LinkObjId="g_2175780_0" Pin1InfoVect2LinkObjId="g_22ab710_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5017,-1217 4998,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2222110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-1033 4807,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8783@0" ObjectIDND1="8791@x" ObjectIDZND0="8793@1" Pin0InfoVect0LinkObjId="SW-48009_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-48007_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-1033 4807,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2222370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4771,-1033 4761,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8793@0" ObjectIDZND0="g_2221680@0" Pin0InfoVect0LinkObjId="g_2221680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48009_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4771,-1033 4761,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2225080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-1004 4826,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="8783@0" ObjectIDZND0="8793@x" ObjectIDZND1="8791@x" Pin0InfoVect0LinkObjId="SW-48009_0" Pin0InfoVect1LinkObjId="SW-48007_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-1004 4826,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22252e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4771,-1110 4761,-1110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8792@0" ObjectIDZND0="g_2227ff0@0" Pin0InfoVect0LinkObjId="g_2227ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4771,-1110 4761,-1110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21a80d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-1109 4807,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8791@x" ObjectIDND1="g_21abb00@0" ObjectIDND2="g_21a87f0@0" ObjectIDZND0="8792@1" Pin0InfoVect0LinkObjId="SW-48008_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-48007_0" Pin1InfoVect1LinkObjId="g_21abb00_0" Pin1InfoVect2LinkObjId="g_21a87f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-1109 4807,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21a8330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-1109 4826,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8792@x" ObjectIDND1="g_21abb00@0" ObjectIDND2="g_21a87f0@0" ObjectIDZND0="8791@1" Pin0InfoVect0LinkObjId="SW-48007_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-48008_0" Pin1InfoVect1LinkObjId="g_21abb00_0" Pin1InfoVect2LinkObjId="g_21a87f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-1109 4826,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21a8590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-1056 4826,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="8791@0" ObjectIDZND0="8793@x" ObjectIDZND1="8783@0" Pin0InfoVect0LinkObjId="SW-48009_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48007_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-1056 4826,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21ab3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-1129 4868,-1129 4868,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_21a87f0@0" ObjectIDND1="8792@x" ObjectIDND2="8791@x" ObjectIDZND0="g_21abb00@0" Pin0InfoVect0LinkObjId="g_21abb00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21a87f0_0" Pin1InfoVect1LinkObjId="SW-48008_0" Pin1InfoVect2LinkObjId="SW-48007_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-1129 4868,-1129 4868,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21ab640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-1150 4826,-1129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_21a87f0@0" ObjectIDZND0="g_21abb00@0" ObjectIDZND1="8792@x" ObjectIDZND2="8791@x" Pin0InfoVect0LinkObjId="g_21abb00_0" Pin0InfoVect1LinkObjId="SW-48008_0" Pin0InfoVect2LinkObjId="SW-48007_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21a87f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-1150 4826,-1129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21ab8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-1129 4826,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_21abb00@0" ObjectIDND1="g_21a87f0@0" ObjectIDZND0="8792@x" ObjectIDZND1="8791@x" Pin0InfoVect0LinkObjId="SW-48008_0" Pin0InfoVect1LinkObjId="SW-48007_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21abb00_0" Pin1InfoVect1LinkObjId="g_21a87f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-1129 4826,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21f3320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3691,-405 3691,-423 3646,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_21f7360@0" ObjectIDZND0="8829@x" ObjectIDZND1="8827@x" Pin0InfoVect0LinkObjId="SW-48094_0" Pin0InfoVect1LinkObjId="SW-48092_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f7360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3691,-405 3691,-423 3646,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21f3580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4066,-407 4066,-423 4021,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_21f4a50@0" ObjectIDZND0="10556@x" ObjectIDZND1="8817@x" Pin0InfoVect0LinkObjId="SW-48081_0" Pin0InfoVect1LinkObjId="SW-48079_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f4a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4066,-407 4066,-423 4021,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21f37e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-405 4252,-423 4207,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_21f5800@0" ObjectIDZND0="8814@x" ObjectIDZND1="8812@x" Pin0InfoVect0LinkObjId="SW-48074_0" Pin0InfoVect1LinkObjId="SW-48072_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f5800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-405 4252,-423 4207,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21f3a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4439,-405 4439,-423 4394,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_21f65b0@0" ObjectIDZND0="8809@x" ObjectIDZND1="8807@x" Pin0InfoVect0LinkObjId="SW-48067_0" Pin0InfoVect1LinkObjId="SW-48065_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21f65b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4439,-405 4439,-423 4394,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21f8110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5017,-1201 5017,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="8796@1" ObjectIDZND0="8799@x" ObjectIDZND1="g_2175780@0" ObjectIDZND2="8833@x" Pin0InfoVect0LinkObjId="SW-48015_0" Pin0InfoVect1LinkObjId="g_2175780_0" Pin0InfoVect2LinkObjId="g_22ab710_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48012_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5017,-1201 5017,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21f8e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4963,-1151 4953,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8798@0" ObjectIDZND0="g_21f8370@0" Pin0InfoVect0LinkObjId="g_21f8370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4963,-1151 4953,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21fbb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5018,-1151 4999,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8794@x" ObjectIDND1="8796@x" ObjectIDZND0="8798@1" Pin0InfoVect0LinkObjId="SW-48014_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48010_0" Pin1InfoVect1LinkObjId="SW-48012_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5018,-1151 4999,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21fbd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5017,-1134 5017,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="8794@1" ObjectIDZND0="8798@x" ObjectIDZND1="8796@x" Pin0InfoVect0LinkObjId="SW-48014_0" Pin0InfoVect1LinkObjId="SW-48012_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48010_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5017,-1134 5017,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21fbfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5017,-1151 5017,-1165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8794@x" ObjectIDND1="8798@x" ObjectIDZND0="8796@0" Pin0InfoVect0LinkObjId="SW-48012_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48010_0" Pin1InfoVect1LinkObjId="SW-48014_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5017,-1151 5017,-1165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21af420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-1152 4585,-1152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8788@0" ObjectIDZND0="g_21fc230@0" Pin0InfoVect0LinkObjId="g_21fc230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48003_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-1152 4585,-1152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21b20f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-1152 4631,-1152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8784@x" ObjectIDND1="8786@x" ObjectIDZND0="8788@1" Pin0InfoVect0LinkObjId="SW-48003_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47999_0" Pin1InfoVect1LinkObjId="SW-48001_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-1152 4631,-1152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21b2350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-1136 4648,-1152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="8784@1" ObjectIDZND0="8788@x" ObjectIDZND1="8786@x" Pin0InfoVect0LinkObjId="SW-48003_0" Pin0InfoVect1LinkObjId="SW-48001_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47999_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-1136 4648,-1152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21b25b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-1152 4648,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8788@x" ObjectIDND1="8784@x" ObjectIDZND0="8786@0" Pin0InfoVect0LinkObjId="SW-48001_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48003_0" Pin1InfoVect1LinkObjId="SW-47999_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-1152 4648,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b3560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-282 3645,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_21b2810@1" Pin0InfoVect0LinkObjId="g_21b2810_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3645,-282 3645,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b37c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-347 3645,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_21b2810@0" ObjectIDZND0="8829@x" ObjectIDZND1="8831@x" Pin0InfoVect0LinkObjId="SW-48094_0" Pin0InfoVect1LinkObjId="SW-48096_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b2810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3645,-347 3645,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b4770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-283 4021,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_21b3a20@1" ObjectIDZND0="8819@1" Pin0InfoVect0LinkObjId="SW-58240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b3a20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-283 4021,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b5720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-254 4393,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="8832@1" ObjectIDZND0="g_21b49d0@1" Pin0InfoVect0LinkObjId="g_21b49d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48099_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-254 4393,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b5980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-326 4393,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_21b49d0@0" ObjectIDZND0="8809@x" ObjectIDZND1="8811@x" Pin0InfoVect0LinkObjId="SW-48067_0" Pin0InfoVect1LinkObjId="SW-48069_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b49d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-326 4393,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21b5be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3608,-255 3608,-269 3645,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2216880@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2216880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3608,-255 3608,-269 3645,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2148c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-212 3955,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8821@0" ObjectIDZND0="g_2148440@0" Pin0InfoVect0LinkObjId="g_2148440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48098_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-212 3955,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214b930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4019,-212 4001,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="8819@x" ObjectIDND1="10559@x" ObjectIDZND0="8821@1" Pin0InfoVect0LinkObjId="SW-48098_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58240_0" Pin1InfoVect1LinkObjId="CB-CX_LYS.CX_LYS_1C_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4019,-212 4001,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214bb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-224 4021,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="8819@0" ObjectIDZND0="8821@x" ObjectIDZND1="10559@x" Pin0InfoVect0LinkObjId="SW-48098_0" Pin0InfoVect1LinkObjId="CB-CX_LYS.CX_LYS_1C_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-224 4021,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_214bdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-212 4021,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="8819@x" ObjectIDND1="8821@x" ObjectIDZND0="10559@0" Pin0InfoVect0LinkObjId="CB-CX_LYS.CX_LYS_1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58240_0" Pin1InfoVect1LinkObjId="SW-48098_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-212 4021,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_220c7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-668 4310,-687 4263,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_21ae410@0" ObjectIDZND0="8803@x" ObjectIDZND1="8801@x" Pin0InfoVect0LinkObjId="SW-48032_0" Pin0InfoVect1LinkObjId="SW-48030_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21ae410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-668 4310,-687 4263,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_220c990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4263,-670 4263,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8801@1" ObjectIDZND0="8803@x" ObjectIDZND1="g_21ae410@0" Pin0InfoVect0LinkObjId="SW-48032_0" Pin0InfoVect1LinkObjId="g_21ae410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4263,-670 4263,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_220cb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4263,-687 4263,-707 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8801@x" ObjectIDND1="g_21ae410@0" ObjectIDZND0="8803@0" Pin0InfoVect0LinkObjId="SW-48032_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48030_0" Pin1InfoVect1LinkObjId="g_21ae410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4263,-687 4263,-707 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21c39b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3833,-410 3833,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="8824@1" ObjectIDZND0="8822@x" ObjectIDZND1="g_21f3ca0@0" Pin0InfoVect0LinkObjId="SW-48086_0" Pin0InfoVect1LinkObjId="g_21f3ca0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3833,-410 3833,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21c3ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3833,-423 3878,-423 3878,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="8824@x" ObjectIDND1="8822@x" ObjectIDZND0="g_21f3ca0@0" Pin0InfoVect0LinkObjId="g_21f3ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48088_0" Pin1InfoVect1LinkObjId="SW-48086_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3833,-423 3878,-423 3878,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21c4630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-571 4574,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10517@0" ObjectIDZND0="10525@1" Pin0InfoVect0LinkObjId="SW-57005_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-571 4574,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21c50a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-489 4556,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="10525@x" ObjectIDND1="10523@x" ObjectIDZND0="10540@1" Pin0InfoVect0LinkObjId="SW-57008_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57005_0" Pin1InfoVect1LinkObjId="SW-57004_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-489 4556,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21c52d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-489 4510,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10540@0" ObjectIDZND0="g_21c4820@0" Pin0InfoVect0LinkObjId="g_21c4820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-489 4510,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21c5500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-510 4574,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10525@0" ObjectIDZND0="10540@x" ObjectIDZND1="10523@x" Pin0InfoVect0LinkObjId="SW-57008_0" Pin0InfoVect1LinkObjId="SW-57004_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57005_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-510 4574,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fa980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-489 4574,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10525@x" ObjectIDND1="10540@x" ObjectIDZND0="10523@0" Pin0InfoVect0LinkObjId="SW-57004_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57005_0" Pin1InfoVect1LinkObjId="SW-57008_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-489 4574,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fb670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4519,-360 4509,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10539@0" ObjectIDZND0="g_20fabe0@0" Pin0InfoVect0LinkObjId="g_20fabe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57009_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4519,-360 4509,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20bc280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-443 4574,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="10523@1" ObjectIDZND0="10536@x" ObjectIDZND1="g_20c3360@0" Pin0InfoVect0LinkObjId="SW-57007_0" Pin0InfoVect1LinkObjId="g_20c3360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-443 4574,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20bc4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-424 4574,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="10523@x" ObjectIDND1="g_20c3360@0" ObjectIDZND0="10536@1" Pin0InfoVect0LinkObjId="SW-57007_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57004_0" Pin1InfoVect1LinkObjId="g_20c3360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-424 4574,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c18a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4575,-97 4575,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="10537@1" Pin0InfoVect0LinkObjId="SW-57011_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4575,-97 4575,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c1b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4575,-49 4574,-27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10537@0" ObjectIDZND0="g_20c1d60@0" Pin0InfoVect0LinkObjId="g_20c1d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57011_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4575,-49 4574,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c3170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4619,-408 4619,-424 4574,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_20c3360@0" ObjectIDZND0="10536@x" ObjectIDZND1="10523@x" Pin0InfoVect0LinkObjId="SW-57007_0" Pin0InfoVect1LinkObjId="SW-57004_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20c3360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4619,-408 4619,-424 4574,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c4c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-284 4574,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_20c3f10@1" ObjectIDZND0="10535@1" Pin0InfoVect0LinkObjId="SW-57006_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20c3f10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-284 4574,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c4ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-213 4499,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10538@0" ObjectIDZND0="g_21219c0@0" Pin0InfoVect0LinkObjId="g_21219c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-213 4499,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c7bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-213 4556,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="10535@x" ObjectIDND1="10558@x" ObjectIDZND0="10538@1" Pin0InfoVect0LinkObjId="SW-57010_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57006_0" Pin1InfoVect1LinkObjId="CB-CX_LYS.CX_LYS_2C_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-213 4556,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c7e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-225 4574,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="10535@0" ObjectIDZND0="10538@x" ObjectIDZND1="10558@x" Pin0InfoVect0LinkObjId="SW-57010_0" Pin0InfoVect1LinkObjId="CB-CX_LYS.CX_LYS_2C_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-225 4574,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c8090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-213 4574,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="10535@x" ObjectIDND1="10538@x" ObjectIDZND0="10558@0" Pin0InfoVect0LinkObjId="CB-CX_LYS.CX_LYS_2C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57006_0" Pin1InfoVect1LinkObjId="SW-57010_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-213 4574,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20c8ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-667 3894,-667 3894,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8806@x" ObjectIDND1="8805@x" ObjectIDND2="g_2163df0@0" ObjectIDZND0="g_2163040@0" Pin0InfoVect0LinkObjId="g_2163040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-48057_0" Pin1InfoVect1LinkObjId="SW-48056_0" Pin1InfoVect2LinkObjId="g_2163df0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-667 3894,-667 3894,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20ca850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-739 3815,-739 3815,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_20caab0@0" Pin0InfoVect0LinkObjId="g_20caab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-739 3815,-739 3815,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cb500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3793,-658 3783,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8806@0" ObjectIDZND0="g_2164b30@0" Pin0InfoVect0LinkObjId="g_2164b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48057_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3793,-658 3783,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_215fe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3847,-658 3829,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2163040@0" ObjectIDND1="g_2163df0@0" ObjectIDND2="8805@x" ObjectIDZND0="8806@1" Pin0InfoVect0LinkObjId="SW-48057_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2163040_0" Pin1InfoVect1LinkObjId="g_2163df0_0" Pin1InfoVect2LinkObjId="SW-48056_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3847,-658 3829,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2160070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-658 3848,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8806@x" ObjectIDND1="8805@x" ObjectIDZND0="g_2163040@0" ObjectIDZND1="g_2163df0@0" Pin0InfoVect0LinkObjId="g_2163040_0" Pin0InfoVect1LinkObjId="g_2163df0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48057_0" Pin1InfoVect1LinkObjId="SW-48056_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-658 3848,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2162b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-571 3848,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10517@0" ObjectIDZND0="8805@0" Pin0InfoVect0LinkObjId="SW-48056_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-571 3848,-598 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2162de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-634 3848,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8805@1" ObjectIDZND0="g_2163040@0" ObjectIDZND1="g_2163df0@0" ObjectIDZND2="8806@x" Pin0InfoVect0LinkObjId="g_2163040_0" Pin0InfoVect1LinkObjId="g_2163df0_0" Pin0InfoVect2LinkObjId="SW-48057_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48056_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-634 3848,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2164670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-667 3848,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8806@x" ObjectIDND1="8805@x" ObjectIDND2="g_2163040@0" ObjectIDZND0="g_2163df0@1" Pin0InfoVect0LinkObjId="g_2163df0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-48057_0" Pin1InfoVect1LinkObjId="SW-48056_0" Pin1InfoVect2LinkObjId="g_2163040_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-667 3848,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21648d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-720 3848,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2163df0@0" ObjectIDZND0="g_20c8cd0@0" Pin0InfoVect0LinkObjId="g_20c8cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2163df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-720 3848,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2166050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-487 4720,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="10532@x" ObjectIDND1="10530@x" ObjectIDZND0="10534@1" Pin0InfoVect0LinkObjId="SW-56988_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56986_0" Pin1InfoVect1LinkObjId="SW-56985_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-487 4720,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21662b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4684,-487 4674,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10534@0" ObjectIDZND0="g_21655c0@0" Pin0InfoVect0LinkObjId="g_21655c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56988_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4684,-487 4674,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2166510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-508 4738,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10532@0" ObjectIDZND0="10534@x" ObjectIDZND1="10530@x" Pin0InfoVect0LinkObjId="SW-56988_0" Pin0InfoVect1LinkObjId="SW-56985_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56986_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-508 4738,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21687c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-487 4738,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10532@x" ObjectIDND1="10534@x" ObjectIDZND0="10530@0" Pin0InfoVect0LinkObjId="SW-56985_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56986_0" Pin1InfoVect1LinkObjId="SW-56988_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-487 4738,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21694b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-358 4719,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="generator" EndDevType0="switch" ObjectIDND0="10531@x" ObjectIDND1="43304@x" ObjectIDZND0="10533@1" Pin0InfoVect0LinkObjId="SW-56989_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56987_0" Pin1InfoVect1LinkObjId="SM-CX_LYS.P3_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-358 4719,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2169710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-358 4673,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10533@0" ObjectIDZND0="g_2168a20@0" Pin0InfoVect0LinkObjId="g_2168a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56989_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-358 4673,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2169970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-373 4738,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="10531@0" ObjectIDZND0="10533@x" ObjectIDZND1="43304@x" Pin0InfoVect0LinkObjId="SW-56989_0" Pin0InfoVect1LinkObjId="SM-CX_LYS.P3_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56987_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-373 4738,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2169bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-358 4738,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="10531@x" ObjectIDND1="10533@x" ObjectIDZND0="43304@0" Pin0InfoVect0LinkObjId="SM-CX_LYS.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56987_0" Pin1InfoVect1LinkObjId="SW-56989_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-358 4738,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2325770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-441 4738,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="10530@1" ObjectIDZND0="10531@x" ObjectIDZND1="g_2326f60@0" Pin0InfoVect0LinkObjId="SW-56987_0" Pin0InfoVect1LinkObjId="g_2326f60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56985_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-441 4738,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2327a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-409 4738,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="10531@1" ObjectIDZND0="10530@x" ObjectIDZND1="g_2326f60@0" Pin0InfoVect0LinkObjId="SW-56985_0" Pin0InfoVect1LinkObjId="g_2326f60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56987_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-409 4738,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2327c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-422 4783,-422 4783,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="10531@x" ObjectIDND1="10530@x" ObjectIDZND0="g_2326f60@0" Pin0InfoVect0LinkObjId="g_2326f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56987_0" Pin1InfoVect1LinkObjId="SW-56985_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-422 4783,-422 4783,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2327ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4738,-571 4738,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10517@0" ObjectIDZND0="10532@1" Pin0InfoVect0LinkObjId="SW-56986_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4738,-571 4738,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2328b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-487 4879,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="10557@x" ObjectIDND1="10544@x" ObjectIDZND0="10541@1" Pin0InfoVect0LinkObjId="SW-56994_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56992_0" Pin1InfoVect1LinkObjId="SW-56991_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-487 4879,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2328d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4843,-487 4833,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10541@0" ObjectIDZND0="g_2328130@0" Pin0InfoVect0LinkObjId="g_2328130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56994_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4843,-487 4833,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2328fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-508 4897,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10557@0" ObjectIDZND0="10541@x" ObjectIDZND1="10544@x" Pin0InfoVect0LinkObjId="SW-56994_0" Pin0InfoVect1LinkObjId="SW-56991_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56992_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-508 4897,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_232b1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-487 4897,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10557@x" ObjectIDND1="10541@x" ObjectIDZND0="10544@0" Pin0InfoVect0LinkObjId="SW-56991_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56992_0" Pin1InfoVect1LinkObjId="SW-56994_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-487 4897,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_232bee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-358 4878,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="generator" EndDevType0="switch" ObjectIDND0="10543@x" ObjectIDND1="43305@x" ObjectIDZND0="10542@1" Pin0InfoVect0LinkObjId="SW-56995_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56993_0" Pin1InfoVect1LinkObjId="SM-CX_LYS.P4_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-358 4878,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_232c140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4842,-358 4832,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10542@0" ObjectIDZND0="g_232b450@0" Pin0InfoVect0LinkObjId="g_232b450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4842,-358 4832,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_232c3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-373 4897,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="10543@0" ObjectIDZND0="10542@x" ObjectIDZND1="43305@x" Pin0InfoVect0LinkObjId="SW-56995_0" Pin0InfoVect1LinkObjId="SM-CX_LYS.P4_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56993_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-373 4897,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_232c600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-358 4897,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="10543@x" ObjectIDND1="10542@x" ObjectIDZND0="43305@0" Pin0InfoVect0LinkObjId="SM-CX_LYS.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56993_0" Pin1InfoVect1LinkObjId="SW-56995_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-358 4897,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2336f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-441 4897,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="10544@1" ObjectIDZND0="10543@x" ObjectIDZND1="g_2337da0@0" Pin0InfoVect0LinkObjId="SW-56993_0" Pin0InfoVect1LinkObjId="g_2337da0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56991_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-441 4897,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23388c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-409 4897,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="10543@1" ObjectIDZND0="10544@x" ObjectIDZND1="g_2337da0@0" Pin0InfoVect0LinkObjId="SW-56991_0" Pin0InfoVect1LinkObjId="g_2337da0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56993_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-409 4897,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2338b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-422 4942,-422 4942,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="10543@x" ObjectIDND1="10544@x" ObjectIDZND0="g_2337da0@0" Pin0InfoVect0LinkObjId="g_2337da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56993_0" Pin1InfoVect1LinkObjId="SW-56991_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-422 4942,-422 4942,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2338d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-571 4897,-544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10517@0" ObjectIDZND0="10557@1" Pin0InfoVect0LinkObjId="SW-56992_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-571 4897,-544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23394e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6453,-514 6453,-493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49671@0" ObjectIDZND0="49670@x" ObjectIDZND1="49672@x" Pin0InfoVect0LinkObjId="SW-319581_0" Pin0InfoVect1LinkObjId="SW-319583_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319582_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6453,-514 6453,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21c96f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6453,-493 6453,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49671@x" ObjectIDND1="49672@x" ObjectIDZND0="49670@0" Pin0InfoVect0LinkObjId="SW-319581_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319582_0" Pin1InfoVect1LinkObjId="SW-319583_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6453,-493 6453,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ca3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6398,-364 6388,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49674@0" ObjectIDZND0="g_21c9950@0" Pin0InfoVect0LinkObjId="g_21c9950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319585_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6398,-364 6388,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21d2ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6399,-493 6389,-493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49672@0" ObjectIDZND0="g_21d2250@0" Pin0InfoVect0LinkObjId="g_21d2250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319583_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6399,-493 6389,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21d59f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6453,-493 6435,-493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="49670@x" ObjectIDND1="49671@x" ObjectIDZND0="49672@1" Pin0InfoVect0LinkObjId="SW-319583_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319581_0" Pin1InfoVect1LinkObjId="SW-319582_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6453,-493 6435,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21da000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6453,-260 6453,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="49675@1" ObjectIDZND0="g_21d92b0@1" Pin0InfoVect0LinkObjId="g_21d92b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319586_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6453,-260 6453,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21da260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-360 4574,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="10539@1" ObjectIDZND0="g_20c3f10@0" ObjectIDZND1="10536@x" Pin0InfoVect0LinkObjId="g_20c3f10_0" Pin0InfoVect1LinkObjId="SW-57007_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57009_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-360 4574,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21da4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-337 4574,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_20c3f10@0" ObjectIDZND0="10539@x" ObjectIDZND1="10536@x" Pin0InfoVect0LinkObjId="SW-57009_0" Pin0InfoVect1LinkObjId="SW-57007_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20c3f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-337 4574,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21da720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4574,-360 4574,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_20c3f10@0" ObjectIDND1="10539@x" ObjectIDZND0="10536@0" Pin0InfoVect0LinkObjId="SW-57007_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_20c3f10_0" Pin1InfoVect1LinkObjId="SW-57009_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4574,-360 4574,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21da980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-359 4021,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="10875@1" ObjectIDZND0="g_21b3a20@0" ObjectIDZND1="10556@x" Pin0InfoVect0LinkObjId="g_21b3a20_0" Pin0InfoVect1LinkObjId="SW-48081_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-359 4021,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21dabe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-336 4021,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_21b3a20@0" ObjectIDZND0="10875@x" ObjectIDZND1="10556@x" Pin0InfoVect0LinkObjId="SW-58241_0" Pin0InfoVect1LinkObjId="SW-48081_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21b3a20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-336 4021,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21dae40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-359 4021,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_21b3a20@0" ObjectIDND1="10875@x" ObjectIDZND0="10556@0" Pin0InfoVect0LinkObjId="SW-48081_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21b3a20_0" Pin1InfoVect1LinkObjId="SW-58241_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-359 4021,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21df3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6389,-1055 6389,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" BeginDevType1="earth" BeginDevType2="earth" ObjectIDND0="g_1f1ab30@0" ObjectIDND1="g_1f1ab30@0" ObjectIDND2="g_1f1ab30@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f1ab30_0" Pin1InfoVect1LinkObjId="g_1f1ab30_0" Pin1InfoVect2LinkObjId="g_1f1ab30_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="6389,-1055 6389,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21df5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6389,-1055 6436,-1131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" BeginDevType1="earth" BeginDevType2="earth" EndDevType0="earth" EndDevType1="earth" ObjectIDND0="g_1f1ab30@0" ObjectIDND1="g_1f1ab30@0" ObjectIDND2="g_1f1ab30@0" ObjectIDZND0="g_1f1ab30@0" ObjectIDZND1="g_1f1ab30@0" Pin0InfoVect0LinkObjId="g_1f1ab30_0" Pin0InfoVect1LinkObjId="g_1f1ab30_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f1ab30_0" Pin1InfoVect1LinkObjId="g_1f1ab30_0" Pin1InfoVect2LinkObjId="g_1f1ab30_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6389,-1055 6436,-1131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2176420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5037,-1230 5017,-1230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2175780@0" ObjectIDZND0="8833@x" ObjectIDZND1="8796@x" ObjectIDZND2="8799@x" Pin0InfoVect0LinkObjId="g_22ab710_0" Pin0InfoVect1LinkObjId="SW-48012_0" Pin0InfoVect2LinkObjId="SW-48015_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2175780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5037,-1230 5017,-1230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2176680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5106,-968 5106,-1253 5017,-1253 5017,-1230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="8833@1" ObjectIDZND0="g_2175780@0" ObjectIDZND1="8796@x" ObjectIDZND2="8799@x" Pin0InfoVect0LinkObjId="g_2175780_0" Pin0InfoVect1LinkObjId="SW-48012_0" Pin0InfoVect2LinkObjId="SW-48015_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22ab710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5106,-968 5106,-1253 5017,-1253 5017,-1230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21768f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5017,-1230 5017,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2175780@0" ObjectIDND1="8833@x" ObjectIDZND0="8796@x" ObjectIDZND1="8799@x" Pin0InfoVect0LinkObjId="SW-48012_0" Pin0InfoVect1LinkObjId="SW-48015_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2175780_0" Pin1InfoVect1LinkObjId="g_22ab710_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5017,-1230 5017,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_217f560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4609,-1276 4648,-1276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2217be0@1" ObjectIDZND0="g_21ac8b0@0" ObjectIDZND1="8789@x" ObjectIDZND2="8786@x" Pin0InfoVect0LinkObjId="g_21ac8b0_0" Pin0InfoVect1LinkObjId="SW-48004_0" Pin0InfoVect2LinkObjId="SW-48001_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2217be0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4609,-1276 4648,-1276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_217f750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-1276 4648,-1303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2217be0@0" ObjectIDND1="g_21ac8b0@0" ObjectIDND2="8789@x" ObjectIDZND0="11551@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2217be0_0" Pin1InfoVect1LinkObjId="g_21ac8b0_0" Pin1InfoVect2LinkObjId="SW-48004_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-1276 4648,-1303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_217f940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4629,-1219 4648,-1219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="8789@1" ObjectIDZND0="8786@x" ObjectIDZND1="g_21ac8b0@0" ObjectIDZND2="g_2217be0@0" Pin0InfoVect0LinkObjId="SW-48001_0" Pin0InfoVect1LinkObjId="g_21ac8b0_0" Pin0InfoVect2LinkObjId="g_2217be0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4629,-1219 4648,-1219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_217fb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-1205 4648,-1219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="8786@1" ObjectIDZND0="8789@x" ObjectIDZND1="g_21ac8b0@0" ObjectIDZND2="g_2217be0@0" Pin0InfoVect0LinkObjId="SW-48004_0" Pin0InfoVect1LinkObjId="g_21ac8b0_0" Pin0InfoVect2LinkObjId="g_2217be0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48001_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-1205 4648,-1219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_217fd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4690,-1238 4690,-1258 4648,-1258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_21ac8b0@0" ObjectIDZND0="8789@x" ObjectIDZND1="8786@x" ObjectIDZND2="g_2217be0@0" Pin0InfoVect0LinkObjId="SW-48004_0" Pin0InfoVect1LinkObjId="SW-48001_0" Pin0InfoVect2LinkObjId="g_2217be0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21ac8b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4690,-1238 4690,-1258 4648,-1258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_217ff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-1219 4648,-1258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="8789@x" ObjectIDND1="8786@x" ObjectIDZND0="g_21ac8b0@0" ObjectIDZND1="g_2217be0@0" ObjectIDZND2="11551@1" Pin0InfoVect0LinkObjId="g_21ac8b0_0" Pin0InfoVect1LinkObjId="g_2217be0_0" Pin0InfoVect2LinkObjId="g_217f750_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-48004_0" Pin1InfoVect1LinkObjId="SW-48001_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-1219 4648,-1258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_21801c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-1258 4648,-1276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_21ac8b0@0" ObjectIDND1="8789@x" ObjectIDND2="8786@x" ObjectIDZND0="g_2217be0@0" ObjectIDZND1="11551@1" Pin0InfoVect0LinkObjId="g_2217be0_0" Pin0InfoVect1LinkObjId="g_217f750_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21ac8b0_0" Pin1InfoVect1LinkObjId="SW-48004_0" Pin1InfoVect2LinkObjId="SW-48001_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-1258 4648,-1276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_210fcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6389,-1055 6389,-1129 6387,-1131 6436,-1131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" BeginDevType1="earth" BeginDevType2="earth" EndDevType0="earth" EndDevType1="earth" ObjectIDND0="g_1f1ab30@0" ObjectIDND1="g_1f1ab30@0" ObjectIDND2="g_1f1ab30@0" ObjectIDZND0="g_1f1ab30@0" ObjectIDZND1="g_1f1ab30@0" Pin0InfoVect0LinkObjId="g_1f1ab30_0" Pin0InfoVect1LinkObjId="g_1f1ab30_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f1ab30_0" Pin1InfoVect1LinkObjId="g_1f1ab30_0" Pin1InfoVect2LinkObjId="g_1f1ab30_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6389,-1055 6389,-1129 6387,-1131 6436,-1131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2116400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4019,-78 4019,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="10874@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48083_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4019,-78 4019,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_211be00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5008,-857 5008,-871 4992,-871 4992,-888 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22ab970@0" ObjectIDZND0="8800@0" Pin0InfoVect0LinkObjId="SW-48016_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22ab970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5008,-857 5008,-871 4992,-871 4992,-888 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_211c070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5050,-950 4993,-950 4993,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_21ad660@0" ObjectIDND1="8833@x" ObjectIDZND0="8800@1" Pin0InfoVect0LinkObjId="SW-48016_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21ad660_0" Pin1InfoVect1LinkObjId="g_22ab710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5050,-950 4993,-950 4993,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21217d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-218 4393,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="8832@0" ObjectIDZND0="22108@0" Pin0InfoVect0LinkObjId="EC-CX_LYS.CX_LYS_3856LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-48099_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-218 4393,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_212c4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4263,-571 4263,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10517@0" ObjectIDZND0="8802@0" Pin0InfoVect0LinkObjId="SW-48031_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4263,-571 4263,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_212df40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5575,-495 5557,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49686@x" ObjectIDND1="49685@x" ObjectIDZND0="49688@1" Pin0InfoVect0LinkObjId="SW-319813_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319812_0" Pin1InfoVect1LinkObjId="SW-319811_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5575,-495 5557,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_212e1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5521,-495 5511,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49688@0" ObjectIDZND0="g_212d6f0@0" Pin0InfoVect0LinkObjId="g_212d6f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319813_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5521,-495 5511,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_212e400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5575,-516 5575,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49686@0" ObjectIDZND0="49688@x" ObjectIDZND1="49685@x" Pin0InfoVect0LinkObjId="SW-319813_0" Pin0InfoVect1LinkObjId="SW-319811_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319812_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5575,-516 5575,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2130650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5575,-495 5575,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49688@x" ObjectIDND1="49686@x" ObjectIDZND0="49685@0" Pin0InfoVect0LinkObjId="SW-319811_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319813_0" Pin1InfoVect1LinkObjId="SW-319812_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5575,-495 5575,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2131340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5575,-366 5556,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="generator" EndDevType0="switch" ObjectIDND0="49689@x" ObjectIDND1="0@x" ObjectIDZND0="49690@1" Pin0InfoVect0LinkObjId="SW-319815_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319814_0" Pin1InfoVect1LinkObjId="SM-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5575,-366 5556,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21315a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5520,-366 5510,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49690@0" ObjectIDZND0="g_21308b0@0" Pin0InfoVect0LinkObjId="g_21308b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319815_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5520,-366 5510,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2131800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5575,-381 5575,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="49689@0" ObjectIDZND0="49690@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-319815_0" Pin0InfoVect1LinkObjId="SM-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319814_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5575,-381 5575,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2131a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5575,-366 5575,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="49690@x" ObjectIDND1="49689@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319815_0" Pin1InfoVect1LinkObjId="SW-319814_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5575,-366 5575,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213c380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5575,-449 5575,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49685@1" ObjectIDZND0="49689@x" ObjectIDZND1="g_213c5e0@0" Pin0InfoVect0LinkObjId="SW-319814_0" Pin0InfoVect1LinkObjId="g_213c5e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319811_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5575,-449 5575,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213d390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5575,-417 5575,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="49689@1" ObjectIDZND0="49685@x" ObjectIDZND1="g_213c5e0@0" Pin0InfoVect0LinkObjId="SW-319811_0" Pin0InfoVect1LinkObjId="g_213c5e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319814_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5575,-417 5575,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213d5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5575,-430 5620,-430 5620,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="49689@x" ObjectIDND1="49685@x" ObjectIDZND0="g_213c5e0@0" Pin0InfoVect0LinkObjId="g_213c5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319814_0" Pin1InfoVect1LinkObjId="SW-319811_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5575,-430 5620,-430 5620,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213f8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5783,-494 5765,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49656@x" ObjectIDND1="49655@x" ObjectIDZND0="49657@1" Pin0InfoVect0LinkObjId="SW-319560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319559_0" Pin1InfoVect1LinkObjId="SW-319558_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5783,-494 5765,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213fb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5729,-494 5719,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49657@0" ObjectIDZND0="g_213ee20@0" Pin0InfoVect0LinkObjId="g_213ee20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5729,-494 5719,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_213fd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5783,-515 5783,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49656@0" ObjectIDZND0="49657@x" ObjectIDZND1="49655@x" Pin0InfoVect0LinkObjId="SW-319560_0" Pin0InfoVect1LinkObjId="SW-319558_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319559_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5783,-515 5783,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2142020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5783,-494 5783,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49657@x" ObjectIDND1="49656@x" ObjectIDZND0="49655@0" Pin0InfoVect0LinkObjId="SW-319558_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319560_0" Pin1InfoVect1LinkObjId="SW-319559_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5783,-494 5783,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23f0610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5783,-365 5764,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="generator" EndDevType0="switch" ObjectIDND0="49658@x" ObjectIDND1="0@x" ObjectIDZND0="49659@1" Pin0InfoVect0LinkObjId="SW-319562_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319561_0" Pin1InfoVect1LinkObjId="SM-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5783,-365 5764,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23f0870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5728,-365 5718,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49659@0" ObjectIDZND0="g_2142280@0" Pin0InfoVect0LinkObjId="g_2142280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5728,-365 5718,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23f0ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5783,-380 5783,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="49658@0" ObjectIDZND0="49659@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-319562_0" Pin0InfoVect1LinkObjId="SM-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319561_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5783,-380 5783,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23f0d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5783,-365 5783,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="49659@x" ObjectIDND1="49658@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319562_0" Pin1InfoVect1LinkObjId="SW-319561_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5783,-365 5783,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23fb650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5783,-448 5783,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49655@1" ObjectIDZND0="49658@x" ObjectIDZND1="g_23fb8b0@0" Pin0InfoVect0LinkObjId="SW-319561_0" Pin0InfoVect1LinkObjId="g_23fb8b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319558_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5783,-448 5783,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23fc660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5783,-416 5783,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="49658@1" ObjectIDZND0="49655@x" ObjectIDZND1="g_23fb8b0@0" Pin0InfoVect0LinkObjId="SW-319558_0" Pin0InfoVect1LinkObjId="g_23fb8b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319561_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5783,-416 5783,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23fc8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5783,-429 5828,-429 5828,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="49658@x" ObjectIDND1="49655@x" ObjectIDZND0="g_23fb8b0@0" Pin0InfoVect0LinkObjId="g_23fb8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319561_0" Pin1InfoVect1LinkObjId="SW-319558_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5783,-429 5828,-429 5828,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23feb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5982,-489 5964,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49661@x" ObjectIDND1="49660@x" ObjectIDZND0="49662@1" Pin0InfoVect0LinkObjId="SW-319568_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319567_0" Pin1InfoVect1LinkObjId="SW-319566_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5982,-489 5964,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23fede0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5928,-489 5918,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49662@0" ObjectIDZND0="g_23fe0f0@0" Pin0InfoVect0LinkObjId="g_23fe0f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5928,-489 5918,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23ff040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5982,-510 5982,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49661@0" ObjectIDZND0="49662@x" ObjectIDZND1="49660@x" Pin0InfoVect0LinkObjId="SW-319568_0" Pin0InfoVect1LinkObjId="SW-319566_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319567_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5982,-510 5982,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24012f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5982,-489 5982,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49662@x" ObjectIDND1="49661@x" ObjectIDZND0="49660@0" Pin0InfoVect0LinkObjId="SW-319566_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319568_0" Pin1InfoVect1LinkObjId="SW-319567_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5982,-489 5982,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2401fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5982,-360 5963,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="generator" EndDevType0="switch" ObjectIDND0="49663@x" ObjectIDND1="0@x" ObjectIDZND0="49664@1" Pin0InfoVect0LinkObjId="SW-319570_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319569_0" Pin1InfoVect1LinkObjId="SM-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5982,-360 5963,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2402240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5927,-360 5917,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49664@0" ObjectIDZND0="g_2401550@0" Pin0InfoVect0LinkObjId="g_2401550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5927,-360 5917,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24024a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5982,-375 5982,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="49663@0" ObjectIDZND0="49664@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-319570_0" Pin0InfoVect1LinkObjId="SM-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319569_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5982,-375 5982,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2402700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5982,-360 5982,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="49664@x" ObjectIDND1="49663@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319570_0" Pin1InfoVect1LinkObjId="SW-319569_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5982,-360 5982,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_240d020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5982,-443 5982,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49660@1" ObjectIDZND0="49663@x" ObjectIDZND1="g_240d280@0" Pin0InfoVect0LinkObjId="SW-319569_0" Pin0InfoVect1LinkObjId="g_240d280_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319566_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5982,-443 5982,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_240e030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5982,-411 5982,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="49663@1" ObjectIDZND0="49660@x" ObjectIDZND1="g_240d280@0" Pin0InfoVect0LinkObjId="SW-319566_0" Pin0InfoVect1LinkObjId="g_240d280_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319569_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5982,-411 5982,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_240e290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5982,-424 6027,-424 6027,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="49663@x" ObjectIDND1="49660@x" ObjectIDZND0="g_240d280@0" Pin0InfoVect0LinkObjId="g_240d280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319569_0" Pin1InfoVect1LinkObjId="SW-319566_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5982,-424 6027,-424 6027,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2410930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6232,-501 6214,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49666@x" ObjectIDND1="49665@x" ObjectIDZND0="49667@1" Pin0InfoVect0LinkObjId="SW-319576_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319575_0" Pin1InfoVect1LinkObjId="SW-319574_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6232,-501 6214,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2410b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6178,-501 6168,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49667@0" ObjectIDZND0="g_2410100@0" Pin0InfoVect0LinkObjId="g_2410100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319576_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6178,-501 6168,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2410d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6232,-522 6232,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49666@0" ObjectIDZND0="49667@x" ObjectIDZND1="49665@x" Pin0InfoVect0LinkObjId="SW-319576_0" Pin0InfoVect1LinkObjId="SW-319574_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319575_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6232,-522 6232,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2412f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6232,-501 6232,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49667@x" ObjectIDND1="49666@x" ObjectIDZND0="49665@0" Pin0InfoVect0LinkObjId="SW-319574_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319576_0" Pin1InfoVect1LinkObjId="SW-319575_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6232,-501 6232,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2413c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6231,-372 6213,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="49668@x" ObjectIDND1="g_2422f30@0" ObjectIDZND0="49669@1" Pin0InfoVect0LinkObjId="SW-319578_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319577_0" Pin1InfoVect1LinkObjId="g_2422f30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6231,-372 6213,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2413e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6177,-372 6167,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49669@0" ObjectIDZND0="g_2413170@0" Pin0InfoVect0LinkObjId="g_2413170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319578_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6177,-372 6167,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24140c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6232,-387 6232,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49668@0" ObjectIDZND0="49669@x" ObjectIDZND1="g_2422f30@0" Pin0InfoVect0LinkObjId="SW-319578_0" Pin0InfoVect1LinkObjId="g_2422f30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319577_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6232,-387 6232,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_241e9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6232,-455 6232,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49665@1" ObjectIDZND0="49668@x" ObjectIDZND1="g_2422180@0" Pin0InfoVect0LinkObjId="SW-319577_0" Pin0InfoVect1LinkObjId="g_2422180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319574_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6232,-455 6232,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_241ec40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6232,-436 6232,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="49665@x" ObjectIDND1="g_2422180@0" ObjectIDZND0="49668@1" Pin0InfoVect0LinkObjId="SW-319577_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319574_0" Pin1InfoVect1LinkObjId="g_2422180_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6232,-436 6232,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2421f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6277,-418 6277,-436 6232,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2422180@0" ObjectIDZND0="49668@x" ObjectIDZND1="49665@x" Pin0InfoVect0LinkObjId="SW-319577_0" Pin0InfoVect1LinkObjId="SW-319574_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2422180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6277,-418 6277,-436 6232,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2423c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6231,-295 6231,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2422f30@1" Pin0InfoVect0LinkObjId="g_2422f30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6231,-295 6231,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2423ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6231,-360 6231,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2422f30@0" ObjectIDZND0="49669@x" ObjectIDZND1="49668@x" Pin0InfoVect0LinkObjId="SW-319578_0" Pin0InfoVect1LinkObjId="SW-319577_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2422f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6231,-360 6231,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2424140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6194,-268 6194,-282 6231,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2420d30@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2420d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6194,-268 6194,-282 6231,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2424e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-485 5060,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="10547@x" ObjectIDND1="10550@x" ObjectIDZND0="10545@1" Pin0InfoVect0LinkObjId="SW-57001_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56998_0" Pin1InfoVect1LinkObjId="SW-56997_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-485 5060,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2425090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5024,-485 5014,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10545@0" ObjectIDZND0="g_24243a0@0" Pin0InfoVect0LinkObjId="g_24243a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57001_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5024,-485 5014,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24252f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-506 5078,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="10547@0" ObjectIDZND0="10545@x" ObjectIDZND1="10550@x" Pin0InfoVect0LinkObjId="SW-57001_0" Pin0InfoVect1LinkObjId="SW-56997_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56998_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-506 5078,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24275a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-485 5078,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10547@x" ObjectIDND1="10545@x" ObjectIDZND0="10550@0" Pin0InfoVect0LinkObjId="SW-56997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56998_0" Pin1InfoVect1LinkObjId="SW-57001_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-485 5078,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2428290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-356 5059,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="generator" EndDevType0="switch" ObjectIDND0="10549@x" ObjectIDND1="0@x" ObjectIDZND0="10546@1" Pin0InfoVect0LinkObjId="SW-57002_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57000_0" Pin1InfoVect1LinkObjId="SM-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-356 5059,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24284f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5023,-356 5013,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10546@0" ObjectIDZND0="g_2427800@0" Pin0InfoVect0LinkObjId="g_2427800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57002_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5023,-356 5013,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2428750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-371 5078,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="10549@0" ObjectIDZND0="10546@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-57002_0" Pin0InfoVect1LinkObjId="SM-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-371 5078,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24289b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-356 5078,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="10549@x" ObjectIDND1="10546@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57000_0" Pin1InfoVect1LinkObjId="SW-57002_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-356 5078,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24332d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-439 5078,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="10550@1" ObjectIDZND0="10549@x" ObjectIDZND1="g_2433530@0" Pin0InfoVect0LinkObjId="SW-57000_0" Pin0InfoVect1LinkObjId="g_2433530_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-439 5078,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24342e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-407 5078,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="10549@1" ObjectIDZND0="10550@x" ObjectIDZND1="g_2433530@0" Pin0InfoVect0LinkObjId="SW-56997_0" Pin0InfoVect1LinkObjId="g_2433530_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-407 5078,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2434540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-420 5123,-420 5123,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="10549@x" ObjectIDND1="10550@x" ObjectIDZND0="g_2433530@0" Pin0InfoVect0LinkObjId="g_2433530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57000_0" Pin1InfoVect1LinkObjId="SW-56997_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-420 5123,-420 5123,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24347a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-571 5078,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10517@0" ObjectIDZND0="10547@1" Pin0InfoVect0LinkObjId="SW-56998_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-571 5078,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2438c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5357,-694 5339,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49513@x" ObjectIDND1="49512@x" ObjectIDZND0="49515@1" Pin0InfoVect0LinkObjId="SW-319370_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319368_0" Pin1InfoVect1LinkObjId="SW-319367_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5357,-694 5339,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2438e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5303,-694 5293,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49515@0" ObjectIDZND0="g_24381a0@0" Pin0InfoVect0LinkObjId="g_24381a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5303,-694 5293,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24390f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5357,-715 5357,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49513@0" ObjectIDZND0="49515@x" ObjectIDZND1="49512@x" Pin0InfoVect0LinkObjId="SW-319370_0" Pin0InfoVect1LinkObjId="SW-319367_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319368_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5357,-715 5357,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_243b3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5357,-694 5357,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49515@x" ObjectIDND1="49513@x" ObjectIDZND0="49512@0" Pin0InfoVect0LinkObjId="SW-319367_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319370_0" Pin1InfoVect1LinkObjId="SW-319368_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5357,-694 5357,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_243c090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5357,-565 5338,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="49514@x" ObjectIDND1="g_2460a70@0" ObjectIDZND0="49516@1" Pin0InfoVect0LinkObjId="SW-319371_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319369_0" Pin1InfoVect1LinkObjId="g_243c7b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5357,-565 5338,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_243c2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5302,-565 5292,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49516@0" ObjectIDZND0="g_243b600@0" Pin0InfoVect0LinkObjId="g_243b600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319371_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5302,-565 5292,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_243c550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5357,-580 5357,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49514@0" ObjectIDZND0="49516@x" ObjectIDZND1="g_2460a70@0" Pin0InfoVect0LinkObjId="SW-319371_0" Pin0InfoVect1LinkObjId="g_243c7b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319369_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5357,-580 5357,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_243c7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5357,-565 5357,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="49516@x" ObjectIDND1="49514@x" ObjectIDZND0="g_2460a70@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319371_0" Pin1InfoVect1LinkObjId="SW-319369_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5357,-565 5357,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24470d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5357,-648 5357,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="49512@1" ObjectIDZND0="49514@x" ObjectIDZND1="g_2447330@0" Pin0InfoVect0LinkObjId="SW-319369_0" Pin0InfoVect1LinkObjId="g_2447330_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319367_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5357,-648 5357,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24480e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5357,-616 5357,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="49514@1" ObjectIDZND0="49512@x" ObjectIDZND1="g_2447330@0" Pin0InfoVect0LinkObjId="SW-319367_0" Pin0InfoVect1LinkObjId="g_2447330_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319369_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5357,-616 5357,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2448340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5357,-629 5402,-629 5402,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="49514@x" ObjectIDND1="49512@x" ObjectIDZND0="g_2447330@0" Pin0InfoVect0LinkObjId="g_2447330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319369_0" Pin1InfoVect1LinkObjId="SW-319367_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5357,-629 5402,-629 5402,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24501e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6114,-635 6096,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49678@x" ObjectIDND1="49677@x" ObjectIDZND0="49680@1" Pin0InfoVect0LinkObjId="SW-319593_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319591_0" Pin1InfoVect1LinkObjId="SW-319590_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6114,-635 6096,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2450440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6060,-635 6050,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49680@0" ObjectIDZND0="g_244f750@0" Pin0InfoVect0LinkObjId="g_244f750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319593_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6060,-635 6050,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2453150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6115,-624 6115,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49678@1" ObjectIDZND0="49680@x" ObjectIDZND1="49677@x" Pin0InfoVect0LinkObjId="SW-319593_0" Pin0InfoVect1LinkObjId="SW-319590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319591_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6115,-624 6115,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24533b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6115,-635 6115,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49680@x" ObjectIDND1="49678@x" ObjectIDZND0="49677@0" Pin0InfoVect0LinkObjId="SW-319590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319593_0" Pin1InfoVect1LinkObjId="SW-319591_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6115,-635 6115,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24543c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6163,-759 6163,-778 6116,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2453610@0" ObjectIDZND0="49679@x" ObjectIDZND1="49513@x" ObjectIDZND2="8803@x" Pin0InfoVect0LinkObjId="SW-319592_0" Pin0InfoVect1LinkObjId="SW-319368_0" Pin0InfoVect2LinkObjId="SW-48032_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2453610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6163,-759 6163,-778 6116,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2454880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6115,-748 6115,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="49679@1" ObjectIDZND0="g_2453610@0" ObjectIDZND1="49513@x" ObjectIDZND2="8803@x" Pin0InfoVect0LinkObjId="g_2453610_0" Pin0InfoVect1LinkObjId="SW-319368_0" Pin0InfoVect2LinkObjId="SW-48032_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319592_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6115,-748 6115,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2454ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6115,-675 6115,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49677@1" ObjectIDZND0="49679@0" Pin0InfoVect0LinkObjId="SW-319592_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6115,-675 6115,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2455380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6380,-663 6426,-663 6426,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="49682@x" ObjectIDND1="49681@x" ObjectIDND2="g_245e6b0@0" ObjectIDZND0="g_245d900@0" Pin0InfoVect0LinkObjId="g_245d900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-319601_0" Pin1InfoVect1LinkObjId="SW-319600_0" Pin1InfoVect2LinkObjId="g_245e6b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6380,-663 6426,-663 6426,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2456f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6380,-735 6347,-735 6347,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_24571d0@0" Pin0InfoVect0LinkObjId="g_24571d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6380,-735 6347,-735 6347,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2457c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6325,-654 6315,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49682@0" ObjectIDZND0="g_245f3f0@0" Pin0InfoVect0LinkObjId="g_245f3f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6325,-654 6315,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_245a930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6379,-654 6361,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_245d900@0" ObjectIDND1="g_245e6b0@0" ObjectIDND2="49681@x" ObjectIDZND0="49682@1" Pin0InfoVect0LinkObjId="SW-319601_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_245d900_0" Pin1InfoVect1LinkObjId="g_245e6b0_0" Pin1InfoVect2LinkObjId="SW-319600_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6379,-654 6361,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_245ab90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6380,-654 6380,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="49682@x" ObjectIDND1="49681@x" ObjectIDZND0="g_245d900@0" ObjectIDZND1="g_245e6b0@0" Pin0InfoVect0LinkObjId="g_245d900_0" Pin0InfoVect1LinkObjId="g_245e6b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319601_0" Pin1InfoVect1LinkObjId="SW-319600_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6380,-654 6380,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_245d6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6380,-630 6380,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49681@1" ObjectIDZND0="g_245d900@0" ObjectIDZND1="g_245e6b0@0" ObjectIDZND2="49682@x" Pin0InfoVect0LinkObjId="g_245d900_0" Pin0InfoVect1LinkObjId="g_245e6b0_0" Pin0InfoVect2LinkObjId="SW-319601_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319600_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6380,-630 6380,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_245ef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6380,-663 6380,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_245d900@0" ObjectIDND1="49682@x" ObjectIDND2="49681@x" ObjectIDZND0="g_245e6b0@1" Pin0InfoVect0LinkObjId="g_245e6b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_245d900_0" Pin1InfoVect1LinkObjId="SW-319601_0" Pin1InfoVect2LinkObjId="SW-319600_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6380,-663 6380,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_245f190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6380,-716 6380,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_245e6b0@0" ObjectIDZND0="g_2455570@0" Pin0InfoVect0LinkObjId="g_2455570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_245e6b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6380,-716 6380,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_245fe80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-888 5105,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8833@0" ObjectIDZND0="8803@x" ObjectIDZND1="49513@x" ObjectIDZND2="g_2453610@0" Pin0InfoVect0LinkObjId="SW-48032_0" Pin0InfoVect1LinkObjId="SW-319368_0" Pin0InfoVect2LinkObjId="g_2453610_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22ab710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-888 5105,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24600e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-791 4263,-791 4263,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8833@x" ObjectIDND1="49513@x" ObjectIDND2="g_2453610@0" ObjectIDZND0="8803@1" Pin0InfoVect0LinkObjId="SW-48032_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22ab710_0" Pin1InfoVect1LinkObjId="SW-319368_0" Pin1InfoVect2LinkObjId="g_2453610_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-791 4263,-791 4263,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2460340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5357,-751 5357,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="49513@1" ObjectIDZND0="g_2453610@0" ObjectIDZND1="49679@x" ObjectIDZND2="8803@x" Pin0InfoVect0LinkObjId="g_2453610_0" Pin0InfoVect1LinkObjId="SW-319592_0" Pin0InfoVect2LinkObjId="SW-48032_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319368_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5357,-751 5357,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24605a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6115,-778 6115,-790 6114,-791 5357,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_2453610@0" ObjectIDND1="49679@x" ObjectIDZND0="49513@x" ObjectIDZND1="8803@x" ObjectIDZND2="8833@x" Pin0InfoVect0LinkObjId="SW-319368_0" Pin0InfoVect1LinkObjId="SW-48032_0" Pin0InfoVect2LinkObjId="g_22ab710_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2453610_0" Pin1InfoVect1LinkObjId="SW-319592_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6115,-778 6115,-790 6114,-791 5357,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2460810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5357,-791 5105,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="49513@x" ObjectIDND1="g_2453610@0" ObjectIDND2="49679@x" ObjectIDZND0="8803@x" ObjectIDZND1="8833@x" Pin0InfoVect0LinkObjId="SW-48032_0" Pin0InfoVect1LinkObjId="g_22ab710_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-319368_0" Pin1InfoVect1LinkObjId="g_2453610_0" Pin1InfoVect2LinkObjId="SW-319592_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5357,-791 5105,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24688b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6115,-588 6115,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49678@0" ObjectIDZND0="49687@0" Pin0InfoVect0LinkObjId="g_2468aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6115,-588 6115,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2468aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6380,-594 6380,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49681@0" ObjectIDZND0="49687@0" Pin0InfoVect0LinkObjId="g_24688b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6380,-594 6380,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2468c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5575,-552 5575,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49686@1" ObjectIDZND0="49687@0" Pin0InfoVect0LinkObjId="g_24688b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319812_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5575,-552 5575,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2468ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5783,-551 5783,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49656@1" ObjectIDZND0="49687@0" Pin0InfoVect0LinkObjId="g_24688b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319559_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5783,-551 5783,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24690d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5982,-546 5982,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49661@1" ObjectIDZND0="49687@0" Pin0InfoVect0LinkObjId="g_24688b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319567_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5982,-546 5982,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2469300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6232,-558 6232,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49666@1" ObjectIDZND0="49687@0" Pin0InfoVect0LinkObjId="g_24688b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319575_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6232,-558 6232,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2469530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6453,-550 6453,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49671@1" ObjectIDZND0="49687@0" Pin0InfoVect0LinkObjId="g_24688b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319582_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6453,-550 6453,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_246ebd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6453,-415 6453,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49673@1" ObjectIDZND0="49670@1" Pin0InfoVect0LinkObjId="SW-319581_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319584_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6453,-415 6453,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_246edc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6434,-364 6453,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49674@1" ObjectIDZND0="g_21d92b0@0" ObjectIDZND1="g_21d8500@0" ObjectIDZND2="49673@x" Pin0InfoVect0LinkObjId="g_21d92b0_0" Pin0InfoVect1LinkObjId="g_21d8500_0" Pin0InfoVect2LinkObjId="SW-319584_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319585_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6434,-364 6453,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_246efb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6453,-333 6453,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_21d92b0@0" ObjectIDZND0="49674@x" ObjectIDZND1="g_21d8500@0" ObjectIDZND2="49673@x" Pin0InfoVect0LinkObjId="SW-319585_0" Pin0InfoVect1LinkObjId="g_21d8500_0" Pin0InfoVect2LinkObjId="SW-319584_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21d92b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6453,-333 6453,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_246f1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6453,-364 6453,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_21d92b0@0" ObjectIDND1="49674@x" ObjectIDND2="g_21d8500@0" ObjectIDZND0="49673@0" Pin0InfoVect0LinkObjId="SW-319584_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21d92b0_0" Pin1InfoVect1LinkObjId="SW-319585_0" Pin1InfoVect2LinkObjId="g_21d8500_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6453,-364 6453,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_246f3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6453,-364 6502,-364 6502,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_21d92b0@0" ObjectIDND1="49674@x" ObjectIDND2="49673@x" ObjectIDZND0="g_21d8500@0" Pin0InfoVect0LinkObjId="g_21d8500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_21d92b0_0" Pin1InfoVect1LinkObjId="SW-319585_0" Pin1InfoVect2LinkObjId="SW-319584_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6453,-364 6502,-364 6502,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2472a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6397,-209 6387,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49676@0" ObjectIDZND0="g_2471fa0@0" Pin0InfoVect0LinkObjId="g_2471fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319587_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6397,-209 6387,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2473a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6433,-209 6453,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="49676@1" ObjectIDZND0="g_2472c90@0" ObjectIDZND1="49675@x" Pin0InfoVect0LinkObjId="g_2472c90_0" Pin0InfoVect1LinkObjId="SW-319586_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319587_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6433,-209 6453,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2473c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6453,-185 6453,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2472c90@0" ObjectIDZND0="49676@x" ObjectIDZND1="49675@x" Pin0InfoVect0LinkObjId="SW-319587_0" Pin0InfoVect1LinkObjId="SW-319586_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2472c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6453,-185 6453,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2473e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6453,-209 6453,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2472c90@0" ObjectIDND1="49676@x" ObjectIDZND0="49675@0" Pin0InfoVect0LinkObjId="SW-319586_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2472c90_0" Pin1InfoVect1LinkObjId="SW-319587_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6453,-209 6453,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2474040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6450,-246 6417,-246 6417,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="6450,-246 6417,-246 6417,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3872a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5357,-472 5357,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3911670@0" ObjectIDZND0="g_3916c10@0" Pin0InfoVect0LinkObjId="g_3916c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5357,-472 5357,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38f92c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5407,-347 5407,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3243c70@0" ObjectIDZND0="g_2155a20@0" Pin0InfoVect0LinkObjId="g_2155a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5407,-347 5407,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a40420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5407,-380 5407,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_38724a0@0" ObjectIDZND0="49517@0" Pin0InfoVect0LinkObjId="SW-319372_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38724a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5407,-380 5407,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bbf000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5407,-432 5407,-455 5357,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="49517@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319372_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5407,-432 5407,-455 5357,-455 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="10517" cx="3646" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10517" cx="3833" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10517" cx="4021" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10517" cx="4207" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10517" cx="4394" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10517" cx="4574" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10517" cx="3848" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10517" cx="4738" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10517" cx="4897" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8783" cx="4648" cy="-1004" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8783" cx="4826" cy="-1004" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10517" cx="5078" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8783" cx="5017" cy="-1004" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10517" cx="4263" cy="-571" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49687" cx="6115" cy="-575" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49687" cx="6380" cy="-575" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49687" cx="5575" cy="-575" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49687" cx="5783" cy="-575" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49687" cx="5982" cy="-575" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49687" cx="6232" cy="-575" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49687" cx="6453" cy="-575" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37325" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3435.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5907" ObjectName="DYN-CX_LYS"/>
     <cge:Meas_Ref ObjectId="37325"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21f29b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3544.000000 -601.000000) translate(0,15)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_230a350" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4168.000000 -274.000000) translate(0,15)">雷应山II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20111d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4153.000000 -253.000000) translate(0,15)">(17-33号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2157960" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4376.000000 -171.000000) translate(0,12)">SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ed3380" transform="matrix(1.000000 0.000000 -0.000000 1.000000 6325.000000 -1000.000000) translate(0,15)">10kV3号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21a5400" transform="matrix(1.000000 0.000000 -0.000000 1.000000 6336.000000 -1385.000000) translate(0,15)">35kV能禹变10kV星</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21a5400" transform="matrix(1.000000 0.000000 -0.000000 1.000000 6336.000000 -1385.000000) translate(0,33)">火凉山支线曹家村次支线线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21f2b20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3580.000000 -180.000000) translate(0,15)">35kV1号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22456c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4310.000000 -143.000000) translate(0,15)">1号静止无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_215c170" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3778.000000 -275.000000) translate(0,15)">雷应山Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_215c9c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3772.000000 -254.000000) translate(0,15)">(1-16号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_215cfb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4056.000000 -130.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b5e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4659.000000 -1128.000000) translate(0,12)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b6a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4593.000000 -1114.000000) translate(0,12)">18117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b6dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4592.500000 -1177.000000) translate(0,12)">18160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b7510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4655.000000 -1056.000000) translate(0,12)">1811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b7790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4653.500000 -1194.000000) translate(0,12)">1816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b79d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4592.500000 -1243.000000) translate(0,12)">18167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b7c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -1059.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b8130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4833.000000 -1081.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b83b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -1136.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2142b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5028.000000 -1128.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2142d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5024.000000 -1056.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2142f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4962.000000 -1114.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21431d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.500000 -1177.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2143410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.500000 -1243.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2143650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5023.500000 -1194.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2143890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5133.000000 -935.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2144250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4269.500000 -732.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21448c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -664.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2144b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4206.000000 -656.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2144d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4270.000000 -608.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4392.000000 -1001.000000) translate(0,12)">110kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2146060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3655.500000 -463.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21464b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3651.500000 -535.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21466f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3589.500000 -511.000000) translate(0,12)">38117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2146930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3651.000000 -399.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2146b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3589.000000 -383.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2146db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3841.500000 -463.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21470a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3837.500000 -535.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2147480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3775.500000 -511.000000) translate(0,12)">38217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21476c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3837.000000 -399.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2147900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3775.000000 -383.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2147b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -463.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2147d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4029.000000 -535.000000) translate(0,12)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2147fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3964.500000 -511.000000) translate(0,12)">38317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2148200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.500000 -250.000000) translate(0,12)">3836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_214c050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3964.000000 -237.000000) translate(0,12)">38360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_214c540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.500000 -463.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_214c880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4211.500000 -535.000000) translate(0,12)">3841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_220a450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.500000 -511.000000) translate(0,12)">38417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_220a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4211.000000 -399.000000) translate(0,12)">3846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_220a8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.000000 -383.000000) translate(0,12)">38467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_220ab10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4403.500000 -463.000000) translate(0,12)">385</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_220ae50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.500000 -535.000000) translate(0,12)">3851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_220b2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.500000 -511.000000) translate(0,12)">38517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_220b4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -399.000000) translate(0,12)">3853</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_220b730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -383.000000) translate(0,12)">38537</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_220b970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4400.000000 -244.000000) translate(0,12)">3856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_220cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2215550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2215550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2215550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2215550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2215550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2215550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2215550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_21bc6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3271.000000 -1165.500000) translate(0,16)">雷应山风电场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" graphid="g_21be530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -911.000000) translate(0,12)">温度：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" graphid="g_21c1c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -886.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21c3d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4956.000000 -909.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20c27b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4609.000000 -130.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20c82f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3793.000000 -799.000000) translate(0,15)">35kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23259d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4671.000000 -274.000000) translate(0,15)">黑马井Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2326a50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4631.000000 -253.000000) translate(0,15)">(1-11、16-20号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2337180" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4871.500000 -274.000000) translate(0,15)">黑马井Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2337af0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4828.000000 -253.000000) translate(0,15)">(12-15、21-32号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2338fe0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 6369.000000 -147.000000) translate(0,15)">2号静止无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21db0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4028.500000 -399.000000) translate(0,12)">3833</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21db6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4581.000000 -535.000000) translate(0,12)">3861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21db910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.500000 -511.000000) translate(0,12)">38617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dbb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4580.500000 -399.000000) translate(0,12)">3863</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dbd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4585.000000 -463.000000) translate(0,12)">386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dbfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.000000 -383.000000) translate(0,12)">38637</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4580.500000 -250.000000) translate(0,12)">3866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.000000 -237.000000) translate(0,12)">38660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4580.000000 -74.000000) translate(0,12)">38667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dc8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4745.000000 -535.000000) translate(0,12)">3871</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dcb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4682.000000 -511.000000) translate(0,12)">38717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dcd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -463.000000) translate(0,12)">387</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dcf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -399.000000) translate(0,12)">3876</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dd1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4680.000000 -383.000000) translate(0,12)">38767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dd410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4902.500000 -535.000000) translate(0,12)">3881</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dd650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4839.500000 -511.000000) translate(0,12)">38817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21dd890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.500000 -463.000000) translate(0,12)">388</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21ddad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4902.000000 -399.000000) translate(0,12)">3886</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21ddd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4839.000000 -383.000000) translate(0,12)">38867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21deaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3964.000000 -383.000000) translate(0,12)">38337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21ded30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4025.000000 -66.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21def70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3794.000000 -684.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21df1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -626.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2174ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4655.500000 -1320.000000) translate(0,12)">元雷线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2176b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5236.000000 -923.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2176b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5236.000000 -923.000000) translate(0,27)">SFZ11-120000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2176b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5236.000000 -923.000000) translate(0,42)">115±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2176b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5236.000000 -923.000000) translate(0,57)">120000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2176b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5236.000000 -923.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2176b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5236.000000 -923.000000) translate(0,87)">U%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217a590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4074.000000 -108.000000) translate(0,12)">7MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217af20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4632.000000 -108.000000) translate(0,12)">10MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217b3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4364.000000 -122.000000) translate(0,12)">10MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217f320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6441.000000 -126.000000) translate(0,12)">10MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2118400" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3256.000000 -219.000000) translate(0,15)">4727</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_211c2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -535.000000) translate(0,15)">全站检修停电前应挂“全站检修”牌，“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_211c2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -535.000000) translate(0,33)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_211c2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -535.000000) translate(0,51)">全站检修完工后仅可摘除“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_211c2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -535.000000) translate(0,69)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_211c2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -535.000000) translate(0,87)">全站检修复电后才可以摘除“全站检修”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_212b890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3377.000000 -1038.000000) translate(0,12)">AGC/AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_240fac0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 6166.000000 -193.000000) translate(0,15)">35kV2号所用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2434a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5092.000000 -460.000000) translate(0,12)">389</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2454d40" transform="matrix(1.000000 0.000000 -0.000000 1.000000 6325.000000 -795.000000) translate(0,15)">35kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2466500" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5381.000000 -269.000000) translate(0,15)">接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24671c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5527.000000 -271.000000) translate(0,15)">龙海古Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24671c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5527.000000 -271.000000) translate(0,33)">(7-8号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24683b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5731.000000 -268.000000) translate(0,15)">龙海古Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24683b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5731.000000 -268.000000) translate(0,33)">(4-6号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2468660" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5926.000000 -270.000000) translate(0,15)">龙海古Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2468660" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5926.000000 -270.000000) translate(0,33)">(1-3号发电机)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2469760" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5055.000000 -273.000000) translate(0,15)">储能进线一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246a3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5364.000000 -740.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246a830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5301.000000 -720.000000) translate(0,12)">33117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246aa70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5367.000000 -669.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246acb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5364.000000 -605.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246aef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5300.000000 -591.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246b560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5582.000000 -541.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246b810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5519.000000 -521.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246ba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5585.000000 -470.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246bc90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5582.000000 -406.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246bed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5518.000000 -392.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246c110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5790.000000 -540.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246c350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5727.000000 -520.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246c590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5793.000000 -469.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246c7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5790.000000 -405.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246ca10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5725.000000 -391.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246cc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5989.000000 -535.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246ce90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5926.000000 -515.000000) translate(0,12)">36317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246d0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5992.000000 -464.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246d310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5989.000000 -400.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246d550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5924.000000 -386.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246d790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6239.000000 -547.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246d9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6176.000000 -527.000000) translate(0,12)">36417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246dc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6241.000000 -476.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246de50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6239.000000 -412.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246e090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6175.000000 -398.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246e2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6460.000000 -539.000000) translate(0,12)">3651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246e510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6397.000000 -519.000000) translate(0,12)">36517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6462.000000 -468.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246e990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6460.000000 -404.000000) translate(0,12)">3653</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246f600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6396.000000 -390.000000) translate(0,12)">36537</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2474270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6460.000000 -254.000000) translate(0,12)">3656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2474630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6395.000000 -199.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2474870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6124.000000 -669.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2474ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6122.000000 -737.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2474cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6122.000000 -613.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2474f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6058.000000 -661.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2475170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6387.000000 -619.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24753b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6322.000000 -680.000000) translate(0,12)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24755f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5541.000000 -595.000000) translate(0,12)">35kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248e500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5085.000000 -531.000000) translate(0,12)">3891</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248e740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5022.000000 -511.000000) translate(0,12)">38917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248e980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5085.000000 -396.000000) translate(0,12)">3896</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248ebc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5020.000000 -382.000000) translate(0,12)">38967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a3b2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5414.000000 -421.000000) translate(0,12)">3310</text>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5025,-918 5020,-929 5031,-929 5025,-918 5025,-919 5025,-918 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5025,-904 5020,-893 5031,-893 5025,-904 5025,-903 5025,-904 " stroke="rgb(170,85,127)"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20c8cd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3833.000000 -754.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2455570">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6365.000000 -750.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_LYS.CX_LYS_1C">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3994.000000 -94.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10559" ObjectName="CB-CX_LYS.CX_LYS_1C"/>
    <cge:TPSR_Ref TObjectID="10559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_LYS.CX_LYS_2C">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4547.000000 -95.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10558" ObjectName="CB-CX_LYS.CX_LYS_2C"/>
    <cge:TPSR_Ref TObjectID="10558"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2216880">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3601.000000 -208.000000)" xlink:href="#lightningRod:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2217be0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4551.000000 -1236.000000)" xlink:href="#lightningRod:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_215da20">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 6393.000000 -1229.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_221fc90">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 6394.000000 -1171.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a87f0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4794.000000 -1148.000000)" xlink:href="#lightningRod:shape131"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21abb00">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4861.000000 -1144.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ac8b0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4683.000000 -1180.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ad660">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5043.000000 -878.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ae410">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -610.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f3ca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3871.000000 -348.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f4a50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4059.000000 -349.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f5800">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -347.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f65b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4432.000000 -347.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21f7360">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3684.000000 -347.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b2810">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3640.000000 -289.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b3a20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.000000 -278.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21b49d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4388.000000 -268.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c3360">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 -350.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c3f10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4569.000000 -279.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2163040">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3887.000000 -761.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2163df0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3839.000000 -725.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2326f60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4776.000000 -347.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2337da0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4935.000000 -347.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d8500">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6495.000000 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21d92b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6448.000000 -275.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2175780">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.000000 -1224.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_213c5e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5613.000000 -355.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23fb8b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5821.000000 -354.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_240d280">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6020.000000 -349.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2420d30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6187.000000 -221.000000)" xlink:href="#lightningRod:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2422180">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6270.000000 -360.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2422f30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6226.000000 -302.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2433530">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5116.000000 -345.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2447330">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5395.000000 -554.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2453610">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6156.000000 -701.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_245d900">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6419.000000 -757.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_245e6b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6371.000000 -721.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2472c90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6436.000000 -155.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2155a20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5400.000000 -272.000000)" xlink:href="#lightningRod:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3916c10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5352.000000 -484.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38724a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5400.000000 -341.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a615e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5340.000000 -439.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47925" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5180.000000 -1134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8794"/>
     <cge:Term_Ref ObjectID="12424"/>
    <cge:TPSR_Ref TObjectID="8794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47926" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5180.000000 -1134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8794"/>
     <cge:Term_Ref ObjectID="12424"/>
    <cge:TPSR_Ref TObjectID="8794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47921" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5180.000000 -1134.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8794"/>
     <cge:Term_Ref ObjectID="12424"/>
    <cge:TPSR_Ref TObjectID="8794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47958" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 -473.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8812"/>
     <cge:Term_Ref ObjectID="12460"/>
    <cge:TPSR_Ref TObjectID="8812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47959" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 -473.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8812"/>
     <cge:Term_Ref ObjectID="12460"/>
    <cge:TPSR_Ref TObjectID="8812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47952" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 -473.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8812"/>
     <cge:Term_Ref ObjectID="12460"/>
    <cge:TPSR_Ref TObjectID="8812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47949" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -473.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47949" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8807"/>
     <cge:Term_Ref ObjectID="12450"/>
    <cge:TPSR_Ref TObjectID="8807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47950" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -473.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47950" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8807"/>
     <cge:Term_Ref ObjectID="12450"/>
    <cge:TPSR_Ref TObjectID="8807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47943" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -473.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8807"/>
     <cge:Term_Ref ObjectID="12450"/>
    <cge:TPSR_Ref TObjectID="8807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47968" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -473.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8817"/>
     <cge:Term_Ref ObjectID="12470"/>
    <cge:TPSR_Ref TObjectID="8817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47969" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -473.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47969" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8817"/>
     <cge:Term_Ref ObjectID="12470"/>
    <cge:TPSR_Ref TObjectID="8817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47962" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -473.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47962" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8817"/>
     <cge:Term_Ref ObjectID="12470"/>
    <cge:TPSR_Ref TObjectID="8817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47914" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -1144.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8784"/>
     <cge:Term_Ref ObjectID="12404"/>
    <cge:TPSR_Ref TObjectID="8784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47915" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -1144.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8784"/>
     <cge:Term_Ref ObjectID="12404"/>
    <cge:TPSR_Ref TObjectID="8784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47910" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -1144.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8784"/>
     <cge:Term_Ref ObjectID="12404"/>
    <cge:TPSR_Ref TObjectID="8784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-47991" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4479.000000 -1085.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8783"/>
     <cge:Term_Ref ObjectID="12403"/>
    <cge:TPSR_Ref TObjectID="8783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-47992" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4479.000000 -1085.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47992" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8783"/>
     <cge:Term_Ref ObjectID="12403"/>
    <cge:TPSR_Ref TObjectID="8783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-47993" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4479.000000 -1085.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47993" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8783"/>
     <cge:Term_Ref ObjectID="12403"/>
    <cge:TPSR_Ref TObjectID="8783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-47994" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4479.000000 -1085.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8783"/>
     <cge:Term_Ref ObjectID="12403"/>
    <cge:TPSR_Ref TObjectID="8783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-47997" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4479.000000 -1085.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8783"/>
     <cge:Term_Ref ObjectID="12403"/>
    <cge:TPSR_Ref TObjectID="8783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47987" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -473.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47987" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8827"/>
     <cge:Term_Ref ObjectID="12490"/>
    <cge:TPSR_Ref TObjectID="8827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47988" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -473.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47988" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8827"/>
     <cge:Term_Ref ObjectID="12490"/>
    <cge:TPSR_Ref TObjectID="8827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47981" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -473.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47981" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8827"/>
     <cge:Term_Ref ObjectID="12490"/>
    <cge:TPSR_Ref TObjectID="8827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47977" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -473.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47977" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8822"/>
     <cge:Term_Ref ObjectID="12480"/>
    <cge:TPSR_Ref TObjectID="8822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47978" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -473.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8822"/>
     <cge:Term_Ref ObjectID="12480"/>
    <cge:TPSR_Ref TObjectID="8822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47971" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -473.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8822"/>
     <cge:Term_Ref ObjectID="12480"/>
    <cge:TPSR_Ref TObjectID="8822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47936" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4394.000000 -680.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8801"/>
     <cge:Term_Ref ObjectID="12438"/>
    <cge:TPSR_Ref TObjectID="8801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47937" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4394.000000 -680.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47937" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8801"/>
     <cge:Term_Ref ObjectID="12438"/>
    <cge:TPSR_Ref TObjectID="8801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47932" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4394.000000 -680.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8801"/>
     <cge:Term_Ref ObjectID="12438"/>
    <cge:TPSR_Ref TObjectID="8801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56981" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.000000 -473.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56981" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10530"/>
     <cge:Term_Ref ObjectID="14662"/>
    <cge:TPSR_Ref TObjectID="10530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56982" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.000000 -473.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10530"/>
     <cge:Term_Ref ObjectID="14662"/>
    <cge:TPSR_Ref TObjectID="10530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56974" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.000000 -473.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56974" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10530"/>
     <cge:Term_Ref ObjectID="14662"/>
    <cge:TPSR_Ref TObjectID="10530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56968" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4827.000000 -473.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10544"/>
     <cge:Term_Ref ObjectID="14690"/>
    <cge:TPSR_Ref TObjectID="10544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56969" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4827.000000 -473.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56969" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10544"/>
     <cge:Term_Ref ObjectID="14690"/>
    <cge:TPSR_Ref TObjectID="10544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56959" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4827.000000 -473.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10544"/>
     <cge:Term_Ref ObjectID="14690"/>
    <cge:TPSR_Ref TObjectID="10544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-65796" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4490.000000 -473.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="65796" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10523"/>
     <cge:Term_Ref ObjectID="14659"/>
    <cge:TPSR_Ref TObjectID="10523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-65797" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4490.000000 -473.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="65797" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10523"/>
     <cge:Term_Ref ObjectID="14659"/>
    <cge:TPSR_Ref TObjectID="10523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-65787" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4490.000000 -473.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="65787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10523"/>
     <cge:Term_Ref ObjectID="14659"/>
    <cge:TPSR_Ref TObjectID="10523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-319819" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5632.000000 -684.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319819" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49687"/>
     <cge:Term_Ref ObjectID="49446"/>
    <cge:TPSR_Ref TObjectID="49687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-319820" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5632.000000 -684.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319820" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49687"/>
     <cge:Term_Ref ObjectID="49446"/>
    <cge:TPSR_Ref TObjectID="49687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-319821" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5632.000000 -684.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49687"/>
     <cge:Term_Ref ObjectID="49446"/>
    <cge:TPSR_Ref TObjectID="49687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-319822" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5632.000000 -684.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319822" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49687"/>
     <cge:Term_Ref ObjectID="49446"/>
    <cge:TPSR_Ref TObjectID="49687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-319825" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5632.000000 -684.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319825" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49687"/>
     <cge:Term_Ref ObjectID="49446"/>
    <cge:TPSR_Ref TObjectID="49687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319384" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5261.000000 -661.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49512"/>
     <cge:Term_Ref ObjectID="48946"/>
    <cge:TPSR_Ref TObjectID="49512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319385" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5261.000000 -661.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319385" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49512"/>
     <cge:Term_Ref ObjectID="48946"/>
    <cge:TPSR_Ref TObjectID="49512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319375" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5261.000000 -661.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49512"/>
     <cge:Term_Ref ObjectID="48946"/>
    <cge:TPSR_Ref TObjectID="49512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319704" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6225.000000 -673.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319704" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49677"/>
     <cge:Term_Ref ObjectID="49430"/>
    <cge:TPSR_Ref TObjectID="49677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319705" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6225.000000 -673.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319705" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49677"/>
     <cge:Term_Ref ObjectID="49430"/>
    <cge:TPSR_Ref TObjectID="49677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319700" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6225.000000 -673.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49677"/>
     <cge:Term_Ref ObjectID="49430"/>
    <cge:TPSR_Ref TObjectID="49677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319839" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5524.000000 -474.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319839" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49685"/>
     <cge:Term_Ref ObjectID="49442"/>
    <cge:TPSR_Ref TObjectID="49685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319840" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5524.000000 -474.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49685"/>
     <cge:Term_Ref ObjectID="49442"/>
    <cge:TPSR_Ref TObjectID="49685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319830" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5524.000000 -474.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319830" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49685"/>
     <cge:Term_Ref ObjectID="49442"/>
    <cge:TPSR_Ref TObjectID="49685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319654" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5707.000000 -477.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49655"/>
     <cge:Term_Ref ObjectID="49384"/>
    <cge:TPSR_Ref TObjectID="49655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319655" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5707.000000 -477.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319655" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49655"/>
     <cge:Term_Ref ObjectID="49384"/>
    <cge:TPSR_Ref TObjectID="49655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319645" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5707.000000 -477.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49655"/>
     <cge:Term_Ref ObjectID="49384"/>
    <cge:TPSR_Ref TObjectID="49655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319667" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5902.000000 -476.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319667" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49660"/>
     <cge:Term_Ref ObjectID="49394"/>
    <cge:TPSR_Ref TObjectID="49660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319668" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5902.000000 -476.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319668" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49660"/>
     <cge:Term_Ref ObjectID="49394"/>
    <cge:TPSR_Ref TObjectID="49660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319658" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5902.000000 -476.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49660"/>
     <cge:Term_Ref ObjectID="49394"/>
    <cge:TPSR_Ref TObjectID="49660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319680" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6148.000000 -486.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49665"/>
     <cge:Term_Ref ObjectID="49404"/>
    <cge:TPSR_Ref TObjectID="49665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319681" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6148.000000 -486.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319681" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49665"/>
     <cge:Term_Ref ObjectID="49404"/>
    <cge:TPSR_Ref TObjectID="49665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319671" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6148.000000 -486.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49665"/>
     <cge:Term_Ref ObjectID="49404"/>
    <cge:TPSR_Ref TObjectID="49665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319693" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6373.000000 -480.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49670"/>
     <cge:Term_Ref ObjectID="49416"/>
    <cge:TPSR_Ref TObjectID="49670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319694" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6373.000000 -480.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319694" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49670"/>
     <cge:Term_Ref ObjectID="49416"/>
    <cge:TPSR_Ref TObjectID="49670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319684" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6373.000000 -480.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="49670"/>
     <cge:Term_Ref ObjectID="49416"/>
    <cge:TPSR_Ref TObjectID="49670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-65772" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -474.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="65772" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10550"/>
     <cge:Term_Ref ObjectID="14702"/>
    <cge:TPSR_Ref TObjectID="10550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-65773" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -474.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="65773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10550"/>
     <cge:Term_Ref ObjectID="14702"/>
    <cge:TPSR_Ref TObjectID="10550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-65763" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -474.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="65763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10550"/>
     <cge:Term_Ref ObjectID="14702"/>
    <cge:TPSR_Ref TObjectID="10550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-319708" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3648.000000 -675.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319708" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10517"/>
     <cge:Term_Ref ObjectID="14656"/>
    <cge:TPSR_Ref TObjectID="10517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-319709" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3648.000000 -675.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10517"/>
     <cge:Term_Ref ObjectID="14656"/>
    <cge:TPSR_Ref TObjectID="10517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-319710" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3648.000000 -675.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10517"/>
     <cge:Term_Ref ObjectID="14656"/>
    <cge:TPSR_Ref TObjectID="10517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-319711" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3648.000000 -675.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319711" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10517"/>
     <cge:Term_Ref ObjectID="14656"/>
    <cge:TPSR_Ref TObjectID="10517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-319714" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3648.000000 -675.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319714" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10517"/>
     <cge:Term_Ref ObjectID="14656"/>
    <cge:TPSR_Ref TObjectID="10517"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="42" qtmmishow="hidden" width="164" x="3246" y="-1176"/></g>
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
   <g href="AVC雷应山风电场.svg" style="fill-opacity:0"><rect height="31" qtmmishow="hidden" width="86" x="3370" y="-1048"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217bd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3528.000000 474.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217ccd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3517.000000 459.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217d220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 444.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217d960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.000000 680.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217dc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4330.000000 665.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217dea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 650.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217e2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4456.000000 1144.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217e580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4445.000000 1129.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217e7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4470.000000 1114.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217ebe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5122.000000 1134.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217eea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5111.000000 1119.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217f0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5136.000000 1104.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2122c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4394.000000 1068.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2123b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4394.000000 1083.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2123dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 1040.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2124000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4394.000000 1054.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2124580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4400.000000 1026.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2124f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 660.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2125220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 675.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2125460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3554.000000 632.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21256a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 646.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21258e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3568.000000 618.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2475930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5548.000000 670.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2475b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5548.000000 685.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2475dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5540.000000 642.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2476010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5548.000000 656.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2476250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5554.000000 628.000000) translate(0,12)">F（Hz）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2476c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5205.000000 662.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2476f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5194.000000 647.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2477160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5219.000000 632.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2477b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6173.000000 675.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2477e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6162.000000 660.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2478080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6187.000000 645.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248bda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5467.000000 474.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248c3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5456.000000 459.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_248c630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5481.000000 444.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LYS.CX_LYS_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3527,-571 5118,-571 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10517" ObjectName="BS-CX_LYS.CX_LYS_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="10517"/></metadata>
   <polyline fill="none" opacity="0" points="3527,-571 5118,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LYS.CX_LYS_1ⅠM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4388,-1004 5242,-1004 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="8783" ObjectName="BS-CX_LYS.CX_LYS_1ⅠM"/>
    <cge:TPSR_Ref TObjectID="8783"/></metadata>
   <polyline fill="none" opacity="0" points="4388,-1004 5242,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LYS.CX_LYS_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-575 6578,-575 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49687" ObjectName="BS-CX_LYS.CX_LYS_3IIM"/>
    <cge:TPSR_Ref TObjectID="49687"/></metadata>
   <polyline fill="none" opacity="0" points="5508,-575 6578,-575 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-47940" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5192.000000 -911.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47940" ObjectName="CX_LYS:CX_LYS_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-47990" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5191.000000 -887.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47990" ObjectName="CX_LYS:CX_LYS_1T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78584" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3256.538462 -1017.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78584" ObjectName="CX_LYS:CX_LYS_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79731" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3254.538462 -972.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79731" ObjectName="CX_LYS:CX_LYS_sumQ"/>
    </metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-48010">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5008.000000 -1099.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8794" ObjectName="SW-CX_LYS.CX_LYS_101BK"/>
     <cge:Meas_Ref ObjectId="48010"/>
    <cge:TPSR_Ref TObjectID="8794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48030">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -635.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8801" ObjectName="SW-CX_LYS.CX_LYS_301BK"/>
     <cge:Meas_Ref ObjectId="48030"/>
    <cge:TPSR_Ref TObjectID="8801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48092">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3637.000000 -478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8827" ObjectName="SW-CX_LYS.CX_LYS_381BK"/>
     <cge:Meas_Ref ObjectId="48092"/>
    <cge:TPSR_Ref TObjectID="8827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48086">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3824.250000 -478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8822" ObjectName="SW-CX_LYS.CX_LYS_382BK"/>
     <cge:Meas_Ref ObjectId="48086"/>
    <cge:TPSR_Ref TObjectID="8822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48079">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4011.500000 -478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8817" ObjectName="SW-CX_LYS.CX_LYS_383BK"/>
     <cge:Meas_Ref ObjectId="48079"/>
    <cge:TPSR_Ref TObjectID="8817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48072">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4197.750000 -478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8812" ObjectName="SW-CX_LYS.CX_LYS_384BK"/>
     <cge:Meas_Ref ObjectId="48072"/>
    <cge:TPSR_Ref TObjectID="8812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-48065">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4385.000000 -478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8807" ObjectName="SW-CX_LYS.CX_LYS_385BK"/>
     <cge:Meas_Ref ObjectId="48065"/>
    <cge:TPSR_Ref TObjectID="8807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47999">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4639.000000 -1101.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8784" ObjectName="SW-CX_LYS.CX_LYS_181BK"/>
     <cge:Meas_Ref ObjectId="47999"/>
    <cge:TPSR_Ref TObjectID="8784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4564.500000 -479.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10523" ObjectName="SW-CX_LYS.CX_LYS_386BK"/>
     <cge:Meas_Ref ObjectId="57004"/>
    <cge:TPSR_Ref TObjectID="10523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56985">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4729.250000 -477.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10530" ObjectName="SW-CX_LYS.CX_LYS_387BK"/>
     <cge:Meas_Ref ObjectId="56985"/>
    <cge:TPSR_Ref TObjectID="10530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56991">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4888.250000 -477.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10544" ObjectName="SW-CX_LYS.CX_LYS_388BK"/>
     <cge:Meas_Ref ObjectId="56991"/>
    <cge:TPSR_Ref TObjectID="10544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319581">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6444.000000 -483.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49670" ObjectName="SW-CX_LYS.CX_LYS_365BK"/>
     <cge:Meas_Ref ObjectId="319581"/>
    <cge:TPSR_Ref TObjectID="49670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319811">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5566.250000 -485.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49685" ObjectName="SW-CX_LYS.CX_LYS_361BK"/>
     <cge:Meas_Ref ObjectId="319811"/>
    <cge:TPSR_Ref TObjectID="49685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319558">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5774.250000 -484.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49655" ObjectName="SW-CX_LYS.CX_LYS_362BK"/>
     <cge:Meas_Ref ObjectId="319558"/>
    <cge:TPSR_Ref TObjectID="49655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319566">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5973.250000 -479.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49660" ObjectName="SW-CX_LYS.CX_LYS_363BK"/>
     <cge:Meas_Ref ObjectId="319566"/>
    <cge:TPSR_Ref TObjectID="49660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319574">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 6223.000000 -491.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49665" ObjectName="SW-CX_LYS.CX_LYS_364BK"/>
     <cge:Meas_Ref ObjectId="319574"/>
    <cge:TPSR_Ref TObjectID="49665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56997">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5069.250000 -475.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10550" ObjectName="SW-CX_LYS.CX_LYS_389BK"/>
     <cge:Meas_Ref ObjectId="56997"/>
    <cge:TPSR_Ref TObjectID="10550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319367">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5348.250000 -684.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49512" ObjectName="SW-CX_LYS.CX_LYS_331BK"/>
     <cge:Meas_Ref ObjectId="319367"/>
    <cge:TPSR_Ref TObjectID="49512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319590">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6106.000000 -640.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49677" ObjectName="SW-CX_LYS.CX_LYS_302BK"/>
     <cge:Meas_Ref ObjectId="319590"/>
    <cge:TPSR_Ref TObjectID="49677"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="42" qtmmishow="hidden" width="164" x="3246" y="-1176"/>
    </a>
   <metadata/><rect fill="white" height="42" opacity="0" stroke="white" transform="" width="164" x="3246" y="-1176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="31" qtmmishow="hidden" width="86" x="3370" y="-1048"/>
    </a>
   <metadata/><rect fill="white" height="31" opacity="0" stroke="white" transform="" width="86" x="3370" y="-1048"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_LYS.">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3630.000000 -287.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3630.000000 -287.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-CX_LYS."/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_LYS.CX_LYS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="12504"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5071.000000 -883.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5071.000000 -883.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="8833" ObjectName="TF-CX_LYS.CX_LYS_1T"/>
    <cge:TPSR_Ref TObjectID="8833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.884615 -0.000000 0.000000 -0.923077 6378.000000 -1125.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.884615 -0.000000 0.000000 -0.923077 6378.000000 -1125.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 6216.000000 -300.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 6216.000000 -300.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LYS"/>
</svg>