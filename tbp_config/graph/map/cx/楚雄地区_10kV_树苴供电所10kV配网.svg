<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" aopId="0" id="thSvg" viewBox="-2 -2972 5202 2974">
 
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    
   </symbol>
   <symbol id="Tag:shape27">
    
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="0.5" width="111" x="0" y="0"/>
    <line stroke="rgb(50,205,50)" stroke-width="1.5" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)"/>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(127,127,127);fill:none}
.BKBV-0KV { stroke:rgb(127,127,127);fill:rgb(127,127,127)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(0,72,216);fill:none}
.BKBV-10KV { stroke:rgb(0,72,216);fill:rgb(0,72,216)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(0,255,0);fill:none}
.BKBV-20KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(213,0,0);fill:none}
.BKBV-110KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-220KV { stroke:rgb(255,0,255);fill:none}
.BKBV-220KV { stroke:rgb(255,0,255);fill:rgb(255,0,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(255,0,0);fill:none}
.BKBV-500KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-750KV { stroke:rgb(255,0,0);fill:none}
.BKBV-750KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-22KV { stroke:rgb(255,255,255);fill:none}
.BKBV-22KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-380KV { stroke:rgb(255,255,255);fill:none}
.BKBV-380KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="2984" width="5212" x="-7" y="-2977"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="0" fill="none" points="1711,-1795 1711,-1794 1711,-1793 1712,-1792 1712,-1791 1713,-1790 1714,-1789 1715,-1788 1716,-1788 1717,-1787 1718,-1787 1719,-1787 1720,-1787 1721,-1787 1722,-1787 1723,-1787 1724,-1788 1725,-1788 1726,-1789 1727,-1790 1728,-1791 1728,-1792 1729,-1793 1729,-1794 1729,-1795 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1729,-1795 1729,-1794 1729,-1793 1730,-1792 1730,-1791 1731,-1790 1732,-1789 1733,-1788 1734,-1788 1735,-1787 1736,-1787 1737,-1787 1738,-1787 1739,-1787 1740,-1787 1741,-1787 1742,-1788 1743,-1788 1744,-1789 1745,-1790 1746,-1791 1746,-1792 1747,-1793 1747,-1794 1747,-1795 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1561,-1795 1561,-1794 1561,-1793 1562,-1792 1562,-1791 1563,-1790 1564,-1789 1565,-1788 1566,-1788 1567,-1787 1568,-1787 1569,-1787 1570,-1787 1571,-1787 1572,-1787 1573,-1787 1574,-1788 1575,-1788 1576,-1789 1577,-1790 1578,-1791 1578,-1792 1579,-1793 1579,-1794 1579,-1795 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1579,-1795 1579,-1794 1579,-1793 1580,-1792 1580,-1791 1581,-1790 1582,-1789 1583,-1788 1584,-1788 1585,-1787 1586,-1787 1587,-1787 1588,-1787 1589,-1787 1590,-1787 1591,-1787 1592,-1788 1593,-1788 1594,-1789 1595,-1790 1596,-1791 1596,-1792 1597,-1793 1597,-1794 1597,-1795 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1717,-1398 1717,-1397 1717,-1396 1718,-1395 1718,-1394 1719,-1393 1720,-1392 1721,-1391 1722,-1391 1723,-1390 1724,-1390 1725,-1390 1726,-1390 1727,-1390 1728,-1390 1729,-1390 1730,-1391 1731,-1391 1732,-1392 1733,-1393 1734,-1394 1734,-1395 1735,-1396 1735,-1397 1735,-1398 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1735,-1398 1735,-1397 1735,-1396 1736,-1395 1736,-1394 1737,-1393 1738,-1392 1739,-1391 1740,-1391 1741,-1390 1742,-1390 1743,-1390 1744,-1390 1745,-1390 1746,-1390 1747,-1390 1748,-1391 1749,-1391 1750,-1392 1751,-1393 1752,-1394 1752,-1395 1753,-1396 1753,-1397 1753,-1398 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2909,-2176 2910,-2176 2911,-2176 2912,-2177 2913,-2177 2914,-2178 2915,-2179 2916,-2180 2916,-2181 2917,-2182 2917,-2183 2917,-2184 2917,-2185 2917,-2186 2917,-2187 2917,-2188 2916,-2189 2916,-2190 2915,-2191 2914,-2192 2913,-2193 2912,-2193 2911,-2194 2910,-2194 2909,-2194 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2909,-2194 2910,-2194 2911,-2194 2912,-2195 2913,-2195 2914,-2196 2915,-2197 2916,-2198 2916,-2199 2917,-2200 2917,-2201 2917,-2202 2917,-2203 2917,-2204 2917,-2205 2917,-2206 2916,-2207 2916,-2208 2915,-2209 2914,-2210 2913,-2211 2912,-2211 2911,-2212 2910,-2212 2909,-2212 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3417,-2171 3418,-2171 3419,-2171 3420,-2171 3421,-2172 3422,-2173 3423,-2173 3424,-2174 3425,-2175 3425,-2176 3426,-2177 3426,-2178 3426,-2179 3426,-2180 3426,-2181 3426,-2182 3426,-2183 3425,-2184 3425,-2185 3424,-2186 3423,-2187 3422,-2188 3421,-2188 3420,-2189 3419,-2189 3418,-2189 3417,-2189 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3417,-2189 3418,-2189 3419,-2189 3420,-2189 3421,-2190 3422,-2191 3423,-2191 3424,-2192 3425,-2193 3425,-2194 3426,-2195 3426,-2196 3426,-2197 3426,-2198 3426,-2199 3426,-2200 3426,-2201 3425,-2202 3425,-2203 3424,-2204 3423,-2205 3422,-2206 3421,-2206 3420,-2207 3419,-2207 3418,-2207 3417,-2207 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3417,-1492 3418,-1492 3419,-1492 3420,-1492 3421,-1493 3422,-1494 3423,-1494 3424,-1495 3425,-1496 3425,-1497 3426,-1498 3426,-1499 3426,-1500 3426,-1501 3426,-1502 3426,-1503 3426,-1504 3425,-1505 3425,-1506 3424,-1507 3423,-1508 3422,-1509 3421,-1509 3420,-1510 3419,-1510 3418,-1510 3417,-1510 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3417,-1510 3418,-1510 3419,-1510 3420,-1510 3421,-1511 3422,-1512 3423,-1512 3424,-1513 3425,-1514 3425,-1515 3426,-1516 3426,-1517 3426,-1518 3426,-1519 3426,-1520 3426,-1521 3426,-1522 3425,-1523 3425,-1524 3424,-1525 3423,-1526 3422,-1527 3421,-1527 3420,-1528 3419,-1528 3418,-1528 3417,-1528 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3332,-1343 3332,-1342 3332,-1341 3333,-1340 3333,-1339 3334,-1338 3335,-1337 3336,-1336 3337,-1336 3338,-1335 3339,-1335 3340,-1335 3341,-1335 3342,-1335 3343,-1335 3344,-1335 3345,-1336 3346,-1336 3347,-1337 3348,-1338 3349,-1339 3349,-1340 3350,-1341 3350,-1342 3350,-1343 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3350,-1343 3350,-1342 3350,-1341 3351,-1340 3351,-1339 3352,-1338 3353,-1337 3354,-1336 3355,-1336 3356,-1335 3357,-1335 3358,-1335 3359,-1335 3360,-1335 3361,-1335 3362,-1335 3363,-1336 3364,-1336 3365,-1337 3366,-1338 3367,-1339 3367,-1340 3368,-1341 3368,-1342 3368,-1343 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3444,-384 3445,-384 3446,-384 3447,-385 3448,-385 3449,-386 3450,-387 3451,-388 3451,-389 3452,-390 3452,-391 3452,-392 3452,-393 3452,-394 3452,-395 3452,-396 3451,-397 3451,-398 3450,-399 3449,-400 3448,-401 3447,-401 3446,-402 3445,-402 3444,-402 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3444,-402 3445,-402 3446,-402 3447,-403 3448,-403 3449,-404 3450,-405 3451,-406 3451,-407 3452,-408 3452,-409 3452,-410 3452,-411 3452,-412 3452,-413 3452,-414 3451,-415 3451,-416 3450,-417 3449,-418 3448,-419 3447,-419 3446,-420 3445,-420 3444,-420 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3518,-384 3519,-384 3520,-384 3521,-385 3522,-385 3523,-386 3524,-387 3525,-388 3525,-389 3526,-390 3526,-391 3526,-392 3526,-393 3526,-394 3526,-395 3526,-396 3525,-397 3525,-398 3524,-399 3523,-400 3522,-401 3521,-401 3520,-402 3519,-402 3518,-402 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3518,-402 3519,-402 3520,-402 3521,-403 3522,-403 3523,-404 3524,-405 3525,-406 3525,-407 3526,-408 3526,-409 3526,-410 3526,-411 3526,-412 3526,-413 3526,-414 3525,-415 3525,-416 3524,-417 3523,-418 3522,-419 3521,-419 3520,-420 3519,-420 3518,-420 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3597,-523 3597,-522 3597,-521 3598,-520 3598,-519 3599,-518 3600,-517 3601,-516 3602,-516 3603,-515 3604,-515 3605,-515 3606,-515 3607,-515 3608,-515 3609,-515 3610,-516 3611,-516 3612,-517 3613,-518 3614,-519 3614,-520 3615,-521 3615,-522 3615,-523 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3579,-523 3579,-522 3579,-521 3580,-520 3580,-519 3581,-518 3582,-517 3583,-516 3584,-516 3585,-515 3586,-515 3587,-515 3588,-515 3589,-515 3590,-515 3591,-515 3592,-516 3593,-516 3594,-517 3595,-518 3596,-519 3596,-520 3597,-521 3597,-522 3597,-523 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1166,-1546 1166,-1545 1166,-1544 1166,-1543 1167,-1542 1168,-1541 1168,-1540 1169,-1539 1170,-1538 1171,-1538 1172,-1537 1173,-1537 1174,-1537 1175,-1537 1176,-1537 1177,-1537 1178,-1537 1179,-1538 1180,-1538 1181,-1539 1182,-1540 1183,-1541 1183,-1542 1184,-1543 1184,-1544 1184,-1545 1184,-1546 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="1184,-1546 1184,-1545 1184,-1544 1184,-1543 1185,-1542 1186,-1541 1186,-1540 1187,-1539 1188,-1538 1189,-1538 1190,-1537 1191,-1537 1192,-1537 1193,-1537 1194,-1537 1195,-1537 1196,-1537 1197,-1538 1198,-1538 1199,-1539 1200,-1540 1201,-1541 1201,-1542 1202,-1543 1202,-1544 1202,-1545 1202,-1546 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2700,-2044 2701,-2044 2702,-2044 2703,-2045 2704,-2045 2705,-2046 2706,-2047 2707,-2048 2707,-2049 2708,-2050 2708,-2051 2708,-2052 2708,-2053 2708,-2054 2708,-2055 2708,-2056 2707,-2057 2707,-2058 2706,-2059 2705,-2060 2704,-2061 2703,-2061 2702,-2062 2701,-2062 2700,-2062 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2700,-2062 2701,-2062 2702,-2062 2703,-2063 2704,-2063 2705,-2064 2706,-2065 2707,-2066 2707,-2067 2708,-2068 2708,-2069 2708,-2070 2708,-2071 2708,-2072 2708,-2073 2708,-2074 2707,-2075 2707,-2076 2706,-2077 2705,-2078 2704,-2079 2703,-2079 2702,-2080 2701,-2080 2700,-2080 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1914,-1399 1914,-1398 1914,-1397 1914,-1396 1915,-1394 1915,-1393 1916,-1393 1917,-1392 1918,-1391 1919,-1390 1920,-1390 1921,-1389 1922,-1389 1923,-1389 1924,-1389 1925,-1389 1926,-1390 1927,-1390 1928,-1391 1929,-1392 1930,-1393 1931,-1393 1931,-1394 1932,-1396 1932,-1397 1932,-1398 1932,-1399 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1932,-1399 1932,-1398 1932,-1397 1932,-1396 1933,-1394 1933,-1393 1934,-1393 1935,-1392 1936,-1391 1937,-1390 1938,-1390 1939,-1389 1940,-1389 1941,-1389 1942,-1389 1943,-1389 1944,-1390 1945,-1390 1946,-1391 1947,-1392 1948,-1393 1949,-1393 1949,-1394 1950,-1396 1950,-1397 1950,-1398 1950,-1399 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3700,-1520 3701,-1520 3702,-1520 3703,-1520 3704,-1521 3705,-1522 3706,-1522 3707,-1523 3708,-1524 3708,-1525 3709,-1526 3709,-1527 3709,-1528 3709,-1529 3709,-1530 3709,-1531 3709,-1532 3708,-1533 3708,-1534 3707,-1535 3706,-1536 3705,-1537 3704,-1537 3703,-1538 3702,-1538 3701,-1538 3700,-1538 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="3700,-1538 3701,-1538 3702,-1538 3703,-1538 3704,-1539 3705,-1540 3706,-1540 3707,-1541 3708,-1542 3708,-1543 3709,-1544 3709,-1545 3709,-1546 3709,-1547 3709,-1548 3709,-1549 3709,-1550 3708,-1551 3708,-1552 3707,-1553 3706,-1554 3705,-1555 3704,-1555 3703,-1556 3702,-1556 3701,-1556 3700,-1556 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="0" fill="none" points="2700,-1695 2701,-1695 2702,-1695 2703,-1696 2704,-1696 2705,-1697 2706,-1698 2707,-1699 2707,-1700 2708,-1701 2708,-1702 2708,-1703 2708,-1704 2708,-1705 2708,-1706 2708,-1707 2707,-1708 2707,-1709 2706,-1710 2705,-1711 2704,-1712 2703,-1712 2702,-1713 2701,-1713 2700,-1713 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="2700,-1713 2701,-1713 2702,-1713 2703,-1714 2704,-1714 2705,-1715 2706,-1716 2707,-1717 2707,-1718 2708,-1719 2708,-1720 2708,-1721 2708,-1722 2708,-1723 2708,-1724 2708,-1725 2707,-1726 2707,-1727 2706,-1728 2705,-1729 2704,-1730 2703,-1730 2702,-1731 2701,-1731 2700,-1731 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1417,-1578 1418,-1578 1419,-1579 1420,-1580 1421,-1580 1421,-1581 1422,-1582 1422,-1583 1423,-1584 1423,-1585 1423,-1587 1423,-1588 1423,-1589 1423,-1590 1422,-1591 1422,-1592 1421,-1593 1420,-1593 1419,-1594 1418,-1595 1417,-1595 1416,-1596 1415,-1596 1414,-1596 1413,-1596 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="1421,-1561 1422,-1561 1423,-1562 1424,-1562 1425,-1563 1426,-1563 1427,-1564 1428,-1565 1428,-1566 1428,-1567 1429,-1568 1429,-1570 1429,-1571 1429,-1572 1428,-1573 1428,-1574 1427,-1575 1426,-1576 1426,-1577 1425,-1577 1424,-1578 1423,-1578 1421,-1578 1420,-1579 1419,-1578 1418,-1578 1417,-1578 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3450,-843 3450,-842 3451,-841 3451,-840 3452,-839 3453,-838 3454,-838 3455,-837 3456,-837 3457,-836 3458,-836 3459,-836 3460,-836 3461,-836 3462,-837 3463,-837 3464,-838 3465,-839 3466,-840 3466,-841 3467,-841 3467,-843 3467,-844 3468,-845 3467,-846 3467,-847 3467,-848 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="0" fill="none" points="3432,-839 3432,-838 3433,-837 3433,-836 3434,-835 3435,-834 3435,-833 3436,-833 3437,-832 3439,-832 3440,-831 3441,-831 3442,-831 3443,-831 3444,-832 3445,-832 3446,-833 3447,-834 3448,-834 3449,-835 3449,-836 3450,-837 3450,-838 3450,-840 3450,-841 3450,-842 3450,-843 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="支线" fill="none" points="3439,-27 3439,-26 3439,-26 3439,-25 3440,-25 3440,-24 3440,-24 3441,-23 3441,-23 3442,-22 3442,-22 3443,-22 3444,-22 3444,-22 3445,-22 3446,-22 3446,-22 3447,-23 3447,-23 3448,-24 3448,-24 3448,-25 3449,-25 3449,-26 3449,-26 3449,-27 " stroke="rgb(255,255,255)" stroke-width="0.01625"/>
   <polyline DF8003:Layer="支线" fill="none" points="3429,-27 3429,-26 3429,-26 3429,-25 3430,-25 3430,-24 3430,-24 3431,-23 3431,-23 3432,-22 3432,-22 3433,-22 3434,-22 3434,-22 3435,-22 3436,-22 3436,-22 3437,-23 3437,-23 3438,-24 3438,-24 3438,-25 3439,-25 3439,-26 3439,-26 3439,-27 " stroke="rgb(255,255,255)" stroke-width="0.01625"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="0" fill="none" points="1637,-2302 1756,-2302 1756,-2181 1637,-2181 1637,-2302 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="3421,-584 3540,-584 3540,-463 3421,-463 3421,-584 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="426,-2211 460,-2211 460,-2189 426,-2189 426,-2211 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="3319,-1040 3352,-1040 3352,-1019 3319,-1019 3319,-1040 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="701,-2199 734,-2199 734,-2178 701,-2178 701,-2199 " stroke="rgb(255,0,0)"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="3605" y1="-149" y2="-150"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="3605" y1="-210" y2="-210"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3854" x2="3605" y1="-114" y2="-114"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3854" x2="3605" y1="-75" y2="-75"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3680" x2="3680" y1="-150" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3605" x2="3605" y1="-210" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3854" x2="3854" y1="-150" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3960" x2="3960" y1="-37" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3605" x2="4205" y1="0" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3458" x2="3426" y1="-56" y2="-56"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3458" x2="3426" y1="-71" y2="-71"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.9" x1="3374" x2="3374" y1="-207" y2="-10"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.9" x1="3598" x2="3598" y1="-207" y2="-10"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="658" x2="638" y1="-2648" y2="-2648"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="638" x2="658" y1="-2678" y2="-2678"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="638" x2="638" y1="-2648" y2="-2678"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="658" x2="658" y1="-2678" y2="-2648"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="638" x2="658" y1="-2704" y2="-2684"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="648" x2="648" y1="-2754" y2="-2678"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="902" x2="902" y1="-2678" y2="-2648"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="882" x2="882" y1="-2648" y2="-2678"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="882" x2="902" y1="-2678" y2="-2678"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="902" x2="882" y1="-2648" y2="-2648"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="892" x2="892" y1="-2754" y2="-2678"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="882" x2="902" y1="-2704" y2="-2684"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1143" x2="1143" y1="-2678" y2="-2648"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1123" x2="1123" y1="-2648" y2="-2678"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1123" x2="1143" y1="-2678" y2="-2678"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1143" x2="1123" y1="-2648" y2="-2648"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1133" x2="1133" y1="-2754" y2="-2678"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1123" x2="1143" y1="-2704" y2="-2684"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1591" x2="1591" y1="-2678" y2="-2648"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1571" x2="1571" y1="-2648" y2="-2678"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1571" x2="1591" y1="-2678" y2="-2678"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1591" x2="1571" y1="-2648" y2="-2648"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1581" x2="1581" y1="-2754" y2="-2678"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1571" x2="1591" y1="-2704" y2="-2684"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1339" x2="1359" y1="-2701" y2="-2681"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1349" x2="1349" y1="-2750" y2="-2674"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1359" x2="1359" y1="-2674" y2="-2644"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1359" x2="1339" y1="-2644" y2="-2644"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1339" x2="1359" y1="-2674" y2="-2674"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1339" x2="1339" y1="-2644" y2="-2674"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="648" x2="648" y1="-2648" y2="-2537"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="972" x2="972" y1="-2754" y2="-2755"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="880" x2="900" y1="-2606" y2="-2586"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="638" x2="658" y1="-2580" y2="-2560"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="638" x2="658" y1="-2624" y2="-2604"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="795" x2="551" y1="-2754" y2="-2754"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="795" x2="1668" y1="-2754" y2="-2754"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="3371" x2="2758" y1="-2742" y2="-2742"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2733" x2="2713" y1="-2631" y2="-2631"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2713" x2="2733" y1="-2661" y2="-2661"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2713" x2="2713" y1="-2631" y2="-2661"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2733" x2="2733" y1="-2661" y2="-2631"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2713" x2="2733" y1="-2687" y2="-2667"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2723" x2="2723" y1="-2737" y2="-2661"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2713" x2="2733" y1="-2563" y2="-2543"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2713" x2="2733" y1="-2607" y2="-2587"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2913" x2="2893" y1="-2636" y2="-2636"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2893" x2="2913" y1="-2666" y2="-2666"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2893" x2="2893" y1="-2636" y2="-2666"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2913" x2="2913" y1="-2666" y2="-2636"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2893" x2="2913" y1="-2692" y2="-2672"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2903" x2="2903" y1="-2742" y2="-2666"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2903" x2="2903" y1="-2636" y2="-2525"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2893" x2="2913" y1="-2569" y2="-2549"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2893" x2="2913" y1="-2612" y2="-2592"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3091" x2="3071" y1="-2636" y2="-2636"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3071" x2="3091" y1="-2666" y2="-2666"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3071" x2="3071" y1="-2636" y2="-2666"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3091" x2="3091" y1="-2666" y2="-2636"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3071" x2="3091" y1="-2692" y2="-2672"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3081" x2="3081" y1="-2742" y2="-2666"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3081" x2="3081" y1="-2636" y2="-2525"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3071" x2="3091" y1="-2569" y2="-2549"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3071" x2="3091" y1="-2612" y2="-2592"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3490" x2="3470" y1="-2636" y2="-2636"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3470" x2="3490" y1="-2666" y2="-2666"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3470" x2="3470" y1="-2636" y2="-2666"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3490" x2="3490" y1="-2666" y2="-2636"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3470" x2="3490" y1="-2692" y2="-2672"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3480" x2="3480" y1="-2742" y2="-2666"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3480" x2="3480" y1="-2636" y2="-2525"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3470" x2="3490" y1="-2569" y2="-2549"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3470" x2="3490" y1="-2612" y2="-2592"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3650" x2="3630" y1="-2636" y2="-2636"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3630" x2="3650" y1="-2666" y2="-2666"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3630" x2="3630" y1="-2636" y2="-2666"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3650" x2="3650" y1="-2666" y2="-2636"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3630" x2="3650" y1="-2692" y2="-2672"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3640" x2="3640" y1="-2742" y2="-2666"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3640" x2="3640" y1="-2636" y2="-2525"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3630" x2="3650" y1="-2569" y2="-2549"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3630" x2="3650" y1="-2612" y2="-2592"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="892" x2="892" y1="-2648" y2="-2537"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1245" x2="1245" y1="-2754" y2="-2755"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1012" x2="1668" y1="-2754" y2="-2754"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1133" x2="1133" y1="-2648" y2="-2537"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="1196" x2="1668" y1="-2754" y2="-2754"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1349" x2="1349" y1="-2644" y2="-2533"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1581" x2="1581" y1="-2648" y2="-2537"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3731" x2="3118" y1="-2742" y2="-2742"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="3723" x2="3111" y1="-2742" y2="-2742"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="3664" x2="3051" y1="-2742" y2="-2742"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2723" x2="2723" y1="-2631" y2="-2537"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="647" x2="648" y1="-1553" y2="-2537"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="596" x2="576" y1="-2096" y2="-2076"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="452" x2="452" y1="-1737" y2="-1534"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.9" x1="452" x2="452" y1="-1962" y2="-1704"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="340" x2="340" y1="-2084" y2="-2084"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="365" x2="648" y1="-2084" y2="-2084"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="449" x2="523" y1="-1894" y2="-1894"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="536" x2="535" y1="-1894" y2="-1894"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="452" x2="452" y1="-1842" y2="-1817"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="452" x2="452" y1="-1834" y2="-1829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="340" x2="340" y1="-1829" y2="-1829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="365" x2="452" y1="-1829" y2="-1829"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="647" x2="647" y1="-983" y2="-1553"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="341" x2="340" y1="-1620" y2="-1620"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="365" x2="452" y1="-1620" y2="-1620"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="452" x2="452" y1="-1696" y2="-1691"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="452" x2="595" y1="-1691" y2="-1691"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="452" x2="452" y1="-1150" y2="-911"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="452" x2="526" y1="-1083" y2="-1083"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="539" x2="538" y1="-1083" y2="-1083"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="450" x2="450" y1="-887" y2="-887"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="892" x2="892" y1="-2537" y2="-1486"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="781" x2="781" y1="-2418" y2="-2418"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="806" x2="892" y1="-2418" y2="-2418"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="781" x2="781" y1="-2331" y2="-2331"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="806" x2="892" y1="-2331" y2="-2331"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="806" x2="892" y1="-2188" y2="-2188"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="854" x2="874" y1="-2198" y2="-2178"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="816" x2="816" y1="-2188" y2="-1894"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="705" x2="704" y1="-2038" y2="-2038"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="729" x2="816" y1="-2038" y2="-2038"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="816" x2="816" y1="-1870" y2="-1869"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="882" x2="902" y1="-1587" y2="-1567"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="840" x2="860" y1="-1759" y2="-1739"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="821" x2="892" y1="-1748" y2="-1748"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="911" x2="911" y1="-1650" y2="-1600"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="874" x2="874" y1="-1600" y2="-1650"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1409" x2="1637" y1="-2242" y2="-2242"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1673" x2="1673" y1="-2138" y2="-1296"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1673" x2="1867" y1="-2034" y2="-2034"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1879" x2="1879" y1="-2034" y2="-2034"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1714" x2="1734" y1="-2044" y2="-2024"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1673" x2="1867" y1="-1926" y2="-1926"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1562" x2="1561" y1="-1926" y2="-1926"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1586" x2="1673" y1="-1926" y2="-1926"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1673" x2="1867" y1="-1795" y2="-1795"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1879" x2="1879" y1="-1795" y2="-1795"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1392" x2="1391" y1="-1795" y2="-1795"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1416" x2="1673" y1="-1795" y2="-1795"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1409" x2="1352" y1="-2242" y2="-2242"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1756" x2="2584" y1="-2242" y2="-2242"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1673" x2="1867" y1="-1632" y2="-1632"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1714" x2="1734" y1="-1642" y2="-1622"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1867" x2="2085" y1="-1632" y2="-1632"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2097" x2="2097" y1="-1632" y2="-1632"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1916" x2="1916" y1="-1521" y2="-1521"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1916" x2="1916" y1="-1533" y2="-1534"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1916" x2="1916" y1="-1546" y2="-1632"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2001" x2="2001" y1="-1631" y2="-1704"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2001" x2="2001" y1="-1717" y2="-1716"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2001" x2="2001" y1="-1729" y2="-1729"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1560" x2="1560" y1="-1369" y2="-1369"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1585" x2="1672" y1="-1369" y2="-1369"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1673" x2="1722" y1="-1398" y2="-1398"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1722" x2="1777" y1="-1398" y2="-1398"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1673" x2="1673" y1="-1271" y2="-1271"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1673" x2="1673" y1="-1283" y2="-1284"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1777" x2="1994" y1="-1398" y2="-1398"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2006" x2="2006" y1="-1398" y2="-1398"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1886" x2="1886" y1="-1288" y2="-1287"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1886" x2="1886" y1="-1300" y2="-1300"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1886" x2="1886" y1="-1312" y2="-1398"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1817" x2="1837" y1="-2252" y2="-2232"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2319" x2="2319" y1="-2242" y2="-1357"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2309" x2="2329" y1="-2207" y2="-2187"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1352" x2="1349" y1="-2242" y2="-2242"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2232" x2="2233" y1="-2032" y2="-2032"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2220" x2="2220" y1="-2032" y2="-2032"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1673" x2="1867" y1="-1926" y2="-1926"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1753" x2="1946" y1="-1926" y2="-1926"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1958" x2="1958" y1="-1926" y2="-1926"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2319" x2="2512" y1="-2138" y2="-2138"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2525" x2="2525" y1="-2138" y2="-2138"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2319" x2="2512" y1="-1942" y2="-1942"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2525" x2="2525" y1="-1942" y2="-1942"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2245" x2="2319" y1="-2032" y2="-2032"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2319" x2="2512" y1="-1679" y2="-1679"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2525" x2="2525" y1="-1679" y2="-1679"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2232" x2="2233" y1="-1838" y2="-1838"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2245" x2="2319" y1="-1838" y2="-1838"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2220" x2="2220" y1="-1838" y2="-1838"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2232" x2="2233" y1="-1516" y2="-1516"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2245" x2="2319" y1="-1516" y2="-1516"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2220" x2="2220" y1="-1516" y2="-1516"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2319" x2="2416" y1="-1479" y2="-1479"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2471" x2="2471" y1="-1479" y2="-1479"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2319" x2="2319" y1="-1332" y2="-1332"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2319" x2="2319" y1="-1344" y2="-1344"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2416" x2="2458" y1="-1479" y2="-1479"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2574" x2="2594" y1="-2308" y2="-2288"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2699" x2="2699" y1="-2242" y2="-1566"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2611" x2="2611" y1="-2195" y2="-2195"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2623" x2="2697" y1="-2195" y2="-2195"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2598" x2="2598" y1="-2195" y2="-2195"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2616" x2="2616" y1="-1566" y2="-1566"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2628" x2="2699" y1="-1566" y2="-1566"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2604" x2="2603" y1="-1566" y2="-1566"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2612" x2="2612" y1="-1832" y2="-1832"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2624" x2="2698" y1="-1832" y2="-1832"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2599" x2="2599" y1="-1832" y2="-1832"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2909" x2="2909" y1="-2242" y2="-1563"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2822" x2="2822" y1="-2004" y2="-2004"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2835" x2="2909" y1="-2004" y2="-2004"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2810" x2="2810" y1="-2004" y2="-2004"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2822" x2="2822" y1="-1566" y2="-1566"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2835" x2="2909" y1="-1566" y2="-1566"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2810" x2="2810" y1="-1566" y2="-1566"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2822" x2="2822" y1="-1832" y2="-1832"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2835" x2="2909" y1="-1832" y2="-1832"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2810" x2="2810" y1="-1832" y2="-1832"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1349" x2="1349" y1="-2242" y2="-2533"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2903" x2="2903" y1="-2360" y2="-2525"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2584" x2="2584" y1="-2242" y2="-2360"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1581" x2="1581" y1="-2537" y2="-2442"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2723" x2="2723" y1="-2537" y2="-2441"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3640" x2="3640" y1="-2525" y2="-1215"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3640" x2="3833" y1="-2308" y2="-2308"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3845" x2="3845" y1="-2308" y2="-2308"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3640" x2="3640" y1="-1187" y2="-1187"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3640" x2="3640" y1="-1199" y2="-1199"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3640" x2="3707" y1="-1981" y2="-1981"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3707" x2="3848" y1="-1981" y2="-1981"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3861" x2="3860" y1="-1981" y2="-1981"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3707" x2="3707" y1="-1981" y2="-1784"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3707" x2="3848" y1="-1784" y2="-1784"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3861" x2="3860" y1="-1784" y2="-1784"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3661" x2="3681" y1="-1991" y2="-1971"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3644" x2="3711" y1="-1569" y2="-1569"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3711" x2="3755" y1="-1569" y2="-1569"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3767" x2="3767" y1="-1569" y2="-1569"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3699" x2="3742" y1="-1400" y2="-1400"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3754" x2="3754" y1="-1400" y2="-1400"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3480" x2="3480" y1="-2525" y2="-584"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3437" x2="3437" y1="-2392" y2="-2344"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3437" x2="3369" y1="-2344" y2="-2344"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3437" x2="3437" y1="-2392" y2="-2435"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3437" x2="3386" y1="-2435" y2="-2435"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3437" x2="3480" y1="-2392" y2="-2392"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3357" x2="3357" y1="-2344" y2="-2344"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3345" x2="3344" y1="-2344" y2="-2344"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3373" x2="3374" y1="-2435" y2="-2435"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3361" x2="3361" y1="-2435" y2="-2435"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3437" x2="3480" y1="-2232" y2="-2232"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3417" x2="3417" y1="-2232" y2="-1956"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3417" x2="3417" y1="-1956" y2="-1811"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3437" x2="3417" y1="-2232" y2="-2232"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3330" x2="3331" y1="-2131" y2="-2131"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3343" x2="3417" y1="-2131" y2="-2131"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3318" x2="3318" y1="-2131" y2="-2131"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3330" x2="3331" y1="-2026" y2="-2026"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3343" x2="3417" y1="-2026" y2="-2026"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3318" x2="3318" y1="-2026" y2="-2026"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3330" x2="3331" y1="-1910" y2="-1910"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3343" x2="3417" y1="-1910" y2="-1910"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3318" x2="3318" y1="-1910" y2="-1910"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3330" x2="3331" y1="-1811" y2="-1811"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3343" x2="3417" y1="-1811" y2="-1811"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3318" x2="3318" y1="-1811" y2="-1811"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3394" x2="3394" y1="-1693" y2="-1693"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3406" x2="3480" y1="-1693" y2="-1693"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3382" x2="3381" y1="-1693" y2="-1693"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3437" x2="3480" y1="-1553" y2="-1553"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3417" x2="3417" y1="-1553" y2="-1276"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3417" x2="3417" y1="-1276" y2="-1132"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3437" x2="3417" y1="-1553" y2="-1553"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3330" x2="3331" y1="-1451" y2="-1451"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3343" x2="3417" y1="-1451" y2="-1451"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3318" x2="3318" y1="-1451" y2="-1451"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3240" x2="3241" y1="-1343" y2="-1343"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3253" x2="3417" y1="-1343" y2="-1343"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3228" x2="3228" y1="-1343" y2="-1343"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3330" x2="3331" y1="-1282" y2="-1282"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3343" x2="3417" y1="-1282" y2="-1282"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3318" x2="3318" y1="-1282" y2="-1282"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3330" x2="3331" y1="-1182" y2="-1182"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3343" x2="3417" y1="-1182" y2="-1182"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3318" x2="3318" y1="-1182" y2="-1182"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3203" x2="3203" y1="-1028" y2="-1028"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3215" x2="3319" y1="-1028" y2="-1028"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3191" x2="3190" y1="-1028" y2="-1028"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3417" x2="3417" y1="-1132" y2="-943"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3470" x2="3490" y1="-947" y2="-927"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3327" x2="3327" y1="-841" y2="-841"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3340" x2="3378" y1="-841" y2="-841"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3315" x2="3315" y1="-841" y2="-841"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3303" x2="3304" y1="-708" y2="-708"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3316" x2="3480" y1="-708" y2="-708"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3291" x2="3291" y1="-708" y2="-708"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3480" x2="3547" y1="-662" y2="-662"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3547" x2="3591" y1="-662" y2="-662"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3604" x2="3604" y1="-662" y2="-662"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3640" x2="3640" y1="-1215" y2="-1212"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3480" x2="3674" y1="-770" y2="-770"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3686" x2="3686" y1="-770" y2="-770"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3444" x2="3444" y1="-463" y2="-326"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3320" x2="3107" y1="-349" y2="-349"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3518" x2="3518" y1="-463" y2="-326"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3540" x2="3669" y1="-523" y2="-523"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3681" x2="3681" y1="-523" y2="-523"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3518" x2="3518" y1="-329" y2="-326"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3518" x2="3518" y1="-301" y2="-301"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3518" x2="3518" y1="-313" y2="-314"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3444" x2="3444" y1="-329" y2="-326"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3444" x2="3444" y1="-301" y2="-301"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3444" x2="3444" y1="-313" y2="-314"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2776" x2="2208" y1="-349" y2="-349"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="2758" x2="2598" y1="-2742" y2="-2742"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2723" x2="1581" y1="-2441" y2="-2442"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2903" x2="2584" y1="-2360" y2="-2360"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2584" x2="2909" y1="-2242" y2="-2242"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3112" x2="3112" y1="-349" y2="-302"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3112" x2="2689" y1="-275" y2="-275"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3112" x2="3112" y1="-275" y2="-302"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3068" x2="3068" y1="-165" y2="-164"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3068" x2="3068" y1="-177" y2="-177"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3068" x2="3068" y1="-189" y2="-275"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2868" x2="2868" y1="-165" y2="-164"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2868" x2="2868" y1="-177" y2="-177"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2868" x2="2868" y1="-189" y2="-275"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2691" x2="2691" y1="-165" y2="-164"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2691" x2="2691" y1="-177" y2="-177"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2691" x2="2691" y1="-189" y2="-275"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2429" x2="2429" y1="-238" y2="-238"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2429" x2="2429" y1="-250" y2="-251"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2429" x2="2429" y1="-263" y2="-349"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2208" x2="2208" y1="-238" y2="-238"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2208" x2="2208" y1="-250" y2="-251"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2208" x2="2208" y1="-263" y2="-349"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1500" x2="3081" y1="-846" y2="-846"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1133" x2="1133" y1="-2537" y2="-2412"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="3081" x2="3081" y1="-2525" y2="-846"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3081" x2="3148" y1="-2246" y2="-2246"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3148" x2="3192" y1="-2246" y2="-2246"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3205" x2="3205" y1="-2246" y2="-2246"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3081" x2="3148" y1="-1742" y2="-1742"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3148" x2="3192" y1="-1742" y2="-1742"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3205" x2="3205" y1="-1742" y2="-1742"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3081" x2="3148" y1="-1547" y2="-1547"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3148" x2="3192" y1="-1547" y2="-1547"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3205" x2="3205" y1="-1547" y2="-1547"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2794" x2="2794" y1="-1426" y2="-1425"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2794" x2="2794" y1="-1438" y2="-1438"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2794" x2="2974" y1="-925" y2="-925"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2987" x2="2986" y1="-925" y2="-925"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2617" x2="2618" y1="-956" y2="-956"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2630" x2="2794" y1="-956" y2="-956"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2605" x2="2605" y1="-956" y2="-956"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2794" x2="2865" y1="-1025" y2="-1025"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2992" x2="2991" y1="-1025" y2="-1025"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2946" x2="2946" y1="-1181" y2="-1181"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2703" x2="2703" y1="-1146" y2="-1146"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2716" x2="2794" y1="-1146" y2="-1146"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2691" x2="2691" y1="-1146" y2="-1146"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3378" x2="3480" y1="-841" y2="-841"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2617" x2="2618" y1="-1242" y2="-1242"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2630" x2="2794" y1="-1242" y2="-1242"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2605" x2="2605" y1="-1242" y2="-1242"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2794" x2="2862" y1="-1293" y2="-1293"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2874" x2="2874" y1="-1293" y2="-1293"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2566" x2="2586" y1="-856" y2="-836"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2415" x2="2415" y1="-846" y2="-853"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2415" x2="2415" y1="-853" y2="-937"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2415" x2="2415" y1="-950" y2="-949"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2415" x2="2415" y1="-962" y2="-962"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2236" x2="2236" y1="-699" y2="-699"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2236" x2="2236" y1="-711" y2="-712"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2236" x2="2236" y1="-724" y2="-846"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2004" x2="2004" y1="-699" y2="-699"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2004" x2="2004" y1="-711" y2="-712"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2004" x2="2004" y1="-724" y2="-846"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1766" x2="1766" y1="-736" y2="-735"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1766" x2="1766" y1="-748" y2="-748"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1766" x2="1766" y1="-760" y2="-846"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2794" x2="2794" y1="-1413" y2="-845"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1636" x2="1636" y1="-846" y2="-461"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1500" x2="1500" y1="-606" y2="-606"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1500" x2="1500" y1="-618" y2="-619"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1500" x2="1500" y1="-631" y2="-846"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1864" x2="2794" y1="-461" y2="-461"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1143" x2="1143" y1="-2412" y2="-2382"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1123" x2="1123" y1="-2382" y2="-2412"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1123" x2="1143" y1="-2412" y2="-2412"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1143" x2="1123" y1="-2382" y2="-2382"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1133" x2="1132" y1="-2382" y2="-1142"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1133" x2="1200" y1="-2184" y2="-2184"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1200" x2="1244" y1="-2184" y2="-2184"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1256" x2="1256" y1="-2184" y2="-2184"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1133" x2="1200" y1="-1972" y2="-1972"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1200" x2="1244" y1="-1972" y2="-1972"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1256" x2="1256" y1="-1972" y2="-1972"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1133" x2="1200" y1="-1546" y2="-1546"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1200" x2="1478" y1="-1546" y2="-1546"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1551" x2="1551" y1="-1544" y2="-1544"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1291" x2="1291" y1="-1289" y2="-1289"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1291" x2="1291" y1="-1301" y2="-1301"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1291" x2="1291" y1="-1397" y2="-1546"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1636" x2="1864" y1="-461" y2="-461"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1132" x2="1132" y1="-1142" y2="-508"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="980" x2="1011" y1="-1215" y2="-1215"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1063" x2="1083" y1="-1225" y2="-1205"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1011" x2="1133" y1="-1215" y2="-1215"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="980" x2="906" y1="-1215" y2="-1215"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="906" x2="906" y1="-1215" y2="-1157"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="906" x2="906" y1="-1273" y2="-1215"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="906" x2="906" y1="-1132" y2="-1132"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="906" x2="906" y1="-1144" y2="-1145"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="906" x2="906" y1="-1285" y2="-1285"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="906" x2="906" y1="-1297" y2="-1298"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1107" x2="1107" y1="-388" y2="-438"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1157" x2="1107" y1="-388" y2="-388"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1157" x2="1157" y1="-438" y2="-388"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.9" x1="1107" x2="1157" y1="-438" y2="-438"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1133" x2="1200" y1="-653" y2="-653"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1200" x2="1244" y1="-653" y2="-653"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1256" x2="1256" y1="-653" y2="-653"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1143" x2="1143" y1="-508" y2="-478"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1123" x2="1123" y1="-478" y2="-508"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1123" x2="1143" y1="-508" y2="-508"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1143" x2="1123" y1="-478" y2="-478"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1123" x2="1143" y1="-535" y2="-515"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1132" x2="1132" y1="-478" y2="-438"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1132" x2="1132" y1="-388" y2="-358"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1142" x2="1142" y1="-336" y2="-306"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1122" x2="1122" y1="-306" y2="-336"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1122" x2="1142" y1="-336" y2="-336"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1142" x2="1122" y1="-306" y2="-306"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1132" x2="1132" y1="-306" y2="-266"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1132" x2="1132" y1="-266" y2="-181"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1123" x2="1143" y1="-285" y2="-265"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1157" x2="1284" y1="-413" y2="-413"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1297" x2="1296" y1="-413" y2="-413"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1132" x2="1142" y1="-181" y2="-197"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1123" x2="1132" y1="-197" y2="-181"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1132" x2="1132" y1="-336" y2="-358"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="647" x2="647" y1="-983" y2="-644"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="647" x2="402" y1="-519" y2="-519"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="4" x1="402" x2="921" y1="-519" y2="-519"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="658" x2="638" y1="-614" y2="-614"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="638" x2="658" y1="-644" y2="-644"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="638" x2="638" y1="-614" y2="-644"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="658" x2="658" y1="-644" y2="-614"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="638" x2="658" y1="-576" y2="-556"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="638" x2="658" y1="-697" y2="-677"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="638" x2="658" y1="-740" y2="-720"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="647" x2="647" y1="-614" y2="-519"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="892" x2="902" y1="-1486" y2="-1502"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="883" x2="892" y1="-1502" y2="-1486"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="839" x2="859" y1="-2428" y2="-2408"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2979" x2="2865" y1="-1025" y2="-1025"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1291" x2="1377" y1="-1397" y2="-1397"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1291" x2="1291" y1="-1301" y2="-1301"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1390" x2="1389" y1="-1397" y2="-1397"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1291" x2="1291" y1="-1314" y2="-1397"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2794" x2="2921" y1="-1384" y2="-1384"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2934" x2="2933" y1="-1384" y2="-1384"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3122" x2="3142" y1="-2256" y2="-2236"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="674" x2="674" y1="-2188" y2="-2188"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="699" x2="701" y1="-2188" y2="-2188"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1418" x2="1418" y1="-1545" y2="-1619"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1418" x2="1418" y1="-1632" y2="-1631"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1418" x2="1418" y1="-1644" y2="-1644"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1797" x2="1797" y1="-1521" y2="-1520"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1797" x2="1797" y1="-1533" y2="-1533"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1797" x2="1797" y1="-1545" y2="-1631"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3643" x2="3784" y1="-1695" y2="-1695"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3797" x2="3796" y1="-1695" y2="-1695"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3699" x2="3699" y1="-1569" y2="-1400"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3332" x2="3333" y1="-943" y2="-943"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3345" x2="3417" y1="-943" y2="-943"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3320" x2="3320" y1="-943" y2="-943"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3356" x2="3376" y1="-534" y2="-514"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="442" x2="462" y1="-1012" y2="-992"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="452" x2="648" y1="-1962" y2="-1962"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="599" x2="579" y1="-1971" y2="-1951"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="452" x2="647" y1="-1150" y2="-1150"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="3605" y1="-37" y2="-37"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="574" x2="594" y1="-1161" y2="-1141"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="340" x2="340" y1="-1046" y2="-1046"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="365" x2="451" y1="-1046" y2="-1046"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="509" x2="509" y1="-1691" y2="-1737"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="509" x2="509" y1="-1749" y2="-1749"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="509" x2="509" y1="-1762" y2="-1762"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="441" x2="461" y1="-1674" y2="-1654"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="521" x2="541" y1="-1701" y2="-1681"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3417" x2="3320" y1="-522" y2="-522"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3320" x2="3320" y1="-522" y2="-476"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3320" x2="3320" y1="-476" y2="-349"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1132" x2="2794" y1="-1100" y2="-1100"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1149" x2="1169" y1="-1108" y2="-1088"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="2794" x2="2794" y1="-845" y2="-461"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3437" x2="3457" y1="-718" y2="-698"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1673" x2="1673" y1="-2183" y2="-2079"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2877" x2="2897" y1="-2013" y2="-1993"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3366" x2="3386" y1="-1039" y2="-1019"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="490" x2="534" y1="-1975" y2="-1975"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="490" x2="534" y1="-1949" y2="-1949"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1664" x2="1684" y1="-2168" y2="-2148"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1549" x2="1569" y1="-2252" y2="-2232"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="3" x1="1127" x2="1147" y1="-2483" y2="-2463"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="584" x2="584" y1="-1383" y2="-1436"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="584" x2="584" y1="-1450" y2="-1449"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="584" x2="584" y1="-1462" y2="-1462"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="613" x2="633" y1="-1393" y2="-1373"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="647" x2="398" y1="-1382" y2="-1382"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="622" x2="602" y1="-2210" y2="-2190"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="484" x2="484" y1="-2200" y2="-2283"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="481" x2="481" y1="-2306" y2="-2307"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="511" x2="555" y1="-1396" y2="-1396"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="511" x2="555" y1="-1370" y2="-1370"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="648" x2="460" y1="-2200" y2="-2200"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="425" x2="304" y1="-2200" y2="-2200"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="338" x2="382" y1="-2212" y2="-2212"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="338" x2="382" y1="-2186" y2="-2186"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1480" x2="1538" y1="-1545" y2="-1545"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2787" x2="2807" y1="-884" y2="-864"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2836" x2="2856" y1="-1396" y2="-1376"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2677" x2="2677" y1="-705" y2="-705"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3427" x2="3447" y1="-2384" y2="-2364"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="780" x2="800" y1="-2197" y2="-2177"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2676" x2="2676" y1="-844" y2="-717"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2667" x2="2687" y1="-838" y2="-818"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1169" x2="1291" y1="-902" y2="-902"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1322" x2="1339" y1="-902" y2="-902"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1294" x2="1301" y1="-874" y2="-881"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1316" x2="1322" y1="-895" y2="-902"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1291" x2="1291" y1="-907" y2="-897"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1154" x2="1132" y1="-902" y2="-902"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1168" x2="1289" y1="-768" y2="-768"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1321" x2="1337" y1="-768" y2="-768"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1293" x2="1300" y1="-740" y2="-748"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1314" x2="1321" y1="-762" y2="-768"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1289" x2="1289" y1="-774" y2="-763"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1153" x2="1131" y1="-768" y2="-768"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2858" x2="2898" y1="-1181" y2="-1181"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2830" x2="2837" y1="-1153" y2="-1160"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2851" x2="2858" y1="-1174" y2="-1181"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2826" x2="2826" y1="-1186" y2="-1176"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2898" x2="2933" y1="-1181" y2="-1181"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="2794" x2="2826" y1="-1181" y2="-1181"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3090" x2="3107" y1="-349" y2="-349"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3062" x2="3069" y1="-321" y2="-328"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3083" x2="3090" y1="-342" y2="-349"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3059" x2="3059" y1="-354" y2="-344"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3059" x2="2776" y1="-349" y2="-349"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3352" x2="3417" y1="-1028" y2="-1028"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3201" x2="3201" y1="-1099" y2="-1099"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3213" x2="3253" y1="-1099" y2="-1099"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3189" x2="3188" y1="-1099" y2="-1099"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3253" x2="3253" y1="-1099" y2="-1028"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3480" x2="3711" y1="-999" y2="-999"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3724" x2="3724" y1="-998" y2="-998"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3520" x2="3540" y1="-1008" y2="-988"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1169" x2="1291" y1="-1027" y2="-1027"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1322" x2="1339" y1="-1027" y2="-1027"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1294" x2="1301" y1="-999" y2="-1006"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1316" x2="1322" y1="-1020" y2="-1027"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1291" x2="1291" y1="-1032" y2="-1022"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="1154" x2="1132" y1="-1027" y2="-1027"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="596" x2="576" y1="-2442" y2="-2422"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="340" x2="340" y1="-2431" y2="-2431"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="365" x2="648" y1="-2431" y2="-2431"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="740" x2="760" y1="-2198" y2="-2178"/>
   <line DF8003:Layer="0" stroke="rgb(255,0,0)" stroke-width="0.5" x1="734" x2="805" y1="-2188" y2="-2188"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3394" x2="3488" y1="-27" y2="-27"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3463" x2="3488" y1="-118" y2="-118"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3433" x2="3463" y1="-128" y2="-128"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3463" x2="3433" y1="-108" y2="-108"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3433" x2="3433" y1="-108" y2="-128"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3463" x2="3463" y1="-128" y2="-108"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3397" x2="3433" y1="-118" y2="-118"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3433" x2="3453" y1="-144" y2="-164"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3397" x2="3488" y1="-154" y2="-154"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3374" x2="3598" y1="-10" y2="-10"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3598" x2="3374" y1="-207" y2="-207"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="3397" x2="3488" y1="-64" y2="-64"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="0" x2="4205" y1="-2970" y2="-2970"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="4205" y1="-2970" y2="0"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4205" x2="0" y1="0" y2="0"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="0" x2="0" y1="0" y2="-2970"/>
  </g><g id="Circle_Layer">
   <circle DF8003:Layer="0" cx="512" cy="-1949" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="512" cy="-1975" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="647" cy="-2200" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="340" cy="-2084" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="352" cy="-2084" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="451" cy="-1894" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="535" cy="-1894" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="547" cy="-1894" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="451" cy="-1829" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="340" cy="-1829" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="352" cy="-1829" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="451" cy="-1620" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="340" cy="-1619" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="352" cy="-1619" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="451" cy="-1690" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="606" cy="-1693" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="618" cy="-1693" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="451" cy="-1082" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="538" cy="-1082" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="550" cy="-1082" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="450" cy="-886" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="450" cy="-886" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="450" cy="-886" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="450" cy="-886" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="450" cy="-899" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="647" cy="-1382" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="892" cy="-2418" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="781" cy="-2418" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="793" cy="-2418" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="892" cy="-2331" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="781" cy="-2331" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="793" cy="-2331" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="892" cy="-2188" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="815" cy="-2037" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="704" cy="-2037" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="716" cy="-2037" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="815" cy="-1869" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="815" cy="-1869" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="815" cy="-1869" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="815" cy="-1869" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="815" cy="-1881" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="892" cy="-1747" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="911" cy="-1625" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="873" cy="-1625" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1673" cy="-2033" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1879" cy="-2033" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1891" cy="-2033" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1673" cy="-1926" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1561" cy="-1926" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1573" cy="-1926" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1673" cy="-1795" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1879" cy="-1795" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1891" cy="-1795" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1391" cy="-1795" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1403" cy="-1795" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1673" cy="-1631" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2097" cy="-1631" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2109" cy="-1631" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1915" cy="-1521" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1915" cy="-1521" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1915" cy="-1521" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1915" cy="-1521" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1915" cy="-1533" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1915" cy="-1631" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2001" cy="-1630" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2001" cy="-1716" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2001" cy="-1716" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2001" cy="-1716" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2001" cy="-1716" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2001" cy="-1728" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1560" cy="-1369" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1572" cy="-1369" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1673" cy="-1398" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1673" cy="-1271" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1673" cy="-1271" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1673" cy="-1271" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1673" cy="-1271" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1673" cy="-1283" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2006" cy="-1398" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2018" cy="-1398" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1885" cy="-1287" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1885" cy="-1287" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1885" cy="-1287" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1885" cy="-1287" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1885" cy="-1299" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1885" cy="-1398" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1636" cy="-2241" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1756" cy="-2241" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-2241" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-2031" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2220" cy="-2031" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2232" cy="-2031" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-2138" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1958" cy="-1926" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1970" cy="-1926" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2524" cy="-2138" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2537" cy="-2138" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-1942" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2524" cy="-1942" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2537" cy="-1942" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-1679" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2524" cy="-1679" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2537" cy="-1679" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-1837" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2220" cy="-1837" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2232" cy="-1837" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-1515" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2220" cy="-1515" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2232" cy="-1515" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-1479" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2470" cy="-1479" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2483" cy="-1479" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-1331" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-1331" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-1331" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-1331" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2319" cy="-1344" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2583" cy="-2241" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2699" cy="-2196" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2700" cy="-2079" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2598" cy="-2195" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2610" cy="-2195" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2699" cy="-1566" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2603" cy="-1565" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2615" cy="-1565" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2698" cy="-1832" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2599" cy="-1832" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2611" cy="-1832" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2908" cy="-2241" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2908" cy="-2003" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2809" cy="-2003" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2822" cy="-2003" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2908" cy="-1565" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2809" cy="-1565" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2822" cy="-1565" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2908" cy="-1832" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2809" cy="-1832" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2822" cy="-1832" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2583" cy="-2359" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2903" cy="-2359" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3639" cy="-2308" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3845" cy="-2308" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3857" cy="-2308" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3639" cy="-1186" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3639" cy="-1186" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3639" cy="-1186" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3639" cy="-1186" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3639" cy="-1199" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3639" cy="-1980" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3860" cy="-1980" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3872" cy="-1980" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3860" cy="-1783" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3872" cy="-1783" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3643" cy="-1569" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3767" cy="-1569" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3779" cy="-1569" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3754" cy="-1400" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3766" cy="-1400" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3480" cy="-583" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3480" cy="-2391" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3344" cy="-2343" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3356" cy="-2343" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3361" cy="-2434" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3373" cy="-2434" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3480" cy="-2232" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3417" cy="-2232" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3417" cy="-2130" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3417" cy="-2025" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3417" cy="-1910" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3417" cy="-1811" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3318" cy="-2130" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3330" cy="-2130" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3318" cy="-2025" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3330" cy="-2025" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3318" cy="-1910" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3330" cy="-1910" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3318" cy="-1811" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3330" cy="-1811" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3480" cy="-1693" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3381" cy="-1693" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3393" cy="-1693" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3480" cy="-1552" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3418" cy="-1525" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3417" cy="-1451" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3417" cy="-1281" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3417" cy="-1182" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3318" cy="-1451" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3330" cy="-1451" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3228" cy="-1343" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3240" cy="-1343" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3318" cy="-1281" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3330" cy="-1281" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3318" cy="-1182" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3330" cy="-1182" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3417" cy="-1028" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3190" cy="-1028" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3202" cy="-1028" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3314" cy="-840" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3327" cy="-840" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3480" cy="-840" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3291" cy="-707" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3303" cy="-707" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3480" cy="-707" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3480" cy="-662" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3603" cy="-662" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3616" cy="-662" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3686" cy="-769" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3698" cy="-769" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3480" cy="-769" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3681" cy="-523" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3693" cy="-523" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3517" cy="-301" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3517" cy="-301" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3517" cy="-301" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3517" cy="-301" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3517" cy="-313" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3444" cy="-301" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3444" cy="-301" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3444" cy="-301" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3444" cy="-301" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3444" cy="-313" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3420" cy="-523" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3444" cy="-462" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3517" cy="-462" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2429" cy="-348" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3112" cy="-348" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3068" cy="-275" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2868" cy="-275" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2691" cy="-275" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2207" cy="-348" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3068" cy="-164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3068" cy="-164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3068" cy="-164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3068" cy="-164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3068" cy="-176" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2868" cy="-164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2868" cy="-164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2868" cy="-164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2868" cy="-164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2868" cy="-176" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2691" cy="-164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2691" cy="-164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2691" cy="-164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2691" cy="-164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2691" cy="-176" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2429" cy="-238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2429" cy="-238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2429" cy="-238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2429" cy="-238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2429" cy="-250" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2207" cy="-238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2207" cy="-238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2207" cy="-238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2207" cy="-238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2207" cy="-250" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3081" cy="-2245" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3204" cy="-2245" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3217" cy="-2245" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3081" cy="-1742" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3204" cy="-1742" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3217" cy="-1742" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3081" cy="-1546" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3204" cy="-1546" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3217" cy="-1546" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2794" cy="-1425" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2794" cy="-1425" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2794" cy="-1425" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2794" cy="-1425" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2794" cy="-1437" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2793" cy="-924" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2986" cy="-924" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2998" cy="-924" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2605" cy="-956" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2617" cy="-956" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2793" cy="-955" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2991" cy="-1025" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3003" cy="-1025" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2794" cy="-1180" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2945" cy="-1180" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2958" cy="-1180" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2690" cy="-1145" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2703" cy="-1145" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2794" cy="-1025" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2794" cy="-1241" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2605" cy="-1241" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2617" cy="-1241" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2793" cy="-1293" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2874" cy="-1292" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2886" cy="-1292" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2793" cy="-846" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2414" cy="-847" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2415" cy="-949" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2415" cy="-949" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2415" cy="-949" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2415" cy="-949" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2415" cy="-961" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2235" cy="-846" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2235" cy="-699" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2235" cy="-699" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2235" cy="-699" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2235" cy="-699" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2235" cy="-711" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2003" cy="-846" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2003" cy="-699" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2003" cy="-699" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2003" cy="-699" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2003" cy="-699" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2003" cy="-711" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1765" cy="-735" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1765" cy="-735" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1765" cy="-735" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1765" cy="-735" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1765" cy="-747" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1765" cy="-846" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1636" cy="-846" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1500" cy="-846" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1500" cy="-606" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1500" cy="-606" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1500" cy="-606" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1500" cy="-606" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1500" cy="-618" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1132" cy="-2183" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1256" cy="-2183" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1268" cy="-2183" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1132" cy="-1972" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1256" cy="-1972" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1268" cy="-1972" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1132" cy="-1545" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1551" cy="-1544" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1563" cy="-1544" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1291" cy="-1545" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1291" cy="-1288" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1291" cy="-1288" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1291" cy="-1288" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1291" cy="-1288" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1291" cy="-1301" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1132" cy="-1214" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="906" cy="-1132" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="906" cy="-1132" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="906" cy="-1132" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="906" cy="-1132" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="906" cy="-1144" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="906" cy="-1285" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="906" cy="-1285" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="906" cy="-1285" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="906" cy="-1285" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="906" cy="-1297" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1132" cy="-1141" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1132" cy="-652" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1256" cy="-652" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1268" cy="-652" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1296" cy="-413" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1308" cy="-413" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1389" cy="-1397" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1401" cy="-1397" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2933" cy="-1384" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2945" cy="-1384" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="674" cy="-2187" fill="none" r="12.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="686" cy="-2187" fill="none" r="12.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1417" cy="-1631" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1417" cy="-1631" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1417" cy="-1631" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1417" cy="-1631" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1417" cy="-1643" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1417" cy="-1546" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1797" cy="-1520" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1797" cy="-1520" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1797" cy="-1520" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1797" cy="-1520" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1797" cy="-1532" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1797" cy="-1631" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1673" cy="-1369" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3796" cy="-1694" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3808" cy="-1694" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3644" cy="-1694" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3699" cy="-1570" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3320" cy="-943" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3332" cy="-943" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3417" cy="-943" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3706" cy="-1980" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3417" cy="-1343" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="340" cy="-1046" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="352" cy="-1046" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="451" cy="-1046" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="508" cy="-1749" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="508" cy="-1749" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="508" cy="-1749" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="508" cy="-1749" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="508" cy="-1761" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="508" cy="-1691" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="450" cy="-1509" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="450" cy="-1521" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2793" cy="-1145" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2794" cy="-1099" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2793" cy="-1383" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2793" cy="-1115" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="1636" cy="-2241" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="583" cy="-1449" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="583" cy="-1449" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="583" cy="-1449" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="583" cy="-1449" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="583" cy="-1461" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="480" cy="-2306" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="481" cy="-2294" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="533" cy="-1369" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="533" cy="-1395" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="360" cy="-2186" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="360" cy="-2212" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2676" cy="-704" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="2676" cy="-692" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="819" cy="-1746" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3188" cy="-1099" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3200" cy="-1099" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3723" cy="-997" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="3736" cy="-997" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="340" cy="-2430" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="352" cy="-2430" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3442" cy="-56" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3442" cy="-56" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3442" cy="-71" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="3442" cy="-71" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 602.000000 -2733.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 602.000000 -2733.000000) translate(0,45)">一</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 602.000000 -2733.000000) translate(0,70)">三</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 602.000000 -2733.000000) translate(0,95)">联</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 602.000000 -2733.000000) translate(0,120)">网</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 602.000000 -2733.000000) translate(0,145)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 665.000000 -2711.000000) translate(0,20)">0651</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 812.000000 -2727.000000) translate(0,20)">10kV中三树线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 909.000000 -2706.000000) translate(0,20)">0641</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1052.000000 -2727.000000) translate(0,20)">10kV煤矿线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1143.000000 -2713.000000) translate(0,20)">0631</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1501.000000 -2727.000000) translate(0,20)">10kV上网线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1596.000000 -2708.000000) translate(0,20)">0621</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1359.000000 -2707.000000) translate(0,20)">0611</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1268.000000 -2724.000000) translate(0,20)">10kV集镇线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 904.000000 -2868.000000) translate(0,40)">五街河一级电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3102.000000 -2656.000000) translate(0,20)">082</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -2859.000000) translate(0,40)">树苴变电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.000000 -2675.000000) translate(0,20)">065</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 663.000000 -2628.000000) translate(0,20)">0653</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 666.000000 -2581.000000) translate(0,20)">0656</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 909.000000 -2673.000000) translate(0,20)">064</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 905.000000 -2613.000000) translate(0,20)">0646</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1149.000000 -2677.000000) translate(0,20)">063</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1367.000000 -2671.000000) translate(0,20)">061</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1597.000000 -2675.000000) translate(0,20)">062</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 625.000000 -2197.000000) translate(0,12)">36</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 656.000000 -2093.000000) translate(0,12)">45</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 372.000000 -2104.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 510.000000 -1891.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 368.000000 -1847.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 369.000000 -1637.000000) translate(0,12)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 429.000000 -1699.000000) translate(0,12)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.000000 -1389.000000) translate(0,12)">61</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 900.000000 -2426.000000) translate(0,12)">81T38</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 809.000000 -2416.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 900.000000 -2339.000000) translate(0,12)">81T32</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 900.000000 -2196.000000) translate(0,12)">81T22</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 824.000000 -2046.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 900.000000 -1755.000000) translate(0,12)">81T20</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 798.000000 -1575.000000) translate(0,20)">F0781</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 906.000000 -1591.000000) translate(0,12)">81T16</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1657.000000 -2041.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1853.000000 -2030.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1648.000000 -1950.000000) translate(0,12)">14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1651.000000 -1814.000000) translate(0,12)">15</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1853.000000 -1817.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1422.000000 -1818.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1646.000000 -1639.000000) translate(0,12)">16</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2065.000000 -1653.000000) translate(0,12)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1986.000000 -1704.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1907.000000 -1654.000000) translate(0,12)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1993.000000 -1625.000000) translate(0,12)">15</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1650.000000 -1407.000000) translate(0,12)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1647.000000 -1315.000000) translate(0,12)">24</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1974.000000 -1420.000000) translate(0,12)">16</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1878.000000 -1420.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2317.000000 -2271.000000) translate(0,12)">33</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2502.000000 -2158.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1933.000000 -1922.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2297.000000 -2148.000000) translate(0,12)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2324.000000 -2040.000000) translate(0,12)">17</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2498.000000 -1963.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2297.000000 -1953.000000) translate(0,12)">24</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2498.000000 -1700.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2288.000000 -1690.000000) translate(0,12)">40</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2324.000000 -1846.000000) translate(0,12)">32</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2324.000000 -1524.000000) translate(0,12)">45</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2297.000000 -1490.000000) translate(0,12)">52</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2290.000000 -1373.000000) translate(0,12)">56</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2560.000000 -2298.000000) translate(0,12)">28</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2689.000000 -2271.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2681.000000 -2216.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2708.000000 -1575.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2704.000000 -1841.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2920.000000 -2200.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2920.000000 -2013.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2914.000000 -1841.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2558.000000 -2368.000000) translate(0,12)">27</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2891.000000 -2353.000000) translate(0,12)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2722.000000 -2436.000000) translate(0,12)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3624.000000 -2318.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3819.000000 -2329.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3611.000000 -1228.000000) translate(0,12)">31</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3614.000000 -1991.000000) translate(0,12)">25</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3619.000000 -1579.000000) translate(0,12)">28</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3452.000000 -607.000000) translate(0,12)">55</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3487.000000 -2402.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3487.000000 -2242.000000) translate(0,12)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3403.000000 -2242.000000) translate(0,12)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3424.000000 -2141.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3431.000000 -2036.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3431.000000 -1921.000000) translate(0,12)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3431.000000 -1821.000000) translate(0,12)">18</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3487.000000 -1703.000000) translate(0,12)">24</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3487.000000 -1563.000000) translate(0,12)">27</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3424.000000 -1462.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3431.000000 -1354.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3431.000000 -1292.000000) translate(0,12)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3431.000000 -1193.000000) translate(0,12)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3204.000000 -1354.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3423.000000 -1037.000000) translate(0,12)">16</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3456.000000 -936.000000) translate(0,12)">30</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3490.000000 -851.000000) translate(0,12)">34</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3343.000000 -860.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3457.000000 -778.000000) translate(0,12)">35</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3659.000000 -788.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3490.000000 -718.000000) translate(0,12)">45</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3319.000000 -727.000000) translate(0,12)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3455.000000 -672.000000) translate(0,12)">49</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3657.000000 -520.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3534.000000 -346.000000) translate(0,12)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3077.000000 -203.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3430.000000 -535.000000) translate(0,12)">九街四方架</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3121.000000 -344.000000) translate(0,12)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3055.000000 -296.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2855.000000 -296.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2665.000000 -287.000000) translate(0,12)">23</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2423.000000 -372.000000) translate(0,12)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2197.000000 -374.000000) translate(0,12)">20</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3063.000000 -2256.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3063.000000 -1752.000000) translate(0,12)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3051.000000 -1557.000000) translate(0,12)">12</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2780.000000 -935.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2782.000000 -974.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2633.000000 -975.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2781.000000 -1165.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2780.000000 -1195.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2922.000000 -1179.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2777.000000 -1236.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2578.000000 -1254.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2770.000000 -1293.000000) translate(0,12)">15</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2771.000000 -869.000000) translate(0,12)">15</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2530.000000 -886.000000) translate(0,20)">F0451</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2559.000000 -837.000000) translate(0,12)">24</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2396.000000 -936.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2402.000000 -839.000000) translate(0,12)">24</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2223.000000 -869.000000) translate(0,12)">26</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2247.000000 -743.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2015.000000 -743.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1753.000000 -869.000000) translate(0,12)">33</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1623.000000 -869.000000) translate(0,12)">35</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1487.000000 -869.000000) translate(0,12)">38</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1511.000000 -650.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1149.000000 -2411.000000) translate(0,20)">F032</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1107.000000 -2194.000000) translate(0,12)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1107.000000 -1982.000000) translate(0,12)">18</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1107.000000 -1556.000000) translate(0,12)">20</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1528.000000 -1565.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1301.000000 -1543.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1303.000000 -1416.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 835.000000 -1468.000000) translate(0,20)">至三街变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1142.000000 -1225.000000) translate(0,12)">21</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 917.000000 -1176.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 891.000000 -1273.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1105.000000 -1111.000000) translate(0,12)">29</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,20)">煤</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,45)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,70)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,95)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,120)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,145)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,170)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,195)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,220)">矿</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,245)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,270)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,295)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,320)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,345)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,370)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,395)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,420)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,445)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1070.000000 -1738.000000) translate(0,470)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1107.000000 -663.000000) translate(0,12)">51</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1141.000000 -539.000000) translate(0,12)">K0391</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1148.000000 -501.000000) translate(0,12)">K039</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1147.000000 -329.000000) translate(0,12)">K069</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1141.000000 -289.000000) translate(0,12)">K0691</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 665.000000 -582.000000) translate(0,20)">0921</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 664.000000 -640.000000) translate(0,20)">092</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 663.000000 -738.000000) translate(0,20)">0926</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 666.000000 -691.000000) translate(0,20)">0923</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 492.000000 -501.000000) translate(0,40)">五街河三级电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1274.000000 -1329.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2573.000000 -2233.000000) translate(0,12)">28</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2690.000000 -2100.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1821.000000 -2225.000000) translate(0,12)">35</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1410.000000 -1541.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1802.000000 -1648.000000) translate(0,12)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1674.000000 -1368.000000) translate(0,12)">20</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3758.000000 -1439.000000) translate(0,12)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3827.000000 -1704.000000) translate(0,12)">#1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3888.000000 -1792.000000) translate(0,12)">#2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3408.000000 -1546.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3424.000000 -959.000000) translate(0,12)">17</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3613.000000 -1703.000000) translate(0,12)">26</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3721.000000 -1979.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 655.000000 -1974.000000) translate(0,12)">53</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 657.000000 -1159.000000) translate(0,12)">72</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 439.000000 -1093.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 457.000000 -1054.000000) translate(0,12)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 427.000000 -1007.000000) translate(0,12)">16</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 464.000000 -923.000000) translate(0,12)">17</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 595.000000 -1958.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 459.000000 -1838.000000) translate(0,12)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 438.000000 -1905.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 456.000000 -1627.000000) translate(0,12)">16</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 432.000000 -1664.000000) translate(0,12)">15</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 467.000000 -1535.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 540.000000 -1703.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2812.000000 -1442.000000) translate(0,12)">20</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2764.000000 -1391.000000) translate(0,12)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2759.000000 -1091.000000) translate(0,12)">8+1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2777.000000 -1037.000000) translate(0,12)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2777.000000 -1126.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2713.000000 -1719.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3317.000000 -1339.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1716.000000 -1823.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1933.000000 -1420.000000) translate(0,12)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3135.000000 -2272.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1720.000000 -1426.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 474.000000 -1987.000000) translate(0,12)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3365.000000 -1025.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1088.000000 -511.000000) translate(0,12)">54</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3662.000000 -2009.000000) translate(0,12)">Z0241</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3723.000000 -1537.000000) translate(0,12)">BZ026</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 830.000000 -1736.000000) translate(0,12)">K0771</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3363.000000 -1057.000000) translate(0,12)">Z0956</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1180.000000 -1534.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3665.000000 -1979.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1677.000000 -2325.000000) translate(0,12)">35</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1553.000000 -2225.000000) translate(0,12)">36</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1152.000000 -2490.000000) translate(0,20)">F0321</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1105.000000 -2481.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 495.000000 -2225.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 311.000000 -2177.000000) translate(0,12)">CT：40/5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 810.000000 -2323.000000) translate(0,10)">１</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 798.000000 -1901.000000) translate(0,10)">６</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2916.000000 -1571.000000) translate(0,10)">１６</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2901.000000 -1162.000000) translate(0,10)">毛羊厂公变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3427.000000 -335.000000) translate(0,10)">６</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3396.000000 -699.000000) translate(0,10)">Z0931</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3065.000000 -2560.000000) translate(0,10)">０</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 1996.000000 -864.000000) translate(0,10)">29</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3696.000000 -1592.000000) translate(0,10)">６</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2669.000000 -874.000000) translate(0,12)">17</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2686.000000 -813.000000) translate(0,12)">k0411</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 804.000000 -2184.000000) translate(0,12)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 849.000000 -1768.000000) translate(0,10)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 817.000000 -1772.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2850.000000 -1404.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3429.000000 -866.000000) translate(0,10)">BZ093</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1676.000000 -2176.000000) translate(0,12)">36</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1156.500000 -921.500000) translate(0,8)">37</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3767.000000 -1597.000000) translate(0,10)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1155.500000 -788.500000) translate(0,8)">37</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3257.000000 -1046.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3219.000000 -1026.000000) translate(0,12)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3457.000000 -1007.000000) translate(0,12)">30</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3522.000000 -998.000000) translate(0,10)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3541.000000 -993.000000) translate(0,10)">K0952</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1156.500000 -1046.500000) translate(0,8)">37</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 656.000000 -2440.000000) translate(0,12)">12</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 743.000000 -2210.000000) translate(0,10)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 433.000000 -2169.000000) translate(0,12)">ZO54</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 428.000000 -2188.000000) translate(0,12)">#4杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1166.000000 -950.000000) translate(0,12)">10kV压叶子箐煤矿支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1108.000000 -911.000000) translate(0,12)">48</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1146.000000 -894.000000) translate(0,12)">K0381</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1308.000000 -879.000000) translate(0,12)">S11-M-80kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1327.000000 -901.000000) translate(0,12)">3</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1317.000000 -953.000000) translate(0,12)">10kV压叶子箐煤矿专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1165.000000 -816.000000) translate(0,12)">10kV白路洼煤矿支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1107.000000 -777.000000) translate(0,12)">49</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1167.000000 -765.000000) translate(0,12)">K0371</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1306.000000 -746.000000) translate(0,12)">S11-M-100kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1325.000000 -767.000000) translate(0,12)">8</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1315.000000 -819.000000) translate(0,12)">10kV白路洼煤矿专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1146.000000 -761.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3313.000000 -1058.000000) translate(0,12)">ZO95</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1139.000000 -1053.000000) translate(0,12)">10kV普中新支线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1108.000000 -1035.000000) translate(0,12)">41</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1146.000000 -1018.000000) translate(0,12)">K0382</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1321.000000 -1017.000000) translate(0,12)">S13-M-10kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 1370.000000 -1031.000000) translate(0,8)">1-2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1321.000000 -1057.000000) translate(0,12)">普中新专变</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1832.000000 -1756.000000) translate(0,12)">S11-M-50kVA</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1356.000000 -1754.000000) translate(0,12)">S11-M-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -86.000000) translate(0,10)">高压计量箱</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3392.000000 -2686.000000) translate(0,20)">10kV九</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3392.000000 -2686.000000) translate(0,45)">街</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3392.000000 -2686.000000) translate(0,70)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2835.000000 -2727.000000) translate(0,20)">10kV集</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2835.000000 -2727.000000) translate(0,45)">镇</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2835.000000 -2727.000000) translate(0,70)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2914.000000 -2693.000000) translate(0,20)">0851</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3008.000000 -2728.000000) translate(0,20)">10kV二</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3008.000000 -2728.000000) translate(0,45)">街</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3008.000000 -2728.000000) translate(0,70)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2654.000000 -2728.000000) translate(0,20)">10kV上</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2654.000000 -2728.000000) translate(0,45)">网</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2654.000000 -2728.000000) translate(0,70)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2732.000000 -2693.000000) translate(0,20)">0861</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2743.000000 -2563.000000) translate(0,20)">F0611</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2925.000000 -2563.000000) translate(0,20)">F0811</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3103.000000 -2562.000000) translate(0,20)">F0411</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3499.000000 -2565.000000) translate(0,20)">F0911</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3489.000000 -2698.000000) translate(0,20)">0831</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3494.000000 -2610.000000) translate(0,20)">0836</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3498.000000 -2662.000000) translate(0,20)">083</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2741.000000 -2656.000000) translate(0,20)">086</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2732.000000 -2608.000000) translate(0,20)">0866</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3661.000000 -2562.000000) translate(0,20)">F0211</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3755.000000 -2704.000000) translate(0,20)">10kV迤</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3755.000000 -2704.000000) translate(0,45)">能</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3755.000000 -2704.000000) translate(0,70)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3657.000000 -2658.000000) translate(0,20)">084</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3653.000000 -2697.000000) translate(0,20)">0841</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3653.000000 -2615.000000) translate(0,20)">0846</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3095.000000 -2697.000000) translate(0,20)">0821</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3093.000000 -2614.000000) translate(0,20)">0826</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2921.000000 -2657.000000) translate(0,20)">085</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2914.000000 -2613.000000) translate(0,20)">0856</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 441.000000 -2368.000000) translate(0,12)">鱼坝田公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 451.000000 -2341.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 492.000000 -2113.000000) translate(0,12)">10kV龙潭支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 487.000000 -2009.000000) translate(0,12)">计量</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 331.000000 -2272.000000) translate(0,12)">10kV鱼坝田支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 558.000000 -2076.000000) translate(0,12)">Z0521</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 312.000000 -2062.000000) translate(0,12)">大龙潭公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 324.000000 -2036.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 516.000000 -1876.000000) translate(0,12)">小龙潭村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 539.000000 -1850.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 465.000000 -1933.000000) translate(0,12)">10kV小龙潭分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 293.000000 -1815.000000) translate(0,12)">杨家田公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 302.000000 -1793.000000) translate(0,12)">S11-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 277.000000 -1870.000000) translate(0,12)">10kV杨家田分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 241.000000 -1690.000000) translate(0,12)">西马郎村委会公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 283.000000 -1667.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 234.000000 -1580.000000) translate(0,12)">10kV西马郎中村分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 468.000000 -1725.000000) translate(0,12)">10kV山头分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 510.000000 -1067.000000) translate(0,12)">卡门公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 509.000000 -1044.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 409.000000 -877.000000) translate(0,12)">丫口田公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 411.000000 -856.000000) translate(0,12)">S11-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 481.000000 -1200.000000) translate(0,12)">10kV丫口田支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 753.000000 -2396.000000) translate(0,12)">磨盘山</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 743.000000 -2376.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 737.000000 -2454.000000) translate(0,12)">10kV磨盘山支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 753.000000 -2309.000000) translate(0,12)">小村变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 743.000000 -2289.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 689.000000 -2020.000000) translate(0,12)">卡卡咪公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 704.000000 -1996.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 785.000000 -1852.000000) translate(0,12)">学房公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 782.000000 -1825.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 829.000000 -2135.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 829.000000 -2135.000000) translate(0,27)">学</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 829.000000 -2135.000000) translate(0,42)">房</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 829.000000 -2135.000000) translate(0,57)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 829.000000 -2135.000000) translate(0,72)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 818.000000 -1640.000000) translate(0,12)">计量</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1614.000000 -2164.000000) translate(0,12)">Z0851</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1848.000000 -2015.000000) translate(0,12)">河底公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1849.000000 -1995.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1688.000000 -2069.000000) translate(0,12)">10kV河底分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1709.000000 -2020.000000) translate(0,12)">Z0856</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1546.000000 -1908.000000) translate(0,12)">瓦房公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1536.000000 -1888.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1848.000000 -1777.000000) translate(0,12)">上排公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1703.000000 -1782.000000) translate(0,12)">10kV上排分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1730.000000 -1816.000000) translate(0,12)">BZ086</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1545.000000 -1826.000000) translate(0,12)">BZ088</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1363.000000 -1777.000000) translate(0,12)">中村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1520.000000 -1781.000000) translate(0,12)">10kV中村分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1705.000000 -1678.000000) translate(0,12)">10kV闪片房分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1730.000000 -1653.000000) translate(0,12)">Z0871</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2066.000000 -1613.000000) translate(0,12)">闪片房公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2067.000000 -1590.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1961.000000 -1782.000000) translate(0,12)">大水沟公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1976.000000 -1761.000000) translate(0,12)">S9-5kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1545.000000 -1351.000000) translate(0,12)">大地公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1535.000000 -1331.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1625.000000 -1257.000000) translate(0,12)">大岭岗公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1627.000000 -1237.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1709.000000 -1385.000000) translate(0,12)">10kV木瓜树分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1735.000000 -1415.000000) translate(0,12)">BZ089</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1975.000000 -1380.000000) translate(0,12)">荒田公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1976.000000 -1357.000000) translate(0,12)">D-5kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1854.000000 -1270.000000) translate(0,12)">木瓜树公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1869.000000 -1248.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1801.000000 -2274.000000) translate(0,20)">F0881</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1628.000000 -2132.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1628.000000 -2132.000000) translate(0,27)">法</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1628.000000 -2132.000000) translate(0,42)">古</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1628.000000 -2132.000000) translate(0,57)">么</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1628.000000 -2132.000000) translate(0,72)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1628.000000 -2132.000000) translate(0,87)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2327.000000 -2213.000000) translate(0,12)">Z0841</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2265.000000 -2230.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2265.000000 -2230.000000) translate(0,27)">云</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2265.000000 -2230.000000) translate(0,42)">盘</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2265.000000 -2230.000000) translate(0,57)">山</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2265.000000 -2230.000000) translate(0,72)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2265.000000 -2230.000000) translate(0,87)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2180.000000 -2017.000000) translate(0,12)">秧糯耙公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2193.000000 -1994.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1695.000000 -1918.000000) translate(0,12)">10kV胡广凹分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1927.000000 -1908.000000) translate(0,12)">胡广凹公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1928.000000 -1887.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2327.000000 -2164.000000) translate(0,12)">10kV小作郎分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2453.000000 -2122.000000) translate(0,12)">小作郎公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2486.000000 -2094.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2344.000000 -1968.000000) translate(0,12)">10kV平田分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2487.000000 -1926.000000) translate(0,12)">平田公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2486.000000 -1899.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2336.000000 -1670.000000) translate(0,12)">10kV石头窝分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2555.000000 -1704.000000) translate(0,12)">石头窝公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2554.000000 -1677.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2180.000000 -1823.000000) translate(0,12)">云盘山公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2193.000000 -1800.000000) translate(0,12)">S9-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2180.000000 -1502.000000) translate(0,12)">郭家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2193.000000 -1478.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2433.000000 -1463.000000) translate(0,12)">洼子公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2432.000000 -1436.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2288.000000 -1314.000000) translate(0,12)">杨梅树公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2302.000000 -1292.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2596.000000 -2315.000000) translate(0,20)">F0851</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2558.000000 -2181.000000) translate(0,12)">集镇1号公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2572.000000 -2158.000000) translate(0,12)">S9-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2563.000000 -1552.000000) translate(0,12)">大平掌公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2577.000000 -1528.000000) translate(0,12)">S9-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2559.000000 -1818.000000) translate(0,12)">塔苦公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2564.000000 -1795.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2716.000000 -1989.000000) translate(0,12)">集镇2号公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2729.000000 -1966.000000) translate(0,12)">S9-160kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2770.000000 -1552.000000) translate(0,12)">树苴上村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2783.000000 -1528.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2713.000000 -2231.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2713.000000 -2231.000000) translate(0,27)">树</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2713.000000 -2231.000000) translate(0,42)">苴</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2713.000000 -2231.000000) translate(0,57)">中</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2713.000000 -2231.000000) translate(0,72)">学</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2713.000000 -2231.000000) translate(0,87)">分</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2713.000000 -2231.000000) translate(0,102)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2713.000000 -2231.000000) translate(0,117)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2717.000000 -2289.000000) translate(0,12)">10kV树苴中村支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2844.000000 -2208.000000) translate(0,12)">BZ083</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3659.000000 -2301.000000) translate(0,12)">10kV大迤能支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3885.000000 -2332.000000) translate(0,12)">大迤能公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3883.000000 -2304.000000) translate(0,12)">S9-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3595.000000 -1169.000000) translate(0,12)">迤能山公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3605.000000 -1143.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3823.000000 -1964.000000) translate(0,12)">王开宝煤矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3821.000000 -1937.000000) translate(0,12)">S7-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3823.000000 -1767.000000) translate(0,12)">王世忠煤矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3821.000000 -1740.000000) translate(0,12)">S11-200kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3727.000000 -2014.000000) translate(0,12)">10kV王开宝煤矿支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3808.000000 -1591.000000) translate(0,12)">桂花箐煤矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3808.000000 -1567.000000) translate(0,12)">S7-125kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3713.000000 -1379.000000) translate(0,12)">王连增煤矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3711.000000 -1352.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3307.000000 -2329.000000) translate(0,12)">联通公司私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3320.000000 -2306.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3305.000000 -2419.000000) translate(0,12)">移动公司私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3318.000000 -2395.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3352.000000 -2203.000000) translate(0,12)">BZ094</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3278.000000 -2117.000000) translate(0,12)">迤能下村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3291.000000 -2093.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3278.000000 -1896.000000) translate(0,12)">泥拉堵公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3291.000000 -1873.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3278.000000 -2010.000000) translate(0,12)">迤能上村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3291.000000 -1986.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3278.000000 -1792.000000) translate(0,12)">挖补地山公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3291.000000 -1768.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3434.000000 -2221.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3434.000000 -2221.000000) translate(0,27)">泥</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3434.000000 -2221.000000) translate(0,42)">拉</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3434.000000 -2221.000000) translate(0,57)">堵</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3434.000000 -2221.000000) translate(0,72)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3434.000000 -2221.000000) translate(0,87)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3341.000000 -1674.000000) translate(0,12)">挖补地公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3355.000000 -1650.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3352.000000 -1524.000000) translate(0,12)">BZ095</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3278.000000 -1437.000000) translate(0,12)">塔把箐公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3291.000000 -1414.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3278.000000 -1268.000000) translate(0,12)">挖挤苴公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3291.000000 -1244.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3278.000000 -1163.000000) translate(0,12)">必架梁公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3291.000000 -1140.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3434.000000 -1541.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3434.000000 -1541.000000) translate(0,27)">马</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3434.000000 -1541.000000) translate(0,42)">渣</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3434.000000 -1541.000000) translate(0,57)">拉</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3434.000000 -1541.000000) translate(0,72)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3434.000000 -1541.000000) translate(0,87)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3189.000000 -1328.000000) translate(0,12)">塔把公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3203.000000 -1305.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3270.000000 -1370.000000) translate(0,12)">10kV塔把分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3151.000000 -1009.000000) translate(0,12)">马渣拉煤矿2号变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3156.000000 -965.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3327.000000 -1332.000000) translate(0,12)">BZ092</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3499.000000 -943.000000) translate(0,20)">F0951</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3179.000000 -859.000000) translate(0,12)">依齐么公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3184.000000 -835.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3296.000000 -827.000000) translate(0,12)">10kV依齐么支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3653.000000 -753.000000) translate(0,12)">咪格公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3667.000000 -729.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3528.000000 -794.000000) translate(0,12)">10kV咪格支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3252.000000 -692.000000) translate(0,12)">布扎公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3266.000000 -669.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3350.000000 -740.000000) translate(0,12)">10kV布扎支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3563.000000 -641.000000) translate(0,12)">马家村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3561.000000 -614.000000) translate(0,12)">S9-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3640.000000 -502.000000) translate(0,12)">腊武地公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3639.000000 -475.000000) translate(0,12)">S9-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3532.000000 -406.000000) translate(0,12)">BZ097</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3376.000000 -410.000000) translate(0,12)">BZ096</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3331.000000 -552.000000) translate(0,12)">Z0991</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3571.000000 -511.000000) translate(0,12)">BZ098</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3549.000000 -557.000000) translate(0,12)">10kV腊武地支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3541.000000 -318.000000) translate(0,12)">小咪打公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3551.000000 -292.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3381.000000 -287.000000) translate(0,12)">九街集镇公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3391.000000 -261.000000) translate(0,12)">S9-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3460.000000 -460.000000) translate(0,12)">九</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3460.000000 -460.000000) translate(0,27)">街</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3460.000000 -460.000000) translate(0,42)">集</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3460.000000 -460.000000) translate(0,57)">镇</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3460.000000 -460.000000) translate(0,72)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3460.000000 -460.000000) translate(0,87)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3490.000000 -459.000000) translate(0,12)">小</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3490.000000 -459.000000) translate(0,27)">咪</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3490.000000 -459.000000) translate(0,42)">打</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3490.000000 -459.000000) translate(0,57)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3490.000000 -459.000000) translate(0,72)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3022.000000 -252.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3022.000000 -252.000000) translate(0,27)">羊</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3022.000000 -252.000000) translate(0,42)">血</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3022.000000 -252.000000) translate(0,57)">地</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3022.000000 -252.000000) translate(0,72)">分</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3022.000000 -252.000000) translate(0,87)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3022.000000 -252.000000) translate(0,102)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3051.000000 -125.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2837.000000 -147.000000) translate(0,12)">普汤郎公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2851.000000 -125.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2660.000000 -147.000000) translate(0,12)">的湾公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2651.000000 -120.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2398.000000 -220.000000) translate(0,12)">法依口公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2412.000000 -199.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2177.000000 -220.000000) translate(0,12)">大凹子公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2191.000000 -199.000000) translate(0,12)">S9-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3050.000000 -150.000000) translate(0,12)">羊血地公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2900.000000 -300.000000) translate(0,12)">10kV的湾分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3167.000000 -2229.000000) translate(0,12)">刘发金煤矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3166.000000 -2202.000000) translate(0,12)">S7-160kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3167.000000 -1726.000000) translate(0,12)">故布子公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3166.000000 -1699.000000) translate(0,12)">S11-63kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3167.000000 -1530.000000) translate(0,12)">鹅毛村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3166.000000 -1503.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2698.000000 -1466.000000) translate(0,12)">鲁钠公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2689.000000 -1440.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2947.000000 -881.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2948.000000 -906.000000) translate(0,12)">次武地公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2566.000000 -941.000000) translate(0,12)">罗把苦公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2580.000000 -918.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2620.000000 -995.000000) translate(0,12)">10kV罗把苦分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2952.000000 -982.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2953.000000 -1006.000000) translate(0,12)">二可把公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2905.000000 -1140.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 2802.000000 -1222.000000) translate(0,10)">10kV毛羊厂分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2590.000000 -1144.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2590.000000 -1169.000000) translate(0,12)">姚家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2567.000000 -1227.000000) translate(0,12)">天生桥公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2580.000000 -1203.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2613.000000 -1276.000000) translate(0,12)">10kV天生桥分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2901.000000 -1290.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2902.000000 -1315.000000) translate(0,12)">挤苴公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3275.000000 -520.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3275.000000 -520.000000) translate(0,27)">法</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3275.000000 -520.000000) translate(0,42)">依</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3275.000000 -520.000000) translate(0,57)">口</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3275.000000 -520.000000) translate(0,72)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3275.000000 -520.000000) translate(0,87)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3003.000000 -2151.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3003.000000 -2151.000000) translate(0,45)"> </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3003.000000 -2151.000000) translate(0,70)">二</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3003.000000 -2151.000000) translate(0,95)"> </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3003.000000 -2151.000000) translate(0,120)">街</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3003.000000 -2151.000000) translate(0,145)"> </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3003.000000 -2151.000000) translate(0,170)"> </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3003.000000 -2151.000000) translate(0,195)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1784.000000 -893.000000) translate(0,20)">10kV    二             街              线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2297.000000 -1000.000000) translate(0,12)">二街集镇公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2311.000000 -979.000000) translate(0,12)">S9-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2441.000000 -1032.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2441.000000 -1032.000000) translate(0,27)">二</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2441.000000 -1032.000000) translate(0,42)">街</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2441.000000 -1032.000000) translate(0,57)">集</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2441.000000 -1032.000000) translate(0,72)">镇</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2441.000000 -1032.000000) translate(0,87)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2441.000000 -1032.000000) translate(0,102)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2192.000000 -684.000000) translate(0,12)">塔武公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2200.000000 -659.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2196.000000 -841.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2196.000000 -841.000000) translate(0,27)">塔</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2196.000000 -841.000000) translate(0,42)">武</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2196.000000 -841.000000) translate(0,57)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2196.000000 -841.000000) translate(0,72)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1960.000000 -684.000000) translate(0,12)">阿旧苦公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1969.000000 -659.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1964.000000 -839.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1964.000000 -839.000000) translate(0,27)">阿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1964.000000 -839.000000) translate(0,42)">旧</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1964.000000 -839.000000) translate(0,57)">苦</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1964.000000 -839.000000) translate(0,72)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1964.000000 -839.000000) translate(0,87)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1708.000000 -718.000000) translate(0,12)">必伞郎新村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1735.000000 -696.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1921.000000 -505.000000) translate(0,20)">10kV   鹅      必      联      络     线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1436.000000 -591.000000) translate(0,12)">必伞郎大村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1465.000000 -566.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1457.000000 -819.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1457.000000 -819.000000) translate(0,27)">必</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1457.000000 -819.000000) translate(0,42)">伞</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1457.000000 -819.000000) translate(0,57)">郎</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1457.000000 -819.000000) translate(0,72)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1457.000000 -819.000000) translate(0,87)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1219.000000 -2167.000000) translate(0,12)">果咪公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1217.000000 -2140.000000) translate(0,12)">S9-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1219.000000 -1956.000000) translate(0,12)">菜地公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1217.000000 -1928.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1502.000000 -1531.000000) translate(0,12)">利湾公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1500.000000 -1504.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1186.000000 -1567.000000) translate(0,12)">BZ034</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1257.000000 -1573.000000) translate(0,12)">10kV利湾支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1362.000000 -1377.000000) translate(0,12)">牛坡希公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1370.000000 -1352.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1252.000000 -1538.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1252.000000 -1538.000000) translate(0,27)">牛</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1252.000000 -1538.000000) translate(0,42)">坡</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1252.000000 -1538.000000) translate(0,57)">希</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1252.000000 -1538.000000) translate(0,72)">分</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1252.000000 -1538.000000) translate(0,87)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1252.000000 -1538.000000) translate(0,102)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 950.000000 -1248.000000) translate(0,12)">10kV甲利么支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1057.000000 -1200.000000) translate(0,12)">Z0351</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 868.000000 -1117.000000) translate(0,12)">甲利么公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 871.000000 -1093.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 867.000000 -1356.000000) translate(0,12)">张彩富煤矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 881.000000 -1329.000000) translate(0,12)">S9-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1219.000000 -636.000000) translate(0,12)">自苦么公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1217.000000 -609.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1231.000000 -396.000000) translate(0,12)">三街煤矿自变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1235.000000 -367.000000) translate(0,12)">S9-90+100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1034.000000 -180.000000) translate(0,20)">至10kV黑泥线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1196.000000 -521.000000) translate(0,12)">(10kV三街煤矿II回线)</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1196.000000 -311.000000) translate(0,12)">(10kV三街煤矿I回线)</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 587.000000 -802.000000) translate(0,20)">一</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 587.000000 -802.000000) translate(0,45)">三</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 587.000000 -802.000000) translate(0,70)">联</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 587.000000 -802.000000) translate(0,95)">网</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 587.000000 -802.000000) translate(0,120)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2634.000000 -2074.000000) translate(0,12)">BZ082</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 838.000000 -2178.000000) translate(0,12)">Z0881</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 833.000000 -2408.000000) translate(0,12)">Z0891</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1693.000000 -2767.000000) translate(0,20)">10kV母线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 3743.000000 -2761.000000) translate(0,20)">10kV母线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1239.000000 -1274.000000) translate(0,12)">牛波希煤矿自变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1247.000000 -1249.000000) translate(0,12)">S11-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2927.000000 -1340.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2912.000000 -1365.000000) translate(0,12)">鲁纳煤矿自变</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 671.000000 -2169.000000) translate(0,12)">夏国政煤矿变</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 669.000000 -2149.000000) translate(0,12)">S11-M-200kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1364.000000 -1699.000000) translate(0,12)">王连增煤矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1378.000000 -1678.000000) translate(0,12)">S7-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1748.000000 -1509.000000) translate(0,12)">小岭岗公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1762.000000 -1487.000000) translate(0,12)">S11-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3759.000000 -1678.000000) translate(0,12)">王世忠煤矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3758.000000 -1651.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3286.000000 -929.000000) translate(0,12)">马渣拉公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3300.000000 -906.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 561.000000 -1989.000000) translate(0,12)">Z0551</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 558.000000 -1178.000000) translate(0,12)">Z0561</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 294.000000 -1033.000000) translate(0,12)">大转弯公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 303.000000 -1011.000000) translate(0,12)">S11-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 462.000000 -1008.000000) translate(0,12)">Z0569</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 484.000000 -1813.000000) translate(0,12)">老董坟公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 496.000000 -1791.000000) translate(0,12)">S11-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 461.000000 -1664.000000) translate(0,12)">Z0556</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 355.000000 -1495.000000) translate(0,12)">西马郎中村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 395.000000 -1471.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 510.000000 -1684.000000) translate(0,12)">Z0559</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3676.000000 -1982.000000) translate(0,12)"> </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3676.000000 -1982.000000) translate(0,27)">王</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3676.000000 -1982.000000) translate(0,42)">世</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3676.000000 -1982.000000) translate(0,57)">忠</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3676.000000 -1982.000000) translate(0,72)">煤</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3676.000000 -1982.000000) translate(0,87)">矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3676.000000 -1982.000000) translate(0,102)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3676.000000 -1982.000000) translate(0,117)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3668.000000 -1584.000000) translate(0,12)"> </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3668.000000 -1584.000000) translate(0,27)">王</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3668.000000 -1584.000000) translate(0,42)">连</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3668.000000 -1584.000000) translate(0,57)">增</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3668.000000 -1584.000000) translate(0,72)">煤</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3668.000000 -1584.000000) translate(0,87)">矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3668.000000 -1584.000000) translate(0,102)">支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3668.000000 -1584.000000) translate(0,117)">线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1153.000000 -1130.000000) translate(0,12)">L0461</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1592.000000 -1088.000000) translate(0,20)">10kV 二 街 线 T 二 可 把 联 络 线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2815.000000 -1108.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2815.000000 -1108.000000) translate(0,45)">二街</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2815.000000 -1108.000000) translate(0,70)">线T</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2815.000000 -1108.000000) translate(0,95)">二可</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2815.000000 -1108.000000) translate(0,120)">把联</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 2815.000000 -1108.000000) translate(0,145)">络线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 2801.000000 -1369.000000) translate(0,6)">10kV二 可 把 联 络 线   T鲁 纳 支 线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2708.000000 -2548.000000) translate(0,12)">1</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2882.000000 -2554.000000) translate(0,12)">1</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3460.000000 -2556.000000) translate(0,12)">1</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3623.000000 -2558.000000) translate(0,12)">1</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1905.000000 -1389.000000) translate(0,12)">BZ091</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3107.000000 -2233.000000) translate(0,12)">Z0421</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 409.000000 -2002.000000) translate(0,12)">10kV西马郎支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2770.000000 -2041.000000) translate(0,12)">集镇2号变分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2711.000000 -1699.000000) translate(0,12)">BZ084</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2852.000000 -1987.000000) translate(0,12)">Z0833</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1533.000000 -2274.000000) translate(0,20)">F0891</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 532.000000 -1529.000000) translate(0,12)">取水口公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 547.000000 -1503.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 587.000000 -1367.000000) translate(0,12)">Z0571</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 473.000000 -1335.000000) translate(0,12)">10kV庄房支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 318.000000 -2145.000000) translate(0,12)">10kV法古苴支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 563.000000 -2223.000000) translate(0,12)">Z0541</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 508.000000 -1430.000000) translate(0,12)">计量</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2728.000000 -904.000000) translate(0,12)">L0464</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3384.000000 -2371.000000) translate(0,12)">Z0911</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2612.000000 -675.000000) translate(0,12)">电信基站自变</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 684.000000 -2228.000000) translate(0,8)">10kV夏国政煤矿支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 786.000000 -2207.000000) translate(0,10)">K0781</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1220.000000 -1617.000000) translate(0,12)">10kV王连增煤矿支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1428.000000 -1580.000000) translate(0,12)">BK036</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2824.000000 -1420.000000) translate(0,12)">10kV鲁纳煤矿支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2841.000000 -1374.000000) translate(0,12)">K0471</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3715.000000 -269.000000) translate(0,12)">注：绿色框内为本次新装设备</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 0.000000 0.000000 1.000000 2799.000000 -1178.000000) translate(0,6)">10kV二 可 把 联 络 线   T鲁 纳 支 线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2821.000000 -1203.000000) translate(0,12)">KR0468</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3053.000000 -372.000000) translate(0,12)">ZR0912</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 830.000000 -2235.000000) translate(0,10)">10kV学房支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3255.000000 -1016.000000) translate(0,12)">10kV马渣拉煤矿分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3150.000000 -1174.000000) translate(0,12)">马渣拉煤矿1号变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -1130.000000) translate(0,12)">S11-M-200kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3610.000000 -985.000000) translate(0,12)">鹏程建筑公司专变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3609.000000 -960.000000) translate(0,12)">S11-M-160kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3531.000000 -1019.000000) translate(0,10)">10kV鹏程建筑公司支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 1625.000000 -2616.000000) translate(0,20)">注意:                                                           1、10kV上网线03号杆至18号杆、10kV集镇线04号杆至19号杆、10kV迤能线05号杆至20号杆段线路为同杆架设。                                                  2、10kV集镇线02号杆至04号杆、10kV迤能线03号杆至05号杆段线路为同杆架设。</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 550.000000 -1651.000000) translate(0,12)">山头公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 549.000000 -1668.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1892.000000 -1500.000000) translate(0,12)">蔡龙树公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 1906.000000 -1478.000000) translate(0,12)">S9-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2768.000000 -1814.000000) translate(0,12)">树苴中村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 2785.000000 -1791.000000) translate(0,12)">S9-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 493.000000 -2459.000000) translate(0,12)">10kV钱思林支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 558.000000 -2422.000000) translate(0,12)">K0511</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 322.000000 -2416.000000) translate(0,12)">钱思林专变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 322.000000 -2392.000000) translate(0,12)">S11-M-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 697.000000 -2214.000000) translate(0,10)">K001</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3518.000000 -37.000000) translate(0,10)">跌落保险</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3518.000000 -125.000000) translate(0,10)">断路器</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -164.000000) translate(0,10)">隔离开关</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3873.000000 -107.000000) translate(0,10)"> 树苴供电所辖区电网接线图</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3875.000000 -25.000000) translate(0,10)">更新日期</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 4024.000000 -23.000000) translate(0,10)">2012年12月17日</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -25.000000) translate(0,10)">李忠平</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3730.000000 -63.000000) translate(0,10)">戎加万</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3730.000000 -105.000000) translate(0,10)">胡助平</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3730.000000 -142.000000) translate(0,10)">刘福紧</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3680.000000 -196.000000) translate(0,18)">楚 雄 市 供 电 有 限 公 司</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3613.000000 -99.000000) translate(0,10)">审　核</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3614.000000 -27.000000) translate(0,10)">制  图</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3613.000000 -64.000000) translate(0,10)">校　核</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3613.000000 -142.000000) translate(0,10)">审　定</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 3453.000000 -200.000000) translate(0,10)"> 图  例</text>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="标注、文字:0.000000 0.000000" layer11="粗线:0.000000 0.000000" layer12="35KV母线:0.000000 0.000000" layer13="10KV线路:0.000000 0.000000" layer14="10KV母线:0.000000 0.000000" layer15="35KV线路:0.000000 0.000000" layer16="虚线:0.000000 0.000000" layer17="图框（粗实线）:0.000000 0.000000" layer18="PEN4:0.000000 0.000000" layer19="0:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer20="主干线:0.000000 0.000000" layer21="次干线:0.000000 0.000000" layer22="支线:0.000000 0.000000" layer23="公变:0.000000 0.000000" layer24="自变:0.000000 0.000000" layer25="图框（细实线）:0.000000 0.000000" layer26="标注、文字:0.000000 0.000000" layer27="粗线:0.000000 0.000000" layer28="35KV母线:0.000000 0.000000" layer29="10KV线路:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer30="10KV母线:0.000000 0.000000" layer31="35KV线路:0.000000 0.000000" layer32="虚线:0.000000 0.000000" layer33="图框（粗实线）:0.000000 0.000000" layer34="PEN4:0.000000 0.000000" layer35="PUBLIC:0.000000 0.000000" layer4="主干线:0.000000 0.000000" layer5="次干线:0.000000 0.000000" layer6="支线:0.000000 0.000000" layer7="公变:0.000000 0.000000" layer8="自变:0.000000 0.000000" layer9="图框（细实线）:0.000000 0.000000" layerN="36"/>
</svg>