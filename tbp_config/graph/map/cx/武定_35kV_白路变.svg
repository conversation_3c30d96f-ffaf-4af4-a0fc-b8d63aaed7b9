<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-171" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3015 -1196 2135 1294">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="37" x2="37" y1="122" y2="130"/>
    <polyline arcFlag="1" points="37,122 35,122 33,121 32,121 30,120 29,119 27,118 26,116 25,114 25,113 24,111 24,109 24,107 25,105 25,104 26,102 27,101 29,99 30,98 32,97 33,97 35,96 37,96 39,96 41,97 42,97 44,98 45,99 47,101 48,102 49,104 49,105 50,107 50,109 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="50" x2="38" y1="109" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.915724" x1="37" x2="37" y1="109" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="23" y2="14"/>
    <polyline arcFlag="1" points="13,23 12,23 12,23 11,23 10,23 10,24 9,24 8,25 8,25 8,26 7,27 7,27 7,28 7,29 7,30 7,30 8,31 8,32 8,32 9,33 10,33 10,34 11,34 12,34 12,34 13,34 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,34 12,34 12,34 11,34 10,34 10,35 9,35 8,36 8,36 8,37 7,38 7,38 7,39 7,40 7,41 7,41 8,42 8,43 8,43 9,44 10,44 10,45 11,45 12,45 12,45 13,45 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,45 12,45 12,45 11,45 10,45 10,46 9,46 8,47 8,47 8,48 7,49 7,49 7,50 7,51 7,52 7,52 8,53 8,54 8,54 9,55 10,55 10,56 11,56 12,56 12,56 13,56 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="65" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="13" y2="13"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="56" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="16" y2="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape71_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,55 6,55 6,26 " stroke-width="1"/>
    <circle cx="31" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="43" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="31,12 25,25 37,25 31,12 31,13 31,12 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="55" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="55" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="55" y2="60"/>
   </symbol>
   <symbol id="transformer2:shape71_1">
    <circle cx="31" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="79" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="79" y2="84"/>
   </symbol>
   <symbol id="voltageTransformer:shape64">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="38" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="8" y1="16" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="17" y1="24" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="10" y1="17" y2="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape101">
    <rect height="24" stroke-width="0.379884" width="14" x="9" y="36"/>
    <circle cx="15" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="73" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="20" y1="11" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="13" x2="16" y1="24" y2="21"/>
    <circle cx="23" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.128496" x1="8" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.123356" x1="6" x2="8" y1="10" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.154195" x1="8" x2="7" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="16" x2="19" y1="21" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="16" x2="16" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="20" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="26" y1="6" y2="9"/>
   </symbol>
   <symbol id="voltageTransformer:shape94">
    <rect height="24" stroke-width="0.379884" width="14" x="2" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="21" y1="14" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="8" y2="11"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="25" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="75" y2="31"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8f180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8fb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a90530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a91760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a926d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a93370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a93b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a94570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a95bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a95bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a96d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a96d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a98980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a98980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2a99990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9b620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a9c270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a9d150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a9da30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9f420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9fc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa0220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa0be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa1d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa26e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa31d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa3b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa5080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa5ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa6bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa7840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ab5c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa90c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2aaa5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2aabbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1304" width="2145" x="3010" y="-1201"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="3862" x2="3866" y1="-781" y2="-777"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4429" x2="4433" y1="-119" y2="-115"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5141" x2="5150" y1="-410" y2="-410"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3070" y="-1135"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3046" y="-1091"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-120163">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -727.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22367" ObjectName="SW-WD_BL.WD_BL_301BK"/>
     <cge:Meas_Ref ObjectId="120163"/>
    <cge:TPSR_Ref TObjectID="22367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120164">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -460.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22368" ObjectName="SW-WD_BL.WD_BL_001BK"/>
     <cge:Meas_Ref ObjectId="120164"/>
    <cge:TPSR_Ref TObjectID="22368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120002">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 -261.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22342" ObjectName="SW-WD_BL.WD_BL_051BK"/>
     <cge:Meas_Ref ObjectId="120002"/>
    <cge:TPSR_Ref TObjectID="22342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119911">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3845.000000 -283.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22380" ObjectName="SW-WD_BL.WD_BL_052BK"/>
     <cge:Meas_Ref ObjectId="119911"/>
    <cge:TPSR_Ref TObjectID="22380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120181">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -907.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22374" ObjectName="SW-WD_BL.WD_BL_351BK"/>
     <cge:Meas_Ref ObjectId="120181"/>
    <cge:TPSR_Ref TObjectID="22374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120057">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4005.000000 -284.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22352" ObjectName="SW-WD_BL.WD_BL_053BK"/>
     <cge:Meas_Ref ObjectId="120057"/>
    <cge:TPSR_Ref TObjectID="22352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120012">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -284.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22347" ObjectName="SW-WD_BL.WD_BL_054BK"/>
     <cge:Meas_Ref ObjectId="120012"/>
    <cge:TPSR_Ref TObjectID="22347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119956">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -282.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22334" ObjectName="SW-WD_BL.WD_BL_055BK"/>
     <cge:Meas_Ref ObjectId="119956"/>
    <cge:TPSR_Ref TObjectID="22334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120097">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.000000 -223.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22357" ObjectName="SW-WD_BL.WD_BL_016BK"/>
     <cge:Meas_Ref ObjectId="120097"/>
    <cge:TPSR_Ref TObjectID="22357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4869.000000 -282.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22381" ObjectName="SW-WD_BL.WD_BL_061BK"/>
     <cge:Meas_Ref ObjectId="120107"/>
    <cge:TPSR_Ref TObjectID="22381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-201306">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5029.000000 -280.688406)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30748" ObjectName="SW-WD_BL.WD_BL_062BK"/>
     <cge:Meas_Ref ObjectId="201306"/>
    <cge:TPSR_Ref TObjectID="30748"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1f35d30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4282.000000 -1038.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e50710">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4653.311108 -592.000000)" xlink:href="#voltageTransformer:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e84e70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3525.000000 -176.000000)" xlink:href="#voltageTransformer:shape94"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="WD_DXS" endPointId="0" endStationName="WD_BL" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dabai" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4245,-1150 4245,-1174 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38119" ObjectName="AC-35kV.LN_dabai"/>
    <cge:TPSR_Ref TObjectID="38119_SS-171"/></metadata>
   <polyline fill="none" opacity="0" points="4245,-1150 4245,-1174 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-WD_BL.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 14.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33620" ObjectName="EC-WD_BL.052Ld"/>
    <cge:TPSR_Ref TObjectID="33620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_BL.053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4009.000000 13.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33621" ObjectName="EC-WD_BL.053Ld"/>
    <cge:TPSR_Ref TObjectID="33621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_BL.054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 13.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33622" ObjectName="EC-WD_BL.054Ld"/>
    <cge:TPSR_Ref TObjectID="33622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_BL.055Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4329.000000 15.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33623" ObjectName="EC-WD_BL.055Ld"/>
    <cge:TPSR_Ref TObjectID="33623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_BL.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.000000 15.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33626" ObjectName="EC-WD_BL.061Ld"/>
    <cge:TPSR_Ref TObjectID="33626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_BL.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5033.000000 17.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33627" ObjectName="EC-WD_BL.062Ld"/>
    <cge:TPSR_Ref TObjectID="33627"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1f39d90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -894.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f06ac0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -950.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ece100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -1012.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f8d1c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4304.000000 -451.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f5ce50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.000000 -766.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f5d8e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4754.311108 -780.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e7c7c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.000000 -285.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_215bd30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3753.000000 -301.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e8b5e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3754.000000 -195.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e9c100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3753.000000 -115.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f0b7c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3753.000000 -57.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c967b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -324.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ffbbf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4072.000000 -325.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d298f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.000000 -325.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_377f840" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4392.000000 -323.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_342bf60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.000000 -291.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3783ae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4760.000000 -290.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3795850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4936.000000 -323.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ba1b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5096.000000 -321.450000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33cbd30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4753.311108 -692.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_2c95900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-627 4245,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="22382@0" ObjectIDZND0="22364@1" Pin0InfoVect0LinkObjId="SW-120160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e7ad80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-627 4245,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3240e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-408 4245,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22365@0" ObjectIDZND0="22324@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120161_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-408 4245,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e7ad80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-735 4245,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22367@0" ObjectIDZND0="22382@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120163_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-735 4245,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b54d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-271 3694,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22342@0" ObjectIDZND0="22337@1" Pin0InfoVect0LinkObjId="SW-119997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120002_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-271 3694,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32da110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-385 3694,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22324@0" ObjectIDZND0="22335@1" Pin0InfoVect0LinkObjId="SW-119995_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3240e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-385 3694,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31db650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-511 4245,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22364@0" ObjectIDZND0="22368@1" Pin0InfoVect0LinkObjId="SW-120164_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-511 4245,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ddac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-293 3854,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22380@0" ObjectIDZND0="22328@1" Pin0InfoVect0LinkObjId="SW-119907_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119911_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-293 3854,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32497a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-385 3854,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22324@0" ObjectIDZND0="22326@1" Pin0InfoVect0LinkObjId="SW-119905_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3240e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-385 3854,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f9f2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-884 4245,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22369@1" ObjectIDZND0="22374@x" ObjectIDZND1="22370@x" Pin0InfoVect0LinkObjId="SW-120181_0" Pin0InfoVect1LinkObjId="SW-120177_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120176_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-884 4245,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f9f4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-900 4245,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22369@x" ObjectIDND1="22370@x" ObjectIDZND0="22374@0" Pin0InfoVect0LinkObjId="SW-120181_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120176_0" Pin1InfoVect1LinkObjId="SW-120177_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-900 4245,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f398d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-900 4259,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22374@x" ObjectIDND1="22369@x" ObjectIDZND0="22370@0" Pin0InfoVect0LinkObjId="SW-120177_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120181_0" Pin1InfoVect1LinkObjId="SW-120176_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-900 4259,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f39b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-900 4295,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f39d90@0" ObjectIDZND0="22370@1" Pin0InfoVect0LinkObjId="SW-120177_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f39d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-900 4295,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f06600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-956 4259,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22374@x" ObjectIDND1="22371@x" ObjectIDZND0="22372@0" Pin0InfoVect0LinkObjId="SW-120179_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120181_0" Pin1InfoVect1LinkObjId="SW-120178_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-956 4259,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f06860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-956 4295,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f06ac0@0" ObjectIDZND0="22372@1" Pin0InfoVect0LinkObjId="SW-120179_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f06ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-956 4295,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e88310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-942 4245,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22374@1" ObjectIDZND0="22372@x" ObjectIDZND1="22371@x" Pin0InfoVect0LinkObjId="SW-120179_0" Pin0InfoVect1LinkObjId="SW-120178_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120181_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-942 4245,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e88570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-956 4245,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22374@x" ObjectIDND1="22372@x" ObjectIDZND0="22371@0" Pin0InfoVect0LinkObjId="SW-120178_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120181_0" Pin1InfoVect1LinkObjId="SW-120179_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-956 4245,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ecdc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-1018 4259,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="22371@x" ObjectIDND1="g_2bd0b90@0" ObjectIDND2="38119@1" ObjectIDZND0="22373@0" Pin0InfoVect0LinkObjId="SW-120180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120178_0" Pin1InfoVect1LinkObjId="g_2bd0b90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-1018 4259,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ecdea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-1018 4295,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1ece100@0" ObjectIDZND0="22373@1" Pin0InfoVect0LinkObjId="SW-120180_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ece100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-1018 4295,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f34d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-1018 4245,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="22373@x" ObjectIDND1="g_2bd0b90@0" ObjectIDND2="38119@1" ObjectIDZND0="22371@1" Pin0InfoVect0LinkObjId="SW-120178_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120180_0" Pin1InfoVect1LinkObjId="g_2bd0b90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-1018 4245,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f34fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-1035 4290,-1035 4290,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="22371@x" ObjectIDND1="22373@x" ObjectIDND2="g_2bd0b90@0" ObjectIDZND0="g_1f35d30@0" Pin0InfoVect0LinkObjId="g_1f35d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120178_0" Pin1InfoVect1LinkObjId="SW-120180_0" Pin1InfoVect2LinkObjId="g_2bd0b90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-1035 4290,-1035 4290,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f35ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-1031 4245,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2bd0b90@0" ObjectIDND1="38119@1" ObjectIDND2="g_1f35d30@0" ObjectIDZND0="22371@x" ObjectIDZND1="22373@x" Pin0InfoVect0LinkObjId="SW-120178_0" Pin0InfoVect1LinkObjId="SW-120180_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2bd0b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_1f35d30_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-1031 4245,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f0a680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-1046 4226,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="22371@x" ObjectIDND1="22373@x" ObjectIDND2="g_1f35d30@0" ObjectIDZND0="g_2bd0b90@0" Pin0InfoVect0LinkObjId="g_2bd0b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120178_0" Pin1InfoVect1LinkObjId="SW-120180_0" Pin1InfoVect2LinkObjId="g_1f35d30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-1046 4226,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bd06d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-1150 4245,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="38119@1" ObjectIDZND0="22371@x" ObjectIDZND1="22373@x" ObjectIDZND2="g_1f35d30@0" Pin0InfoVect0LinkObjId="SW-120178_0" Pin0InfoVect1LinkObjId="SW-120180_0" Pin0InfoVect2LinkObjId="g_1f35d30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-1150 4245,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bd0930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-1046 4245,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2bd0b90@0" ObjectIDND1="38119@1" ObjectIDZND0="22371@x" ObjectIDZND1="22373@x" ObjectIDZND2="g_1f35d30@0" Pin0InfoVect0LinkObjId="SW-120178_0" Pin0InfoVect1LinkObjId="SW-120180_0" Pin0InfoVect2LinkObjId="g_1f35d30_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bd0b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-1046 4245,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb4810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-783 4245,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="22362@0" ObjectIDZND0="22367@x" ObjectIDZND1="22363@x" Pin0InfoVect0LinkObjId="SW-120163_0" Pin0InfoVect1LinkObjId="SW-120156_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120155_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-783 4245,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fb4a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-775 4245,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22362@x" ObjectIDND1="22363@x" ObjectIDZND0="22367@1" Pin0InfoVect0LinkObjId="SW-120163_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120155_0" Pin1InfoVect1LinkObjId="SW-120156_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-775 4245,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f8cd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4246,-457 4260,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22368@x" ObjectIDND1="22365@x" ObjectIDZND0="22366@0" Pin0InfoVect0LinkObjId="SW-120162_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120164_0" Pin1InfoVect1LinkObjId="SW-120161_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4246,-457 4260,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f8cf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-457 4296,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f8d1c0@0" ObjectIDZND0="22366@1" Pin0InfoVect0LinkObjId="SW-120162_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f8d1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-457 4296,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f8e330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-468 4245,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22368@0" ObjectIDZND0="22366@x" ObjectIDZND1="22365@x" Pin0InfoVect0LinkObjId="SW-120162_0" Pin0InfoVect1LinkObjId="SW-120161_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120164_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-468 4245,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f8e590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-457 4245,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22366@x" ObjectIDND1="22368@x" ObjectIDZND0="22365@1" Pin0InfoVect0LinkObjId="SW-120161_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120162_0" Pin1InfoVect1LinkObjId="SW-120164_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-457 4245,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f5c990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-772 4259,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22362@x" ObjectIDND1="22367@x" ObjectIDZND0="22363@0" Pin0InfoVect0LinkObjId="SW-120156_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120155_0" Pin1InfoVect1LinkObjId="SW-120163_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-772 4259,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f5cbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-772 4295,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f5ce50@0" ObjectIDZND0="22363@1" Pin0InfoVect0LinkObjId="SW-120156_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f5ce50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-772 4295,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e80bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-831 4245,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22323@0" ObjectIDZND0="22369@0" Pin0InfoVect0LinkObjId="SW-120176_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a9d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-831 4245,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e52000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4669,-698 4669,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1e80e10@0" ObjectIDND1="22375@x" ObjectIDND2="22376@x" ObjectIDZND0="g_1e50710@0" Pin0InfoVect0LinkObjId="g_1e50710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e80e10_0" Pin1InfoVect1LinkObjId="SW-120197_0" Pin1InfoVect2LinkObjId="SW-120198_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4669,-698 4669,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eb7510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4669,-698 4615,-698 4615,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1e50710@0" ObjectIDND1="22375@x" ObjectIDND2="22376@x" ObjectIDZND0="g_1e80e10@0" Pin0InfoVect0LinkObjId="g_1e80e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e50710_0" Pin1InfoVect1LinkObjId="SW-120197_0" Pin1InfoVect2LinkObjId="SW-120198_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4669,-698 4615,-698 4615,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eb0bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4669,-786 4711,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="22375@x" ObjectIDND1="22323@0" ObjectIDZND0="22377@0" Pin0InfoVect0LinkObjId="SW-120199_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120197_0" Pin1InfoVect1LinkObjId="g_33a9d40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4669,-786 4711,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eb0e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-786 4747,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f5d8e0@0" ObjectIDZND0="22377@1" Pin0InfoVect0LinkObjId="SW-120199_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f5d8e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-786 4747,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eb1070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4669,-729 4669,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="22375@0" ObjectIDZND0="g_1e50710@0" ObjectIDZND1="g_1e80e10@0" ObjectIDZND2="22376@x" Pin0InfoVect0LinkObjId="g_1e50710_0" Pin0InfoVect1LinkObjId="g_1e80e10_0" Pin0InfoVect2LinkObjId="SW-120198_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120197_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4669,-729 4669,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f23e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-831 3866,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22323@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1f35d30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a9d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-831 3866,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f240f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-762 3866,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1f35d30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f35d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-762 3866,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f58a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3534,-385 3534,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22324@0" ObjectIDZND0="22378@1" Pin0InfoVect0LinkObjId="SW-120200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3240e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3534,-385 3534,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e7c300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3534,-291 3548,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1e84190@0" ObjectIDND1="g_1e84e70@0" ObjectIDND2="22378@x" ObjectIDZND0="22379@0" Pin0InfoVect0LinkObjId="SW-120201_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e84190_0" Pin1InfoVect1LinkObjId="g_1e84e70_0" Pin1InfoVect2LinkObjId="SW-120200_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3534,-291 3548,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e7c560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-291 3584,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1e7c7c0@0" ObjectIDZND0="22379@1" Pin0InfoVect0LinkObjId="SW-120201_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e7c7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-291 3584,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e83f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3534,-273 3495,-273 3495,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="22379@x" ObjectIDND1="22378@x" ObjectIDND2="g_1e84e70@0" ObjectIDZND0="g_1e84190@0" Pin0InfoVect0LinkObjId="g_1e84190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120201_0" Pin1InfoVect1LinkObjId="SW-120200_0" Pin1InfoVect2LinkObjId="g_1e84e70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3534,-273 3495,-273 3495,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_215b870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-308 3708,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22335@x" ObjectIDND1="22342@x" ObjectIDZND0="22336@0" Pin0InfoVect0LinkObjId="SW-119996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119995_0" Pin1InfoVect1LinkObjId="SW-120002_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-308 3708,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_215bad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3757,-307 3744,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_215bd30@0" ObjectIDZND0="22336@1" Pin0InfoVect0LinkObjId="SW-119996_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_215bd30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3757,-307 3744,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2159090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-320 3694,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22335@0" ObjectIDZND0="22336@x" ObjectIDZND1="22342@x" Pin0InfoVect0LinkObjId="SW-119996_0" Pin0InfoVect1LinkObjId="SW-120002_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-320 3694,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21592f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-308 3694,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22336@x" ObjectIDND1="22335@x" ObjectIDZND0="22342@1" Pin0InfoVect0LinkObjId="SW-120002_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119996_0" Pin1InfoVect1LinkObjId="SW-119995_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-308 3694,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e8b120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-202 3709,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="22337@x" ObjectIDND1="g_1f8fff0@0" ObjectIDND2="22341@x" ObjectIDZND0="22340@0" Pin0InfoVect0LinkObjId="SW-120000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-119997_0" Pin1InfoVect1LinkObjId="g_1f8fff0_0" Pin1InfoVect2LinkObjId="SW-120001_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-202 3709,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e8b380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3758,-201 3745,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1e8b5e0@0" ObjectIDZND0="22340@1" Pin0InfoVect0LinkObjId="SW-120000_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e8b5e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3758,-201 3745,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f8fb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-217 3694,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="22337@0" ObjectIDZND0="22340@x" ObjectIDZND1="g_1f8fff0@0" ObjectIDZND2="22341@x" Pin0InfoVect0LinkObjId="SW-120000_0" Pin0InfoVect1LinkObjId="g_1f8fff0_0" Pin0InfoVect2LinkObjId="SW-120001_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-217 3694,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f8fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-202 3677,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="22340@x" ObjectIDND1="22337@x" ObjectIDND2="22341@x" ObjectIDZND0="g_1f8fff0@0" Pin0InfoVect0LinkObjId="g_1f8fff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120000_0" Pin1InfoVect1LinkObjId="SW-119997_0" Pin1InfoVect2LinkObjId="SW-120001_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-202 3677,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ec85b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3534,-293 3534,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="22379@x" ObjectIDND1="22378@x" ObjectIDZND0="g_1e84190@0" ObjectIDZND1="g_1e84e70@0" Pin0InfoVect0LinkObjId="g_1e84190_0" Pin0InfoVect1LinkObjId="g_1e84e70_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120201_0" Pin1InfoVect1LinkObjId="SW-120200_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3534,-293 3534,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ec8810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3534,-273 3534,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1e84190@0" ObjectIDND1="22379@x" ObjectIDND2="22378@x" ObjectIDZND0="g_1e84e70@0" Pin0InfoVect0LinkObjId="g_1e84e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e84190_0" Pin1InfoVect1LinkObjId="SW-120201_0" Pin1InfoVect2LinkObjId="SW-120200_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3534,-273 3534,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ec8a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3534,-293 3534,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="22379@x" ObjectIDND1="g_1e84190@0" ObjectIDND2="g_1e84e70@0" ObjectIDZND0="22378@0" Pin0InfoVect0LinkObjId="SW-120200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120201_0" Pin1InfoVect1LinkObjId="g_1e84190_0" Pin1InfoVect2LinkObjId="g_1e84e70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3534,-293 3534,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e9bc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-122 3708,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="22340@x" ObjectIDND1="22337@x" ObjectIDND2="g_1f8fff0@0" ObjectIDZND0="22341@0" Pin0InfoVect0LinkObjId="SW-120001_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120000_0" Pin1InfoVect1LinkObjId="SW-119997_0" Pin1InfoVect2LinkObjId="g_1f8fff0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-122 3708,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e9bea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3757,-121 3744,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1e9c100@0" ObjectIDZND0="22341@1" Pin0InfoVect0LinkObjId="SW-120001_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e9c100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3757,-121 3744,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e9d3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-202 3694,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22340@x" ObjectIDND1="22337@x" ObjectIDND2="g_1f8fff0@0" ObjectIDZND0="22341@x" ObjectIDZND1="22338@x" Pin0InfoVect0LinkObjId="SW-120001_0" Pin0InfoVect1LinkObjId="SW-119998_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120000_0" Pin1InfoVect1LinkObjId="SW-119997_0" Pin1InfoVect2LinkObjId="g_1f8fff0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-202 3694,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f514c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-122 3694,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="22341@x" ObjectIDND1="22340@x" ObjectIDND2="22337@x" ObjectIDZND0="22338@1" Pin0InfoVect0LinkObjId="SW-119998_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120001_0" Pin1InfoVect1LinkObjId="SW-120000_0" Pin1InfoVect2LinkObjId="SW-119997_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-122 3694,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f0b300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-64 3708,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="22338@x" ObjectIDND1="g_1f0c4b0@0" ObjectIDND2="0@x" ObjectIDZND0="22339@0" Pin0InfoVect0LinkObjId="SW-119999_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-119998_0" Pin1InfoVect1LinkObjId="g_1f0c4b0_0" Pin1InfoVect2LinkObjId="g_1f35d30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-64 3708,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f0b560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3757,-63 3744,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f0b7c0@0" ObjectIDZND0="22339@1" Pin0InfoVect0LinkObjId="SW-119999_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f0b7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3757,-63 3744,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f0c250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-52 3676,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="lightningRod" ObjectIDND0="22339@x" ObjectIDND1="22338@x" ObjectIDND2="0@x" ObjectIDZND0="g_1f0c4b0@0" Pin0InfoVect0LinkObjId="g_1f0c4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-119999_0" Pin1InfoVect1LinkObjId="SW-119998_0" Pin1InfoVect2LinkObjId="g_1f35d30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-52 3676,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f40f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-64 3694,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="22339@x" ObjectIDND1="g_1f0c4b0@0" ObjectIDND2="0@x" ObjectIDZND0="22338@0" Pin0InfoVect0LinkObjId="SW-119998_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-119999_0" Pin1InfoVect1LinkObjId="g_1f0c4b0_0" Pin1InfoVect2LinkObjId="g_1f35d30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-64 3694,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f111c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-330 3868,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22326@x" ObjectIDND1="22380@x" ObjectIDZND0="22327@0" Pin0InfoVect0LinkObjId="SW-119906_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119905_0" Pin1InfoVect1LinkObjId="SW-119911_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-330 3868,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f11420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3916,-330 3904,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2c967b0@0" ObjectIDZND0="22327@1" Pin0InfoVect0LinkObjId="SW-119906_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c967b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3916,-330 3904,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c97a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-339 3854,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22326@0" ObjectIDZND0="22327@x" ObjectIDZND1="22380@x" Pin0InfoVect0LinkObjId="SW-119906_0" Pin0InfoVect1LinkObjId="SW-119911_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119905_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-339 3854,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c97cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-330 3854,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22327@x" ObjectIDND1="22326@x" ObjectIDZND0="22380@1" Pin0InfoVect0LinkObjId="SW-119911_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119906_0" Pin1InfoVect1LinkObjId="SW-119905_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-330 3854,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c97f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-209 3836,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22328@x" ObjectIDND1="22329@x" ObjectIDZND0="g_2c98170@0" Pin0InfoVect0LinkObjId="g_2c98170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119907_0" Pin1InfoVect1LinkObjId="SW-119908_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-209 3836,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e02060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-224 3854,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22328@0" ObjectIDZND0="22329@x" ObjectIDZND1="g_2c98170@0" Pin0InfoVect0LinkObjId="SW-119908_0" Pin0InfoVect1LinkObjId="g_2c98170_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119907_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-224 3854,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e022c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-209 3854,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22328@x" ObjectIDND1="g_2c98170@0" ObjectIDZND0="22329@1" Pin0InfoVect0LinkObjId="SW-119908_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119907_0" Pin1InfoVect1LinkObjId="g_2c98170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-209 3854,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e02520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-77 3836,-77 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33620@x" ObjectIDND1="22329@x" ObjectIDZND0="g_2e02780@0" Pin0InfoVect0LinkObjId="g_2e02780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BL.052Ld_0" Pin1InfoVect1LinkObjId="SW-119908_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-77 3836,-77 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_324d310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-7 3854,-77 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33620@0" ObjectIDZND0="22329@x" ObjectIDZND1="g_2e02780@0" Pin0InfoVect0LinkObjId="SW-119908_0" Pin0InfoVect1LinkObjId="g_2e02780_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BL.052Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-7 3854,-77 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_324d570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-77 3854,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33620@x" ObjectIDND1="g_2e02780@0" ObjectIDZND0="22329@0" Pin0InfoVect0LinkObjId="SW-119908_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BL.052Ld_0" Pin1InfoVect1LinkObjId="g_2e02780_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3854,-77 3854,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_324e040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-52 3694,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1f0c4b0@0" ObjectIDND1="0@x" ObjectIDZND0="22339@x" ObjectIDZND1="22338@x" Pin0InfoVect0LinkObjId="SW-119999_0" Pin0InfoVect1LinkObjId="SW-119998_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f0c4b0_0" Pin1InfoVect1LinkObjId="g_1f35d30_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-52 3694,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_324e2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-34 3694,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1f0c4b0@0" ObjectIDZND1="22339@x" ObjectIDZND2="22338@x" Pin0InfoVect0LinkObjId="g_1f0c4b0_0" Pin0InfoVect1LinkObjId="SW-119999_0" Pin0InfoVect2LinkObjId="SW-119998_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f35d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-34 3694,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1edc7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-294 4014,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22352@0" ObjectIDZND0="22350@1" Pin0InfoVect0LinkObjId="SW-120053_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120057_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-294 4014,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e8dfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-385 4014,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22324@0" ObjectIDZND0="22348@1" Pin0InfoVect0LinkObjId="SW-120051_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3240e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-385 4014,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffb730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-331 4028,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22348@x" ObjectIDND1="22352@x" ObjectIDZND0="22349@0" Pin0InfoVect0LinkObjId="SW-120052_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120051_0" Pin1InfoVect1LinkObjId="SW-120057_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-331 4028,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffb990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-331 4064,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1ffbbf0@0" ObjectIDZND0="22349@1" Pin0InfoVect0LinkObjId="SW-120052_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ffbbf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-331 4064,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffc680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-340 4014,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22348@0" ObjectIDZND0="22349@x" ObjectIDZND1="22352@x" Pin0InfoVect0LinkObjId="SW-120052_0" Pin0InfoVect1LinkObjId="SW-120057_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120051_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-340 4014,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffc8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-331 4014,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22349@x" ObjectIDND1="22348@x" ObjectIDZND0="22352@1" Pin0InfoVect0LinkObjId="SW-120057_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120052_0" Pin1InfoVect1LinkObjId="SW-120051_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-331 4014,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffcb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-210 3996,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22350@x" ObjectIDND1="22351@x" ObjectIDZND0="g_1ffcda0@0" Pin0InfoVect0LinkObjId="g_1ffcda0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120053_0" Pin1InfoVect1LinkObjId="SW-120054_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-210 3996,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffdb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-225 4014,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22350@0" ObjectIDZND0="22351@x" ObjectIDZND1="g_1ffcda0@0" Pin0InfoVect0LinkObjId="SW-120054_0" Pin0InfoVect1LinkObjId="g_1ffcda0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120053_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-225 4014,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffddb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-210 4014,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22350@x" ObjectIDND1="g_1ffcda0@0" ObjectIDZND0="22351@1" Pin0InfoVect0LinkObjId="SW-120054_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120053_0" Pin1InfoVect1LinkObjId="g_1ffcda0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-210 4014,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ffe010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-78 3996,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33621@x" ObjectIDND1="22351@x" ObjectIDZND0="g_2c9ddc0@0" Pin0InfoVect0LinkObjId="g_2c9ddc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BL.053Ld_0" Pin1InfoVect1LinkObjId="SW-120054_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-78 3996,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c9ea30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-8 4014,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33621@0" ObjectIDZND0="22351@x" ObjectIDZND1="g_2c9ddc0@0" Pin0InfoVect0LinkObjId="SW-120054_0" Pin0InfoVect1LinkObjId="g_2c9ddc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BL.053Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-8 4014,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c9ec90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-78 4014,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33621@x" ObjectIDND1="g_2c9ddc0@0" ObjectIDZND0="22351@0" Pin0InfoVect0LinkObjId="SW-120054_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BL.053Ld_0" Pin1InfoVect1LinkObjId="g_2c9ddc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4014,-78 4014,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32a43a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-294 4174,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22347@0" ObjectIDZND0="22345@1" Pin0InfoVect0LinkObjId="SW-120008_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120012_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-294 4174,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc3b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-385 4174,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22324@0" ObjectIDZND0="22343@1" Pin0InfoVect0LinkObjId="SW-120006_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3240e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-385 4174,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d29430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-331 4188,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22343@x" ObjectIDND1="22347@x" ObjectIDZND0="22344@0" Pin0InfoVect0LinkObjId="SW-120007_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120006_0" Pin1InfoVect1LinkObjId="SW-120012_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-331 4188,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d29690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4236,-331 4224,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d298f0@0" ObjectIDZND0="22344@1" Pin0InfoVect0LinkObjId="SW-120007_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d298f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4236,-331 4224,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2a380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-340 4174,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22343@0" ObjectIDZND0="22344@x" ObjectIDZND1="22347@x" Pin0InfoVect0LinkObjId="SW-120007_0" Pin0InfoVect1LinkObjId="SW-120012_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-340 4174,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2a5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-331 4174,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22343@x" ObjectIDND1="22344@x" ObjectIDZND0="22347@1" Pin0InfoVect0LinkObjId="SW-120012_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120006_0" Pin1InfoVect1LinkObjId="SW-120007_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-331 4174,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2a840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-210 4156,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22345@x" ObjectIDND1="22346@x" ObjectIDZND0="g_2d2aaa0@0" Pin0InfoVect0LinkObjId="g_2d2aaa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120008_0" Pin1InfoVect1LinkObjId="SW-120009_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-210 4156,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2c020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-225 4174,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22345@0" ObjectIDZND0="22346@x" ObjectIDZND1="g_2d2aaa0@0" Pin0InfoVect0LinkObjId="SW-120009_0" Pin0InfoVect1LinkObjId="g_2d2aaa0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-225 4174,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2c280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-210 4174,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22345@x" ObjectIDND1="g_2d2aaa0@0" ObjectIDZND0="22346@1" Pin0InfoVect0LinkObjId="SW-120009_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120008_0" Pin1InfoVect1LinkObjId="g_2d2aaa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-210 4174,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2c4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-78 4156,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33622@x" ObjectIDND1="22346@x" ObjectIDZND0="g_2d2c740@0" Pin0InfoVect0LinkObjId="g_2d2c740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BL.054Ld_0" Pin1InfoVect1LinkObjId="SW-120009_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-78 4156,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2d4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-8 4174,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33622@0" ObjectIDZND0="22346@x" ObjectIDZND1="g_2d2c740@0" Pin0InfoVect0LinkObjId="SW-120009_0" Pin0InfoVect1LinkObjId="g_2d2c740_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BL.054Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-8 4174,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d2d750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-78 4174,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33622@x" ObjectIDND1="g_2d2c740@0" ObjectIDZND0="22346@0" Pin0InfoVect0LinkObjId="SW-120009_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BL.054Ld_0" Pin1InfoVect1LinkObjId="g_2d2c740_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-78 4174,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d1f950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-292 4334,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22334@0" ObjectIDZND0="22332@1" Pin0InfoVect0LinkObjId="SW-119952_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119956_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-292 4334,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d223b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-385 4334,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22324@0" ObjectIDZND0="22330@1" Pin0InfoVect0LinkObjId="SW-119950_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3240e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-385 4334,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_377f380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-329 4348,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22330@x" ObjectIDND1="22334@x" ObjectIDZND0="22331@0" Pin0InfoVect0LinkObjId="SW-119951_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119950_0" Pin1InfoVect1LinkObjId="SW-119956_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-329 4348,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_377f5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-329 4384,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_377f840@0" ObjectIDZND0="22331@1" Pin0InfoVect0LinkObjId="SW-119951_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_377f840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-329 4384,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37802d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-338 4334,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22330@0" ObjectIDZND0="22331@x" ObjectIDZND1="22334@x" Pin0InfoVect0LinkObjId="SW-119951_0" Pin0InfoVect1LinkObjId="SW-119956_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-338 4334,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3780530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-329 4334,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22330@x" ObjectIDND1="22331@x" ObjectIDZND0="22334@1" Pin0InfoVect0LinkObjId="SW-119956_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119950_0" Pin1InfoVect1LinkObjId="SW-119951_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-329 4334,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3780790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-208 4316,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="22332@x" ObjectIDND1="0@x" ObjectIDND2="g_3447e10@0" ObjectIDZND0="g_37809f0@0" Pin0InfoVect0LinkObjId="g_37809f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-119952_0" Pin1InfoVect1LinkObjId="g_1f35d30_0" Pin1InfoVect2LinkObjId="g_3447e10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-208 4316,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34449f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-223 4334,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="22332@0" ObjectIDZND0="g_37809f0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_3447e10@0" Pin0InfoVect0LinkObjId="g_37809f0_0" Pin0InfoVect1LinkObjId="g_1f35d30_0" Pin0InfoVect2LinkObjId="g_3447e10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-119952_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-223 4334,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3444c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-76 4316,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33623@x" ObjectIDND1="22333@x" ObjectIDZND0="g_3444eb0@0" Pin0InfoVect0LinkObjId="g_3444eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BL.055Ld_0" Pin1InfoVect1LinkObjId="SW-119953_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-76 4316,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3445c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-6 4334,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33623@0" ObjectIDZND0="22333@x" ObjectIDZND1="g_3444eb0@0" Pin0InfoVect0LinkObjId="SW-119953_0" Pin0InfoVect1LinkObjId="g_3444eb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BL.055Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-6 4334,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3445ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-76 4334,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33623@x" ObjectIDND1="g_3444eb0@0" ObjectIDZND0="22333@0" Pin0InfoVect0LinkObjId="SW-119953_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BL.055Ld_0" Pin1InfoVect1LinkObjId="g_3444eb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-76 4334,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344be30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4414,-166 4414,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3447e10@0" ObjectIDND1="22332@x" ObjectIDND2="g_37809f0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1f35d30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3447e10_0" Pin1InfoVect1LinkObjId="SW-119952_0" Pin1InfoVect2LinkObjId="g_37809f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4414,-166 4414,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_341c580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4414,-100 4414,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1f35d30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f35d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4414,-100 4414,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_341f0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-166 4414,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22332@x" ObjectIDND1="g_37809f0@0" ObjectIDND2="22333@x" ObjectIDZND0="0@x" ObjectIDZND1="g_3447e10@0" Pin0InfoVect0LinkObjId="g_1f35d30_0" Pin0InfoVect1LinkObjId="g_3447e10_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-119952_0" Pin1InfoVect1LinkObjId="g_37809f0_0" Pin1InfoVect2LinkObjId="SW-119953_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-166 4414,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_341f2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4414,-166 4424,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="22332@x" ObjectIDND2="g_37809f0@0" ObjectIDZND0="g_3447e10@0" Pin0InfoVect0LinkObjId="g_3447e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f35d30_0" Pin1InfoVect1LinkObjId="SW-119952_0" Pin1InfoVect2LinkObjId="g_37809f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4414,-166 4424,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34262e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4542,-385 4542,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22324@0" ObjectIDZND0="22355@1" Pin0InfoVect0LinkObjId="SW-120095_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3240e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4542,-385 4542,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3429310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4702,-355 4702,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22353@1" ObjectIDZND0="22325@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120093_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4702,-355 4702,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342baa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4542,-297 4556,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22355@x" ObjectIDND1="22357@x" ObjectIDZND0="22356@0" Pin0InfoVect0LinkObjId="SW-120096_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120095_0" Pin1InfoVect1LinkObjId="SW-120097_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4542,-297 4556,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342bd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-297 4592,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_342bf60@0" ObjectIDZND0="22356@1" Pin0InfoVect0LinkObjId="SW-120096_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_342bf60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-297 4592,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342d280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4542,-319 4542,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22355@0" ObjectIDZND0="22356@x" ObjectIDZND1="22357@x" Pin0InfoVect0LinkObjId="SW-120096_0" Pin0InfoVect1LinkObjId="SW-120097_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120095_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4542,-319 4542,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342d4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4542,-297 4542,-233 4612,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22356@x" ObjectIDND1="22355@x" ObjectIDZND0="22357@1" Pin0InfoVect0LinkObjId="SW-120097_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120096_0" Pin1InfoVect1LinkObjId="SW-120095_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4542,-297 4542,-233 4612,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3783620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4702,-296 4716,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22357@x" ObjectIDND1="22353@x" ObjectIDZND0="22354@0" Pin0InfoVect0LinkObjId="SW-120094_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120097_0" Pin1InfoVect1LinkObjId="SW-120093_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4702,-296 4716,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3783880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-296 4752,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3783ae0@0" ObjectIDZND0="22354@1" Pin0InfoVect0LinkObjId="SW-120094_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3783ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-296 4752,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3784e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4639,-233 4702,-233 4702,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22357@0" ObjectIDZND0="22354@x" ObjectIDZND1="22353@x" Pin0InfoVect0LinkObjId="SW-120094_0" Pin0InfoVect1LinkObjId="SW-120093_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120097_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4639,-233 4702,-233 4702,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3785060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4702,-296 4702,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22354@x" ObjectIDND1="22357@x" ObjectIDZND0="22353@0" Pin0InfoVect0LinkObjId="SW-120093_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120094_0" Pin1InfoVect1LinkObjId="SW-120097_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4702,-296 4702,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3787ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-292 4878,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22381@0" ObjectIDZND0="22360@1" Pin0InfoVect0LinkObjId="SW-120103_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-292 4878,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_378a520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-384 4878,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22325@0" ObjectIDZND0="22358@1" Pin0InfoVect0LinkObjId="SW-120101_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3429310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-384 4878,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3792530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-329 4892,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="22358@x" ObjectIDND1="22381@x" ObjectIDZND0="22359@0" Pin0InfoVect0LinkObjId="SW-120102_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120101_0" Pin1InfoVect1LinkObjId="SW-120107_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-329 4892,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3792790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4940,-329 4928,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3795850@0" ObjectIDZND0="22359@1" Pin0InfoVect0LinkObjId="SW-120102_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3795850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4940,-329 4928,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37929f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-338 4878,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="22358@0" ObjectIDZND0="22359@x" ObjectIDZND1="22381@x" Pin0InfoVect0LinkObjId="SW-120102_0" Pin0InfoVect1LinkObjId="SW-120107_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120101_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-338 4878,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3792c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-329 4878,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="22358@x" ObjectIDND1="22359@x" ObjectIDZND0="22381@1" Pin0InfoVect0LinkObjId="SW-120107_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120101_0" Pin1InfoVect1LinkObjId="SW-120102_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-329 4878,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3792eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-208 4860,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="22360@x" ObjectIDND1="22361@x" ObjectIDZND0="g_3793110@0" Pin0InfoVect0LinkObjId="g_3793110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120103_0" Pin1InfoVect1LinkObjId="SW-120104_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-208 4860,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3793ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-223 4878,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22360@0" ObjectIDZND0="g_3793110@0" ObjectIDZND1="22361@x" Pin0InfoVect0LinkObjId="g_3793110_0" Pin0InfoVect1LinkObjId="SW-120104_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120103_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-223 4878,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3794120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-208 4878,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22360@x" ObjectIDND1="g_3793110@0" ObjectIDZND0="22361@1" Pin0InfoVect0LinkObjId="SW-120104_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120103_0" Pin1InfoVect1LinkObjId="g_3793110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-208 4878,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3794380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-76 4860,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33626@x" ObjectIDND1="22361@x" ObjectIDZND0="g_37945e0@0" Pin0InfoVect0LinkObjId="g_37945e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BL.061Ld_0" Pin1InfoVect1LinkObjId="SW-120104_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-76 4860,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3795390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-6 4878,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33626@0" ObjectIDZND0="g_37945e0@0" ObjectIDZND1="22361@x" Pin0InfoVect0LinkObjId="g_37945e0_0" Pin0InfoVect1LinkObjId="SW-120104_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BL.061Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-6 4878,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37955f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-76 4878,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33626@x" ObjectIDND1="g_37945e0@0" ObjectIDZND0="22361@0" Pin0InfoVect0LinkObjId="SW-120104_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BL.061Ld_0" Pin1InfoVect1LinkObjId="g_37945e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-76 4878,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a9d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-819 4245,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22362@1" ObjectIDZND0="22323@0" Pin0InfoVect0LinkObjId="g_33c8c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120155_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-819 4245,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aad80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-208 4334,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="22332@x" ObjectIDND1="g_37809f0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3447e10@0" ObjectIDZND2="22333@x" Pin0InfoVect0LinkObjId="g_1f35d30_0" Pin0InfoVect1LinkObjId="g_3447e10_0" Pin0InfoVect2LinkObjId="SW-119953_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-119952_0" Pin1InfoVect1LinkObjId="g_37809f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-208 4334,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aafe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-166 4334,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3447e10@0" ObjectIDND2="22332@x" ObjectIDZND0="22333@1" Pin0InfoVect0LinkObjId="SW-119953_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f35d30_0" Pin1InfoVect1LinkObjId="g_3447e10_0" Pin1InfoVect2LinkObjId="SW-119952_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-166 4334,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ac6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-291 5038,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30748@0" ObjectIDZND0="30746@1" Pin0InfoVect0LinkObjId="SW-201302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-201306_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-291 5038,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33aee80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-384 5038,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22325@0" ObjectIDZND0="30744@1" Pin0InfoVect0LinkObjId="SW-201300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3429310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-384 5038,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b6e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-327 5052,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="30744@x" ObjectIDND1="30748@x" ObjectIDZND0="30745@0" Pin0InfoVect0LinkObjId="SW-201301_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-201300_0" Pin1InfoVect1LinkObjId="SW-201306_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-327 5052,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b70f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5100,-327 5088,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33ba1b0@0" ObjectIDZND0="30745@1" Pin0InfoVect0LinkObjId="SW-201301_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33ba1b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5100,-327 5088,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b7350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-336 5038,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30744@0" ObjectIDZND0="30745@x" ObjectIDZND1="30748@x" Pin0InfoVect0LinkObjId="SW-201301_0" Pin0InfoVect1LinkObjId="SW-201306_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-201300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-336 5038,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b75b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-327 5038,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="30744@x" ObjectIDND1="30745@x" ObjectIDZND0="30748@1" Pin0InfoVect0LinkObjId="SW-201306_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-201300_0" Pin1InfoVect1LinkObjId="SW-201301_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-327 5038,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b7810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-206 5020,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="30746@x" ObjectIDND1="30747@x" ObjectIDZND0="g_33b7a70@0" Pin0InfoVect0LinkObjId="g_33b7a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-201302_0" Pin1InfoVect1LinkObjId="SW-201303_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-206 5020,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b8820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-221 5038,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="30746@0" ObjectIDZND0="g_33b7a70@0" ObjectIDZND1="30747@x" Pin0InfoVect0LinkObjId="g_33b7a70_0" Pin0InfoVect1LinkObjId="SW-201303_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-201302_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-221 5038,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b8a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-206 5038,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="30746@x" ObjectIDND1="g_33b7a70@0" ObjectIDZND0="30747@1" Pin0InfoVect0LinkObjId="SW-201303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-201302_0" Pin1InfoVect1LinkObjId="g_33b7a70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-206 5038,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b8ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-74 5020,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33627@x" ObjectIDND1="30747@x" ObjectIDZND0="g_33b8f40@0" Pin0InfoVect0LinkObjId="g_33b8f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BL.062Ld_0" Pin1InfoVect1LinkObjId="SW-201303_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-74 5020,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b9cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-4 5038,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33627@0" ObjectIDZND0="g_33b8f40@0" ObjectIDZND1="30747@x" Pin0InfoVect0LinkObjId="g_33b8f40_0" Pin0InfoVect1LinkObjId="SW-201303_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_BL.062Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-4 5038,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b9f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-74 5038,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33627@x" ObjectIDND1="g_33b8f40@0" ObjectIDZND0="30747@0" Pin0InfoVect0LinkObjId="SW-201303_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_BL.062Ld_0" Pin1InfoVect1LinkObjId="g_33b8f40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5038,-74 5038,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c8a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4669,-765 4669,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="22375@1" ObjectIDZND0="22377@x" ObjectIDZND1="22323@0" Pin0InfoVect0LinkObjId="SW-120199_0" Pin0InfoVect1LinkObjId="g_33a9d40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120197_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4669,-765 4669,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c8c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4669,-786 4669,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="22377@x" ObjectIDND1="22375@x" ObjectIDZND0="22323@0" Pin0InfoVect0LinkObjId="g_33a9d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120199_0" Pin1InfoVect1LinkObjId="SW-120197_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4669,-786 4669,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cb380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4668,-698 4710,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1e50710@0" ObjectIDND1="g_1e80e10@0" ObjectIDND2="22375@x" ObjectIDZND0="22376@0" Pin0InfoVect0LinkObjId="SW-120198_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e50710_0" Pin1InfoVect1LinkObjId="g_1e80e10_0" Pin1InfoVect2LinkObjId="SW-120197_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4668,-698 4710,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cb5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4758,-698 4746,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33cbd30@0" ObjectIDZND0="22376@1" Pin0InfoVect0LinkObjId="SW-120198_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33cbd30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4758,-698 4746,-698 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="22324" cx="3854" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22324" cx="3534" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22324" cx="4014" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22324" cx="4334" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22324" cx="4174" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22324" cx="3694" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22324" cx="4542" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22323" cx="3866" cy="-831" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22323" cx="4245" cy="-831" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22323" cx="4245" cy="-831" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22324" cx="4245" cy="-385" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22325" cx="4702" cy="-384" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22325" cx="4878" cy="-384" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22325" cx="5038" cy="-384" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22323" cx="4669" cy="-831" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-119707" type="2">
    <use transform="matrix(1.387097 -0.000000 0.000000 -1.233333 3374.693548 -987.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22304" ObjectName="DYN-WD_BL"/>
     <cge:Meas_Ref ObjectId="119707"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1f31340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -943.000000) translate(0,20)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1f31340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -943.000000) translate(0,45)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1f31340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -943.000000) translate(0,70)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1f31340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -943.000000) translate(0,95)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1f31340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -943.000000) translate(0,120)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1f31340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -943.000000) translate(0,145)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1f31340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -943.000000) translate(0,170)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1f31340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -943.000000) translate(0,195)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1f31340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -943.000000) translate(0,220)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="50" graphid="g_31b7e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3119.000000 -1112.500000) translate(0,40)">白路变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3259470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -724.000000) translate(0,20)">1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3259470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -724.000000) translate(0,45)">S11-2500/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3259470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -724.000000) translate(0,70)">2500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3259470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -724.000000) translate(0,95)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3259470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -724.000000) translate(0,120)">Y/△-11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_3259470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -724.000000) translate(0,145)">Ud=7.02%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1f9ada0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -580.000000) translate(0,20)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2bd1900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4210.000000 -1196.000000) translate(0,20)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2bd1900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4210.000000 -1196.000000) translate(0,45)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2bd1900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4210.000000 -1196.000000) translate(0,70)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2bd1900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4210.000000 -1196.000000) translate(0,95)">白</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2bd1900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4210.000000 -1196.000000) translate(0,120)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1e9b380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3796.000000 -628.000000) translate(0,20)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1e828c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3423.000000 -163.000000) translate(0,20)">10kVⅠ母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_341e340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4405.000000 26.000000) translate(0,20)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_341f4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4421.500000 57.000000) translate(0,20)">50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_3797c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 -423.000000) translate(0,24)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_37999a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3375.000000 -426.000000) translate(0,24)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_379a290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4610.000000 -871.000000) translate(0,24)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_379aad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3715.000000 -339.000000) translate(0,16)">05117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379b850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3626.500000 -366.000000) translate(0,20)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_379bcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -242.000000) translate(0,16)">05127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379c140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3623.500000 -258.000000) translate(0,20)">0512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_379c3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -95.000000) translate(0,16)">05130</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_379c8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3710.000000 -152.000000) translate(0,16)">05137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379cb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3629.000000 -107.000000) translate(0,20)">0513</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379cd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3638.500000 -298.000000) translate(0,20)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_379cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4355.000000 -364.000000) translate(0,16)">05517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379d200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -374.000000) translate(0,20)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379d440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -258.000000) translate(0,20)">0552</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379d680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4262.000000 -125.000000) translate(0,20)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_379d8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4032.000000 -363.000000) translate(0,16)">05317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379db00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.000000 -366.000000) translate(0,20)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379dd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3947.000000 -259.000000) translate(0,20)">0532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379df80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3944.000000 -126.000000) translate(0,20)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379e1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3956.000000 -318.000000) translate(0,20)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_379e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3872.000000 -364.000000) translate(0,16)">05217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379e640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3785.000000 -371.000000) translate(0,20)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379e880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3786.000000 -262.000000) translate(0,20)">0522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379eac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3796.000000 -318.000000) translate(0,20)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379ed00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3787.000000 -130.000000) translate(0,20)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_379ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4189.000000 -361.000000) translate(0,16)">05417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379f480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4110.000000 -363.000000) translate(0,20)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379f9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4104.000000 -257.000000) translate(0,20)">0542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379fc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -130.000000) translate(0,20)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_379fe80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4116.000000 -318.000000) translate(0,20)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_37a00c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4563.000000 -330.000000) translate(0,16)">01617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a0600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4475.000000 -363.000000) translate(0,20)">0161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_37a0860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4718.000000 -329.000000) translate(0,16)">01627</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a0aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4714.000000 -363.000000) translate(0,20)">0162</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a0ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4599.000000 -276.000000) translate(0,20)">016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_37a0f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3559.000000 -327.000000) translate(0,16)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a1460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3461.000000 -344.000000) translate(0,20)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_37a19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4895.000000 -359.000000) translate(0,16)">06117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a1c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4813.000000 -366.000000) translate(0,20)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a1e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4808.000000 -258.000000) translate(0,20)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a20a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4806.000000 -126.000000) translate(0,20)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a22e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -318.000000) translate(0,20)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_37a2520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -501.000000) translate(0,16)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a2760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -492.000000) translate(0,20)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a29a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -546.000000) translate(0,20)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a2be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -437.000000) translate(0,20)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a2e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4184.000000 -757.000000) translate(0,20)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_37a3060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -803.000000) translate(0,16)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a32a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4173.000000 -819.000000) translate(0,20)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_37a34e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4266.000000 -892.000000) translate(0,16)">35117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a3720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4172.000000 -891.000000) translate(0,20)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_37a3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4268.000000 -946.000000) translate(0,16)">35160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_37a3ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4262.000000 -1007.000000) translate(0,16)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a3de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -1006.000000) translate(0,20)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a4020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4187.000000 -938.000000) translate(0,20)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_37a4260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4707.311108 -814.000000) translate(0,16)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_37a44a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4604.311108 -766.000000) translate(0,20)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="32" graphid="g_37a46e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4299.000000 -684.000000) translate(0,26)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33a50d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -22.000000) translate(0,20)">1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33a50d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -22.000000) translate(0,45)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33a50d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -22.000000) translate(0,70)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33a50d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -22.000000) translate(0,95)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33a5f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3790.247853 -792.000000) translate(0,20)">3551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33a8ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.000000 -316.000000) translate(0,20)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33bac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5055.000000 -357.000000) translate(0,16)">06217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33bb270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4973.000000 -364.000000) translate(0,20)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33bb4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4968.000000 -256.000000) translate(0,20)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33bb6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4966.000000 -124.000000) translate(0,20)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33bb930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4980.000000 -317.000000) translate(0,20)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_33c1c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3072.000000 -572.000000) translate(0,24)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_33c2e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3339.000000 -1085.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_33c41c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3339.000000 -1120.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,16)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,76)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,96)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,136)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,156)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,196)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,216)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,256)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,276)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,296)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,316)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c4870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3058.000000 -419.000000) translate(0,336)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33c6bc0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3162.142857 -97.800000) translate(0,16)">8831018</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_33c70d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3813.000000 -600.000000) translate(0,20)">50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33cb840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4708.311108 -729.000000) translate(0,16)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336ce80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3814.000000 -49.000000) translate(0,20)">关</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336ce80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3814.000000 -49.000000) translate(0,45)">坡</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336ce80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3814.000000 -49.000000) translate(0,70)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336d6b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3983.000000 -55.000000) translate(0,20)">岔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336d6b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3983.000000 -55.000000) translate(0,45)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336d6b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3983.000000 -55.000000) translate(0,70)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336df30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4139.000000 -60.000000) translate(0,20)">石</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336df30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4139.000000 -60.000000) translate(0,45)">腊</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336df30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4139.000000 -60.000000) translate(0,70)">它</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336df30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4139.000000 -60.000000) translate(0,95)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336eab0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4301.000000 -61.000000) translate(0,20)">白</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336eab0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4301.000000 -61.000000) translate(0,45)">路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336eab0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4301.000000 -61.000000) translate(0,70)">集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336eab0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4301.000000 -61.000000) translate(0,95)">镇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336eab0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4301.000000 -61.000000) translate(0,120)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336f670" transform="matrix(1.000000 0.000000 0.000000 1.000000 4843.000000 -53.000000) translate(0,20)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336f670" transform="matrix(1.000000 0.000000 0.000000 1.000000 4843.000000 -53.000000) translate(0,45)">合</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336f670" transform="matrix(1.000000 0.000000 0.000000 1.000000 4843.000000 -53.000000) translate(0,70)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336ff30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4998.000000 -48.000000) translate(0,20)">洒</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336ff30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4998.000000 -48.000000) translate(0,45)">胶</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336ff30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4998.000000 -48.000000) translate(0,70)">泥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_336ff30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4998.000000 -48.000000) translate(0,95)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3370ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3025.000000 -51.500000) translate(0,16)">武定巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3371f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -71.500000) translate(0,16)">18787878990</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3371f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -71.500000) translate(0,36)">18787842893</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3371f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -71.500000) translate(0,56)">13987880311</text>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-150 3687,-135 3702,-135 3694,-150 3694,-150 3694,-150 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-178 3687,-193 3702,-193 3694,-178 3694,-178 3694,-178 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-581 4238,-566 4253,-566 4245,-581 4245,-581 4245,-581 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-590 4238,-605 4253,-605 4245,-590 4245,-590 4245,-590 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-157 3847,-142 3862,-142 3854,-157 3854,-157 3854,-157 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3854,-180 3847,-195 3862,-195 3854,-180 3854,-180 3854,-180 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-158 4007,-143 4022,-143 4014,-158 4014,-158 4014,-158 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4014,-181 4007,-196 4022,-196 4014,-181 4014,-181 4014,-181 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-158 4167,-143 4182,-143 4174,-158 4174,-158 4174,-158 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-181 4167,-196 4182,-196 4174,-181 4174,-181 4174,-181 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-156 4327,-141 4342,-141 4334,-156 4334,-156 4334,-156 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-179 4327,-194 4342,-194 4334,-179 4334,-179 4334,-179 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-166 4356,-159 4356,-174 4371,-166 4371,-166 4371,-166 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-166 4392,-159 4392,-174 4377,-166 4377,-166 4377,-166 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-156 4871,-141 4886,-141 4878,-156 4878,-156 4878,-156 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-179 4871,-194 4886,-194 4878,-179 4878,-179 4878,-179 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-154 5031,-139 5046,-139 5038,-154 5038,-154 5038,-154 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5038,-177 5031,-192 5046,-192 5038,-177 5038,-177 5038,-177 " stroke="rgb(0,255,0)"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-120155">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -778.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22362" ObjectName="SW-WD_BL.WD_BL_3011SW"/>
     <cge:Meas_Ref ObjectId="120155"/>
    <cge:TPSR_Ref TObjectID="22362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120160">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -506.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22364" ObjectName="SW-WD_BL.WD_BL_0016SW"/>
     <cge:Meas_Ref ObjectId="120160"/>
    <cge:TPSR_Ref TObjectID="22364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120161">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -403.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22365" ObjectName="SW-WD_BL.WD_BL_0011SW"/>
     <cge:Meas_Ref ObjectId="120161"/>
    <cge:TPSR_Ref TObjectID="22365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 -212.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22337" ObjectName="SW-WD_BL.WD_BL_0512SW"/>
     <cge:Meas_Ref ObjectId="119997"/>
    <cge:TPSR_Ref TObjectID="22337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119995">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 -315.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22335" ObjectName="SW-WD_BL.WD_BL_0511SW"/>
     <cge:Meas_Ref ObjectId="119995"/>
    <cge:TPSR_Ref TObjectID="22335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119998">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 -70.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22338" ObjectName="SW-WD_BL.WD_BL_0513SW"/>
     <cge:Meas_Ref ObjectId="119998"/>
    <cge:TPSR_Ref TObjectID="22338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119907">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3845.000000 -219.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22328" ObjectName="SW-WD_BL.WD_BL_0522SW"/>
     <cge:Meas_Ref ObjectId="119907"/>
    <cge:TPSR_Ref TObjectID="22328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119905">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3845.000000 -334.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22326" ObjectName="SW-WD_BL.WD_BL_0521SW"/>
     <cge:Meas_Ref ObjectId="119905"/>
    <cge:TPSR_Ref TObjectID="22326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119908">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3845.000000 -84.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22329" ObjectName="SW-WD_BL.WD_BL_0526SW"/>
     <cge:Meas_Ref ObjectId="119908"/>
    <cge:TPSR_Ref TObjectID="22329"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120176">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -843.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22369" ObjectName="SW-WD_BL.WD_BL_3511SW"/>
     <cge:Meas_Ref ObjectId="120176"/>
    <cge:TPSR_Ref TObjectID="22369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120178">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -966.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22371" ObjectName="SW-WD_BL.WD_BL_3516SW"/>
     <cge:Meas_Ref ObjectId="120178"/>
    <cge:TPSR_Ref TObjectID="22371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120177">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -895.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22370" ObjectName="SW-WD_BL.WD_BL_35117SW"/>
     <cge:Meas_Ref ObjectId="120177"/>
    <cge:TPSR_Ref TObjectID="22370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120179">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -951.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22372" ObjectName="SW-WD_BL.WD_BL_35160SW"/>
     <cge:Meas_Ref ObjectId="120179"/>
    <cge:TPSR_Ref TObjectID="22372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120180">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -1013.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22373" ObjectName="SW-WD_BL.WD_BL_35167SW"/>
     <cge:Meas_Ref ObjectId="120180"/>
    <cge:TPSR_Ref TObjectID="22373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120162">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -452.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22366" ObjectName="SW-WD_BL.WD_BL_00117SW"/>
     <cge:Meas_Ref ObjectId="120162"/>
    <cge:TPSR_Ref TObjectID="22366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120156">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -767.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22363" ObjectName="SW-WD_BL.WD_BL_30117SW"/>
     <cge:Meas_Ref ObjectId="120156"/>
    <cge:TPSR_Ref TObjectID="22363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120199">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.311108 -781.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22377" ObjectName="SW-WD_BL.WD_BL_39010SW"/>
     <cge:Meas_Ref ObjectId="120199"/>
    <cge:TPSR_Ref TObjectID="22377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120197">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4660.311108 -724.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22375" ObjectName="SW-WD_BL.WD_BL_3901SW"/>
     <cge:Meas_Ref ObjectId="120197"/>
    <cge:TPSR_Ref TObjectID="22375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.220076 -757.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120200">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3525.000000 -299.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22378" ObjectName="SW-WD_BL.WD_BL_0901SW"/>
     <cge:Meas_Ref ObjectId="120200"/>
    <cge:TPSR_Ref TObjectID="22378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120201">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 -286.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22379" ObjectName="SW-WD_BL.WD_BL_09017SW"/>
     <cge:Meas_Ref ObjectId="120201"/>
    <cge:TPSR_Ref TObjectID="22379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3703.000000 -303.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22336" ObjectName="SW-WD_BL.WD_BL_05117SW"/>
     <cge:Meas_Ref ObjectId="119996"/>
    <cge:TPSR_Ref TObjectID="22336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120000">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -197.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22340" ObjectName="SW-WD_BL.WD_BL_05127SW"/>
     <cge:Meas_Ref ObjectId="120000"/>
    <cge:TPSR_Ref TObjectID="22340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120001">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3703.000000 -117.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22341" ObjectName="SW-WD_BL.WD_BL_05137SW"/>
     <cge:Meas_Ref ObjectId="120001"/>
    <cge:TPSR_Ref TObjectID="22341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119999">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3703.000000 -59.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22339" ObjectName="SW-WD_BL.WD_BL_05130SW"/>
     <cge:Meas_Ref ObjectId="119999"/>
    <cge:TPSR_Ref TObjectID="22339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119906">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 -325.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22327" ObjectName="SW-WD_BL.WD_BL_05217SW"/>
     <cge:Meas_Ref ObjectId="119906"/>
    <cge:TPSR_Ref TObjectID="22327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120053">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4005.000000 -220.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22350" ObjectName="SW-WD_BL.WD_BL_0532SW"/>
     <cge:Meas_Ref ObjectId="120053"/>
    <cge:TPSR_Ref TObjectID="22350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120051">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4005.000000 -335.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22348" ObjectName="SW-WD_BL.WD_BL_0531SW"/>
     <cge:Meas_Ref ObjectId="120051"/>
    <cge:TPSR_Ref TObjectID="22348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120054">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4005.000000 -85.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22351" ObjectName="SW-WD_BL.WD_BL_0536SW"/>
     <cge:Meas_Ref ObjectId="120054"/>
    <cge:TPSR_Ref TObjectID="22351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120052">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4023.000000 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22349" ObjectName="SW-WD_BL.WD_BL_05317SW"/>
     <cge:Meas_Ref ObjectId="120052"/>
    <cge:TPSR_Ref TObjectID="22349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120008">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -220.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22345" ObjectName="SW-WD_BL.WD_BL_0542SW"/>
     <cge:Meas_Ref ObjectId="120008"/>
    <cge:TPSR_Ref TObjectID="22345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120006">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -335.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22343" ObjectName="SW-WD_BL.WD_BL_0541SW"/>
     <cge:Meas_Ref ObjectId="120006"/>
    <cge:TPSR_Ref TObjectID="22343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120009">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -85.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22346" ObjectName="SW-WD_BL.WD_BL_0546SW"/>
     <cge:Meas_Ref ObjectId="120009"/>
    <cge:TPSR_Ref TObjectID="22346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120007">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22344" ObjectName="SW-WD_BL.WD_BL_05417SW"/>
     <cge:Meas_Ref ObjectId="120007"/>
    <cge:TPSR_Ref TObjectID="22344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119952">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -218.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22332" ObjectName="SW-WD_BL.WD_BL_0552SW"/>
     <cge:Meas_Ref ObjectId="119952"/>
    <cge:TPSR_Ref TObjectID="22332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119950">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -333.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22330" ObjectName="SW-WD_BL.WD_BL_0551SW"/>
     <cge:Meas_Ref ObjectId="119950"/>
    <cge:TPSR_Ref TObjectID="22330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119953">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -83.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22333" ObjectName="SW-WD_BL.WD_BL_0556SW"/>
     <cge:Meas_Ref ObjectId="119953"/>
    <cge:TPSR_Ref TObjectID="22333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-119951">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -324.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22331" ObjectName="SW-WD_BL.WD_BL_05517SW"/>
     <cge:Meas_Ref ObjectId="119951"/>
    <cge:TPSR_Ref TObjectID="22331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -95.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120095">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4533.000000 -314.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22355" ObjectName="SW-WD_BL.WD_BL_0161SW"/>
     <cge:Meas_Ref ObjectId="120095"/>
    <cge:TPSR_Ref TObjectID="22355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120093">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4693.000000 -314.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22353" ObjectName="SW-WD_BL.WD_BL_0162SW"/>
     <cge:Meas_Ref ObjectId="120093"/>
    <cge:TPSR_Ref TObjectID="22353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120096">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4551.000000 -292.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22356" ObjectName="SW-WD_BL.WD_BL_01617SW"/>
     <cge:Meas_Ref ObjectId="120096"/>
    <cge:TPSR_Ref TObjectID="22356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120094">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 -291.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22354" ObjectName="SW-WD_BL.WD_BL_01627SW"/>
     <cge:Meas_Ref ObjectId="120094"/>
    <cge:TPSR_Ref TObjectID="22354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120103">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4869.000000 -218.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22360" ObjectName="SW-WD_BL.WD_BL_0612SW"/>
     <cge:Meas_Ref ObjectId="120103"/>
    <cge:TPSR_Ref TObjectID="22360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120101">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4869.000000 -333.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22358" ObjectName="SW-WD_BL.WD_BL_0611SW"/>
     <cge:Meas_Ref ObjectId="120101"/>
    <cge:TPSR_Ref TObjectID="22358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120104">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4869.000000 -83.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22361" ObjectName="SW-WD_BL.WD_BL_0616SW"/>
     <cge:Meas_Ref ObjectId="120104"/>
    <cge:TPSR_Ref TObjectID="22361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120102">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.000000 -324.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22359" ObjectName="SW-WD_BL.WD_BL_06117SW"/>
     <cge:Meas_Ref ObjectId="120102"/>
    <cge:TPSR_Ref TObjectID="22359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-201300">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5029.000000 -331.450000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30744" ObjectName="SW-WD_BL.WD_BL_0621SW"/>
     <cge:Meas_Ref ObjectId="201300"/>
    <cge:TPSR_Ref TObjectID="30744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-201303">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5029.000000 -81.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30747" ObjectName="SW-WD_BL.WD_BL_0626SW"/>
     <cge:Meas_Ref ObjectId="201303"/>
    <cge:TPSR_Ref TObjectID="30747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-201301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5047.000000 -322.450000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30745" ObjectName="SW-WD_BL.WD_BL_06217SW"/>
     <cge:Meas_Ref ObjectId="201301"/>
    <cge:TPSR_Ref TObjectID="30745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-201302">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5029.000000 -215.934783)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30746" ObjectName="SW-WD_BL.WD_BL_0622SW"/>
     <cge:Meas_Ref ObjectId="201302"/>
    <cge:TPSR_Ref TObjectID="30746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120198">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4705.311108 -693.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22376" ObjectName="SW-WD_BL.WD_BL_39017SW"/>
     <cge:Meas_Ref ObjectId="120198"/>
    <cge:TPSR_Ref TObjectID="22376"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3657.000000 97.000000)" xlink:href="#capacitor:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2bd0b90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -1039.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e80e10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4608.311108 -598.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e84190">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3488.000000 -189.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f8fff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3620.000000 -195.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f0c4b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3619.000000 -45.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c98170">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3779.000000 -202.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e02780">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3779.000000 -70.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ffcda0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3939.000000 -203.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c9ddc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3939.000000 -71.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2aaa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.000000 -203.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2c740">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.000000 -71.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37809f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4259.000000 -201.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3444eb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4259.000000 -69.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3447e10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 -158.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3793110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4803.000000 -201.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37945e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4803.000000 -69.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33b7a70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4963.000000 -199.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33b8f40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4963.000000 -67.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -953.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22374"/>
     <cge:Term_Ref ObjectID="31415"/>
    <cge:TPSR_Ref TObjectID="22374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -953.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22374"/>
     <cge:Term_Ref ObjectID="31415"/>
    <cge:TPSR_Ref TObjectID="22374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119865" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -953.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119865" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22374"/>
     <cge:Term_Ref ObjectID="31415"/>
    <cge:TPSR_Ref TObjectID="22374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119771" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3718.000000 14.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119771" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22342"/>
     <cge:Term_Ref ObjectID="31351"/>
    <cge:TPSR_Ref TObjectID="22342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119772" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3718.000000 14.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119772" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22342"/>
     <cge:Term_Ref ObjectID="31351"/>
    <cge:TPSR_Ref TObjectID="22342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119763" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3718.000000 14.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22342"/>
     <cge:Term_Ref ObjectID="31351"/>
    <cge:TPSR_Ref TObjectID="22342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119741" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 20.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119741" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22380"/>
     <cge:Term_Ref ObjectID="31427"/>
    <cge:TPSR_Ref TObjectID="22380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119742" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 20.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119742" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22380"/>
     <cge:Term_Ref ObjectID="31427"/>
    <cge:TPSR_Ref TObjectID="22380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119733" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 20.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119733" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22380"/>
     <cge:Term_Ref ObjectID="31427"/>
    <cge:TPSR_Ref TObjectID="22380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 18.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22352"/>
     <cge:Term_Ref ObjectID="31371"/>
    <cge:TPSR_Ref TObjectID="22352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119802" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 18.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22352"/>
     <cge:Term_Ref ObjectID="31371"/>
    <cge:TPSR_Ref TObjectID="22352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119793" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 18.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119793" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22352"/>
     <cge:Term_Ref ObjectID="31371"/>
    <cge:TPSR_Ref TObjectID="22352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119786" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 20.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22347"/>
     <cge:Term_Ref ObjectID="31361"/>
    <cge:TPSR_Ref TObjectID="22347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119787" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 20.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22347"/>
     <cge:Term_Ref ObjectID="31361"/>
    <cge:TPSR_Ref TObjectID="22347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 20.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22347"/>
     <cge:Term_Ref ObjectID="31361"/>
    <cge:TPSR_Ref TObjectID="22347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119756" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.000000 15.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119756" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22334"/>
     <cge:Term_Ref ObjectID="31335"/>
    <cge:TPSR_Ref TObjectID="22334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119757" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.000000 15.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22334"/>
     <cge:Term_Ref ObjectID="31335"/>
    <cge:TPSR_Ref TObjectID="22334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119748" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.000000 15.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119748" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22334"/>
     <cge:Term_Ref ObjectID="31335"/>
    <cge:TPSR_Ref TObjectID="22334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119831" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 23.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119831" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22381"/>
     <cge:Term_Ref ObjectID="31429"/>
    <cge:TPSR_Ref TObjectID="22381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119832" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 23.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119832" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22381"/>
     <cge:Term_Ref ObjectID="31429"/>
    <cge:TPSR_Ref TObjectID="22381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119823" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 23.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119823" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22381"/>
     <cge:Term_Ref ObjectID="31429"/>
    <cge:TPSR_Ref TObjectID="22381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119859" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4437.000000 -505.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119859" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22368"/>
     <cge:Term_Ref ObjectID="31403"/>
    <cge:TPSR_Ref TObjectID="22368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119860" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4437.000000 -505.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119860" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22368"/>
     <cge:Term_Ref ObjectID="31403"/>
    <cge:TPSR_Ref TObjectID="22368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119851" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4437.000000 -505.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119851" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22368"/>
     <cge:Term_Ref ObjectID="31403"/>
    <cge:TPSR_Ref TObjectID="22368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-119888" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3383.000000 -628.000000) translate(0,20)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22324"/>
     <cge:Term_Ref ObjectID="31317"/>
    <cge:TPSR_Ref TObjectID="22324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-119889" prefix="Ub " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3383.000000 -628.000000) translate(0,45)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22324"/>
     <cge:Term_Ref ObjectID="31317"/>
    <cge:TPSR_Ref TObjectID="22324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-119890" prefix="Uc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3383.000000 -628.000000) translate(0,70)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22324"/>
     <cge:Term_Ref ObjectID="31317"/>
    <cge:TPSR_Ref TObjectID="22324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-119892" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3383.000000 -628.000000) translate(0,95)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22324"/>
     <cge:Term_Ref ObjectID="31317"/>
    <cge:TPSR_Ref TObjectID="22324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-119893" prefix="Ubc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3383.000000 -628.000000) translate(0,120)">Ubc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22324"/>
     <cge:Term_Ref ObjectID="31317"/>
    <cge:TPSR_Ref TObjectID="22324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-119894" prefix="Uca " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3383.000000 -628.000000) translate(0,145)">Uca  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22324"/>
     <cge:Term_Ref ObjectID="31317"/>
    <cge:TPSR_Ref TObjectID="22324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-119891" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3383.000000 -628.000000) translate(0,170)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22324"/>
     <cge:Term_Ref ObjectID="31317"/>
    <cge:TPSR_Ref TObjectID="22324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-119881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.000000 -1002.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22323"/>
     <cge:Term_Ref ObjectID="31316"/>
    <cge:TPSR_Ref TObjectID="22323"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-119882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.000000 -1002.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22323"/>
     <cge:Term_Ref ObjectID="31316"/>
    <cge:TPSR_Ref TObjectID="22323"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-119883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.000000 -1002.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22323"/>
     <cge:Term_Ref ObjectID="31316"/>
    <cge:TPSR_Ref TObjectID="22323"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-119885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.000000 -1002.000000) translate(0,95)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22323"/>
     <cge:Term_Ref ObjectID="31316"/>
    <cge:TPSR_Ref TObjectID="22323"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-119884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4656.000000 -1002.000000) translate(0,120)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22323"/>
     <cge:Term_Ref ObjectID="31316"/>
    <cge:TPSR_Ref TObjectID="22323"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119816" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -215.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119816" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22357"/>
     <cge:Term_Ref ObjectID="31381"/>
    <cge:TPSR_Ref TObjectID="22357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119817" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -215.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119817" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22357"/>
     <cge:Term_Ref ObjectID="31381"/>
    <cge:TPSR_Ref TObjectID="22357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119808" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -215.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119808" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22357"/>
     <cge:Term_Ref ObjectID="31381"/>
    <cge:TPSR_Ref TObjectID="22357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-119846" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4421.000000 -806.000000) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22367"/>
     <cge:Term_Ref ObjectID="31401"/>
    <cge:TPSR_Ref TObjectID="22367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-119847" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4421.000000 -806.000000) translate(0,45)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119847" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22367"/>
     <cge:Term_Ref ObjectID="31401"/>
    <cge:TPSR_Ref TObjectID="22367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-119838" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4421.000000 -806.000000) translate(0,70)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119838" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22367"/>
     <cge:Term_Ref ObjectID="31401"/>
    <cge:TPSR_Ref TObjectID="22367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-119844" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4421.000000 -806.000000) translate(0,95)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119844" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22367"/>
     <cge:Term_Ref ObjectID="31401"/>
    <cge:TPSR_Ref TObjectID="22367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-119821" prefix="3Uo  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3384.000000 -458.000000) translate(0,20)">3Uo   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22357"/>
     <cge:Term_Ref ObjectID="31381"/>
    <cge:TPSR_Ref TObjectID="22357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-201333" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5043.000000 27.000000) translate(0,18)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="201333" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30748"/>
     <cge:Term_Ref ObjectID="43898"/>
    <cge:TPSR_Ref TObjectID="30748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-201334" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5043.000000 27.000000) translate(0,40)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="201334" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30748"/>
     <cge:Term_Ref ObjectID="43898"/>
    <cge:TPSR_Ref TObjectID="30748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-201329" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5043.000000 27.000000) translate(0,62)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="201329" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30748"/>
     <cge:Term_Ref ObjectID="43898"/>
    <cge:TPSR_Ref TObjectID="30748"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="64" qtmmishow="hidden" width="217" x="3087" y="-1119"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="92" qtmmishow="hidden" width="106" x="3015" y="-1136"/></g>
   <g href="35kV白路变10kV1号电容器051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="43" x="3638" y="-298"/></g>
   <g href="35kV白路变10kV关坡线052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="3796" y="-318"/></g>
   <g href="35kV白路变10kV岔河线053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="3956" y="-318"/></g>
   <g href="35kV白路变10kV石腊它线054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="4116" y="-318"/></g>
   <g href="35kV白路变10kV母线分段016间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="4599" y="-276"/></g>
   <g href="35kV白路变10kV白路集镇055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="4280" y="-316"/></g>
   <g href="35kV白路变35kV大东白线351间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="42" x="4187" y="-938"/></g>
   <g href="35kV白路变10kV三合线061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="37" x="4820" y="-318"/></g>
   <g href="35kV白路变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="32" qtmmishow="hidden" width="112" x="4299" y="-684"/></g>
   <g href="35kV白路变10kV洒胶泥线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="45" x="4978" y="-313"/></g>
   <g href="35kV白路变GG虚拟间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="37" qtmmishow="hidden" width="129" x="3066" y="-576"/></g>
   <g href="cx_配调_配网接线图35_武定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3328" y="-1093"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3328" y="-1128"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.098361 -0.000000 0.000000 -1.509375 -225.770492 532.303125)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_327e280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3872.000000 984.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e708d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 969.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7b980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 954.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.557377 -0.000000 0.000000 -1.717391 -4385.098361 1554.945652)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f677d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5067.000000 902.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0e510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5056.000000 887.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31dd640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5081.000000 872.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.148278 -0.000000 0.000000 -1.574013 -324.578298 383.555556)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32d9630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 620.000000) translate(0,15)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f0f0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 635.000000) translate(0,15)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.315789 -0.000000 0.000000 -1.662162 -312.000000 469.675676)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec6d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3713.000000 871.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eac0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3713.000000 885.500000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eac340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3713.000000 856.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eac860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 841.500000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eacae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3721.000000 827.500000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.098361 -0.000000 0.000000 -1.773649 115.229508 1235.456081)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a6360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3872.000000 984.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a6660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 969.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a68a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 954.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.016129 -0.000000 0.000000 -1.505044 251.016129 385.177175)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a6bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 775.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a6e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 760.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a70c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4059.000000 744.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33a7c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 790.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_BL.WD_BL_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-831 4729,-831 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22323" ObjectName="BS-WD_BL.WD_BL_3IM"/>
    <cge:TPSR_Ref TObjectID="22323"/></metadata>
   <polyline fill="none" opacity="0" points="3806,-831 4729,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_BL.WD_BL_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3371,-385 4590,-385 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22324" ObjectName="BS-WD_BL.WD_BL_9IM"/>
    <cge:TPSR_Ref TObjectID="22324"/></metadata>
   <polyline fill="none" opacity="0" points="3371,-385 4590,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_BL.WD_BL_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4654,-384 5120,-384 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22325" ObjectName="BS-WD_BL.WD_BL_9IIM"/>
    <cge:TPSR_Ref TObjectID="22325"/></metadata>
   <polyline fill="none" opacity="0" points="4654,-384 5120,-384 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="25" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3047.000000 -1027.513514) translate(0,20)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-119846" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3188.538462 -852.966362) translate(0,20)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119846" ObjectName="WD_BL:WD_BL_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-119847" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3187.538462 -802.966362) translate(0,20)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119847" ObjectName="WD_BL:WD_BL_301BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-119864" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4474.000000 -587.000000) translate(0,20)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119864" ObjectName="WD_BL:WD_BL_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-121143" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4477.000000 -614.000000) translate(0,20)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121143" ObjectName="WD_BL:WD_BL_1T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-119846" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3188.538462 -945.966362) translate(0,20)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119846" ObjectName="WD_BL:WD_BL_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-119846" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3187.538462 -901.966362) translate(0,20)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="119846" ObjectName="WD_BL:WD_BL_301BK_P"/>
    </metadata>
   </g>
  </g><g id="PieSliceF_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" points="5107,-719 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" points="5070,-553 " stroke="rgb(255,255,255)"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="64" qtmmishow="hidden" width="217" x="3087" y="-1119"/>
    </a>
   <metadata/><rect fill="white" height="64" opacity="0" stroke="white" transform="" width="217" x="3087" y="-1119"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="92" qtmmishow="hidden" width="106" x="3015" y="-1136"/>
    </a>
   <metadata/><rect fill="white" height="92" opacity="0" stroke="white" transform="" width="106" x="3015" y="-1136"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="43" x="3638" y="-298"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="43" x="3638" y="-298"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="3796" y="-318"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="3796" y="-318"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="3956" y="-318"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="3956" y="-318"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="4116" y="-318"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="4116" y="-318"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="4599" y="-276"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="4599" y="-276"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="4280" y="-316"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="4280" y="-316"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="42" x="4187" y="-938"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="42" x="4187" y="-938"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="37" x="4820" y="-318"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="37" x="4820" y="-318"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="32" qtmmishow="hidden" width="112" x="4299" y="-684"/>
    </a>
   <metadata/><rect fill="white" height="32" opacity="0" stroke="white" transform="" width="112" x="4299" y="-684"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="45" x="4978" y="-313"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="45" x="4978" y="-313"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="37" qtmmishow="hidden" width="129" x="3066" y="-576"/>
    </a>
   <metadata/><rect fill="white" height="37" opacity="0" stroke="white" transform="" width="129" x="3066" y="-576"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3328" y="-1093"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3328" y="-1093"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3328" y="-1128"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3328" y="-1128"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-WD_BL.WD_BL_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31433"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4206.000000 -622.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4206.000000 -622.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22382" ObjectName="TF-WD_BL.WD_BL_1T"/>
    <cge:TPSR_Ref TObjectID="22382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.220076 -636.000000)" xlink:href="#transformer2:shape71_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.220076 -636.000000)" xlink:href="#transformer2:shape71_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4382.000000 26.000000)" xlink:href="#transformer2:shape71_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4382.000000 26.000000)" xlink:href="#transformer2:shape71_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="WD_BL"/>
</svg>