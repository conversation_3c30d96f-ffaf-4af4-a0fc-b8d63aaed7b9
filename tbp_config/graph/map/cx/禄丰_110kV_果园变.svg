<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-128" aopId="270" id="thSvg" viewBox="3117 -1279 2070 1401">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape14">
    <polyline arcFlag="1" fill="none" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 " stroke-width="0.0972"/>
    <rect height="23" stroke-width="0.945274" width="11" x="41" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="47" x2="45" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="49" x2="47" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07143" x1="47" x2="47" y1="54" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="47" x2="47" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="54" x2="40" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="50" x2="44" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.309343" x1="49" x2="45" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <polyline arcFlag="1" fill="none" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <polyline arcFlag="1" fill="none" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" fill="none" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="64" x2="64" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape14">
    <polyline fill="none" points="9,9 3,12 1,13 1,14 1,14 3,16 6,17 10,19 11,20 11,20 11,21 10,22 6,23 3,25 2,25 2,26 2,27 3,28 6,29 10,31 11,31 11,32 11,33 10,33 6,35 3,36 2,37 2,38 2,39 3,39 6,41 10,42 11,43 11,44 11,44 10,45 6,47 3,48 1,50 1,50 1,51 3,52 9,55 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="10" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="54" y2="61"/>
   </symbol>
   <symbol id="lightningRod:shape15">
    <polyline fill="none" points="57,10 54,4 52,2 52,2 51,2 50,3 48,7 47,10 46,11 45,11 44,11 44,10 42,7 41,4 40,3 39,3 39,3 38,4 36,7 35,10 34,11 34,11 33,11 32,10 31,7 29,4 28,3 28,3 27,3 26,4 25,7 23,10 23,11 22,11 21,11 20,10 19,7 17,3 16,2 15,2 15,2 13,4 10,10 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="56" x2="62" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="4" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape192">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="26" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="5,19 17,9 5,0 5,19 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,1 26,10 38,19 38,1 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="13" stroke-width="0.424575" width="29" x="14" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="4" x2="4" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="5" x2="5" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="20" y1="8" y2="8"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape36">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline fill="none" points="19,9 35,9 35,22 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="18" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="7" x2="5" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="voltageTransformer:shape46">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="26" x2="26" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="45" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="18" x2="14" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="19" x2="12" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="22" x2="10" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="16" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="33" y1="31" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="33" x2="33" y1="38" y2="25"/>
    <circle cx="7" cy="12" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="3" y2="3"/>
    <circle cx="16" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="16" cy="17" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="11" y2="5"/>
    <circle cx="15" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="11" y2="5"/>
   </symbol>
   <symbol id="voltageTransformer:shape48">
    <circle cx="8" cy="19" r="7.5" stroke-width="0.804311"/>
    <circle cx="8" cy="8" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="10" x2="8" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="10" x2="8" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="8" x2="6" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="7" x2="7" y1="5" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="11" x2="7" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="11" x2="7" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="23" x2="21" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="23" x2="21" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="21" x2="18" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="23" x2="21" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="21" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="23" x2="21" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="16" x2="26" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="19" x2="23" y1="39" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="18" x2="24" y1="37" y2="37"/>
    <polyline fill="none" points="21,19 21,35 " stroke-width="1"/>
    <circle cx="20" cy="8" r="7.5" stroke-width="0.804311"/>
    <circle cx="20" cy="19" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="voltageTransformer:shape45">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="34" x2="34" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="24" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="24" x2="24" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="17" x2="17" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="41" y1="7" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="41" x2="41" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="5" x2="5" y1="11" y2="4"/>
    <circle cx="29" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="26" x2="27" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="22" x2="21" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="21" x2="27" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="8" x2="8" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="21" y2="23"/>
    <circle cx="19" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="29" y1="21" y2="23"/>
    <circle cx="24" cy="42" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="29" y2="29"/>
    <circle cx="29" cy="33" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="29" y2="29"/>
    <circle cx="19" cy="33" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="29" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="31" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="31" y2="33"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb1950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb2380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fb2d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fb4000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fb5260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fb5e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb6c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb7680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb7f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb8840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb94f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fb9dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fba6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fbb270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fbbb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fbc440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fbdb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fbec40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b49400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fc0120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fc1300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fc1c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fc2770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fc7b80" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1fc88d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fc4510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1fc5260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1fca800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1411" width="2080" x="3112" y="-1284"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3921" x2="3921" y1="34" y2="34"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3921" x2="3921" y1="42" y2="42"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3921" x2="3921" y1="37" y2="47"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-73878">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3835.000000 -765.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16374" ObjectName="SW-CX_GY.CX_GY_101BK"/>
     <cge:Meas_Ref ObjectId="73878"/>
    <cge:TPSR_Ref TObjectID="16374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73889">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 -421.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16384" ObjectName="SW-CX_GY.CX_GY_001BK"/>
     <cge:Meas_Ref ObjectId="73889"/>
    <cge:TPSR_Ref TObjectID="16384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73885">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -582.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16381" ObjectName="SW-CX_GY.CX_GY_301BK"/>
     <cge:Meas_Ref ObjectId="73885"/>
    <cge:TPSR_Ref TObjectID="16381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73932">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4026.000000 -811.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16399" ObjectName="SW-CX_GY.CX_GY_112BK"/>
     <cge:Meas_Ref ObjectId="73932"/>
    <cge:TPSR_Ref TObjectID="16399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73955">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -1064.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16404" ObjectName="SW-CX_GY.CX_GY_381BK"/>
     <cge:Meas_Ref ObjectId="73955"/>
    <cge:TPSR_Ref TObjectID="16404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74388">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3800.000000 -972.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16461" ObjectName="SW-CX_GY.CX_GY_183BK"/>
     <cge:Meas_Ref ObjectId="74388"/>
    <cge:TPSR_Ref TObjectID="16461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73913">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.638298 -973.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16393" ObjectName="SW-CX_GY.CX_GY_181BK"/>
     <cge:Meas_Ref ObjectId="73913"/>
    <cge:TPSR_Ref TObjectID="16393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73986">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -939.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16407" ObjectName="SW-CX_GY.CX_GY_382BK"/>
     <cge:Meas_Ref ObjectId="73986"/>
    <cge:TPSR_Ref TObjectID="16407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74017">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -824.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16410" ObjectName="SW-CX_GY.CX_GY_383BK"/>
     <cge:Meas_Ref ObjectId="74017"/>
    <cge:TPSR_Ref TObjectID="16410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73894">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -974.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16387" ObjectName="SW-CX_GY.CX_GY_182BK"/>
     <cge:Meas_Ref ObjectId="73894"/>
    <cge:TPSR_Ref TObjectID="16387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -973.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74048">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -705.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16413" ObjectName="SW-CX_GY.CX_GY_384BK"/>
     <cge:Meas_Ref ObjectId="74048"/>
    <cge:TPSR_Ref TObjectID="16413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74079">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.000000 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16416" ObjectName="SW-CX_GY.CX_GY_081BK"/>
     <cge:Meas_Ref ObjectId="74079"/>
    <cge:TPSR_Ref TObjectID="16416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74110">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16420" ObjectName="SW-CX_GY.CX_GY_083BK"/>
     <cge:Meas_Ref ObjectId="74110"/>
    <cge:TPSR_Ref TObjectID="16420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74369">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16456" ObjectName="SW-CX_GY.CX_GY_084BK"/>
     <cge:Meas_Ref ObjectId="74369"/>
    <cge:TPSR_Ref TObjectID="16456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74346">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16451" ObjectName="SW-CX_GY.CX_GY_085BK"/>
     <cge:Meas_Ref ObjectId="74346"/>
    <cge:TPSR_Ref TObjectID="16451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74141">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16424" ObjectName="SW-CX_GY.CX_GY_086BK"/>
     <cge:Meas_Ref ObjectId="74141"/>
    <cge:TPSR_Ref TObjectID="16424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74172">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16428" ObjectName="SW-CX_GY.CX_GY_087BK"/>
     <cge:Meas_Ref ObjectId="74172"/>
    <cge:TPSR_Ref TObjectID="16428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74203">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16432" ObjectName="SW-CX_GY.CX_GY_088BK"/>
     <cge:Meas_Ref ObjectId="74203"/>
    <cge:TPSR_Ref TObjectID="16432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74234">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4408.000000 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16436" ObjectName="SW-CX_GY.CX_GY_089BK"/>
     <cge:Meas_Ref ObjectId="74234"/>
    <cge:TPSR_Ref TObjectID="16436"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_GY.CX_GY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="23822"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3803.000000 -545.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="23824"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3803.000000 -545.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="23826"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3803.000000 -545.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="16472" ObjectName="TF-CX_GY.CX_GY_1T"/>
    <cge:TPSR_Ref TObjectID="16472"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_GY.CX_GY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-358 4559,-358 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="16470" ObjectName="BS-CX_GY.CX_GY_9IM"/>
    <cge:TPSR_Ref TObjectID="16470"/></metadata>
   <polyline fill="none" opacity="0" points="3560,-358 4559,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_GY.CX_GY_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-896 4021,-896 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="16468" ObjectName="BS-CX_GY.CX_GY_1IM"/>
    <cge:TPSR_Ref TObjectID="16468"/></metadata>
   <polyline fill="none" opacity="0" points="3560,-896 4021,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_GY.CX_GY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-1132 4781,-547 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="16471" ObjectName="BS-CX_GY.CX_GY_3IM"/>
    <cge:TPSR_Ref TObjectID="16471"/></metadata>
   <polyline fill="none" opacity="0" points="4781,-1132 4781,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_GY.CX_GY_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-896 4550,-896 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="16469" ObjectName="BS-CX_GY.CX_GY_1IIM"/>
    <cge:TPSR_Ref TObjectID="16469"/></metadata>
   <polyline fill="none" opacity="0" points="4076,-896 4550,-896 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_GY.GY_#1Cb">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.000000 33.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18016" ObjectName="CB-CX_GY.GY_#1Cb"/>
    <cge:TPSR_Ref TObjectID="18016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_GY.GY_#2Cb">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 33.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18017" ObjectName="CB-CX_GY.GY_#2Cb"/>
    <cge:TPSR_Ref TObjectID="18017"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_GY.CX_WM_2TZyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5089.000000 -607.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5089.000000 -607.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-CX_GY.CX_WM_2TZyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_GY.CX_WM_1TZyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3683.000000 -75.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3683.000000 -75.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-CX_GY.CX_WM_1TZyb"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b603d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3785.000000 -546.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b60ac0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 -549.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b8d450">
    <use class="BV-10KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 3857.000000 -522.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b92d00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4020.000000 -511.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b95bb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 -432.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b96880">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -426.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b9d990">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3608.000000 -1014.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb0950">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3829.000000 -1102.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb4ff0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3978.638298 -1103.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb6f00">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3949.638298 -1176.000000)" xlink:href="#lightningRod:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bbfbc0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4462.000000 -1019.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bcaf80">
    <use class="BV-110KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 3858.000000 -657.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cc4a70">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 -1104.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cdc2b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4352.000000 -1103.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cea2c0">
    <use class="BV-35KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 4966.000000 -1038.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cf2030">
    <use class="BV-35KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 4966.000000 -913.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cf8f60">
    <use class="BV-35KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 4966.000000 -798.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cfeaa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4987.000000 -825.000000)" xlink:href="#lightningRod:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d10680">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4978.000000 -612.000000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d163c0">
    <use class="BV-35KV" transform="matrix(0.984615 -0.000000 0.000000 -1.000000 4992.000000 -665.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d24080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3601.000000 -162.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d25e00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3633.000000 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d2cd00">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4708.500000 -939.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d2d7e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4645.000000 -908.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d32700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3689.000000 -235.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d331e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3688.000000 -187.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d3fb70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3769.000000 -162.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d418f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3801.000000 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d566c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.000000 -162.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d6ce80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.000000 -162.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d83640">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -162.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d853c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4157.000000 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d915a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 -162.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d93320">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d9ed30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4311.000000 -162.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1da0ab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dac030">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4407.000000 -162.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1daddb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3226.000000 -1119.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78570" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3271.538462 -1000.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78570" ObjectName="CX_GY:CX_GY_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79723" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3271.538462 -959.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79723" ObjectName="CX_GY:CX_GY_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74709" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3884.000000 -1258.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16461"/>
     <cge:Term_Ref ObjectID="23805"/>
    <cge:TPSR_Ref TObjectID="16461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74710" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3884.000000 -1258.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16461"/>
     <cge:Term_Ref ObjectID="23805"/>
    <cge:TPSR_Ref TObjectID="16461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74705" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3884.000000 -1258.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74705" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16461"/>
     <cge:Term_Ref ObjectID="23805"/>
    <cge:TPSR_Ref TObjectID="16461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74581" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3987.000000 -1259.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16393"/>
     <cge:Term_Ref ObjectID="20857"/>
    <cge:TPSR_Ref TObjectID="16393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74582" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3987.000000 -1259.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16393"/>
     <cge:Term_Ref ObjectID="20857"/>
    <cge:TPSR_Ref TObjectID="16393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74577" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3987.000000 -1259.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74577" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16393"/>
     <cge:Term_Ref ObjectID="20857"/>
    <cge:TPSR_Ref TObjectID="16393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74573" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4195.000000 -1261.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16387"/>
     <cge:Term_Ref ObjectID="20845"/>
    <cge:TPSR_Ref TObjectID="16387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74574" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4195.000000 -1261.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16387"/>
     <cge:Term_Ref ObjectID="20845"/>
    <cge:TPSR_Ref TObjectID="16387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74569" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4195.000000 -1261.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16387"/>
     <cge:Term_Ref ObjectID="20845"/>
    <cge:TPSR_Ref TObjectID="16387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74624" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3594.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16416"/>
     <cge:Term_Ref ObjectID="20903"/>
    <cge:TPSR_Ref TObjectID="16416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74625" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3594.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74625" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16416"/>
     <cge:Term_Ref ObjectID="20903"/>
    <cge:TPSR_Ref TObjectID="16416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74620" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3594.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74620" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16416"/>
     <cge:Term_Ref ObjectID="20903"/>
    <cge:TPSR_Ref TObjectID="16416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74631" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3763.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16420"/>
     <cge:Term_Ref ObjectID="20911"/>
    <cge:TPSR_Ref TObjectID="16420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74632" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3763.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74632" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16420"/>
     <cge:Term_Ref ObjectID="20911"/>
    <cge:TPSR_Ref TObjectID="16420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74627" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3763.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16420"/>
     <cge:Term_Ref ObjectID="20911"/>
    <cge:TPSR_Ref TObjectID="16420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74702" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3877.000000 77.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16456"/>
     <cge:Term_Ref ObjectID="23795"/>
    <cge:TPSR_Ref TObjectID="16456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74703" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3877.000000 77.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74703" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16456"/>
     <cge:Term_Ref ObjectID="23795"/>
    <cge:TPSR_Ref TObjectID="16456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74699" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3877.000000 77.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74699" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16456"/>
     <cge:Term_Ref ObjectID="23795"/>
    <cge:TPSR_Ref TObjectID="16456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74696" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4053.000000 77.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16451"/>
     <cge:Term_Ref ObjectID="23785"/>
    <cge:TPSR_Ref TObjectID="16451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74697" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4053.000000 77.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74697" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16451"/>
     <cge:Term_Ref ObjectID="23785"/>
    <cge:TPSR_Ref TObjectID="16451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74693" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4053.000000 77.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16451"/>
     <cge:Term_Ref ObjectID="23785"/>
    <cge:TPSR_Ref TObjectID="16451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74638" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4123.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16424"/>
     <cge:Term_Ref ObjectID="20919"/>
    <cge:TPSR_Ref TObjectID="16424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74639" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4123.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16424"/>
     <cge:Term_Ref ObjectID="20919"/>
    <cge:TPSR_Ref TObjectID="16424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74634" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4123.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16424"/>
     <cge:Term_Ref ObjectID="20919"/>
    <cge:TPSR_Ref TObjectID="16424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74645" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4219.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16428"/>
     <cge:Term_Ref ObjectID="20927"/>
    <cge:TPSR_Ref TObjectID="16428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74646" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4219.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16428"/>
     <cge:Term_Ref ObjectID="20927"/>
    <cge:TPSR_Ref TObjectID="16428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74641" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4219.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16428"/>
     <cge:Term_Ref ObjectID="20927"/>
    <cge:TPSR_Ref TObjectID="16428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74652" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4310.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74652" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16432"/>
     <cge:Term_Ref ObjectID="20935"/>
    <cge:TPSR_Ref TObjectID="16432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74653" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4310.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74653" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16432"/>
     <cge:Term_Ref ObjectID="20935"/>
    <cge:TPSR_Ref TObjectID="16432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74648" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4310.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16432"/>
     <cge:Term_Ref ObjectID="20935"/>
    <cge:TPSR_Ref TObjectID="16432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74659" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4409.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16436"/>
     <cge:Term_Ref ObjectID="20943"/>
    <cge:TPSR_Ref TObjectID="16436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74660" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4409.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74660" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16436"/>
     <cge:Term_Ref ObjectID="20943"/>
    <cge:TPSR_Ref TObjectID="16436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74655" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4409.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74655" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16436"/>
     <cge:Term_Ref ObjectID="20943"/>
    <cge:TPSR_Ref TObjectID="16436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74552" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3734.000000 -808.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16374"/>
     <cge:Term_Ref ObjectID="20819"/>
    <cge:TPSR_Ref TObjectID="16374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74553" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3734.000000 -808.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16374"/>
     <cge:Term_Ref ObjectID="20819"/>
    <cge:TPSR_Ref TObjectID="16374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74549" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3734.000000 -808.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16374"/>
     <cge:Term_Ref ObjectID="20819"/>
    <cge:TPSR_Ref TObjectID="16374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74565" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3975.000000 -462.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16384"/>
     <cge:Term_Ref ObjectID="20839"/>
    <cge:TPSR_Ref TObjectID="16384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74566" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3975.000000 -462.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16384"/>
     <cge:Term_Ref ObjectID="20839"/>
    <cge:TPSR_Ref TObjectID="16384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74562" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3975.000000 -462.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16384"/>
     <cge:Term_Ref ObjectID="20839"/>
    <cge:TPSR_Ref TObjectID="16384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74558" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4474.000000 -578.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16381"/>
     <cge:Term_Ref ObjectID="20833"/>
    <cge:TPSR_Ref TObjectID="16381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74559" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4474.000000 -578.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16381"/>
     <cge:Term_Ref ObjectID="20833"/>
    <cge:TPSR_Ref TObjectID="16381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74555" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4474.000000 -578.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16381"/>
     <cge:Term_Ref ObjectID="20833"/>
    <cge:TPSR_Ref TObjectID="16381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74596" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -1084.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74596" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16404"/>
     <cge:Term_Ref ObjectID="20879"/>
    <cge:TPSR_Ref TObjectID="16404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74597" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -1084.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16404"/>
     <cge:Term_Ref ObjectID="20879"/>
    <cge:TPSR_Ref TObjectID="16404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74592" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -1084.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74592" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16404"/>
     <cge:Term_Ref ObjectID="20879"/>
    <cge:TPSR_Ref TObjectID="16404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74603" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -960.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16407"/>
     <cge:Term_Ref ObjectID="20885"/>
    <cge:TPSR_Ref TObjectID="16407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74604" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -960.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16407"/>
     <cge:Term_Ref ObjectID="20885"/>
    <cge:TPSR_Ref TObjectID="16407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74599" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -960.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16407"/>
     <cge:Term_Ref ObjectID="20885"/>
    <cge:TPSR_Ref TObjectID="16407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74610" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -845.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74610" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16410"/>
     <cge:Term_Ref ObjectID="20891"/>
    <cge:TPSR_Ref TObjectID="16410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74611" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -845.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16410"/>
     <cge:Term_Ref ObjectID="20891"/>
    <cge:TPSR_Ref TObjectID="16410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74606" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -845.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16410"/>
     <cge:Term_Ref ObjectID="20891"/>
    <cge:TPSR_Ref TObjectID="16410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74617" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -725.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16413"/>
     <cge:Term_Ref ObjectID="20897"/>
    <cge:TPSR_Ref TObjectID="16413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74618" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -725.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16413"/>
     <cge:Term_Ref ObjectID="20897"/>
    <cge:TPSR_Ref TObjectID="16413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74613" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -725.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16413"/>
     <cge:Term_Ref ObjectID="20897"/>
    <cge:TPSR_Ref TObjectID="16413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-74662" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3612.000000 -879.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74662" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16468"/>
     <cge:Term_Ref ObjectID="23817"/>
    <cge:TPSR_Ref TObjectID="16468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-74663" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3612.000000 -879.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74663" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16468"/>
     <cge:Term_Ref ObjectID="23817"/>
    <cge:TPSR_Ref TObjectID="16468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-74664" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3612.000000 -879.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16468"/>
     <cge:Term_Ref ObjectID="23817"/>
    <cge:TPSR_Ref TObjectID="16468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="1" id="ME-74676" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3612.000000 -879.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74676" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16468"/>
     <cge:Term_Ref ObjectID="23817"/>
    <cge:TPSR_Ref TObjectID="16468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-74668" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3612.000000 -879.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74668" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16468"/>
     <cge:Term_Ref ObjectID="23817"/>
    <cge:TPSR_Ref TObjectID="16468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-74677" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3626.000000 -464.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74677" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16470"/>
     <cge:Term_Ref ObjectID="23819"/>
    <cge:TPSR_Ref TObjectID="16470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-74678" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3626.000000 -464.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74678" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16470"/>
     <cge:Term_Ref ObjectID="23819"/>
    <cge:TPSR_Ref TObjectID="16470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-74679" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3626.000000 -464.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74679" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16470"/>
     <cge:Term_Ref ObjectID="23819"/>
    <cge:TPSR_Ref TObjectID="16470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-74684" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3626.000000 -464.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16470"/>
     <cge:Term_Ref ObjectID="23819"/>
    <cge:TPSR_Ref TObjectID="16470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-74680" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3626.000000 -464.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16470"/>
     <cge:Term_Ref ObjectID="23819"/>
    <cge:TPSR_Ref TObjectID="16470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-74685" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4710.000000 -1136.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74685" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16471"/>
     <cge:Term_Ref ObjectID="23820"/>
    <cge:TPSR_Ref TObjectID="16471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-74686" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4710.000000 -1136.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16471"/>
     <cge:Term_Ref ObjectID="23820"/>
    <cge:TPSR_Ref TObjectID="16471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-74687" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4710.000000 -1136.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16471"/>
     <cge:Term_Ref ObjectID="23820"/>
    <cge:TPSR_Ref TObjectID="16471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-74692" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4710.000000 -1136.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16471"/>
     <cge:Term_Ref ObjectID="23820"/>
    <cge:TPSR_Ref TObjectID="16471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-74688" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4710.000000 -1136.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74688" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16471"/>
     <cge:Term_Ref ObjectID="23820"/>
    <cge:TPSR_Ref TObjectID="16471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-74588" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4041.000000 -798.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16399"/>
     <cge:Term_Ref ObjectID="20869"/>
    <cge:TPSR_Ref TObjectID="16399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-74589" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4041.000000 -798.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16399"/>
     <cge:Term_Ref ObjectID="20869"/>
    <cge:TPSR_Ref TObjectID="16399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-74585" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4041.000000 -798.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16399"/>
     <cge:Term_Ref ObjectID="20869"/>
    <cge:TPSR_Ref TObjectID="16399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-74561" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4032.000000 -634.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16472"/>
     <cge:Term_Ref ObjectID="23825"/>
    <cge:TPSR_Ref TObjectID="16472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-74568" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4032.000000 -634.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16472"/>
     <cge:Term_Ref ObjectID="23825"/>
    <cge:TPSR_Ref TObjectID="16472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-74665" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4475.000000 -868.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74665" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16469"/>
     <cge:Term_Ref ObjectID="23818"/>
    <cge:TPSR_Ref TObjectID="16469"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-74666" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4475.000000 -868.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74666" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16469"/>
     <cge:Term_Ref ObjectID="23818"/>
    <cge:TPSR_Ref TObjectID="16469"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-74667" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4475.000000 -868.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74667" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16469"/>
     <cge:Term_Ref ObjectID="23818"/>
    <cge:TPSR_Ref TObjectID="16469"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-78554" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4475.000000 -868.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16469"/>
     <cge:Term_Ref ObjectID="23818"/>
    <cge:TPSR_Ref TObjectID="16469"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-74671" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4475.000000 -868.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="74671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16469"/>
     <cge:Term_Ref ObjectID="23818"/>
    <cge:TPSR_Ref TObjectID="16469"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3238" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3238" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3189" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3189" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3818" y="-1001"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3818" y="-1001"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="3968" y="-1002"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="3968" y="-1002"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4185" y="-1003"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4185" y="-1003"/></g>
  </g><g id="MotifButton_Layer">
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3238" y="-1178"/></g>
   <g href="cx_索引_接线图_局属变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3189" y="-1195"/></g>
   <g href="110kV果园变110kV仙果线183断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3818" y="-1001"/></g>
   <g href="110kV果园变110kV狮果I回线181断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="3968" y="-1002"/></g>
   <g href="110kV果园变110kV狮果II回线182断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4185" y="-1003"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3128" y="-1064"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b939c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 -473.000000)" xlink:href="#voltageTransformer:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bae7c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3746.000000 -1127.000000)" xlink:href="#voltageTransformer:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb31a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.638298 -1128.000000)" xlink:href="#voltageTransformer:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bfc180">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4113.000000 -1129.000000)" xlink:href="#voltageTransformer:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cda6c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -1128.000000)" xlink:href="#voltageTransformer:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ce9850">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5040.000000 -1090.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cf15c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5040.000000 -965.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cf84f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5040.000000 -850.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d078f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5040.000000 -731.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d27b20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.000000 -929.000000)" xlink:href="#voltageTransformer:shape48"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d45490">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4403.000000 -1000.000000)" xlink:href="#voltageTransformer:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d48fb0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3549.000000 -996.000000)" xlink:href="#voltageTransformer:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_GY.CX_GY_381LD">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5102.000000 -1065.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18006" ObjectName="EC-CX_GY.CX_GY_381LD"/>
    <cge:TPSR_Ref TObjectID="18006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_GY.CX_GY_382LD">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5102.000000 -940.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18007" ObjectName="EC-CX_GY.CX_GY_382LD"/>
    <cge:TPSR_Ref TObjectID="18007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_GY.CX_GY_383LD">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5102.000000 -825.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18008" ObjectName="EC-CX_GY.CX_GY_383LD"/>
    <cge:TPSR_Ref TObjectID="18008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_GY.CX_GY_384LD">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5102.000000 -706.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18009" ObjectName="EC-CX_GY.CX_GY_384LD"/>
    <cge:TPSR_Ref TObjectID="18009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_GY.CX_GY_081LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.000000 -18.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18010" ObjectName="EC-CX_GY.CX_GY_081LD"/>
    <cge:TPSR_Ref TObjectID="18010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_GY.CX_GY_083LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 -18.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18011" ObjectName="EC-CX_GY.CX_GY_083LD"/>
    <cge:TPSR_Ref TObjectID="18011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_GY.CX_GY_086LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -18.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18012" ObjectName="EC-CX_GY.CX_GY_086LD"/>
    <cge:TPSR_Ref TObjectID="18012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_GY.CX_GY_087LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -18.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18013" ObjectName="EC-CX_GY.CX_GY_087LD"/>
    <cge:TPSR_Ref TObjectID="18013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_GY.CX_GY_088LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 -18.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18014" ObjectName="EC-CX_GY.CX_GY_088LD"/>
    <cge:TPSR_Ref TObjectID="18014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_GY.CX_GY_089LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4408.000000 -18.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18015" ObjectName="EC-CX_GY.CX_GY_089LD"/>
    <cge:TPSR_Ref TObjectID="18015"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_1b8df20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-751 3829,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="16376@x" ObjectIDND1="16374@x" ObjectIDZND0="16378@1" Pin0InfoVect0LinkObjId="SW-73882_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73880_0" Pin1InfoVect1LinkObjId="SW-73878_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-751 3829,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8e150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3791,-616 3791,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="16380@x" ObjectIDND1="g_1b60ac0@0" ObjectIDND2="16472@x" ObjectIDZND0="g_1b603d0@0" Pin0InfoVect0LinkObjId="g_1b603d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-73884_0" Pin1InfoVect1LinkObjId="g_1b60ac0_0" Pin1InfoVect2LinkObjId="g_1b91c10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3791,-616 3791,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8e380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3763,-555 3763,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1b5fda0@0" ObjectIDZND0="16380@1" Pin0InfoVect0LinkObjId="SW-73884_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b5fda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3763,-555 3763,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8e5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-922 3632,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16468@0" ObjectIDND1="16443@x" ObjectIDZND0="16444@0" Pin0InfoVect0LinkObjId="SW-74336_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-74335_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-922 3632,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8e810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-922 3682,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16444@1" ObjectIDZND0="g_1b6a9d0@0" Pin0InfoVect0LinkObjId="g_1b6a9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74336_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3669,-922 3682,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8ea70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-1004 3634,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="16443@x" ObjectIDND1="g_1b9d990@0" ObjectIDND2="g_1d48fb0@0" ObjectIDZND0="16445@0" Pin0InfoVect0LinkObjId="SW-74337_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-74335_0" Pin1InfoVect1LinkObjId="g_1b9d990_0" Pin1InfoVect2LinkObjId="g_1d48fb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-1004 3634,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8ecd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-1004 3681,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16445@1" ObjectIDZND0="g_1b6c9f0@0" Pin0InfoVect0LinkObjId="g_1b6c9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74337_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-1004 3681,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8ef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-895 4002,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16468@0" ObjectIDZND0="16400@0" Pin0InfoVect0LinkObjId="SW-73933_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-895 4002,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8f190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-768 4002,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16402@1" ObjectIDZND0="g_1b713a0@0" Pin0InfoVect0LinkObjId="g_1b713a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73935_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-768 4002,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8f3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-895 4093,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16469@0" ObjectIDZND0="16401@0" Pin0InfoVect0LinkObjId="SW-73934_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-895 4093,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8f650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-769 4093,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16403@1" ObjectIDZND0="g_1b75d30@0" Pin0InfoVect0LinkObjId="g_1b75d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73936_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-769 4093,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8f8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-836 4002,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="16400@1" ObjectIDZND0="16402@x" ObjectIDZND1="16399@x" Pin0InfoVect0LinkObjId="SW-73935_0" Pin0InfoVect1LinkObjId="SW-73932_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73933_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-836 4002,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8fb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-821 4002,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="16400@x" ObjectIDND1="16399@x" ObjectIDZND0="16402@0" Pin0InfoVect0LinkObjId="SW-73935_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73933_0" Pin1InfoVect1LinkObjId="SW-73932_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-821 4002,-804 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b8fd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4062,-821 4093,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16399@0" ObjectIDZND0="16403@x" ObjectIDZND1="16401@x" Pin0InfoVect0LinkObjId="SW-73936_0" Pin0InfoVect1LinkObjId="SW-73934_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4062,-821 4093,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b8ffd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-529 3861,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16472@x" ObjectIDND1="16386@x" ObjectIDZND0="g_1b8d450@0" Pin0InfoVect0LinkObjId="g_1b8d450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b91c10_0" Pin1InfoVect1LinkObjId="SW-73891_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-529 3861,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b90230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-1074 4800,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16471@0" ObjectIDZND0="16405@0" Pin0InfoVect0LinkObjId="SW-73956_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b912d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-1074 4800,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b90490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-1074 4836,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16404@1" ObjectIDZND0="16405@1" Pin0InfoVect0LinkObjId="SW-73956_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73955_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-1074 4836,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b906f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-1074 4882,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16406@0" ObjectIDZND0="16404@0" Pin0InfoVect0LinkObjId="SW-73955_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73957_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-1074 4882,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b90950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-456 3843,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16384@1" ObjectIDZND0="16386@1" Pin0InfoVect0LinkObjId="SW-73891_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73889_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-456 3843,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b90bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-411 3843,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16385@0" ObjectIDZND0="16384@0" Pin0InfoVect0LinkObjId="SW-73889_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-411 3843,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b90e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-592 4458,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16383@1" ObjectIDZND0="16381@1" Pin0InfoVect0LinkObjId="SW-73885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73887_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-592 4458,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b91070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4485,-592 4527,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16381@0" ObjectIDZND0="16382@0" Pin0InfoVect0LinkObjId="SW-73886_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4485,-592 4527,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b912d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-592 4674,-592 4674,-834 4781,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16382@1" ObjectIDZND0="16471@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73886_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-592 4674,-592 4674,-834 4781,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b91520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-1019 3615,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1b9d990@0" ObjectIDZND0="16445@x" ObjectIDZND1="16443@x" ObjectIDZND2="g_1d48fb0@0" Pin0InfoVect0LinkObjId="SW-74337_0" Pin0InfoVect1LinkObjId="SW-74335_0" Pin0InfoVect2LinkObjId="g_1d48fb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b9d990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-1019 3615,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b91750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-984 3615,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="16443@0" ObjectIDZND0="16445@x" ObjectIDZND1="g_1b9d990@0" ObjectIDZND2="g_1d48fb0@0" Pin0InfoVect0LinkObjId="SW-74337_0" Pin0InfoVect1LinkObjId="g_1b9d990_0" Pin0InfoVect2LinkObjId="g_1d48fb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-984 3615,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b919b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-675 3830,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="16472@x" ObjectIDND1="g_1bcaf80@0" ObjectIDND2="16376@x" ObjectIDZND0="16379@1" Pin0InfoVect0LinkObjId="SW-73883_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b91c10_0" Pin1InfoVect1LinkObjId="g_1bcaf80_0" Pin1InfoVect2LinkObjId="SW-73880_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-675 3830,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b91c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-694 3844,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="16376@1" ObjectIDZND0="16472@x" ObjectIDZND1="g_1bcaf80@0" ObjectIDZND2="16379@x" Pin0InfoVect0LinkObjId="g_1b9ad70_0" Pin0InfoVect1LinkObjId="g_1bcaf80_0" Pin0InfoVect2LinkObjId="SW-73883_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-694 3844,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b91e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-821 3830,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16374@x" ObjectIDND1="16375@x" ObjectIDZND0="16377@1" Pin0InfoVect0LinkObjId="SW-73881_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73878_0" Pin1InfoVect1LinkObjId="SW-73879_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-821 3830,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b920d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-895 3844,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16468@0" ObjectIDZND0="16375@0" Pin0InfoVect0LinkObjId="SW-73879_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-895 3844,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b97040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-475 3721,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1b939c0@0" ObjectIDZND0="g_1b96880@0" Pin0InfoVect0LinkObjId="g_1b96880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b939c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-475 3721,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b972a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3682,-436 3682,-418 3721,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1b95bb0@0" ObjectIDZND0="16450@x" ObjectIDZND1="g_1b96880@0" Pin0InfoVect0LinkObjId="SW-74342_0" Pin0InfoVect1LinkObjId="g_1b96880_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b95bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3682,-436 3682,-418 3721,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b97500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-431 3721,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1b96880@1" ObjectIDZND0="g_1b95bb0@0" ObjectIDZND1="16450@x" Pin0InfoVect0LinkObjId="g_1b95bb0_0" Pin0InfoVect1LinkObjId="SW-74342_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b96880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-431 3721,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b99b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-405 3721,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="16450@0" ObjectIDZND0="g_1b95bb0@0" ObjectIDZND1="g_1b96880@0" Pin0InfoVect0LinkObjId="g_1b95bb0_0" Pin0InfoVect1LinkObjId="g_1b96880_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-405 3721,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b9ad70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3791,-616 3846,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="g_1b603d0@0" ObjectIDND1="16380@x" ObjectIDND2="g_1b60ac0@0" ObjectIDZND0="16472@x" Pin0InfoVect0LinkObjId="g_1b91c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b603d0_0" Pin1InfoVect1LinkObjId="SW-73884_0" Pin1InfoVect2LinkObjId="g_1b60ac0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3791,-616 3846,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b9afd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3763,-605 3763,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="16380@0" ObjectIDZND0="g_1b603d0@0" ObjectIDZND1="16472@x" ObjectIDZND2="g_1b60ac0@0" Pin0InfoVect0LinkObjId="g_1b603d0_0" Pin0InfoVect1LinkObjId="g_1b91c10_0" Pin0InfoVect2LinkObjId="g_1b60ac0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73884_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3763,-605 3763,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b9ba10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-603 3729,-616 3763,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="g_1b60ac0@0" ObjectIDZND0="16380@x" ObjectIDZND1="g_1b603d0@0" ObjectIDZND2="16472@x" Pin0InfoVect0LinkObjId="SW-73884_0" Pin0InfoVect1LinkObjId="g_1b603d0_0" Pin0InfoVect2LinkObjId="g_1b91c10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b60ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-603 3729,-616 3763,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b9bc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3791,-616 3763,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1b603d0@0" ObjectIDND1="16472@x" ObjectIDZND0="16380@x" ObjectIDZND1="g_1b60ac0@0" Pin0InfoVect0LinkObjId="SW-73884_0" Pin0InfoVect1LinkObjId="g_1b60ac0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1b603d0_0" Pin1InfoVect1LinkObjId="g_1b91c10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3791,-616 3763,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9beb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-512 3843,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="16386@0" ObjectIDZND0="16472@x" ObjectIDZND1="g_1b8d450@0" Pin0InfoVect0LinkObjId="g_1b91c10_0" Pin0InfoVect1LinkObjId="g_1b8d450_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73891_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-512 3843,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b9c110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-550 3843,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="16472@2" ObjectIDZND0="16386@x" ObjectIDZND1="g_1b8d450@0" Pin0InfoVect0LinkObjId="SW-73891_0" Pin0InfoVect1LinkObjId="g_1b8d450_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b91c10_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-550 3843,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b9c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-751 3844,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16374@x" ObjectIDND1="16378@x" ObjectIDZND0="16376@0" Pin0InfoVect0LinkObjId="SW-73880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73878_0" Pin1InfoVect1LinkObjId="SW-73882_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-751 3844,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b9c5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-773 3844,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16374@0" ObjectIDZND0="16376@x" ObjectIDZND1="16378@x" Pin0InfoVect0LinkObjId="SW-73880_0" Pin0InfoVect1LinkObjId="SW-73882_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73878_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-773 3844,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b9c830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-821 3844,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16375@x" ObjectIDND1="16377@x" ObjectIDZND0="16374@1" Pin0InfoVect0LinkObjId="SW-73878_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73879_0" Pin1InfoVect1LinkObjId="SW-73881_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-821 3844,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b9ca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-841 3844,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16375@1" ObjectIDZND0="16374@x" ObjectIDZND1="16377@x" Pin0InfoVect0LinkObjId="SW-73878_0" Pin0InfoVect1LinkObjId="SW-73881_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73879_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-841 3844,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b9d4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-895 3615,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16468@0" ObjectIDZND0="16444@x" ObjectIDZND1="16443@x" Pin0InfoVect0LinkObjId="SW-74336_0" Pin0InfoVect1LinkObjId="SW-74335_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-895 3615,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1b9d730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-948 3615,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="16443@1" ObjectIDZND0="16444@x" ObjectIDZND1="16468@0" Pin0InfoVect0LinkObjId="SW-74336_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74335_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-948 3615,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ba0bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-895 3809,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16468@0" ObjectIDZND0="16462@0" Pin0InfoVect0LinkObjId="SW-74389_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-895 3809,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ba5510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-949 3809,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16462@1" ObjectIDZND0="16461@x" ObjectIDZND1="16464@x" Pin0InfoVect0LinkObjId="SW-74388_0" Pin0InfoVect1LinkObjId="SW-74391_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74389_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-949 3809,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ba5770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-966 3809,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16462@x" ObjectIDND1="16464@x" ObjectIDZND0="16461@0" Pin0InfoVect0LinkObjId="SW-74388_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-74389_0" Pin1InfoVect1LinkObjId="SW-74391_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-966 3809,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ba61b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1073 3809,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="16463@1" ObjectIDZND0="16466@x" ObjectIDZND1="g_1bae7c0@0" ObjectIDZND2="g_1bb0950@0" Pin0InfoVect0LinkObjId="SW-74393_0" Pin0InfoVect1LinkObjId="g_1bae7c0_0" Pin0InfoVect2LinkObjId="g_1bb0950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1073 3809,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ba6bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1007 3809,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16461@1" ObjectIDZND0="16463@x" ObjectIDZND1="16465@x" Pin0InfoVect0LinkObjId="SW-74390_0" Pin0InfoVect1LinkObjId="SW-74392_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1007 3809,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ba6e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1023 3809,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16461@x" ObjectIDND1="16465@x" ObjectIDZND0="16463@0" Pin0InfoVect0LinkObjId="SW-74390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-74388_0" Pin1InfoVect1LinkObjId="SW-74392_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1023 3809,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bad980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1091 3791,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="16463@x" ObjectIDND1="g_1bae7c0@0" ObjectIDND2="g_1bb0950@0" ObjectIDZND0="16466@1" Pin0InfoVect0LinkObjId="SW-74393_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-74390_0" Pin1InfoVect1LinkObjId="g_1bae7c0_0" Pin1InfoVect2LinkObjId="g_1bb0950_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1091 3791,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1badbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3757,-1091 3745,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16466@0" ObjectIDZND0="g_1bde6a0@0" Pin0InfoVect0LinkObjId="g_1bde6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3757,-1091 3745,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bade40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1023 3789,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16461@x" ObjectIDND1="16463@x" ObjectIDZND0="16465@1" Pin0InfoVect0LinkObjId="SW-74392_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-74388_0" Pin1InfoVect1LinkObjId="SW-74390_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1023 3789,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bae0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-1023 3745,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16465@0" ObjectIDZND0="g_1bdf130@0" Pin0InfoVect0LinkObjId="g_1bdf130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74392_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-1023 3745,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bae300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-966 3790,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="16462@x" ObjectIDND1="16461@x" ObjectIDZND0="16464@1" Pin0InfoVect0LinkObjId="SW-74391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-74389_0" Pin1InfoVect1LinkObjId="SW-74388_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-966 3790,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bae560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-966 3745,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16464@0" ObjectIDZND0="g_1bdfbc0@0" Pin0InfoVect0LinkObjId="g_1bdfbc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-966 3745,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bafce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-1158 3809,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1bae7c0@0" ObjectIDZND0="16463@x" ObjectIDZND1="16466@x" ObjectIDZND2="g_1bb0950@0" Pin0InfoVect0LinkObjId="SW-74390_0" Pin0InfoVect1LinkObjId="SW-74393_0" Pin0InfoVect2LinkObjId="g_1bb0950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bae7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-1158 3809,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bb06f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1091 3809,-1158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="16463@x" ObjectIDND1="16466@x" ObjectIDZND0="g_1bae7c0@0" ObjectIDZND1="g_1bb0950@0" ObjectIDZND2="17996@1" Pin0InfoVect0LinkObjId="g_1bae7c0_0" Pin0InfoVect1LinkObjId="g_1bb0950_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-74390_0" Pin1InfoVect1LinkObjId="SW-74393_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1091 3809,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bb48d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-1159 3959,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1bb31a0@0" ObjectIDZND0="g_1bb4ff0@0" ObjectIDZND1="g_1bb6f00@0" ObjectIDZND2="16398@x" Pin0InfoVect0LinkObjId="g_1bb4ff0_0" Pin0InfoVect1LinkObjId="g_1bb6f00_0" Pin0InfoVect2LinkObjId="SW-73918_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bb31a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-1159 3959,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bb4b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-1170 3986,-1170 3986,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1bb31a0@0" ObjectIDND1="16398@x" ObjectIDND2="16395@x" ObjectIDZND0="g_1bb4ff0@0" Pin0InfoVect0LinkObjId="g_1bb4ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bb31a0_0" Pin1InfoVect1LinkObjId="SW-73918_0" Pin1InfoVect2LinkObjId="SW-73915_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-1170 3986,-1170 3986,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bb4d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-1159 3959,-1173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_1bb31a0@0" ObjectIDND1="16398@x" ObjectIDND2="16395@x" ObjectIDZND0="g_1bb4ff0@0" ObjectIDZND1="g_1bb6f00@0" Pin0InfoVect0LinkObjId="g_1bb4ff0_0" Pin0InfoVect1LinkObjId="g_1bb6f00_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bb31a0_0" Pin1InfoVect1LinkObjId="SW-73918_0" Pin1InfoVect2LinkObjId="SW-73915_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-1159 3959,-1173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bb7ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-1170 3959,-1180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1bb31a0@0" ObjectIDND1="16398@x" ObjectIDND2="16395@x" ObjectIDZND0="g_1bb6f00@0" Pin0InfoVect0LinkObjId="g_1bb6f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bb31a0_0" Pin1InfoVect1LinkObjId="SW-73918_0" Pin1InfoVect2LinkObjId="SW-73915_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-1170 3959,-1180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bb8ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-1237 3959,-1253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_1bb6f00@1" ObjectIDZND0="11546@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bb6f00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-1237 3959,-1253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bbed80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-926 4485,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16469@0" ObjectIDND1="16446@x" ObjectIDZND0="16447@0" Pin0InfoVect0LinkObjId="SW-74339_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-74338_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-926 4485,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bbefe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4522,-926 4535,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16447@1" ObjectIDZND0="g_1bb8e90@0" Pin0InfoVect0LinkObjId="g_1bb8e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74339_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4522,-926 4535,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bbf240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4521,-1008 4534,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16448@1" ObjectIDZND0="g_1bbbc30@0" Pin0InfoVect0LinkObjId="g_1bbbc30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4521,-1008 4534,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bbf4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-988 4468,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="16446@0" ObjectIDZND0="g_1bbfbc0@0" ObjectIDZND1="16448@x" ObjectIDZND2="g_1d45490@0" Pin0InfoVect0LinkObjId="g_1bbfbc0_0" Pin0InfoVect1LinkObjId="SW-74340_0" Pin0InfoVect2LinkObjId="g_1d45490_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74338_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-988 4468,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bbf700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-895 4468,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16469@0" ObjectIDZND0="16447@x" ObjectIDZND1="16446@x" Pin0InfoVect0LinkObjId="SW-74339_0" Pin0InfoVect1LinkObjId="SW-74338_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-895 4468,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bbf960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-952 4468,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="16446@1" ObjectIDZND0="16447@x" ObjectIDZND1="16469@0" Pin0InfoVect0LinkObjId="SW-74339_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74338_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-952 4468,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bcbcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-664 3862,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="16472@x" ObjectIDND1="16376@x" ObjectIDND2="16379@x" ObjectIDZND0="g_1bcaf80@0" Pin0InfoVect0LinkObjId="g_1bcaf80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b91c10_0" Pin1InfoVect1LinkObjId="SW-73880_0" Pin1InfoVect2LinkObjId="SW-73883_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-664 3862,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bcbf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-675 3844,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" EndDevType1="lightningRod" ObjectIDND0="16376@x" ObjectIDND1="16379@x" ObjectIDZND0="16472@x" ObjectIDZND1="g_1bcaf80@0" Pin0InfoVect0LinkObjId="g_1b91c10_0" Pin0InfoVect1LinkObjId="g_1bcaf80_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73880_0" Pin1InfoVect1LinkObjId="SW-73883_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-675 3844,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bcc1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-664 3844,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer" ObjectIDND0="16376@x" ObjectIDND1="16379@x" ObjectIDND2="g_1bcaf80@0" ObjectIDZND0="16472@1" Pin0InfoVect0LinkObjId="g_1b91c10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-73880_0" Pin1InfoVect1LinkObjId="SW-73883_0" Pin1InfoVect2LinkObjId="g_1bcaf80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-664 3844,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bcd530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4380,-592 4027,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="16383@0" ObjectIDZND0="g_1b92d00@0" ObjectIDZND1="16472@x" Pin0InfoVect0LinkObjId="g_1b92d00_0" Pin0InfoVect1LinkObjId="g_1b91c10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73887_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4380,-592 4027,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd3910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-949 4800,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16471@0" ObjectIDZND0="16408@0" Pin0InfoVect0LinkObjId="SW-73987_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b912d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-949 4800,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bd3b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-949 4836,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16407@1" ObjectIDZND0="16408@1" Pin0InfoVect0LinkObjId="SW-73987_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73986_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-949 4836,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bdac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-834 4800,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16471@0" ObjectIDZND0="16411@0" Pin0InfoVect0LinkObjId="SW-74018_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b912d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-834 4800,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bdae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-834 4836,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16410@1" ObjectIDZND0="16411@1" Pin0InfoVect0LinkObjId="SW-74018_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74017_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-834 4836,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bdb020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-834 4882,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16412@0" ObjectIDZND0="16410@0" Pin0InfoVect0LinkObjId="SW-74017_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74019_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-834 4882,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bddf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1158 3809,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1bae7c0@0" ObjectIDND1="16463@x" ObjectIDND2="16466@x" ObjectIDZND0="g_1bb0950@0" ObjectIDZND1="17996@1" Pin0InfoVect0LinkObjId="g_1bb0950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bae7c0_0" Pin1InfoVect1LinkObjId="SW-74390_0" Pin1InfoVect2LinkObjId="SW-74393_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1158 3809,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bde1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1169 3836,-1169 3836,-1160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1bae7c0@0" ObjectIDND1="16463@x" ObjectIDND2="16466@x" ObjectIDZND0="g_1bb0950@0" Pin0InfoVect0LinkObjId="g_1bb0950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1bae7c0_0" Pin1InfoVect1LinkObjId="SW-74390_0" Pin1InfoVect2LinkObjId="SW-74393_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1169 3836,-1169 3836,-1160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bde440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4002,-821 4035,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16402@x" ObjectIDND1="16400@x" ObjectIDZND0="16399@1" Pin0InfoVect0LinkObjId="SW-73932_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73935_0" Pin1InfoVect1LinkObjId="SW-73933_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4002,-821 4035,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1be75e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3907,-1091 3896,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16398@0" ObjectIDZND0="g_1be7d00@0" Pin0InfoVect0LinkObjId="g_1be7d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73918_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3907,-1091 3896,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1be7840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3907,-1023 3896,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16397@0" ObjectIDZND0="g_1be8790@0" Pin0InfoVect0LinkObjId="g_1be8790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73917_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3907,-1023 3896,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1be7aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3907,-966 3896,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16396@0" ObjectIDZND0="g_1be9220@0" Pin0InfoVect0LinkObjId="g_1be9220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3907,-966 3896,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1be9cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-1091 3959,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="16398@1" ObjectIDZND0="g_1bb31a0@0" ObjectIDZND1="g_1bb4ff0@0" ObjectIDZND2="g_1bb6f00@0" Pin0InfoVect0LinkObjId="g_1bb31a0_0" Pin0InfoVect1LinkObjId="g_1bb4ff0_0" Pin0InfoVect2LinkObjId="g_1bb6f00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73918_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-1091 3959,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bea7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-1091 3959,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="16398@x" ObjectIDND1="16395@x" ObjectIDZND0="g_1bb31a0@0" ObjectIDZND1="g_1bb4ff0@0" ObjectIDZND2="g_1bb6f00@0" Pin0InfoVect0LinkObjId="g_1bb31a0_0" Pin0InfoVect1LinkObjId="g_1bb4ff0_0" Pin0InfoVect2LinkObjId="g_1bb6f00_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73918_0" Pin1InfoVect1LinkObjId="SW-73915_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-1091 3959,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1beaa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-966 3959,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16396@1" ObjectIDZND0="16393@x" ObjectIDZND1="16394@x" Pin0InfoVect0LinkObjId="SW-73913_0" Pin0InfoVect1LinkObjId="SW-73914_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-966 3959,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1beb4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-966 3959,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16396@x" ObjectIDND1="16394@x" ObjectIDZND0="16393@0" Pin0InfoVect0LinkObjId="SW-73913_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73916_0" Pin1InfoVect1LinkObjId="SW-73914_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-966 3959,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bebfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-805 4093,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16403@0" ObjectIDZND0="16399@x" ObjectIDZND1="16401@x" Pin0InfoVect0LinkObjId="SW-73932_0" Pin0InfoVect1LinkObjId="SW-73934_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73936_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-805 4093,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bec240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-821 4093,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16399@x" ObjectIDND1="16403@x" ObjectIDZND0="16401@1" Pin0InfoVect0LinkObjId="SW-73934_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73932_0" Pin1InfoVect1LinkObjId="SW-73936_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-821 4093,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1beeca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-895 4176,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16469@0" ObjectIDZND0="16388@0" Pin0InfoVect0LinkObjId="SW-73895_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-895 4176,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf37d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-951 4176,-968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16388@1" ObjectIDZND0="16387@x" ObjectIDZND1="16390@x" Pin0InfoVect0LinkObjId="SW-73894_0" Pin0InfoVect1LinkObjId="SW-73897_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73895_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-951 4176,-968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf3a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-968 4176,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16388@x" ObjectIDND1="16390@x" ObjectIDZND0="16387@0" Pin0InfoVect0LinkObjId="SW-73894_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73895_0" Pin1InfoVect1LinkObjId="SW-73897_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-968 4176,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf3c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-1075 4176,-1093 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="16389@1" ObjectIDZND0="16392@x" ObjectIDZND1="g_1bfc180@0" ObjectIDZND2="g_1cc4a70@0" Pin0InfoVect0LinkObjId="SW-73899_0" Pin0InfoVect1LinkObjId="g_1bfc180_0" Pin0InfoVect2LinkObjId="g_1cc4a70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73896_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-1075 4176,-1093 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf3ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-1009 4176,-1025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16387@1" ObjectIDZND0="16389@x" ObjectIDZND1="16391@x" Pin0InfoVect0LinkObjId="SW-73896_0" Pin0InfoVect1LinkObjId="SW-73898_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73894_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-1009 4176,-1025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bf4150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-1025 4176,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16387@x" ObjectIDND1="16391@x" ObjectIDZND0="16389@0" Pin0InfoVect0LinkObjId="SW-73896_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73894_0" Pin1InfoVect1LinkObjId="SW-73898_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-1025 4176,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bfb340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-1093 4158,-1093 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="16389@x" ObjectIDND1="g_1bfc180@0" ObjectIDND2="g_1cc4a70@0" ObjectIDZND0="16392@1" Pin0InfoVect0LinkObjId="SW-73899_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-73896_0" Pin1InfoVect1LinkObjId="g_1bfc180_0" Pin1InfoVect2LinkObjId="g_1cc4a70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-1093 4158,-1093 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bfb5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4124,-1093 4112,-1093 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16392@0" ObjectIDZND0="g_1cc61a0@0" Pin0InfoVect0LinkObjId="g_1cc61a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73899_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4124,-1093 4112,-1093 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bfb800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-1025 4156,-1025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="16389@x" ObjectIDND1="16387@x" ObjectIDZND0="16391@1" Pin0InfoVect0LinkObjId="SW-73898_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73896_0" Pin1InfoVect1LinkObjId="SW-73894_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-1025 4156,-1025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bfba60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-1025 4112,-1025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16391@0" ObjectIDZND0="g_1cc6c30@0" Pin0InfoVect0LinkObjId="g_1cc6c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73898_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-1025 4112,-1025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bfbcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-968 4157,-968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16387@x" ObjectIDND1="16388@x" ObjectIDZND0="16390@1" Pin0InfoVect0LinkObjId="SW-73897_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73894_0" Pin1InfoVect1LinkObjId="SW-73895_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-968 4157,-968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1bfbf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-968 4112,-968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16390@0" ObjectIDZND0="g_1cc76c0@0" Pin0InfoVect0LinkObjId="g_1cc76c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73897_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-968 4112,-968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cc45b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4157,-1160 4176,-1160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1bfc180@0" ObjectIDZND0="16392@x" ObjectIDZND1="16389@x" ObjectIDZND2="g_1cc4a70@0" Pin0InfoVect0LinkObjId="SW-73899_0" Pin0InfoVect1LinkObjId="SW-73896_0" Pin0InfoVect2LinkObjId="g_1cc4a70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bfc180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4157,-1160 4176,-1160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cc4810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-1093 4176,-1160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="16392@x" ObjectIDND1="16389@x" ObjectIDZND0="g_1bfc180@0" ObjectIDZND1="g_1cc4a70@0" ObjectIDZND2="11547@1" Pin0InfoVect0LinkObjId="g_1bfc180_0" Pin0InfoVect1LinkObjId="g_1cc4a70_0" Pin0InfoVect2LinkObjId="g_1cc57e0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73899_0" Pin1InfoVect1LinkObjId="SW-73896_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-1093 4176,-1160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cc57e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-1171 4176,-1249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="16392@x" ObjectIDND1="16389@x" ObjectIDND2="g_1bfc180@0" ObjectIDZND0="11547@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-73899_0" Pin1InfoVect1LinkObjId="SW-73896_0" Pin1InfoVect2LinkObjId="g_1bfc180_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-1171 4176,-1249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cc5a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-1160 4176,-1171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="16392@x" ObjectIDND1="16389@x" ObjectIDND2="g_1bfc180@0" ObjectIDZND0="g_1cc4a70@0" ObjectIDZND1="11547@1" Pin0InfoVect0LinkObjId="g_1cc4a70_0" Pin0InfoVect1LinkObjId="g_1cc57e0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-73899_0" Pin1InfoVect1LinkObjId="SW-73896_0" Pin1InfoVect2LinkObjId="g_1bfc180_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-1160 4176,-1171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1cc5ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-1171 4203,-1171 4203,-1162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="16392@x" ObjectIDND1="16389@x" ObjectIDND2="g_1bfc180@0" ObjectIDZND0="g_1cc4a70@0" Pin0InfoVect0LinkObjId="g_1cc4a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-73899_0" Pin1InfoVect1LinkObjId="SW-73896_0" Pin1InfoVect2LinkObjId="g_1bfc180_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-1171 4203,-1171 4203,-1162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ccd3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-895 4332,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16469@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-895 4332,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd1e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-950 4332,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-950 4332,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd2090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-967 4332,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-967 4332,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd22f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1074 4332,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1cda6c0@0" ObjectIDZND2="g_1cdc2b0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1cda6c0_0" Pin0InfoVect2LinkObjId="g_1cdc2b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1074 4332,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd2550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1008 4332,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1008 4332,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd27b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1024 4332,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1024 4332,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd9880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1092 4314,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1cda6c0@0" ObjectIDND2="g_1cdc2b0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1cda6c0_0" Pin1InfoVect2LinkObjId="g_1cdc2b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1092 4314,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd9ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-1092 4268,-1092 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1cdd980@0" Pin0InfoVect0LinkObjId="g_1cdd980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-1092 4268,-1092 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd9d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1024 4312,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1024 4312,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cd9fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4279,-1024 4268,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1cde410@0" Pin0InfoVect0LinkObjId="g_1cde410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4279,-1024 4268,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cda200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-967 4313,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-967 4313,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cda460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4279,-967 4268,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1cdeea0@0" Pin0InfoVect0LinkObjId="g_1cdeea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4279,-967 4268,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cdbdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4313,-1159 4332,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1cda6c0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1cdc2b0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1cdc2b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cda6c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4313,-1159 4332,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cdc050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1092 4332,-1159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_1cda6c0@0" ObjectIDZND1="g_1cdc2b0@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_1cda6c0_0" Pin0InfoVect1LinkObjId="g_1cdc2b0_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1092 4332,-1159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cdd020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1170 4332,-1253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_1cda6c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1cda6c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1170 4332,-1253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cdd280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1159 4332,-1170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_1cda6c0@0" ObjectIDZND0="g_1cdc2b0@0" ObjectIDZND1="0@1" Pin0InfoVect0LinkObjId="g_1cdc2b0_0" Pin0InfoVect1LinkObjId="SW-0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1cda6c0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1159 4332,-1170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cdd4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-1170 4359,-1170 4359,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_1cda6c0@0" ObjectIDZND0="g_1cdc2b0@0" Pin0InfoVect0LinkObjId="g_1cdc2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1cda6c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-1170 4359,-1170 4359,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ce3b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3899,-592 4027,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="16472@0" ObjectIDZND0="16383@x" ObjectIDZND1="g_1b92d00@0" Pin0InfoVect0LinkObjId="SW-73887_0" Pin0InfoVect1LinkObjId="g_1b92d00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b91c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3899,-592 4027,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ce3da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4027,-592 4027,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="16383@x" ObjectIDND1="16472@x" ObjectIDZND0="g_1b92d00@0" Pin0InfoVect0LinkObjId="g_1b92d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73887_0" Pin1InfoVect1LinkObjId="g_1b91c10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4027,-592 4027,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cee100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4975,-1098 4960,-1098 4960,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="18006@x" ObjectIDZND1="16406@x" ObjectIDZND2="g_1cea2c0@0" Pin0InfoVect0LinkObjId="EC-CX_GY.CX_GY_381LD_0" Pin0InfoVect1LinkObjId="SW-73957_0" Pin0InfoVect2LinkObjId="g_1cea2c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4975,-1098 4960,-1098 4960,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cee360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5027,-1098 5045,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_1ce9850@0" Pin0InfoVect0LinkObjId="g_1ce9850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5027,-1098 5045,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cee5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4970,-1046 4948,-1046 4948,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_1cea2c0@0" ObjectIDZND0="16406@x" ObjectIDZND1="18006@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-73957_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_381LD_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cea2c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4970,-1046 4948,-1046 4948,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cef0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-1074 4960,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="18006@0" ObjectIDZND0="0@x" ObjectIDZND1="16406@x" ObjectIDZND2="g_1cea2c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-73957_0" Pin0InfoVect2LinkObjId="g_1cea2c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_GY.CX_GY_381LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-1074 4960,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cefba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4936,-1074 4948,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="16406@1" ObjectIDZND0="18006@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1cea2c0@0" Pin0InfoVect0LinkObjId="EC-CX_GY.CX_GY_381LD_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1cea2c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73957_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4936,-1074 4948,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cefe00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-1074 4960,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="16406@x" ObjectIDND1="g_1cea2c0@0" ObjectIDZND0="18006@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="EC-CX_GY.CX_GY_381LD_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73957_0" Pin1InfoVect1LinkObjId="g_1cea2c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-1074 4960,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf0c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-949 4882,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16409@0" ObjectIDZND0="16407@0" Pin0InfoVect0LinkObjId="SW-73986_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73988_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-949 4882,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf5e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4975,-973 4960,-973 4960,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="18007@x" ObjectIDZND1="16409@x" ObjectIDZND2="g_1cf2030@0" Pin0InfoVect0LinkObjId="EC-CX_GY.CX_GY_382LD_0" Pin0InfoVect1LinkObjId="SW-73988_0" Pin0InfoVect2LinkObjId="g_1cf2030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4975,-973 4960,-973 4960,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cf60d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5027,-973 5045,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_1cf15c0@0" Pin0InfoVect0LinkObjId="g_1cf15c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5027,-973 5045,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf6330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4970,-921 4948,-921 4948,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_1cf2030@0" ObjectIDZND0="0@x" ObjectIDZND1="18007@x" ObjectIDZND2="16409@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_382LD_0" Pin0InfoVect2LinkObjId="SW-73988_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cf2030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4970,-921 4948,-921 4948,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf6590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-949 4960,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="18007@0" ObjectIDZND0="0@x" ObjectIDZND1="16409@x" ObjectIDZND2="g_1cf2030@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-73988_0" Pin0InfoVect2LinkObjId="g_1cf2030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_GY.CX_GY_382LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-949 4960,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf67f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-949 4960,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="16409@x" ObjectIDND1="g_1cf2030@0" ObjectIDZND0="0@x" ObjectIDZND1="18007@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_382LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73988_0" Pin1InfoVect1LinkObjId="g_1cf2030_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-949 4960,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf6a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4936,-949 4948,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="16409@1" ObjectIDZND0="0@x" ObjectIDZND1="18007@x" ObjectIDZND2="g_1cf2030@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_382LD_0" Pin0InfoVect2LinkObjId="g_1cf2030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73988_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4936,-949 4948,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfcda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4975,-858 4960,-858 4960,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="16412@x" ObjectIDZND1="g_1cf8f60@0" ObjectIDZND2="g_1cfeaa0@0" Pin0InfoVect0LinkObjId="SW-74019_0" Pin0InfoVect1LinkObjId="g_1cf8f60_0" Pin0InfoVect2LinkObjId="g_1cfeaa0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4975,-858 4960,-858 4960,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cfd000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5027,-858 5045,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_1cf84f0@0" Pin0InfoVect0LinkObjId="g_1cf84f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5027,-858 5045,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfd260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4970,-806 4948,-806 4948,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1cf8f60@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1cfeaa0@0" ObjectIDZND2="16412@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1cfeaa0_0" Pin0InfoVect2LinkObjId="SW-74019_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cf8f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4970,-806 4948,-806 4948,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfd4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-834 4960,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="16412@x" ObjectIDND1="g_1cf8f60@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1cfeaa0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1cfeaa0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-74019_0" Pin1InfoVect1LinkObjId="g_1cf8f60_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-834 4960,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cfd720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4936,-834 4948,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="16412@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1cfeaa0@0" ObjectIDZND2="g_1cf8f60@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1cfeaa0_0" Pin0InfoVect2LinkObjId="g_1cf8f60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74019_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4936,-834 4948,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cff660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-834 5048,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="18008@0" ObjectIDZND0="g_1cfeaa0@0" Pin0InfoVect0LinkObjId="g_1cfeaa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_GY.CX_GY_383LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-834 5048,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cff8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4991,-834 4960,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1cfeaa0@1" ObjectIDZND0="0@x" ObjectIDZND1="16412@x" ObjectIDZND2="g_1cf8f60@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-74019_0" Pin0InfoVect2LinkObjId="g_1cf8f60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cfeaa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4991,-834 4960,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d04580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-715 4800,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16471@0" ObjectIDZND0="16414@0" Pin0InfoVect0LinkObjId="SW-74049_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b912d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-715 4800,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d047e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-715 4836,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16413@1" ObjectIDZND0="16414@1" Pin0InfoVect0LinkObjId="SW-74049_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74048_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-715 4836,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d06f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-715 4882,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16415@0" ObjectIDZND0="16413@0" Pin0InfoVect0LinkObjId="SW-74048_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-715 4882,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0b430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4975,-739 4960,-739 4960,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="16415@x" ObjectIDZND1="18009@x" ObjectIDZND2="g_1d10680@0" Pin0InfoVect0LinkObjId="SW-74050_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_384LD_0" Pin0InfoVect2LinkObjId="g_1d10680_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4975,-739 4960,-739 4960,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d0b690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5027,-739 5045,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_1d078f0@0" Pin0InfoVect0LinkObjId="g_1d078f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5027,-739 5045,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0c750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-715 4936,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="18009@x" ObjectIDND2="g_1d10680@0" ObjectIDZND0="16415@1" Pin0InfoVect0LinkObjId="SW-74050_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="EC-CX_GY.CX_GY_384LD_0" Pin1InfoVect2LinkObjId="g_1d10680_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-715 4936,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0d240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5075,-715 4975,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="18009@0" ObjectIDZND0="0@x" ObjectIDZND1="16415@x" ObjectIDZND2="g_1d10680@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-74050_0" Pin0InfoVect2LinkObjId="g_1d10680_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_GY.CX_GY_384LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5075,-715 4975,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0d4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4975,-715 4960,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="18009@x" ObjectIDND1="g_1d10680@0" ObjectIDND2="g_1d163c0@0" ObjectIDZND0="0@x" ObjectIDZND1="16415@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-74050_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_GY.CX_GY_384LD_0" Pin1InfoVect1LinkObjId="g_1d10680_0" Pin1InfoVect2LinkObjId="g_1d163c0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4975,-715 4960,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0d700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-601 4800,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16471@0" ObjectIDZND0="16483@0" Pin0InfoVect0LinkObjId="SW-75498_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b912d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-601 4800,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0d960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-601 4836,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="16483@1" Pin0InfoVect0LinkObjId="SW-75498_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-601 4836,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d15f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-622 5029,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d10680@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d10680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-622 5029,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d16160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5081,-622 5094,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5081,-622 5094,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d17130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-673 4975,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d163c0@0" ObjectIDZND0="18009@x" ObjectIDZND1="0@x" ObjectIDZND2="16415@x" Pin0InfoVect0LinkObjId="EC-CX_GY.CX_GY_384LD_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-74050_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d163c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-673 4975,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d17c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4975,-715 4975,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="18009@x" ObjectIDND1="0@x" ObjectIDND2="16415@x" ObjectIDZND0="g_1d10680@0" ObjectIDZND1="g_1d163c0@0" Pin0InfoVect0LinkObjId="g_1d10680_0" Pin0InfoVect1LinkObjId="g_1d163c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_GY.CX_GY_384LD_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-74050_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4975,-715 4975,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d17e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4975,-673 4975,-622 4983,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="18009@x" ObjectIDND1="0@x" ObjectIDND2="16415@x" ObjectIDZND0="g_1d10680@1" Pin0InfoVect0LinkObjId="g_1d10680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_GY.CX_GY_384LD_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-74050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4975,-673 4975,-622 4983,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d180e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3794,-821 3782,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16377@0" ObjectIDZND0="g_1d18800@0" Pin0InfoVect0LinkObjId="g_1d18800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73881_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3794,-821 3782,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d18340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3793,-751 3782,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16378@0" ObjectIDZND0="g_1d19290@0" Pin0InfoVect0LinkObjId="g_1d19290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73882_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3793,-751 3782,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d185a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3794,-675 3782,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16379@0" ObjectIDZND0="g_1d19d20@0" Pin0InfoVect0LinkObjId="g_1d19d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73883_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3794,-675 3782,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d24b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-357 3611,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16470@0" ObjectIDZND0="16417@1" Pin0InfoVect0LinkObjId="SW-74080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dca100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-357 3611,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d24d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-308 3611,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16417@0" ObjectIDZND0="16416@1" Pin0InfoVect0LinkObjId="SW-74079_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-308 3611,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d24fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-265 3611,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16416@0" ObjectIDZND0="16418@1" Pin0InfoVect0LinkObjId="SW-74081_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74079_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-265 3611,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d25220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-215 3611,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16418@0" ObjectIDZND0="g_1d24080@1" Pin0InfoVect0LinkObjId="g_1d24080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74081_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-215 3611,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d25480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-167 3611,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d24080@0" ObjectIDZND0="16419@1" Pin0InfoVect0LinkObjId="SW-74082_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d24080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-167 3611,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d26b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3640,-86 3640,-102 3611,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1d25e00@0" ObjectIDZND0="16419@x" ObjectIDZND1="18010@x" Pin0InfoVect0LinkObjId="SW-74082_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_081LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d25e00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3640,-86 3640,-102 3611,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d27660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-117 3611,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="16419@0" ObjectIDZND0="g_1d25e00@0" ObjectIDZND1="18010@x" Pin0InfoVect0LinkObjId="g_1d25e00_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_081LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74082_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-117 3611,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d278c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3611,-102 3611,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1d25e00@0" ObjectIDND1="16419@x" ObjectIDZND0="18010@0" Pin0InfoVect0LinkObjId="EC-CX_GY.CX_GY_081LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d25e00_0" Pin1InfoVect1LinkObjId="SW-74082_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3611,-102 3611,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d29fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-949 4765,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16471@0" ObjectIDZND0="16481@1" Pin0InfoVect0LinkObjId="SW-75412_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b912d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-949 4765,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2d580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-949 4672,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1d27b20@0" ObjectIDZND0="g_1d2cd00@0" Pin0InfoVect0LinkObjId="g_1d2cd00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d27b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-949 4672,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2e590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-916 4718,-916 4718,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1d2d7e0@0" ObjectIDZND0="g_1d2cd00@0" ObjectIDZND1="16481@x" Pin0InfoVect0LinkObjId="g_1d2cd00_0" Pin0InfoVect1LinkObjId="SW-75412_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d2d7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-916 4718,-916 4718,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2f080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-949 4718,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1d2cd00@1" ObjectIDZND0="g_1d2d7e0@0" ObjectIDZND1="16481@x" Pin0InfoVect0LinkObjId="g_1d2d7e0_0" Pin0InfoVect1LinkObjId="SW-75412_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d2cd00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-949 4718,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2f2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4718,-949 4729,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d2d7e0@0" ObjectIDND1="g_1d2cd00@0" ObjectIDZND0="16481@0" Pin0InfoVect0LinkObjId="SW-75412_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d2d7e0_0" Pin1InfoVect1LinkObjId="g_1d2cd00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4718,-949 4729,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d324a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-357 3698,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16470@0" ObjectIDZND0="16441@1" Pin0InfoVect0LinkObjId="SW-74324_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dca100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-357 3698,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d32f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-307 3698,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16441@0" ObjectIDZND0="g_1d32700@0" Pin0InfoVect0LinkObjId="g_1d32700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-307 3698,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d33c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-240 3698,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1d32700@1" ObjectIDZND0="g_1d331e0@1" Pin0InfoVect0LinkObjId="g_1d331e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d32700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-240 3698,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d33ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-192 3698,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1d331e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d331e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-192 3698,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d405f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-357 3779,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16470@0" ObjectIDZND0="16421@1" Pin0InfoVect0LinkObjId="SW-74111_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dca100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-357 3779,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d40850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-308 3779,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16421@0" ObjectIDZND0="16420@1" Pin0InfoVect0LinkObjId="SW-74110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74111_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-308 3779,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d40ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-265 3779,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16420@0" ObjectIDZND0="16422@1" Pin0InfoVect0LinkObjId="SW-74112_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-265 3779,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d40d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-215 3779,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16422@0" ObjectIDZND0="g_1d3fb70@1" Pin0InfoVect0LinkObjId="g_1d3fb70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-215 3779,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d40f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-167 3779,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d3fb70@0" ObjectIDZND0="16423@1" Pin0InfoVect0LinkObjId="SW-74113_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d3fb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-167 3779,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d42660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3808,-86 3808,-102 3779,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1d418f0@0" ObjectIDZND0="16423@x" ObjectIDZND1="18011@x" Pin0InfoVect0LinkObjId="SW-74113_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_083LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d418f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3808,-86 3808,-102 3779,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d428c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-117 3779,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="16423@0" ObjectIDZND0="g_1d418f0@0" ObjectIDZND1="18011@x" Pin0InfoVect0LinkObjId="g_1d418f0_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_083LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74113_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-117 3779,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d42b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-102 3779,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1d418f0@0" ObjectIDND1="16423@x" ObjectIDZND0="18011@0" Pin0InfoVect0LinkObjId="EC-CX_GY.CX_GY_083LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d418f0_0" Pin1InfoVect1LinkObjId="SW-74113_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-102 3779,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d44dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-1023 4468,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1bbfbc0@0" ObjectIDZND0="16446@x" ObjectIDZND1="16448@x" ObjectIDZND2="g_1d45490@0" Pin0InfoVect0LinkObjId="SW-74338_0" Pin0InfoVect1LinkObjId="SW-74340_0" Pin0InfoVect2LinkObjId="g_1d45490_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bbfbc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-1023 4468,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d44ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-1008 4487,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="16446@x" ObjectIDND1="g_1bbfbc0@0" ObjectIDND2="g_1d45490@0" ObjectIDZND0="16448@0" Pin0InfoVect0LinkObjId="SW-74340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-74338_0" Pin1InfoVect1LinkObjId="g_1bbfbc0_0" Pin1InfoVect2LinkObjId="g_1d45490_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-1008 4487,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d45230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4468,-1008 4455,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="16446@x" ObjectIDND1="g_1bbfbc0@0" ObjectIDND2="16448@x" ObjectIDZND0="g_1d45490@0" Pin0InfoVect0LinkObjId="g_1d45490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-74338_0" Pin1InfoVect1LinkObjId="g_1bbfbc0_0" Pin1InfoVect2LinkObjId="SW-74340_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4468,-1008 4455,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d4cb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-1004 3615,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1d48fb0@0" ObjectIDZND0="16445@x" ObjectIDZND1="16443@x" ObjectIDZND2="g_1b9d990@0" Pin0InfoVect0LinkObjId="SW-74337_0" Pin0InfoVect1LinkObjId="SW-74335_0" Pin0InfoVect2LinkObjId="g_1b9d990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d48fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-1004 3615,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d57140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-357 3921,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16470@0" ObjectIDZND0="16457@1" Pin0InfoVect0LinkObjId="SW-74370_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dca100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-357 3921,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d573a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-308 3921,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16457@0" ObjectIDZND0="16456@1" Pin0InfoVect0LinkObjId="SW-74369_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-308 3921,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d57600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-265 3921,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16456@0" ObjectIDZND0="16458@1" Pin0InfoVect0LinkObjId="SW-74371_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74369_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-265 3921,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d57860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-215 3921,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16458@0" ObjectIDZND0="g_1d566c0@1" Pin0InfoVect0LinkObjId="g_1d566c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74371_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-215 3921,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d5ad50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,92 3921,82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d5a2c0@0" ObjectIDZND0="16440@0" Pin0InfoVect0LinkObjId="SW-74318_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d5a2c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,92 3921,82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d5afb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,46 3921,43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="16440@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74318_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3921,46 3921,43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d61a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-167 3921,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1d566c0@0" ObjectIDZND0="16459@x" ObjectIDZND1="16442@x" Pin0InfoVect0LinkObjId="SW-74372_0" Pin0InfoVect1LinkObjId="SW-74325_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d566c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-167 3921,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d61c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-151 3921,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1d566c0@0" ObjectIDND1="16442@x" ObjectIDZND0="16459@1" Pin0InfoVect0LinkObjId="SW-74372_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d566c0_0" Pin1InfoVect1LinkObjId="SW-74325_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-151 3921,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d61ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-151 3876,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d5b210@0" ObjectIDZND0="16442@0" Pin0InfoVect0LinkObjId="SW-74325_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d5b210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-151 3876,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d62140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-151 3921,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16442@1" ObjectIDZND0="g_1d566c0@0" ObjectIDZND1="16459@x" Pin0InfoVect0LinkObjId="g_1d566c0_0" Pin0InfoVect1LinkObjId="SW-74372_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-151 3921,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d62c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-102 3921,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" ObjectIDND0="16459@0" ObjectIDZND0="18016@x" ObjectIDZND1="16460@x" Pin0InfoVect0LinkObjId="CB-CX_GY.GY_#1Cb_0" Pin0InfoVect1LinkObjId="SW-74373_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74372_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-102 3921,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d62e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-89 3921,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="16459@x" ObjectIDND1="16460@x" ObjectIDZND0="18016@0" Pin0InfoVect0LinkObjId="CB-CX_GY.GY_#1Cb_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-74372_0" Pin1InfoVect1LinkObjId="SW-74373_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-89 3921,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d630f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3866,-89 3875,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d5bca0@0" ObjectIDZND0="16460@0" Pin0InfoVect0LinkObjId="SW-74373_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d5bca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3866,-89 3875,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d63350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3911,-89 3921,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="16460@1" ObjectIDZND0="16459@x" ObjectIDZND1="18016@x" Pin0InfoVect0LinkObjId="SW-74372_0" Pin0InfoVect1LinkObjId="CB-CX_GY.GY_#1Cb_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74373_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3911,-89 3921,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d6d900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,-357 4042,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16470@0" ObjectIDZND0="16452@1" Pin0InfoVect0LinkObjId="SW-74347_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dca100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4042,-357 4042,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d6db60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,-308 4042,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16452@0" ObjectIDZND0="16451@1" Pin0InfoVect0LinkObjId="SW-74346_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74347_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4042,-308 4042,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d6ddc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,-265 4042,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16451@0" ObjectIDZND0="16453@1" Pin0InfoVect0LinkObjId="SW-74348_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74346_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4042,-265 4042,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d6e020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,-215 4042,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16453@0" ObjectIDZND0="g_1d6ce80@1" Pin0InfoVect0LinkObjId="g_1d6ce80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74348_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4042,-215 4042,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d71510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,92 4042,82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d70a80@0" ObjectIDZND0="16482@0" Pin0InfoVect0LinkObjId="SW-75396_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d70a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4042,92 4042,82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d71770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,46 4042,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="16482@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-75396_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4042,46 4042,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d77950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,-167 4042,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1d6ce80@0" ObjectIDZND0="16454@x" ObjectIDZND1="16449@x" Pin0InfoVect0LinkObjId="SW-74349_0" Pin0InfoVect1LinkObjId="SW-74341_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d6ce80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4042,-167 4042,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d77bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,-151 4042,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1d6ce80@0" ObjectIDND1="16449@x" ObjectIDZND0="16454@1" Pin0InfoVect0LinkObjId="SW-74349_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d6ce80_0" Pin1InfoVect1LinkObjId="SW-74341_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4042,-151 4042,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d77e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3987,-151 3997,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d719d0@0" ObjectIDZND0="16449@0" Pin0InfoVect0LinkObjId="SW-74341_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d719d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3987,-151 3997,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d78070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4033,-151 4042,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="16449@1" ObjectIDZND0="16454@x" ObjectIDZND1="g_1d6ce80@0" Pin0InfoVect0LinkObjId="SW-74349_0" Pin0InfoVect1LinkObjId="g_1d6ce80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74341_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4033,-151 4042,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d782d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,-102 4042,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="16454@0" ObjectIDZND0="16455@x" ObjectIDZND1="18017@x" Pin0InfoVect0LinkObjId="SW-74350_0" Pin0InfoVect1LinkObjId="CB-CX_GY.GY_#2Cb_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74349_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4042,-102 4042,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d78530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4042,-89 4042,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="16454@x" ObjectIDND1="16455@x" ObjectIDZND0="18017@0" Pin0InfoVect0LinkObjId="CB-CX_GY.GY_#2Cb_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-74349_0" Pin1InfoVect1LinkObjId="SW-74350_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4042,-89 4042,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d78790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3987,-89 3996,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d72460@0" ObjectIDZND0="16455@0" Pin0InfoVect0LinkObjId="SW-74350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d72460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3987,-89 3996,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d789f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-89 4042,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="16455@1" ObjectIDZND0="16454@x" ObjectIDZND1="18017@x" Pin0InfoVect0LinkObjId="SW-74349_0" Pin0InfoVect1LinkObjId="CB-CX_GY.GY_#2Cb_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-89 4042,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d840c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-357 4135,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16470@0" ObjectIDZND0="16425@1" Pin0InfoVect0LinkObjId="SW-74142_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dca100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-357 4135,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d84320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-308 4135,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16425@0" ObjectIDZND0="16424@1" Pin0InfoVect0LinkObjId="SW-74141_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-308 4135,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d84580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-265 4135,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16424@0" ObjectIDZND0="16426@1" Pin0InfoVect0LinkObjId="SW-74143_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74141_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-265 4135,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d847e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-215 4135,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16426@0" ObjectIDZND0="g_1d83640@1" Pin0InfoVect0LinkObjId="g_1d83640_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74143_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-215 4135,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d84a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-167 4135,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d83640@0" ObjectIDZND0="16427@1" Pin0InfoVect0LinkObjId="SW-74144_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d83640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-167 4135,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d86130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-86 4164,-102 4135,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1d853c0@0" ObjectIDZND0="16427@x" ObjectIDZND1="18012@x" Pin0InfoVect0LinkObjId="SW-74144_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_086LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d853c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-86 4164,-102 4135,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d86390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-117 4135,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="16427@0" ObjectIDZND0="g_1d853c0@0" ObjectIDZND1="18012@x" Pin0InfoVect0LinkObjId="g_1d853c0_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_086LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74144_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-117 4135,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d865f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-102 4135,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="16427@x" ObjectIDND1="g_1d853c0@0" ObjectIDZND0="18012@0" Pin0InfoVect0LinkObjId="EC-CX_GY.CX_GY_086LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-74144_0" Pin1InfoVect1LinkObjId="g_1d853c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-102 4135,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d92020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-357 4228,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16470@0" ObjectIDZND0="16429@1" Pin0InfoVect0LinkObjId="SW-74173_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dca100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-357 4228,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d92280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-308 4228,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16429@0" ObjectIDZND0="16428@1" Pin0InfoVect0LinkObjId="SW-74172_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74173_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-308 4228,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d924e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-265 4228,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16428@0" ObjectIDZND0="16430@1" Pin0InfoVect0LinkObjId="SW-74174_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74172_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-265 4228,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d92740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-215 4228,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16430@0" ObjectIDZND0="g_1d915a0@1" Pin0InfoVect0LinkObjId="g_1d915a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74174_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-215 4228,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d929a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-167 4228,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d915a0@0" ObjectIDZND0="16431@1" Pin0InfoVect0LinkObjId="SW-74175_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d915a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-167 4228,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d94090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-86 4257,-102 4228,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1d93320@0" ObjectIDZND0="16431@x" ObjectIDZND1="18013@x" Pin0InfoVect0LinkObjId="SW-74175_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_087LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d93320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-86 4257,-102 4228,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d942f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-117 4228,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="16431@0" ObjectIDZND0="g_1d93320@0" ObjectIDZND1="18013@x" Pin0InfoVect0LinkObjId="g_1d93320_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_087LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74175_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-117 4228,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d94550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-102 4228,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="16431@x" ObjectIDND1="g_1d93320@0" ObjectIDZND0="18013@0" Pin0InfoVect0LinkObjId="EC-CX_GY.CX_GY_087LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-74175_0" Pin1InfoVect1LinkObjId="g_1d93320_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-102 4228,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9f7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4321,-357 4321,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16470@0" ObjectIDZND0="16433@1" Pin0InfoVect0LinkObjId="SW-74204_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dca100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4321,-357 4321,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9fa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4321,-308 4321,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16433@0" ObjectIDZND0="16432@1" Pin0InfoVect0LinkObjId="SW-74203_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74204_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4321,-308 4321,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9fc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4321,-265 4321,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16432@0" ObjectIDZND0="16434@1" Pin0InfoVect0LinkObjId="SW-74205_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4321,-265 4321,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9fed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4321,-215 4321,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16434@0" ObjectIDZND0="g_1d9ed30@1" Pin0InfoVect0LinkObjId="g_1d9ed30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74205_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4321,-215 4321,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da0130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4321,-167 4321,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d9ed30@0" ObjectIDZND0="16435@1" Pin0InfoVect0LinkObjId="SW-74206_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d9ed30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4321,-167 4321,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da1820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-86 4350,-102 4321,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1da0ab0@0" ObjectIDZND0="16435@x" ObjectIDZND1="18014@x" Pin0InfoVect0LinkObjId="SW-74206_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_088LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1da0ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-86 4350,-102 4321,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da1a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4321,-117 4321,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="16435@0" ObjectIDZND0="g_1da0ab0@0" ObjectIDZND1="18014@x" Pin0InfoVect0LinkObjId="g_1da0ab0_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_088LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74206_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4321,-117 4321,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da1ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4321,-102 4321,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="16435@x" ObjectIDND1="g_1da0ab0@0" ObjectIDZND0="18014@0" Pin0InfoVect0LinkObjId="EC-CX_GY.CX_GY_088LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-74206_0" Pin1InfoVect1LinkObjId="g_1da0ab0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4321,-102 4321,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dacab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-357 4417,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16470@0" ObjectIDZND0="16437@1" Pin0InfoVect0LinkObjId="SW-74235_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dca100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-357 4417,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dacd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-308 4417,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16437@0" ObjectIDZND0="16436@1" Pin0InfoVect0LinkObjId="SW-74234_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-308 4417,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dacf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-265 4417,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16436@0" ObjectIDZND0="16438@1" Pin0InfoVect0LinkObjId="SW-74236_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-265 4417,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dad1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-215 4417,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16438@0" ObjectIDZND0="g_1dac030@1" Pin0InfoVect0LinkObjId="g_1dac030_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74236_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-215 4417,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dad430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-167 4417,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1dac030@0" ObjectIDZND0="16439@1" Pin0InfoVect0LinkObjId="SW-74237_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dac030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-167 4417,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1daeb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4446,-86 4446,-102 4417,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1daddb0@0" ObjectIDZND0="16439@x" ObjectIDZND1="18015@x" Pin0InfoVect0LinkObjId="SW-74237_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_089LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1daddb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4446,-86 4446,-102 4417,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1daed80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-117 4417,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="16439@0" ObjectIDZND0="g_1daddb0@0" ObjectIDZND1="18015@x" Pin0InfoVect0LinkObjId="g_1daddb0_0" Pin0InfoVect1LinkObjId="EC-CX_GY.CX_GY_089LD_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74237_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-117 4417,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1daefe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4417,-102 4417,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="16439@x" ObjectIDND1="g_1daddb0@0" ObjectIDZND0="18015@0" Pin0InfoVect0LinkObjId="EC-CX_GY.CX_GY_089LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-74237_0" Pin1InfoVect1LinkObjId="g_1daddb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4417,-102 4417,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dca100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-375 3843,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16385@1" ObjectIDZND0="16470@0" Pin0InfoVect0LinkObjId="g_1dcb7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-375 3843,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dcb7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-369 3721,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16450@1" ObjectIDZND0="16470@0" Pin0InfoVect0LinkObjId="g_1dca100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-74342_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-369 3721,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dd38f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4504,-357 4504,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16470@0" ObjectIDZND0="16484@1" Pin0InfoVect0LinkObjId="SW-75492_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dca100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4504,-357 4504,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dd3b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4504,-309 4504,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="16484@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-75492_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4504,-309 4504,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ddf3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-1023 3959,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16397@1" ObjectIDZND0="16393@x" ObjectIDZND1="16395@x" Pin0InfoVect0LinkObjId="SW-73913_0" Pin0InfoVect1LinkObjId="SW-73915_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73917_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-1023 3959,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ddf5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-1023 3959,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16397@x" ObjectIDND1="16395@x" ObjectIDZND0="16393@1" Pin0InfoVect0LinkObjId="SW-73913_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73917_0" Pin1InfoVect1LinkObjId="SW-73915_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-1023 3959,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1de4650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-894 3959,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16468@0" ObjectIDZND0="16394@0" Pin0InfoVect0LinkObjId="SW-73914_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-894 3959,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1de48b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-947 3959,-966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="16394@1" ObjectIDZND0="16396@x" ObjectIDZND1="16393@x" Pin0InfoVect0LinkObjId="SW-73916_0" Pin0InfoVect1LinkObjId="SW-73913_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73914_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-947 3959,-966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1de4b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-1024 3959,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="16397@x" ObjectIDND1="16393@x" ObjectIDZND0="16395@0" Pin0InfoVect0LinkObjId="SW-73915_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-73917_0" Pin1InfoVect1LinkObjId="SW-73913_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-1024 3959,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1de4d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-1074 3959,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="16395@1" ObjectIDZND0="16398@x" ObjectIDZND1="g_1bb31a0@0" ObjectIDZND2="g_1bb4ff0@0" Pin0InfoVect0LinkObjId="SW-73918_0" Pin0InfoVect1LinkObjId="g_1bb31a0_0" Pin0InfoVect2LinkObjId="g_1bb4ff0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-73915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-1074 3959,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1de5550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-1206 3809,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="17996@1" ObjectIDZND0="g_1bae7c0@0" ObjectIDZND1="16463@x" ObjectIDZND2="16466@x" Pin0InfoVect0LinkObjId="g_1bae7c0_0" Pin0InfoVect1LinkObjId="SW-74390_0" Pin0InfoVect2LinkObjId="SW-74393_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3809,-1206 3809,-1169 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-72446" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3397.000000 -1087.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16200" ObjectName="DYN-CX_GY"/>
     <cge:Meas_Ref ObjectId="72446"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b87a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4417.000000 578.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b880c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4406.000000 563.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b884d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4431.000000 548.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b88900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3920.000000 463.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b88bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3909.000000 448.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b88dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3934.000000 433.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b891b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3679.000000 809.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b89440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3668.000000 794.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b89650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3693.000000 779.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b8ca00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3939.000000 619.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b8d050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3939.000000 634.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbfb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3827.000000 1258.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbfe90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3816.000000 1243.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dc00d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3841.000000 1228.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dc0fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3539.000000 14.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dc1270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3528.000000 -1.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dc14b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3553.000000 -16.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddac80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3529.000000 835.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddaed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3530.000000 851.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddb470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3530.000000 867.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddba20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3530.000000 882.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddbc70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3522.000000 820.000000) translate(0,12)">Uab（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddbfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.000000 856.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddc210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.000000 840.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddc450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4392.000000 824.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddc690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4385.000000 809.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddc8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.000000 871.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddcc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 466.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddce70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 451.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddd0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 435.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddd2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 419.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddd530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3535.000000 404.000000) translate(0,12)">Uab（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ddd860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 1124.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dddad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 1108.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dddd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 1092.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dddf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4622.000000 1077.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dde190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 1139.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SS" endPointId="0" endStationName="CX_GY" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiguoIhui_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3959,-1253 3959,-1274 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11546" ObjectName="AC-110kV.shiguoIhui_line"/>
    <cge:TPSR_Ref TObjectID="11546_SS-128"/></metadata>
   <polyline fill="none" opacity="0" points="3959,-1253 3959,-1274 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SS" endPointId="0" endStationName="CX_GY" flowDrawDirect="1" flowShape="0" id="AC-110kV.shiguoIIhui_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4176,-1249 4176,-1270 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11547" ObjectName="AC-110kV.shiguoIIhui_line"/>
    <cge:TPSR_Ref TObjectID="11547_SS-128"/></metadata>
   <polyline fill="none" opacity="0" points="4176,-1249 4176,-1270 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4332,-1253 4332,-1274 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4332,-1253 4332,-1274 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_XRD" endPointId="0" endStationName="CX_GY" flowDrawDirect="1" flowShape="0" id="AC-110kV.xianguo_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3809,-1275 3809,-1207 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17996" ObjectName="AC-110kV.xianguo_line"/>
    <cge:TPSR_Ref TObjectID="17996_SS-128"/></metadata>
   <polyline fill="none" opacity="0" points="3809,-1275 3809,-1207 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="16469" cx="4093" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16469" cx="4468" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16469" cx="4176" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16469" cx="4332" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16471" cx="4780" cy="-1074" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16471" cx="4780" cy="-949" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16471" cx="4781" cy="-834" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16471" cx="4780" cy="-834" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16471" cx="4780" cy="-715" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16471" cx="4780" cy="-601" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16471" cx="4780" cy="-949" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16468" cx="4002" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16468" cx="3844" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16468" cx="3615" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16468" cx="3809" cy="-895" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16470" cx="3611" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16470" cx="3698" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16470" cx="3779" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16470" cx="3921" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16470" cx="4042" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16470" cx="4135" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16470" cx="4228" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16470" cx="4321" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16470" cx="4417" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16470" cx="3843" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16470" cx="3721" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16470" cx="4504" cy="-357" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-73880">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3835.000000 -689.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16376" ObjectName="SW-CX_GY.CX_GY_1016SW"/>
     <cge:Meas_Ref ObjectId="73880"/>
    <cge:TPSR_Ref TObjectID="16376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73884">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3754.000000 -564.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16380" ObjectName="SW-CX_GY.CX_GY_1010SW"/>
     <cge:Meas_Ref ObjectId="73884"/>
    <cge:TPSR_Ref TObjectID="16380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73891">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 -471.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16386" ObjectName="SW-CX_GY.CX_GY_0016SW"/>
     <cge:Meas_Ref ObjectId="73891"/>
    <cge:TPSR_Ref TObjectID="16386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73890">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 -370.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16385" ObjectName="SW-CX_GY.CX_GY_0011SW"/>
     <cge:Meas_Ref ObjectId="73890"/>
    <cge:TPSR_Ref TObjectID="16385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73887">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4375.000000 -587.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16383" ObjectName="SW-CX_GY.CX_GY_3016SW"/>
     <cge:Meas_Ref ObjectId="73887"/>
    <cge:TPSR_Ref TObjectID="16383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73886">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4522.000000 -587.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16382" ObjectName="SW-CX_GY.CX_GY_3011SW"/>
     <cge:Meas_Ref ObjectId="73886"/>
    <cge:TPSR_Ref TObjectID="16382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74336">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3628.000000 -917.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16444" ObjectName="SW-CX_GY.CX_GY_19010SW"/>
     <cge:Meas_Ref ObjectId="74336"/>
    <cge:TPSR_Ref TObjectID="16444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74337">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3627.000000 -999.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16445" ObjectName="SW-CX_GY.CX_GY_19017SW"/>
     <cge:Meas_Ref ObjectId="74337"/>
    <cge:TPSR_Ref TObjectID="16445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73933">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.000000 -831.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16400" ObjectName="SW-CX_GY.CX_GY_1121SW"/>
     <cge:Meas_Ref ObjectId="73933"/>
    <cge:TPSR_Ref TObjectID="16400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73935">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.000000 -763.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16402" ObjectName="SW-CX_GY.CX_GY_11217SW"/>
     <cge:Meas_Ref ObjectId="73935"/>
    <cge:TPSR_Ref TObjectID="16402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73934">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 -832.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16401" ObjectName="SW-CX_GY.CX_GY_1122SW"/>
     <cge:Meas_Ref ObjectId="73934"/>
    <cge:TPSR_Ref TObjectID="16401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73936">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 -764.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16403" ObjectName="SW-CX_GY.CX_GY_11227SW"/>
     <cge:Meas_Ref ObjectId="73936"/>
    <cge:TPSR_Ref TObjectID="16403"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73956">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -1069.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16405" ObjectName="SW-CX_GY.CX_GY_3811SW"/>
     <cge:Meas_Ref ObjectId="73956"/>
    <cge:TPSR_Ref TObjectID="16405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73957">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4895.000000 -1069.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16406" ObjectName="SW-CX_GY.CX_GY_3816SW"/>
     <cge:Meas_Ref ObjectId="73957"/>
    <cge:TPSR_Ref TObjectID="16406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74335">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 -943.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16443" ObjectName="SW-CX_GY.CX_GY_1901SW"/>
     <cge:Meas_Ref ObjectId="74335"/>
    <cge:TPSR_Ref TObjectID="16443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73879">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3835.000000 -836.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16375" ObjectName="SW-CX_GY.CX_GY_1011SW"/>
     <cge:Meas_Ref ObjectId="73879"/>
    <cge:TPSR_Ref TObjectID="16375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74342">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -364.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16450" ObjectName="SW-CX_GY.CX_GY_0901SW"/>
     <cge:Meas_Ref ObjectId="74342"/>
    <cge:TPSR_Ref TObjectID="16450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74389">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3800.000000 -908.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16462" ObjectName="SW-CX_GY.CX_GY_1831SW"/>
     <cge:Meas_Ref ObjectId="74389"/>
    <cge:TPSR_Ref TObjectID="16462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74390">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3800.000000 -1032.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16463" ObjectName="SW-CX_GY.CX_GY_1836SW"/>
     <cge:Meas_Ref ObjectId="74390"/>
    <cge:TPSR_Ref TObjectID="16463"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74393">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3752.000000 -1086.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16466" ObjectName="SW-CX_GY.CX_GY_18367SW"/>
     <cge:Meas_Ref ObjectId="74393"/>
    <cge:TPSR_Ref TObjectID="16466"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74392">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 -1018.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16465" ObjectName="SW-CX_GY.CX_GY_18360SW"/>
     <cge:Meas_Ref ObjectId="74392"/>
    <cge:TPSR_Ref TObjectID="16465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74391">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 -961.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16464" ObjectName="SW-CX_GY.CX_GY_18317SW"/>
     <cge:Meas_Ref ObjectId="74391"/>
    <cge:TPSR_Ref TObjectID="16464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74340">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4482.000000 -1003.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16448" ObjectName="SW-CX_GY.CX_GY_19027SW"/>
     <cge:Meas_Ref ObjectId="74340"/>
    <cge:TPSR_Ref TObjectID="16448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74338">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4459.000000 -947.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16446" ObjectName="SW-CX_GY.CX_GY_1902SW"/>
     <cge:Meas_Ref ObjectId="74338"/>
    <cge:TPSR_Ref TObjectID="16446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74339">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -921.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16447" ObjectName="SW-CX_GY.CX_GY_19020SW"/>
     <cge:Meas_Ref ObjectId="74339"/>
    <cge:TPSR_Ref TObjectID="16447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73883">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3789.000000 -670.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16379" ObjectName="SW-CX_GY.CX_GY_10167SW"/>
     <cge:Meas_Ref ObjectId="73883"/>
    <cge:TPSR_Ref TObjectID="16379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73882">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3788.000000 -746.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16378" ObjectName="SW-CX_GY.CX_GY_10160SW"/>
     <cge:Meas_Ref ObjectId="73882"/>
    <cge:TPSR_Ref TObjectID="16378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73881">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3789.000000 -816.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16377" ObjectName="SW-CX_GY.CX_GY_10117SW"/>
     <cge:Meas_Ref ObjectId="73881"/>
    <cge:TPSR_Ref TObjectID="16377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73988">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4895.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16409" ObjectName="SW-CX_GY.CX_GY_3826SW"/>
     <cge:Meas_Ref ObjectId="73988"/>
    <cge:TPSR_Ref TObjectID="16409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73987">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16408" ObjectName="SW-CX_GY.CX_GY_3821SW"/>
     <cge:Meas_Ref ObjectId="73987"/>
    <cge:TPSR_Ref TObjectID="16408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74019">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4895.000000 -829.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16412" ObjectName="SW-CX_GY.CX_GY_3836SW"/>
     <cge:Meas_Ref ObjectId="74019"/>
    <cge:TPSR_Ref TObjectID="16412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74018">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -829.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16411" ObjectName="SW-CX_GY.CX_GY_3831SW"/>
     <cge:Meas_Ref ObjectId="74018"/>
    <cge:TPSR_Ref TObjectID="16411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73918">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 -1086.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16398" ObjectName="SW-CX_GY.CX_GY_18167SW"/>
     <cge:Meas_Ref ObjectId="73918"/>
    <cge:TPSR_Ref TObjectID="16398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73917">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 -1018.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16397" ObjectName="SW-CX_GY.CX_GY_18160SW"/>
     <cge:Meas_Ref ObjectId="73917"/>
    <cge:TPSR_Ref TObjectID="16397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73916">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 -961.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16396" ObjectName="SW-CX_GY.CX_GY_18117SW"/>
     <cge:Meas_Ref ObjectId="73916"/>
    <cge:TPSR_Ref TObjectID="16396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73895">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -910.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16388" ObjectName="SW-CX_GY.CX_GY_1822SW"/>
     <cge:Meas_Ref ObjectId="73895"/>
    <cge:TPSR_Ref TObjectID="16388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73896">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -1034.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16389" ObjectName="SW-CX_GY.CX_GY_1826SW"/>
     <cge:Meas_Ref ObjectId="73896"/>
    <cge:TPSR_Ref TObjectID="16389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73899">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4119.000000 -1088.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16392" ObjectName="SW-CX_GY.CX_GY_18267SW"/>
     <cge:Meas_Ref ObjectId="73899"/>
    <cge:TPSR_Ref TObjectID="16392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73898">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.000000 -1020.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16391" ObjectName="SW-CX_GY.CX_GY_18260SW"/>
     <cge:Meas_Ref ObjectId="73898"/>
    <cge:TPSR_Ref TObjectID="16391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73897">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.000000 -963.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16390" ObjectName="SW-CX_GY.CX_GY_18227SW"/>
     <cge:Meas_Ref ObjectId="73897"/>
    <cge:TPSR_Ref TObjectID="16390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -909.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -1033.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4275.000000 -1087.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 -1019.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 -962.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4970.000000 -1093.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4970.000000 -968.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4970.000000 -853.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74050">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4895.000000 -710.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16415" ObjectName="SW-CX_GY.CX_GY_3846SW"/>
     <cge:Meas_Ref ObjectId="74050"/>
    <cge:TPSR_Ref TObjectID="16415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74049">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -710.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16414" ObjectName="SW-CX_GY.CX_GY_3841SW"/>
     <cge:Meas_Ref ObjectId="74049"/>
    <cge:TPSR_Ref TObjectID="16414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4970.000000 -734.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-75498">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 -596.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16483" ObjectName="SW-CX_GY.CX_GY_3121SW"/>
     <cge:Meas_Ref ObjectId="75498"/>
    <cge:TPSR_Ref TObjectID="16483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5024.000000 -617.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16417" ObjectName="SW-CX_GY.CX_GY_0811SW"/>
     <cge:Meas_Ref ObjectId="74080"/>
    <cge:TPSR_Ref TObjectID="16417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74081">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.000000 -210.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16418" ObjectName="SW-CX_GY.CX_GY_0813SW"/>
     <cge:Meas_Ref ObjectId="74081"/>
    <cge:TPSR_Ref TObjectID="16418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74082">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.000000 -112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16419" ObjectName="SW-CX_GY.CX_GY_0816SW"/>
     <cge:Meas_Ref ObjectId="74082"/>
    <cge:TPSR_Ref TObjectID="16419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-75412">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 -944.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16481" ObjectName="SW-CX_GY.CX_GY_3901SW"/>
     <cge:Meas_Ref ObjectId="75412"/>
    <cge:TPSR_Ref TObjectID="16481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74324">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3689.000000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16441" ObjectName="SW-CX_GY.CX_GY_0821SW"/>
     <cge:Meas_Ref ObjectId="74324"/>
    <cge:TPSR_Ref TObjectID="16441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74111">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16421" ObjectName="SW-CX_GY.CX_GY_0831SW"/>
     <cge:Meas_Ref ObjectId="74111"/>
    <cge:TPSR_Ref TObjectID="16421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74112">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 -210.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16422" ObjectName="SW-CX_GY.CX_GY_0833SW"/>
     <cge:Meas_Ref ObjectId="74112"/>
    <cge:TPSR_Ref TObjectID="16422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74113">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 -112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16423" ObjectName="SW-CX_GY.CX_GY_0836SW"/>
     <cge:Meas_Ref ObjectId="74113"/>
    <cge:TPSR_Ref TObjectID="16423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74370">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16457" ObjectName="SW-CX_GY.CX_GY_0841SW"/>
     <cge:Meas_Ref ObjectId="74370"/>
    <cge:TPSR_Ref TObjectID="16457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74371">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -210.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16458" ObjectName="SW-CX_GY.CX_GY_0843SW"/>
     <cge:Meas_Ref ObjectId="74371"/>
    <cge:TPSR_Ref TObjectID="16458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74372">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -97.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16459" ObjectName="SW-CX_GY.CX_GY_0846SW"/>
     <cge:Meas_Ref ObjectId="74372"/>
    <cge:TPSR_Ref TObjectID="16459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74318">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 87.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16440" ObjectName="SW-CX_GY.CX_GY_08400SW"/>
     <cge:Meas_Ref ObjectId="74318"/>
    <cge:TPSR_Ref TObjectID="16440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3871.000000 -146.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16442" ObjectName="SW-CX_GY.CX_GY_08460SW"/>
     <cge:Meas_Ref ObjectId="74325"/>
    <cge:TPSR_Ref TObjectID="16442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74373">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 -84.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16460" ObjectName="SW-CX_GY.CX_GY_08467SW"/>
     <cge:Meas_Ref ObjectId="74373"/>
    <cge:TPSR_Ref TObjectID="16460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74347">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16452" ObjectName="SW-CX_GY.CX_GY_0851SW"/>
     <cge:Meas_Ref ObjectId="74347"/>
    <cge:TPSR_Ref TObjectID="16452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74348">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 -210.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16453" ObjectName="SW-CX_GY.CX_GY_0853SW"/>
     <cge:Meas_Ref ObjectId="74348"/>
    <cge:TPSR_Ref TObjectID="16453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74349">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 -97.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16454" ObjectName="SW-CX_GY.CX_GY_0856SW"/>
     <cge:Meas_Ref ObjectId="74349"/>
    <cge:TPSR_Ref TObjectID="16454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-75396">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 87.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16482" ObjectName="SW-CX_GY.CX_GY_08500SW"/>
     <cge:Meas_Ref ObjectId="75396"/>
    <cge:TPSR_Ref TObjectID="16482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74341">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 -146.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16449" ObjectName="SW-CX_GY.CX_GY_08560SW"/>
     <cge:Meas_Ref ObjectId="74341"/>
    <cge:TPSR_Ref TObjectID="16449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74350">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -84.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16455" ObjectName="SW-CX_GY.CX_GY_08567SW"/>
     <cge:Meas_Ref ObjectId="74350"/>
    <cge:TPSR_Ref TObjectID="16455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16425" ObjectName="SW-CX_GY.CX_GY_0861SW"/>
     <cge:Meas_Ref ObjectId="74142"/>
    <cge:TPSR_Ref TObjectID="16425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74143">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -210.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16426" ObjectName="SW-CX_GY.CX_GY_0863SW"/>
     <cge:Meas_Ref ObjectId="74143"/>
    <cge:TPSR_Ref TObjectID="16426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74144">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16427" ObjectName="SW-CX_GY.CX_GY_0866SW"/>
     <cge:Meas_Ref ObjectId="74144"/>
    <cge:TPSR_Ref TObjectID="16427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74173">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16429" ObjectName="SW-CX_GY.CX_GY_0871SW"/>
     <cge:Meas_Ref ObjectId="74173"/>
    <cge:TPSR_Ref TObjectID="16429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74174">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -210.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16430" ObjectName="SW-CX_GY.CX_GY_0873SW"/>
     <cge:Meas_Ref ObjectId="74174"/>
    <cge:TPSR_Ref TObjectID="16430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74175">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16431" ObjectName="SW-CX_GY.CX_GY_0876SW"/>
     <cge:Meas_Ref ObjectId="74175"/>
    <cge:TPSR_Ref TObjectID="16431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74204">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16433" ObjectName="SW-CX_GY.CX_GY_0881SW"/>
     <cge:Meas_Ref ObjectId="74204"/>
    <cge:TPSR_Ref TObjectID="16433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74205">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 -210.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16434" ObjectName="SW-CX_GY.CX_GY_0883SW"/>
     <cge:Meas_Ref ObjectId="74205"/>
    <cge:TPSR_Ref TObjectID="16434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74206">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 -112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16435" ObjectName="SW-CX_GY.CX_GY_0886SW"/>
     <cge:Meas_Ref ObjectId="74206"/>
    <cge:TPSR_Ref TObjectID="16435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74235">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4408.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16437" ObjectName="SW-CX_GY.CX_GY_0891SW"/>
     <cge:Meas_Ref ObjectId="74235"/>
    <cge:TPSR_Ref TObjectID="16437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74236">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4408.000000 -210.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16438" ObjectName="SW-CX_GY.CX_GY_0893SW"/>
     <cge:Meas_Ref ObjectId="74236"/>
    <cge:TPSR_Ref TObjectID="16438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-74237">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4408.000000 -112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16439" ObjectName="SW-CX_GY.CX_GY_0896SW"/>
     <cge:Meas_Ref ObjectId="74237"/>
    <cge:TPSR_Ref TObjectID="16439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-75492">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 -304.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16484" ObjectName="SW-CX_GY.CX_GY_0121SW"/>
     <cge:Meas_Ref ObjectId="75492"/>
    <cge:TPSR_Ref TObjectID="16484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73915">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3950.000000 -1033.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16395" ObjectName="SW-CX_GY.CX_GY_1816SW"/>
     <cge:Meas_Ref ObjectId="73915"/>
    <cge:TPSR_Ref TObjectID="16395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-73914">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3950.000000 -906.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16394" ObjectName="SW-CX_GY.CX_GY_1811SW"/>
     <cge:Meas_Ref ObjectId="73914"/>
    <cge:TPSR_Ref TObjectID="16394"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_187c650" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1041.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_187c650" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1041.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_187c650" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1041.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_187c650" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1041.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_187c650" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1041.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_187c650" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1041.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_187c650" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1041.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_197dc80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3146.000000 -589.000000) translate(0,353)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b7da10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5082.000000 -1106.000000) translate(0,15)">果九T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b84460" transform="matrix(1.000000 0.000000 0.000000 1.000000 3566.000000 -383.000000) translate(0,15)">10kVIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b85310" transform="matrix(1.000000 0.000000 0.000000 1.000000 4786.000000 -1129.000000) translate(0,15)">35kVIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1b89f70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3278.000000 -1167.500000) translate(0,16)">果园变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bb7d20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3575.000000 -971.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bb8700" transform="matrix(1.000000 0.000000 0.000000 1.000000 3631.000000 -947.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bb8980" transform="matrix(1.000000 0.000000 0.000000 1.000000 3630.000000 -1030.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bc2ea0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3543.000000 -917.000000) translate(0,12)">110kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bc3d50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4483.000000 -887.000000) translate(0,12)">110kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bcd790" transform="matrix(1.000000 0.000000 0.000000 1.000000 5079.000000 -982.000000) translate(0,15)">果勤罗线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bd2380" transform="matrix(1.000000 0.000000 0.000000 1.000000 5087.000000 -608.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bda2a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5084.000000 -867.000000) translate(0,15)">果大T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cffb20" transform="matrix(1.000000 0.000000 0.000000 1.000000 5082.000000 -747.000000) translate(0,15)">果仁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d2f540" transform="matrix(1.000000 0.000000 0.000000 1.000000 3585.000000 -108.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d2f540" transform="matrix(1.000000 0.000000 0.000000 1.000000 3585.000000 -108.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d2f540" transform="matrix(1.000000 0.000000 0.000000 1.000000 3585.000000 -108.000000) translate(0,51)">下</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d2f540" transform="matrix(1.000000 0.000000 0.000000 1.000000 3585.000000 -108.000000) translate(0,69)">营</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d2f540" transform="matrix(1.000000 0.000000 0.000000 1.000000 3585.000000 -108.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d35ce0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3669.000000 -153.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d35ce0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3669.000000 -153.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d35ce0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3669.000000 -153.000000) translate(0,51)">所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d35ce0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3669.000000 -153.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d35ce0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3669.000000 -153.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d42d80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -117.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d42d80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -117.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d42d80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -117.000000) translate(0,51)">学</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d42d80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -117.000000) translate(0,69)">庄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d42d80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -117.000000) translate(0,87)">田</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d42d80" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -117.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d86850" transform="matrix(1.000000 0.000000 0.000000 1.000000 4107.000000 -116.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d86850" transform="matrix(1.000000 0.000000 0.000000 1.000000 4107.000000 -116.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d86850" transform="matrix(1.000000 0.000000 0.000000 1.000000 4107.000000 -116.000000) translate(0,51)">芦</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d86850" transform="matrix(1.000000 0.000000 0.000000 1.000000 4107.000000 -116.000000) translate(0,69)">子</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d86850" transform="matrix(1.000000 0.000000 0.000000 1.000000 4107.000000 -116.000000) translate(0,87)">沟</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d86850" transform="matrix(1.000000 0.000000 0.000000 1.000000 4107.000000 -116.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d947b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4200.000000 -116.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d947b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4200.000000 -116.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d947b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4200.000000 -116.000000) translate(0,51)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d947b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4200.000000 -116.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d947b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4200.000000 -116.000000) translate(0,87)">四</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1da1f40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4293.000000 -116.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1da1f40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4293.000000 -116.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1da1f40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4293.000000 -116.000000) translate(0,51)">革</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1da1f40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4293.000000 -116.000000) translate(0,69)">里</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1da1f40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4293.000000 -116.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1daf240" transform="matrix(1.000000 0.000000 0.000000 1.000000 4389.000000 -116.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1daf240" transform="matrix(1.000000 0.000000 0.000000 1.000000 4389.000000 -116.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1daf240" transform="matrix(1.000000 0.000000 0.000000 1.000000 4389.000000 -116.000000) translate(0,51)">清</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1daf240" transform="matrix(1.000000 0.000000 0.000000 1.000000 4389.000000 -116.000000) translate(0,69)">水</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1daf240" transform="matrix(1.000000 0.000000 0.000000 1.000000 4389.000000 -116.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db0e90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3873.000000 -52.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db0e90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3873.000000 -52.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db0e90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3873.000000 -52.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db0e90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3873.000000 -52.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db0e90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3873.000000 -52.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db0e90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3873.000000 -52.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db1e10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3998.000000 -47.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db1e10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3998.000000 -47.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db1e10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3998.000000 -47.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db1e10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3998.000000 -47.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db1e10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3998.000000 -47.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db1e10" transform="matrix(1.000000 0.000000 0.000000 1.000000 3998.000000 -47.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db21e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3260.000000 -252.000000) translate(0,12)">4773</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db27c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3818.000000 -1001.000000) translate(0,12)">183</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db2cb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3816.000000 -938.000000) translate(0,12)">1831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db2f30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3816.000000 -1062.000000) translate(0,12)">1836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db3170" transform="matrix(1.000000 0.000000 0.000000 1.000000 3754.000000 -992.000000) translate(0,12)">18317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db33b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3754.000000 -1049.000000) translate(0,12)">18360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db35f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3755.000000 -1117.000000) translate(0,12)">18367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db3830" transform="matrix(1.000000 0.000000 0.000000 1.000000 3968.000000 -1002.000000) translate(0,12)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db3a70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3966.000000 -939.000000) translate(0,12)">1811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db3cb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3966.000000 -1063.000000) translate(0,12)">1816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db3ef0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3904.000000 -1046.000000) translate(0,12)">18160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db4130" transform="matrix(1.000000 0.000000 0.000000 1.000000 3905.000000 -992.000000) translate(0,12)">18117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db4370" transform="matrix(1.000000 0.000000 0.000000 1.000000 3905.000000 -1117.000000) translate(0,12)">18167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db45b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4185.000000 -1003.000000) translate(0,12)">182</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db47f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4183.000000 -940.000000) translate(0,12)">1822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db4a30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4183.000000 -1064.000000) translate(0,12)">1826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db4c70" transform="matrix(1.000000 0.000000 0.000000 1.000000 4121.000000 -994.000000) translate(0,12)">18227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db4eb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4121.000000 -1051.000000) translate(0,12)">18260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db50f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4122.000000 -1119.000000) translate(0,12)">18267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db5330" transform="matrix(1.000000 0.000000 0.000000 1.000000 4475.000000 -977.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db5570" transform="matrix(1.000000 0.000000 0.000000 1.000000 4484.000000 -952.000000) translate(0,12)">19020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db57b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4485.000000 -1034.000000) translate(0,12)">19027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db59f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4037.000000 -845.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db5c30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4100.000000 -862.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db5e70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3956.000000 -861.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db60b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3956.000000 -793.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db62f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4100.000000 -794.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db6530" transform="matrix(1.000000 0.000000 0.000000 1.000000 3853.000000 -794.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db6770" transform="matrix(1.000000 0.000000 0.000000 1.000000 3851.000000 -866.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db69b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3793.000000 -847.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db6bf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3791.000000 -777.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db6e30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3792.000000 -701.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db7070" transform="matrix(1.000000 0.000000 0.000000 1.000000 3851.000000 -719.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db72b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3744.000000 -633.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db74f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3852.000000 -450.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db7730" transform="matrix(1.000000 0.000000 0.000000 1.000000 3850.000000 -400.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db7970" transform="matrix(1.000000 0.000000 0.000000 1.000000 3850.000000 -501.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db7bb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3728.000000 -394.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db7df0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3620.000000 -286.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db8030" transform="matrix(1.000000 0.000000 0.000000 1.000000 3618.000000 -333.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db8270" transform="matrix(1.000000 0.000000 0.000000 1.000000 3618.000000 -240.000000) translate(0,12)">0813</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db84b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3618.000000 -142.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db86f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3788.000000 -286.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db8930" transform="matrix(1.000000 0.000000 0.000000 1.000000 3786.000000 -333.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db8b70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3786.000000 -240.000000) translate(0,12)">0833</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db8db0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3786.000000 -142.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db8ff0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3705.000000 -332.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db9230" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -286.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db9470" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -333.000000) translate(0,12)">0841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db96b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -240.000000) translate(0,12)">0843</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db98f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -127.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db9b30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3874.000000 -177.000000) translate(0,12)">08460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db9d70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3873.000000 -115.000000) translate(0,12)">08467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db9fb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 56.000000) translate(0,12)">08400</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dba1f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4051.000000 -286.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dba480" transform="matrix(1.000000 0.000000 0.000000 1.000000 4049.000000 -333.000000) translate(0,12)">0851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dba9c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4049.000000 -240.000000) translate(0,12)">0853</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbac00" transform="matrix(1.000000 0.000000 0.000000 1.000000 4049.000000 -127.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbae40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3994.000000 -115.000000) translate(0,12)">08567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbb080" transform="matrix(1.000000 0.000000 0.000000 1.000000 3995.000000 -177.000000) translate(0,12)">08560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbb2c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4144.000000 -286.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbb500" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -333.000000) translate(0,12)">0861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbb740" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -240.000000) translate(0,12)">0863</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbb980" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -142.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbbbc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4237.000000 -286.000000) translate(0,12)">087</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbbe00" transform="matrix(1.000000 0.000000 0.000000 1.000000 4235.000000 -333.000000) translate(0,12)">0871</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbc040" transform="matrix(1.000000 0.000000 0.000000 1.000000 4235.000000 -240.000000) translate(0,12)">0873</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbc280" transform="matrix(1.000000 0.000000 0.000000 1.000000 4235.000000 -142.000000) translate(0,12)">0876</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbc4c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4330.000000 -286.000000) translate(0,12)">088</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbc700" transform="matrix(1.000000 0.000000 0.000000 1.000000 4328.000000 -333.000000) translate(0,12)">0881</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbc940" transform="matrix(1.000000 0.000000 0.000000 1.000000 4328.000000 -240.000000) translate(0,12)">0883</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbcb80" transform="matrix(1.000000 0.000000 0.000000 1.000000 4328.000000 -142.000000) translate(0,12)">0886</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbcdc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4426.000000 -286.000000) translate(0,12)">089</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbd000" transform="matrix(1.000000 0.000000 0.000000 1.000000 4424.000000 -333.000000) translate(0,12)">0891</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbd240" transform="matrix(1.000000 0.000000 0.000000 1.000000 4424.000000 -240.000000) translate(0,12)">0893</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbd480" transform="matrix(1.000000 0.000000 0.000000 1.000000 4424.000000 -142.000000) translate(0,12)">0896</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbd6c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4856.000000 -1098.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbd900" transform="matrix(1.000000 0.000000 0.000000 1.000000 4802.000000 -1100.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbdb40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4902.000000 -1100.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbdd80" transform="matrix(1.000000 0.000000 0.000000 1.000000 4856.000000 -973.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbdfc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4802.000000 -975.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbe200" transform="matrix(1.000000 0.000000 0.000000 1.000000 4901.000000 -975.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbe440" transform="matrix(1.000000 0.000000 0.000000 1.000000 4856.000000 -858.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbe680" transform="matrix(1.000000 0.000000 0.000000 1.000000 4802.000000 -860.000000) translate(0,12)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbe8c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4901.000000 -860.000000) translate(0,12)">3836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbeb00" transform="matrix(1.000000 0.000000 0.000000 1.000000 4856.000000 -739.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbed40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4802.000000 -741.000000) translate(0,12)">3841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbef80" transform="matrix(1.000000 0.000000 0.000000 1.000000 4902.000000 -741.000000) translate(0,12)">3846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dc4f40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4459.000000 -616.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dc5150" transform="matrix(1.000000 0.000000 0.000000 1.000000 4382.000000 -618.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dc5390" transform="matrix(1.000000 0.000000 0.000000 1.000000 4529.000000 -618.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc8950" transform="matrix(1.000000 0.000000 0.000000 1.000000 3778.000000 -1279.000000) translate(0,15)">仙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc8950" transform="matrix(1.000000 0.000000 0.000000 1.000000 3778.000000 -1279.000000) translate(0,33)">果</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc8950" transform="matrix(1.000000 0.000000 0.000000 1.000000 3778.000000 -1279.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc8ec0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3927.000000 -1268.000000) translate(0,15)">狮</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc8ec0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3927.000000 -1268.000000) translate(0,33)">果</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc8ec0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3927.000000 -1268.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc8ec0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3927.000000 -1268.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc8ec0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3927.000000 -1268.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc9750" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -1271.000000) translate(0,15)">狮</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc9750" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -1271.000000) translate(0,33)">果</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc9750" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -1271.000000) translate(0,51)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc9750" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -1271.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc9750" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -1271.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc99d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4337.000000 -1228.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dc99d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4337.000000 -1228.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dcba50" transform="matrix(1.000000 0.000000 0.000000 1.000000 3918.000000 -564.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dccf30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4738.000000 -975.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dcd170" transform="matrix(1.000000 0.000000 0.000000 1.000000 4803.000000 -626.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dd3db0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4511.000000 -334.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dd4790" transform="matrix(1.000000 0.000000 0.000000 1.000000 4049.000000 56.000000) translate(0,12)">08500</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dd65a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3544.000000 -684.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dd65a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3544.000000 -684.000000) translate(0,27)">SFSZ10-50000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dd65a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3544.000000 -684.000000) translate(0,42)">110±8×1.25%/38.5±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dd65a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3544.000000 -684.000000) translate(0,57)">U12%=10.6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dd65a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3544.000000 -684.000000) translate(0,72)">U13%=18.67</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dd65a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3544.000000 -684.000000) translate(0,87)">U23%=6.84</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dd65a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3544.000000 -684.000000) translate(0,102)">50MVA/50MVA/50MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dd65a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3544.000000 -684.000000) translate(0,117)">YN,yn0,d11</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1b5fda0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3757.000000 -538.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b6a9d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.666667 -916.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b6c9f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3675.666667 -998.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b713a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 -733.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b75d30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 -734.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb8e90" refnum="0">
    <use class="BV-0KV" transform="matrix(0.954545 -0.000000 0.000000 -1.000000 4530.000000 -920.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bbbc30" refnum="0">
    <use class="BV-0KV" transform="matrix(0.954545 -0.000000 0.000000 -1.000000 4529.000000 -1002.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bde6a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.000000 -1085.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bdf130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.000000 -1017.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bdfbc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.000000 -960.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1be7d00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3878.000000 -1085.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1be8790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3878.000000 -1017.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1be9220" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3878.000000 -960.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cc61a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 -1087.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cc6c30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 -1019.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cc76c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 -962.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cdd980" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -1086.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cde410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -1018.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cdeea0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 -961.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d18800" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -815.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d19290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -745.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d19d20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -669.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d5a2c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3915.000000 110.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d5b210" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 -145.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d5bca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 -83.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d70a80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4036.000000 110.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d719d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 -145.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d72460" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 -83.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_GY"/>
</svg>