<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-36" aopId="0" id="thSvg" viewBox="3116 -1198 2152 1202">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
    <line stroke-width="0.5" x1="3" x2="16" y1="35" y2="10"/>
    <line stroke-width="0.5" x1="14" x2="14" y1="9" y2="9"/>
    <line stroke-width="0.5" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line stroke-width="0.5" x1="15" x2="3" y1="35" y2="10"/>
    <line stroke-width="0.5" x1="14" x2="14" y1="9" y2="9"/>
    <line stroke-width="0.5" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.208305" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.208305" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.208305" width="26" x="9" y="3"/>
    <line stroke-width="0.5" x1="9" x2="34" y1="16" y2="5"/>
    <line stroke-width="0.5" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line stroke-width="0.5" x1="34" x2="10" y1="15" y2="4"/>
    <line stroke-width="0.5" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.208305" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="earth:shape3">
    <line stroke-width="0.185606" x1="29" x2="29" y1="7" y2="11"/>
    <line stroke-width="0.226608" x1="4" x2="22" y1="9" y2="9"/>
    <line stroke-width="0.226608" x1="22" x2="22" y1="0" y2="18"/>
    <line stroke-width="0.226608" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line stroke-width="0.226608" x1="5" x2="5" y1="13" y2="5"/>
    <line stroke-width="0.185606" x1="2" x2="2" y1="11" y2="7"/>
    <line stroke-width="0.226608" x1="26" x2="9" y1="9" y2="9"/>
    <line stroke-width="0.226608" x1="9" x2="9" y1="18" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line stroke-width="0.305732" x1="2" x2="13" y1="8" y2="8"/>
    <line stroke-width="0.125874" x1="7" x2="7" y1="9" y2="17"/>
    <line stroke-width="0.196875" x1="11" x2="4" y1="4" y2="4"/>
    <line stroke-width="0.125" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="0.5" width="12" x="1" y="18"/>
    <line stroke-width="0.5" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line stroke-width="0.5" x1="6" x2="6" y1="50" y2="42"/>
    <line stroke-width="0.5" x1="3" x2="9" y1="41" y2="41"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <rect height="4" stroke-width="0.5" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line stroke-width="0.5" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="0.5" width="12" x="1" y="20"/>
    <line stroke-width="0.125" x1="9" x2="6" y1="63" y2="63"/>
    <line stroke-width="0.196875" x1="11" x2="4" y1="60" y2="60"/>
    <line stroke-width="0.125874" x1="7" x2="7" y1="55" y2="47"/>
    <line stroke-width="0.305732" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line stroke-width="0.5" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" rx="8.5" ry="7.5"/>
    <line stroke-width="0.0778547" x1="25" x2="20" y1="8" y2="10"/>
    <line stroke-width="0.0778547" x1="25" x2="20" y1="8" y2="5"/>
    <line stroke-width="0.0778547" x1="20" x2="20" y1="10" y2="5"/>
    <line stroke-width="0.0778547" x1="8" x2="5" y1="7" y2="9"/>
    <line stroke-width="0.0778547" x1="10" x2="8" y1="9" y2="7"/>
    <line stroke-width="0.0778547" x1="8" x2="8" y1="4" y2="7"/>
    <line stroke-width="0.0778547" x1="14" x2="11" y1="18" y2="20"/>
    <line stroke-width="0.0778547" x1="16" x2="14" y1="20" y2="18"/>
    <line stroke-width="0.0778547" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" rx="8.5" ry="7.5"/>
    <ellipse cx="14" cy="16" rx="9" ry="7.5"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line stroke-width="0.305732" x1="55" x2="55" y1="12" y2="1"/>
    <line stroke-width="0.125874" x1="54" x2="46" y1="8" y2="8"/>
    <line stroke-width="0.196875" x1="59" x2="59" y1="3" y2="10"/>
    <line stroke-width="0.125" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="0.5" width="26" x="19" y="1"/>
    <line stroke-width="0.5" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape133">
    <line stroke-width="0.5" x1="77" x2="117" y1="2" y2="2"/>
    <line stroke-width="0.5" x1="117" x2="117" y1="85" y2="2"/>
    <line stroke-width="0.5" x1="83" x2="117" y1="85" y2="85"/>
    <line stroke-width="0.5" x1="77" x2="77" y1="2" y2="2"/>
    <line stroke-width="0.144552" x1="34" x2="54" y1="20" y2="20"/>
    <line stroke-width="0.144552" x1="34" x2="54" y1="12" y2="12"/>
    <line stroke-width="0.15625" x1="44" x2="44" y1="20" y2="30"/>
    <line stroke-width="0.15625" x1="10" x2="10" y1="2" y2="12"/>
    <line stroke-width="0.144552" x1="0" x2="20" y1="20" y2="20"/>
    <line stroke-width="0.144552" x1="0" x2="20" y1="12" y2="12"/>
    <line stroke-width="0.15625" x1="10" x2="10" y1="20" y2="30"/>
    <line stroke-width="0.15625" x1="77" x2="77" y1="2" y2="12"/>
    <line stroke-width="0.144552" x1="67" x2="87" y1="20" y2="20"/>
    <line stroke-width="0.144552" x1="67" x2="87" y1="12" y2="12"/>
    <line stroke-width="0.15625" x1="77" x2="77" y1="20" y2="30"/>
    <line stroke-width="0.5" x1="10" x2="77" y1="2" y2="2"/>
    <line stroke-width="0.15625" x1="44" x2="44" y1="2" y2="12"/>
    <ellipse cx="83" cy="103" rx="13" ry="12.5"/>
    <line stroke-width="0.132653" x1="79" x2="83" y1="89" y2="85"/>
    <line stroke-width="0.132653" x1="83" x2="87" y1="85" y2="89"/>
    <line stroke-width="0.132653" x1="83" x2="83" y1="81" y2="85"/>
    <line stroke-width="0.132653" x1="79" x2="83" y1="109" y2="105"/>
    <line stroke-width="0.132653" x1="83" x2="87" y1="105" y2="109"/>
    <line stroke-width="0.132653" x1="83" x2="83" y1="101" y2="105"/>
    <circle cx="83" cy="85" r="13"/>
    <line stroke-width="0.185606" x1="81" x2="85" y1="48" y2="48"/>
    <line stroke-width="0.226608" x1="83" x2="83" y1="73" y2="55"/>
    <line stroke-width="0.226608" x1="74" x2="92" y1="55" y2="55"/>
    <line stroke-width="0.226608" x1="80" x2="87" y1="52" y2="52"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line stroke-width="0.166154" x1="13" x2="13" y1="39" y2="47"/>
    <line stroke-width="0.22" x1="13" x2="13" y1="5" y2="26"/>
    <line stroke-width="0.24" x1="0" x2="12" y1="26" y2="26"/>
    <polyline fill="none" points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 "/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line stroke-width="0.162432" x1="7" x2="15" y1="48" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line stroke-width="0.162432" x1="15" x2="15" y1="51" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line stroke-width="0.162432" x1="7" x2="15" y1="48" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line stroke-width="0.162432" x1="15" x2="15" y1="51" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line stroke-width="0.162432" x1="17" x2="0" y1="34" y2="26"/>
    <line stroke-width="0.234885" x1="-9" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="27" y2="25"/>
    <line stroke-width="0.234885" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line stroke-width="0.234885" x1="-8" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="24" y2="27"/>
    <line stroke-width="0.234885" x1="19" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line stroke-width="0.234885" x1="18" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="27" y2="25"/>
    <line stroke-width="0.234885" x1="-9" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line stroke-width="0.234885" x1="-8" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="24" y2="27"/>
    <line stroke-width="0.234885" x1="19" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line stroke-width="0.5" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="0.5" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line stroke-width="0.5" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line stroke-width="0.5" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="0.5" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line stroke-width="0.5" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line stroke-width="0.5" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="0.5" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line stroke-width="0.5" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line stroke-width="0.5" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="0.5" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line stroke-width="0.5" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5"/>
    <line stroke-width="0.132653" x1="9" x2="13" y1="40" y2="36"/>
    <line stroke-width="0.132653" x1="13" x2="17" y1="36" y2="40"/>
    <line stroke-width="0.132653" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" r="13"/>
    <line stroke-width="0.132653" x1="9" x2="13" y1="20" y2="16"/>
    <line stroke-width="0.132653" x1="13" x2="17" y1="16" y2="20"/>
    <line stroke-width="0.132653" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" r="24"/>
    <line stroke-width="0.255102" x1="26" x2="17" y1="34" y2="18"/>
    <line stroke-width="0.255102" x1="26" x2="34" y1="34" y2="18"/>
    <line stroke-width="0.255102" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" rx="24" ry="24.5"/>
    <line stroke-width="0.255102" x1="24" x2="24" y1="58" y2="66"/>
    <line stroke-width="0.255102" x1="24" x2="32" y1="66" y2="74"/>
    <line stroke-width="0.255102" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="2.07143" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="2.07143" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="0.5" width="111" x="0" y="0"/>
    <line stroke="rgb(50,205,50)" stroke-width="1.5" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 19.000000) translate(0,12)">禁止刷新</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(127,127,127);fill:none}
.BKBV-0KV { stroke:rgb(127,127,127);fill:rgb(127,127,127)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(0,72,216);fill:none}
.BKBV-10KV { stroke:rgb(0,72,216);fill:rgb(0,72,216)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(0,255,0);fill:none}
.BKBV-20KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(213,0,0);fill:none}
.BKBV-110KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-220KV { stroke:rgb(255,0,255);fill:none}
.BKBV-220KV { stroke:rgb(255,0,255);fill:rgb(255,0,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(255,0,0);fill:none}
.BKBV-500KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-750KV { stroke:rgb(255,0,0);fill:none}
.BKBV-750KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-22KV { stroke:rgb(255,255,255);fill:none}
.BKBV-22KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-380KV { stroke:rgb(255,255,255);fill:none}
.BKBV-380KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1212" width="2162" x="3111" y="-1203"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4677.000000 787.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4691.000000 757.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 772.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 655.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4258.000000 625.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4233.000000 640.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4677.000000 655.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4691.000000 625.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 640.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3823.000000 100.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3837.000000 70.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3812.000000 85.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.000000 100.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4046.000000 70.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4021.000000 85.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4408.000000 100.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4422.000000 70.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4397.000000 85.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4564.000000 100.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4578.000000 70.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4553.000000 85.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4709.000000 100.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.000000 70.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.000000 85.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 0.000000 0.500000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4913.000000 93.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4938.000000 78.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4282.000000 431.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.000000 401.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 416.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.065147 -763.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32314">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.065147 -611.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5291" ObjectName="SW-CX_LC.CX_LC_421BK"/>
     <cge:Meas_Ref ObjectId="32314"/>
    <cge:TPSR_Ref TObjectID="5291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.057818 -762.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9613" ObjectName="SW-CX_LC.CX_LC_302BK"/>
     <cge:Meas_Ref ObjectId="54435"/>
    <cge:TPSR_Ref TObjectID="9613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54439">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.057818 -610.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9616" ObjectName="SW-CX_LC.CX_LC_402BK"/>
     <cge:Meas_Ref ObjectId="54439"/>
    <cge:TPSR_Ref TObjectID="9616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54443">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -469.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9619" ObjectName="SW-CX_LC.CX_LC_412BK"/>
     <cge:Meas_Ref ObjectId="54443"/>
    <cge:TPSR_Ref TObjectID="9619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32333">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4950.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5309" ObjectName="SW-CX_LC.CX_LC_481BK"/>
     <cge:Meas_Ref ObjectId="32333"/>
    <cge:TPSR_Ref TObjectID="5309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32324">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4456.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5300" ObjectName="SW-CX_LC.CX_LC_484BK"/>
     <cge:Meas_Ref ObjectId="32324"/>
    <cge:TPSR_Ref TObjectID="5300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32327">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4604.500000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5303" ObjectName="SW-CX_LC.CX_LC_483BK"/>
     <cge:Meas_Ref ObjectId="32327"/>
    <cge:TPSR_Ref TObjectID="5303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32330">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5306" ObjectName="SW-CX_LC.CX_LC_482BK"/>
     <cge:Meas_Ref ObjectId="32330"/>
    <cge:TPSR_Ref TObjectID="5306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32318">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3840.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5294" ObjectName="SW-CX_LC.CX_LC_486BK"/>
     <cge:Meas_Ref ObjectId="32318"/>
    <cge:TPSR_Ref TObjectID="5294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32321">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4080.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5297" ObjectName="SW-CX_LC.CX_LC_485BK"/>
     <cge:Meas_Ref ObjectId="32321"/>
    <cge:TPSR_Ref TObjectID="5297"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3885,-878 4834,-878 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3885,-878 4834,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3631,-543 4315,-543 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3631,-543 4315,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-543 5055,-543 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4371,-543 5055,-543 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="3382890">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 -948.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3403240">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4578.000000 -947.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="35ebbc0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4684.057818 -803.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3494010">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4868.000000 -360.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="283e310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-878 4132,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-878 4132,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="36cd350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-933 4132,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="338c810@0" ObjectIDZND2="28e00d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="338c810_0" Pin0InfoVect2LinkObjId="28e00d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-933 4132,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3405730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-958 4148,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="338c810@0" ObjectIDND2="28e00d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="338c810_0" Pin1InfoVect2LinkObjId="28e00d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-958 4148,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="28f8fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4184,-958 4201,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="3382890@0" Pin0InfoVect0LinkObjId="3382890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4184,-958 4201,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2831830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-1061 4173,-1061 4173,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="28e00d0@0" ObjectIDZND0="338c810@0" Pin0InfoVect0LinkObjId="338c810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="28e00d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-1061 4173,-1061 4173,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="361a310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-1061 4132,-1123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="338c810@0" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="338c810_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-1061 4132,-1123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="345ff80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-958 4132,-1025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="338c810@0" ObjectIDZND1="28e00d0@0" Pin0InfoVect0LinkObjId="338c810_0" Pin0InfoVect1LinkObjId="28e00d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-958 4132,-1025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="28681b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-1025 4132,-1061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="28e00d0@0" ObjectIDZND0="338c810@0" Pin0InfoVect0LinkObjId="338c810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="28e00d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-1025 4132,-1061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="29af350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-1025 4066,-1025 4066,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="338c810@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="28e00d0@0" Pin0InfoVect0LinkObjId="28e00d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="338c810_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-1025 4066,-1025 4066,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="29bb560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4066,-971 4066,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="28e00d0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="28e00d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4066,-971 4066,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2904640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-878 4515,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-878 4515,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="33971e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4514,-957 4530,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="393b8d0@0" ObjectIDND2="38d82b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="393b8d0_0" Pin1InfoVect2LinkObjId="38d82b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4514,-957 4530,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="339a4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4566,-957 4583,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="3403240@0" Pin0InfoVect0LinkObjId="3403240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4566,-957 4583,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="3527ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-931 4515,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="393b8d0@0" ObjectIDZND2="38d82b0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="393b8d0_0" Pin0InfoVect2LinkObjId="38d82b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-931 4515,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="1db5060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-974 4471,-974 4471,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="38d82b0@0" ObjectIDZND0="393b8d0@0" Pin0InfoVect0LinkObjId="393b8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="38d82b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-974 4471,-974 4471,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="243b3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-957 4515,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="393b8d0@0" ObjectIDZND1="38d82b0@0" Pin0InfoVect0LinkObjId="393b8d0_0" Pin0InfoVect1LinkObjId="38d82b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-957 4515,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="337b7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4515,-974 4515,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="393b8d0@0" ObjectIDZND0="38d82b0@0" Pin0InfoVect0LinkObjId="38d82b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="393b8d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4515,-974 4515,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2990230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-543 3814,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-543 3814,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="337bf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-616 3839,-616 3839,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="2a0e1c0@0" ObjectIDZND0="39d88d0@0" Pin0InfoVect0LinkObjId="39d88d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="2a0e1c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-616 3839,-616 3839,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="221f750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3814,-616 3814,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="39d88d0@0" ObjectIDND1="2a0e1c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="39d88d0_0" Pin1InfoVect1LinkObjId="2a0e1c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3814,-616 3814,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="283e960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-688 3792,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="36218e0@0" ObjectIDZND0="2a0e1c0@0" Pin0InfoVect0LinkObjId="2a0e1c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="36218e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-688 3792,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="39628e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-627 3792,-616 3814,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="2a0e1c0@1" ObjectIDZND0="39d88d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="39d88d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="2a0e1c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-627 3792,-616 3814,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="25655f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-701 3766,-701 3766,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="27d5790@0" Pin0InfoVect0LinkObjId="27d5790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-701 3766,-701 3766,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="28a6720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-878 4186,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-878 4186,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2665150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-818 4186,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-818 4186,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="35435a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-771 4186,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-771 4186,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="281b930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-669 4186,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="5291@1" Pin0InfoVect0LinkObjId="SW-32314_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-669 4186,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="36458b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-619 4186,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5291@0" ObjectIDZND0="5292@1" Pin0InfoVect0LinkObjId="SW-32315_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-619 4186,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="35e8770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-565 4186,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5292@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32315_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-565 4186,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="28860c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-770 4620,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="9613@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-770 4620,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="35e60d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-668 4620,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="9616@1" Pin0InfoVect0LinkObjId="SW-54439_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-668 4620,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="38d3150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-618 4620,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9616@0" ObjectIDZND0="9617@1" Pin0InfoVect0LinkObjId="SW-54440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-618 4620,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="38d7480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-564 4620,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9617@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-564 4620,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="35cb200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-862 4620,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9614@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-862 4620,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="35d3e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-813 4636,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9613@x" ObjectIDND1="9614@x" ObjectIDZND0="9615@0" Pin0InfoVect0LinkObjId="SW-54437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54435_0" Pin1InfoVect1LinkObjId="SW-54436_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-813 4636,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="3516ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-813 4689,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9615@1" ObjectIDZND0="35ebbc0@0" Pin0InfoVect0LinkObjId="35ebbc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54437_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-813 4689,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="3380550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-797 4620,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="9613@1" ObjectIDZND0="9615@x" ObjectIDZND1="9614@x" Pin0InfoVect0LinkObjId="SW-54437_0" Pin0InfoVect1LinkObjId="SW-54436_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-797 4620,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="3641d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4620,-813 4620,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="9615@x" ObjectIDND1="9613@x" ObjectIDZND0="9614@0" Pin0InfoVect0LinkObjId="SW-54436_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54437_0" Pin1InfoVect1LinkObjId="SW-54435_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4620,-813 4620,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="35e5070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-543 5004,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-543 5004,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="29769f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-614 5021,-614 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="2737c80@0" ObjectIDZND0="3395520@0" Pin0InfoVect0LinkObjId="3395520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="2737c80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-614 5021,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2690e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-597 5004,-614 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="3395520@0" ObjectIDZND1="2737c80@0" Pin0InfoVect0LinkObjId="3395520_0" Pin0InfoVect1LinkObjId="2737c80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-597 5004,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="39d9ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-614 5004,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3395520@0" ObjectIDND1="0@x" ObjectIDZND0="2737c80@1" Pin0InfoVect0LinkObjId="2737c80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="3395520_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-614 5004,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="39d5780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5005,-683 5005,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="2737c80@0" ObjectIDZND0="33a5a70@0" Pin0InfoVect0LinkObjId="33a5a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="2737c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5005,-683 5005,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="35d7cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-714 4984,-714 4984,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="3517ef0@0" Pin0InfoVect0LinkObjId="3517ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-714 4984,-714 4984,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3404610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-543 4404,-479 4386,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="9620@1" Pin0InfoVect0LinkObjId="SW-54444_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-543 4404,-479 4386,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39ced60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-479 4330,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9620@0" ObjectIDZND0="9619@0" Pin0InfoVect0LinkObjId="SW-54443_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54444_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-479 4330,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1db5e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-479 4289,-479 4289,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="9619@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54443_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-479 4289,-479 4289,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="33cf110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-543 4959,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="5310@1" Pin0InfoVect0LinkObjId="SW-32334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-543 4959,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3881b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-490 4959,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5310@0" ObjectIDZND0="5309@1" Pin0InfoVect0LinkObjId="SW-32333_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32334_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-490 4959,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3376380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-445 4959,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5309@0" ObjectIDZND0="5311@1" Pin0InfoVect0LinkObjId="SW-32335_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32333_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-445 4959,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="2c34530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-390 4959,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5311@0" ObjectIDZND0="0@x" ObjectIDZND1="3384450@0" ObjectIDZND2="2a13500@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="3384450_0" Pin0InfoVect2LinkObjId="2a13500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-390 4959,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="2908300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-368 4942,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="5311@x" ObjectIDND1="3384450@0" ObjectIDND2="2a13500@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-32335_0" Pin1InfoVect1LinkObjId="3384450_0" Pin1InfoVect2LinkObjId="2a13500_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-368 4942,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="2908560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4906,-368 4893,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="3494010@0" Pin0InfoVect0LinkObjId="3494010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4906,-368 4893,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="2737850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-368 4959,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="5311@x" ObjectIDND1="0@x" ObjectIDZND0="3384450@0" ObjectIDZND1="2a13500@0" ObjectIDZND2="35e4e90@0" Pin0InfoVect0LinkObjId="3384450_0" Pin0InfoVect1LinkObjId="2a13500_0" Pin0InfoVect2LinkObjId="35e4e90_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-32335_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-368 4959,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="33841f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4975,-352 4975,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5311@x" ObjectIDND1="0@x" ObjectIDND2="35e4e90@0" ObjectIDZND0="3384450@0" Pin0InfoVect0LinkObjId="3384450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-32335_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="35e4e90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4975,-352 4975,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3394da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-353 4975,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="5311@x" ObjectIDND1="0@x" ObjectIDND2="35e4e90@0" ObjectIDZND0="3384450@0" ObjectIDZND1="2a13500@0" Pin0InfoVect0LinkObjId="3384450_0" Pin0InfoVect1LinkObjId="2a13500_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-32335_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="35e4e90_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-353 4975,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3395000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-298 4997,-353 4975,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="2a13500@0" ObjectIDZND0="3384450@0" ObjectIDZND1="5311@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="3384450_0" Pin0InfoVect1LinkObjId="SW-32335_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="2a13500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-298 4997,-353 4975,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="338e430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-353 4959,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="5311@x" ObjectIDND1="0@x" ObjectIDND2="3384450@0" ObjectIDZND0="35e4e90@1" Pin0InfoVect0LinkObjId="35e4e90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-32335_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="3384450_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-353 4959,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="338e660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-238 4959,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="35e4e90@0" ObjectIDZND0="2a13500@1" Pin0InfoVect0LinkObjId="2a13500_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="35e4e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-238 4959,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="33884c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-543 4465,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="5301@1" Pin0InfoVect0LinkObjId="SW-32325_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-543 4465,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3398f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-490 4465,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5301@0" ObjectIDZND0="5300@1" Pin0InfoVect0LinkObjId="SW-32324_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-490 4465,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="37df8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-445 4465,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5300@0" ObjectIDZND0="5302@1" Pin0InfoVect0LinkObjId="SW-32326_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-445 4465,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="28d7b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-366 4496,-366 4496,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5302@x" ObjectIDZND0="39677a0@0" Pin0InfoVect0LinkObjId="39677a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32326_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-366 4496,-366 4496,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="28d7d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-390 4465,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5302@0" ObjectIDZND0="39677a0@0" Pin0InfoVect0LinkObjId="39677a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32326_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-390 4465,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3967540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-366 4465,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="5302@x" ObjectIDND1="39677a0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-32326_0" Pin1InfoVect1LinkObjId="39677a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-366 4465,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3362230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4615,-543 4614,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="5304@1" Pin0InfoVect0LinkObjId="SW-32328_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4615,-543 4614,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="351c3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-490 4614,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5304@0" ObjectIDZND0="5303@1" Pin0InfoVect0LinkObjId="SW-32327_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32328_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-490 4614,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="350efa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-445 4614,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5303@0" ObjectIDZND0="5305@1" Pin0InfoVect0LinkObjId="SW-32329_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32327_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-445 4614,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="350f200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-366 4645,-366 4645,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5305@x" ObjectIDZND0="3947c70@0" Pin0InfoVect0LinkObjId="3947c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32329_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-366 4645,-366 4645,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="350f460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-390 4614,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5305@0" ObjectIDZND0="3947c70@0" Pin0InfoVect0LinkObjId="3947c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32329_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-390 4614,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3947a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4614,-366 4614,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="5305@x" ObjectIDND1="3947c70@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-32329_0" Pin1InfoVect1LinkObjId="3947c70_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4614,-366 4614,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39646f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-543 4761,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="5307@1" Pin0InfoVect0LinkObjId="SW-32331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-543 4761,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="394f8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-490 4761,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5307@0" ObjectIDZND0="5306@1" Pin0InfoVect0LinkObjId="SW-32330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-490 4761,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="37e0570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-445 4761,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5306@0" ObjectIDZND0="5308@1" Pin0InfoVect0LinkObjId="SW-32332_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-445 4761,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="37e07d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-366 4792,-366 4792,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5308@x" ObjectIDZND0="3383aa0@0" Pin0InfoVect0LinkObjId="3383aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32332_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-366 4792,-366 4792,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="37e0a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-390 4761,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5308@0" ObjectIDZND0="3383aa0@0" Pin0InfoVect0LinkObjId="3383aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32332_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-390 4761,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3383840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-366 4761,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="5308@x" ObjectIDND1="3383aa0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-32332_0" Pin1InfoVect1LinkObjId="3383aa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-366 4761,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3519a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3849,-543 3849,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="5295@1" Pin0InfoVect0LinkObjId="SW-32319_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3849,-543 3849,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="35e6d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3849,-490 3849,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5295@0" ObjectIDZND0="5294@1" Pin0InfoVect0LinkObjId="SW-32318_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32319_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3849,-490 3849,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3395b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3849,-445 3849,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5294@0" ObjectIDZND0="5296@1" Pin0InfoVect0LinkObjId="SW-32320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32318_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3849,-445 3849,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3395dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3849,-366 3880,-366 3880,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5296@x" ObjectIDZND0="33964f0@0" Pin0InfoVect0LinkObjId="33964f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3849,-366 3880,-366 3880,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3396030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3849,-390 3849,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5296@0" ObjectIDZND0="33964f0@0" Pin0InfoVect0LinkObjId="33964f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3849,-390 3849,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3396290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3849,-366 3849,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="5296@x" ObjectIDND1="33964f0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-32320_0" Pin1InfoVect1LinkObjId="33964f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3849,-366 3849,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3882930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-543 4089,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="5298@1" Pin0InfoVect0LinkObjId="SW-32322_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-543 4089,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="351a1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-490 4089,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5298@0" ObjectIDZND0="5297@1" Pin0InfoVect0LinkObjId="SW-32321_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-490 4089,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="394fc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-445 4089,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5297@0" ObjectIDZND0="5299@1" Pin0InfoVect0LinkObjId="SW-32323_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32321_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-445 4089,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="394fe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-366 4120,-366 4120,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5299@x" ObjectIDZND0="39505b0@0" Pin0InfoVect0LinkObjId="39505b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-366 4120,-366 4120,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="39500f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-390 4089,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5299@0" ObjectIDZND0="39505b0@0" Pin0InfoVect0LinkObjId="39505b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-32323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-390 4089,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="3950350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-366 4089,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="5299@x" ObjectIDND1="39505b0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-32323_0" Pin1InfoVect1LinkObjId="39505b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-366 4089,-213 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer"/><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">罗川变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3991.000000 -908.000000) translate(0,18)">35kV#1站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4139.000000 -1158.000000) translate(0,18)">罗川支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4767.000000 -904.000000) translate(0,18)">35kV 母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3795.000000 -749.000000) translate(0,18)">10kVI母PT</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5011.000000 -749.000000) translate(0,18)">10kVII母PT</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3604.000000 -577.000000) translate(0,18)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4892.000000 -157.000000) translate(0,18)">#1电容器750千乏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4454.000000 -193.000000) translate(0,18)">南</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4454.000000 -193.000000) translate(0,40)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4454.000000 -193.000000) translate(0,62)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4602.500000 -193.000000) translate(0,18)">彩</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4602.500000 -193.000000) translate(0,40)">云</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4602.500000 -193.000000) translate(0,62)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4750.000000 -193.000000) translate(0,18)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4750.000000 -193.000000) translate(0,40)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3838.000000 -193.000000) translate(0,18)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3838.000000 -193.000000) translate(0,40)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3838.000000 -193.000000) translate(0,62)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -193.000000) translate(0,18)">竹</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -193.000000) translate(0,40)">溪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -193.000000) translate(0,62)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4145.000000 -926.000000) translate(0,15)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4526.000000 -924.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4199.000000 -793.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3822.000000 -589.000000) translate(0,15)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5016.000000 -589.000000) translate(0,15)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4152.000000 -987.000000) translate(0,15)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4532.000000 -988.000000) translate(0,15)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4579.000000 -791.000000) translate(0,15)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4632.000000 -842.000000) translate(0,15)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4569.000000 -853.000000) translate(0,15)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3858.000000 -468.000000) translate(0,15)">486</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3856.000000 -517.000000) translate(0,15)">4861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3856.000000 -417.000000) translate(0,15)">4862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4098.000000 -468.000000) translate(0,15)">485</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4096.000000 -417.000000) translate(0,15)">4852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4096.000000 -517.000000) translate(0,15)">4851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4195.000000 -642.000000) translate(0,15)">421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4629.000000 -641.000000) translate(0,15)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4628.000000 -591.000000) translate(0,15)">4021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4194.000000 -592.000000) translate(0,15)">4212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4303.000000 -506.000000) translate(0,15)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4349.000000 -467.000000) translate(0,15)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4474.000000 -468.000000) translate(0,15)">484</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4472.000000 -417.000000) translate(0,15)">4842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4472.000000 -517.000000) translate(0,15)">4841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4623.000000 -468.000000) translate(0,15)">483</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4622.000000 -417.000000) translate(0,15)">4832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4622.000000 -517.000000) translate(0,15)">4831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4770.000000 -468.000000) translate(0,15)">482</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4768.000000 -417.000000) translate(0,15)">4822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4768.000000 -517.000000) translate(0,15)">4821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4968.000000 -468.000000) translate(0,15)">481</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4966.000000 -417.000000) translate(0,15)">4812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4966.000000 -517.000000) translate(0,15)">4811</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(213,0,0)" stroke-width="0.5" width="360" x="3117" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="1200" stroke="rgb(213,0,0)" stroke-width="0.5" width="2150" x="3117" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(213,0,0)" stroke-width="0.5" width="360" x="3117" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(213,0,0)" stroke-width="0.5" width="360" x="3117" y="-597"/>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 -910.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 -910.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4162.065147 -664.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4162.065147 -664.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4596.057818 -663.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4596.057818 -663.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" stroke="rgb(255,0,0)" width="138" x="3248" y="-1177"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" stroke="rgb(255,0,0)" width="77" x="3199" y="-1194"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="338c810">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4166.000000 -989.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="28e00d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 -966.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="393b8d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4464.000000 -981.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="38d82b0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4529.000000 -1040.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="39d88d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.851792 -622.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2a0e1c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3786.851792 -622.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="36218e0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3805.851792 -712.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="27d5790">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3759.000000 -707.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3395520">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5016.100163 -608.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2737c80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4999.100163 -633.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="33a5a70">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5019.100163 -722.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3517ef0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4977.000000 -720.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3384450">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4968.000000 -273.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="2a13500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.000000 -183.000000)" xlink:href="#lightningRod:shape133"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="35e4e90">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4972.000000 -233.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="39677a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3947c70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4637.500000 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="3383aa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="33964f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="39505b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4113.000000 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4117.000000 -875.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4157.000000 -932.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4500.000000 -873.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4539.000000 -931.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3798.851792 -539.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.065147 -813.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32315">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.065147 -543.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5292" ObjectName="SW-CX_LC.CX_LC_4212SW"/>
     <cge:Meas_Ref ObjectId="32315"/>
    <cge:TPSR_Ref TObjectID="5292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54440">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.057818 -542.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9617" ObjectName="SW-CX_LC.CX_LC_4021SW"/>
     <cge:Meas_Ref ObjectId="54440"/>
    <cge:TPSR_Ref TObjectID="9617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54436">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.057818 -804.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9614" ObjectName="SW-CX_LC.CX_LC_3021SW"/>
     <cge:Meas_Ref ObjectId="54436"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54437">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4645.057818 -787.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9615" ObjectName="SW-CX_LC.CX_LC_30217SW"/>
     <cge:Meas_Ref ObjectId="54437"/>
    <cge:TPSR_Ref TObjectID="9615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4989.100163 -539.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54444">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4359.000000 -453.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9620" ObjectName="SW-CX_LC.CX_LC_4121SW"/>
     <cge:Meas_Ref ObjectId="54444"/>
    <cge:TPSR_Ref TObjectID="9620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32334">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4944.000000 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5310" ObjectName="SW-CX_LC.CX_LC_4811SW"/>
     <cge:Meas_Ref ObjectId="32334"/>
    <cge:TPSR_Ref TObjectID="5310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32335">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4944.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5311" ObjectName="SW-CX_LC.CX_LC_4812SW"/>
     <cge:Meas_Ref ObjectId="32335"/>
    <cge:TPSR_Ref TObjectID="5311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.000000 -342.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4450.000000 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5301" ObjectName="SW-CX_LC.CX_LC_4841SW"/>
     <cge:Meas_Ref ObjectId="32325"/>
    <cge:TPSR_Ref TObjectID="5301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32326">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4450.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5302" ObjectName="SW-CX_LC.CX_LC_4842SW"/>
     <cge:Meas_Ref ObjectId="32326"/>
    <cge:TPSR_Ref TObjectID="5302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32328">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.500000 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5304" ObjectName="SW-CX_LC.CX_LC_4831SW"/>
     <cge:Meas_Ref ObjectId="32328"/>
    <cge:TPSR_Ref TObjectID="5304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32329">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.500000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5305" ObjectName="SW-CX_LC.CX_LC_4832SW"/>
     <cge:Meas_Ref ObjectId="32329"/>
    <cge:TPSR_Ref TObjectID="5305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32331">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4746.000000 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5307" ObjectName="SW-CX_LC.CX_LC_4821SW"/>
     <cge:Meas_Ref ObjectId="32331"/>
    <cge:TPSR_Ref TObjectID="5307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32332">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4746.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5308" ObjectName="SW-CX_LC.CX_LC_4822SW"/>
     <cge:Meas_Ref ObjectId="32332"/>
    <cge:TPSR_Ref TObjectID="5308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32319">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5295" ObjectName="SW-CX_LC.CX_LC_4861SW"/>
     <cge:Meas_Ref ObjectId="32319"/>
    <cge:TPSR_Ref TObjectID="5295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32320">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5296" ObjectName="SW-CX_LC.CX_LC_4862SW"/>
     <cge:Meas_Ref ObjectId="32320"/>
    <cge:TPSR_Ref TObjectID="5296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32322">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.000000 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5298" ObjectName="SW-CX_LC.CX_LC_4851SW"/>
     <cge:Meas_Ref ObjectId="32322"/>
    <cge:TPSR_Ref TObjectID="5298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-32323">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5299" ObjectName="SW-CX_LC.CX_LC_4852SW"/>
     <cge:Meas_Ref ObjectId="32323"/>
    <cge:TPSR_Ref TObjectID="5299"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-54359" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 -787.000000) translate(0,12)">54359.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54359" ObjectName="CX_LC.CX_LC_302BK:F"/>
     <cge:PSR_Ref ObjectID="9613"/>
     <cge:Term_Ref ObjectID="13657"/>
    <cge:TPSR_Ref TObjectID="9613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-54360" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 -787.000000) translate(0,27)">54360.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54360" ObjectName="CX_LC.CX_LC_302BK:F"/>
     <cge:PSR_Ref ObjectID="9613"/>
     <cge:Term_Ref ObjectID="13657"/>
    <cge:TPSR_Ref TObjectID="9613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-54355" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 -787.000000) translate(0,42)">54355.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54355" ObjectName="CX_LC.CX_LC_302BK:F"/>
     <cge:PSR_Ref ObjectID="9613"/>
     <cge:Term_Ref ObjectID="13657"/>
    <cge:TPSR_Ref TObjectID="9613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-54374" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 -655.000000) translate(0,12)">54374.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54374" ObjectName="CX_LC.CX_LC_402BK:F"/>
     <cge:PSR_Ref ObjectID="9616"/>
     <cge:Term_Ref ObjectID="13663"/>
    <cge:TPSR_Ref TObjectID="9616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-54375" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 -655.000000) translate(0,27)">54375.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54375" ObjectName="CX_LC.CX_LC_402BK:F"/>
     <cge:PSR_Ref ObjectID="9616"/>
     <cge:Term_Ref ObjectID="13663"/>
    <cge:TPSR_Ref TObjectID="9616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-54370" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 -655.000000) translate(0,42)">54370.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54370" ObjectName="CX_LC.CX_LC_402BK:F"/>
     <cge:PSR_Ref ObjectID="9616"/>
     <cge:Term_Ref ObjectID="13663"/>
    <cge:TPSR_Ref TObjectID="9616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-32298" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4307.000000 -655.000000) translate(0,12)">32298.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="32298" ObjectName="CX_LC.CX_LC_421BK:F"/>
     <cge:PSR_Ref ObjectID="5291"/>
     <cge:Term_Ref ObjectID="7676"/>
    <cge:TPSR_Ref TObjectID="5291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-32299" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4307.000000 -655.000000) translate(0,27)">32299.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="32299" ObjectName="CX_LC.CX_LC_421BK:F"/>
     <cge:PSR_Ref ObjectID="5291"/>
     <cge:Term_Ref ObjectID="7676"/>
    <cge:TPSR_Ref TObjectID="5291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-32294" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4307.000000 -655.000000) translate(0,42)">32294.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="32294" ObjectName="CX_LC.CX_LC_421BK:F"/>
     <cge:PSR_Ref ObjectID="5291"/>
     <cge:Term_Ref ObjectID="7676"/>
    <cge:TPSR_Ref TObjectID="5291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-54305" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3892.000000 -100.000000) translate(0,12)">54305.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54305" ObjectName="CX_LC.CX_LC_486BK:F"/>
     <cge:PSR_Ref ObjectID="5294"/>
     <cge:Term_Ref ObjectID="7682"/>
    <cge:TPSR_Ref TObjectID="5294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-32301" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3892.000000 -100.000000) translate(0,27)">32301.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="32301" ObjectName="CX_LC.CX_LC_486BK:F"/>
     <cge:PSR_Ref ObjectID="5294"/>
     <cge:Term_Ref ObjectID="7682"/>
    <cge:TPSR_Ref TObjectID="5294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-54301" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3892.000000 -100.000000) translate(0,42)">54301.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54301" ObjectName="CX_LC.CX_LC_486BK:F"/>
     <cge:PSR_Ref ObjectID="5294"/>
     <cge:Term_Ref ObjectID="7682"/>
    <cge:TPSR_Ref TObjectID="5294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-54311" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4104.000000 -100.000000) translate(0,12)">54311.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54311" ObjectName="CX_LC.CX_LC_485BK:F"/>
     <cge:PSR_Ref ObjectID="5297"/>
     <cge:Term_Ref ObjectID="7688"/>
    <cge:TPSR_Ref TObjectID="5297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-54312" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4104.000000 -100.000000) translate(0,27)">54312.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54312" ObjectName="CX_LC.CX_LC_485BK:F"/>
     <cge:PSR_Ref ObjectID="5297"/>
     <cge:Term_Ref ObjectID="7688"/>
    <cge:TPSR_Ref TObjectID="5297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-32302" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4104.000000 -100.000000) translate(0,42)">32302.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="32302" ObjectName="CX_LC.CX_LC_485BK:F"/>
     <cge:PSR_Ref ObjectID="5297"/>
     <cge:Term_Ref ObjectID="7688"/>
    <cge:TPSR_Ref TObjectID="5297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-54385" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4340.000000 -431.000000) translate(0,12)">54385.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54385" ObjectName="CX_LC.CX_LC_412BK:F"/>
     <cge:PSR_Ref ObjectID="9619"/>
     <cge:Term_Ref ObjectID="13669"/>
    <cge:TPSR_Ref TObjectID="9619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-54386" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4340.000000 -431.000000) translate(0,27)">54386.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54386" ObjectName="CX_LC.CX_LC_412BK:F"/>
     <cge:PSR_Ref ObjectID="9619"/>
     <cge:Term_Ref ObjectID="13669"/>
    <cge:TPSR_Ref TObjectID="9619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-54381" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4340.000000 -431.000000) translate(0,42)">54381.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54381" ObjectName="CX_LC.CX_LC_412BK:F"/>
     <cge:PSR_Ref ObjectID="9619"/>
     <cge:Term_Ref ObjectID="13669"/>
    <cge:TPSR_Ref TObjectID="9619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-54319" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4480.000000 -100.000000) translate(0,12)">54319.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54319" ObjectName="CX_LC.CX_LC_484BK:F"/>
     <cge:PSR_Ref ObjectID="5300"/>
     <cge:Term_Ref ObjectID="7694"/>
    <cge:TPSR_Ref TObjectID="5300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-32303" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4480.000000 -100.000000) translate(0,27)">32303.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="32303" ObjectName="CX_LC.CX_LC_484BK:F"/>
     <cge:PSR_Ref ObjectID="5300"/>
     <cge:Term_Ref ObjectID="7694"/>
    <cge:TPSR_Ref TObjectID="5300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-54315" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4480.000000 -100.000000) translate(0,42)">54315.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54315" ObjectName="CX_LC.CX_LC_484BK:F"/>
     <cge:PSR_Ref ObjectID="5300"/>
     <cge:Term_Ref ObjectID="7694"/>
    <cge:TPSR_Ref TObjectID="5300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-54325" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4629.000000 -100.000000) translate(0,12)">54325.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54325" ObjectName="CX_LC.CX_LC_483BK:F"/>
     <cge:PSR_Ref ObjectID="5303"/>
     <cge:Term_Ref ObjectID="7700"/>
    <cge:TPSR_Ref TObjectID="5303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-54326" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4629.000000 -100.000000) translate(0,27)">54326.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54326" ObjectName="CX_LC.CX_LC_483BK:F"/>
     <cge:PSR_Ref ObjectID="5303"/>
     <cge:Term_Ref ObjectID="7700"/>
    <cge:TPSR_Ref TObjectID="5303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-32304" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4629.000000 -100.000000) translate(0,42)">32304.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="32304" ObjectName="CX_LC.CX_LC_483BK:F"/>
     <cge:PSR_Ref ObjectID="5303"/>
     <cge:Term_Ref ObjectID="7700"/>
    <cge:TPSR_Ref TObjectID="5303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-54333" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4776.000000 -100.000000) translate(0,12)">54333.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54333" ObjectName="CX_LC.CX_LC_482BK:F"/>
     <cge:PSR_Ref ObjectID="5306"/>
     <cge:Term_Ref ObjectID="7706"/>
    <cge:TPSR_Ref TObjectID="5306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-32305" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4776.000000 -100.000000) translate(0,27)">32305.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="32305" ObjectName="CX_LC.CX_LC_482BK:F"/>
     <cge:PSR_Ref ObjectID="5306"/>
     <cge:Term_Ref ObjectID="7706"/>
    <cge:TPSR_Ref TObjectID="5306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-54329" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4776.000000 -100.000000) translate(0,42)">54329.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54329" ObjectName="CX_LC.CX_LC_482BK:F"/>
     <cge:PSR_Ref ObjectID="5306"/>
     <cge:Term_Ref ObjectID="7706"/>
    <cge:TPSR_Ref TObjectID="5306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-54340" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4984.000000 -92.500000) translate(0,12)">54340.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54340" ObjectName="CX_LC.CX_LC_481BK:F"/>
     <cge:PSR_Ref ObjectID="5309"/>
     <cge:Term_Ref ObjectID="7712"/>
    <cge:TPSR_Ref TObjectID="5309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-32306" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4984.000000 -92.500000) translate(0,27)">32306.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="32306" ObjectName="CX_LC.CX_LC_481BK:F"/>
     <cge:PSR_Ref ObjectID="5309"/>
     <cge:Term_Ref ObjectID="7712"/>
    <cge:TPSR_Ref TObjectID="5309"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4"/>
</svg>