<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-38" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3115 -1203 2147 1213">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape95">
    <ellipse cx="20" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="20" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="20" x2="20" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="34" x2="29" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="34" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="31" x2="29" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="31" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="32" x2="32" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="21" x2="21" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="9" x2="0" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="6" x2="3" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="7" x2="2" y1="4" y2="4"/>
    <polyline points="21,19 5,19 5,6 " stroke-width="1"/>
    <ellipse cx="31" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="20" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="31" cy="7" rx="7.5" ry="6.5" stroke-width="0.726474"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="lightningRod:shape83">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="19" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="5" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="58" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="5" y2="11"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="68" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="68" y2="59"/>
    <polyline arcFlag="1" points="13,48 12,48 12,48 11,48 10,48 10,49 9,49 8,50 8,50 8,51 7,52 7,52 7,53 7,54 7,55 7,55 8,56 8,57 8,57 9,58 10,58 10,59 11,59 12,59 12,59 13,59 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,37 12,37 12,37 11,37 10,37 10,38 9,38 8,39 8,39 8,40 7,41 7,41 7,42 7,43 7,44 7,44 8,45 8,46 8,46 9,47 10,47 10,48 11,48 12,48 12,48 13,48 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,26 12,26 12,26 11,26 10,26 10,27 9,27 8,28 8,28 8,29 7,30 7,30 7,31 7,32 7,33 7,33 8,34 8,35 8,35 9,36 10,36 10,37 11,37 12,37 12,37 13,37 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="26" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.529576" x1="37" x2="37" y1="75" y2="28"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape22">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="55" x2="60" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="46" x2="51" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="38" x2="43" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="30" x2="35" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="86" x2="72" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="18" y1="9" y2="9"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="11" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="19" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="33" y2="33"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,100 64,93 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21ba9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bb3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21bbd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21bca60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21bdc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21be870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bf420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bfd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c0380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c0d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c0d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c2550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c2550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_21c31d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c4dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c5980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c6270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21c6b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c8290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c8a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c9150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21c98e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21ca960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21cb2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21cbdd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21d0d90" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21d1ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21cdbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21cf0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21cfb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21dd780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21d38c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1223" width="2157" x="3110" y="-1208"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-597"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1197"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-37539">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -684.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5966" ObjectName="SW-CX_HX.CX_HX_301XC"/>
     <cge:Meas_Ref ObjectId="37539"/>
    <cge:TPSR_Ref TObjectID="5966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37539">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -602.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5967" ObjectName="SW-CX_HX.CX_HX_301XC1"/>
     <cge:Meas_Ref ObjectId="37539"/>
    <cge:TPSR_Ref TObjectID="5967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4582.000000 -683.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -951.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -870.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.000000 -951.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.000000 -870.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37540">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4162.000000 -760.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5969" ObjectName="SW-CX_HX.CX_HX_381XC1"/>
     <cge:Meas_Ref ObjectId="37540"/>
    <cge:TPSR_Ref TObjectID="5969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37540">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4162.000000 -842.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5968" ObjectName="SW-CX_HX.CX_HX_381XC"/>
     <cge:Meas_Ref ObjectId="37540"/>
    <cge:TPSR_Ref TObjectID="5968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37541">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -763.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5971" ObjectName="SW-CX_HX.CX_HX_382XC1"/>
     <cge:Meas_Ref ObjectId="37541"/>
    <cge:TPSR_Ref TObjectID="5971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37541">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -844.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5970" ObjectName="SW-CX_HX.CX_HX_382XC"/>
     <cge:Meas_Ref ObjectId="37541"/>
    <cge:TPSR_Ref TObjectID="5970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37542">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4470.000000 -840.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5972" ObjectName="SW-CX_HX.CX_HX_383XC"/>
     <cge:Meas_Ref ObjectId="37542"/>
    <cge:TPSR_Ref TObjectID="5972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37542">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4470.000000 -761.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5973" ObjectName="SW-CX_HX.CX_HX_383XC1"/>
     <cge:Meas_Ref ObjectId="37542"/>
    <cge:TPSR_Ref TObjectID="5973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37442">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.000000 -589.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5942" ObjectName="SW-CX_HX.CX_HX_30167SW"/>
     <cge:Meas_Ref ObjectId="37442"/>
    <cge:TPSR_Ref TObjectID="5942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37545">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 -1115.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5975" ObjectName="SW-CX_HX.CX_HX_38167SW"/>
     <cge:Meas_Ref ObjectId="37545"/>
    <cge:TPSR_Ref TObjectID="5975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37544">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.000000 -1062.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5974" ObjectName="SW-CX_HX.CX_HX_3816SW"/>
     <cge:Meas_Ref ObjectId="37544"/>
    <cge:TPSR_Ref TObjectID="5974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58725">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -1006.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11286" ObjectName="SW-CX_HX.CX_HX_3823SW"/>
     <cge:Meas_Ref ObjectId="58725"/>
    <cge:TPSR_Ref TObjectID="11286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37475">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4377.000000 -877.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5948" ObjectName="SW-CX_HX.CX_HX_38267SW"/>
     <cge:Meas_Ref ObjectId="37475"/>
    <cge:TPSR_Ref TObjectID="5948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37480">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4500.000000 -844.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5950" ObjectName="SW-CX_HX.CX_HX_38367SW"/>
     <cge:Meas_Ref ObjectId="37480"/>
    <cge:TPSR_Ref TObjectID="5950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37496">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4625.000000 -246.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5961" ObjectName="SW-CX_HX.CX_HX_0911SW"/>
     <cge:Meas_Ref ObjectId="37496"/>
    <cge:TPSR_Ref TObjectID="5961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37497">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.000000 -244.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5962" ObjectName="SW-CX_HX.CX_HX_0916SW"/>
     <cge:Meas_Ref ObjectId="37497"/>
    <cge:TPSR_Ref TObjectID="5962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37557">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.000000 -392.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5986" ObjectName="SW-CX_HX.CX_HX_0941SW"/>
     <cge:Meas_Ref ObjectId="37557"/>
    <cge:TPSR_Ref TObjectID="5986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37555">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 -246.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5984" ObjectName="SW-CX_HX.CX_HX_0931SW"/>
     <cge:Meas_Ref ObjectId="37555"/>
    <cge:TPSR_Ref TObjectID="5984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37553">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4891.000000 -98.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5982" ObjectName="SW-CX_HX.CX_HX_0921SW"/>
     <cge:Meas_Ref ObjectId="37553"/>
    <cge:TPSR_Ref TObjectID="5982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37547">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3798.000000 -91.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5976" ObjectName="SW-CX_HX.CX_HX_0721SW"/>
     <cge:Meas_Ref ObjectId="37547"/>
    <cge:TPSR_Ref TObjectID="5976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37549">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3799.000000 -239.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5978" ObjectName="SW-CX_HX.CX_HX_0731SW"/>
     <cge:Meas_Ref ObjectId="37549"/>
    <cge:TPSR_Ref TObjectID="5978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37551">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3800.000000 -385.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5980" ObjectName="SW-CX_HX.CX_HX_0741SW"/>
     <cge:Meas_Ref ObjectId="37551"/>
    <cge:TPSR_Ref TObjectID="5980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37492">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 -239.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5958" ObjectName="SW-CX_HX.CX_HX_0716SW"/>
     <cge:Meas_Ref ObjectId="37492"/>
    <cge:TPSR_Ref TObjectID="5958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 -239.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5957" ObjectName="SW-CX_HX.CX_HX_0711SW"/>
     <cge:Meas_Ref ObjectId="37491"/>
    <cge:TPSR_Ref TObjectID="5957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37552">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3738.000000 -332.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5981" ObjectName="SW-CX_HX.CX_HX_07467SW"/>
     <cge:Meas_Ref ObjectId="37552"/>
    <cge:TPSR_Ref TObjectID="5981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37550">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3737.000000 -186.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5979" ObjectName="SW-CX_HX.CX_HX_07367SW"/>
     <cge:Meas_Ref ObjectId="37550"/>
    <cge:TPSR_Ref TObjectID="5979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37548">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3736.000000 -38.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5977" ObjectName="SW-CX_HX.CX_HX_07267SW"/>
     <cge:Meas_Ref ObjectId="37548"/>
    <cge:TPSR_Ref TObjectID="5977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37560">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4216.000000 -253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5988" ObjectName="SW-CX_HX.CX_HX_0901SW"/>
     <cge:Meas_Ref ObjectId="37560"/>
    <cge:TPSR_Ref TObjectID="5988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37495">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4158.000000 -253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5960" ObjectName="SW-CX_HX.CX_HX_0811SW"/>
     <cge:Meas_Ref ObjectId="37495"/>
    <cge:TPSR_Ref TObjectID="5960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37482">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4353.000000 -255.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5951" ObjectName="SW-CX_HX.CX_HX_0821SW"/>
     <cge:Meas_Ref ObjectId="37482"/>
    <cge:TPSR_Ref TObjectID="5951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37488">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4500.000000 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5955" ObjectName="SW-CX_HX.CX_HX_0831SW"/>
     <cge:Meas_Ref ObjectId="37488"/>
    <cge:TPSR_Ref TObjectID="5955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4500.000000 -142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5954" ObjectName="SW-CX_HX.CX_HX_0836SW"/>
     <cge:Meas_Ref ObjectId="41431"/>
    <cge:TPSR_Ref TObjectID="5954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37483">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4353.000000 -143.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5952" ObjectName="SW-CX_HX.CX_HX_0826SW"/>
     <cge:Meas_Ref ObjectId="37483"/>
    <cge:TPSR_Ref TObjectID="5952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37500">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4566.000000 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5964" ObjectName="SW-CX_HX.CX_HX_0841SW"/>
     <cge:Meas_Ref ObjectId="37500"/>
    <cge:TPSR_Ref TObjectID="5964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37558">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4987.000000 -339.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5987" ObjectName="SW-CX_HX.CX_HX_09467SW"/>
     <cge:Meas_Ref ObjectId="37558"/>
    <cge:TPSR_Ref TObjectID="5987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37556">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4988.000000 -194.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5985" ObjectName="SW-CX_HX.CX_HX_09367SW"/>
     <cge:Meas_Ref ObjectId="37556"/>
    <cge:TPSR_Ref TObjectID="5985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37554">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4989.000000 -45.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5983" ObjectName="SW-CX_HX.CX_HX_09267SW"/>
     <cge:Meas_Ref ObjectId="37554"/>
    <cge:TPSR_Ref TObjectID="5983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37449">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -325.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5945" ObjectName="SW-CX_HX.CX_HX_0011SW"/>
     <cge:Meas_Ref ObjectId="37449"/>
    <cge:TPSR_Ref TObjectID="5945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.000000 -1069.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11287" ObjectName="SW-CX_HX.CX_HX_38237SW"/>
     <cge:Meas_Ref ObjectId="58724"/>
    <cge:TPSR_Ref TObjectID="11287"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4034.000000 -737.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4034.000000 -737.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -422.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -422.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1481130">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -996.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_146e3a0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.024390 4572.500000 -522.536585)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12a8a10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.000000 -521.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14cbef0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4402.500000 -62.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17d8e10">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4549.500000 -61.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17c6cc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -177.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17c75a0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4266.500000 -181.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17fd3c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4205.000000 -90.000000)" xlink:href="#lightningRod:shape95"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1709ac0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3686.000000 -231.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17db560">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3672.500000 -270.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_181b7c0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3590.500000 -426.500000)" xlink:href="#lightningRod:shape83"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_181c700">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3685.000000 -377.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17b1e80">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3673.500000 -416.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17b2a80">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3588.500000 -132.500000)" xlink:href="#lightningRod:shape83"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17538b0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3685.000000 -83.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1771a30">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3671.500000 -122.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1772600">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3589.500000 -280.500000)" xlink:href="#lightningRod:shape83"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1801b90">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3903.500000 -269.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16f56f0">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5070.500000 -277.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17d9e70">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5153.500000 -433.500000)" xlink:href="#lightningRod:shape83"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17b9f70">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5056.000000 -384.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16eb690">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5069.500000 -423.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16ec290">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5155.500000 -139.500000)" xlink:href="#lightningRod:shape83"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17beed0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5058.000000 -90.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17bfe60">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 5071.500000 -129.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_174d300">
    <use class="BV-10KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5154.500000 -287.500000)" xlink:href="#lightningRod:shape83"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_174ea60">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4854.500000 -280.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1711720">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5057.000000 -238.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1825200">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4278.500000 -588.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_176fe30">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4620.500000 -599.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17bd2b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4583.000000 -605.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1730b50">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4089.500000 -1113.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17eecb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 -813.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17ef510">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4081.500000 -937.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_176b0c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 -804.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1715aa0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4326.500000 -867.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16cc650">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4348.000000 -939.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17509a0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4454.500000 -835.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17df980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4475.000000 -908.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_171ec20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 -902.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_171f890">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 -909.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_165c3e0">
    <use class="BV-10KV" transform="matrix(0.274430 -0.000000 0.000000 -1.000000 4135.787879 -235.000000)" xlink:href="#lightningRod:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_165d780">
    <use class="BV-10KV" transform="matrix(0.274430 -0.000000 0.000000 -1.000000 4586.787879 -242.000000)" xlink:href="#lightningRod:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1df02f0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4270.500000 -1067.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -499.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -483.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3233.000000 -1115.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78573" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3248.538462 -1014.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78573" ObjectName="CX_HX:CX_HX_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37402" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -800.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5946"/>
     <cge:Term_Ref ObjectID="8604"/>
    <cge:TPSR_Ref TObjectID="5946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37403" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -800.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5946"/>
     <cge:Term_Ref ObjectID="8604"/>
    <cge:TPSR_Ref TObjectID="5946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37401" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4267.000000 -800.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5946"/>
     <cge:Term_Ref ObjectID="8604"/>
    <cge:TPSR_Ref TObjectID="5946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37406" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -800.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5947"/>
     <cge:Term_Ref ObjectID="8606"/>
    <cge:TPSR_Ref TObjectID="5947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37407" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -800.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5947"/>
     <cge:Term_Ref ObjectID="8606"/>
    <cge:TPSR_Ref TObjectID="5947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37405" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -800.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5947"/>
     <cge:Term_Ref ObjectID="8606"/>
    <cge:TPSR_Ref TObjectID="5947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37410" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4593.000000 -808.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5949"/>
     <cge:Term_Ref ObjectID="8610"/>
    <cge:TPSR_Ref TObjectID="5949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37411" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4593.000000 -808.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37411" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5949"/>
     <cge:Term_Ref ObjectID="8610"/>
    <cge:TPSR_Ref TObjectID="5949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37409" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4593.000000 -808.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37409" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5949"/>
     <cge:Term_Ref ObjectID="8610"/>
    <cge:TPSR_Ref TObjectID="5949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37392" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.500000 -691.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37392" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5943"/>
     <cge:Term_Ref ObjectID="8598"/>
    <cge:TPSR_Ref TObjectID="5943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37393" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.500000 -691.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5943"/>
     <cge:Term_Ref ObjectID="8598"/>
    <cge:TPSR_Ref TObjectID="5943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37391" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.500000 -691.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5943"/>
     <cge:Term_Ref ObjectID="8598"/>
    <cge:TPSR_Ref TObjectID="5943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37397" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -419.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37397" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5944"/>
     <cge:Term_Ref ObjectID="8600"/>
    <cge:TPSR_Ref TObjectID="5944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37398" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -419.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5944"/>
     <cge:Term_Ref ObjectID="8600"/>
    <cge:TPSR_Ref TObjectID="5944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37396" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -419.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37396" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5944"/>
     <cge:Term_Ref ObjectID="8600"/>
    <cge:TPSR_Ref TObjectID="5944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37414" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4393.000000 -35.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37414" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5953"/>
     <cge:Term_Ref ObjectID="8618"/>
    <cge:TPSR_Ref TObjectID="5953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37415" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4393.000000 -35.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37415" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5953"/>
     <cge:Term_Ref ObjectID="8618"/>
    <cge:TPSR_Ref TObjectID="5953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37413" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4393.000000 -35.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37413" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5953"/>
     <cge:Term_Ref ObjectID="8618"/>
    <cge:TPSR_Ref TObjectID="5953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-37418" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4556.000000 -35.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37418" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5956"/>
     <cge:Term_Ref ObjectID="8624"/>
    <cge:TPSR_Ref TObjectID="5956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37419" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4556.000000 -35.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37419" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5956"/>
     <cge:Term_Ref ObjectID="8624"/>
    <cge:TPSR_Ref TObjectID="5956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37417" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4556.000000 -35.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37417" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5956"/>
     <cge:Term_Ref ObjectID="8624"/>
    <cge:TPSR_Ref TObjectID="5956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-37441" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -514.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37441" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5989"/>
     <cge:Term_Ref ObjectID="8690"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-37430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -407.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37430" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5941"/>
     <cge:Term_Ref ObjectID="8595"/>
    <cge:TPSR_Ref TObjectID="5941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-37431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -407.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5941"/>
     <cge:Term_Ref ObjectID="8595"/>
    <cge:TPSR_Ref TObjectID="5941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-37432" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -407.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37432" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5941"/>
     <cge:Term_Ref ObjectID="8595"/>
    <cge:TPSR_Ref TObjectID="5941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-37434" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -407.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37434" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5941"/>
     <cge:Term_Ref ObjectID="8595"/>
    <cge:TPSR_Ref TObjectID="5941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-37433" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -407.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37433" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5941"/>
     <cge:Term_Ref ObjectID="8595"/>
    <cge:TPSR_Ref TObjectID="5941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-37425" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4724.000000 -827.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37425" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5940"/>
     <cge:Term_Ref ObjectID="8594"/>
    <cge:TPSR_Ref TObjectID="5940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-37426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4724.000000 -827.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5940"/>
     <cge:Term_Ref ObjectID="8594"/>
    <cge:TPSR_Ref TObjectID="5940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-37427" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4724.000000 -827.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37427" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5940"/>
     <cge:Term_Ref ObjectID="8594"/>
    <cge:TPSR_Ref TObjectID="5940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-37429" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4724.000000 -827.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37429" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5940"/>
     <cge:Term_Ref ObjectID="8594"/>
    <cge:TPSR_Ref TObjectID="5940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-37428" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4724.000000 -827.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37428" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5940"/>
     <cge:Term_Ref ObjectID="8594"/>
    <cge:TPSR_Ref TObjectID="5940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37424" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4701.000000 -212.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37424" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5963"/>
     <cge:Term_Ref ObjectID="8638"/>
    <cge:TPSR_Ref TObjectID="5963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4701.000000 -212.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5963"/>
     <cge:Term_Ref ObjectID="8638"/>
    <cge:TPSR_Ref TObjectID="5963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-37422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -212.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37422" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5959"/>
     <cge:Term_Ref ObjectID="8630"/>
    <cge:TPSR_Ref TObjectID="5959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-37421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -212.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="37421" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="5959"/>
     <cge:Term_Ref ObjectID="8630"/>
    <cge:TPSR_Ref TObjectID="5959"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3245" y="-1174"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3245" y="-1174"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3196" y="-1191"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3196" y="-1191"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3245" y="-1174"/></g>
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3196" y="-1191"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-37485">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4353.000000 -199.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5953" ObjectName="SW-CX_HX.CX_HX_082BK"/>
     <cge:Meas_Ref ObjectId="37485"/>
    <cge:TPSR_Ref TObjectID="5953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37490">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4500.000000 -198.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5956" ObjectName="SW-CX_HX.CX_HX_083BK"/>
     <cge:Meas_Ref ObjectId="37490"/>
    <cge:TPSR_Ref TObjectID="5956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37448">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -375.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5944" ObjectName="SW-CX_HX.CX_HX_001BK"/>
     <cge:Meas_Ref ObjectId="37448"/>
    <cge:TPSR_Ref TObjectID="5944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37444">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.214286 -0.000000 0.000000 -1.131579 4354.000000 -632.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5943" ObjectName="SW-CX_HX.CX_HX_301BK"/>
     <cge:Meas_Ref ObjectId="37444"/>
    <cge:TPSR_Ref TObjectID="5943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37473">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.214286 -0.000000 0.000000 -1.131579 4342.000000 -793.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5947" ObjectName="SW-CX_HX.CX_HX_382BK"/>
     <cge:Meas_Ref ObjectId="37473"/>
    <cge:TPSR_Ref TObjectID="5947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37478">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.214286 -0.000000 0.000000 -1.131579 4469.000000 -791.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5949" ObjectName="SW-CX_HX.CX_HX_383BK"/>
     <cge:Meas_Ref ObjectId="37478"/>
    <cge:TPSR_Ref TObjectID="5949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37470">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.214286 -0.000000 0.000000 -1.131579 4161.000000 -789.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5946" ObjectName="SW-CX_HX.CX_HX_381BK"/>
     <cge:Meas_Ref ObjectId="37470"/>
    <cge:TPSR_Ref TObjectID="5946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.214286 -1.131579 -0.000000 4998.000000 -386.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10504" ObjectName="SW-CX_HX.CX_HX_094BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.214286 -1.131579 -0.000000 4999.000000 -240.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10503" ObjectName="SW-CX_HX.CX_HX_093BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.214286 -1.131579 -0.000000 5000.000000 -92.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10502" ObjectName="SW-CX_HX.CX_HX_092BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37493">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.214286 -1.131579 -0.000000 4082.000000 -233.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5959" ObjectName="SW-CX_HX.CX_HX_071BK"/>
     <cge:Meas_Ref ObjectId="37493"/>
    <cge:TPSR_Ref TObjectID="5959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.214286 -1.131579 -0.000000 3798.000000 -379.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10501" ObjectName="SW-CX_HX.CX_HX_074BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.214286 -1.131579 -0.000000 3797.000000 -233.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10500" ObjectName="SW-CX_HX.CX_HX_073BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.214286 -1.131579 -0.000000 3796.000000 -85.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10499" ObjectName="SW-CX_HX.CX_HX_072BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-37498">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.214286 -1.131579 -0.000000 4724.000000 -238.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5963" ObjectName="SW-CX_HX.CX_HX_091BK"/>
     <cge:Meas_Ref ObjectId="37498"/>
    <cge:TPSR_Ref TObjectID="5963"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1666900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4461.000000 -588.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1992f80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.000000 -847.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1993a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -819.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d61ec0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3741.000000 -303.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d62950" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 -157.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d633e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 -9.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d63e70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4990.000000 -310.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d64900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4991.000000 -164.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d65390" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4992.000000 -16.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d68880" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.000000 -1114.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1de0fe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4447.000000 -1068.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="5940" cx="4172" cy="-739" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5940" cx="4353" cy="-739" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5940" cx="4480" cy="-739" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5940" cx="4366" cy="-739" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5940" cx="4593" cy="-739" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5941" cx="4863" cy="-249" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5941" cx="3879" cy="-390" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5941" cx="3879" cy="-96" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5941" cx="4863" cy="-397" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5941" cx="4863" cy="-251" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5941" cx="4863" cy="-103" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5941" cx="4225" cy="-314" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5941" cx="4167" cy="-314" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5941" cx="4362" cy="-314" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5941" cx="4509" cy="-314" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5941" cx="4575" cy="-314" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="5941" cx="4364" cy="-314" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14b2fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4309.000000 -59.000000) translate(0,15)">球团主控I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17d95e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 -58.000000) translate(0,15)">球团主控II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17fe7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4189.000000 -87.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17fe7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4189.000000 -87.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17b8f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3492.000000 -418.000000) translate(0,15)">600kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17b8f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3492.000000 -418.000000) translate(0,33)">3号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15ad4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3489.000000 -270.000000) translate(0,15)">600kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15ad4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3489.000000 -270.000000) translate(0,33)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16f4e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3489.000000 -123.000000) translate(0,15)">600kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16f4e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3489.000000 -123.000000) translate(0,33)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_174f470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5157.000000 -414.000000) translate(0,15)">600kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_174f470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5157.000000 -414.000000) translate(0,33)">6号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1711240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5156.000000 -270.000000) translate(0,15)">600kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1711240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5156.000000 -270.000000) translate(0,33)">5号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17113f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5162.000000 -120.000000) translate(0,15)">600kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17113f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5162.000000 -120.000000) translate(0,33)">4号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1820730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4557.000000 -514.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1820730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4557.000000 -514.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1820c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4275.000000 -456.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16d9840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3997.000000 -733.000000) translate(0,15)">35kV站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1720760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4136.000000 -821.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17a2aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -1092.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17a2e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -1146.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17a3250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -827.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17a3a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4391.000000 -916.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17a3f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4440.000000 -827.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17a41c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4516.000000 -884.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178ac10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4097.000000 -759.000000) translate(0,12)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178b980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4375.000000 -666.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178c020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -620.000000) translate(0,12)">30167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178c2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4375.000000 -404.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178c4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4371.000000 -355.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178c720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4375.000000 -228.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178c960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -285.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178cba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -173.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178cde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4522.000000 -227.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178d020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4520.000000 -284.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178d260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4520.000000 -172.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178d4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -283.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178dda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4174.000000 -283.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178e100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4047.000000 -268.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178e340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4091.000000 -270.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178e580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3995.000000 -270.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_178e7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4145.000000 -334.000000) translate(0,12)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a0d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3807.000000 -416.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a11c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -362.000000) translate(0,12)">07467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a1440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -270.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a1680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -216.000000) translate(0,12)">07367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a18c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -122.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a1b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3752.000000 -68.000000) translate(0,12)">07267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a1d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4582.000000 -297.000000) translate(0,12)">0841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a1f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4685.000000 -273.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a21c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4632.000000 -278.000000) translate(0,12)">0911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a2400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4734.000000 -275.000000) translate(0,12)">0916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a2640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4894.000000 -423.000000) translate(0,12)">0941</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a2880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5003.000000 -369.000000) translate(0,12)">09467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a2ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -277.000000) translate(0,12)">0931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a2d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5004.000000 -224.000000) translate(0,12)">09367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a2f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 -129.000000) translate(0,12)">0921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a3180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5005.000000 -75.000000) translate(0,12)">09267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a3a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4074.000000 -231.000000) translate(0,12)">10kV无功补偿I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1755ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -576.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1702f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1702f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1702f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1702f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1702f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1702f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1702f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3129.000000 -1055.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1757040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3285.000000 -1163.500000) translate(0,16)">华翔变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17001b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4712.000000 -233.000000) translate(0,12)">10kV无功补偿II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16ea970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3758.000000 -122.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1655200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3759.000000 -270.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1655410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3761.000000 -416.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1655650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4962.000000 -129.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1655890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -277.000000) translate(0,12)">093</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1655ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -423.000000) translate(0,12)">094</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1655d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4396.000000 -527.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1655d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4396.000000 -527.000000) translate(0,27)">SZ11-10000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1655d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4396.000000 -527.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1655d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4396.000000 -527.000000) translate(0,57)">10000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1655d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4396.000000 -527.000000) translate(0,72)">Ud=7.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1655d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4396.000000 -527.000000) translate(0,87)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_165bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4395.000000 -1102.000000) translate(0,12)">38237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1663540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4360.000000 -1036.000000) translate(0,12)">3823</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d79550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4327.000000 -1161.000000) translate(0,15)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_165f7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4130.000000 -1202.000000) translate(0,15)">西华盛武线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cd6c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4456.000000 -1039.000000) translate(0,15)">华玉线</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_175f920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 800.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_175fc70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4213.000000 770.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_175feb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4188.000000 785.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1760ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 691.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1761070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4237.000000 661.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17612b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4212.000000 676.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1761860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.000000 419.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1709ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4242.000000 389.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_170a1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4217.000000 404.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_170a790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 35.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_170ab30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4342.000000 5.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_170ad30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4317.000000 20.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_170b2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4480.000000 35.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_170b670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 5.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_170b880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4469.000000 20.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_170bf20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4162.000000 514.000000) translate(0,12)">档位（档）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_170c9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4162.000000 499.000000) translate(0,12)">油温（℃）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_170d220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 484.000000) translate(0,12)">绕组温度（℃）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_166e9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4538.000000 811.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_166f020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.000000 781.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_166f260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4527.000000 796.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_166f590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 365.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16fe2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4482.000000 381.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16fe7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4474.000000 347.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16fed20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4482.000000 398.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16fefa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4482.000000 411.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16ff2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 784.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16ff540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 800.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16ff780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4635.000000 766.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16ff9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 817.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16ffc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 830.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16625a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4660.000000 197.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1662800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4635.000000 212.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1662b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 197.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1662d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3965.000000 212.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_17b0bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-314 4363,-311 4362,-312 4362,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5941@0" ObjectIDZND0="5951@1" Pin0InfoVect0LinkObjId="SW-37482_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c73b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-314 4363,-311 4362,-312 4362,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b0dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-260 4362,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5951@0" ObjectIDZND0="5953@1" Pin0InfoVect0LinkObjId="SW-37485_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37482_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-260 4362,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b0fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-207 4362,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5953@0" ObjectIDZND0="5952@1" Pin0InfoVect0LinkObjId="SW-37483_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37485_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-207 4362,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b11a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-122 4396,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5952@x" ObjectIDZND0="g_14cbef0@0" Pin0InfoVect0LinkObjId="g_14cbef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37483_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-122 4396,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b1390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-148 4362,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5952@0" ObjectIDZND0="g_14cbef0@0" Pin0InfoVect0LinkObjId="g_14cbef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37483_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-148 4362,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d79d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-122 4362,-62 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_14cbef0@0" ObjectIDND1="5952@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14cbef0_0" Pin1InfoVect1LinkObjId="SW-37483_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-122 4362,-62 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c6120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4509,-314 4510,-310 4509,-311 4509,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5941@0" ObjectIDZND0="5955@1" Pin0InfoVect0LinkObjId="SW-37488_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c73b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4509,-314 4510,-310 4509,-311 4509,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c6310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4509,-259 4509,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5955@0" ObjectIDZND0="5956@1" Pin0InfoVect0LinkObjId="SW-37490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37488_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4509,-259 4509,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c6500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4509,-206 4509,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5956@0" ObjectIDZND0="5954@1" Pin0InfoVect0LinkObjId="SW-41431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4509,-206 4509,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c66f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4509,-121 4543,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5954@x" ObjectIDZND0="g_17d8e10@0" Pin0InfoVect0LinkObjId="g_17d8e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4509,-121 4543,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c68e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4509,-147 4509,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5954@0" ObjectIDZND0="g_17d8e10@0" Pin0InfoVect0LinkObjId="g_17d8e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4509,-147 4509,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c6ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4509,-121 4509,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_17d8e10@0" ObjectIDND1="5954@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17d8e10_0" Pin1InfoVect1LinkObjId="SW-41431_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4509,-121 4509,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c73b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-294 4225,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5988@1" ObjectIDZND0="5941@0" Pin0InfoVect0LinkObjId="g_17a0240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37560_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-294 4225,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c7d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-242 4261,-242 4260,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_17c6cc0@0" ObjectIDND1="5988@x" ObjectIDZND0="g_17c75a0@0" Pin0InfoVect0LinkObjId="g_17c75a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17c6cc0_0" Pin1InfoVect1LinkObjId="SW-37560_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-242 4261,-242 4260,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17fd1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-242 4225,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_17c75a0@0" ObjectIDND1="g_17c6cc0@0" ObjectIDZND0="5988@0" Pin0InfoVect0LinkObjId="SW-37560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17c75a0_0" Pin1InfoVect1LinkObjId="g_17c6cc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-242 4225,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a0240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4167,-294 4167,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5960@1" ObjectIDZND0="5941@0" Pin0InfoVect0LinkObjId="g_17c73b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37495_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4167,-294 4167,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b70e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3664,-244 3691,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1772600@0" ObjectIDZND0="g_1709ac0@0" Pin0InfoVect0LinkObjId="g_1709ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1772600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3664,-244 3691,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b72d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3784,-244 3804,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10500@0" ObjectIDZND0="5978@0" Pin0InfoVect0LinkObjId="SW-37549_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3784,-244 3804,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_179ef00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3733,-244 3745,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_1709ac0@1" ObjectIDZND0="g_17db560@0" ObjectIDZND1="10500@x" ObjectIDZND2="5979@x" Pin0InfoVect0LinkObjId="g_17db560_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-37550_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1709ac0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3733,-244 3745,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_179f0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-244 3757,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_1709ac0@0" ObjectIDND1="g_17db560@0" ObjectIDND2="5979@x" ObjectIDZND0="10500@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1709ac0_0" Pin1InfoVect1LinkObjId="g_17db560_0" Pin1InfoVect2LinkObjId="SW-37550_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-244 3757,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17db180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-244 3746,-243 3746,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_1709ac0@0" ObjectIDND1="g_17db560@0" ObjectIDND2="10500@x" ObjectIDZND0="5979@1" Pin0InfoVect0LinkObjId="SW-37550_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1709ac0_0" Pin1InfoVect1LinkObjId="g_17db560_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-244 3746,-243 3746,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17db370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3746,-191 3746,-175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5979@0" ObjectIDZND0="g_1d62950@0" Pin0InfoVect0LinkObjId="g_1d62950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3746,-191 3746,-175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_181b5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3746,-243 3746,-277 3732,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1709ac0@0" ObjectIDND1="10500@x" ObjectIDND2="5979@x" ObjectIDZND0="g_17db560@0" Pin0InfoVect0LinkObjId="g_17db560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1709ac0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-37550_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3746,-243 3746,-277 3732,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_181d0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3785,-390 3805,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10501@0" ObjectIDZND0="5980@0" Pin0InfoVect0LinkObjId="SW-37551_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3785,-390 3805,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_181d2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-390 3879,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5980@1" ObjectIDZND0="5941@0" Pin0InfoVect0LinkObjId="g_17c73b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37551_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3841,-390 3879,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b18b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3746,-390 3758,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_17b1e80@0" ObjectIDND1="g_181c700@0" ObjectIDND2="5981@x" ObjectIDZND0="10501@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17b1e80_0" Pin1InfoVect1LinkObjId="g_181c700_0" Pin1InfoVect2LinkObjId="SW-37552_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3746,-390 3758,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b1aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3746,-390 3747,-389 3747,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_17b1e80@0" ObjectIDND1="g_181c700@0" ObjectIDND2="10501@x" ObjectIDZND0="5981@1" Pin0InfoVect0LinkObjId="SW-37552_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17b1e80_0" Pin1InfoVect1LinkObjId="g_181c700_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3746,-390 3747,-389 3747,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b1c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3747,-337 3747,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5981@0" ObjectIDZND0="g_1d61ec0@0" Pin0InfoVect0LinkObjId="g_1d61ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37552_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3747,-337 3747,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b2890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3747,-389 3747,-423 3733,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_181c700@0" ObjectIDND1="10501@x" ObjectIDND2="5981@x" ObjectIDZND0="g_17b1e80@0" Pin0InfoVect0LinkObjId="g_17b1e80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_181c700_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-37552_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3747,-389 3747,-423 3733,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1754390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3663,-96 3689,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_17b2a80@0" ObjectIDZND0="g_17538b0@0" Pin0InfoVect0LinkObjId="g_17538b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17b2a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3663,-96 3689,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1754580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3783,-96 3803,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10499@0" ObjectIDZND0="5976@0" Pin0InfoVect0LinkObjId="SW-37547_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3783,-96 3803,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1754770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3839,-96 3879,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5976@1" ObjectIDZND0="5941@0" Pin0InfoVect0LinkObjId="g_17c73b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37547_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3839,-96 3879,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1754960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-96 3744,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_17538b0@1" ObjectIDZND0="g_1771a30@0" ObjectIDZND1="10499@x" ObjectIDZND2="5977@x" Pin0InfoVect0LinkObjId="g_1771a30_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-37548_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17538b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-96 3744,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1754b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-96 3756,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_17538b0@0" ObjectIDND1="g_1771a30@0" ObjectIDND2="5977@x" ObjectIDZND0="10499@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17538b0_0" Pin1InfoVect1LinkObjId="g_1771a30_0" Pin1InfoVect2LinkObjId="SW-37548_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-96 3756,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1754d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-96 3745,-95 3745,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_17538b0@0" ObjectIDND1="g_1771a30@0" ObjectIDND2="10499@x" ObjectIDZND0="5977@1" Pin0InfoVect0LinkObjId="SW-37548_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17538b0_0" Pin1InfoVect1LinkObjId="g_1771a30_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-96 3745,-95 3745,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1754f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-43 3745,-27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5977@0" ObjectIDZND0="g_1d633e0@0" Pin0InfoVect0LinkObjId="g_1d633e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37548_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-43 3745,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1772280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3745,-95 3745,-129 3731,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_17538b0@0" ObjectIDND1="10499@x" ObjectIDND2="5977@x" ObjectIDZND0="g_1771a30@0" Pin0InfoVect0LinkObjId="g_1771a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17538b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-37548_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3745,-95 3745,-129 3731,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_176a360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3840,-244 3976,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5978@1" ObjectIDZND0="g_1801b90@0" ObjectIDZND1="5958@x" Pin0InfoVect0LinkObjId="g_1801b90_0" Pin0InfoVect1LinkObjId="SW-37492_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37549_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3840,-244 3976,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_176a550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-244 4045,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5958@1" ObjectIDZND0="5959@1" Pin0InfoVect0LinkObjId="SW-37493_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37492_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-244 4045,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18019a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4072,-244 4089,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5959@0" ObjectIDZND0="5957@0" Pin0InfoVect0LinkObjId="SW-37491_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37493_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4072,-244 4089,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1803a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4575,-295 4575,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5964@1" ObjectIDZND0="5941@0" Pin0InfoVect0LinkObjId="g_17c73b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4575,-295 4575,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b8b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3993,-244 3976,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="5958@0" ObjectIDZND0="g_1801b90@0" ObjectIDZND1="5978@x" Pin0InfoVect0LinkObjId="g_1801b90_0" Pin0InfoVect1LinkObjId="SW-37549_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37492_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3993,-244 3976,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17b8d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-244 3976,-276 3963,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5978@x" ObjectIDND1="5958@x" ObjectIDZND0="g_1801b90@0" Pin0InfoVect0LinkObjId="g_1801b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37549_0" Pin1InfoVect1LinkObjId="SW-37492_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-244 3976,-276 3963,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16f5120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5079,-251 5055,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_174d300@0" ObjectIDZND0="g_1711720@0" Pin0InfoVect0LinkObjId="g_1711720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_174d300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5079,-251 5055,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16f5310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5010,-251 4998,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_1711720@1" ObjectIDZND0="g_16f56f0@0" ObjectIDZND1="10503@x" ObjectIDZND2="5985@x" Pin0InfoVect0LinkObjId="g_16f56f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-37556_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1711720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5010,-251 4998,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16f5500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4998,-251 4986,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_1711720@0" ObjectIDND1="g_16f56f0@0" ObjectIDND2="5985@x" ObjectIDZND0="10503@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1711720_0" Pin1InfoVect1LinkObjId="g_16f56f0_0" Pin1InfoVect2LinkObjId="SW-37556_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4998,-251 4986,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d9c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-250 4997,-284 5011,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1711720@0" ObjectIDND1="10503@x" ObjectIDND2="5985@x" ObjectIDZND0="g_16f56f0@0" Pin0InfoVect0LinkObjId="g_16f56f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1711720_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-37556_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-250 4997,-284 5011,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16eb0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-397 5052,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_17d9e70@0" ObjectIDZND0="g_17b9f70@0" Pin0InfoVect0LinkObjId="g_17b9f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17d9e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-397 5052,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16eb2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-397 4997,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_17b9f70@1" ObjectIDZND0="g_16eb690@0" ObjectIDZND1="10504@x" ObjectIDZND2="5987@x" Pin0InfoVect0LinkObjId="g_16eb690_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-37558_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17b9f70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-397 4997,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16eb4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-397 4985,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_17b9f70@0" ObjectIDND1="g_16eb690@0" ObjectIDND2="5987@x" ObjectIDZND0="10504@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17b9f70_0" Pin1InfoVect1LinkObjId="g_16eb690_0" Pin1InfoVect2LinkObjId="SW-37558_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-397 4985,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16ec0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-396 4996,-430 5010,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_17b9f70@0" ObjectIDND1="10504@x" ObjectIDND2="5987@x" ObjectIDZND0="g_16eb690@0" Pin0InfoVect0LinkObjId="g_16eb690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17b9f70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-37558_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-396 4996,-430 5010,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17bf890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5080,-103 5055,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_16ec290@0" ObjectIDZND0="g_17beed0@0" Pin0InfoVect0LinkObjId="g_17beed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16ec290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5080,-103 5055,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17bfa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5011,-103 4999,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_17beed0@1" ObjectIDZND0="g_17bfe60@0" ObjectIDZND1="10502@x" ObjectIDZND2="5983@x" Pin0InfoVect0LinkObjId="g_17bfe60_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-37554_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17beed0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5011,-103 4999,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17bfc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4999,-103 4987,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_17beed0@0" ObjectIDND1="g_17bfe60@0" ObjectIDND2="5983@x" ObjectIDZND0="10502@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17beed0_0" Pin1InfoVect1LinkObjId="g_17bfe60_0" Pin1InfoVect2LinkObjId="SW-37554_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4999,-103 4987,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c0870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4998,-102 4998,-136 5012,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_17beed0@0" ObjectIDND1="10502@x" ObjectIDND2="5983@x" ObjectIDZND0="g_17bfe60@0" Pin0InfoVect0LinkObjId="g_17bfe60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17beed0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-37554_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4998,-102 4998,-136 5012,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18234e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-314 4364,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5941@0" ObjectIDZND0="5945@0" Pin0InfoVect0LinkObjId="SW-37449_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c73b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-314 4364,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1824c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-366 4364,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5945@1" ObjectIDZND0="5944@0" Pin0InfoVect0LinkObjId="SW-37448_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37449_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-366 4364,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1824e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-410 4364,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="5944@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37448_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-410 4364,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1825010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4365,-514 4365,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_12a8a10@1" Pin0InfoVect0LinkObjId="g_12a8a10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4365,-514 4365,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17c2d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4338,-595 4339,-594 4365,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1825200@0" ObjectIDZND0="g_12a8a10@0" ObjectIDZND1="5967@x" ObjectIDZND2="5942@x" Pin0InfoVect0LinkObjId="g_12a8a10_0" Pin0InfoVect1LinkObjId="SW-37539_0" Pin0InfoVect2LinkObjId="SW-37442_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1825200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4338,-595 4339,-594 4365,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17c3820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4365,-579 4365,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_12a8a10@0" ObjectIDZND0="g_1825200@0" ObjectIDZND1="5967@x" ObjectIDZND2="5942@x" Pin0InfoVect0LinkObjId="g_1825200_0" Pin0InfoVect1LinkObjId="SW-37539_0" Pin0InfoVect2LinkObjId="SW-37442_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12a8a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4365,-579 4365,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17c3a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4365,-594 4366,-594 4407,-594 4404,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1825200@0" ObjectIDND1="g_12a8a10@0" ObjectIDND2="5967@x" ObjectIDZND0="5942@0" Pin0InfoVect0LinkObjId="SW-37442_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1825200_0" Pin1InfoVect1LinkObjId="g_12a8a10_0" Pin1InfoVect2LinkObjId="SW-37539_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4365,-594 4366,-594 4407,-594 4404,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_176f610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4365,-594 4365,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1825200@0" ObjectIDND1="g_12a8a10@0" ObjectIDND2="5942@x" ObjectIDZND0="5967@1" Pin0InfoVect0LinkObjId="SW-37539_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1825200_0" Pin1InfoVect1LinkObjId="g_12a8a10_0" Pin1InfoVect2LinkObjId="SW-37442_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4365,-594 4365,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_176f830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4365,-626 4365,-641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5967@0" ObjectIDZND0="5943@0" Pin0InfoVect0LinkObjId="SW-37444_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37539_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4365,-626 4365,-641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_176fa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4365,-674 4365,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5943@1" ObjectIDZND0="5966@1" Pin0InfoVect0LinkObjId="SW-37539_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37444_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4365,-674 4365,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17bdb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-610 4592,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_17bd2b0@1" ObjectIDZND0="g_146e3a0@0" Pin0InfoVect0LinkObjId="g_146e3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17bd2b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-610 4592,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17bdd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-660 4627,-660 4627,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_17bd2b0@0" ObjectIDZND0="g_176fe30@0" Pin0InfoVect0LinkObjId="g_176fe30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_17bd2b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-660 4627,-660 4627,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18202f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-690 4592,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_176fe30@0" ObjectIDZND1="g_17bd2b0@0" Pin0InfoVect0LinkObjId="g_176fe30_0" Pin0InfoVect1LinkObjId="g_17bd2b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-690 4592,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1820510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-660 4592,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_176fe30@0" ObjectIDND1="0@x" ObjectIDZND0="g_17bd2b0@0" Pin0InfoVect0LinkObjId="g_17bd2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_176fe30_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-660 4592,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1731d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-1103 4172,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="5974@1" ObjectIDZND0="5975@x" ObjectIDZND1="g_1730b50@0" Pin0InfoVect0LinkObjId="SW-37545_0" Pin0InfoVect1LinkObjId="g_1730b50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37544_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-1103 4172,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1731f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-1054 4172,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1481130@0" ObjectIDZND0="5974@0" Pin0InfoVect0LinkObjId="SW-37544_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1481130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-1054 4172,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17eea50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-876 4208,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_17eecb0@0" Pin0InfoVect0LinkObjId="g_17eecb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-876 4208,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17ef2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4141,-944 4172,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_17ef510@0" ObjectIDZND0="5968@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1481130@0" Pin0InfoVect0LinkObjId="SW-37540_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1481130_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17ef510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4141,-944 4172,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_176bc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-876 4047,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_176b0c0@0" Pin0InfoVect0LinkObjId="g_176b0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-876 4047,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_176beb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-809 4047,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_176b0c0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_176b0c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-809 4047,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1715380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-739 4353,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5940@0" ObjectIDZND0="5971@1" Pin0InfoVect0LinkObjId="SW-37541_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_171cb20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-739 4353,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17155e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-787 4353,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5971@0" ObjectIDZND0="5947@0" Pin0InfoVect0LinkObjId="SW-37473_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37541_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-787 4353,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1715840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-836 4353,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5947@1" ObjectIDZND0="5970@1" Pin0InfoVect0LinkObjId="SW-37541_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37473_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-836 4353,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1716710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4320,-927 4353,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1715aa0@0" ObjectIDZND0="5970@x" ObjectIDZND1="g_16cc650@0" ObjectIDZND2="5948@x" Pin0InfoVect0LinkObjId="SW-37541_0" Pin0InfoVect1LinkObjId="g_16cc650_0" Pin0InfoVect2LinkObjId="SW-37475_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1715aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4320,-927 4353,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16cc3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-868 4353,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="5970@0" ObjectIDZND0="g_1715aa0@0" ObjectIDZND1="g_16cc650@0" ObjectIDZND2="5948@x" Pin0InfoVect0LinkObjId="g_1715aa0_0" Pin0InfoVect1LinkObjId="g_16cc650_0" Pin0InfoVect2LinkObjId="SW-37475_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37541_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-868 4353,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16cd0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-927 4353,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1715aa0@0" ObjectIDND1="5970@x" ObjectIDND2="5948@x" ObjectIDZND0="g_16cc650@1" Pin0InfoVect0LinkObjId="g_16cc650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1715aa0_0" Pin1InfoVect1LinkObjId="SW-37541_0" Pin1InfoVect2LinkObjId="SW-37475_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-927 4353,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16cd320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-997 4353,-1011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_16cc650@0" ObjectIDZND0="11286@0" Pin0InfoVect0LinkObjId="SW-58725_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16cc650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-997 4353,-1011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17de3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-739 4480,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="5940@0" ObjectIDZND0="5973@1" Pin0InfoVect0LinkObjId="SW-37542_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_171cb20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-739 4480,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17de600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-785 4480,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5973@0" ObjectIDZND0="5949@0" Pin0InfoVect0LinkObjId="SW-37478_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37542_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-785 4480,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17de860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-832 4480,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5949@1" ObjectIDZND0="5972@1" Pin0InfoVect0LinkObjId="SW-37542_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37478_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-832 4480,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17deac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-895 4449,-896 4480,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_17509a0@0" ObjectIDZND0="5972@x" ObjectIDZND1="g_17df980@0" ObjectIDZND2="5950@x" Pin0InfoVect0LinkObjId="SW-37542_0" Pin0InfoVect1LinkObjId="g_17df980_0" Pin0InfoVect2LinkObjId="SW-37480_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17509a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-895 4449,-896 4480,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17df720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-864 4480,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="5972@0" ObjectIDZND0="g_17509a0@0" ObjectIDZND1="g_17df980@0" ObjectIDZND2="5950@x" Pin0InfoVect0LinkObjId="g_17509a0_0" Pin0InfoVect1LinkObjId="g_17df980_0" Pin0InfoVect2LinkObjId="SW-37480_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37542_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-864 4480,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e05b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-896 4480,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_17509a0@0" ObjectIDND1="5972@x" ObjectIDND2="5950@x" ObjectIDZND0="g_17df980@1" Pin0InfoVect0LinkObjId="g_17df980_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17509a0_0" Pin1InfoVect1LinkObjId="SW-37542_0" Pin1InfoVect2LinkObjId="SW-37480_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-896 4480,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17e0810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-966 4480,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_17df980@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17df980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-966 4480,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_171cb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4365,-708 4365,-740 4366,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5966@0" ObjectIDZND0="5940@0" Pin0InfoVect0LinkObjId="g_171cd10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37539_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4365,-708 4365,-740 4366,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_171cd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-706 4592,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="5940@0" Pin0InfoVect0LinkObjId="g_171cb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-706 4592,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_171f3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-958 4208,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_171ec20@1" Pin0InfoVect0LinkObjId="g_171ec20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-958 4208,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_171f630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-907 4208,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_171ec20@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_171ec20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-907 4208,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1720040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-958 4047,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_171f890@1" Pin0InfoVect0LinkObjId="g_171f890_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-958 4047,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17202a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-914 4047,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_171f890@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_171f890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-914 4047,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1720500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-389 3689,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_181b7c0@0" ObjectIDZND0="g_181c700@0" Pin0InfoVect0LinkObjId="g_181c700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_181b7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-389 3689,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a05e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-390 3746,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_181c700@1" ObjectIDZND0="g_17b1e80@0" ObjectIDZND1="10501@x" ObjectIDZND2="5981@x" Pin0InfoVect0LinkObjId="g_17b1e80_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-37552_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_181c700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-390 3746,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a0840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4714,-249 4732,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5963@0" ObjectIDZND0="5962@0" Pin0InfoVect0LinkObjId="SW-37497_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37498_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4714,-249 4732,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a0aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4958,-397 4928,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10504@1" ObjectIDZND0="5986@1" Pin0InfoVect0LinkObjId="SW-37557_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4958,-397 4928,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a0d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4892,-397 4863,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5986@0" ObjectIDZND0="5941@0" Pin0InfoVect0LinkObjId="g_17c73b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37557_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4892,-397 4863,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a0f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4959,-251 4925,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10503@1" ObjectIDZND0="5984@1" Pin0InfoVect0LinkObjId="SW-37555_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4959,-251 4925,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a11c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4889,-251 4863,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5984@0" ObjectIDZND0="5941@0" Pin0InfoVect0LinkObjId="g_17c73b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37555_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4889,-251 4863,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a1420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-103 4932,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10502@1" ObjectIDZND0="5982@1" Pin0InfoVect0LinkObjId="SW-37553_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-103 4932,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a1680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4896,-103 4863,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5982@0" ObjectIDZND0="5941@0" Pin0InfoVect0LinkObjId="g_17c73b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37553_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4896,-103 4863,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a18e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-328 4996,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d63e70@0" ObjectIDZND0="5987@0" Pin0InfoVect0LinkObjId="SW-37558_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d63e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-328 4996,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a1b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-380 4996,-396 4997,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="5987@1" ObjectIDZND0="g_17b9f70@0" ObjectIDZND1="g_16eb690@0" ObjectIDZND2="10504@x" Pin0InfoVect0LinkObjId="g_17b9f70_0" Pin0InfoVect1LinkObjId="g_16eb690_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37558_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-380 4996,-396 4997,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a1da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4998,-34 4998,-50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d65390@0" ObjectIDZND0="5983@0" Pin0InfoVect0LinkObjId="SW-37554_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d65390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4998,-34 4998,-50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a2000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4998,-86 4998,-102 4999,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="5983@1" ObjectIDZND0="g_17beed0@0" ObjectIDZND1="g_17bfe60@0" ObjectIDZND2="10502@x" Pin0InfoVect0LinkObjId="g_17beed0_0" Pin0InfoVect1LinkObjId="g_17bfe60_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37554_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4998,-86 4998,-102 4999,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a2260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-182 4997,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1d64900@0" ObjectIDZND0="5985@0" Pin0InfoVect0LinkObjId="SW-37556_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d64900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-182 4997,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17a24c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-235 4997,-250 4998,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="5985@1" ObjectIDZND0="g_1711720@0" ObjectIDZND1="g_16f56f0@0" ObjectIDZND2="10503@x" Pin0InfoVect0LinkObjId="g_1711720_0" Pin0InfoVect1LinkObjId="g_16f56f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37556_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-235 4997,-250 4998,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17a3690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-766 4172,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="5969@1" ObjectIDZND0="5940@0" Pin0InfoVect0LinkObjId="g_171cb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37540_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-766 4172,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17a3880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-944 4172,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_17ef510@0" ObjectIDND1="0@x" ObjectIDND2="g_1481130@0" ObjectIDZND0="5968@0" Pin0InfoVect0LinkObjId="SW-37540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_17ef510_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1481130_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-944 4172,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_178d9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-242 4225,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_17c75a0@0" ObjectIDND1="5988@x" ObjectIDZND0="g_17c6cc0@0" Pin0InfoVect0LinkObjId="g_17c6cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17c75a0_0" Pin1InfoVect1LinkObjId="SW-37560_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-242 4225,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_178dbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-182 4225,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_17c6cc0@1" ObjectIDZND0="g_17fd3c0@0" Pin0InfoVect0LinkObjId="g_17fd3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17c6cc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-182 4225,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1755240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4509,-837 4509,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1993a10@0" ObjectIDZND0="5950@0" Pin0InfoVect0LinkObjId="SW-37480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1993a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4509,-837 4509,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1755430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4509,-885 4509,-895 4479,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="5950@1" ObjectIDZND0="g_17509a0@0" ObjectIDZND1="5972@x" ObjectIDZND2="g_17df980@0" Pin0InfoVect0LinkObjId="g_17509a0_0" Pin0InfoVect1LinkObjId="SW-37542_0" Pin0InfoVect2LinkObjId="g_17df980_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4509,-885 4509,-895 4479,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1755620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4386,-865 4386,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1992f80@0" ObjectIDZND0="5948@0" Pin0InfoVect0LinkObjId="SW-37475_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1992f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4386,-865 4386,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1755830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4386,-918 4386,-927 4353,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="5948@1" ObjectIDZND0="g_1715aa0@0" ObjectIDZND1="5970@x" ObjectIDZND2="g_16cc650@0" Pin0InfoVect0LinkObjId="g_1715aa0_0" Pin0InfoVect1LinkObjId="SW-37541_0" Pin0InfoVect2LinkObjId="g_16cc650_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37475_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4386,-918 4386,-927 4353,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1755a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-849 4172,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5968@1" ObjectIDZND0="5946@1" Pin0InfoVect0LinkObjId="SW-37470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37540_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-849 4172,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1755c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-798 4172,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="5946@0" ObjectIDZND0="5969@0" Pin0InfoVect0LinkObjId="SW-37540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-798 4172,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_165d2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4125,-244 4137,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5957@1" ObjectIDZND0="g_165c3e0@0" Pin0InfoVect0LinkObjId="g_165c3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37491_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4125,-244 4137,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_165d520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-244 4167,-244 4167,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_165c3e0@1" ObjectIDZND0="5960@0" Pin0InfoVect0LinkObjId="SW-37495_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_165c3e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-244 4167,-244 4167,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_165e980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4629,-251 4610,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="5961@0" ObjectIDZND0="g_165d780@1" Pin0InfoVect0LinkObjId="g_165d780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37496_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4629,-251 4610,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_165ebe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4588,-251 4575,-251 4575,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_165d780@0" ObjectIDZND0="5964@0" Pin0InfoVect0LinkObjId="SW-37500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_165d780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4588,-251 4575,-251 4575,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_165ee40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4795,-287 4795,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_174ea60@0" ObjectIDZND0="5941@0" ObjectIDZND1="5962@x" Pin0InfoVect0LinkObjId="g_17c73b0_0" Pin0InfoVect1LinkObjId="SW-37497_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_174ea60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4795,-287 4795,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_165f930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-249 4795,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="5962@1" ObjectIDZND0="g_174ea60@0" ObjectIDZND1="5941@0" Pin0InfoVect0LinkObjId="g_174ea60_0" Pin0InfoVect1LinkObjId="g_17c73b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37497_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-249 4795,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_165fb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4795,-249 4863,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_174ea60@0" ObjectIDND1="5962@x" ObjectIDZND0="5941@0" Pin0InfoVect0LinkObjId="g_17c73b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_174ea60_0" Pin1InfoVect1LinkObjId="SW-37497_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4795,-249 4863,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16603c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4666,-251 4686,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="5961@1" ObjectIDZND0="5963@1" Pin0InfoVect0LinkObjId="SW-37498_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37496_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4666,-251 4686,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1663780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-975 4172,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_17ef510@0" ObjectIDZND1="5968@x" ObjectIDZND2="g_1481130@0" Pin0InfoVect0LinkObjId="g_17ef510_0" Pin0InfoVect1LinkObjId="SW-37540_0" Pin0InfoVect2LinkObjId="g_1481130_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-975 4172,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16642e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-944 4172,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_17ef510@0" ObjectIDND1="5968@x" ObjectIDZND0="0@x" ObjectIDZND1="g_1481130@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1481130_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17ef510_0" Pin1InfoVect1LinkObjId="SW-37540_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-944 4172,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16644d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-975 4172,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_17ef510@0" ObjectIDND2="5968@x" ObjectIDZND0="g_1481130@1" Pin0InfoVect0LinkObjId="g_1481130_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_17ef510_0" Pin1InfoVect2LinkObjId="SW-37540_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-975 4172,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16646e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-975 4172,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_17ef510@0" ObjectIDZND2="5968@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_17ef510_0" Pin0InfoVect2LinkObjId="SW-37540_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-975 4172,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16666a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4440,-594 4465,-594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5942@1" ObjectIDZND0="g_1666900@0" Pin0InfoVect0LinkObjId="g_1666900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37442_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4440,-594 4465,-594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16af4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-1120 4173,-1120 4204,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="5974@x" ObjectIDND1="g_1730b50@0" ObjectIDZND0="5975@0" Pin0InfoVect0LinkObjId="SW-37545_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37544_0" Pin1InfoVect1LinkObjId="g_1730b50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-1120 4173,-1120 4204,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d68620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4240,-1120 4270,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="5975@1" ObjectIDZND0="g_1d68880@0" Pin0InfoVect0LinkObjId="g_1d68880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-37545_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4240,-1120 4270,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6c3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-1120 4149,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="5974@x" ObjectIDND1="5975@x" ObjectIDZND0="g_1730b50@0" Pin0InfoVect0LinkObjId="g_1730b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-37544_0" Pin1InfoVect1LinkObjId="SW-37545_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-1120 4149,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d7b250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-1074 4354,-1074 4385,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1df02f0@0" ObjectIDND1="11286@x" ObjectIDZND0="11287@0" Pin0InfoVect0LinkObjId="SW-58724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1df02f0_0" Pin1InfoVect1LinkObjId="SW-58725_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-1074 4354,-1074 4385,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ddc900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-1074 4451,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11287@1" ObjectIDZND0="g_1de0fe0@0" Pin0InfoVect0LinkObjId="g_1de0fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-1074 4451,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1de3c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-1074 4330,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11287@x" ObjectIDND1="11286@x" ObjectIDZND0="g_1df02f0@0" Pin0InfoVect0LinkObjId="g_1df02f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58724_0" Pin1InfoVect1LinkObjId="SW-58725_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-1074 4330,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cbf240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-1047 4353,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="11286@1" ObjectIDZND0="11287@x" ObjectIDZND1="g_1df02f0@0" Pin0InfoVect0LinkObjId="SW-58724_0" Pin0InfoVect1LinkObjId="g_1df02f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58725_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-1047 4353,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f457b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-1120 4172,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="5974@x" ObjectIDND1="5975@x" ObjectIDND2="g_1730b50@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-37544_0" Pin1InfoVect1LinkObjId="SW-37545_0" Pin1InfoVect2LinkObjId="g_1730b50_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-1120 4172,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f4d230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-1074 4353,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="11287@x" ObjectIDND1="g_1df02f0@0" ObjectIDND2="11286@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-58724_0" Pin1InfoVect1LinkObjId="g_1df02f0_0" Pin1InfoVect2LinkObjId="SW-58725_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-1074 4353,-1103 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_HX.CX_HX_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4145,-314 4594,-314 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5941" ObjectName="BS-CX_HX.CX_HX_9IM"/>
    <cge:TPSR_Ref TObjectID="5941"/></metadata>
   <polyline fill="none" opacity="0" points="4145,-314 4594,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HX.CX_HX_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3880,-75 3880,-412 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5941" ObjectName="BS-CX_HX.CX_HX_9IM"/>
    <cge:TPSR_Ref TObjectID="5941"/></metadata>
   <polyline fill="none" opacity="0" points="3880,-75 3880,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HX.CX_HX_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4864,-78 4864,-418 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5941" ObjectName="BS-CX_HX.CX_HX_9IM"/>
    <cge:TPSR_Ref TObjectID="5941"/></metadata>
   <polyline fill="none" opacity="0" points="4864,-78 4864,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HX.CX_HX_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-739 4720,-739 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="5940" ObjectName="BS-CX_HX.CX_HX_3IM"/>
    <cge:TPSR_Ref TObjectID="5940"/></metadata>
   <polyline fill="none" opacity="0" points="4097,-739 4720,-739 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37307" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3415.000000 -1083.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5889" ObjectName="DYN-CX_HX"/>
     <cge:Meas_Ref ObjectId="37307"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_HX"/>
</svg>