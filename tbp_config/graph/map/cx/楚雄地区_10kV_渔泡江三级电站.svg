<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-54" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3025 -1240 2245 1242">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape116">
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="14" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape115">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0319731" x1="9" x2="13" y1="31" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0178798" x1="13" x2="13" y1="35" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0319731" x1="18" x2="13" y1="31" y2="35"/>
    <ellipse cx="13" cy="34" rx="12.5" ry="11.5" stroke-width="0.118558"/>
    <circle cx="13" cy="17" r="12" stroke-width="0.120929"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="18" x2="13" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="9" x2="13" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0182374" x1="13" x2="13" y1="17" y2="21"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="28" stroke-width="1" width="14" x="0" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="51" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="52" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="4" y2="39"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape99">
    <polyline arcFlag="1" points="26,30 26,31 26,32 26,33 25,34 25,35 24,36 24,36 23,37 22,37 21,38 20,38 19,38 18,39 17,38 16,38 15,38 14,37 14,37 13,36 12,36 12,35 11,34 11,33 11,32 10,31 11,30 " stroke-width="0.06"/>
    <polyline points="41,30 42,29 41,29 41,28 41,27 40,26 40,25 39,24 38,24 38,23 37,23 36,22 35,22 34,22 33,22 32,22 31,23 30,23 29,24 28,24 28,25 27,26 27,27 26,28 26,29 26,29 26,30 " stroke-width="0.06"/>
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.5"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape6">
    <circle cx="18" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="18" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2604770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2605910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26063d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2607290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2608560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2609080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2609c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_260a730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_260b730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_260b730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_260cf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_260cf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_260e950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_260e950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_260f8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26114a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2612130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2612e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2613540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2614dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2615a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26162e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2616aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2617b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2618500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2618ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_26199b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_261afd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_261b9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_261cb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_261d820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_262bc30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2623d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2625500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_261fa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1252" width="2255" x="3020" y="-1245"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3063" x2="3063" y1="-1202" y2="-2"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3065" x2="3105" y1="-1201" y2="-1201"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3060" x2="3108" y1="-1078" y2="-1078"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3065" x2="3090" y1="-594" y2="-594"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3120" x2="5267" y1="-1212" y2="-1212"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3118" x2="3118" y1="-1240" y2="-1213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3478" x2="3478" y1="-1237" y2="-1213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5268" x2="5268" y1="-1236" y2="-1210"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-46764">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 -835.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8290" ObjectName="SW-CX_YPJ.CX_YPJ_181BK"/>
     <cge:Meas_Ref ObjectId="46764"/>
    <cge:TPSR_Ref TObjectID="8290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46774">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -394.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8300" ObjectName="SW-CX_YPJ.CX_YPJ_081BK"/>
     <cge:Meas_Ref ObjectId="46774"/>
    <cge:TPSR_Ref TObjectID="8300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 -524.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8296" ObjectName="SW-CX_YPJ.CX_YPJ_001BK"/>
     <cge:Meas_Ref ObjectId="46770"/>
    <cge:TPSR_Ref TObjectID="8296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4615.000000 -393.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8298" ObjectName="SW-CX_YPJ.CX_YPJ_083BK"/>
     <cge:Meas_Ref ObjectId="46772"/>
    <cge:TPSR_Ref TObjectID="8298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46778">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4665.000000 -528.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8304" ObjectName="SW-CX_YPJ.CX_YPJ_084BK"/>
     <cge:Meas_Ref ObjectId="46778"/>
    <cge:TPSR_Ref TObjectID="8304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46776">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -395.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8302" ObjectName="SW-CX_YPJ.CX_YPJ_082BK"/>
     <cge:Meas_Ref ObjectId="46776"/>
    <cge:TPSR_Ref TObjectID="8302"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_23900a0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4363.500000 -604.500000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YPJ" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuda_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4067,-1049 4067,-1079 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11716" ObjectName="AC-110kV.yuda_line"/>
    <cge:TPSR_Ref TObjectID="11716_SS-54"/></metadata>
   <polyline fill="none" opacity="0" points="4067,-1049 4067,-1079 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_260d290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -599.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_260a370" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3993.000000 -970.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2605200" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4547.043860 -363.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26193e0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3991.500000 -778.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2617fa0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3991.500000 -893.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2303080" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3940.500000 -144.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2540430" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 -603.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2698480" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4342.500000 -146.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26a55b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 -667.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_24580f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-338 3921,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="8301@x" ObjectIDND2="g_2653bc0@0" ObjectIDZND0="g_2634fb0@0" Pin0InfoVect0LinkObjId="g_2634fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23900a0_0" Pin1InfoVect1LinkObjId="SW-46775_0" Pin1InfoVect2LinkObjId="g_2653bc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-338 3921,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_240bb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-960 4049,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8292@x" ObjectIDND1="g_2637b40@0" ObjectIDND2="g_2638810@0" ObjectIDZND0="8295@1" Pin0InfoVect0LinkObjId="SW-46769_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46766_0" Pin1InfoVect1LinkObjId="g_2637b40_0" Pin1InfoVect2LinkObjId="g_2638810_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-960 4049,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2340e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-960 3988,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8295@0" ObjectIDZND0="g_260a370@0" Pin0InfoVect0LinkObjId="g_260a370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46769_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-960 3988,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24322b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4148,-609 4126,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_260d290@0" ObjectIDZND0="8297@1" Pin0InfoVect0LinkObjId="SW-46771_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_260d290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4148,-609 4126,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_240e530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-609 4067,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="8297@0" ObjectIDZND0="8306@x" ObjectIDZND1="g_26a6200@0" ObjectIDZND2="8317@x" Pin0InfoVect0LinkObjId="SW-46788_0" Pin0InfoVect1LinkObjId="g_26a6200_0" Pin0InfoVect2LinkObjId="g_238f660_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46771_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-609 4067,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23c5750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-353 4598,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8313@x" ObjectIDND1="g_26335d0@0" ObjectIDND2="g_23424e0@0" ObjectIDZND0="8299@1" Pin0InfoVect0LinkObjId="SW-46773_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46791_0" Pin1InfoVect1LinkObjId="g_26335d0_0" Pin1InfoVect2LinkObjId="g_23424e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-353 4598,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d9f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-353 4542,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8299@0" ObjectIDZND0="g_2605200@0" Pin0InfoVect0LinkObjId="g_2605200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-353 4542,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d7200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-532 4067,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8296@0" ObjectIDZND0="8307@0" Pin0InfoVect0LinkObjId="SW-46788_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-532 4067,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245a6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-574 4067,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8306@1" ObjectIDZND0="8296@1" Pin0InfoVect0LinkObjId="SW-46770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46788_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-574 4067,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_261c6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-609 4067,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="8297@x" ObjectIDND1="g_26a6200@0" ObjectIDND2="8317@x" ObjectIDZND0="8306@0" Pin0InfoVect0LinkObjId="SW-46788_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46771_0" Pin1InfoVect1LinkObjId="g_26a6200_0" Pin1InfoVect2LinkObjId="g_238f660_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-609 4067,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23a8340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-609 4037,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="8297@x" ObjectIDND1="8306@x" ObjectIDND2="8317@x" ObjectIDZND0="g_26a6200@0" Pin0InfoVect0LinkObjId="g_26a6200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46771_0" Pin1InfoVect1LinkObjId="SW-46788_0" Pin1InfoVect2LinkObjId="g_238f660_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-609 4037,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_238f660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-609 4067,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer2" ObjectIDND0="8297@x" ObjectIDND1="8306@x" ObjectIDND2="g_26a6200@0" ObjectIDZND0="8317@0" Pin0InfoVect0LinkObjId="g_2603210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46771_0" Pin1InfoVect1LinkObjId="SW-46788_0" Pin1InfoVect2LinkObjId="g_26a6200_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-609 4067,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_23ecd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-768 4008,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_26193e0@0" ObjectIDZND0="8293@0" Pin0InfoVect0LinkObjId="SW-46767_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26193e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-768 4008,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2603210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4044,-768 4067,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8293@1" ObjectIDZND0="8317@x" ObjectIDZND1="8291@x" ObjectIDZND2="g_2635ca0@0" Pin0InfoVect0LinkObjId="g_238f660_0" Pin0InfoVect1LinkObjId="SW-46765_0" Pin0InfoVect2LinkObjId="g_2635ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46767_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4044,-768 4067,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24aea70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-736 4067,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8317@1" ObjectIDZND0="8293@x" ObjectIDZND1="8291@x" ObjectIDZND2="g_2635ca0@0" Pin0InfoVect0LinkObjId="SW-46767_0" Pin0InfoVect1LinkObjId="SW-46765_0" Pin0InfoVect2LinkObjId="g_2635ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_238f660_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-736 4067,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2342230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-768 4067,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8293@x" ObjectIDND1="8317@x" ObjectIDND2="g_2635ca0@0" ObjectIDZND0="8291@0" Pin0InfoVect0LinkObjId="SW-46765_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46767_0" Pin1InfoVect1LinkObjId="g_238f660_0" Pin1InfoVect2LinkObjId="g_2635ca0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-768 4067,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24e39c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-883 4008,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2617fa0@0" ObjectIDZND0="8294@0" Pin0InfoVect0LinkObjId="SW-46768_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2617fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-883 4008,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_23d5660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4044,-883 4067,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="8294@1" ObjectIDZND0="8290@x" ObjectIDZND1="8292@x" Pin0InfoVect0LinkObjId="SW-46764_0" Pin0InfoVect1LinkObjId="SW-46766_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46768_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4044,-883 4067,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_244d490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-883 4067,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8294@x" ObjectIDND1="8292@x" ObjectIDZND0="8290@1" Pin0InfoVect0LinkObjId="SW-46764_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46768_0" Pin1InfoVect1LinkObjId="SW-46766_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-883 4067,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_261b3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-843 4067,-824 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8290@0" ObjectIDZND0="8291@1" Pin0InfoVect0LinkObjId="SW-46765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46764_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-843 4067,-824 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2465760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-900 4067,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8292@0" ObjectIDZND0="8294@x" ObjectIDZND1="8290@x" Pin0InfoVect0LinkObjId="SW-46768_0" Pin0InfoVect1LinkObjId="SW-46764_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46766_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-900 4067,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2400310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-936 4067,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="8292@1" ObjectIDZND0="8295@x" ObjectIDZND1="g_2637b40@0" ObjectIDZND2="g_2638810@0" Pin0InfoVect0LinkObjId="SW-46769_0" Pin0InfoVect1LinkObjId="g_2637b40_0" Pin0InfoVect2LinkObjId="g_2638810_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46766_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-936 4067,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ff0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-461 3951,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8308@0" ObjectIDZND0="8289@0" Pin0InfoVect0LinkObjId="g_261a640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46789_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-461 3951,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ff6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-429 3951,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8300@1" ObjectIDZND0="8308@1" Pin0InfoVect0LinkObjId="SW-46789_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46774_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-429 3951,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d6650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-301 3951,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="8301@x" ObjectIDND2="g_2653bc0@0" ObjectIDZND0="g_2634fb0@0" ObjectIDZND1="8309@x" Pin0InfoVect0LinkObjId="g_2634fb0_0" Pin0InfoVect1LinkObjId="SW-46789_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23900a0_0" Pin1InfoVect1LinkObjId="SW-46775_0" Pin1InfoVect2LinkObjId="g_2653bc0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-301 3951,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23c1520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-402 3951,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8300@0" ObjectIDZND0="8309@0" Pin0InfoVect0LinkObjId="SW-46789_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-402 3951,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a5f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-366 3951,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="8309@1" ObjectIDZND0="g_2634fb0@0" ObjectIDZND1="0@x" ObjectIDZND2="8301@x" Pin0InfoVect0LinkObjId="g_2634fb0_0" Pin0InfoVect1LinkObjId="g_23900a0_0" Pin0InfoVect2LinkObjId="SW-46775_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46789_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-366 3951,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2384460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-207 3951,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2653bc0@0" ObjectIDZND0="g_2634fb0@0" ObjectIDZND1="8309@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2634fb0_0" Pin0InfoVect1LinkObjId="SW-46789_0" Pin0InfoVect2LinkObjId="g_23900a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2653bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-207 3951,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24675d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-154 3950,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2653bc0@1" ObjectIDZND0="g_2303080@0" Pin0InfoVect0LinkObjId="g_2303080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2653bc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-154 3950,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_261a640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-460 4624,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8312@0" ObjectIDZND0="8289@0" Pin0InfoVect0LinkObjId="g_23ff0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46791_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-460 4624,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_260dd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-428 4624,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8298@1" ObjectIDZND0="8312@1" Pin0InfoVect0LinkObjId="SW-46791_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-428 4624,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ee970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-386 4624,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8313@0" ObjectIDZND0="8298@0" Pin0InfoVect0LinkObjId="SW-46772_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46791_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-386 4624,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245a010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-353 4624,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8299@x" ObjectIDND1="g_26335d0@0" ObjectIDND2="g_23424e0@0" ObjectIDZND0="8313@1" Pin0InfoVect0LinkObjId="SW-46791_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46773_0" Pin1InfoVect1LinkObjId="g_26335d0_0" Pin1InfoVect2LinkObjId="g_23424e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-353 4624,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26026a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-329 4595,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_23424e0@0" ObjectIDND1="8299@x" ObjectIDND2="8313@x" ObjectIDZND0="g_26335d0@0" Pin0InfoVect0LinkObjId="g_26335d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23424e0_0" Pin1InfoVect1LinkObjId="SW-46773_0" Pin1InfoVect2LinkObjId="SW-46791_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-329 4595,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23bd7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-318 4624,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_23424e0@0" ObjectIDZND0="g_26335d0@0" ObjectIDZND1="8299@x" ObjectIDZND2="8313@x" Pin0InfoVect0LinkObjId="g_26335d0_0" Pin0InfoVect1LinkObjId="SW-46773_0" Pin0InfoVect2LinkObjId="SW-46791_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23424e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-318 4624,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ecf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-329 4624,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_26335d0@0" ObjectIDND1="g_23424e0@0" ObjectIDZND0="8299@x" ObjectIDZND1="8313@x" Pin0InfoVect0LinkObjId="SW-46773_0" Pin0InfoVect1LinkObjId="SW-46791_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26335d0_0" Pin1InfoVect1LinkObjId="g_23424e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-329 4624,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2603bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-221 4624,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_23424e0@1" Pin0InfoVect0LinkObjId="g_23424e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-221 4624,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f945d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-513 4385,-513 4385,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8289@0" ObjectIDND1="10864@x" ObjectIDZND0="g_25f9bb0@0" Pin0InfoVect0LinkObjId="g_25f9bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23ff0e0_0" Pin1InfoVect1LinkObjId="SW-46795_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-513 4385,-513 4385,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_238e2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-480 4353,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8289@0" ObjectIDZND0="g_25f9bb0@0" ObjectIDZND1="10864@x" Pin0InfoVect0LinkObjId="g_25f9bb0_0" Pin0InfoVect1LinkObjId="SW-46795_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23ff0e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-480 4353,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_260f040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-532 4353,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="10864@1" ObjectIDZND0="g_25f9bb0@0" ObjectIDZND1="8289@0" Pin0InfoVect0LinkObjId="g_25f9bb0_0" Pin0InfoVect1LinkObjId="g_23ff0e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46795_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-532 4353,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2383500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4755,-613 4733,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2540430@0" ObjectIDZND0="8305@1" Pin0InfoVect0LinkObjId="SW-46779_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2540430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4755,-613 4733,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ee140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-613 4674,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="8305@0" ObjectIDZND0="8314@x" ObjectIDZND1="g_26328e0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-46792_0" Pin0InfoVect1LinkObjId="g_26328e0_0" Pin0InfoVect2LinkObjId="g_23900a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-613 4674,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_260ca40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-578 4674,-563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8314@1" ObjectIDZND0="8304@1" Pin0InfoVect0LinkObjId="SW-46778_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-578 4674,-563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f2350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-613 4674,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="8305@x" ObjectIDND1="g_26328e0@0" ObjectIDND2="0@x" ObjectIDZND0="8314@0" Pin0InfoVect0LinkObjId="SW-46792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46779_0" Pin1InfoVect1LinkObjId="g_26328e0_0" Pin1InfoVect2LinkObjId="g_23900a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-613 4674,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f25b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-613 4644,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="8305@x" ObjectIDND1="8314@x" ObjectIDND2="0@x" ObjectIDZND0="g_26328e0@0" Pin0InfoVect0LinkObjId="g_26328e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46779_0" Pin1InfoVect1LinkObjId="SW-46792_0" Pin1InfoVect2LinkObjId="g_23900a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-613 4644,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f2810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-613 4674,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer2" ObjectIDND0="8305@x" ObjectIDND1="8314@x" ObjectIDND2="g_26328e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_23900a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46779_0" Pin1InfoVect1LinkObjId="SW-46792_0" Pin1InfoVect2LinkObjId="g_26328e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-613 4674,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f6340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-536 4674,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8304@0" ObjectIDZND0="8315@0" Pin0InfoVect0LinkObjId="SW-46792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46778_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-536 4674,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f65a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-502 4674,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8315@1" ObjectIDZND0="8289@0" Pin0InfoVect0LinkObjId="g_23ff0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-502 4674,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2390870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-880 4608,-880 4608,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_26a0f90@0" ObjectIDZND0="g_26a2890@0" Pin0InfoVect0LinkObjId="g_26a2890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26a0f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-880 4608,-880 4608,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2440fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-880 4674,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" ObjectIDND0="g_26a2890@0" ObjectIDND1="g_26a0f90@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26a2890_0" Pin1InfoVect1LinkObjId="g_26a0f90_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-880 4674,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2441230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-501 4067,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8307@1" ObjectIDZND0="8289@0" Pin0InfoVect0LinkObjId="g_23ff0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46788_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-501 4067,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23be470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-816 5025,-816 5025,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_26a1c10@0" ObjectIDZND0="g_26a3540@0" Pin0InfoVect0LinkObjId="g_26a3540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26a1c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-816 5025,-816 5025,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2447290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-942 4961,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDZND0="g_26a3540@0" ObjectIDZND1="g_26a1c10@0" Pin0InfoVect0LinkObjId="g_26a3540_0" Pin0InfoVect1LinkObjId="g_26a1c10_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-942 4961,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25fe620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-301 3872,-301 3872,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2634fb0@0" ObjectIDND1="8309@x" ObjectIDND2="8301@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_23900a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2634fb0_0" Pin1InfoVect1LinkObjId="SW-46789_0" Pin1InfoVect2LinkObjId="SW-46775_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-301 3872,-301 3872,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25ff100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-271 3872,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_25fe880@1" Pin0InfoVect0LinkObjId="g_25fe880_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23900a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-271 3872,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25ff360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-225 3872,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_25fe880@0" ObjectIDZND0="g_23fffb0@0" Pin0InfoVect0LinkObjId="g_23fffb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25fe880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-225 3872,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2691300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-339 4323,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_26545d0@0" ObjectIDND1="0@x" ObjectIDND2="8303@x" ObjectIDZND0="g_26342c0@0" Pin0InfoVect0LinkObjId="g_26342c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26545d0_0" Pin1InfoVect1LinkObjId="g_23900a0_0" Pin1InfoVect2LinkObjId="SW-46777_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-339 4323,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26947f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-462 4353,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8310@0" ObjectIDZND0="8289@0" Pin0InfoVect0LinkObjId="g_23ff0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-462 4353,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2694a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-430 4353,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8302@1" ObjectIDZND0="8310@1" Pin0InfoVect0LinkObjId="SW-46790_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46776_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-430 4353,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26978a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-302 4353,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_26545d0@0" ObjectIDND1="0@x" ObjectIDND2="8303@x" ObjectIDZND0="g_26342c0@0" ObjectIDZND1="8311@x" Pin0InfoVect0LinkObjId="g_26342c0_0" Pin0InfoVect1LinkObjId="SW-46790_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26545d0_0" Pin1InfoVect1LinkObjId="g_23900a0_0" Pin1InfoVect2LinkObjId="SW-46777_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-302 4353,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2697b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-403 4353,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8302@0" ObjectIDZND0="8311@0" Pin0InfoVect0LinkObjId="SW-46790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46776_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-403 4353,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2697d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-367 4353,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8311@1" ObjectIDZND0="g_26342c0@0" ObjectIDZND1="g_26545d0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_26342c0_0" Pin0InfoVect1LinkObjId="g_26545d0_0" Pin0InfoVect2LinkObjId="g_23900a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46790_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-367 4353,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2697fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-208 4353,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_26545d0@0" ObjectIDZND0="g_26342c0@0" ObjectIDZND1="8311@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_26342c0_0" Pin0InfoVect1LinkObjId="SW-46790_0" Pin0InfoVect2LinkObjId="g_23900a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26545d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-208 4353,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2698220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-155 4352,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_26545d0@1" ObjectIDZND0="g_2698480@0" Pin0InfoVect0LinkObjId="g_2698480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26545d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-155 4352,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2699c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-207 4420,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2699490@0" ObjectIDND1="8303@x" ObjectIDZND0="g_2698e70@0" Pin0InfoVect0LinkObjId="g_2698e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2699490_0" Pin1InfoVect1LinkObjId="SW-46777_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-207 4420,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2699e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-207 4467,-207 4467,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2698e70@0" ObjectIDND1="8303@x" ObjectIDZND0="g_2699490@1" Pin0InfoVect0LinkObjId="g_2699490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2698e70_0" Pin1InfoVect1LinkObjId="SW-46777_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-207 4467,-207 4467,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269a0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4467,-168 4467,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2699490@0" ObjectIDZND0="g_269a330@0" Pin0InfoVect0LinkObjId="g_269a330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2699490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4467,-168 4467,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269d440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-302 4274,-302 4274,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_26342c0@0" ObjectIDND1="8311@x" ObjectIDND2="g_26545d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_23900a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26342c0_0" Pin1InfoVect1LinkObjId="SW-46790_0" Pin1InfoVect2LinkObjId="g_26545d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-302 4274,-302 4274,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_269de20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4274,-272 4274,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_269d6a0@1" Pin0InfoVect0LinkObjId="g_269d6a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23900a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4274,-272 4274,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_269e080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4274,-226 4274,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_269d6a0@0" ObjectIDZND0="g_2691560@0" Pin0InfoVect0LinkObjId="g_2691560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_269d6a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4274,-226 4274,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269e2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-287 4420,-302 4353,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8303@0" ObjectIDZND0="g_26342c0@0" ObjectIDZND1="8311@x" ObjectIDZND2="g_26545d0@0" Pin0InfoVect0LinkObjId="g_26342c0_0" Pin0InfoVect1LinkObjId="SW-46790_0" Pin0InfoVect2LinkObjId="g_26545d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46777_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-287 4420,-302 4353,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269e540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-207 4420,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2698e70@0" ObjectIDND1="g_2699490@0" ObjectIDZND0="8303@1" Pin0InfoVect0LinkObjId="SW-46777_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2698e70_0" Pin1InfoVect1LinkObjId="g_2699490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-207 4420,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26a0ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-609 4353,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_23900a0@0" ObjectIDZND0="g_26a0310@1" Pin0InfoVect0LinkObjId="g_26a0310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23900a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-609 4353,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26a0d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-565 4353,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_26a0310@0" ObjectIDZND0="10864@0" Pin0InfoVect0LinkObjId="SW-46795_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26a0310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-565 4353,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26a1750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-740 4674,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_26a0f90@0" Pin0InfoVect0LinkObjId="g_26a0f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23900a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-740 4674,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26a19b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-846 4674,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_26a0f90@1" ObjectIDZND0="g_26a2890@0" Pin0InfoVect0LinkObjId="g_26a2890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26a0f90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-846 4674,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26a23d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-816 4961,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_26a3540@0" ObjectIDZND0="g_26a1c10@1" Pin0InfoVect0LinkObjId="g_26a1c10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26a3540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-816 4961,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26a2630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-725 4961,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_26a1c10@0" ObjectIDZND0="g_24474a0@0" Pin0InfoVect0LinkObjId="g_24474a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26a1c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-725 4961,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26a5350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-717 4006,-717 4006,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="8317@x" ObjectIDZND0="g_26a55b0@0" Pin0InfoVect0LinkObjId="g_26a55b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_238f660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-717 4006,-717 4006,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26a5fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-768 4110,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="8293@x" ObjectIDND1="8317@x" ObjectIDND2="8291@x" ObjectIDZND0="g_2635ca0@0" Pin0InfoVect0LinkObjId="g_2635ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46767_0" Pin1InfoVect1LinkObjId="g_238f660_0" Pin1InfoVect2LinkObjId="SW-46765_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-768 4110,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2636990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4024,-1024 4067,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2637b40@0" ObjectIDZND0="8295@x" ObjectIDZND1="8292@x" ObjectIDZND2="g_2638810@0" Pin0InfoVect0LinkObjId="SW-46769_0" Pin0InfoVect1LinkObjId="SW-46766_0" Pin0InfoVect2LinkObjId="g_2638810_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2637b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4024,-1024 4067,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2637680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1024 4067,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2637b40@0" ObjectIDND1="g_2638810@0" ObjectIDZND0="8295@x" ObjectIDZND1="8292@x" Pin0InfoVect0LinkObjId="SW-46769_0" Pin0InfoVect1LinkObjId="SW-46766_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2637b40_0" Pin1InfoVect1LinkObjId="g_2638810_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1024 4067,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26378e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4112,-1010 4112,-1024 4067,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2638810@0" ObjectIDZND0="g_2637b40@0" ObjectIDZND1="8295@x" ObjectIDZND2="8292@x" Pin0InfoVect0LinkObjId="g_2637b40_0" Pin0InfoVect1LinkObjId="SW-46769_0" Pin0InfoVect2LinkObjId="SW-46766_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2638810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4112,-1010 4112,-1024 4067,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26428a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-159 4018,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_25fa960@0" ObjectIDZND0="g_25fb060@0" ObjectIDZND1="8301@x" Pin0InfoVect0LinkObjId="g_25fb060_0" Pin0InfoVect1LinkObjId="SW-46775_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25fa960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-159 4018,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2643200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-154 4065,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2642b00@0" ObjectIDZND0="g_25fb060@0" Pin0InfoVect0LinkObjId="g_25fb060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2642b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-154 4065,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2643460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-200 4065,-209 4018,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_25fb060@1" ObjectIDZND0="g_25fa960@0" ObjectIDZND1="8301@x" Pin0InfoVect0LinkObjId="g_25fa960_0" Pin0InfoVect1LinkObjId="SW-46775_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25fb060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-200 4065,-209 4018,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26436c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-301 4018,-301 4018,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2634fb0@0" ObjectIDND1="8309@x" ObjectIDND2="0@x" ObjectIDZND0="8301@0" Pin0InfoVect0LinkObjId="SW-46775_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2634fb0_0" Pin1InfoVect1LinkObjId="SW-46789_0" Pin1InfoVect2LinkObjId="g_23900a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-301 4018,-301 4018,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2643920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-219 4018,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8301@1" ObjectIDZND0="g_25fa960@0" ObjectIDZND1="g_25fb060@0" Pin0InfoVect0LinkObjId="g_25fa960_0" Pin0InfoVect1LinkObjId="g_25fb060_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46775_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-219 4018,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26551b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-1024 4067,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2637b40@0" ObjectIDND1="8295@x" ObjectIDND2="8292@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2637b40_0" Pin1InfoVect1LinkObjId="SW-46769_0" Pin1InfoVect2LinkObjId="SW-46766_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-1024 4067,-1051 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="8289" cx="3951" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8289" cx="4624" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8289" cx="4353" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8289" cx="4674" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8289" cx="4067" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8289" cx="4353" cy="-480" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2604c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3119.000000 -1155.000000) translate(0,17)">加南网标志（288＊90）：渔泡江三级电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2388ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3030.000000 -1139.000000) translate(0,12)">0.1h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2442f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3025.000000 -925.000000) translate(0,12)">0.4h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23fd3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3032.000000 -500.000000) translate(0,12)">0.5h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24a53c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3167.000000 -1120.000000) translate(0,12)">系统时间（180＊36）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2364420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -977.000000) translate(0,17)">频率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2364420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -977.000000) translate(0,38)">全站有功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2364420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -977.000000) translate(0,59)">全站无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2364420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -977.000000) translate(0,80)">并网联络点的电压和交换功率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2145d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3118.000000 -589.000000) translate(0,17)">危险点说明</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2145d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3118.000000 -589.000000) translate(0,38)">联系方式</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2383fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3289.000000 -1231.000000) translate(0,12)">0.3h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_247a010" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3901.000000 -108.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23a3180" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4591.000000 -209.000000) translate(0,15)">1号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23c5a90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4316.000000 -647.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2620f80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4068.000000 -1104.000000) translate(0,15)">110kV渔大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_243d2c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3990.000000 -108.000000) translate(0,15)">1号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26139b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4319.000000 -108.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2617170" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4409.000000 -108.000000) translate(0,15)">2号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23ee3a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4637.000000 -967.000000) translate(0,15)">10kV坝区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2441490" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4706.000000 -705.000000) translate(0,15)">坝区变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25409b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4923.000000 -968.000000) translate(0,15)">10kV施工电源</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26229a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4988.000000 -642.000000) translate(0,15)">2号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2638ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4083.000000 -864.000000) translate(0,12)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2639620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3930.000000 -701.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2639bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4640.000000 -423.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2639e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -379.000000) translate(0,12)">08367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_263a1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -423.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_263a620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -424.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2643b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4030.000000 -261.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26441b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4432.000000 -262.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26443f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3808.000000 -500.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2645140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -639.000000) translate(0,12)">08467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26456c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4689.000000 -557.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2645900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -635.000000) translate(0,12)">00167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2645b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4081.000000 -553.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2645d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 -794.000000) translate(0,12)">18117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2645fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4074.000000 -813.000000) translate(0,12)">1811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2646200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4006.000000 -909.000000) translate(0,12)">18160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2646440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4074.000000 -925.000000) translate(0,12)">1816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2646680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4011.000000 -986.000000) translate(0,12)">18167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="50" graphid="g_264fa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3124.000000 -1072.000000) translate(0,40)">渔泡江三级电站</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="1201" stroke="rgb(255,0,0)" stroke-width="1" width="2150" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(255,0,0)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(255,0,0)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(255,0,0)" stroke-width="1" width="360" x="3118" y="-599"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YPJ.CX_YPJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3809,-480 4834,-480 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="8289" ObjectName="BS-CX_YPJ.CX_YPJ_9IM"/>
    <cge:TPSR_Ref TObjectID="8289"/></metadata>
   <polyline fill="none" opacity="0" points="3809,-480 4834,-480 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_23fffb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.000000 -181.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23424e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 -272.000000)" xlink:href="#lightningRod:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24474a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4948.000000 -613.000000)" xlink:href="#lightningRod:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25f9bb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4378.000000 -523.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25fa960">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.000000 -123.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25fb060">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 -164.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25fe880">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 -220.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2691560">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4260.000000 -188.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2698e70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4408.000000 -124.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2699490">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4458.000000 -163.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_269a330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -118.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_269d6a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 -221.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26a0310">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -560.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26a0f90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4665.000000 -810.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26a1c10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -720.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26a2890">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4601.000000 -794.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26a3540">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5018.000000 -730.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26a6200">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3978.000000 -603.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26328e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.000000 -607.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26335d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 -323.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26342c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 -333.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2634fb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 -332.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2635ca0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -762.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2637b40">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3965.000000 -1018.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2638810">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -986.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2642b00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 -118.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2653bc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -149.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26545d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -150.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-46658" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -879.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8290"/>
     <cge:Term_Ref ObjectID="11664"/>
    <cge:TPSR_Ref TObjectID="8290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-46659" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -879.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8290"/>
     <cge:Term_Ref ObjectID="11664"/>
    <cge:TPSR_Ref TObjectID="8290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-46660" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -879.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46660" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8290"/>
     <cge:Term_Ref ObjectID="11664"/>
    <cge:TPSR_Ref TObjectID="8290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-46675" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -569.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46675" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8296"/>
     <cge:Term_Ref ObjectID="11676"/>
    <cge:TPSR_Ref TObjectID="8296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-46676" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -569.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46676" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8296"/>
     <cge:Term_Ref ObjectID="11676"/>
    <cge:TPSR_Ref TObjectID="8296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-46677" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -569.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46677" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8296"/>
     <cge:Term_Ref ObjectID="11676"/>
    <cge:TPSR_Ref TObjectID="8296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-46685" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -569.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46685" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8304"/>
     <cge:Term_Ref ObjectID="11692"/>
    <cge:TPSR_Ref TObjectID="8304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-46686" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -569.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8304"/>
     <cge:Term_Ref ObjectID="11692"/>
    <cge:TPSR_Ref TObjectID="8304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-46687" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -569.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46687" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8304"/>
     <cge:Term_Ref ObjectID="11692"/>
    <cge:TPSR_Ref TObjectID="8304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-46663" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -437.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46663" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8300"/>
     <cge:Term_Ref ObjectID="11684"/>
    <cge:TPSR_Ref TObjectID="8300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-46664" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -437.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8300"/>
     <cge:Term_Ref ObjectID="11684"/>
    <cge:TPSR_Ref TObjectID="8300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-46665" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -437.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46665" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8300"/>
     <cge:Term_Ref ObjectID="11684"/>
    <cge:TPSR_Ref TObjectID="8300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-46669" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -437.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8302"/>
     <cge:Term_Ref ObjectID="11688"/>
    <cge:TPSR_Ref TObjectID="8302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-46670" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -437.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46670" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8302"/>
     <cge:Term_Ref ObjectID="11688"/>
    <cge:TPSR_Ref TObjectID="8302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-46671" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -437.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8302"/>
     <cge:Term_Ref ObjectID="11688"/>
    <cge:TPSR_Ref TObjectID="8302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-46680" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4744.000000 -437.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8298"/>
     <cge:Term_Ref ObjectID="11680"/>
    <cge:TPSR_Ref TObjectID="8298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-46681" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4744.000000 -437.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46681" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8298"/>
     <cge:Term_Ref ObjectID="11680"/>
    <cge:TPSR_Ref TObjectID="8298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-46682" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4744.000000 -437.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46682" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8298"/>
     <cge:Term_Ref ObjectID="11680"/>
    <cge:TPSR_Ref TObjectID="8298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3872.000000 -519.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8289"/>
     <cge:Term_Ref ObjectID="11663"/>
    <cge:TPSR_Ref TObjectID="8289"/></metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26496f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 879.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264ab50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.000000 864.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264b830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 849.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264c230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 569.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264c4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.000000 554.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264c730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 539.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264cb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4002.000000 437.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264ce10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 422.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264d050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.000000 407.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264d470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4404.000000 437.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264d730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4393.000000 422.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264d970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4418.000000 407.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264dd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4682.000000 437.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264e050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.000000 422.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264e290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.000000 407.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264e6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4730.000000 569.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264e970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 554.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264ebb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 539.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264efd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 519.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.000000 -655.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.000000 -655.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YPJ.CX_YPJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="11720"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 -651.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 -651.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="8317" ObjectName="TF-CX_YPJ.CX_YPJ_1T"/>
    <cge:TPSR_Ref TObjectID="8317"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-46771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.000000 -583.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8297" ObjectName="SW-CX_YPJ.CX_YPJ_00167SW"/>
     <cge:Meas_Ref ObjectId="46771"/>
    <cge:TPSR_Ref TObjectID="8297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -495.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8307" ObjectName="SW-CX_YPJ.CX_YPJ_001XC1"/>
     <cge:Meas_Ref ObjectId="46788"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 -567.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8306" ObjectName="SW-CX_YPJ.CX_YPJ_001XC"/>
     <cge:Meas_Ref ObjectId="46788"/>
    <cge:TPSR_Ref TObjectID="8306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46765">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4052.000000 -766.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8291" ObjectName="SW-CX_YPJ.CX_YPJ_1811SW"/>
     <cge:Meas_Ref ObjectId="46765"/>
    <cge:TPSR_Ref TObjectID="8291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46766">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4052.000000 -878.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8292" ObjectName="SW-CX_YPJ.CX_YPJ_1816SW"/>
     <cge:Meas_Ref ObjectId="46766"/>
    <cge:TPSR_Ref TObjectID="8292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46789">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -437.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8308" ObjectName="SW-CX_YPJ.CX_YPJ_081XC"/>
     <cge:Meas_Ref ObjectId="46789"/>
    <cge:TPSR_Ref TObjectID="8308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46789">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -360.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8309" ObjectName="SW-CX_YPJ.CX_YPJ_081XC1"/>
     <cge:Meas_Ref ObjectId="46789"/>
    <cge:TPSR_Ref TObjectID="8309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46791">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -436.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8312" ObjectName="SW-CX_YPJ.CX_YPJ_083XC"/>
     <cge:Meas_Ref ObjectId="46791"/>
    <cge:TPSR_Ref TObjectID="8312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46791">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -362.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8313" ObjectName="SW-CX_YPJ.CX_YPJ_083XC1"/>
     <cge:Meas_Ref ObjectId="46791"/>
    <cge:TPSR_Ref TObjectID="8313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46795">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -526.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10864" ObjectName="SW-CX_YPJ.CX_YPJ_GG_SW"/>
     <cge:Meas_Ref ObjectId="46795"/>
    <cge:TPSR_Ref TObjectID="10864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46779">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.000000 -587.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8305" ObjectName="SW-CX_YPJ.CX_YPJ_08467SW"/>
     <cge:Meas_Ref ObjectId="46779"/>
    <cge:TPSR_Ref TObjectID="8305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 -496.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8315" ObjectName="SW-CX_YPJ.CX_YPJ_084XC1"/>
     <cge:Meas_Ref ObjectId="46792"/>
    <cge:TPSR_Ref TObjectID="8315"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 -571.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8314" ObjectName="SW-CX_YPJ.CX_YPJ_084XC"/>
     <cge:Meas_Ref ObjectId="46792"/>
    <cge:TPSR_Ref TObjectID="8314"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46768">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 -857.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8294" ObjectName="SW-CX_YPJ.CX_YPJ_18160SW"/>
     <cge:Meas_Ref ObjectId="46768"/>
    <cge:TPSR_Ref TObjectID="8294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46769">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4022.000000 -934.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8295" ObjectName="SW-CX_YPJ.CX_YPJ_18167SW"/>
     <cge:Meas_Ref ObjectId="46769"/>
    <cge:TPSR_Ref TObjectID="8295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46767">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 -742.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8293" ObjectName="SW-CX_YPJ.CX_YPJ_18117SW"/>
     <cge:Meas_Ref ObjectId="46767"/>
    <cge:TPSR_Ref TObjectID="8293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 -264.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4571.000000 -327.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8299" ObjectName="SW-CX_YPJ.CX_YPJ_08367SW"/>
     <cge:Meas_Ref ObjectId="46773"/>
    <cge:TPSR_Ref TObjectID="8299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46790">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -438.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8310" ObjectName="SW-CX_YPJ.CX_YPJ_082XC"/>
     <cge:Meas_Ref ObjectId="46790"/>
    <cge:TPSR_Ref TObjectID="8310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46790">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -361.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8311" ObjectName="SW-CX_YPJ.CX_YPJ_082XC1"/>
     <cge:Meas_Ref ObjectId="46790"/>
    <cge:TPSR_Ref TObjectID="8311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.000000 -265.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46775">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -225.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8301" ObjectName="SW-CX_YPJ.CX_YPJ_0816SW"/>
     <cge:Meas_Ref ObjectId="46775"/>
    <cge:TPSR_Ref TObjectID="8301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46777">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 -226.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8303" ObjectName="SW-CX_YPJ.CX_YPJ_0826SW"/>
     <cge:Meas_Ref ObjectId="46777"/>
    <cge:TPSR_Ref TObjectID="8303"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.400000 3224.000000 -1004.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YPJ"/>
</svg>