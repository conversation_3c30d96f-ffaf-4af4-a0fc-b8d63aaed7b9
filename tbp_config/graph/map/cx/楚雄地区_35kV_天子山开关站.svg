<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-131" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-330 -957 1655 1119">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape38">
    <rect height="8" stroke-width="0.75" width="18" x="21" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="1" x2="1" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="4" x2="4" y1="10" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="7" x2="7" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="21" x2="7" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="46" x2="26" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="28" x2="26" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="26" x2="28" y1="7" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="6" x2="6" y1="17" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.337605" x1="7" x2="7" y1="47" y2="36"/>
    <polyline arcFlag="1" points="7,24 7,24 7,24 7,24 8,24 8,24 8,25 8,25 9,25 9,25 9,26 9,26 9,26 9,27 9,27 9,28 9,28 9,28 9,29 8,29 8,29 8,29 8,30 7,30 7,30 7,30 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="6,18 7,18 7,18 7,18 7,18 8,18 8,18 8,19 8,19 8,19 8,20 9,20 9,20 9,21 9,21 9,21 8,22 8,22 8,22 8,23 8,23 8,23 7,23 7,24 7,24 7,24 " stroke-width="0.0170053"/>
    <polyline arcFlag="1" points="7,30 7,30 7,30 7,30 8,30 8,31 8,31 8,31 9,31 9,32 9,32 9,32 9,33 9,33 9,33 9,34 9,34 9,35 9,35 8,35 8,35 8,36 8,36 7,36 7,36 7,36 " stroke-width="0.0170053"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.266312" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="4" x2="9" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="0" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_2e393d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape44">
    <ellipse cx="11" cy="16" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="29" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape188">
    <polyline DF8003:Layer="PUBLIC" points="19,18 10,0 1,19 19,18 " stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape35_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 8,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="55" y2="51"/>
   </symbol>
   <symbol id="transformer2:shape35_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="transformer2:shape1_0">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape1_1">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
   </symbol>
   <symbol id="voltageTransformer:shape44">
    <ellipse cx="19" cy="19" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1.26667" x1="38" x2="59" y1="11" y2="30"/>
    <rect height="17" stroke-width="0.888889" width="8" x="41" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="30" y1="19" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="30" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="38" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="30" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="7" x2="5" y1="17" y2="20"/>
    <ellipse cx="8" cy="18" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="24" cy="29" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="13" cy="29" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline points="30,1 45,1 45,55 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.35645" x1="42" x2="48" y1="58" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="44" x2="46" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16267" x1="41" x2="49" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="24" x2="24" y1="30" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="24" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="13" x2="13" y1="30" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="13" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="10" x2="13" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="17" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="20" x2="20" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="17" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="20" y1="17" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="45" y1="39" y2="39"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3b3b920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3b3ca80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3b3d430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3b3e420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3b3f680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3b402a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3b409e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3b414a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_31b5a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_31b5a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3b44360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3b44360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e7900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e7900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3b46330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3b47fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3b48c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3b49af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3b4a3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3b4bb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3b4c890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3b4d150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3b4d910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3b4e9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3b4f370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3b4fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3b50820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3b51ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3b52840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3b53870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3b544d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3b62840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3b55e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3b57520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3b58970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1129" width="1665" x="-335" y="-962"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.265306" x1="112" x2="121" y1="38" y2="38"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.265306" x1="116" x2="112" y1="30" y2="38"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.265306" x1="116" x2="121" y1="30" y2="38"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.265306" x1="112" x2="121" y1="16" y2="16"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.265306" x1="116" x2="112" y1="8" y2="16"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.265306" x1="116" x2="121" y1="8" y2="16"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1196" x2="1244" y1="-591" y2="-591"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1244" x2="1244" y1="-591" y2="-575"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1244" x2="1195" y1="-575" y2="-546"/>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1205,-719 1196,-701 1187,-719 1205,-719 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-77401">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 107.000000 -182.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16815" ObjectName="SW-CX_TZS.CX_TZS_351BK"/>
     <cge:Meas_Ref ObjectId="77401"/>
    <cge:TPSR_Ref TObjectID="16815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77388">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 896.000000 -139.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16811" ObjectName="SW-CX_TZS.CX_TZS_355BK"/>
     <cge:Meas_Ref ObjectId="77388"/>
    <cge:TPSR_Ref TObjectID="16811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77374">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.000000 -170.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16807" ObjectName="SW-CX_TZS.CX_TZS_352BK"/>
     <cge:Meas_Ref ObjectId="77374"/>
    <cge:TPSR_Ref TObjectID="16807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77362">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 445.000000 -169.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16803" ObjectName="SW-CX_TZS.CX_TZS_353BK"/>
     <cge:Meas_Ref ObjectId="77362"/>
    <cge:TPSR_Ref TObjectID="16803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77350">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 737.000000 -408.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16799" ObjectName="SW-CX_TZS.CX_TZS_354BK"/>
     <cge:Meas_Ref ObjectId="77350"/>
    <cge:TPSR_Ref TObjectID="16799"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b049b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 -410.000000)" xlink:href="#voltageTransformer:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_TZS.CX_TZS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="21,-289 1098,-289 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="16797" ObjectName="BS-CX_TZS.CX_TZS_3IM"/>
    <cge:TPSR_Ref TObjectID="16797"/></metadata>
   <polyline fill="none" opacity="0" points="21,-289 1098,-289 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 889.000000 93.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 889.000000 93.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1170.000000 -567.000000)" xlink:href="#transformer2:shape1_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1170.000000 -567.000000)" xlink:href="#transformer2:shape1_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_338d1f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.000000 -468.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a76a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 741.000000 -594.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33e3110">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.000000 -677.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32267f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 861.000000 113.000000)" xlink:href="#lightningRod:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e2f480">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99.000000 96.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e39c10">
    <use class="BV-35KV" transform="matrix(1.217391 -0.000000 0.000000 -1.177778 102.000000 51.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33e0df0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 128.000000 -169.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33fe830">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 -148.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ee870">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 466.000000 -144.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b59ae0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -358.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b5a4b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 534.000000 -364.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3384f90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1185.000000 -481.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -239.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116486" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -204.461538 -748.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116486" ObjectName="CX_TZS:CX_TZS_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116487" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -206.461538 -707.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116487" ObjectName="CX_TZS:CX_TZS_ZJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-77327" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 891.000000 117.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77327" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16811"/>
     <cge:Term_Ref ObjectID="24167"/>
    <cge:TPSR_Ref TObjectID="16811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-77328" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 891.000000 117.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77328" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16811"/>
     <cge:Term_Ref ObjectID="24167"/>
    <cge:TPSR_Ref TObjectID="16811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-77324" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 891.000000 117.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77324" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16811"/>
     <cge:Term_Ref ObjectID="24167"/>
    <cge:TPSR_Ref TObjectID="16811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-77303" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 429.000000 82.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77303" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16803"/>
     <cge:Term_Ref ObjectID="24151"/>
    <cge:TPSR_Ref TObjectID="16803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-77304" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 429.000000 82.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16803"/>
     <cge:Term_Ref ObjectID="24151"/>
    <cge:TPSR_Ref TObjectID="16803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-77300" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 429.000000 82.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77300" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16803"/>
     <cge:Term_Ref ObjectID="24151"/>
    <cge:TPSR_Ref TObjectID="16803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-77315" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 274.000000 82.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77315" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16807"/>
     <cge:Term_Ref ObjectID="24159"/>
    <cge:TPSR_Ref TObjectID="16807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-77316" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 274.000000 82.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77316" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16807"/>
     <cge:Term_Ref ObjectID="24159"/>
    <cge:TPSR_Ref TObjectID="16807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-77312" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 274.000000 82.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77312" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16807"/>
     <cge:Term_Ref ObjectID="24159"/>
    <cge:TPSR_Ref TObjectID="16807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-77339" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98.000000 128.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77339" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16815"/>
     <cge:Term_Ref ObjectID="24175"/>
    <cge:TPSR_Ref TObjectID="16815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-77336" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98.000000 128.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77336" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16815"/>
     <cge:Term_Ref ObjectID="24175"/>
    <cge:TPSR_Ref TObjectID="16815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-77276" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -354.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16797"/>
     <cge:Term_Ref ObjectID="24140"/>
    <cge:TPSR_Ref TObjectID="16797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-77277" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -354.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16797"/>
     <cge:Term_Ref ObjectID="24140"/>
    <cge:TPSR_Ref TObjectID="16797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-77278" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -354.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77278" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16797"/>
     <cge:Term_Ref ObjectID="24140"/>
    <cge:TPSR_Ref TObjectID="16797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-77279" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -354.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16797"/>
     <cge:Term_Ref ObjectID="24140"/>
    <cge:TPSR_Ref TObjectID="16797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-77291" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 864.000000 -454.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77291" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16799"/>
     <cge:Term_Ref ObjectID="24143"/>
    <cge:TPSR_Ref TObjectID="16799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-77292" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 864.000000 -454.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77292" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16799"/>
     <cge:Term_Ref ObjectID="24143"/>
    <cge:TPSR_Ref TObjectID="16799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-77288" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 864.000000 -454.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="77288" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16799"/>
     <cge:Term_Ref ObjectID="24143"/>
    <cge:TPSR_Ref TObjectID="16799"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="155" x="-227" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="155" x="-227" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-276" y="-957"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-1,-818 -4,-821 -4,-767 -1,-770 -1,-818" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-1,-818 -4,-821 145,-821 142,-818 -1,-818" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="-1,-770 -4,-767 145,-767 142,-770 -1,-770" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="142,-818 145,-821 145,-767 142,-770 142,-818" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="-1" y="-818"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-1" y="-818"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="155" x="-227" y="-940"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/></g>
   <g href="AVC天子山.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-1" y="-818"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="80,-17 80,-48 112,-48 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-77403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 107.000000 -121.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16817" ObjectName="SW-CX_TZS.CX_TZS_3513SW"/>
     <cge:Meas_Ref ObjectId="77403"/>
    <cge:TPSR_Ref TObjectID="16817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77402">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 107.000000 -230.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16816" ObjectName="SW-CX_TZS.CX_TZS_3511SW"/>
     <cge:Meas_Ref ObjectId="77402"/>
    <cge:TPSR_Ref TObjectID="16816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77389">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 896.000000 -204.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16812" ObjectName="SW-CX_TZS.CX_TZS_3551SW"/>
     <cge:Meas_Ref ObjectId="77389"/>
    <cge:TPSR_Ref TObjectID="16812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77390">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 896.000000 -89.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16813" ObjectName="SW-CX_TZS.CX_TZS_3556SW"/>
     <cge:Meas_Ref ObjectId="77390"/>
    <cge:TPSR_Ref TObjectID="16813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78344">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 107.000000 -26.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17189" ObjectName="SW-CX_TZS.CX_TZS_3516SW"/>
     <cge:Meas_Ref ObjectId="78344"/>
    <cge:TPSR_Ref TObjectID="17189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77353">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 -567.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16802" ObjectName="SW-CX_TZS.CX_TZS_35467SW"/>
     <cge:Meas_Ref ObjectId="77353"/>
    <cge:TPSR_Ref TObjectID="16802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78343">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 859.000000 60.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17188" ObjectName="SW-CX_TZS.CX_TZS_3010SW"/>
     <cge:Meas_Ref ObjectId="78343"/>
    <cge:TPSR_Ref TObjectID="17188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78345">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 59.000000 -8.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17190" ObjectName="SW-CX_TZS.CX_TZS_35167SW"/>
     <cge:Meas_Ref ObjectId="78345"/>
    <cge:TPSR_Ref TObjectID="17190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 59.000000 -110.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16818" ObjectName="SW-CX_TZS.CX_TZS_35137SW"/>
     <cge:Meas_Ref ObjectId="77404"/>
    <cge:TPSR_Ref TObjectID="16818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77376">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.000000 -92.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16809" ObjectName="SW-CX_TZS.CX_TZS_3526SW"/>
     <cge:Meas_Ref ObjectId="77376"/>
    <cge:TPSR_Ref TObjectID="16809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77375">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16808" ObjectName="SW-CX_TZS.CX_TZS_3521SW"/>
     <cge:Meas_Ref ObjectId="77375"/>
    <cge:TPSR_Ref TObjectID="16808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77377">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.000000 -70.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16810" ObjectName="SW-CX_TZS.CX_TZS_35267SW"/>
     <cge:Meas_Ref ObjectId="77377"/>
    <cge:TPSR_Ref TObjectID="16810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77364">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 445.000000 -90.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16805" ObjectName="SW-CX_TZS.CX_TZS_3536SW"/>
     <cge:Meas_Ref ObjectId="77364"/>
    <cge:TPSR_Ref TObjectID="16805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77363">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 445.000000 -222.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16804" ObjectName="SW-CX_TZS.CX_TZS_3531SW"/>
     <cge:Meas_Ref ObjectId="77363"/>
    <cge:TPSR_Ref TObjectID="16804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77365">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 397.000000 -69.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16806" ObjectName="SW-CX_TZS.CX_TZS_35367SW"/>
     <cge:Meas_Ref ObjectId="77365"/>
    <cge:TPSR_Ref TObjectID="16806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77391">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 920.000000 -67.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16814" ObjectName="SW-CX_TZS.CX_TZS_35567SW"/>
     <cge:Meas_Ref ObjectId="77391"/>
    <cge:TPSR_Ref TObjectID="16814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77352">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 737.000000 -504.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16801" ObjectName="SW-CX_TZS.CX_TZS_3546SW"/>
     <cge:Meas_Ref ObjectId="77352"/>
    <cge:TPSR_Ref TObjectID="16801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77341">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 569.000000 -297.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16798" ObjectName="SW-CX_TZS.CX_TZS_3901SW"/>
     <cge:Meas_Ref ObjectId="77341"/>
    <cge:TPSR_Ref TObjectID="16798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77351">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 737.000000 -347.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16800" ObjectName="SW-CX_TZS.CX_TZS_3541SW"/>
     <cge:Meas_Ref ObjectId="77351"/>
    <cge:TPSR_Ref TObjectID="16800"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HGY" endPointId="0" endStationName="CX_TZS" flowDrawDirect="1" flowShape="0" id="AC-35kV.tianzishan_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="746,-793 746,-871 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18021" ObjectName="AC-35kV.tianzishan_line"/>
    <cge:TPSR_Ref TObjectID="18021_SS-131"/></metadata>
   <polyline fill="none" opacity="0" points="746,-793 746,-871 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_341dc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="116,-271 116,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16816@1" ObjectIDZND0="16797@0" Pin0InfoVect0LinkObjId="g_2c1f840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77402_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="116,-271 116,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2acb3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-148 905,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16811@0" ObjectIDZND0="16813@1" Pin0InfoVect0LinkObjId="SW-77390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77388_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="905,-148 905,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c1f840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-245 905,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16812@1" ObjectIDZND0="16797@0" Pin0InfoVect0LinkObjId="g_341dc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77389_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="905,-245 905,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a8480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="824,-572 809,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33a87d0@0" ObjectIDZND0="16802@1" Pin0InfoVect0LinkObjId="SW-77353_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a87d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="824,-572 809,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_338ab00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="696,-684 746,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_33e3110@0" ObjectIDZND0="g_33a76a0@0" ObjectIDZND1="18021@1" Pin0InfoVect0LinkObjId="g_33a76a0_0" Pin0InfoVect1LinkObjId="g_33c9340_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33e3110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="696,-684 746,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3414600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="746,-652 746,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_33a76a0@1" ObjectIDZND0="g_33e3110@0" ObjectIDZND1="18021@1" Pin0InfoVect0LinkObjId="g_33e3110_0" Pin0InfoVect1LinkObjId="g_33c9340_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a76a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="746,-652 746,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33dddb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="773,-572 746,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16802@0" ObjectIDZND0="g_33a76a0@0" ObjectIDZND1="16801@x" Pin0InfoVect0LinkObjId="g_33a76a0_0" Pin0InfoVect1LinkObjId="SW-77352_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77353_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="773,-572 746,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b97030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="746,-572 746,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="16801@x" ObjectIDND1="16802@x" ObjectIDZND0="g_33a76a0@0" Pin0InfoVect0LinkObjId="g_33a76a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77352_0" Pin1InfoVect1LinkObjId="SW-77353_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="746,-572 746,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2def100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="696,-475 746,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_338d1f0@0" ObjectIDZND0="16799@x" ObjectIDZND1="16801@x" Pin0InfoVect0LinkObjId="SW-77350_0" Pin0InfoVect1LinkObjId="SW-77352_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_338d1f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="696,-475 746,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3390020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-175 905,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16811@1" ObjectIDZND0="16812@0" Pin0InfoVect0LinkObjId="SW-77389_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="905,-175 905,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34046d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,12 868,12 868,19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="17188@1" Pin0InfoVect0LinkObjId="SW-78343_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b049b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="905,12 868,12 868,19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_333b4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,55 868,66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="17188@0" ObjectIDZND0="g_32267f0@0" Pin0InfoVect0LinkObjId="g_32267f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78343_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="868,55 868,66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2da6040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="116,-235 116,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16816@0" ObjectIDZND0="16815@1" Pin0InfoVect0LinkObjId="SW-77401_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77402_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="116,-235 116,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e399b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="116,45 116,66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2e39c10@0" ObjectIDZND0="g_2e2f480@0" Pin0InfoVect0LinkObjId="g_2e2f480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e39c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="116,45 116,66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b0730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="116,-31 116,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="17189@0" ObjectIDZND0="g_2e39c10@0" ObjectIDZND1="17190@x" Pin0InfoVect0LinkObjId="g_2e39c10_0" Pin0InfoVect1LinkObjId="SW-78345_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78344_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="116,-31 116,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b0990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="116,-13 116,3 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="17189@x" ObjectIDND1="17190@x" ObjectIDZND0="g_2e39c10@1" Pin0InfoVect0LinkObjId="g_2e39c10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78344_0" Pin1InfoVect1LinkObjId="SW-78345_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="116,-13 116,3 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3439b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="49,-13 64,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a872d0@0" ObjectIDZND0="17190@0" Pin0InfoVect0LinkObjId="SW-78345_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a872d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="49,-13 64,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3439db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="100,-13 116,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="17190@1" ObjectIDZND0="g_2e39c10@0" ObjectIDZND1="17189@x" Pin0InfoVect0LinkObjId="g_2e39c10_0" Pin0InfoVect1LinkObjId="SW-78344_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78345_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="100,-13 116,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_338a090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="49,-115 64,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33a9240@0" ObjectIDZND0="16818@0" Pin0InfoVect0LinkObjId="SW-77404_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a9240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="49,-115 64,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b02790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="116,-115 116,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16817@x" ObjectIDND1="16818@x" ObjectIDZND0="17189@1" Pin0InfoVect0LinkObjId="SW-78344_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77403_0" Pin1InfoVect1LinkObjId="SW-77404_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="116,-115 116,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_338a3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="100,-115 116,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16818@1" ObjectIDZND0="16817@x" ObjectIDZND1="17189@x" Pin0InfoVect0LinkObjId="SW-77403_0" Pin0InfoVect1LinkObjId="SW-78344_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77404_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="100,-115 116,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_338a5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="116,-115 116,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="17189@x" ObjectIDND1="16818@x" ObjectIDZND0="16817@0" Pin0InfoVect0LinkObjId="SW-77403_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78344_0" Pin1InfoVect1LinkObjId="SW-77404_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="116,-115 116,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a2dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="133,-176 116,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_33e0df0@0" ObjectIDZND0="16815@x" ObjectIDZND1="16817@x" Pin0InfoVect0LinkObjId="SW-77401_0" Pin0InfoVect1LinkObjId="SW-77403_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33e0df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="133,-176 116,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c3cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="116,-191 116,-175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16815@0" ObjectIDZND0="g_33e0df0@0" ObjectIDZND1="16817@x" Pin0InfoVect0LinkObjId="g_33e0df0_0" Pin0InfoVect1LinkObjId="SW-77403_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77401_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="116,-191 116,-175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac8270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="116,-175 116,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_33e0df0@0" ObjectIDND1="16815@x" ObjectIDZND0="16817@1" Pin0InfoVect0LinkObjId="SW-77403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33e0df0_0" Pin1InfoVect1LinkObjId="SW-77401_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="116,-175 116,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33fdd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="285,-264 285,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16808@1" ObjectIDZND0="16797@0" Pin0InfoVect0LinkObjId="g_341dc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77375_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="285,-264 285,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0d2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="285,-228 285,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16808@0" ObjectIDZND0="16807@1" Pin0InfoVect0LinkObjId="SW-77374_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77375_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="285,-228 285,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_340eea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="218,-75 233,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_333be40@0" ObjectIDZND0="16810@0" Pin0InfoVect0LinkObjId="SW-77377_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_333be40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="218,-75 233,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33fe260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="285,-75 285,-8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="16809@x" ObjectIDND1="16810@x" ObjectIDZND0="43340@0" Pin0InfoVect0LinkObjId="SM-CX_TZS.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77376_0" Pin1InfoVect1LinkObjId="SW-77377_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="285,-75 285,-8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33fe450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="269,-75 285,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="16810@1" ObjectIDZND0="16809@x" ObjectIDZND1="43340@x" Pin0InfoVect0LinkObjId="SW-77376_0" Pin0InfoVect1LinkObjId="SM-CX_TZS.P2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="269,-75 285,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33fe640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="285,-75 285,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="generator" EndDevType0="switch" ObjectIDND0="16810@x" ObjectIDND1="43340@x" ObjectIDZND0="16809@0" Pin0InfoVect0LinkObjId="SW-77376_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77377_0" Pin1InfoVect1LinkObjId="SM-CX_TZS.P2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="285,-75 285,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33ad670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="285,-179 285,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16807@0" ObjectIDZND0="g_33fe830@0" ObjectIDZND1="16809@x" Pin0InfoVect0LinkObjId="g_33fe830_0" Pin0InfoVect1LinkObjId="SW-77376_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77374_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="285,-179 285,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3403070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="302,-155 285,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_33fe830@0" ObjectIDZND0="16807@x" ObjectIDZND1="16809@x" Pin0InfoVect0LinkObjId="SW-77374_0" Pin0InfoVect1LinkObjId="SW-77376_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33fe830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="302,-155 285,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3344260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="285,-152 285,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="16807@x" ObjectIDND1="g_33fe830@0" ObjectIDZND0="16809@1" Pin0InfoVect0LinkObjId="SW-77376_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77374_0" Pin1InfoVect1LinkObjId="g_33fe830_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="285,-152 285,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31be190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-263 454,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16804@1" ObjectIDZND0="16797@0" Pin0InfoVect0LinkObjId="g_341dc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77363_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-263 454,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac7550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-227 454,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16804@0" ObjectIDZND0="16803@1" Pin0InfoVect0LinkObjId="SW-77362_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77363_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-227 454,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3415020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="387,-74 402,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33e6640@0" ObjectIDZND0="16806@0" Pin0InfoVect0LinkObjId="SW-77365_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33e6640_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="387,-74 402,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_342fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-74 454,-8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="generator" ObjectIDND0="16805@x" ObjectIDND1="16806@x" ObjectIDZND0="43339@0" Pin0InfoVect0LinkObjId="SM-CX_TZS.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77364_0" Pin1InfoVect1LinkObjId="SW-77365_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-74 454,-8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_342ff80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="438,-74 454,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="generator" ObjectIDND0="16806@1" ObjectIDZND0="16805@x" ObjectIDZND1="43339@x" Pin0InfoVect0LinkObjId="SW-77364_0" Pin0InfoVect1LinkObjId="SM-CX_TZS.P1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77365_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="438,-74 454,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3430170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-74 454,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="generator" EndDevType0="switch" ObjectIDND0="16806@x" ObjectIDND1="43339@x" ObjectIDZND0="16805@0" Pin0InfoVect0LinkObjId="SW-77364_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77365_0" Pin1InfoVect1LinkObjId="SM-CX_TZS.P1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-74 454,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0c450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-178 454,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16803@0" ObjectIDZND0="g_33ee870@0" ObjectIDZND1="16805@x" Pin0InfoVect0LinkObjId="g_33ee870_0" Pin0InfoVect1LinkObjId="SW-77364_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77362_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="454,-178 454,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0c6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="471,-150 454,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_33ee870@0" ObjectIDZND0="16805@x" ObjectIDZND1="16803@x" Pin0InfoVect0LinkObjId="SW-77364_0" Pin0InfoVect1LinkObjId="SW-77362_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33ee870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="471,-150 454,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33eafd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-150 454,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_33ee870@0" ObjectIDND1="16803@x" ObjectIDZND0="16805@1" Pin0InfoVect0LinkObjId="SW-77364_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33ee870_0" Pin1InfoVect1LinkObjId="SW-77362_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-150 454,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b5a250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-422 578,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2b049b0@0" ObjectIDZND0="g_3b59ae0@1" Pin0InfoVect0LinkObjId="g_3b59ae0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b049b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-422 578,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33ceca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-352 541,-352 541,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3b59ae0@0" ObjectIDND1="16798@x" ObjectIDZND0="g_3b5a4b0@0" Pin0InfoVect0LinkObjId="g_3b5a4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3b59ae0_0" Pin1InfoVect1LinkObjId="SW-77341_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-352 541,-352 541,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b2710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-364 578,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3b59ae0@0" ObjectIDZND0="g_3b5a4b0@0" ObjectIDZND1="16798@x" Pin0InfoVect0LinkObjId="g_3b5a4b0_0" Pin0InfoVect1LinkObjId="SW-77341_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b59ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="578,-364 578,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3375880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-1 905,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="16813@x" ObjectIDZND1="16814@x" Pin0InfoVect0LinkObjId="SW-77390_0" Pin0InfoVect1LinkObjId="SW-77391_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b049b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="905,-1 905,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3375ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="905,-72 905,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="16814@x" ObjectIDZND0="16813@0" Pin0InfoVect0LinkObjId="SW-77390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b049b0_0" Pin1InfoVect1LinkObjId="SW-77391_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="905,-72 905,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b1240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="974,-72 961,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33ca660@0" ObjectIDZND0="16814@1" Pin0InfoVect0LinkObjId="SW-77391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33ca660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="974,-72 961,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b14a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-72 905,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="16814@0" ObjectIDZND0="0@x" ObjectIDZND1="16813@x" Pin0InfoVect0LinkObjId="g_2b049b0_0" Pin0InfoVect1LinkObjId="SW-77390_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="925,-72 905,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_365f380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1196,-652 1196,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b049b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1196,-652 1196,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3384d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1195,-572 1195,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3384f90@0" Pin0InfoVect0LinkObjId="g_3384f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b049b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1195,-572 1195,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cc400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="746,-572 746,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_33a76a0@0" ObjectIDND1="16802@x" ObjectIDZND0="16801@1" Pin0InfoVect0LinkObjId="SW-77352_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33a76a0_0" Pin1InfoVect1LinkObjId="SW-77353_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="746,-572 746,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cc660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="746,-509 746,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="16801@0" ObjectIDZND0="g_338d1f0@0" ObjectIDZND1="16799@x" Pin0InfoVect0LinkObjId="g_338d1f0_0" Pin0InfoVect1LinkObjId="SW-77350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77352_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="746,-509 746,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c8e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-289 578,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16797@0" ObjectIDZND0="16798@0" Pin0InfoVect0LinkObjId="SW-77341_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_341dc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-289 578,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c90e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-338 578,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="16798@1" ObjectIDZND0="g_3b5a4b0@0" ObjectIDZND1="g_3b59ae0@0" Pin0InfoVect0LinkObjId="g_3b5a4b0_0" Pin0InfoVect1LinkObjId="g_3b59ae0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77341_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="578,-338 578,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c9340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="746,-684 746,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_33e3110@0" ObjectIDND1="g_33a76a0@0" ObjectIDZND0="18021@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33e3110_0" Pin1InfoVect1LinkObjId="g_33a76a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="746,-684 746,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b8cd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="746,-475 746,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_338d1f0@0" ObjectIDND1="16801@x" ObjectIDZND0="16799@1" Pin0InfoVect0LinkObjId="SW-77350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_338d1f0_0" Pin1InfoVect1LinkObjId="SW-77352_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="746,-475 746,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33e9c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="746,-416 746,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16799@0" ObjectIDZND0="16800@1" Pin0InfoVect0LinkObjId="SW-77351_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="746,-416 746,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33e9eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="746,-352 746,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16800@0" ObjectIDZND0="16797@0" Pin0InfoVect0LinkObjId="g_341dc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77351_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="746,-352 746,-289 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-76697" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -40.500000 -849.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16648" ObjectName="DYN-CX_TZS"/>
     <cge:Meas_Ref ObjectId="76697"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33c5210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 30.000000 -127.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33c64a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 55.000000 -142.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33c6810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 955.000000 341.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33809a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 955.000000 325.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3380ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 947.000000 309.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3381120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 955.000000 357.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3381540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.000000 455.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3381ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 800.000000 440.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3381d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.000000 425.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3382180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 215.000000 -81.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3382440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 204.000000 -96.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3382680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 229.000000 -111.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3382aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 372.000000 -82.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3382d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 -97.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3382fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 386.000000 -112.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_366f1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 835.000000 -117.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_366f9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 824.000000 -132.000000) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_366fc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 847.000000 -147.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_TZS.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 449.000000 13.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43339" ObjectName="SM-CX_TZS.P1"/>
    <cge:TPSR_Ref TObjectID="43339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_TZS.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 280.000000 13.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43340" ObjectName="SM-CX_TZS.P2"/>
    <cge:TPSR_Ref TObjectID="43340"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="16797" cx="116" cy="-289" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16797" cx="905" cy="-289" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16797" cx="285" cy="-289" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16797" cx="454" cy="-289" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16797" cx="578" cy="-289" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="16797" cx="746" cy="-289" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-31 908,-31 905,-39 902,-31 902,-31 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="908,-57 902,-57 905,-48 908,-56 908,-57 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="113,-78 119,-78 116,-86 113,-78 113,-78 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="119,-103 113,-103 116,-94 119,-102 119,-103 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="282,-31 288,-31 285,-39 282,-31 282,-31 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-59 282,-59 285,-50 288,-58 288,-59 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="451,-30 457,-30 454,-38 451,-30 451,-30 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="457,-58 451,-58 454,-49 457,-57 457,-58 " stroke="rgb(255,255,0)"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_333c8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 63.000000 100.000000) translate(0,15)">动态无功补偿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3418670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 946.000000 44.000000) translate(0,15)">1号站用变兼接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33bcb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -376.000000) translate(0,12)">3541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d2280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 74.000000 -256.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b5b130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 76.000000 -212.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 74.000000 -151.000000) translate(0,12)">3513</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3408db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 23.000000 -309.000000) translate(0,12)">35kVIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2acdff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -119.000000) translate(0,12)">3556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afbf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 914.000000 -169.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3723c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -234.000000) translate(0,12)">3551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d1180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 123.000000 -56.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ba880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ac7830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ac7830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ac7830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ac7830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ac7830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ac7830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2ac7830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2a63fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -929.500000) translate(0,16)">天子山开关站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_336c640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 821.000000 32.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2aceb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 668.000000 -939.000000) translate(0,17)">至110kV黄瓜园变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_338f980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 705.500000 -911.000000) translate(0,17)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34306d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 757.000000 -439.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33dac10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 754.000000 -529.000000) translate(0,12)">3546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abcc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 773.000000 -595.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2def2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 758.000000 -840.000000) translate(0,12)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2def2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 758.000000 -840.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2def2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 758.000000 -840.000000) translate(0,42)">天</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2def2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 758.000000 -840.000000) translate(0,57)">子</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2def2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 758.000000 -840.000000) translate(0,72)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2def2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 758.000000 -840.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33efbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 56.000000 -7.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34154a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 56.000000 -109.000000) translate(0,12)">35137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33fdf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -249.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341bdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.000000 -199.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341c010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 243.000000 -122.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_340f0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 225.000000 -69.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31be3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.000000 -248.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac6ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 414.000000 -198.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac7130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.000000 -121.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3415280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 394.000000 -68.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3416300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 248.000000 25.000000) translate(0,12)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b3e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 240.500000 43.000000) translate(0,12)">11~20号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d9c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 255.500000 60.000000) translate(0,12)">10MWp</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31b1700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 933.000000 -96.000000) translate(0,12)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33858b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -853.000000) translate(0,17)">10kV班恺线4号杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33858b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -853.000000) translate(0,38)">  保留施工电源</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33ec1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1231.000000 -619.000000) translate(0,17)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339f170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 415.500000 43.000000) translate(0,12)">1~10号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339f660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.500000 60.000000) translate(0,12)">10MWp</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339f8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.500000 25.000000) translate(0,12)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_339fae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -205.000000 54.000000) translate(0,15)">4772</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339fd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -325.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3671460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -209.000000 27.000000) translate(0,15)">8993767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36719e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -209.000000 -1.000000) translate(0,15)">8993769</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_33fcc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 25.000000 -804.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_33a87d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 819.000000 -563.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a872d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 24.000000 -4.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a9240" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 24.000000 -106.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_333be40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 193.000000 -66.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33e6640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 362.000000 -65.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ca660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 969.000000 -63.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_TZS"/>
</svg>