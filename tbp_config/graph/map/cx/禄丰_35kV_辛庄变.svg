<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-88" aopId="0" height="1492" id="thSvg" product="E8000V2" version="1.0" viewBox="5956 362 1910 1492" width="1910">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape178">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="131" y2="123"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="107"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="9,120 31,98 31,86 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="28" y1="122" y2="122"/>
    <circle cx="30" cy="71" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="31" y1="71" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="27" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="77" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="43" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="18" y2="18"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,49 6,49 6,20 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,6 26,19 39,19 32,6 32,7 32,6 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="34" y2="0"/>
    <circle cx="30" cy="49" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="49" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="29" y1="49" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="49" y2="54"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,19 9,31 17,19 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape21_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape83_0">
    <circle cx="65" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="84,100 90,100 27,37 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="90,100 90,93 " stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="65" x2="7" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="74" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="27" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="21" y2="21"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="65" x2="53" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="65" x2="53" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="65" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="65" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="65" x2="65" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="65" x2="65" y1="74" y2="85"/>
   </symbol>
   <symbol id="transformer2:shape83_1">
    <circle cx="65" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline DF8003:Layer="PUBLIC" points="65,19 58,34 73,34 65,19 65,19 65,19 "/>
   </symbol>
   <symbol id="transformer2:shape84_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="40" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="49" x2="49" y1="61" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 49,57 49,52 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="transformer2:shape84_1">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
   </symbol>
   <symbol id="voltageTransformer:shape61">
    <ellipse cx="8" cy="7" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="19" cy="8" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="18" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="19" cy="18" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="19" x2="19" y1="21" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.103806" x1="22" x2="19" y1="17" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="22" x2="19" y1="20" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="20" x2="18" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="23" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="20" x2="20" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="8" x2="6" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="11" x2="8" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="8" x2="8" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="7" x2="5" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="10" x2="7" y1="17" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="7" x2="7" y1="19" y2="22"/>
   </symbol>
   <symbol id="voltageTransformer:shape62">
    <ellipse cx="11" cy="37" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="0" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="6" y1="23" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="14" y2="6"/>
    <rect height="13" stroke-width="1" width="7" x="8" y="14"/>
    <ellipse cx="22" cy="38" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="11" cy="48" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="22" cy="48" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="9" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="7" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="22" x2="22" y1="51" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.103806" x1="25" x2="22" y1="47" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="22" y1="50" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="23" x2="21" y1="37" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="26" x2="23" y1="35" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="23" x2="23" y1="37" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="11" x2="9" y1="37" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="14" x2="11" y1="35" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="11" x2="11" y1="37" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="10" x2="8" y1="49" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="13" x2="10" y1="47" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="10" x2="10" y1="49" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="31" y2="12"/>
   </symbol>
   <symbol id="voltageTransformer:shape64">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="38" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="8" y1="16" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="17" y1="24" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="10" y1="17" y2="30"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_297ec40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_297fda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2980750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2981420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2982680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29832a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2983d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_29847c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e187c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e187c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2987a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2987a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29892b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29892b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_298a2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_298bf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_298cba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_298da80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_298e360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_298fb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2990640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2990dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2991580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2992660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2992fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2993ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2994490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2995930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29964a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29974c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2998110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a6420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a6ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_299a510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_299b9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1502" width="1920" x="5951" y="357"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="6577" x2="6635" y1="1468" y2="1468"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.859837" x1="6577" x2="6577" y1="1468" y2="1505"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.896552" x1="6826" x2="6878" y1="1407" y2="1407"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.79012" x1="6826" x2="6826" y1="1407" y2="1441"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1.01724" x1="7033" x2="7092" y1="1469" y2="1469"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.906314" x1="7033" x2="7033" y1="1469" y2="1508"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="7234" x2="7292" y1="1469" y2="1469"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.859837" x1="7234" x2="7234" y1="1469" y2="1506"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="7449" x2="7507" y1="1470" y2="1470"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="7449" x2="7449" y1="1470" y2="1517"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-84810">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6920.000000 913.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18628" ObjectName="SW-LF_XZ.LF_XZ_301BK"/>
     <cge:Meas_Ref ObjectId="84810"/>
    <cge:TPSR_Ref TObjectID="18628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85056">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6630.000000 1439.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18650" ObjectName="SW-LF_XZ.LF_XZ_063BK"/>
     <cge:Meas_Ref ObjectId="85056"/>
    <cge:TPSR_Ref TObjectID="18650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84897">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6828.000000 703.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18633" ObjectName="SW-LF_XZ.LF_XZ_361BK"/>
     <cge:Meas_Ref ObjectId="84897"/>
    <cge:TPSR_Ref TObjectID="18633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84928">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7102.000000 702.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18636" ObjectName="SW-LF_XZ.LF_XZ_362BK"/>
     <cge:Meas_Ref ObjectId="84928"/>
    <cge:TPSR_Ref TObjectID="18636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84959">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7424.000000 702.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18639" ObjectName="SW-LF_XZ.LF_XZ_363BK"/>
     <cge:Meas_Ref ObjectId="84959"/>
    <cge:TPSR_Ref TObjectID="18639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84812">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6920.000000 1233.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18630" ObjectName="SW-LF_XZ.LF_XZ_001BK"/>
     <cge:Meas_Ref ObjectId="84812"/>
    <cge:TPSR_Ref TObjectID="18630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85037">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7085.000000 1437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18649" ObjectName="SW-LF_XZ.LF_XZ_062BK"/>
     <cge:Meas_Ref ObjectId="85037"/>
    <cge:TPSR_Ref TObjectID="18649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85014">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7290.000000 1437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18645" ObjectName="SW-LF_XZ.LF_XZ_061BK"/>
     <cge:Meas_Ref ObjectId="85014"/>
    <cge:TPSR_Ref TObjectID="18645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85079">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7502.000000 1437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18654" ObjectName="SW-LF_XZ.LF_XZ_064BK"/>
     <cge:Meas_Ref ObjectId="85079"/>
    <cge:TPSR_Ref TObjectID="18654"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f8e680">
    <use class="BV-35KV" transform="matrix(1.485204 -0.000000 0.000000 -1.804945 7332.000000 1107.642857)" xlink:href="#voltageTransformer:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28fb090">
    <use class="BV-10KV" transform="matrix(1.548387 -0.000000 0.000000 -1.464286 6856.000000 1608.000000)" xlink:href="#voltageTransformer:shape62"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ef920">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6886.000000 484.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f930e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7159.000000 571.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f287a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7482.000000 570.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="LF_XZ" endPointId="0" endStationName="LF_GT" flowDrawDirect="1" flowShape="0" id="AC-35kV.xinguang_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="7433,522 7433,490 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37737" ObjectName="AC-35kV.xinguang_line"/>
    <cge:TPSR_Ref TObjectID="37737_SS-88"/></metadata>
   <polyline fill="none" opacity="0" points="7433,522 7433,490 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="LF_XZ" endPointId="0" endStationName="LF_DX" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_XinDian" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="6838,450 6838,391 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="45007" ObjectName="AC-35kV.LN_XinDian"/>
    <cge:TPSR_Ref TObjectID="45007_SS-88"/></metadata>
   <polyline fill="none" opacity="0" points="6838,450 6838,391 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-LF_XZ.LF_XZ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="26028"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6864.000000 1042.000000)" xlink:href="#transformer2:shape83_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6864.000000 1042.000000)" xlink:href="#transformer2:shape83_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="18658" ObjectName="TF-LF_XZ.LF_XZ_1T"/>
    <cge:TPSR_Ref TObjectID="18658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7697.000000 1626.000000)" xlink:href="#transformer2:shape84_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7697.000000 1626.000000)" xlink:href="#transformer2:shape84_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_32b6e10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7344.000000 1023.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cad5f0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 7391.500000 969.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_300bca0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 6659.500000 1650.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cef050">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 6659.500000 1523.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3009a20">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 6903.500000 1463.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_303ee50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6873.000000 1508.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3225a30">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 7116.500000 1648.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3028630">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 7116.500000 1521.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f4a540">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 7317.500000 1648.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f4d5e0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 7317.500000 1520.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_303d2a0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 7531.500000 1657.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32a32b0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 7531.500000 1530.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32c1430">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7702.000000 1465.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f251a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6757.000000 503.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b96ca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7031.000000 589.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc18d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7352.000000 588.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd39b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6843.000000 1130.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f8d7b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6919.000000 1110.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30061d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7701.000000 1515.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3035790">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6843.000000 1057.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_303c3a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6629.000000 1574.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_303cd70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7084.000000 1574.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f95d40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7289.000000 1574.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_304cb10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7501.000000 1578.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32d5670">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6743.000000 689.000000)" xlink:href="#lightningRod:shape178"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 6074.000000 527.486486) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 6083.538462 666.033638) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-84802" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 6081.538462 590.033638) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84802" ObjectName="LF_XZ:LF_XZ_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-84802" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 6081.538462 628.033638) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84802" ObjectName="LF_XZ:LF_XZ_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-84809" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7104.000000 985.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84809" ObjectName="LF_XZ:LF_XZ_1T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-86571" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7104.000000 1006.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="86571" ObjectName="LF_XZ:LF_XZ_1T_Tmp"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84794" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6642.000000 1794.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84794" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18650"/>
     <cge:Term_Ref ObjectID="26010"/>
    <cge:TPSR_Ref TObjectID="18650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84795" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6642.000000 1794.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84795" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18650"/>
     <cge:Term_Ref ObjectID="26010"/>
    <cge:TPSR_Ref TObjectID="18650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84793" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6642.000000 1794.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84793" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18650"/>
     <cge:Term_Ref ObjectID="26010"/>
    <cge:TPSR_Ref TObjectID="18650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84790" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7043.000000 1764.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18649"/>
     <cge:Term_Ref ObjectID="26008"/>
    <cge:TPSR_Ref TObjectID="18649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84791" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7043.000000 1764.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84791" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18649"/>
     <cge:Term_Ref ObjectID="26008"/>
    <cge:TPSR_Ref TObjectID="18649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84789" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7043.000000 1764.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18649"/>
     <cge:Term_Ref ObjectID="26008"/>
    <cge:TPSR_Ref TObjectID="18649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84786" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7303.000000 1764.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18645"/>
     <cge:Term_Ref ObjectID="26000"/>
    <cge:TPSR_Ref TObjectID="18645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84787" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7303.000000 1764.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18645"/>
     <cge:Term_Ref ObjectID="26000"/>
    <cge:TPSR_Ref TObjectID="18645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84785" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7303.000000 1764.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18645"/>
     <cge:Term_Ref ObjectID="26000"/>
    <cge:TPSR_Ref TObjectID="18645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84798" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7534.000000 1762.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18654"/>
     <cge:Term_Ref ObjectID="26018"/>
    <cge:TPSR_Ref TObjectID="18654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84799" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7534.000000 1762.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84799" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18654"/>
     <cge:Term_Ref ObjectID="26018"/>
    <cge:TPSR_Ref TObjectID="18654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84797" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7534.000000 1762.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84797" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18654"/>
     <cge:Term_Ref ObjectID="26018"/>
    <cge:TPSR_Ref TObjectID="18654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84802" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7074.000000 853.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18628"/>
     <cge:Term_Ref ObjectID="25966"/>
    <cge:TPSR_Ref TObjectID="18628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84803" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7074.000000 853.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84803" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18628"/>
     <cge:Term_Ref ObjectID="25966"/>
    <cge:TPSR_Ref TObjectID="18628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7074.000000 853.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18628"/>
     <cge:Term_Ref ObjectID="25966"/>
    <cge:TPSR_Ref TObjectID="18628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7249.000000 650.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18636"/>
     <cge:Term_Ref ObjectID="25982"/>
    <cge:TPSR_Ref TObjectID="18636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84779" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7249.000000 650.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84779" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18636"/>
     <cge:Term_Ref ObjectID="25982"/>
    <cge:TPSR_Ref TObjectID="18636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7249.000000 650.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18636"/>
     <cge:Term_Ref ObjectID="25982"/>
    <cge:TPSR_Ref TObjectID="18636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7571.000000 653.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18639"/>
     <cge:Term_Ref ObjectID="25988"/>
    <cge:TPSR_Ref TObjectID="18639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84783" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7571.000000 653.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84783" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18639"/>
     <cge:Term_Ref ObjectID="25988"/>
    <cge:TPSR_Ref TObjectID="18639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7571.000000 653.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18639"/>
     <cge:Term_Ref ObjectID="25988"/>
    <cge:TPSR_Ref TObjectID="18639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84806" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7082.000000 1181.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84806" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18630"/>
     <cge:Term_Ref ObjectID="25970"/>
    <cge:TPSR_Ref TObjectID="18630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84807" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7082.000000 1181.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84807" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18630"/>
     <cge:Term_Ref ObjectID="25970"/>
    <cge:TPSR_Ref TObjectID="18630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84805" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7082.000000 1181.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84805" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18630"/>
     <cge:Term_Ref ObjectID="25970"/>
    <cge:TPSR_Ref TObjectID="18630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-86579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6633.000000 664.000000) translate(0,16)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="86579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18929"/>
     <cge:Term_Ref ObjectID="26317"/>
    <cge:TPSR_Ref TObjectID="18929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-86580" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6633.000000 664.000000) translate(0,36)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="86580" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18929"/>
     <cge:Term_Ref ObjectID="26317"/>
    <cge:TPSR_Ref TObjectID="18929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-86581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6633.000000 664.000000) translate(0,56)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="86581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18929"/>
     <cge:Term_Ref ObjectID="26317"/>
    <cge:TPSR_Ref TObjectID="18929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-86582" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6633.000000 664.000000) translate(0,76)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="86582" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18929"/>
     <cge:Term_Ref ObjectID="26317"/>
    <cge:TPSR_Ref TObjectID="18929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-86583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6633.000000 664.000000) translate(0,96)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="86583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18929"/>
     <cge:Term_Ref ObjectID="26317"/>
    <cge:TPSR_Ref TObjectID="18929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-86572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6545.000000 1241.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="86572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18928"/>
     <cge:Term_Ref ObjectID="26316"/>
    <cge:TPSR_Ref TObjectID="18928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-86573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6545.000000 1241.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="86573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18928"/>
     <cge:Term_Ref ObjectID="26316"/>
    <cge:TPSR_Ref TObjectID="18928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-86574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6545.000000 1241.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="86574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18928"/>
     <cge:Term_Ref ObjectID="26316"/>
    <cge:TPSR_Ref TObjectID="18928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-86575" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6545.000000 1241.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="86575" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18928"/>
     <cge:Term_Ref ObjectID="26316"/>
    <cge:TPSR_Ref TObjectID="18928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-86576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6545.000000 1241.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="86576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18928"/>
     <cge:Term_Ref ObjectID="26316"/>
    <cge:TPSR_Ref TObjectID="18928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6978.000000 651.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18633"/>
     <cge:Term_Ref ObjectID="25976"/>
    <cge:TPSR_Ref TObjectID="18633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84775" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6978.000000 651.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84775" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18633"/>
     <cge:Term_Ref ObjectID="25976"/>
    <cge:TPSR_Ref TObjectID="18633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84773" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6978.000000 651.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18633"/>
     <cge:Term_Ref ObjectID="25976"/>
    <cge:TPSR_Ref TObjectID="18633"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="6086" y="469"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="6086" y="469"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="6037" y="452"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="6037" y="452"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="6326" y="495"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="6326" y="495"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="6326" y="460"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="6326" y="460"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="6846" y="673"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="6846" y="673"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="7127" y="671"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="7127" y="671"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="7447" y="671"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="7447" y="671"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="7526" y="1408"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="7526" y="1408"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="7318" y="1408"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="7318" y="1408"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="7111" y="1408"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="7111" y="1408"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="6659" y="1408"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="6659" y="1408"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="72" x="6018" y="822"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="72" x="6018" y="822"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="6958" y="983"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="6958" y="983"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="6240" y="471"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="6240" y="471"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="6086" y="469"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="6037" y="452"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="6326" y="495"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="6326" y="460"/></g>
   <g href="35kV辛庄变LF_XZ_361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="6846" y="673"/></g>
   <g href="35kV辛庄变LF_XZ_362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="7127" y="671"/></g>
   <g href="35kV辛庄变LF_XZ_363间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="7447" y="671"/></g>
   <g href="35kV辛庄变LF_XZ_064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="7526" y="1408"/></g>
   <g href="35kV辛庄变LF_XZ_061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="7318" y="1408"/></g>
   <g href="35kV辛庄变LF_XZ_062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="7111" y="1408"/></g>
   <g href="35kV辛庄变LF_XZ_063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="6659" y="1408"/></g>
   <g href="35kV辛庄变LF_XZ_GG间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="72" x="6018" y="822"/></g>
   <g href="35kV辛庄变1号主变主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="6958" y="983"/></g>
   <g href="AVC辛庄站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="6240" y="471"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="5957" y="1049"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="5957" y="449"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="6241" y="473"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_XZ.LF_XZ_35IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6684,767 7538,767 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18929" ObjectName="BS-LF_XZ.LF_XZ_35IM"/>
    <cge:TPSR_Ref TObjectID="18929"/></metadata>
   <polyline fill="none" opacity="0" points="6684,767 7538,767 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_XZ.LF_XZ_10IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6508,1329 7866,1329 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18928" ObjectName="BS-LF_XZ.LF_XZ_10IM"/>
    <cge:TPSR_Ref TObjectID="18928"/></metadata>
   <polyline fill="none" opacity="0" points="6508,1329 7866,1329 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f4a250" refnum="0">
    <use class="BV-0KV" transform="matrix(1.750000 -0.000000 0.000000 -1.826087 7258.000000 916.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e26830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.750000 -0.000000 0.000000 -1.826087 7257.000000 1071.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25065b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.750000 -0.000000 0.000000 -1.826087 6575.000000 1753.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3023610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.750000 -0.000000 0.000000 -1.826087 7032.000000 1751.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32d6d10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.750000 -0.000000 0.000000 -1.826087 7233.000000 1751.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f88d70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.750000 -0.000000 0.000000 -1.826087 7447.000000 1760.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fa2420" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6802.000000 1447.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3022d90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6552.000000 1511.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fb2160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7005.000000 1514.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fe15f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7208.000000 1512.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ba9db0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7423.000000 1528.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="18929" cx="6837" cy="767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18929" cx="7111" cy="767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18929" cx="7433" cy="767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18929" cx="7353" cy="767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18929" cx="6929" cy="767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18928" cx="7711" cy="1329" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18928" cx="7094" cy="1329" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18928" cx="7299" cy="1329" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18928" cx="7511" cy="1329" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18928" cx="6882" cy="1329" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18928" cx="6639" cy="1329" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_300f120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6754.000000 750.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32cfd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6490.000000 1346.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ef1ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6938.000000 883.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f63ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6937.000000 818.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2f75890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5978.000000 1070.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2903440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5970.000000 591.000000) translate(0,17)">下网有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2903440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5970.000000 591.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2903440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5970.000000 591.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2903440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5970.000000 591.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2903440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5970.000000 591.000000) translate(0,101)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2903440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5970.000000 591.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2903440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5970.000000 591.000000) translate(0,143)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2903440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5970.000000 591.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2903440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5970.000000 591.000000) translate(0,185)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2ff6900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6115.000000 479.500000) translate(0,16)">辛庄变电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2fb8980" transform="matrix(1.000000 0.000000 -0.000000 1.000000 6789.000000 362.000000) translate(0,15)">35kV辛甸线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcf100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6956.000000 1043.000000) translate(0,12)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcf100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6956.000000 1043.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2e6df00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6076.000000 1410.000000) translate(0,16)">15758580335</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3048040" transform="matrix(1.000000 0.000000 -0.000000 1.000000 7064.000000 439.000000) translate(0,15)">35kV大辛线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ad0590" transform="matrix(1.000000 0.000000 -0.000000 1.000000 7386.000000 439.000000) translate(0,15)">35kV辛广线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f7aa00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6847.500000 606.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_302cf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6848.000000 720.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3038ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7122.500000 614.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fc7660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7123.000000 728.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fcf270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7446.500000 609.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fcb2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7453.000000 725.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b9bd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7368.000000 891.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e87620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7208.000000 832.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6a1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7208.000000 990.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32a8890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6650.000000 1710.000000) translate(0,16)">西王庙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32acaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7113.000000 1708.000000) translate(0,16)">发裱线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2ee7da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7316.000000 1708.000000) translate(0,16)">辛庄线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1b2a150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7524.000000 1700.500000) translate(0,16)">滇中引水大瓦房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1b2a150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7524.000000 1700.500000) translate(0,36)">施工专线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1ad1f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7689.000000 1686.000000) translate(0,16)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2f69120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6781.000000 1615.500000) translate(0,16)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_304e3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6534.000000 1659.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b1f490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6986.000000 1657.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ff480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7186.000000 1657.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28ff6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7401.000000 1666.000000) translate(0,12)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f49840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6655.000000 1457.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30309c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7106.000000 1462.000000) translate(0,12)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3030c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7315.000000 1466.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f32a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7527.000000 1466.000000) translate(0,12)">0642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f25e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6898.000000 1392.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f32cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6828.000000 1416.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_245fb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6946.000000 1202.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c6a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6584.000000 1480.000000) translate(0,12)">06327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c6ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6651.000000 1604.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c6eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7047.000000 1480.000000) translate(0,12)">06227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf9760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7111.000000 1599.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf99a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7243.000000 1478.000000) translate(0,12)">06127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf9be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7310.000000 1595.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6aab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7530.000000 1602.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25042d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 7294.000000 1120.000000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_302c600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6587.000000 1413.000000) translate(0,12)">400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_302c830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7048.000000 1407.000000) translate(0,12)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bfd430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7239.000000 1409.000000) translate(0,12)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bfd670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7459.000000 1409.000000) translate(0,12)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1bfd7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6337.000000 503.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3228d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6337.000000 468.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3229000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6846.000000 673.000000) translate(0,15)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3012390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7127.000000 671.000000) translate(0,15)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30418e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7447.000000 671.000000) translate(0,15)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3041d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7318.000000 1408.000000) translate(0,15)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f70260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7111.000000 1408.000000) translate(0,15)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f704a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6659.000000 1408.000000) translate(0,15)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f706e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7526.000000 1408.000000) translate(0,15)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2fa0b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6018.000000 822.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f17000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6958.000000 983.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ee110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6936.000000 1147.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f8c500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6936.000000 1269.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dbdf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6646.000000 1358.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f86140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7103.000000 1357.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f86350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7304.000000 1356.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f86590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7518.000000 1354.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f867d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7718.000000 1378.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fa5f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 7458.000000 1496.000000) translate(0,12)">06427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ff6d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5961.000000 1467.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ff6d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5961.000000 1467.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_299e860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6088.000000 1456.500000) translate(0,16)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_299e860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6088.000000 1456.500000) translate(0,36)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_299e860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6088.000000 1456.500000) translate(0,56)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_24d4fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6255.000000 483.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32d5180" transform="matrix(1.000000 0.000000 -0.000000 1.000000 6735.000000 703.000000) translate(0,15)">2号站用变</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f87a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6913.000000 -651.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3013140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6902.000000 -666.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3013cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6927.000000 -681.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb1740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7187.000000 -656.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb19d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7176.000000 -671.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb1be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7201.000000 -686.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb5af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7512.000000 -659.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb5db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7501.000000 -674.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb5ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7526.000000 -689.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb6410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7012.000000 -861.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fb66d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7001.000000 -876.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3017fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7026.000000 -891.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3018300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6544.000000 -726.000000) translate(0,14)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_2f48980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6536.000000 -702.000000) translate(0,14)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_24dc450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6535.000000 -685.000000) translate(0,14)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_24dc9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6535.000000 -664.000000) translate(0,14)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_24dcf60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6526.000000 -744.000000) translate(0,14)">Uab（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30399c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7020.000000 -1189.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3039c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7009.000000 -1204.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3039e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7034.000000 -1219.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_303a1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6445.000000 -1270.000000) translate(0,14)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_303a420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6445.000000 -1254.000000) translate(0,14)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_303a660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6446.000000 -1237.000000) translate(0,14)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_303a8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6436.000000 -1300.000000) translate(0,14)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_2cf0460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6453.000000 -1285.000000) translate(0,14)">U0（V）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf0760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6561.000000 -1816.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf09c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6572.000000 -1796.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf0c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6586.000000 -1836.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf0f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6967.000000 -1790.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cf1190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6978.000000 -1770.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe2860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6992.000000 -1810.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe2b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7225.000000 -1788.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe2df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7236.000000 -1768.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe3030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7250.000000 -1808.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe3360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7463.000000 -1787.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fe35c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7474.000000 -1767.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d4c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7488.000000 -1807.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d57d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7027.000000 -1002.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f26ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7027.000000 -987.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_XZ.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6630.000000 1788.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37699" ObjectName="EC-LF_XZ.063Ld"/>
    <cge:TPSR_Ref TObjectID="37699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_XZ.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7085.000000 1755.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37696" ObjectName="EC-LF_XZ.062Ld"/>
    <cge:TPSR_Ref TObjectID="37696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_XZ.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7290.000000 1758.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37697" ObjectName="EC-LF_XZ.061Ld"/>
    <cge:TPSR_Ref TObjectID="37697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_XZ.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7502.000000 1759.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37698" ObjectName="EC-LF_XZ.064Ld"/>
    <cge:TPSR_Ref TObjectID="37698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_XZ.362Ld">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7102.000000 508.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41858" ObjectName="EC-LF_XZ.362Ld"/>
    <cge:TPSR_Ref TObjectID="41858"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-52537" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6255.000000 573.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9247" ObjectName="DYN-LF_XZ"/>
     <cge:Meas_Ref ObjectId="52537"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-84811">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 6914.000000 865.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18629" ObjectName="SW-LF_XZ.LF_XZ_3011SW"/>
     <cge:Meas_Ref ObjectId="84811"/>
    <cge:TPSR_Ref TObjectID="18629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84898">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 6822.000000 772.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18634" ObjectName="SW-LF_XZ.LF_XZ_3611SW"/>
     <cge:Meas_Ref ObjectId="84898"/>
    <cge:TPSR_Ref TObjectID="18634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84899">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 6822.000000 658.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18635" ObjectName="SW-LF_XZ.LF_XZ_3616SW"/>
     <cge:Meas_Ref ObjectId="84899"/>
    <cge:TPSR_Ref TObjectID="18635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84929">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 7096.000000 771.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18637" ObjectName="SW-LF_XZ.LF_XZ_3621SW"/>
     <cge:Meas_Ref ObjectId="84929"/>
    <cge:TPSR_Ref TObjectID="18637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84930">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 7096.000000 657.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18638" ObjectName="SW-LF_XZ.LF_XZ_3626SW"/>
     <cge:Meas_Ref ObjectId="84930"/>
    <cge:TPSR_Ref TObjectID="18638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84960">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 7418.000000 771.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18640" ObjectName="SW-LF_XZ.LF_XZ_3631SW"/>
     <cge:Meas_Ref ObjectId="84960"/>
    <cge:TPSR_Ref TObjectID="18640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84961">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 7418.000000 657.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18641" ObjectName="SW-LF_XZ.LF_XZ_3636SW"/>
     <cge:Meas_Ref ObjectId="84961"/>
    <cge:TPSR_Ref TObjectID="18641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84990">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7338.000000 934.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18642" ObjectName="SW-LF_XZ.LF_XZ_3901SW"/>
     <cge:Meas_Ref ObjectId="84990"/>
    <cge:TPSR_Ref TObjectID="18642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84991">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 7253.000000 883.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18643" ObjectName="SW-LF_XZ.LF_XZ_39010SW"/>
     <cge:Meas_Ref ObjectId="84991"/>
    <cge:TPSR_Ref TObjectID="18643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84992">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 7252.000000 1038.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18644" ObjectName="SW-LF_XZ.LF_XZ_39017SW"/>
     <cge:Meas_Ref ObjectId="84992"/>
    <cge:TPSR_Ref TObjectID="18644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-86562">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 6624.000000 1643.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18922" ObjectName="SW-LF_XZ.LF_XZ_0636SW"/>
     <cge:Meas_Ref ObjectId="86562"/>
    <cge:TPSR_Ref TObjectID="18922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-86563">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6570.000000 1712.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18921" ObjectName="SW-LF_XZ.LF_XZ_06367SW"/>
     <cge:Meas_Ref ObjectId="86563"/>
    <cge:TPSR_Ref TObjectID="18921"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-86564">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 7079.000000 1641.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18919" ObjectName="SW-LF_XZ.LF_XZ_0626SW"/>
     <cge:Meas_Ref ObjectId="86564"/>
    <cge:TPSR_Ref TObjectID="18919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-86570">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7027.000000 1710.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18920" ObjectName="SW-LF_XZ.LF_XZ_06267SW"/>
     <cge:Meas_Ref ObjectId="86570"/>
    <cge:TPSR_Ref TObjectID="18920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-86560">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 7284.000000 1641.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18917" ObjectName="SW-LF_XZ.LF_XZ_0616SW"/>
     <cge:Meas_Ref ObjectId="86560"/>
    <cge:TPSR_Ref TObjectID="18917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-86561">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7228.000000 1710.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18918" ObjectName="SW-LF_XZ.LF_XZ_06167SW"/>
     <cge:Meas_Ref ObjectId="86561"/>
    <cge:TPSR_Ref TObjectID="18918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-86565">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 7496.000000 1650.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18924" ObjectName="SW-LF_XZ.LF_XZ_0646SW"/>
     <cge:Meas_Ref ObjectId="86565"/>
    <cge:TPSR_Ref TObjectID="18924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-86566">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7442.000000 1719.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18923" ObjectName="SW-LF_XZ.LF_XZ_06467SW"/>
     <cge:Meas_Ref ObjectId="86566"/>
    <cge:TPSR_Ref TObjectID="18923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-86569">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6829.000000 1446.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18927" ObjectName="SW-LF_XZ.LF_XZ_09017SW"/>
     <cge:Meas_Ref ObjectId="86569"/>
    <cge:TPSR_Ref TObjectID="18927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85059">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6581.000000 1510.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18653" ObjectName="SW-LF_XZ.LF_XZ_06327SW"/>
     <cge:Meas_Ref ObjectId="85059"/>
    <cge:TPSR_Ref TObjectID="18653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-86586">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7039.000000 1513.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18930" ObjectName="SW-LF_XZ.LF_XZ_06227SW"/>
     <cge:Meas_Ref ObjectId="86586"/>
    <cge:TPSR_Ref TObjectID="18930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85017">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7244.000000 1511.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18648" ObjectName="SW-LF_XZ.LF_XZ_06127SW"/>
     <cge:Meas_Ref ObjectId="85017"/>
    <cge:TPSR_Ref TObjectID="18648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-233738">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7454.000000 1527.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38943" ObjectName="SW-LF_XZ.LF_XZ_06427SW"/>
     <cge:Meas_Ref ObjectId="233738"/>
    <cge:TPSR_Ref TObjectID="38943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-86567">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 6867.000000 1446.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18926" ObjectName="SW-LF_XZ.LF_XZ_0901SW"/>
     <cge:Meas_Ref ObjectId="86567"/>
    <cge:TPSR_Ref TObjectID="18926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85058">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 6624.000000 1507.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18652" ObjectName="SW-LF_XZ.LF_XZ_0632SW"/>
     <cge:Meas_Ref ObjectId="85058"/>
    <cge:TPSR_Ref TObjectID="18652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85102">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 7079.000000 1508.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18660" ObjectName="SW-LF_XZ.LF_XZ_0622SW"/>
     <cge:Meas_Ref ObjectId="85102"/>
    <cge:TPSR_Ref TObjectID="18660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85016">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 7284.000000 1510.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18647" ObjectName="SW-LF_XZ.LF_XZ_0612SW"/>
     <cge:Meas_Ref ObjectId="85016"/>
    <cge:TPSR_Ref TObjectID="18647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85081">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 7496.000000 1510.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18656" ObjectName="SW-LF_XZ.LF_XZ_0642SW"/>
     <cge:Meas_Ref ObjectId="85081"/>
    <cge:TPSR_Ref TObjectID="18656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85057">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6603.000000 1389.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18651" ObjectName="SW-LF_XZ.LF_XZ_0631SW"/>
     <cge:Meas_Ref ObjectId="85057"/>
    <cge:TPSR_Ref TObjectID="18651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84813">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6893.000000 1178.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18631" ObjectName="SW-LF_XZ.LF_XZ_0016SW"/>
     <cge:Meas_Ref ObjectId="84813"/>
    <cge:TPSR_Ref TObjectID="18631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84814">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6893.000000 1300.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18632" ObjectName="SW-LF_XZ.LF_XZ_0011SW"/>
     <cge:Meas_Ref ObjectId="84814"/>
    <cge:TPSR_Ref TObjectID="18632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85101">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7058.000000 1388.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18659" ObjectName="SW-LF_XZ.LF_XZ_0621SW"/>
     <cge:Meas_Ref ObjectId="85101"/>
    <cge:TPSR_Ref TObjectID="18659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85015">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7263.000000 1387.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18646" ObjectName="SW-LF_XZ.LF_XZ_0611SW"/>
     <cge:Meas_Ref ObjectId="85015"/>
    <cge:TPSR_Ref TObjectID="18646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-85080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7475.000000 1385.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18655" ObjectName="SW-LF_XZ.LF_XZ_0641SW"/>
     <cge:Meas_Ref ObjectId="85080"/>
    <cge:TPSR_Ref TObjectID="18655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-86568">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7675.000000 1409.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18925" ObjectName="SW-LF_XZ.LF_XZ_0651SW"/>
     <cge:Meas_Ref ObjectId="86568"/>
    <cge:TPSR_Ref TObjectID="18925"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3009d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6929,807 6929,767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18629@1" ObjectIDZND0="18929@0" Pin0InfoVect0LinkObjId="g_304b500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84811_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6929,807 6929,767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fae710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6837,636 6837,667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18635@0" ObjectIDZND0="18633@1" Pin0InfoVect0LinkObjId="SW-84897_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84899_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6837,636 6837,667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fad1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6837,694 6837,714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18633@0" ObjectIDZND0="18634@1" Pin0InfoVect0LinkObjId="SW-84898_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84897_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6837,694 6837,714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_304b500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6837,750 6837,767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18634@0" ObjectIDZND0="18929@0" Pin0InfoVect0LinkObjId="g_3009d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84898_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6837,750 6837,767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b83330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7111,635 7111,666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18638@0" ObjectIDZND0="18636@1" Pin0InfoVect0LinkObjId="SW-84928_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7111,635 7111,666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2464de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7111,693 7111,713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18636@0" ObjectIDZND0="18637@1" Pin0InfoVect0LinkObjId="SW-84929_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84928_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7111,693 7111,713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fa1f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7111,749 7111,767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18637@0" ObjectIDZND0="18929@0" Pin0InfoVect0LinkObjId="g_3009d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84929_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7111,749 7111,767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fe22a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7433,635 7433,666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18641@0" ObjectIDZND0="18639@1" Pin0InfoVect0LinkObjId="SW-84959_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84961_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7433,635 7433,666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8a8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7433,693 7433,713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18639@0" ObjectIDZND0="18640@1" Pin0InfoVect0LinkObjId="SW-84960_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84959_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7433,693 7433,713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fe80d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7433,749 7433,767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18640@0" ObjectIDZND0="18929@0" Pin0InfoVect0LinkObjId="g_3009d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7433,749 7433,767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24f1b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7353,876 7353,807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="18642@1" ObjectIDZND0="18929@0" ObjectIDZND1="18643@x" Pin0InfoVect0LinkObjId="g_3009d70_0" Pin0InfoVect1LinkObjId="SW-84991_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84990_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="7353,876 7353,807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bae7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7353,807 7353,767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="18642@x" ObjectIDND1="18643@x" ObjectIDZND0="18929@0" Pin0InfoVect0LinkObjId="g_3009d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-84990_0" Pin1InfoVect1LinkObjId="SW-84991_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7353,807 7353,767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3022450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7268,883 7268,861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2f4a250@0" ObjectIDZND0="18643@0" Pin0InfoVect0LinkObjId="SW-84991_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f4a250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7268,883 7268,861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8eb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7268,825 7268,807 7353,807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="18643@1" ObjectIDZND0="18929@0" ObjectIDZND1="18642@x" Pin0InfoVect0LinkObjId="g_3009d70_0" Pin0InfoVect1LinkObjId="SW-84990_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84991_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="7268,825 7268,807 7353,807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1adeaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7267,1038 7267,1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1e26830@0" ObjectIDZND0="18644@0" Pin0InfoVect0LinkObjId="SW-84992_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e26830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7267,1038 7267,1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32c8350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7268,980 7268,962 7353,962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="18644@1" ObjectIDZND0="18642@x" ObjectIDZND1="g_32b6e10@0" ObjectIDZND2="g_1cad5f0@0" Pin0InfoVect0LinkObjId="SW-84990_0" Pin0InfoVect1LinkObjId="g_32b6e10_0" Pin0InfoVect2LinkObjId="g_1cad5f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84992_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="7268,980 7268,962 7353,962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f6e1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7353,911 7353,962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="18642@0" ObjectIDZND0="18644@x" ObjectIDZND1="g_32b6e10@0" ObjectIDZND2="g_1cad5f0@0" Pin0InfoVect0LinkObjId="SW-84992_0" Pin0InfoVect1LinkObjId="g_32b6e10_0" Pin0InfoVect2LinkObjId="g_1cad5f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="7353,911 7353,962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b7e9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7353,962 7353,987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18644@x" ObjectIDND1="18642@x" ObjectIDND2="g_1cad5f0@0" ObjectIDZND0="g_32b6e10@1" Pin0InfoVect0LinkObjId="g_32b6e10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-84992_0" Pin1InfoVect1LinkObjId="SW-84990_0" Pin1InfoVect2LinkObjId="g_1cad5f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7353,962 7353,987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b7bec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7353,1018 7352,1065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_32b6e10@0" ObjectIDZND0="g_2f8e680@0" Pin0InfoVect0LinkObjId="g_2f8e680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b6e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7353,1018 7352,1065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fe4e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7353,962 7397,962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18644@x" ObjectIDND1="18642@x" ObjectIDND2="g_32b6e10@0" ObjectIDZND0="g_1cad5f0@0" Pin0InfoVect0LinkObjId="g_1cad5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-84992_0" Pin1InfoVect1LinkObjId="SW-84990_0" Pin1InfoVect2LinkObjId="g_32b6e10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7353,962 7397,962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b5d7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1621 6639,1643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="18922@0" ObjectIDZND0="g_300bca0@0" ObjectIDZND1="18921@x" ObjectIDZND2="37699@x" Pin0InfoVect0LinkObjId="g_300bca0_0" Pin0InfoVect1LinkObjId="SW-86563_0" Pin0InfoVect2LinkObjId="EC-LF_XZ.063Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-86562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1621 6639,1643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fa4580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1643 6639,1761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="18922@x" ObjectIDND1="g_300bca0@0" ObjectIDND2="18921@x" ObjectIDZND0="37699@0" Pin0InfoVect0LinkObjId="EC-LF_XZ.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86562_0" Pin1InfoVect1LinkObjId="g_300bca0_0" Pin1InfoVect2LinkObjId="SW-86563_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1643 6639,1761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30782f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1643 6665,1643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="18922@x" ObjectIDND1="18921@x" ObjectIDND2="37699@x" ObjectIDZND0="g_300bca0@0" Pin0InfoVect0LinkObjId="g_300bca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86562_0" Pin1InfoVect1LinkObjId="SW-86563_0" Pin1InfoVect2LinkObjId="EC-LF_XZ.063Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1643 6665,1643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e8100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1643 6585,1643 6585,1654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="18922@x" ObjectIDND1="g_300bca0@0" ObjectIDND2="37699@x" ObjectIDZND0="18921@1" Pin0InfoVect0LinkObjId="SW-86563_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86562_0" Pin1InfoVect1LinkObjId="g_300bca0_0" Pin1InfoVect2LinkObjId="EC-LF_XZ.063Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1643 6585,1643 6585,1654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ea280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6585,1689 6585,1720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18921@0" ObjectIDZND0="g_25065b0@0" Pin0InfoVect0LinkObjId="g_25065b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-86563_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6585,1689 6585,1720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ad9aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1516 6665,1516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18653@x" ObjectIDND1="18652@x" ObjectIDND2="g_303c3a0@0" ObjectIDZND0="g_2cef050@0" Pin0InfoVect0LinkObjId="g_2cef050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-85059_0" Pin1InfoVect1LinkObjId="SW-85058_0" Pin1InfoVect2LinkObjId="g_303c3a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1516 6665,1516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f83910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6883,1456 6909,1456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_303ee50@0" ObjectIDND1="18926@x" ObjectIDND2="18927@x" ObjectIDZND0="g_3009a20@0" Pin0InfoVect0LinkObjId="g_3009a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_303ee50_0" Pin1InfoVect1LinkObjId="SW-86567_0" Pin1InfoVect2LinkObjId="SW-86569_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6883,1456 6909,1456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fcd4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6882,1456 6882,1472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18926@x" ObjectIDND1="18927@x" ObjectIDND2="g_3009a20@0" ObjectIDZND0="g_303ee50@1" Pin0InfoVect0LinkObjId="g_303ee50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86567_0" Pin1InfoVect1LinkObjId="SW-86569_0" Pin1InfoVect2LinkObjId="g_3009a20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6882,1456 6882,1472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fa440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6882,1503 6882,1529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_303ee50@0" ObjectIDZND0="g_28fb090@0" Pin0InfoVect0LinkObjId="g_28fb090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_303ee50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6882,1503 6882,1529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2faf660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7094,1619 7094,1641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="18919@0" ObjectIDZND0="18920@x" ObjectIDZND1="g_3225a30@0" ObjectIDZND2="37696@x" Pin0InfoVect0LinkObjId="SW-86570_0" Pin0InfoVect1LinkObjId="g_3225a30_0" Pin0InfoVect2LinkObjId="EC-LF_XZ.062Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-86564_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="7094,1619 7094,1641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_303aa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7094,1641 7094,1728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="18919@x" ObjectIDND1="18920@x" ObjectIDND2="g_3225a30@0" ObjectIDZND0="37696@0" Pin0InfoVect0LinkObjId="EC-LF_XZ.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86564_0" Pin1InfoVect1LinkObjId="SW-86570_0" Pin1InfoVect2LinkObjId="g_3225a30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7094,1641 7094,1728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bfc690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7094,1641 7122,1641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="18919@x" ObjectIDND1="18920@x" ObjectIDND2="37696@x" ObjectIDZND0="g_3225a30@0" Pin0InfoVect0LinkObjId="g_3225a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86564_0" Pin1InfoVect1LinkObjId="SW-86570_0" Pin1InfoVect2LinkObjId="EC-LF_XZ.062Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7094,1641 7122,1641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_304d450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7094,1641 7042,1641 7042,1652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="18919@x" ObjectIDND1="g_3225a30@0" ObjectIDND2="37696@x" ObjectIDZND0="18920@1" Pin0InfoVect0LinkObjId="SW-86570_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86564_0" Pin1InfoVect1LinkObjId="g_3225a30_0" Pin1InfoVect2LinkObjId="EC-LF_XZ.062Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7094,1641 7042,1641 7042,1652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f963d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7042,1687 7042,1718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18920@0" ObjectIDZND0="g_3023610@0" Pin0InfoVect0LinkObjId="g_3023610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-86570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7042,1687 7042,1718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fec770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7094,1514 7122,1514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18930@x" ObjectIDND1="18660@x" ObjectIDND2="g_303cd70@0" ObjectIDZND0="g_3028630@0" Pin0InfoVect0LinkObjId="g_3028630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86586_0" Pin1InfoVect1LinkObjId="SW-85102_0" Pin1InfoVect2LinkObjId="g_303cd70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7094,1514 7122,1514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fed3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7299,1619 7299,1641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="18917@0" ObjectIDZND0="18918@x" ObjectIDZND1="g_2f4a540@0" ObjectIDZND2="37697@x" Pin0InfoVect0LinkObjId="SW-86561_0" Pin0InfoVect1LinkObjId="g_2f4a540_0" Pin0InfoVect2LinkObjId="EC-LF_XZ.061Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-86560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="7299,1619 7299,1641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_304b750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7299,1641 7299,1731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="18917@x" ObjectIDND1="18918@x" ObjectIDND2="g_2f4a540@0" ObjectIDZND0="37697@0" Pin0InfoVect0LinkObjId="EC-LF_XZ.061Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86560_0" Pin1InfoVect1LinkObjId="SW-86561_0" Pin1InfoVect2LinkObjId="g_2f4a540_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7299,1641 7299,1731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b95a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7299,1641 7323,1641 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="18917@x" ObjectIDND1="18918@x" ObjectIDND2="37697@x" ObjectIDZND0="g_2f4a540@0" Pin0InfoVect0LinkObjId="g_2f4a540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86560_0" Pin1InfoVect1LinkObjId="SW-86561_0" Pin1InfoVect2LinkObjId="EC-LF_XZ.061Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7299,1641 7323,1641 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ef8fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7299,1641 7243,1641 7243,1652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="18917@x" ObjectIDND1="g_2f4a540@0" ObjectIDND2="37697@x" ObjectIDZND0="18918@1" Pin0InfoVect0LinkObjId="SW-86561_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86560_0" Pin1InfoVect1LinkObjId="g_2f4a540_0" Pin1InfoVect2LinkObjId="EC-LF_XZ.061Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7299,1641 7243,1641 7243,1652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32d7d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7243,1687 7243,1718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18918@0" ObjectIDZND0="g_32d6d10@0" Pin0InfoVect0LinkObjId="g_32d6d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-86561_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7243,1687 7243,1718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b1d870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7511,1628 7511,1650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="18924@0" ObjectIDZND0="18923@x" ObjectIDZND1="g_303d2a0@0" ObjectIDZND2="37698@x" Pin0InfoVect0LinkObjId="SW-86566_0" Pin0InfoVect1LinkObjId="g_303d2a0_0" Pin0InfoVect2LinkObjId="EC-LF_XZ.064Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-86565_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="7511,1628 7511,1650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_303d040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7511,1650 7511,1732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="18924@x" ObjectIDND1="18923@x" ObjectIDND2="g_303d2a0@0" ObjectIDZND0="37698@0" Pin0InfoVect0LinkObjId="EC-LF_XZ.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86565_0" Pin1InfoVect1LinkObjId="SW-86566_0" Pin1InfoVect2LinkObjId="g_303d2a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7511,1650 7511,1732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f69c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7511,1650 7537,1650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="18924@x" ObjectIDND1="18923@x" ObjectIDND2="37698@x" ObjectIDZND0="g_303d2a0@0" Pin0InfoVect0LinkObjId="g_303d2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86565_0" Pin1InfoVect1LinkObjId="SW-86566_0" Pin1InfoVect2LinkObjId="EC-LF_XZ.064Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7511,1650 7537,1650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f88b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7511,1650 7457,1650 7457,1661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="18924@x" ObjectIDND1="g_303d2a0@0" ObjectIDND2="37698@x" ObjectIDZND0="18923@1" Pin0InfoVect0LinkObjId="SW-86566_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86565_0" Pin1InfoVect1LinkObjId="g_303d2a0_0" Pin1InfoVect2LinkObjId="EC-LF_XZ.064Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7511,1650 7457,1650 7457,1661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_329a780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7457,1696 7457,1727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18923@0" ObjectIDZND0="g_2f88d70@0" Pin0InfoVect0LinkObjId="g_2f88d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-86566_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7457,1696 7457,1727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f25ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6882,1329 6882,1388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18928@0" ObjectIDZND0="18926@1" Pin0InfoVect0LinkObjId="SW-86567_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8c740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6882,1329 6882,1388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4be70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6882,1424 6882,1441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="18926@0" ObjectIDZND0="g_303ee50@0" ObjectIDZND1="g_3009a20@0" ObjectIDZND2="18927@x" Pin0InfoVect0LinkObjId="g_303ee50_0" Pin0InfoVect1LinkObjId="g_3009a20_0" Pin0InfoVect2LinkObjId="SW-86569_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-86567_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6882,1424 6882,1441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f4c060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6882,1441 6882,1456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="18926@x" ObjectIDND1="18927@x" ObjectIDZND0="g_303ee50@0" ObjectIDZND1="g_3009a20@0" Pin0InfoVect0LinkObjId="g_303ee50_0" Pin0InfoVect1LinkObjId="g_3009a20_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-86567_0" Pin1InfoVect1LinkObjId="SW-86569_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6882,1441 6882,1456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bfe1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6882,1441 6870,1441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="18926@x" ObjectIDND1="g_303ee50@0" ObjectIDND2="g_3009a20@0" ObjectIDZND0="18927@1" Pin0InfoVect0LinkObjId="SW-86569_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-86567_0" Pin1InfoVect1LinkObjId="g_303ee50_0" Pin1InfoVect2LinkObjId="g_3009a20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6882,1441 6870,1441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f17b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6834,1441 6820,1441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18927@0" ObjectIDZND0="g_2fa2420@0" Pin0InfoVect0LinkObjId="g_2fa2420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-86569_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6834,1441 6820,1441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3229680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6586,1505 6570,1505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18653@0" ObjectIDZND0="g_3022d90@0" Pin0InfoVect0LinkObjId="g_3022d90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6586,1505 6570,1505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f69970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7044,1508 7023,1508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18930@0" ObjectIDZND0="g_2fb2160@0" Pin0InfoVect0LinkObjId="g_2fb2160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-86586_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7044,1508 7023,1508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d2df50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7299,1506 7285,1506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="18647@x" ObjectIDND1="g_2f4d5e0@0" ObjectIDND2="g_2f95d40@0" ObjectIDZND0="18648@1" Pin0InfoVect0LinkObjId="SW-85017_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-85016_0" Pin1InfoVect1LinkObjId="g_2f4d5e0_0" Pin1InfoVect2LinkObjId="g_2f95d40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7299,1506 7285,1506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f93dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7249,1506 7226,1506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18648@0" ObjectIDZND0="g_2fe15f0@0" Pin0InfoVect0LinkObjId="g_2fe15f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85017_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7249,1506 7226,1506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_300fa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7511,1522 7495,1522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="18656@x" ObjectIDND1="g_32a32b0@0" ObjectIDND2="g_304cb10@0" ObjectIDZND0="38943@1" Pin0InfoVect0LinkObjId="SW-233738_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-85081_0" Pin1InfoVect1LinkObjId="g_32a32b0_0" Pin1InfoVect2LinkObjId="g_304cb10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7511,1522 7495,1522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245f8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7459,1522 7441,1522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38943@0" ObjectIDZND0="g_1ba9db0@0" Pin0InfoVect0LinkObjId="g_1ba9db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-233738_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7459,1522 7441,1522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f6acb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7094,1514 7094,1508 7080,1508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3028630@0" ObjectIDND1="18660@x" ObjectIDND2="g_303cd70@0" ObjectIDZND0="18930@1" Pin0InfoVect0LinkObjId="SW-86586_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3028630_0" Pin1InfoVect1LinkObjId="SW-85102_0" Pin1InfoVect2LinkObjId="g_303cd70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7094,1514 7094,1508 7080,1508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24ef6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6837,496 6814,496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_24ef920@0" ObjectIDND1="g_32d5670@0" ObjectIDND2="18635@x" ObjectIDZND0="g_2f251a0@0" Pin0InfoVect0LinkObjId="g_2f251a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24ef920_0" Pin1InfoVect1LinkObjId="g_32d5670_0" Pin1InfoVect2LinkObjId="SW-84899_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6837,496 6814,496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f92e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7110,582 7167,582 7167,566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="voltageTransformer" ObjectIDND0="g_1b96ca0@0" ObjectIDND1="18638@x" ObjectIDND2="41858@x" ObjectIDZND0="g_2f930e0@0" Pin0InfoVect0LinkObjId="g_2f930e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b96ca0_0" Pin1InfoVect1LinkObjId="SW-84930_0" Pin1InfoVect2LinkObjId="EC-LF_XZ.362Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7110,582 7167,582 7167,566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ba6250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7111,582 7088,582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="18638@x" ObjectIDND1="g_2f930e0@0" ObjectIDND2="41858@x" ObjectIDZND0="g_1b96ca0@0" Pin0InfoVect0LinkObjId="g_1b96ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-84930_0" Pin1InfoVect1LinkObjId="g_2f930e0_0" Pin1InfoVect2LinkObjId="EC-LF_XZ.362Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7111,582 7088,582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2efc8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7111,503 7111,582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="41858@0" ObjectIDZND0="g_1b96ca0@0" ObjectIDZND1="18638@x" ObjectIDZND2="g_2f930e0@0" Pin0InfoVect0LinkObjId="g_1b96ca0_0" Pin0InfoVect1LinkObjId="SW-84930_0" Pin0InfoVect2LinkObjId="g_2f930e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-LF_XZ.362Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="7111,503 7111,582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2efcb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7111,582 7111,599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_1b96ca0@0" ObjectIDND1="g_2f930e0@0" ObjectIDND2="41858@x" ObjectIDZND0="18638@1" Pin0InfoVect0LinkObjId="SW-84930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b96ca0_0" Pin1InfoVect1LinkObjId="g_2f930e0_0" Pin1InfoVect2LinkObjId="EC-LF_XZ.362Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7111,582 7111,599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc1670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7433,581 7490,581 7490,565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="voltageTransformer" ObjectIDZND0="g_2f287a0@0" Pin0InfoVect0LinkObjId="g_2f287a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7433,581 7490,581 7490,565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31d1430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7434,581 7409,581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="18641@x" ObjectIDND1="37737@1" ObjectIDZND0="g_2fc18d0@0" Pin0InfoVect0LinkObjId="g_2fc18d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-84961_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7434,581 7409,581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fddf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6929,1123 6900,1123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18631@x" ObjectIDND1="g_2f8d7b0@0" ObjectIDZND0="g_2fd39b0@0" Pin0InfoVect0LinkObjId="g_2fd39b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-84813_0" Pin1InfoVect1LinkObjId="g_2f8d7b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6929,1123 6900,1123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fde170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6929,877 6929,843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18628@1" ObjectIDZND0="18629@0" Pin0InfoVect0LinkObjId="SW-84811_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84810_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6929,877 6929,843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fde3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6929,945 6929,904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="18658@0" ObjectIDZND0="18628@0" Pin0InfoVect0LinkObjId="SW-84810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6929,945 6929,904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ed9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6929,1197 6929,1173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18630@1" ObjectIDZND0="18631@1" Pin0InfoVect0LinkObjId="SW-84813_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84812_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6929,1197 6929,1173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31edc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6929,1329 6929,1295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18928@0" ObjectIDZND0="18632@1" Pin0InfoVect0LinkObjId="SW-84814_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8c740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6929,1329 6929,1295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31edeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6929,1259 6929,1224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18632@0" ObjectIDZND0="18630@0" Pin0InfoVect0LinkObjId="SW-84812_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84814_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6929,1259 6929,1224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f8c740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1348 6639,1329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18651@0" ObjectIDZND0="18928@0" Pin0InfoVect0LinkObjId="g_2f98aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85057_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1348 6639,1329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f9980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1516 6639,1505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2cef050@0" ObjectIDND1="g_303c3a0@0" ObjectIDZND0="18653@x" ObjectIDZND1="18652@x" Pin0InfoVect0LinkObjId="SW-85059_0" Pin0InfoVect1LinkObjId="SW-85058_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cef050_0" Pin1InfoVect1LinkObjId="g_303c3a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1516 6639,1505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f985e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1505 6622,1505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2cef050@0" ObjectIDND1="g_303c3a0@0" ObjectIDND2="18652@x" ObjectIDZND0="18653@1" Pin0InfoVect0LinkObjId="SW-85059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cef050_0" Pin1InfoVect1LinkObjId="g_303c3a0_0" Pin1InfoVect2LinkObjId="SW-85058_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1505 6622,1505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f98840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7094,1401 7094,1383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18649@1" ObjectIDZND0="18659@1" Pin0InfoVect0LinkObjId="SW-85101_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85037_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7094,1401 7094,1383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f98aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7094,1347 7094,1329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18659@0" ObjectIDZND0="18928@0" Pin0InfoVect0LinkObjId="g_2f8c740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85101_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7094,1347 7094,1329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_302a790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7299,1401 7299,1382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18645@1" ObjectIDZND0="18646@1" Pin0InfoVect0LinkObjId="SW-85015_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85014_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7299,1401 7299,1382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_302a9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7299,1346 7299,1329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18646@0" ObjectIDZND0="18928@0" Pin0InfoVect0LinkObjId="g_2f8c740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85015_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7299,1346 7299,1329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a55fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7511,1401 7511,1380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18654@1" ObjectIDZND0="18655@1" Pin0InfoVect0LinkObjId="SW-85080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7511,1401 7511,1380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a56240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7511,1344 7511,1329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18655@0" ObjectIDZND0="18928@0" Pin0InfoVect0LinkObjId="g_2f8c740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7511,1344 7511,1329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3005d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7711,1429 7711,1404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_32c1430@1" ObjectIDZND0="18925@1" Pin0InfoVect0LinkObjId="SW-86568_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32c1430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7711,1429 7711,1404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3005f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7711,1368 7711,1329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18925@0" ObjectIDZND0="18928@0" Pin0InfoVect0LinkObjId="g_2f8c740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-86568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7711,1368 7711,1329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cee740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7711,1460 7711,1471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_32c1430@0" ObjectIDZND0="g_30061d0@1" Pin0InfoVect0LinkObjId="g_30061d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32c1430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7711,1460 7711,1471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cee9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7711,1510 7711,1533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_30061d0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30061d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="7711,1510 7711,1533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f7dc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7094,1514 7094,1486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3028630@0" ObjectIDND1="18930@x" ObjectIDND2="g_303cd70@0" ObjectIDZND0="18660@0" Pin0InfoVect0LinkObjId="SW-85102_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3028630_0" Pin1InfoVect1LinkObjId="SW-86586_0" Pin1InfoVect2LinkObjId="g_303cd70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7094,1514 7094,1486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f7de80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7094,1450 7094,1428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18660@1" ObjectIDZND0="18649@0" Pin0InfoVect0LinkObjId="SW-85037_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85102_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7094,1450 7094,1428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24db470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7299,1506 7299,1488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="18648@x" ObjectIDND1="g_2f4d5e0@0" ObjectIDND2="g_2f95d40@0" ObjectIDZND0="18647@0" Pin0InfoVect0LinkObjId="SW-85016_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-85017_0" Pin1InfoVect1LinkObjId="g_2f4d5e0_0" Pin1InfoVect2LinkObjId="g_2f95d40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7299,1506 7299,1488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24db6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7299,1452 7299,1428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18647@1" ObjectIDZND0="18645@0" Pin0InfoVect0LinkObjId="SW-85014_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85016_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7299,1452 7299,1428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24db930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7511,1522 7511,1488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_32a32b0@0" ObjectIDND1="g_304cb10@0" ObjectIDND2="38943@x" ObjectIDZND0="18656@0" Pin0InfoVect0LinkObjId="SW-85081_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_32a32b0_0" Pin1InfoVect1LinkObjId="g_304cb10_0" Pin1InfoVect2LinkObjId="SW-233738_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7511,1522 7511,1488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24dbb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7511,1452 7511,1428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18656@1" ObjectIDZND0="18654@0" Pin0InfoVect0LinkObjId="SW-85079_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85081_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7511,1452 7511,1428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3035530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6929,1050 6900,1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="18658@x" ObjectIDND1="g_2f8d7b0@0" ObjectIDZND0="g_3035790@0" Pin0InfoVect0LinkObjId="g_3035790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="g_2f8d7b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6929,1050 6900,1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30042c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6929,1137 6929,1123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="18631@0" ObjectIDZND0="g_2fd39b0@0" ObjectIDZND1="g_2f8d7b0@0" Pin0InfoVect0LinkObjId="g_2fd39b0_0" Pin0InfoVect1LinkObjId="g_2f8d7b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84813_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6929,1137 6929,1123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff0c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6929,1037 6929,1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="18658@1" ObjectIDZND0="g_3035790@0" ObjectIDZND1="g_2f8d7b0@0" Pin0InfoVect0LinkObjId="g_3035790_0" Pin0InfoVect1LinkObjId="g_2f8d7b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6929,1037 6929,1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff0ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6929,1050 6929,1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_3035790@0" ObjectIDND1="18658@x" ObjectIDZND0="g_2f8d7b0@1" Pin0InfoVect0LinkObjId="g_2f8d7b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3035790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6929,1050 6929,1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff1100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6929,1105 6929,1123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2f8d7b0@0" ObjectIDZND0="g_2fd39b0@0" ObjectIDZND1="18631@x" Pin0InfoVect0LinkObjId="g_2fd39b0_0" Pin0InfoVect1LinkObjId="SW-84813_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f8d7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="6929,1105 6929,1123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ff1360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7537,1523 7511,1523 7511,1522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_32a32b0@0" ObjectIDZND0="18656@x" ObjectIDZND1="g_304cb10@0" ObjectIDZND2="38943@x" Pin0InfoVect0LinkObjId="SW-85081_0" Pin0InfoVect1LinkObjId="g_304cb10_0" Pin0InfoVect2LinkObjId="SW-233738_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32a32b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="7537,1523 7511,1523 7511,1522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3040b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7323,1513 7299,1513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2f4d5e0@0" ObjectIDZND0="18648@x" ObjectIDZND1="18647@x" ObjectIDZND2="g_2f95d40@0" Pin0InfoVect0LinkObjId="SW-85017_0" Pin0InfoVect1LinkObjId="SW-85016_0" Pin0InfoVect2LinkObjId="g_2f95d40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f4d5e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="7323,1513 7299,1513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3040da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7299,1513 7299,1506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2f4d5e0@0" ObjectIDND1="g_2f95d40@0" ObjectIDZND0="18648@x" ObjectIDZND1="18647@x" Pin0InfoVect0LinkObjId="SW-85017_0" Pin0InfoVect1LinkObjId="SW-85016_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f4d5e0_0" Pin1InfoVect1LinkObjId="g_2f95d40_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="7299,1513 7299,1506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3041000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1485 6639,1507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="18652@0" ObjectIDZND0="g_2cef050@0" ObjectIDZND1="g_303c3a0@0" ObjectIDZND2="18653@x" Pin0InfoVect0LinkObjId="g_2cef050_0" Pin0InfoVect1LinkObjId="g_303c3a0_0" Pin0InfoVect2LinkObjId="SW-85059_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85058_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1485 6639,1507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3041260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1383 6639,1403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18651@1" ObjectIDZND0="18650@1" Pin0InfoVect0LinkObjId="SW-85056_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85057_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1383 6639,1403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_303c140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1430 6639,1449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18650@0" ObjectIDZND0="18652@1" Pin0InfoVect0LinkObjId="SW-85058_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-85056_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1430 6639,1449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30083e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1515 6639,1530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2cef050@0" ObjectIDND1="18653@x" ObjectIDND2="18652@x" ObjectIDZND0="g_303c3a0@1" Pin0InfoVect0LinkObjId="g_303c3a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cef050_0" Pin1InfoVect1LinkObjId="SW-85059_0" Pin1InfoVect2LinkObjId="SW-85058_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1515 6639,1530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3008640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6639,1569 6639,1585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_303c3a0@0" ObjectIDZND0="18922@1" Pin0InfoVect0LinkObjId="SW-86562_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_303c3a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6639,1569 6639,1585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30088a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7094,1514 7094,1530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3028630@0" ObjectIDND1="18930@x" ObjectIDND2="18660@x" ObjectIDZND0="g_303cd70@1" Pin0InfoVect0LinkObjId="g_303cd70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3028630_0" Pin1InfoVect1LinkObjId="SW-86586_0" Pin1InfoVect2LinkObjId="SW-85102_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7094,1514 7094,1530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3008b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7094,1569 7094,1583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_303cd70@0" ObjectIDZND0="18919@1" Pin0InfoVect0LinkObjId="SW-86564_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_303cd70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7094,1569 7094,1583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3008d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7299,1513 7299,1530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2f4d5e0@0" ObjectIDND1="18648@x" ObjectIDND2="18647@x" ObjectIDZND0="g_2f95d40@1" Pin0InfoVect0LinkObjId="g_2f95d40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f4d5e0_0" Pin1InfoVect1LinkObjId="SW-85017_0" Pin1InfoVect2LinkObjId="SW-85016_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7299,1513 7299,1530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_301f010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7299,1569 7299,1583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2f95d40@0" ObjectIDZND0="18917@1" Pin0InfoVect0LinkObjId="SW-86560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f95d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7299,1569 7299,1583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_301f270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7511,1522 7511,1534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="18656@x" ObjectIDND1="g_32a32b0@0" ObjectIDND2="38943@x" ObjectIDZND0="g_304cb10@1" Pin0InfoVect0LinkObjId="g_304cb10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-85081_0" Pin1InfoVect1LinkObjId="g_32a32b0_0" Pin1InfoVect2LinkObjId="SW-233738_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7511,1522 7511,1534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_301f4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7511,1573 7511,1592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_304cb10@0" ObjectIDZND0="18924@1" Pin0InfoVect0LinkObjId="SW-86565_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_304cb10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7511,1573 7511,1592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_246b230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7433,521 7433,581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37737@1" ObjectIDZND0="g_2fc18d0@0" ObjectIDZND1="18641@x" Pin0InfoVect0LinkObjId="g_2fc18d0_0" Pin0InfoVect1LinkObjId="SW-84961_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="7433,521 7433,581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_246b490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7433,581 7433,599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" ObjectIDND0="g_2fc18d0@0" ObjectIDND1="37737@1" ObjectIDZND0="18641@1" Pin0InfoVect0LinkObjId="SW-84961_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fc18d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7433,581 7433,599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d9930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6894,479 6894,495 6837,495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_24ef920@0" ObjectIDZND0="g_2f251a0@0" ObjectIDZND1="g_32d5670@0" ObjectIDZND2="18635@x" Pin0InfoVect0LinkObjId="g_2f251a0_0" Pin0InfoVect1LinkObjId="g_32d5670_0" Pin0InfoVect2LinkObjId="SW-84899_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24ef920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6894,479 6894,495 6837,495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32d4f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6837,495 6837,445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_2f251a0@0" ObjectIDND1="g_24ef920@0" ObjectIDND2="g_32d5670@0" ObjectIDZND0="45007@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f251a0_0" Pin1InfoVect1LinkObjId="g_24ef920_0" Pin1InfoVect2LinkObjId="g_32d5670_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="6837,495 6837,445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd4730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6774,558 6837,558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="g_32d5670@0" ObjectIDZND0="18635@x" ObjectIDZND1="g_2f251a0@0" ObjectIDZND2="g_24ef920@0" Pin0InfoVect0LinkObjId="SW-84899_0" Pin0InfoVect1LinkObjId="g_2f251a0_0" Pin0InfoVect2LinkObjId="g_24ef920_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32d5670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6774,558 6837,558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd5170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6837,600 6837,558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="18635@1" ObjectIDZND0="g_32d5670@0" ObjectIDZND1="g_2f251a0@0" ObjectIDZND2="g_24ef920@0" Pin0InfoVect0LinkObjId="g_32d5670_0" Pin0InfoVect1LinkObjId="g_2f251a0_0" Pin0InfoVect2LinkObjId="g_24ef920_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84899_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6837,600 6837,558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd53d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="6837,558 6837,495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="g_32d5670@0" ObjectIDND1="18635@x" ObjectIDZND0="g_2f251a0@0" ObjectIDZND1="g_24ef920@0" ObjectIDZND2="45007@1" Pin0InfoVect0LinkObjId="g_2f251a0_0" Pin0InfoVect1LinkObjId="g_24ef920_0" Pin0InfoVect2LinkObjId="g_32d4f70_1" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32d5670_0" Pin1InfoVect1LinkObjId="SW-84899_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="6837,558 6837,495 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_XZ"/>
</svg>