<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-168" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1200 1811 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="60" x2="21" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="15" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="6" x2="6" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="4" x2="4" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="6" y2="9"/>
    <rect height="13" stroke-width="0.424575" width="29" x="15" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape42_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="19" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="22" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="19" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape42-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="22" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape43_0">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
   </symbol>
   <symbol id="transformer2:shape43_1">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape85">
    <circle cx="20" cy="32" fillStyle="0" r="8" stroke-width="0.570276"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="11" x2="13" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="13" x2="16" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="11" x2="13" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="25" x2="27" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="27" x2="30" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="25" x2="27" y1="16" y2="20"/>
    <circle cx="13" cy="20" fillStyle="0" r="8" stroke-width="0.570276"/>
    <circle cx="27" cy="20" fillStyle="0" r="8" stroke-width="0.570276"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="17" x2="19" y1="37" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.960801" x1="21" x2="21" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.989747" x1="18" x2="21" y1="5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.992459" x1="18" x2="21" y1="10" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="19" x2="22" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="17" x2="19" y1="30" y2="34"/>
    <circle cx="20" cy="8" fillStyle="0" r="8" stroke-width="0.536731"/>
   </symbol>
   <symbol id="voltageTransformer:shape73">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="48" y1="4" y2="4"/>
    <circle cx="25" cy="25" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="12" x2="28" y1="22" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="12" x2="28" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="28" x2="28" y1="32" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="59" x2="51" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="67" x2="59" y1="30" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="59" x2="59" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="32" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="48" x2="40" y1="66" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="50" y2="59"/>
    <circle cx="57" cy="26" fillStyle="0" r="25" stroke-width="0.520408"/>
    <circle cx="41" cy="55" fillStyle="0" r="25" stroke-width="0.520408"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f02d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f03d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f045f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f05280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f065a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f07160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f07980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f08260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f08c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f09610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f0a220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1f0ab00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f0c6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f0d320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f0db40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f0e530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f0fa60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f10b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b3d700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f120b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f13260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f13be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a67600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f145b0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f16db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f17690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1f275a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1f189e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1f1aab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1f15a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1821" x="3112" y="-1205"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f864c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4310.000000 886.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f87390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.000000 871.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f87ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 856.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f883c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.000000 841.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f88e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4313.000000 576.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f89200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4302.000000 561.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f89440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 546.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f89680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4322.000000 531.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8a5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3904.000000 73.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8a820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3893.000000 58.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8aa60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.000000 43.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8aca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3913.000000 28.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8d9b0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4308.000000 945.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8e2e0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4308.000000 930.033333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8e960" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4300.000000 915.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8ebe0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 4308.000000 960.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8ef10" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3544.000000 532.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8f180" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3544.000000 517.033333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8f3c0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3536.000000 502.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8f600" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 3544.000000 547.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4883,-759 4878,-769 4888,-769 4883,-759 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4883,-746 4878,-736 4888,-736 4883,-746 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4704,-759 4699,-769 4709,-769 4704,-759 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4704,-746 4699,-736 4709,-736 4704,-746 " stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="292" lineStyle="1" stroke="rgb(255,255,255)" stroke-dasharray="10 5 " stroke-width="1" width="305" x="3980" y="-1055"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(154,205,50)" stroke-width="0.416609" width="14" x="3809" y="-436"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(154,205,50)" stroke-width="0.416609" width="14" x="3632" y="-390"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(154,205,50)" stroke-width="0.416609" width="14" x="4016" y="-290"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(154,205,50)" stroke-width="0.416609" width="14" x="4081" y="-290"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(154,205,50)" stroke-width="0.416609" width="14" x="4484" y="-290"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(154,205,50)" stroke-width="0.416609" width="14" x="4540" y="-290"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-114390">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4158.000000 -887.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21698" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_10160SW"/>
     <cge:Meas_Ref ObjectId="114390"/>
    <cge:TPSR_Ref TObjectID="21698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114392">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 -677.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21700" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_1010SW"/>
     <cge:Meas_Ref ObjectId="114392"/>
    <cge:TPSR_Ref TObjectID="21700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114387">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.000000 -903.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21695" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_1016SW"/>
     <cge:Meas_Ref ObjectId="114387"/>
    <cge:TPSR_Ref TObjectID="21695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114389">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4161.000000 -955.000000)" xlink:href="#switch2:shape42_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21697" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_K1017SW"/>
     <cge:Meas_Ref ObjectId="114389"/>
    <cge:TPSR_Ref TObjectID="21697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114391">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4158.000000 -822.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21699" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_10117SW"/>
     <cge:Meas_Ref ObjectId="114391"/>
    <cge:TPSR_Ref TObjectID="21699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114388">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.000000 -768.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21696" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_1011SW"/>
     <cge:Meas_Ref ObjectId="114388"/>
    <cge:TPSR_Ref TObjectID="21696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114432">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4157.000000 -585.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21707" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_60117SW"/>
     <cge:Meas_Ref ObjectId="114432"/>
    <cge:TPSR_Ref TObjectID="21707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114431">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4209.000000 -551.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21705" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_601XC"/>
     <cge:Meas_Ref ObjectId="114431"/>
    <cge:TPSR_Ref TObjectID="21705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114431">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4209.000000 -488.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21706" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_601XC1"/>
     <cge:Meas_Ref ObjectId="114431"/>
    <cge:TPSR_Ref TObjectID="21706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114433">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3806.000000 -383.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21708" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_6311XC"/>
     <cge:Meas_Ref ObjectId="114433"/>
    <cge:TPSR_Ref TObjectID="21708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114434">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3763.000000 -358.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21710" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_63167SW"/>
     <cge:Meas_Ref ObjectId="114434"/>
    <cge:TPSR_Ref TObjectID="21710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114435">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.000000 -431.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21711" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_6901XC"/>
     <cge:Meas_Ref ObjectId="114435"/>
    <cge:TPSR_Ref TObjectID="21711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114438">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.000000 -299.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21716" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_6321XC"/>
     <cge:Meas_Ref ObjectId="114438"/>
    <cge:TPSR_Ref TObjectID="21716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114439">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4078.000000 -299.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21718" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_6322XC"/>
     <cge:Meas_Ref ObjectId="114439"/>
    <cge:TPSR_Ref TObjectID="21718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114442">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -299.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21723" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_6331XC"/>
     <cge:Meas_Ref ObjectId="114442"/>
    <cge:TPSR_Ref TObjectID="21723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114443">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 -299.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21725" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_6332XC"/>
     <cge:Meas_Ref ObjectId="114443"/>
    <cge:TPSR_Ref TObjectID="21725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114437">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -437.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21714" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_632XC"/>
     <cge:Meas_Ref ObjectId="114437"/>
    <cge:TPSR_Ref TObjectID="21714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114437">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -374.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21715" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_632XC1"/>
     <cge:Meas_Ref ObjectId="114437"/>
    <cge:TPSR_Ref TObjectID="21715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114441">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 -437.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21721" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_633XC"/>
     <cge:Meas_Ref ObjectId="114441"/>
    <cge:TPSR_Ref TObjectID="21721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114441">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 -374.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21722" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_633XC1"/>
     <cge:Meas_Ref ObjectId="114441"/>
    <cge:TPSR_Ref TObjectID="21722"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_XJHYJ.CX_XJHYJ_6IM">
    <g class="BV-3KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3540,-480 4614,-480 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21731" ObjectName="BS-CX_XJHYJ.CX_XJHYJ_6IM"/>
    <cge:TPSR_Ref TObjectID="21731"/></metadata>
   <polyline fill="none" opacity="0" points="3540,-480 4614,-480 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_XJHYJ.CX_XJHYJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="30430"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.000000 -659.000000)" xlink:href="#transformer2:shape43_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.000000 -659.000000)" xlink:href="#transformer2:shape43_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21729" ObjectName="TF-CX_XJHYJ.CX_XJHYJ_1T"/>
    </metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4401.000000 -140.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 -140.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_1a68250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4166,-960 4153,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21697@1" ObjectIDZND0="g_1ac1930@0" Pin0InfoVect0LinkObjId="g_1ac1930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114389_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4166,-960 4153,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ab7e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-892 4163,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a0fc50@0" ObjectIDZND0="21698@0" Pin0InfoVect0LinkObjId="SW-114390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a0fc50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-892 4163,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ab8010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-908 4220,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21695@0" ObjectIDZND0="21694@x" ObjectIDZND1="21698@x" Pin0InfoVect0LinkObjId="SW-114386_0" Pin0InfoVect1LinkObjId="SW-114390_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-908 4220,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a371c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4202,-960 4220,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="21697@0" ObjectIDZND0="21695@x" ObjectIDZND1="0@1" ObjectIDZND2="g_187c910@0" Pin0InfoVect0LinkObjId="SW-114387_0" Pin0InfoVect1LinkObjId="SM-0_1" Pin0InfoVect2LinkObjId="g_187c910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114389_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4202,-960 4220,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a13d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-944 4220,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="21695@1" ObjectIDZND0="21697@x" ObjectIDZND1="0@1" ObjectIDZND2="g_187c910@0" Pin0InfoVect0LinkObjId="SW-114389_0" Pin0InfoVect1LinkObjId="SM-0_1" Pin0InfoVect2LinkObjId="g_187c910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114387_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-944 4220,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a06970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4182,-1038 4220,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_187c910@0" ObjectIDZND0="0@1" ObjectIDZND1="21695@x" ObjectIDZND2="21697@x" Pin0InfoVect0LinkObjId="SM-0_1" Pin0InfoVect1LinkObjId="SW-114387_0" Pin0InfoVect2LinkObjId="SW-114389_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_187c910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4182,-1038 4220,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a06b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-1038 4220,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_187c910@0" ObjectIDND1="21695@x" ObjectIDND2="21697@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SM-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_187c910_0" Pin1InfoVect1LinkObjId="SW-114387_0" Pin1InfoVect2LinkObjId="SW-114389_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-1038 4220,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a67840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-960 4220,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="21695@x" ObjectIDND1="21697@x" ObjectIDZND0="0@1" ObjectIDZND1="g_187c910@0" ObjectIDZND2="g_1a46f90@0" Pin0InfoVect0LinkObjId="SM-0_1" Pin0InfoVect1LinkObjId="g_187c910_0" Pin0InfoVect2LinkObjId="g_1a46f90_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114387_0" Pin1InfoVect1LinkObjId="SW-114389_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-960 4220,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a67a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-1005 4220,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="21695@x" ObjectIDND1="21697@x" ObjectIDND2="g_1a46f90@0" ObjectIDZND0="0@1" ObjectIDZND1="g_187c910@0" Pin0InfoVect0LinkObjId="SM-0_1" Pin0InfoVect1LinkObjId="g_187c910_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-114387_0" Pin1InfoVect1LinkObjId="SW-114389_0" Pin1InfoVect2LinkObjId="g_1a46f90_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-1005 4220,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a67c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-1005 4220,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1a46f90@0" ObjectIDZND0="0@1" ObjectIDZND1="g_187c910@0" ObjectIDZND2="21695@x" Pin0InfoVect0LinkObjId="SM-0_1" Pin0InfoVect1LinkObjId="g_187c910_0" Pin0InfoVect2LinkObjId="SW-114387_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a46f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-1005 4220,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a80820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-189 3957,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1bfe750@0" Pin0InfoVect0LinkObjId="g_1bfe750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-189 3957,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a38c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4425,-189 4425,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1bfcb20@0" Pin0InfoVect0LinkObjId="g_1bfcb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4425,-189 4425,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a10400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-827 4163,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a105f0@0" ObjectIDZND0="21699@0" Pin0InfoVect0LinkObjId="SW-114391_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a105f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-827 4163,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a6bc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-827 4220,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21699@1" ObjectIDZND0="21694@x" ObjectIDZND1="21696@x" Pin0InfoVect0LinkObjId="SW-114386_0" Pin0InfoVect1LinkObjId="SW-114388_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114391_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-827 4220,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ad6ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-773 4220,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="21696@0" ObjectIDZND0="21729@0" Pin0InfoVect0LinkObjId="g_1a92410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114388_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-773 4220,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ad70b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-883 4220,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21695@x" ObjectIDND1="21698@x" ObjectIDZND0="21694@1" Pin0InfoVect0LinkObjId="SW-114386_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114387_0" Pin1InfoVect1LinkObjId="SW-114390_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-883 4220,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ad72a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-845 4220,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21694@0" ObjectIDZND0="21699@x" ObjectIDZND1="21696@x" Pin0InfoVect0LinkObjId="SW-114391_0" Pin0InfoVect1LinkObjId="SW-114388_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-845 4220,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ad7490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-892 4220,-892 4220,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21698@1" ObjectIDZND0="21694@x" ObjectIDZND1="21695@x" Pin0InfoVect0LinkObjId="SW-114386_0" Pin0InfoVect1LinkObjId="SW-114387_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-892 4220,-892 4220,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ad7680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4220,-809 4220,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21696@1" ObjectIDZND0="21694@x" ObjectIDZND1="21699@x" Pin0InfoVect0LinkObjId="SW-114386_0" Pin0InfoVect1LinkObjId="SW-114391_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4220,-809 4220,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ad8270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4103,-671 4103,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="21700@0" Pin0InfoVect0LinkObjId="SW-114392_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4103,-671 4103,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ad8460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4128,-716 4128,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="g_1a12dc0@0" ObjectIDZND0="21700@x" ObjectIDZND1="21729@x" ObjectIDZND2="g_1a67f80@0" Pin0InfoVect0LinkObjId="SW-114392_0" Pin0InfoVect1LinkObjId="g_1ad6ec0_0" Pin0InfoVect2LinkObjId="g_1a67f80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a12dc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4128,-716 4128,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a92220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4128,-727 4103,-727 4103,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a12dc0@0" ObjectIDND1="21729@x" ObjectIDND2="g_1a67f80@0" ObjectIDZND0="21700@1" Pin0InfoVect0LinkObjId="SW-114392_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a12dc0_0" Pin1InfoVect1LinkObjId="g_1ad6ec0_0" Pin1InfoVect2LinkObjId="g_1a67f80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4128,-727 4103,-727 4103,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a92410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4157,-716 4157,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1a67f80@0" ObjectIDZND0="21729@x" ObjectIDZND1="21700@x" ObjectIDZND2="g_1a12dc0@0" Pin0InfoVect0LinkObjId="g_1ad6ec0_0" Pin0InfoVect1LinkObjId="SW-114392_0" Pin0InfoVect2LinkObjId="g_1a12dc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a67f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4157,-716 4157,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a92600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-727 4157,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="21729@x" ObjectIDZND0="g_1a67f80@0" ObjectIDZND1="21700@x" ObjectIDZND2="g_1a12dc0@0" Pin0InfoVect0LinkObjId="g_1a67f80_0" Pin0InfoVect1LinkObjId="SW-114392_0" Pin0InfoVect2LinkObjId="g_1a12dc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ad6ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-727 4157,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1a927f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4157,-727 4128,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="21729@x" ObjectIDND1="g_1a67f80@0" ObjectIDZND0="21700@x" ObjectIDZND1="g_1a12dc0@0" Pin0InfoVect0LinkObjId="SW-114392_0" Pin0InfoVect1LinkObjId="g_1a12dc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ad6ec0_0" Pin1InfoVect1LinkObjId="g_1a67f80_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4157,-727 4128,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a929e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-590 4162,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a92bd0@0" ObjectIDZND0="21707@0" Pin0InfoVect0LinkObjId="SW-114432_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a92bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-590 4162,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1ab3f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4198,-590 4219,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21707@1" ObjectIDZND0="g_1a20fa0@0" ObjectIDZND1="g_1a20610@0" ObjectIDZND2="21705@x" Pin0InfoVect0LinkObjId="g_1a20fa0_0" Pin0InfoVect1LinkObjId="g_1a20610_0" Pin0InfoVect2LinkObjId="SW-114431_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114432_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4198,-590 4219,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a86e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-522 4219,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21704@0" ObjectIDZND0="21706@1" Pin0InfoVect0LinkObjId="SW-114431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-522 4219,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1afcf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-495 4219,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21706@0" ObjectIDZND0="21731@0" Pin0InfoVect0LinkObjId="g_1a90210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-495 4219,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1afd180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-558 4219,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21705@1" ObjectIDZND0="21704@1" Pin0InfoVect0LinkObjId="SW-114430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114431_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-558 4219,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1afd370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-580 4272,-590 4219,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1a20fa0@0" ObjectIDZND0="21707@x" ObjectIDZND1="g_1a20610@0" ObjectIDZND2="21705@x" Pin0InfoVect0LinkObjId="SW-114432_0" Pin0InfoVect1LinkObjId="g_1a20610_0" Pin0InfoVect2LinkObjId="SW-114431_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a20fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-580 4272,-590 4219,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a90210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-457 3816,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21708@1" ObjectIDZND0="21731@0" Pin0InfoVect0LinkObjId="g_1afcf90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114433_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-457 3816,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a90430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-290 3816,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1a9d770@0" ObjectIDZND0="g_1a9e310@0" Pin0InfoVect0LinkObjId="g_1a9e310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a9d770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-290 3816,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a90650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3757,-363 3768,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a90870@0" ObjectIDZND0="21710@0" Pin0InfoVect0LinkObjId="SW-114434_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a90870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3757,-363 3768,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a99960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3804,-363 3816,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21710@1" ObjectIDZND0="g_1a9e310@0" ObjectIDZND1="21708@x" Pin0InfoVect0LinkObjId="g_1a9e310_0" Pin0InfoVect1LinkObjId="SW-114433_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114434_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3804,-363 3816,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a99b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-338 3816,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1a9e310@1" ObjectIDZND0="21710@x" ObjectIDZND1="21708@x" Pin0InfoVect0LinkObjId="SW-114434_0" Pin0InfoVect1LinkObjId="SW-114433_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a9e310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-338 3816,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a99da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-363 3816,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1a9e310@0" ObjectIDND1="21710@x" ObjectIDZND0="21708@0" Pin0InfoVect0LinkObjId="SW-114433_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a9e310_0" Pin1InfoVect1LinkObjId="SW-114434_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-363 3816,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a45fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3639,-455 3639,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21711@0" ObjectIDZND0="21731@0" Pin0InfoVect0LinkObjId="g_1afcf90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3639,-455 3639,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a46200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3672,-394 3672,-404 3639,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_1c03450@0" ObjectIDZND0="21711@x" ObjectIDZND1="g_1c01c40@0" Pin0InfoVect0LinkObjId="SW-114435_0" Pin0InfoVect1LinkObjId="g_1c01c40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c03450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3672,-394 3672,-404 3639,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a46420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3639,-346 3639,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1c01c40@0" ObjectIDZND0="21711@x" ObjectIDZND1="g_1c03450@0" Pin0InfoVect0LinkObjId="SW-114435_0" Pin0InfoVect1LinkObjId="g_1c03450_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c01c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3639,-346 3639,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a46640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3639,-404 3639,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1c01c40@0" ObjectIDND1="g_1c03450@0" ObjectIDZND0="21711@1" Pin0InfoVect0LinkObjId="SW-114435_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c01c40_0" Pin1InfoVect1LinkObjId="g_1c03450_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3639,-404 3639,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a8e720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-323 4023,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21716@0" ObjectIDZND0="21718@x" ObjectIDZND1="g_1a16250@0" ObjectIDZND2="g_1bfe750@0" Pin0InfoVect0LinkObjId="SW-114439_0" Pin0InfoVect1LinkObjId="g_1a16250_0" Pin0InfoVect2LinkObjId="g_1bfe750_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-323 4023,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a8e940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-226 4023,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_1a46860@0" ObjectIDZND0="21716@1" Pin0InfoVect0LinkObjId="SW-114438_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a46860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-226 4023,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a79530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4145,-209 4145,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1c01540@0" ObjectIDZND0="g_1a16250@0" Pin0InfoVect0LinkObjId="g_1a16250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c01540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4145,-209 4145,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a4ddd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4088,-231 4088,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a8eb60@0" ObjectIDZND0="21718@1" Pin0InfoVect0LinkObjId="SW-114439_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a8eb60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4088,-231 4088,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a4e030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4088,-341 4145,-341 4145,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21718@x" ObjectIDND1="21716@x" ObjectIDND2="g_1bfe750@0" ObjectIDZND0="g_1a16250@1" Pin0InfoVect0LinkObjId="g_1a16250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-114439_0" Pin1InfoVect1LinkObjId="SW-114438_0" Pin1InfoVect2LinkObjId="g_1bfe750_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4088,-341 4145,-341 4145,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a68d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-323 4491,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21723@0" ObjectIDZND0="21725@x" ObjectIDZND1="g_1c003c0@0" ObjectIDZND2="g_1bfcb20@0" Pin0InfoVect0LinkObjId="SW-114443_0" Pin0InfoVect1LinkObjId="g_1c003c0_0" Pin0InfoVect2LinkObjId="g_1bfcb20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-323 4491,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a68fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-226 4491,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_1a4e290@0" ObjectIDZND0="21723@1" Pin0InfoVect0LinkObjId="SW-114442_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a4e290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-226 4491,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a6a8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-209 4604,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1c00e40@0" ObjectIDZND0="g_1c003c0@0" Pin0InfoVect0LinkObjId="g_1c003c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c00e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-209 4604,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a3bf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4547,-323 4547,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21725@0" ObjectIDZND0="21723@x" ObjectIDZND1="g_1bfcb20@0" ObjectIDZND2="g_1f92430@0" Pin0InfoVect0LinkObjId="SW-114442_0" Pin0InfoVect1LinkObjId="g_1bfcb20_0" Pin0InfoVect2LinkObjId="g_1f92430_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4547,-323 4547,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a24070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4547,-231 4547,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a69210@0" ObjectIDZND0="21725@1" Pin0InfoVect0LinkObjId="SW-114443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a69210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4547,-231 4547,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a242d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-341 4550,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="21723@x" ObjectIDND1="g_1bfcb20@0" ObjectIDND2="g_1f92430@0" ObjectIDZND0="21725@x" ObjectIDZND1="g_1c003c0@0" Pin0InfoVect0LinkObjId="SW-114443_0" Pin0InfoVect1LinkObjId="g_1c003c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-114442_0" Pin1InfoVect1LinkObjId="g_1bfcb20_0" Pin1InfoVect2LinkObjId="g_1f92430_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-341 4550,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a24530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4550,-341 4604,-341 4604,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21723@x" ObjectIDND1="g_1bfcb20@0" ObjectIDND2="g_1f92430@0" ObjectIDZND0="g_1c003c0@1" Pin0InfoVect0LinkObjId="g_1c003c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-114442_0" Pin1InfoVect1LinkObjId="g_1bfcb20_0" Pin1InfoVect2LinkObjId="g_1f92430_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4550,-341 4604,-341 4604,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a26930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4088,-323 4088,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="21718@0" ObjectIDZND0="g_1a16250@0" ObjectIDZND1="21716@x" ObjectIDZND2="g_1bfe750@0" Pin0InfoVect0LinkObjId="g_1a16250_0" Pin0InfoVect1LinkObjId="SW-114438_0" Pin0InfoVect2LinkObjId="g_1bfe750_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4088,-323 4088,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a26b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4088,-341 4023,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21718@x" ObjectIDND1="g_1a16250@0" ObjectIDZND0="21716@x" ObjectIDZND1="g_1bfe750@0" ObjectIDZND2="g_1f91930@0" Pin0InfoVect0LinkObjId="SW-114438_0" Pin0InfoVect1LinkObjId="g_1bfe750_0" Pin0InfoVect2LinkObjId="g_1f91930_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114439_0" Pin1InfoVect1LinkObjId="g_1a16250_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4088,-341 4023,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a203f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-590 4219,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1a20fa0@0" ObjectIDND1="21707@x" ObjectIDND2="g_1a20610@0" ObjectIDZND0="21705@0" Pin0InfoVect0LinkObjId="SW-114431_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a20fa0_0" Pin1InfoVect1LinkObjId="SW-114432_0" Pin1InfoVect2LinkObjId="g_1a20610_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-590 4219,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a21cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-590 4219,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1a20fa0@0" ObjectIDND1="21707@x" ObjectIDND2="21705@x" ObjectIDZND0="g_1a20610@0" Pin0InfoVect0LinkObjId="g_1a20610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a20fa0_0" Pin1InfoVect1LinkObjId="SW-114432_0" Pin1InfoVect2LinkObjId="SW-114431_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-590 4219,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1a21f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-653 4219,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1a20610@1" ObjectIDZND0="21729@1" Pin0InfoVect0LinkObjId="g_1ad6ec0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a20610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-653 4219,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1b3f220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-407 3957,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21713@0" ObjectIDZND0="21715@1" Pin0InfoVect0LinkObjId="SW-114437_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114436_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-407 3957,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1b3f480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-480 3957,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21731@0" ObjectIDZND0="21714@0" Pin0InfoVect0LinkObjId="SW-114437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1afcf90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-480 3957,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1b3f6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-444 3957,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21714@1" ObjectIDZND0="21713@1" Pin0InfoVect0LinkObjId="SW-114436_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114437_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-444 3957,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_19f4be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4425,-398 4425,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21722@1" ObjectIDZND0="21720@0" Pin0InfoVect0LinkObjId="SW-114440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4425,-398 4425,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_19f4e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4425,-434 4425,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21720@1" ObjectIDZND0="21721@1" Pin0InfoVect0LinkObjId="SW-114441_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4425,-434 4425,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_19f50a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4425,-461 4425,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21721@0" ObjectIDZND0="21731@0" Pin0InfoVect0LinkObjId="g_1afcf90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114441_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4425,-461 4425,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1f90880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-335 3957,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21716@x" ObjectIDND1="21718@x" ObjectIDND2="g_1a16250@0" ObjectIDZND0="g_1bfe750@1" Pin0InfoVect0LinkObjId="g_1bfe750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-114438_0" Pin1InfoVect1LinkObjId="SW-114439_0" Pin1InfoVect2LinkObjId="g_1a16250_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-335 3957,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1f90a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-341 3957,-341 3957,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21716@x" ObjectIDND1="21718@x" ObjectIDND2="g_1a16250@0" ObjectIDZND0="g_1bfe750@0" ObjectIDZND1="g_1f91930@0" ObjectIDZND2="21715@x" Pin0InfoVect0LinkObjId="g_1bfe750_0" Pin0InfoVect1LinkObjId="g_1f91930_0" Pin0InfoVect2LinkObjId="SW-114437_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-114438_0" Pin1InfoVect1LinkObjId="SW-114439_0" Pin1InfoVect2LinkObjId="g_1a16250_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-341 3957,-341 3957,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1f91470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-341 4425,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21725@x" ObjectIDND1="g_1c003c0@0" ObjectIDND2="21723@x" ObjectIDZND0="g_1bfcb20@0" ObjectIDZND1="g_1f92430@0" ObjectIDZND2="21722@x" Pin0InfoVect0LinkObjId="g_1bfcb20_0" Pin0InfoVect1LinkObjId="g_1f92430_0" Pin0InfoVect2LinkObjId="SW-114441_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-114443_0" Pin1InfoVect1LinkObjId="g_1c003c0_0" Pin1InfoVect2LinkObjId="SW-114442_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-341 4425,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1f916d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4425,-341 4425,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="21725@x" ObjectIDND1="g_1c003c0@0" ObjectIDND2="21723@x" ObjectIDZND0="g_1bfcb20@1" Pin0InfoVect0LinkObjId="g_1bfcb20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-114443_0" Pin1InfoVect1LinkObjId="g_1c003c0_0" Pin1InfoVect2LinkObjId="SW-114442_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4425,-341 4425,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1f930a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-365 3912,-365 3912,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="21715@x" ObjectIDND1="g_1bfe750@0" ObjectIDND2="21716@x" ObjectIDZND0="g_1f91930@0" Pin0InfoVect0LinkObjId="g_1f91930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-114437_0" Pin1InfoVect1LinkObjId="g_1bfe750_0" Pin1InfoVect2LinkObjId="SW-114438_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-365 3912,-365 3912,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1f93b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-381 3957,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21715@0" ObjectIDZND0="g_1f91930@0" ObjectIDZND1="g_1bfe750@0" ObjectIDZND2="21716@x" Pin0InfoVect0LinkObjId="g_1f91930_0" Pin0InfoVect1LinkObjId="g_1bfe750_0" Pin0InfoVect2LinkObjId="SW-114438_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114437_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-381 3957,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1f93dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3957,-365 3957,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1f91930@0" ObjectIDND1="21715@x" ObjectIDZND0="g_1bfe750@0" ObjectIDZND1="21716@x" ObjectIDZND2="21718@x" Pin0InfoVect0LinkObjId="g_1bfe750_0" Pin0InfoVect1LinkObjId="SW-114438_0" Pin0InfoVect2LinkObjId="SW-114439_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f91930_0" Pin1InfoVect1LinkObjId="SW-114437_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3957,-365 3957,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1f94030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4425,-364 4377,-364 4377,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="21725@x" ObjectIDND1="g_1c003c0@0" ObjectIDND2="21723@x" ObjectIDZND0="g_1f92430@0" Pin0InfoVect0LinkObjId="g_1f92430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-114443_0" Pin1InfoVect1LinkObjId="g_1c003c0_0" Pin1InfoVect2LinkObjId="SW-114442_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4425,-364 4377,-364 4377,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1f94b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4425,-341 4425,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21725@x" ObjectIDND1="g_1c003c0@0" ObjectIDND2="21723@x" ObjectIDZND0="g_1f92430@0" ObjectIDZND1="21722@x" Pin0InfoVect0LinkObjId="g_1f92430_0" Pin0InfoVect1LinkObjId="SW-114441_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-114443_0" Pin1InfoVect1LinkObjId="g_1c003c0_0" Pin1InfoVect2LinkObjId="SW-114442_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4425,-341 4425,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_1f94d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4425,-364 4425,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1f92430@0" ObjectIDND1="21725@x" ObjectIDND2="g_1c003c0@0" ObjectIDZND0="21722@0" Pin0InfoVect0LinkObjId="SW-114441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f92430_0" Pin1InfoVect1LinkObjId="SW-114443_0" Pin1InfoVect2LinkObjId="g_1c003c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4425,-364 4425,-381 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="21731" cx="4219" cy="-480" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21731" cx="3816" cy="-480" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21731" cx="3639" cy="-480" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21731" cx="3957" cy="-480" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21731" cx="4425" cy="-480" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-114373" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3422.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21691" ObjectName="DYN-CX_XJHYJ"/>
     <cge:Meas_Ref ObjectId="114373"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_193c590" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3910.000000 -120.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cb2c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4298.000000 -1034.000000) translate(0,15)">小</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cb2c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4298.000000 -1034.000000) translate(0,33)">江</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cb2c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4298.000000 -1034.000000) translate(0,51)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cb2c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4298.000000 -1034.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16391f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19f3780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19f3780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19f3780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19f3780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19f3780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19f3780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19f3780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1811620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3272.000000 -1166.500000) translate(0,16)">小江河一级电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1843b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -745.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1843b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -745.000000) translate(0,33)">SF11-12500/121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1843b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -745.000000) translate(0,51)">121±2×2.5%/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1843b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -745.000000) translate(0,69)">12500/12500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1843b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -745.000000) translate(0,87)">YN，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1843b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -745.000000) translate(0,105)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a67e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4236.000000 -986.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a134b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4062.000000 -703.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a806b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3522.000000 -476.000000) translate(0,15)">6kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a80a10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4378.000000 -120.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a80b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -125.000000) translate(0,15)">1号、2号发电机参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a80b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -125.000000) translate(0,33)">SFW5000-6/1730</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a80b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -125.000000) translate(0,51)">5000kW  6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a80b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -125.000000) translate(0,69)">cos∮=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a80b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4157.000000 -125.000000) translate(0,87)">In=572.7A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_1a38e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -219.000000) translate(0,14)">7822008</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_1a38e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -219.000000) translate(0,31)">18087871788</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ad7870" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3988.000000 -885.000000) translate(0,15)">110kVGIS组合电气</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a24790" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4012.000000 -182.000000) translate(0,12)">1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a24f80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4000.500000 -167.000000) translate(0,12)">发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a25b10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4012.500000 -152.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a25f70" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4053.500000 -178.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a261b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4064.500000 -163.000000) translate(0,12)">励磁TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a74f50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4122.500000 -156.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a752b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4478.000000 -182.000000) translate(0,12)">2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75a20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4466.500000 -167.000000) translate(0,12)">发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75ca0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4478.500000 -152.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a75ee0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4516.500000 -178.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a76120" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4527.500000 -163.000000) translate(0,12)">励磁TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a76360" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4585.500000 -156.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a617a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4653.000000 -915.000000) translate(0,15)">6kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19e7480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4838.000000 -932.000000) translate(0,15)">10kV新树线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19e7480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4838.000000 -932.000000) translate(0,33)">普角支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19e8640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4815.000000 -821.000000) translate(0,15)">施工变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19e8ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4610.000000 -820.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19e9740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4731.000000 -703.000000) translate(0,15)">自动切换装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19fa130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4754.000000 -593.000000) translate(0,15)">0.4kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19fa810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -652.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19faa50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4229.000000 -866.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19fac90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -933.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19fafd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -991.000000) translate(0,12)">K1017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19fb850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -798.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19fbcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.000000 -918.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19fbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4162.000000 -853.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19fc130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4228.000000 -542.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19fc370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -616.000000) translate(0,12)">60167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a22190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3651.000000 -454.000000) translate(0,12)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a22960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3828.000000 -430.000000) translate(0,12)">6311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a15fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3766.000000 -389.000000) translate(0,12)">63167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b3f940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3966.000000 -428.000000) translate(0,12)">632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b3ff70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3979.000000 -322.000000) translate(0,12)">6321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b401b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -322.000000) translate(0,12)">6322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f5300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4434.000000 -428.000000) translate(0,12)">633</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f5930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4447.000000 -322.000000) translate(0,12)">6331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19f5b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -322.000000) translate(0,12)">6332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f8f920" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3597.000000 -294.000000) translate(0,15)">6kVⅠ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f8fd40" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3765.000000 -237.000000) translate(0,15)">6kV1号站用变</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="3812" x2="3816" y1="-272" y2="-276"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="3816" x2="3821" y1="-276" y2="-272"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="3816" x2="3816" y1="-281" y2="-276"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4084" x2="4088" y1="-213" y2="-217"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4088" x2="4093" y1="-217" y2="-213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4088" x2="4088" y1="-222" y2="-217"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4084" x2="4088" y1="-190" y2="-194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4088" x2="4093" y1="-194" y2="-190"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4088" x2="4088" y1="-199" y2="-194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4141" x2="4145" y1="-191" y2="-195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4145" x2="4150" y1="-195" y2="-191"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4145" x2="4145" y1="-200" y2="-195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4543" x2="4547" y1="-213" y2="-217"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4547" x2="4552" y1="-217" y2="-213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4547" x2="4547" y1="-222" y2="-217"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4543" x2="4547" y1="-190" y2="-194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4547" x2="4552" y1="-194" y2="-190"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4547" x2="4547" y1="-199" y2="-194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4600" x2="4604" y1="-191" y2="-195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4604" x2="4609" y1="-195" y2="-191"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(154,205,50)" stroke-width="0.306122" x1="4604" x2="4604" y1="-200" y2="-195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.306122" x1="4879" x2="4883" y1="-820" y2="-824"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.306122" x1="4883" x2="4888" y1="-824" y2="-820"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.306122" x1="4883" x2="4883" y1="-829" y2="-824"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4883" x2="4883" y1="-891" y2="-838"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4883" x2="4883" y1="-790" y2="-712"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.9" x1="4892" x2="4883" y1="-721" y2="-712"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.9" x1="4883" x2="4874" y1="-704" y2="-713"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.9" x1="4892" x2="4883" y1="-713" y2="-704"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.9" x1="4883" x2="4874" y1="-712" y2="-721"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4883" x2="4883" y1="-704" y2="-681"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.306122" x1="4700" x2="4704" y1="-820" y2="-824"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.306122" x1="4704" x2="4709" y1="-824" y2="-820"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.306122" x1="4704" x2="4704" y1="-829" y2="-824"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4704" x2="4704" y1="-891" y2="-838"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4704" x2="4704" y1="-790" y2="-712"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.9" x1="4713" x2="4704" y1="-721" y2="-712"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.9" x1="4704" x2="4695" y1="-704" y2="-713"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.9" x1="4713" x2="4704" y1="-713" y2="-704"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.9" x1="4704" x2="4695" y1="-712" y2="-721"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4704" x2="4704" y1="-704" y2="-681"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1.30018" x1="4658" x2="4780" y1="-681" y2="-681"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1.30018" x1="4804" x2="4926" y1="-681" y2="-681"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.9" x1="4803" x2="4794" y1="-639" y2="-630"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.9" x1="4794" x2="4785" y1="-622" y2="-631"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.9" x1="4803" x2="4794" y1="-631" y2="-622"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.9" x1="4794" x2="4785" y1="-630" y2="-639"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4794" x2="4794" y1="-622" y2="-599"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4794" x2="4786" y1="-664" y2="-681"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4794" x2="4794" y1="-630" y2="-664"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.85405" x1="4658" x2="4926" y1="-599" y2="-599"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="4103" x2="4128" y1="-671" y2="-671"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-114386">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.000000 -837.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21694" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_101BK"/>
     <cge:Meas_Ref ObjectId="114386"/>
    <cge:TPSR_Ref TObjectID="21694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114430">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 -514.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21704" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_601BK"/>
     <cge:Meas_Ref ObjectId="114430"/>
    <cge:TPSR_Ref TObjectID="21704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114436">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 -399.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21713" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_632BK"/>
     <cge:Meas_Ref ObjectId="114436"/>
    <cge:TPSR_Ref TObjectID="21713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114440">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4416.000000 -399.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21720" ObjectName="SW-CX_XJHYJ.CX_XJHYJ_633BK"/>
     <cge:Meas_Ref ObjectId="114440"/>
    <cge:TPSR_Ref TObjectID="21720"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4220,-1096 4220,-1053 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4220,-1096 4220,-1053 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_187c910">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 -1030.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a67f80">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.067797 4150.000000 -659.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a12dc0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4122.000000 -659.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a9d770">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3802.000000 -242.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a9e310">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3806.000000 -299.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a8eb60">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.000000 -183.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a69210">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4533.000000 -183.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a20610">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4214.000000 -595.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a20fa0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.050847 4265.000000 -523.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a16250">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.000000 -272.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bfcb20">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 -248.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bfe750">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -247.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c003c0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 -272.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c00e40">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4590.000000 -161.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c01540">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4131.000000 -161.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c03450">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.050847 3665.000000 -337.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f91930">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.050847 3905.000000 -289.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f92430">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.050847 4370.000000 -291.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-114383" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -886.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114383" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21694"/>
     <cge:Term_Ref ObjectID="30364"/>
    <cge:TPSR_Ref TObjectID="21694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-114384" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -886.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21694"/>
     <cge:Term_Ref ObjectID="30364"/>
    <cge:TPSR_Ref TObjectID="21694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-114382" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -886.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114382" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21694"/>
     <cge:Term_Ref ObjectID="30364"/>
    <cge:TPSR_Ref TObjectID="21694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-114381" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4370.000000 -886.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114381" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21694"/>
     <cge:Term_Ref ObjectID="30364"/>
    <cge:TPSR_Ref TObjectID="21694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-114406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -576.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21704"/>
     <cge:Term_Ref ObjectID="30378"/>
    <cge:TPSR_Ref TObjectID="21704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-114407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -576.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21704"/>
     <cge:Term_Ref ObjectID="30378"/>
    <cge:TPSR_Ref TObjectID="21704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-114405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -576.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21704"/>
     <cge:Term_Ref ObjectID="30378"/>
    <cge:TPSR_Ref TObjectID="21704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-114404" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -576.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114404" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21704"/>
     <cge:Term_Ref ObjectID="30378"/>
    <cge:TPSR_Ref TObjectID="21704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-114420" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3969.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114420" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21713"/>
     <cge:Term_Ref ObjectID="30396"/>
    <cge:TPSR_Ref TObjectID="21713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-114421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3969.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114421" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21713"/>
     <cge:Term_Ref ObjectID="30396"/>
    <cge:TPSR_Ref TObjectID="21713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-114419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3969.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114419" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21713"/>
     <cge:Term_Ref ObjectID="30396"/>
    <cge:TPSR_Ref TObjectID="21713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-114418" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3969.000000 -73.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114418" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21713"/>
     <cge:Term_Ref ObjectID="30396"/>
    <cge:TPSR_Ref TObjectID="21713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-114426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -73.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21720"/>
     <cge:Term_Ref ObjectID="30410"/>
    <cge:TPSR_Ref TObjectID="21720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-114427" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -73.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114427" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21720"/>
     <cge:Term_Ref ObjectID="30410"/>
    <cge:TPSR_Ref TObjectID="21720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-114425" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -73.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114425" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21720"/>
     <cge:Term_Ref ObjectID="30410"/>
    <cge:TPSR_Ref TObjectID="21720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-114424" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -73.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114424" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21720"/>
     <cge:Term_Ref ObjectID="30410"/>
    <cge:TPSR_Ref TObjectID="21720"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="173" x="3246" y="-1177"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="4883" cy="-803" fill="none" fillStyle="0" r="13.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4883" cy="-824" fill="none" fillStyle="0" r="14" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4704" cy="-803" fill="none" fillStyle="0" r="13.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4704" cy="-824" fill="none" fillStyle="0" r="14" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4704" cy="-681" fill="rgb(0,255,0)" fillStyle="1" r="3" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4883" cy="-681" fill="rgb(0,255,0)" fillStyle="1" r="3" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="4794" cy="-599" fill="rgb(0,255,0)" fillStyle="1" r="3" stroke="rgb(0,255,0)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a46f90">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4240.000000 -985.000000)" xlink:href="#voltageTransformer:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a46860">
    <use class="BV-3KV" transform="matrix(0.512195 -0.000000 0.000000 -0.523810 4003.000000 -184.000000)" xlink:href="#voltageTransformer:shape73"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a4e290">
    <use class="BV-3KV" transform="matrix(0.512195 -0.000000 0.000000 -0.523810 4471.000000 -184.000000)" xlink:href="#voltageTransformer:shape73"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c01c40">
    <use class="BV-3KV" transform="matrix(0.512195 -0.000000 0.000000 -0.535714 3619.000000 -304.000000)" xlink:href="#voltageTransformer:shape73"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-114398" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3249.000000 -1011.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114398" ObjectName="CX_XJHYJ:CX_XJHYJ_GG_P_0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-114399" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3249.000000 -970.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114399" ObjectName="CX_XJHYJ:CX_XJHYJ_GG_Q_1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-114401" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3604.000000 -547.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114401" ObjectName="CX_XJHYJ:CX_XJHYJ_601BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-114402" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3604.000000 -532.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114402" ObjectName="CX_XJHYJ:CX_XJHYJ_601BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-114403" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3604.000000 -518.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114403" ObjectName="CX_XJHYJ:CX_XJHYJ_601BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-114400" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3604.000000 -503.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114400" ObjectName="CX_XJHYJ:CX_XJHYJ_601BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-114378" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -960.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114378" ObjectName="CX_XJHYJ:CX_XJHYJ_101BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-114379" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -945.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114379" ObjectName="CX_XJHYJ:CX_XJHYJ_101BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-114380" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -931.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114380" ObjectName="CX_XJHYJ:CX_XJHYJ_101BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-114377" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -916.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="114377" ObjectName="CX_XJHYJ:CX_XJHYJ_101BK_Uab"/>
    </metadata>
   </g>
  </g><g id="Polygon_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-249 3816,-258 3821,-249 3811,-249 " stroke="rgb(154,205,50)" stroke-width="0.6"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4140,-168 4145,-177 4150,-168 4140,-168 " stroke="rgb(154,205,50)" stroke-width="0.6"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4599,-168 4604,-177 4609,-168 4599,-168 " stroke="rgb(154,205,50)" stroke-width="0.6"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-797 4883,-806 4888,-797 4878,-797 " stroke="rgb(0,255,0)" stroke-width="0.6"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-874 4883,-865 4888,-874 4878,-874 " stroke="rgb(0,255,0)" stroke-width="0.6"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4699,-797 4704,-806 4709,-797 4699,-797 " stroke="rgb(0,255,0)" stroke-width="0.6"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4699,-874 4704,-865 4709,-874 4699,-874 " stroke="rgb(0,255,0)" stroke-width="0.6"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="173" x="3246" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="173" x="3246" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1ac1930" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.000000 -954.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a0fc50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.000000 -886.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a105f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.000000 -821.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a92bd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4134.000000 -584.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a90870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 -357.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_XJHYJ"/>
</svg>