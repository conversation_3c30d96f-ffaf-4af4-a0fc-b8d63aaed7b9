<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-227" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-755 -1343 2816 1319">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape163">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <polyline points="34,21 34,9 19,9 " stroke-width="1"/>
    <polyline points="35,33 35,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="29" y1="28" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="44" y1="28" y2="28"/>
    <rect height="9" stroke-width="1" width="5" x="32" y="21"/>
    <ellipse cx="8" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="7" x2="7" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="10" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="4" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="10" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="9" x2="9" y1="12" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape192">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="26" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="5,19 17,9 5,0 5,19 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,1 26,10 38,19 38,1 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape33">
    <ellipse cx="7" cy="23" fillStyle="0" rx="6.5" ry="6" stroke-width="0.45993"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="8" x2="8" y1="29" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="29" x2="28" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="30" x2="29" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="29" x2="29" y1="34" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="29" x2="8" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="29" x2="29" y1="16" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="34" x2="24" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="31" x2="27" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.212121" x1="30" x2="28" y1="2" y2="2"/>
    <rect height="14" stroke-width="0.571429" width="6" x="26" y="16"/>
    <circle cx="14" cy="17" fillStyle="0" r="6.5" stroke-width="0.45993"/>
    <circle cx="7" cy="13" fillStyle="0" r="6.5" stroke-width="0.45993"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <rect height="16" stroke-width="1" width="31" x="5" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="36" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape95_0">
    <ellipse cx="14" cy="43" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="13" y1="38" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="13" y1="7" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="9" y1="0" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="31" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="42" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="39" x2="35" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="46" x2="28" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="40" x2="33" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="46" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="42" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="38" y2="42"/>
   </symbol>
   <symbol id="transformer2:shape95_1">
    <polyline DF8003:Layer="PUBLIC" points="12,70 8,61 18,61 12,70 "/>
    <circle cx="13" cy="62" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.1743"/>
    <polyline points="58,100 64,100 " stroke-width="1.1743"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.1743"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_281f190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_281fba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28204e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2821190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28223c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2823060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2823c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2824600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2404bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2404bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2827660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2827660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2829170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2829170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2829e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_282bac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_282c710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_282d5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_282ded0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_282f8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28300a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28306c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2831080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2832200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2832b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2833670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2834030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2835520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2836040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2837090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2837ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2846110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2839290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2839ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_283b450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1329" width="2826" x="-760" y="-1348"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="2045" x2="2045" y1="-636" y2="-621"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-186967">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 668.241796 -1071.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28328" ObjectName="SW-DY_ST.DY_ST_361BK"/>
     <cge:Meas_Ref ObjectId="186967"/>
    <cge:TPSR_Ref TObjectID="28328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186995">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 669.241796 -827.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28334" ObjectName="SW-DY_ST.DY_ST_301BK"/>
     <cge:Meas_Ref ObjectId="186995"/>
    <cge:TPSR_Ref TObjectID="28334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187025">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 667.241796 -660.869565)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28337" ObjectName="SW-DY_ST.DY_ST_001BK"/>
     <cge:Meas_Ref ObjectId="187025"/>
    <cge:TPSR_Ref TObjectID="28337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187086">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.212271 -426.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28343" ObjectName="SW-DY_ST.DY_ST_062BK"/>
     <cge:Meas_Ref ObjectId="187086"/>
    <cge:TPSR_Ref TObjectID="28343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187109">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 924.212271 -423.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28346" ObjectName="SW-DY_ST.DY_ST_063BK"/>
     <cge:Meas_Ref ObjectId="187109"/>
    <cge:TPSR_Ref TObjectID="28346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1095.212271 -421.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28349" ObjectName="SW-DY_ST.DY_ST_064BK"/>
     <cge:Meas_Ref ObjectId="187132"/>
    <cge:TPSR_Ref TObjectID="28349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187201">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1240.000000 -473.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28358" ObjectName="SW-DY_ST.DY_ST_012BK"/>
     <cge:Meas_Ref ObjectId="187201"/>
    <cge:TPSR_Ref TObjectID="28358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187178">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1846.212271 -418.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28355" ObjectName="SW-DY_ST.DY_ST_082BK"/>
     <cge:Meas_Ref ObjectId="187178"/>
    <cge:TPSR_Ref TObjectID="28355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187155">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1527.212271 -419.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28352" ObjectName="SW-DY_ST.DY_ST_081BK"/>
     <cge:Meas_Ref ObjectId="187155"/>
    <cge:TPSR_Ref TObjectID="28352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -101.000000 -718.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -92.000000 -631.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -93.000000 -506.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -96.000000 -394.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -94.000000 -285.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -95.000000 -186.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_ST.DY_ST_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="356,-961 1985,-961 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28324" ObjectName="BS-DY_ST.DY_ST_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   <polyline fill="none" opacity="0" points="356,-961 1985,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_ST.DY_ST_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="351,-570 1216,-570 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28325" ObjectName="BS-DY_ST.DY_ST_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   <polyline fill="none" opacity="0" points="351,-570 1216,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_ST.DY_ST_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1308,-568 1973,-568 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28326" ObjectName="BS-DY_ST.DY_ST_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   <polyline fill="none" opacity="0" points="1308,-568 1973,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-164,-125 -164,-765 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="-164,-125 -164,-765 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_ST.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 421.761290 -271.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34240" ObjectName="EC-DY_ST.062Ld"/>
    <cge:TPSR_Ref TObjectID="34240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ST.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 923.761290 -268.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34241" ObjectName="EC-DY_ST.063Ld"/>
    <cge:TPSR_Ref TObjectID="34241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ST.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1094.761290 -266.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34242" ObjectName="EC-DY_ST.064Ld"/>
    <cge:TPSR_Ref TObjectID="34242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ST.082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1845.761290 -263.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34244" ObjectName="EC-DY_ST.082Ld"/>
    <cge:TPSR_Ref TObjectID="34244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_ST.081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1526.761290 -264.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34243" ObjectName="EC-DY_ST.081Ld"/>
    <cge:TPSR_Ref TObjectID="34243"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1eba590" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 775.241796 -1114.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e77c50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 779.241796 -1044.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e53fe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.241796 -1201.085366)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ea3cd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1755.881701 -1082.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ea7330" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.241796 -886.130435)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d51300" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 43.500000 -607.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e3f1d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 42.500000 -482.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d92e00" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 39.500000 -370.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d9d020" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 41.500000 -261.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1da7200" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 40.500000 -162.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1eb2f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-1120 780,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28332@0" ObjectIDZND0="g_1eba590@0" Pin0InfoVect0LinkObjId="g_1eba590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186972_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-1120 780,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e78400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,-1050 784,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28330@0" ObjectIDZND0="g_1e77c50@0" Pin0InfoVect0LinkObjId="g_1e77c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="770,-1050 784,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e785f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-1050 677,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28330@1" ObjectIDZND0="28328@x" ObjectIDZND1="28329@x" Pin0InfoVect0LinkObjId="SW-186967_0" Pin0InfoVect1LinkObjId="SW-186969_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186970_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="734,-1050 677,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e78ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-1079 677,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28328@0" ObjectIDZND0="28330@x" ObjectIDZND1="28329@x" Pin0InfoVect0LinkObjId="SW-186970_0" Pin0InfoVect1LinkObjId="SW-186969_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186967_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="677,-1079 677,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e790c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-1050 677,-1029 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="28330@x" ObjectIDND1="28328@x" ObjectIDZND0="28329@1" Pin0InfoVect0LinkObjId="SW-186969_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-186970_0" Pin1InfoVect1LinkObjId="SW-186967_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="677,-1050 677,-1029 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e54790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="772,-1207 786,-1207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28333@0" ObjectIDZND0="g_1e53fe0@0" Pin0InfoVect0LinkObjId="g_1e53fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186973_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="772,-1207 786,-1207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e54980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="736,-1207 679,-1207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="28333@1" ObjectIDZND0="28331@x" ObjectIDZND1="g_1e55450@0" ObjectIDZND2="38089@1" Pin0InfoVect0LinkObjId="SW-186971_0" Pin0InfoVect1LinkObjId="g_1e55450_0" Pin0InfoVect2LinkObjId="g_1f191f0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186973_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="736,-1207 679,-1207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e55260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-1185 677,-1207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="28331@1" ObjectIDZND0="28333@x" ObjectIDZND1="g_1e55450@0" ObjectIDZND2="38089@1" Pin0InfoVect0LinkObjId="SW-186973_0" Pin0InfoVect1LinkObjId="g_1e55450_0" Pin0InfoVect2LinkObjId="g_1f191f0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186971_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="677,-1185 677,-1207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f191f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-1219 677,-1248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1e55450@0" ObjectIDND1="28333@x" ObjectIDND2="28331@x" ObjectIDZND0="38089@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e55450_0" Pin1InfoVect1LinkObjId="SW-186973_0" Pin1InfoVect2LinkObjId="SW-186971_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="677,-1219 677,-1248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f19ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-1231 638,-1219 677,-1219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_1e55450@0" ObjectIDZND0="28333@x" ObjectIDZND1="28331@x" ObjectIDZND2="38089@1" Pin0InfoVect0LinkObjId="SW-186973_0" Pin0InfoVect1LinkObjId="SW-186971_0" Pin0InfoVect2LinkObjId="g_1f191f0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e55450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="638,-1231 638,-1219 677,-1219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f19cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-1219 677,-1207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1e55450@0" ObjectIDND1="38089@1" ObjectIDZND0="28333@x" ObjectIDZND1="28331@x" Pin0InfoVect0LinkObjId="SW-186973_0" Pin0InfoVect1LinkObjId="SW-186971_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e55450_0" Pin1InfoVect1LinkObjId="g_1f191f0_1" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="677,-1219 677,-1207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e81e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-1046 1200,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-1046 1200,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e82380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-1001 1200,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="28324@0" Pin0InfoVect0LinkObjId="g_1e95fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-1001 1200,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea4480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1746,-1088 1760,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28340@0" ObjectIDZND0="g_1ea3cd0@0" Pin0InfoVect0LinkObjId="g_1ea3cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187075_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1746,-1088 1760,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea4670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1710,-1088 1662,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28340@1" ObjectIDZND0="28339@x" ObjectIDZND1="g_1ed7180@0" ObjectIDZND2="g_1ea4c40@0" Pin0InfoVect0LinkObjId="SW-187074_0" Pin0InfoVect1LinkObjId="g_1ed7180_0" Pin0InfoVect2LinkObjId="g_1ea4c40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187075_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1710,-1088 1662,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea4860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1663,-1048 1663,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28339@1" ObjectIDZND0="28340@x" ObjectIDZND1="g_1ed7180@0" ObjectIDZND2="g_1ea4c40@0" Pin0InfoVect0LinkObjId="SW-187075_0" Pin0InfoVect1LinkObjId="g_1ed7180_0" Pin0InfoVect2LinkObjId="g_1ea4c40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187074_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1663,-1048 1663,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea4a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1664,-1178 1664,-1163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1e80980@0" ObjectIDZND0="g_1ea4c40@1" Pin0InfoVect0LinkObjId="g_1ea4c40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e80980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1664,-1178 1664,-1163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea52b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1663,-1102 1608,-1102 1608,-1127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28340@x" ObjectIDND1="28339@x" ObjectIDND2="g_1ea4c40@0" ObjectIDZND0="g_1ed7180@0" Pin0InfoVect0LinkObjId="g_1ed7180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-187075_0" Pin1InfoVect1LinkObjId="SW-187074_0" Pin1InfoVect2LinkObjId="g_1ea4c40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1663,-1102 1608,-1102 1608,-1127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ed6da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1663,-1088 1663,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="28340@x" ObjectIDND1="28339@x" ObjectIDZND0="g_1ed7180@0" ObjectIDZND1="g_1ea4c40@0" Pin0InfoVect0LinkObjId="g_1ed7180_0" Pin0InfoVect1LinkObjId="g_1ea4c40_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-187075_0" Pin1InfoVect1LinkObjId="SW-187074_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1663,-1088 1663,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ed6f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1663,-1102 1663,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1ed7180@0" ObjectIDND1="28340@x" ObjectIDND2="28339@x" ObjectIDZND0="g_1ea4c40@0" Pin0InfoVect0LinkObjId="g_1ea4c40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ed7180_0" Pin1InfoVect1LinkObjId="SW-187075_0" Pin1InfoVect2LinkObjId="SW-187074_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1663,-1102 1663,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e95fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1663,-1012 1663,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28339@0" ObjectIDZND0="28324@0" Pin0InfoVect0LinkObjId="g_1e82380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1663,-1012 1663,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f3ba70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,-730 676,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="28361@0" ObjectIDZND0="28337@1" Pin0InfoVect0LinkObjId="SW-187025_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e22da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="676,-730 676,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e22da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="678,-835 678,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="28334@0" ObjectIDZND0="28361@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="678,-835 678,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee3550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-537 431,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28344@1" ObjectIDZND0="28325@0" Pin0InfoVect0LinkObjId="g_1ef1530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="431,-537 431,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee3770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-501 431,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28344@0" ObjectIDZND0="28343@1" Pin0InfoVect0LinkObjId="SW-187086_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="431,-501 431,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee0920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="467,-326 467,-341 431,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1ee29e0@0" ObjectIDZND0="28345@x" ObjectIDZND1="34240@x" Pin0InfoVect0LinkObjId="SW-187089_0" Pin0InfoVect1LinkObjId="EC-DY_ST.062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ee29e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="467,-326 467,-341 431,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee0b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-359 431,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28345@0" ObjectIDZND0="g_1ee29e0@0" ObjectIDZND1="34240@x" Pin0InfoVect0LinkObjId="g_1ee29e0_0" Pin0InfoVect1LinkObjId="EC-DY_ST.062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187089_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="431,-359 431,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ee0d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-341 431,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1ee29e0@0" ObjectIDND1="28345@x" ObjectIDZND0="34240@0" Pin0InfoVect0LinkObjId="EC-DY_ST.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ee29e0_0" Pin1InfoVect1LinkObjId="SW-187089_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="431,-341 431,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea9d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="755,-892 769,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28336@0" ObjectIDZND0="g_1ea7330@0" Pin0InfoVect0LinkObjId="g_1ea7330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186998_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-892 769,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ef1530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="933,-534 933,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28347@1" ObjectIDZND0="28325@0" Pin0InfoVect0LinkObjId="g_1ee3550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187111_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="933,-534 933,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ef1750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="933,-498 933,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28347@0" ObjectIDZND0="28346@1" Pin0InfoVect0LinkObjId="SW-187109_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187111_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="933,-498 933,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ecbc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="969,-323 969,-338 933,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1ef09c0@0" ObjectIDZND0="28348@x" ObjectIDZND1="34241@x" Pin0InfoVect0LinkObjId="SW-187112_0" Pin0InfoVect1LinkObjId="EC-DY_ST.063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ef09c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="969,-323 969,-338 933,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ecbe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="933,-356 933,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28348@0" ObjectIDZND0="g_1ef09c0@0" ObjectIDZND1="34241@x" Pin0InfoVect0LinkObjId="g_1ef09c0_0" Pin0InfoVect1LinkObjId="EC-DY_ST.063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="933,-356 933,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ecc080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="933,-338 933,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1ef09c0@0" ObjectIDND1="28348@x" ObjectIDZND0="34241@0" Pin0InfoVect0LinkObjId="EC-DY_ST.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ef09c0_0" Pin1InfoVect1LinkObjId="SW-187112_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="933,-338 933,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dff4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1104,-532 1104,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28350@1" ObjectIDZND0="28325@0" Pin0InfoVect0LinkObjId="g_1ee3550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187134_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1104,-532 1104,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dff6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1104,-496 1104,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28350@0" ObjectIDZND0="28349@1" Pin0InfoVect0LinkObjId="SW-187132_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187134_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1104,-496 1104,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dff8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1104,-415 1104,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="28351@1" Pin0InfoVect0LinkObjId="SW-187135_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1104,-415 1104,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e74970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,-321 1140,-336 1104,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1e99cd0@0" ObjectIDZND0="28351@x" ObjectIDZND1="34242@x" Pin0InfoVect0LinkObjId="SW-187135_0" Pin0InfoVect1LinkObjId="EC-DY_ST.064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e99cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1140,-321 1140,-336 1104,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e74bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1104,-354 1104,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28351@0" ObjectIDZND0="g_1e99cd0@0" ObjectIDZND1="34242@x" Pin0InfoVect0LinkObjId="g_1e99cd0_0" Pin0InfoVect1LinkObjId="EC-DY_ST.064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187135_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1104,-354 1104,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e74e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1104,-336 1104,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1e99cd0@0" ObjectIDND1="28351@x" ObjectIDZND0="34242@0" Pin0InfoVect0LinkObjId="EC-DY_ST.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e99cd0_0" Pin1InfoVect1LinkObjId="SW-187135_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1104,-336 1104,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dea590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1322,-495 1322,-483 1276,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28360@0" ObjectIDZND0="28358@0" Pin0InfoVect0LinkObjId="SW-187201_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187204_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1322,-495 1322,-483 1276,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dea7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,-500 1198,-483 1249,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28359@0" ObjectIDZND0="28358@1" Pin0InfoVect0LinkObjId="SW-187201_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1198,-500 1198,-483 1249,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1deaa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,-536 1198,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28359@1" ObjectIDZND0="28325@0" Pin0InfoVect0LinkObjId="g_1ee3550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187203_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1198,-536 1198,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1deacb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1322,-531 1322,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28360@1" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_1ec0cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187204_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1322,-531 1322,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ec0cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1855,-529 1855,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28356@1" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_1deacb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1855,-529 1855,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ec0f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1855,-493 1855,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28356@0" ObjectIDZND0="28355@1" Pin0InfoVect0LinkObjId="SW-187178_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1855,-493 1855,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ec3380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1891,-318 1891,-333 1855,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1e38300@0" ObjectIDZND0="28357@x" ObjectIDZND1="34244@x" Pin0InfoVect0LinkObjId="SW-187181_0" Pin0InfoVect1LinkObjId="EC-DY_ST.082Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e38300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1891,-318 1891,-333 1855,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ec35e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1855,-351 1855,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28357@0" ObjectIDZND0="g_1e38300@0" ObjectIDZND1="34244@x" Pin0InfoVect0LinkObjId="g_1e38300_0" Pin0InfoVect1LinkObjId="EC-DY_ST.082Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187181_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1855,-351 1855,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e8caf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1855,-333 1855,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1e38300@0" ObjectIDND1="28357@x" ObjectIDZND0="34244@0" Pin0InfoVect0LinkObjId="EC-DY_ST.082Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e38300_0" Pin1InfoVect1LinkObjId="SW-187181_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1855,-333 1855,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f13db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1536,-530 1536,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28353@1" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_1deacb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187157_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1536,-530 1536,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f14010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1536,-494 1536,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28353@0" ObjectIDZND0="28352@1" Pin0InfoVect0LinkObjId="SW-187155_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187157_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1536,-494 1536,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f163f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1572,-319 1572,-334 1536,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1f11650@0" ObjectIDZND0="28354@x" ObjectIDZND1="34243@x" Pin0InfoVect0LinkObjId="SW-187158_0" Pin0InfoVect1LinkObjId="EC-DY_ST.081Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f11650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1572,-319 1572,-334 1536,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f16670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1536,-352 1536,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28354@0" ObjectIDZND0="g_1f11650@0" ObjectIDZND1="34243@x" Pin0InfoVect0LinkObjId="g_1f11650_0" Pin0InfoVect1LinkObjId="EC-DY_ST.081Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187158_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1536,-352 1536,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e39190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1536,-334 1536,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1f11650@0" ObjectIDND1="28354@x" ObjectIDZND0="34243@0" Pin0InfoVect0LinkObjId="EC-DY_ST.081Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f11650_0" Pin1InfoVect1LinkObjId="SW-187158_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1536,-334 1536,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dfc250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="892,-774 892,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1e741b0@0" ObjectIDZND0="g_1dfc4b0@1" Pin0InfoVect0LinkObjId="g_1dfc4b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e741b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="892,-774 892,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dfd950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="891,-689 855,-689 855,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1dfc4b0@0" ObjectIDND1="28341@x" ObjectIDZND0="g_1dfcc90@0" Pin0InfoVect0LinkObjId="g_1dfcc90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1dfc4b0_0" Pin1InfoVect1LinkObjId="SW-187078_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="891,-689 855,-689 855,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dfe3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="891,-714 891,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1dfc4b0@0" ObjectIDZND0="g_1dfcc90@0" ObjectIDZND1="28341@x" Pin0InfoVect0LinkObjId="g_1dfcc90_0" Pin0InfoVect1LinkObjId="SW-187078_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dfc4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="891,-714 891,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dfe650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="891,-689 891,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1dfcc90@0" ObjectIDND1="g_1dfc4b0@0" ObjectIDZND0="28341@1" Pin0InfoVect0LinkObjId="SW-187078_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1dfcc90_0" Pin1InfoVect1LinkObjId="g_1dfc4b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="891,-689 891,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ed4370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1519,-768 1519,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1e71510@0" ObjectIDZND0="g_1ed45d0@1" Pin0InfoVect0LinkObjId="g_1ed45d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e71510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1519,-768 1519,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8e3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1518,-683 1482,-683 1482,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1ed45d0@0" ObjectIDND1="28342@x" ObjectIDZND0="g_1ed4ed0@0" Pin0InfoVect0LinkObjId="g_1ed4ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ed45d0_0" Pin1InfoVect1LinkObjId="SW-187079_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1518,-683 1482,-683 1482,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8e600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1518,-708 1518,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1ed45d0@0" ObjectIDZND0="g_1ed4ed0@0" ObjectIDZND1="28342@x" Pin0InfoVect0LinkObjId="g_1ed4ed0_0" Pin0InfoVect1LinkObjId="SW-187079_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ed45d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1518,-708 1518,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8e860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1518,-683 1518,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1ed4ed0@0" ObjectIDND1="g_1ed45d0@0" ObjectIDZND0="28342@1" Pin0InfoVect0LinkObjId="SW-187079_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ed4ed0_0" Pin1InfoVect1LinkObjId="g_1ed45d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1518,-683 1518,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8eac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1518,-602 1518,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28342@0" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_1deacb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187079_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1518,-602 1518,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1efc200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-163,-727 -136,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-163,-727 -136,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d4e200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-109,-727 -91,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-109,-727 -91,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d4f210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-25,-727 5,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d4e460@0" ObjectIDND1="0@x" ObjectIDZND0="g_1d4f6d0@1" Pin0InfoVect0LinkObjId="g_1d4f6d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d4e460_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-25,-727 5,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d4f470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-25,-727 -25,-755 -10,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d4f6d0@0" ObjectIDND1="0@x" ObjectIDZND0="g_1d4e460@0" Pin0InfoVect0LinkObjId="g_1d4e460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d4f6d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-25,-727 -25,-755 -10,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d510a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-25,-640 5,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d502f0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1e2ae40@1" Pin0InfoVect0LinkObjId="g_1e2ae40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d502f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-25,-640 5,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d51d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-25,-640 -25,-667 -10,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1e2ae40@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1d502f0@0" Pin0InfoVect0LinkObjId="g_1d502f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e2ae40_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-25,-640 -25,-667 -10,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e2abe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11,-601 25,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1d51300@0" Pin0InfoVect0LinkObjId="g_1d51300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="11,-601 25,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e2cbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-25,-727 -55,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d4e460@0" ObjectIDND1="g_1d4f6d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d4e460_0" Pin1InfoVect1LinkObjId="g_1d4f6d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-25,-727 -55,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e2d470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="891,-608 891,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28341@0" ObjectIDZND0="28325@0" Pin0InfoVect0LinkObjId="g_1ee3550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187078_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="891,-608 891,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e2dc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,-596 676,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="28325@0" Pin0InfoVect0LinkObjId="g_1ee3550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="676,-596 676,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e2e430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="678,-941 678,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28335@1" ObjectIDZND0="28324@0" Pin0InfoVect0LinkObjId="g_1e82380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="678,-941 678,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e2f1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-993 677,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28329@0" ObjectIDZND0="28324@0" Pin0InfoVect0LinkObjId="g_1e82380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186969_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="677,-993 677,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e2f9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="44,-728 480,-728 480,-659 676,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_1d4f6d0@0" ObjectIDZND0="28337@x" ObjectIDZND1="28338@x" Pin0InfoVect0LinkObjId="SW-187025_0" Pin0InfoVect1LinkObjId="SW-187027_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d4f6d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="44,-728 480,-728 480,-659 676,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e2fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="44,-640 302,-640 302,-409 431,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1e2ae40@0" ObjectIDZND0="28345@x" Pin0InfoVect0LinkObjId="SW-187089_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e2ae40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="44,-640 302,-640 302,-409 431,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e2fed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-426 431,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_1e2ae40@0" ObjectIDZND1="28345@x" Pin0InfoVect0LinkObjId="g_1e2ae40_0" Pin0InfoVect1LinkObjId="SW-187089_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="431,-426 431,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e30130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-409 431,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1e2ae40@0" ObjectIDZND0="28345@1" Pin0InfoVect0LinkObjId="SW-187089_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e2ae40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="431,-409 431,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e31030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-25,-601 -25,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1d502f0@0" ObjectIDZND1="g_1e2ae40@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1d502f0_0" Pin0InfoVect1LinkObjId="g_1e2ae40_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-25,-601 -25,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e33050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-163,-640 -127,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-163,-640 -127,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e3d880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-100,-640 -82,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-100,-640 -82,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3e210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-46,-640 -24,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1d502f0@0" ObjectIDZND1="g_1e2ae40@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1d502f0_0" Pin0InfoVect1LinkObjId="g_1e2ae40_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-46,-640 -24,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3ef70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-26,-515 4,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_1e3e400@0" ObjectIDZND0="g_1e425f0@1" Pin0InfoVect0LinkObjId="g_1e425f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1e3e400_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-26,-515 4,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-26,-515 -26,-542 -11,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1e425f0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1e3e400@0" Pin0InfoVect0LinkObjId="g_1e3e400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e425f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-26,-515 -26,-542 -11,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e42390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10,-476 24,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1e3f1d0@0" Pin0InfoVect0LinkObjId="g_1e3f1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="10,-476 24,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e43210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-26,-476 -26,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1e425f0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_1e3e400@0" Pin0InfoVect0LinkObjId="g_1e425f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1e3e400_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-26,-476 -26,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e454e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-163,-515 -128,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-163,-515 -128,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d91930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-101,-515 -80,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-101,-515 -80,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d91b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-47,-515 -25,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1e425f0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_1e3e400@0" Pin0InfoVect0LinkObjId="g_1e425f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1e3e400_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-47,-515 -25,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d92ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-29,-403 1,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d91df0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1d96220@1" Pin0InfoVect0LinkObjId="g_1d96220_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d91df0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-29,-403 1,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d93890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-29,-403 -29,-430 -14,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d96220@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1d91df0@0" Pin0InfoVect0LinkObjId="g_1d91df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d96220_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-29,-403 -29,-430 -14,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d95fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="7,-364 21,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1d92e00@0" Pin0InfoVect0LinkObjId="g_1d92e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="7,-364 21,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d96e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-29,-364 -29,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1d91df0@0" ObjectIDZND1="g_1d96220@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1d91df0_0" Pin0InfoVect1LinkObjId="g_1d96220_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-29,-364 -29,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d99110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-163,-403 -131,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-163,-403 -131,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d9bb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-104,-403 -82,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-104,-403 -82,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9bd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-50,-403 -28,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1d91df0@0" ObjectIDZND1="g_1d96220@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1d91df0_0" Pin0InfoVect1LinkObjId="g_1d96220_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-50,-403 -28,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9cdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-27,-294 3,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1d9bfd0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1da0440@1" Pin0InfoVect0LinkObjId="g_1da0440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d9bfd0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-27,-294 3,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d9dab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-27,-294 -27,-321 -12,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1da0440@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1d9bfd0@0" Pin0InfoVect0LinkObjId="g_1d9bfd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1da0440_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-27,-294 -27,-321 -12,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1da01e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="9,-255 23,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1d9d020@0" Pin0InfoVect0LinkObjId="g_1d9d020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="9,-255 23,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da1060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-27,-255 -27,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1d9bfd0@0" ObjectIDZND1="g_1da0440@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1d9bfd0_0" Pin0InfoVect1LinkObjId="g_1da0440_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-27,-255 -27,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1da3330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-163,-294 -129,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-163,-294 -129,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1da5d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-102,-294 -80,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-102,-294 -80,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da5f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-48,-294 -26,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1d9bfd0@0" ObjectIDZND1="g_1da0440@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1d9bfd0_0" Pin0InfoVect1LinkObjId="g_1da0440_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-48,-294 -26,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1da6fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-28,-195 2,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_1da61f0@0" ObjectIDZND0="g_1dda360@1" Pin0InfoVect0LinkObjId="g_1dda360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1da61f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-28,-195 2,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dd79d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-28,-195 -28,-222 -13,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1dda360@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1da61f0@0" Pin0InfoVect0LinkObjId="g_1da61f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1dda360_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-28,-195 -28,-222 -13,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1dda100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="8,-156 22,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1da7200@0" Pin0InfoVect0LinkObjId="g_1da7200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="8,-156 22,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ddaf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-28,-156 -28,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1dda360@0" ObjectIDZND1="0@x" ObjectIDZND2="g_1da61f0@0" Pin0InfoVect0LinkObjId="g_1dda360_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1da61f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-28,-156 -28,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ddd250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-163,-195 -130,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-163,-195 -130,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ddfc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-103,-195 -81,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-103,-195 -81,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ddfeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-49,-195 -27,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1dda360@0" ObjectIDZND1="0@x" ObjectIDZND2="g_1da61f0@0" Pin0InfoVect0LinkObjId="g_1dda360_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_1da61f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-49,-195 -27,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d74220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-420 931,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_1e425f0@0" ObjectIDZND1="28348@x" Pin0InfoVect0LinkObjId="g_1e425f0_0" Pin0InfoVect1LinkObjId="SW-187112_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="931,-420 931,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d74cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="43,-515 43,-516 269,-516 269,-129 835,-129 835,-401 931,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1e425f0@0" ObjectIDZND0="28348@x" Pin0InfoVect0LinkObjId="SW-187112_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e425f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="43,-515 43,-516 269,-516 269,-129 835,-129 835,-401 931,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d74f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="933,-392 933,-399 931,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="28348@1" ObjectIDZND0="g_1e425f0@0" Pin0InfoVect0LinkObjId="g_1e425f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187112_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="933,-392 933,-399 931,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d775d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="40,-403 215,-403 215,-89 1456,-89 1456,-397 1536,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d96220@0" ObjectIDZND0="28354@x" Pin0InfoVect0LinkObjId="SW-187158_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d96220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="40,-403 215,-403 215,-89 1456,-89 1456,-397 1536,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d77f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1536,-413 1536,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_1d96220@0" ObjectIDZND1="28354@x" Pin0InfoVect0LinkObjId="g_1d96220_0" Pin0InfoVect1LinkObjId="SW-187158_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1536,-413 1536,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d78130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1536,-397 1536,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d96220@0" ObjectIDZND0="28354@1" Pin0InfoVect0LinkObjId="SW-187158_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d96220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1536,-397 1536,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d79730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="42,-294 171,-294 171,-55 1759,-55 1759,-394 1855,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1da0440@0" ObjectIDZND0="28357@x" Pin0InfoVect0LinkObjId="SW-187181_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1da0440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="42,-294 171,-294 171,-55 1759,-55 1759,-394 1855,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7a130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1855,-413 1855,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_1da0440@0" ObjectIDZND1="28357@x" Pin0InfoVect0LinkObjId="g_1da0440_0" Pin0InfoVect1LinkObjId="SW-187181_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1855,-413 1855,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7a360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1855,-394 1855,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1da0440@0" ObjectIDZND0="28357@1" Pin0InfoVect0LinkObjId="SW-187181_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1da0440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1855,-394 1855,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7af00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1769,-591 1769,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_23f50e0@0" ObjectIDZND0="28326@0" Pin0InfoVect0LinkObjId="g_1deacb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23f50e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1769,-591 1769,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7c630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1769,-645 1769,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_23f50e0@1" Pin0InfoVect0LinkObjId="g_23f50e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1769,-645 1769,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7d980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,-673 676,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="28337@0" ObjectIDZND0="g_1d4f6d0@0" ObjectIDZND1="28338@x" Pin0InfoVect0LinkObjId="g_1d4f6d0_0" Pin0InfoVect1LinkObjId="SW-187027_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187025_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="676,-673 676,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d7db70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,-659 676,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_1d4f6d0@0" ObjectIDND1="28337@x" ObjectIDZND0="28338@1" Pin0InfoVect0LinkObjId="SW-187027_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d4f6d0_0" Pin1InfoVect1LinkObjId="SW-187025_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="676,-659 676,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d81860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="730,-1120 677,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28332@1" ObjectIDZND0="28328@x" Pin0InfoVect0LinkObjId="SW-186967_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186972_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="730,-1120 677,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d822c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-1106 677,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28328@1" ObjectIDZND0="28332@x" Pin0InfoVect0LinkObjId="SW-186972_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186967_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="677,-1106 677,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d82520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-1120 677,-1129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" ObjectIDND0="28332@x" ObjectIDND1="28328@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-186972_0" Pin1InfoVect1LinkObjId="SW-186967_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="677,-1120 677,-1129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d82780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-892 678,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="28336@1" ObjectIDZND0="28335@x" Pin0InfoVect0LinkObjId="SW-186997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-892 678,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d83270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="678,-905 678,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="28335@0" ObjectIDZND0="28336@x" Pin0InfoVect0LinkObjId="SW-186998_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="678,-905 678,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d834d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="678,-892 678,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="28336@x" ObjectIDND1="28335@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-186998_0" Pin1InfoVect1LinkObjId="SW-186997_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="678,-892 678,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d83fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-1137 677,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" ObjectIDZND0="28331@x" ObjectIDZND1="g_1d851d0@0" Pin0InfoVect0LinkObjId="SW-186971_0" Pin0InfoVect1LinkObjId="g_1d851d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="677,-1137 677,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d84220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-1143 677,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d851d0@0" ObjectIDZND0="28331@0" Pin0InfoVect0LinkObjId="SW-186971_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d851d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="677,-1143 677,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d84d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="678,-862 678,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="28334@1" ObjectIDZND0="g_1d851d0@0" Pin0InfoVect0LinkObjId="g_1d851d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186995_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="678,-862 678,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d84f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="678,-869 678,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" ObjectIDND0="28334@x" ObjectIDND1="g_1d851d0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-186995_0" Pin1InfoVect1LinkObjId="g_1d851d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="678,-869 678,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f65970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="678,-872 440,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="28334@x" ObjectIDZND0="g_1d851d0@0" Pin0InfoVect0LinkObjId="g_1d851d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="678,-872 440,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f65bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="407,-872 341,-872 341,-1141 677,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d851d0@1" ObjectIDZND0="28331@x" Pin0InfoVect0LinkObjId="SW-186971_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d851d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="407,-872 341,-872 341,-1141 677,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f6a990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-197,-683 -163,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-197,-683 -163,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f6b1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-293,-683 -325,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1f678f0@0" ObjectIDZND0="g_1f66210@0" Pin0InfoVect0LinkObjId="g_1f66210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f678f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-293,-683 -325,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f6c1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-250,-640 -250,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1f6b420@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1f678f0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f678f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f6b420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-250,-640 -250,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f6ccc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-233,-683 -250,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1f6b420@0" ObjectIDZND1="g_1f678f0@0" Pin0InfoVect0LinkObjId="g_1f6b420_0" Pin0InfoVect1LinkObjId="g_1f678f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-233,-683 -250,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f6cf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-250,-683 -262,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1f6b420@0" ObjectIDND1="0@x" ObjectIDZND0="g_1f678f0@1" Pin0InfoVect0LinkObjId="g_1f678f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f6b420_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-250,-683 -262,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2424820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1769,-669 1769,-652 1849,-652 1849,-746 2060,-746 2060,-25 126,-25 126,-200 40,-200 41,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1dda360@0" Pin0InfoVect0LinkObjId="g_1dda360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1769,-669 1769,-652 1849,-652 1849,-746 2060,-746 2060,-25 126,-25 126,-200 40,-200 41,-195 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="28324" cx="1200" cy="-961" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28324" cx="1663" cy="-961" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28325" cx="431" cy="-570" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28325" cx="1104" cy="-570" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28325" cx="1198" cy="-570" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="1322" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="1855" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="1536" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="1518" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28325" cx="933" cy="-570" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28325" cx="891" cy="-570" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28324" cx="678" cy="-961" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28324" cx="677" cy="-961" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-163" cy="-727" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-163" cy="-640" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-163" cy="-515" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-163" cy="-403" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-163" cy="-294" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-163" cy="-195" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="-163" cy="-683" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28326" cx="1769" cy="-568" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="28325" cx="676" cy="-570" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153544" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -418.000000 -1163.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26034" ObjectName="DYN-DY_ST"/>
     <cge:Meas_Ref ObjectId="153544"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_1f47a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -567.000000 -1239.500000) translate(0,16)">三台变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f080a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f080a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f080a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f080a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f080a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f080a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f080a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f080a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f080a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b5e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：0878-6148328</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f19eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 635.000000 -1316.000000) translate(0,12)">35kV三台T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e82050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1226.000000 -1139.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ed7b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1623.000000 -1236.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ee0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 405.000000 -268.000000) translate(0,12)">多底河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecc2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 907.000000 -265.000000) translate(0,12)">必期拉线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e01b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1065.000000 -264.000000) translate(0,12)">零级电站并网线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e393f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1510.000000 -261.000000) translate(0,12)">菜西拉铜矿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dfe8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 827.000000 -827.000000) translate(0,12)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d8f5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1471.000000 -820.000000) translate(0,12)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec7e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1718.000000 -783.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec8340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 687.000000 -1100.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec8840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.000000 -1018.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec8ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.000000 -1174.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec8d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 734.000000 -1233.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec9040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 728.000000 -1146.000000) translate(0,12)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec94a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -1076.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec96e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1670.000000 -1037.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec9c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1708.000000 -1114.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db0a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 -856.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db0c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 685.000000 -930.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db0ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 717.000000 -918.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db1110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -690.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db1350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 683.000000 -619.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db1590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 440.000000 -455.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db17d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 438.000000 -526.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 438.000000 -384.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db1c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 943.000000 -452.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db1e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 940.000000 -381.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db20d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 940.000000 -523.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db2310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1114.000000 -450.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db2550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1111.000000 -521.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db2790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1111.000000 -379.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db29d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1250.000000 -507.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db2c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1329.000000 -520.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db2e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1205.000000 -525.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db3090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 -448.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db32d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -377.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db3510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -519.000000) translate(0,12)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db3750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1865.000000 -447.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db3990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 -376.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db3bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1862.000000 -518.000000) translate(0,12)">0822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db3e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1598.000000 -591.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db4050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1117.000000 -610.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db4290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 943.000000 -982.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db44d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -627.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db4710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 898.000000 -633.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e90a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1844.000000 -252.000000) translate(0,12)">干河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e925a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -721.000000) translate(0,12)">SZ11-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e925a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -721.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e925a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -721.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e925a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -721.000000) translate(0,57)">7.07%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e7d0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -705.000000 -874.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1e7dee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -446.000000 -1224.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1e80180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -446.000000 -1259.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e27ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -719.000000 -608.000000) translate(0,16)">直流系统信号采集异常，经变电站现场人员检查，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e27ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -719.000000 -608.000000) translate(0,35)">直流系统与远动通信异常，需报缺陷处理。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1dc44e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -755.000000 -261.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1eb4a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -601.000000 -271.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1eb4a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -601.000000 -271.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dbed00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 584.000000 -751.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1ef6360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -479.500000 -1330.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1e2ba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 66.000000 -675.000000) translate(0,18)">多底河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e307c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -757.000000) translate(0,12)">013</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e30df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -85.000000 -757.000000) translate(0,12)">0136</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e3dae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -128.000000 -670.000000) translate(0,12)">014</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e3dfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -76.000000 -670.000000) translate(0,12)">0146</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d75190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -9.000000 -587.000000) translate(0,12)">01467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d757c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -126.000000 -550.000000) translate(0,12)">015</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d75a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -81.000000 -551.000000) translate(0,12)">0156</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d75c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -17.000000 -464.000000) translate(0,12)">01567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1d75e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 79.000000 -553.000000) translate(0,18)">必期拉线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d76dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -130.000000 -436.000000) translate(0,12)">016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d77150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -85.000000 -434.000000) translate(0,12)">0166</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d77390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -17.000000 -352.000000) translate(0,12)">01667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1d78320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 57.000000 -437.000000) translate(0,18)">菜西拉铜矿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7a590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -128.000000 -325.000000) translate(0,12)">017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7aa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -86.000000 -327.000000) translate(0,12)">0176</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7acc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -21.000000 -246.000000) translate(0,12)">01767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7c890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -127.000000 -225.000000) translate(0,12)">018</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7cd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -77.000000 -223.000000) translate(0,12)">0186</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -23.000000 -143.000000) translate(0,12)">01867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1d7dd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 80.000000 -333.000000) translate(0,18)">干河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d7e490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45.000000 -235.000000) translate(0,16)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d7ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -204.000000 -268.000000) translate(0,16)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d7ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -204.000000 -268.000000) translate(0,36)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d7ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -204.000000 -268.000000) translate(0,56)">转</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d7ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -204.000000 -268.000000) translate(0,76)">供</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d7ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -204.000000 -268.000000) translate(0,96)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d7ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -204.000000 -268.000000) translate(0,116)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d7ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -204.000000 -268.000000) translate(0,136)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6d180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -232.000000 -715.000000) translate(0,12)">0903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6d7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -434.000000 -732.000000) translate(0,12)">10kV转供母线电压互感器</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-499" y="-1342"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ST" endPointId="0" endStationName="DY_GH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_tiangui" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="677,-1244 677,-1289 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38089" ObjectName="AC-35kV.LN_tiangui"/>
    <cge:TPSR_Ref TObjectID="38089_SS-227"/></metadata>
   <polyline fill="none" opacity="0" points="677,-1244 677,-1289 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1e55450">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 631.000000 -1227.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ea4c40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1656.000000 -1113.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ed7180">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.000000 -1123.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ee29e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 459.676442 -272.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ef09c0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 961.676442 -269.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e99cd0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1132.676442 -267.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e38300">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1883.676442 -264.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f11650">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1564.676442 -265.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dfc4b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 884.000000 -709.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dfcc90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 848.000000 -704.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ed45d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 -703.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ed4ed0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1475.000000 -698.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e80980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1645.000000 -1176.000000)" xlink:href="#lightningRod:shape163"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e71510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1500.000000 -766.000000)" xlink:href="#lightningRod:shape163"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e741b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 873.000000 -772.000000)" xlink:href="#lightningRod:shape163"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d4e460">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 48.000000 -748.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d4f6d0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 49.500000 -718.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d502f0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 48.166667 -660.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e2ae40">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 48.666667 -630.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e3e400">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 47.166667 -535.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e425f0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 47.666667 -505.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d91df0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 44.166667 -423.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d96220">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 44.666667 -393.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d9bfd0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 46.166667 -314.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1da0440">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 46.666667 -284.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1da61f0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 45.166667 -215.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dda360">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 45.666667 -185.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d851d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 403.000000 -863.000000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f66210">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 -367.500000 -691.500000)" xlink:href="#lightningRod:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f678f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -298.000000 -674.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f6b420">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -308.000000 -633.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23f50e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1764.000000 -586.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 901.000000 -1291.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28328"/>
     <cge:Term_Ref ObjectID="40210"/>
    <cge:TPSR_Ref TObjectID="28328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 901.000000 -1291.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28328"/>
     <cge:Term_Ref ObjectID="40210"/>
    <cge:TPSR_Ref TObjectID="28328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 901.000000 -1291.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28328"/>
     <cge:Term_Ref ObjectID="40210"/>
    <cge:TPSR_Ref TObjectID="28328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-186872" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 901.000000 -1291.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28328"/>
     <cge:Term_Ref ObjectID="40210"/>
    <cge:TPSR_Ref TObjectID="28328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-186875" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 901.000000 -1291.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28328"/>
     <cge:Term_Ref ObjectID="40210"/>
    <cge:TPSR_Ref TObjectID="28328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 -225.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28343"/>
     <cge:Term_Ref ObjectID="40240"/>
    <cge:TPSR_Ref TObjectID="28343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 -225.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28343"/>
     <cge:Term_Ref ObjectID="40240"/>
    <cge:TPSR_Ref TObjectID="28343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 -225.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28343"/>
     <cge:Term_Ref ObjectID="40240"/>
    <cge:TPSR_Ref TObjectID="28343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-186915" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 -225.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28343"/>
     <cge:Term_Ref ObjectID="40240"/>
    <cge:TPSR_Ref TObjectID="28343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-186918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 -225.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28343"/>
     <cge:Term_Ref ObjectID="40240"/>
    <cge:TPSR_Ref TObjectID="28343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 948.000000 -218.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28346"/>
     <cge:Term_Ref ObjectID="40246"/>
    <cge:TPSR_Ref TObjectID="28346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 948.000000 -218.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28346"/>
     <cge:Term_Ref ObjectID="40246"/>
    <cge:TPSR_Ref TObjectID="28346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 948.000000 -218.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28346"/>
     <cge:Term_Ref ObjectID="40246"/>
    <cge:TPSR_Ref TObjectID="28346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-186920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 948.000000 -218.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28346"/>
     <cge:Term_Ref ObjectID="40246"/>
    <cge:TPSR_Ref TObjectID="28346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-186923" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 948.000000 -218.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186923" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28346"/>
     <cge:Term_Ref ObjectID="40246"/>
    <cge:TPSR_Ref TObjectID="28346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 -222.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28349"/>
     <cge:Term_Ref ObjectID="40252"/>
    <cge:TPSR_Ref TObjectID="28349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 -222.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28349"/>
     <cge:Term_Ref ObjectID="40252"/>
    <cge:TPSR_Ref TObjectID="28349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 -222.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28349"/>
     <cge:Term_Ref ObjectID="40252"/>
    <cge:TPSR_Ref TObjectID="28349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-186925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 -222.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28349"/>
     <cge:Term_Ref ObjectID="40252"/>
    <cge:TPSR_Ref TObjectID="28349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-186928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 -222.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28349"/>
     <cge:Term_Ref ObjectID="40252"/>
    <cge:TPSR_Ref TObjectID="28349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1561.000000 -221.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28352"/>
     <cge:Term_Ref ObjectID="40258"/>
    <cge:TPSR_Ref TObjectID="28352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1561.000000 -221.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28352"/>
     <cge:Term_Ref ObjectID="40258"/>
    <cge:TPSR_Ref TObjectID="28352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1561.000000 -221.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28352"/>
     <cge:Term_Ref ObjectID="40258"/>
    <cge:TPSR_Ref TObjectID="28352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-186930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1561.000000 -221.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28352"/>
     <cge:Term_Ref ObjectID="40258"/>
    <cge:TPSR_Ref TObjectID="28352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-186933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1561.000000 -221.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28352"/>
     <cge:Term_Ref ObjectID="40258"/>
    <cge:TPSR_Ref TObjectID="28352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1869.000000 -221.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28355"/>
     <cge:Term_Ref ObjectID="40264"/>
    <cge:TPSR_Ref TObjectID="28355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186937" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1869.000000 -221.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186937" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28355"/>
     <cge:Term_Ref ObjectID="40264"/>
    <cge:TPSR_Ref TObjectID="28355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1869.000000 -221.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28355"/>
     <cge:Term_Ref ObjectID="40264"/>
    <cge:TPSR_Ref TObjectID="28355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-186935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1869.000000 -221.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28355"/>
     <cge:Term_Ref ObjectID="40264"/>
    <cge:TPSR_Ref TObjectID="28355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-186938" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1869.000000 -221.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186938" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28355"/>
     <cge:Term_Ref ObjectID="40264"/>
    <cge:TPSR_Ref TObjectID="28355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 -466.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186939" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28358"/>
     <cge:Term_Ref ObjectID="40270"/>
    <cge:TPSR_Ref TObjectID="28358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-186940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 -466.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28358"/>
     <cge:Term_Ref ObjectID="40270"/>
    <cge:TPSR_Ref TObjectID="28358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-186941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 -466.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28358"/>
     <cge:Term_Ref ObjectID="40270"/>
    <cge:TPSR_Ref TObjectID="28358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-186944" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 -466.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28358"/>
     <cge:Term_Ref ObjectID="40270"/>
    <cge:TPSR_Ref TObjectID="28358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 796.000000 -684.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28337"/>
     <cge:Term_Ref ObjectID="40228"/>
    <cge:TPSR_Ref TObjectID="28337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 796.000000 -684.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28337"/>
     <cge:Term_Ref ObjectID="40228"/>
    <cge:TPSR_Ref TObjectID="28337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 796.000000 -684.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28337"/>
     <cge:Term_Ref ObjectID="40228"/>
    <cge:TPSR_Ref TObjectID="28337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-186883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 796.000000 -684.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28337"/>
     <cge:Term_Ref ObjectID="40228"/>
    <cge:TPSR_Ref TObjectID="28337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-186884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 796.000000 -684.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28337"/>
     <cge:Term_Ref ObjectID="40228"/>
    <cge:TPSR_Ref TObjectID="28337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-186887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 796.000000 -684.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28337"/>
     <cge:Term_Ref ObjectID="40228"/>
    <cge:TPSR_Ref TObjectID="28337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-186879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 601.000000 -870.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28334"/>
     <cge:Term_Ref ObjectID="40222"/>
    <cge:TPSR_Ref TObjectID="28334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-186880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 601.000000 -870.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28334"/>
     <cge:Term_Ref ObjectID="40222"/>
    <cge:TPSR_Ref TObjectID="28334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-186876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 601.000000 -870.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28334"/>
     <cge:Term_Ref ObjectID="40222"/>
    <cge:TPSR_Ref TObjectID="28334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-186877" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 601.000000 -870.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28334"/>
     <cge:Term_Ref ObjectID="40222"/>
    <cge:TPSR_Ref TObjectID="28334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-186878" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 601.000000 -870.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28334"/>
     <cge:Term_Ref ObjectID="40222"/>
    <cge:TPSR_Ref TObjectID="28334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-186881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 601.000000 -870.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28334"/>
     <cge:Term_Ref ObjectID="40222"/>
    <cge:TPSR_Ref TObjectID="28334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-186898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.000000 -773.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-186899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.000000 -773.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-186900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.000000 -773.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-186904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.000000 -773.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-186901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.000000 -773.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-186902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.000000 -773.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-186903" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.000000 -773.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-186905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.000000 -773.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28325"/>
     <cge:Term_Ref ObjectID="40206"/>
    <cge:TPSR_Ref TObjectID="28325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-186906" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1938.000000 -717.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186906" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-186907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1938.000000 -717.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-186908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1938.000000 -717.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-186912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1938.000000 -717.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-186909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1938.000000 -717.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-186910" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1938.000000 -717.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-186911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1938.000000 -717.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-186913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1938.000000 -717.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28326"/>
     <cge:Term_Ref ObjectID="40207"/>
    <cge:TPSR_Ref TObjectID="28326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-186890" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -1110.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-186891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -1110.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-186892" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -1110.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-186896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -1110.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-186893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -1110.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-186894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -1110.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-186895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -1110.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-186897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -1110.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28324"/>
     <cge:Term_Ref ObjectID="40205"/>
    <cge:TPSR_Ref TObjectID="28324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-186889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 789.000000 -785.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28361"/>
     <cge:Term_Ref ObjectID="40279"/>
    <cge:TPSR_Ref TObjectID="28361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-186888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 -764.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28361"/>
     <cge:Term_Ref ObjectID="40279"/>
    <cge:TPSR_Ref TObjectID="28361"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-606" y="-1252"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="35kV三台变35kV三台T接线361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="687" y="-1100"/></g>
   <g href="35kV三台变10kV多底河线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="440" y="-455"/></g>
   <g href="35kV三台变10kV必期拉线063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="943" y="-452"/></g>
   <g href="35kV三台变10kV零级电站并网线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1114" y="-450"/></g>
   <g href="35kV三台变10kV菜西拉铜矿线081间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1546" y="-448"/></g>
   <g href="大姚35kV三台变DY_ST_082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1865" y="-447"/></g>
   <g href="35kV三台变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1250" y="-507"/></g>
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-457" y="-1232"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-457" y="-1267"/></g>
   <g href="35kV三台变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="30" qtmmishow="hidden" width="80" x="-711" y="-880"/></g>
   <g href="35kV三台变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="584" y="-751"/></g>
   <g href="AVC三台站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-498" y="-1343"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db4a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 747.000000 787.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db51c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.500000 765.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db6490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.000000 717.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db6a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1127.000000 770.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db6f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1119.000000 682.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db7810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1119.000000 664.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db7a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1135.000000 647.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1db85f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1127.000000 751.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eabf20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1119.000000 700.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eac120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1127.000000 734.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eac450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1882.000000 661.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eac6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1876.000000 714.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eac920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1868.000000 626.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eacb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1868.000000 608.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eacda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1884.000000 591.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eacfe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1876.000000 695.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ead220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1868.000000 644.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ead460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1876.000000 678.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ead790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 370.000000 1055.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eada20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 364.000000 1108.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eadc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 356.000000 1020.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eadea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 356.000000 1002.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eae0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 372.000000 985.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eae320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 364.000000 1089.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eae560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 356.000000 1038.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eae7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 364.000000 1072.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eaead0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 747.000000 646.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eaf350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 747.000000 629.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eaf5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 747.000000 611.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eaf810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.000000 594.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb0370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 733.000000 683.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e88890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 721.500000 664.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e89240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 833.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e894c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 816.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e89700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 798.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e89940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 555.000000 781.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e89b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 537.000000 870.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e89dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 525.500000 851.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df37f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 1255.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df3a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 1237.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df3c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 860.000000 1220.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df3eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 1289.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df40f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 830.500000 1272.500000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df4420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 397.000000 189.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df4690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 397.000000 171.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df48d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 401.000000 154.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df4b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 223.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df4d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 371.500000 206.500000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df5080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 906.000000 183.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df52f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 906.000000 165.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df5530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 910.000000 148.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df5770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 892.000000 217.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df59b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 880.500000 200.500000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df5ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1080.000000 185.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df5f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1080.000000 167.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df6190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1084.000000 150.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df63d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1066.000000 219.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df6610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1054.500000 202.500000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df6940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 184.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df6bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 166.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df6df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1515.000000 149.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df7030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1497.000000 218.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df7270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1485.500000 201.500000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df75a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1819.000000 186.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e8f880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1819.000000 168.500000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e8fac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1823.000000 151.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e8fd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1805.000000 220.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e8ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1793.500000 203.500000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e91d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 448.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e91f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 430.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e92160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1225.000000 413.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e92360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 465.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.252330 -0.000000 0.000000 -0.225287 -90.780645 -251.512644)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2055" x2="2081" y1="767" y2="741"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2080" x2="2054" y1="767" y2="741"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2055" x2="2081" y1="767" y2="741"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.252330 -0.000000 0.000000 -0.225287 409.219355 -242.512644)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2055" x2="2081" y1="767" y2="741"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2080" x2="2054" y1="767" y2="741"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2055" x2="2081" y1="767" y2="741"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.252330 -0.000000 0.000000 -0.225287 1014.219355 -238.512644)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2055" x2="2081" y1="767" y2="741"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2080" x2="2054" y1="767" y2="741"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2055" x2="2081" y1="767" y2="741"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.252330 -0.000000 0.000000 -0.225287 1333.219355 -234.512644)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2055" x2="2081" y1="767" y2="741"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2080" x2="2054" y1="767" y2="741"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2055" x2="2081" y1="767" y2="741"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.252330 -0.000000 0.000000 -0.225287 1247.219355 -474.512644)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2055" x2="2081" y1="767" y2="741"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2080" x2="2054" y1="767" y2="741"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2055" x2="2081" y1="767" y2="741"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.252330 -0.000000 0.000000 -0.225287 156.219355 -713.512644)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2055" x2="2081" y1="767" y2="741"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2080" x2="2054" y1="767" y2="741"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2055" x2="2081" y1="767" y2="741"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.252330 -0.000000 0.000000 -0.225287 155.219355 -954.512644)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2055" x2="2081" y1="767" y2="741"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2080" x2="2054" y1="767" y2="741"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2055" x2="2081" y1="767" y2="741"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.252330 -0.000000 0.000000 -0.225287 582.219355 -237.512644)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2055" x2="2081" y1="767" y2="741"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2080" x2="2054" y1="767" y2="741"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2055" x2="2081" y1="767" y2="741"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.252330 -0.000000 0.000000 -0.225287 154.219355 -423.512644)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2055" x2="2081" y1="767" y2="741"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="4" x1="2080" x2="2054" y1="767" y2="741"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="2055" x2="2081" y1="767" y2="741"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.304348 -0.000000 -0.000000 1.187500 1217.000000 -1165.000000)" xlink:href="#transformer2:shape95_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.304348 -0.000000 -0.000000 1.187500 1217.000000 -1165.000000)" xlink:href="#transformer2:shape95_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.152174 -0.000000 -0.000000 1.135802 1784.000000 -755.864198)" xlink:href="#transformer2:shape95_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-1.152174 -0.000000 -0.000000 1.135802 1784.000000 -755.864198)" xlink:href="#transformer2:shape95_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_ST.DY_ST_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="40278"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.862037 -0.000000 0.000000 -0.851573 645.000000 -728.869565)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.862037 -0.000000 0.000000 -0.851573 645.000000 -728.869565)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28361" ObjectName="TF-DY_ST.DY_ST_1T"/>
    <cge:TPSR_Ref TObjectID="28361"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-606" y="-1252"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-606" y="-1252"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="687" y="-1100"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="687" y="-1100"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="440" y="-455"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="440" y="-455"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="943" y="-452"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="943" y="-452"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1114" y="-450"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1114" y="-450"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1546" y="-448"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1546" y="-448"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1865" y="-447"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1865" y="-447"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1250" y="-507"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1250" y="-507"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-457" y="-1232"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-457" y="-1232"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-457" y="-1267"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-457" y="-1267"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="30" qtmmishow="hidden" width="80" x="-711" y="-880"/>
    </a>
   <metadata/><rect fill="white" height="30" opacity="0" stroke="white" transform="" width="80" x="-711" y="-880"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="584" y="-751"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="584" y="-751"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-498" y="-1343"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-498" y="-1343"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-186971">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 668.241796 -1144.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28331" ObjectName="SW-DY_ST.DY_ST_3616SW"/>
     <cge:Meas_Ref ObjectId="186971"/>
    <cge:TPSR_Ref TObjectID="28331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186972">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.241796 -1115.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28332" ObjectName="SW-DY_ST.DY_ST_36160SW"/>
     <cge:Meas_Ref ObjectId="186972"/>
    <cge:TPSR_Ref TObjectID="28332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186969">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 668.241796 -988.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28329" ObjectName="SW-DY_ST.DY_ST_3611SW"/>
     <cge:Meas_Ref ObjectId="186969"/>
    <cge:TPSR_Ref TObjectID="28329"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186970">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 729.241796 -1045.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28330" ObjectName="SW-DY_ST.DY_ST_36117SW"/>
     <cge:Meas_Ref ObjectId="186970"/>
    <cge:TPSR_Ref TObjectID="28330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186973">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 731.241796 -1202.085366)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28333" ObjectName="SW-DY_ST.DY_ST_36167SW"/>
     <cge:Meas_Ref ObjectId="186973"/>
    <cge:TPSR_Ref TObjectID="28333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1195.203031 -996.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187074">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1653.881701 -1007.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28339" ObjectName="SW-DY_ST.DY_ST_3901SW"/>
     <cge:Meas_Ref ObjectId="187074"/>
    <cge:TPSR_Ref TObjectID="28339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187075">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1704.881701 -1083.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28340" ObjectName="SW-DY_ST.DY_ST_39017SW"/>
     <cge:Meas_Ref ObjectId="187075"/>
    <cge:TPSR_Ref TObjectID="28340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186997">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 669.241796 -900.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28335" ObjectName="SW-DY_ST.DY_ST_3011SW"/>
     <cge:Meas_Ref ObjectId="186997"/>
    <cge:TPSR_Ref TObjectID="28335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187027">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 667.241796 -601.130435)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28338" ObjectName="SW-DY_ST.DY_ST_0011SW"/>
     <cge:Meas_Ref ObjectId="187027"/>
    <cge:TPSR_Ref TObjectID="28338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 421.761290 -496.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28344" ObjectName="SW-DY_ST.DY_ST_0621SW"/>
     <cge:Meas_Ref ObjectId="187088"/>
    <cge:TPSR_Ref TObjectID="28344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187089">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 421.761290 -353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28345" ObjectName="SW-DY_ST.DY_ST_0626SW"/>
     <cge:Meas_Ref ObjectId="187089"/>
    <cge:TPSR_Ref TObjectID="28345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186998">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.241796 -887.130435)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28336" ObjectName="SW-DY_ST.DY_ST_30117SW"/>
     <cge:Meas_Ref ObjectId="186998"/>
    <cge:TPSR_Ref TObjectID="28336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187111">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 923.761290 -493.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28347" ObjectName="SW-DY_ST.DY_ST_0631SW"/>
     <cge:Meas_Ref ObjectId="187111"/>
    <cge:TPSR_Ref TObjectID="28347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187112">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 923.761290 -351.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28348" ObjectName="SW-DY_ST.DY_ST_0636SW"/>
     <cge:Meas_Ref ObjectId="187112"/>
    <cge:TPSR_Ref TObjectID="28348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187134">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1094.761290 -491.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28350" ObjectName="SW-DY_ST.DY_ST_0641SW"/>
     <cge:Meas_Ref ObjectId="187134"/>
    <cge:TPSR_Ref TObjectID="28350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187135">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1094.761290 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28351" ObjectName="SW-DY_ST.DY_ST_0646SW"/>
     <cge:Meas_Ref ObjectId="187135"/>
    <cge:TPSR_Ref TObjectID="28351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187203">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1189.226115 -495.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28359" ObjectName="SW-DY_ST.DY_ST_0121SW"/>
     <cge:Meas_Ref ObjectId="187203"/>
    <cge:TPSR_Ref TObjectID="28359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187204">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1313.226115 -490.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28360" ObjectName="SW-DY_ST.DY_ST_0122SW"/>
     <cge:Meas_Ref ObjectId="187204"/>
    <cge:TPSR_Ref TObjectID="28360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187180">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1845.761290 -488.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28356" ObjectName="SW-DY_ST.DY_ST_0822SW"/>
     <cge:Meas_Ref ObjectId="187180"/>
    <cge:TPSR_Ref TObjectID="28356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187181">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1845.761290 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28357" ObjectName="SW-DY_ST.DY_ST_0826SW"/>
     <cge:Meas_Ref ObjectId="187181"/>
    <cge:TPSR_Ref TObjectID="28357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187157">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1526.761290 -489.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28353" ObjectName="SW-DY_ST.DY_ST_0812SW"/>
     <cge:Meas_Ref ObjectId="187157"/>
    <cge:TPSR_Ref TObjectID="28353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187158">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1526.761290 -347.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28354" ObjectName="SW-DY_ST.DY_ST_0816SW"/>
     <cge:Meas_Ref ObjectId="187158"/>
    <cge:TPSR_Ref TObjectID="28354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187078">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.881701 -603.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28341" ObjectName="SW-DY_ST.DY_ST_0901SW"/>
     <cge:Meas_Ref ObjectId="187078"/>
    <cge:TPSR_Ref TObjectID="28341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187079">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1508.881701 -597.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28342" ObjectName="SW-DY_ST.DY_ST_0902SW"/>
     <cge:Meas_Ref ObjectId="187079"/>
    <cge:TPSR_Ref TObjectID="28342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 -50.000000 -736.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -30.000000 -596.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 -41.000000 -649.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -31.000000 -471.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 -42.000000 -524.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -34.000000 -359.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 -45.000000 -412.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -32.000000 -250.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 -43.000000 -303.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -33.000000 -151.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 -44.000000 -204.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 -192.000000 -692.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-186879" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -599.000000 -1099.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186879" ObjectName="DY_ST:DY_ST_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-186879" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -598.000000 -1057.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186879" ObjectName="DY_ST:DY_ST_301BK_P"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_ST"/>
</svg>