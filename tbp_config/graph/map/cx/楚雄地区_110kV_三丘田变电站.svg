<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-151" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3108 -1204 1866 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="13" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="20" y2="20"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape62">
    <polyline DF8003:Layer="PUBLIC" points="41,28 38,21 45,21 41,28 41,28 41,28 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="29" y1="22" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="28" y1="22" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="17" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="29" y1="35" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="28" y1="35" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="27" stroke-width="0.388889" width="12" x="1" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="29" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="49" y2="38"/>
    <circle cx="29" cy="29" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="29" cy="18" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="39" cy="23" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
   </symbol>
   <symbol id="lightningRod:shape125">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="99" y2="29"/>
    <rect height="17" stroke-width="0.271429" width="9" x="10" y="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="87" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="53" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0784711" x1="50" x2="55" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0784711" x1="50" x2="55" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.101786" x1="53" x2="53" y1="38" y2="41"/>
    <rect height="9" stroke-width="0.133626" width="5" x="50" y="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="61" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="59" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.101786" x1="53" x2="53" y1="61" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0784711" x1="50" x2="55" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0784711" x1="50" x2="55" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.101786" x1="61" x2="61" y1="61" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0784711" x1="59" x2="64" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0784711" x1="59" x2="64" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.101786" x1="61" x2="61" y1="55" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.101786" x1="44" x2="44" y1="61" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0784711" x1="41" x2="46" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0784711" x1="41" x2="46" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.101786" x1="44" x2="44" y1="55" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="61" y1="78" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="61" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="73" y2="78"/>
    <rect height="9" stroke-width="0.133626" width="4" x="60" y="64"/>
    <rect height="9" stroke-width="0.133626" width="4" x="51" y="64"/>
    <rect height="9" stroke-width="0.133626" width="5" x="41" y="64"/>
    <circle cx="20" cy="9" fillStyle="0" r="9.5" stroke-width="0.193806"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="15" x2="15" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="17" x2="15" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="15" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="21" x2="21" y1="12" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="26" x2="21" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="26" x2="21" y1="9" y2="12"/>
    <ellipse cx="9" cy="9" fillStyle="0" rx="9.5" ry="9" stroke-width="0.190353"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="9" x2="9" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="11" x2="9" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="9" x2="6" y1="7" y2="10"/>
    <ellipse cx="15" cy="19" fillStyle="0" rx="9" ry="9.5" stroke-width="0.190353"/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="71" y1="62" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="40" y1="99" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="5" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="57" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="72" x2="72" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="71" y1="64" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="62" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.436761" x1="9" x2="1" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.154328" x1="5" x2="5" y1="30" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.28125" x1="2" x2="8" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.178571" x1="3" x2="5" y1="26" y2="26"/>
    <rect height="19" stroke-width="1" width="10" x="0" y="35"/>
    <ellipse cx="72" cy="19" fillStyle="0" rx="9" ry="9.5" stroke-width="0.190353"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="66" x2="63" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="68" x2="66" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="66" x2="66" y1="4" y2="7"/>
    <ellipse cx="66" cy="9" fillStyle="0" rx="9.5" ry="9" stroke-width="0.190353"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="83" x2="78" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="83" x2="78" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="78" x2="78" y1="12" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="72" x2="69" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="74" x2="72" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183107" x1="72" x2="72" y1="20" y2="23"/>
    <circle cx="77" cy="9" fillStyle="0" r="9.5" stroke-width="0.193806"/>
    <rect height="17" stroke-width="0.271429" width="9" x="67" y="36"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape81">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="40" y2="27"/>
    <ellipse cx="9" cy="18" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="12" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape101">
    <ellipse cx="12" cy="33" rx="11.5" ry="12" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="9" x2="15" y1="13" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="15" x2="15" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="9" x2="15" y1="13" y2="18"/>
    <ellipse cx="12" cy="13" rx="11.5" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="17" x2="12" y1="36" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="33" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="36" y2="33"/>
   </symbol>
   <symbol id="lightningRod:shape127">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="145" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="71" y1="150" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="114" y1="90" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="114" x2="114" y1="90" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="114" x2="102" y1="61" y2="46"/>
    <rect height="30" stroke-width="0.38" width="12" x="32" y="104"/>
    <rect height="30" stroke-width="0.38" width="13" x="0" y="105"/>
    <rect height="30" stroke-width="0.38" width="12" x="66" y="105"/>
    <ellipse cx="95" cy="38" rx="9.5" ry="11.5" stroke-width="0.0929447"/>
    <ellipse cx="96" cy="20" rx="9.5" ry="11.5" stroke-width="0.0929447"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0259883" x1="37" x2="37" y1="31" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0298538" x1="32" x2="37" y1="27" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0298538" x1="42" x2="37" y1="28" y2="31"/>
    <ellipse cx="36" cy="30" rx="9.5" ry="11.5" stroke-width="0.0929447"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0576212" x1="42" x2="35" y1="10" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.041158" x1="35" x2="35" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.041158" x1="42" x2="35" y1="9" y2="4"/>
    <ellipse cx="37" cy="12" rx="9.5" ry="11.5" stroke-width="0.0929447"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="153" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="63" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="63" x2="88" y1="76" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="92" x2="100" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="92" x2="101" y1="18" y2="18"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="transformer2:shape8_0">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape8_1">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="22" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <circle cx="15" cy="19" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="10" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="10" x2="15" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="15" cy="41" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="11" x2="15" y1="50" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="45" y2="50"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1dfbdb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dfcb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dfd6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dfe2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dff4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e00330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e00920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e01370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_17b8c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_17b8c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e04600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e04600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e05d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e05d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1e06b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e08670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e092d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e0a140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e0a890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e0c110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e0cd70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e0d630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e0ddf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e0eed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e0f850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e10340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e10d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e12320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e12d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1e13ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e14b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e22f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e1b320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1e1c520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1e16aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1876" x="3103" y="-1209"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(176,48,96)" stroke-width="1" x1="3869" x2="4012" y1="-1101" y2="-1101"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3109" y="-603"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3109" y="-1083"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3109" y="-1203"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-106479">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3926.000000 -516.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20735" ObjectName="SW-CX_SQT.CX_SQT_6312SW"/>
     <cge:Meas_Ref ObjectId="106479"/>
    <cge:TPSR_Ref TObjectID="20735"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125793">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3943.442247 -527.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23033" ObjectName="SW-CX_SQT.CX_SQT_63127SW"/>
     <cge:Meas_Ref ObjectId="125793"/>
    <cge:TPSR_Ref TObjectID="23033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 -321.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106480">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4390.000000 -514.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20736" ObjectName="SW-CX_SQT.CX_SQT_6322SW"/>
     <cge:Meas_Ref ObjectId="106480"/>
    <cge:TPSR_Ref TObjectID="20736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4476.000000 -315.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4665.000000 -319.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125791">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4834.442247 -485.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23035" ObjectName="SW-CX_SQT.CX_SQT_36017SW"/>
     <cge:Meas_Ref ObjectId="125791"/>
    <cge:TPSR_Ref TObjectID="23035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125784">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.304348 -478.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23032" ObjectName="SW-CX_SQT.CX_SQT_3601SW"/>
     <cge:Meas_Ref ObjectId="125784"/>
    <cge:TPSR_Ref TObjectID="23032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106482">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3942.442247 -902.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20738" ObjectName="SW-CX_SQT.CX_SQT_10117SW"/>
     <cge:Meas_Ref ObjectId="106482"/>
    <cge:TPSR_Ref TObjectID="20738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106475">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -607.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20734" ObjectName="SW-CX_SQT.CX_SQT_1T"/>
     <cge:Meas_Ref ObjectId="106475"/>
    <cge:TPSR_Ref TObjectID="20734"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125792">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4407.442247 -525.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23034" ObjectName="SW-CX_SQT.CX_SQT_63227SW"/>
     <cge:Meas_Ref ObjectId="125792"/>
    <cge:TPSR_Ref TObjectID="23034"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125786">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4484.442247 -702.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23037" ObjectName="SW-CX_SQT.CX_SQT_41117SW"/>
     <cge:Meas_Ref ObjectId="125786"/>
    <cge:TPSR_Ref TObjectID="23037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-125785">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.000000 -606.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23036" ObjectName="SW-CX_SQT.CX_SQT_4111SW"/>
     <cge:Meas_Ref ObjectId="125785"/>
    <cge:TPSR_Ref TObjectID="23036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106481">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -880.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20737" ObjectName="SW-CX_SQT.CX_SQT_1011SW"/>
     <cge:Meas_Ref ObjectId="106481"/>
    <cge:TPSR_Ref TObjectID="20737"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SQT" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.sanqiutianT_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3940,-1051 3940,-1101 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9195" ObjectName="AC-110kV.sanqiutianT_line"/>
    <cge:TPSR_Ref TObjectID="9195_SS-151"/></metadata>
   <polyline fill="none" opacity="0" points="3940,-1051 3940,-1101 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4578.000000 -833.000000)" xlink:href="#transformer2:shape8_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4578.000000 -833.000000)" xlink:href="#transformer2:shape8_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_SQT.CX_SQT_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28803"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.666667 -0.000000 0.000000 -1.475410 3915.000000 -683.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.666667 -0.000000 0.000000 -1.475410 3915.000000 -683.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="20739" ObjectName="TF-CX_SQT.CX_SQT_1T"/>
    <cge:TPSR_Ref TObjectID="20739"/></metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_SQT.P1">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3916.000000 -150.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43425" ObjectName="SM-CX_SQT.P1"/>
    <cge:TPSR_Ref TObjectID="43425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_SQT.P2">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.000000 -148.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43426" ObjectName="SM-CX_SQT.P2"/>
    <cge:TPSR_Ref TObjectID="43426"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-6KV" id="g_17bcf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-512 4018,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23033@1" ObjectIDZND0="g_1512020@0" Pin0InfoVect0LinkObjId="g_1512020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125793_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-512 4018,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_18d2eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-410 3840,-410 3840,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="43425@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1742e90@0" Pin0InfoVect0LinkObjId="g_1742e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_SQT.P1_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-410 3840,-410 3840,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_181ff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-410 3940,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="g_1742e90@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="43425@0" Pin0InfoVect0LinkObjId="SM-CX_SQT.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1742e90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-410 3940,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_18211f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-410 4027,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43425@x" ObjectIDND1="g_1742e90@0" ObjectIDND2="46566@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_SQT.P1_0" Pin1InfoVect1LinkObjId="g_1742e90_0" Pin1InfoVect2LinkObjId="SW-106473_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-410 4027,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_18213e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4027,-410 4027,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="43425@x" ObjectIDND1="g_1742e90@0" ObjectIDND2="46566@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_SQT.P1_0" Pin1InfoVect1LinkObjId="g_1742e90_0" Pin1InfoVect2LinkObjId="SW-106473_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4027,-410 4027,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18215d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4027,-339 4027,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1855520@0" Pin0InfoVect0LinkObjId="g_1855520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4027,-339 4027,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1866360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4216,-316 4216,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_17e1d00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17e1d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4216,-316 4216,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1866550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4216,-379 4216,-410 4027,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="0@1" ObjectIDZND0="43425@x" ObjectIDZND1="g_1742e90@0" ObjectIDZND2="46566@x" Pin0InfoVect0LinkObjId="SM-CX_SQT.P1_0" Pin0InfoVect1LinkObjId="g_1742e90_0" Pin0InfoVect2LinkObjId="SW-106473_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4216,-379 4216,-410 4027,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1866740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-410 3940,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="43425@x" ObjectIDND1="g_1742e90@0" ObjectIDND2="0@x" ObjectIDZND0="46566@0" Pin0InfoVect0LinkObjId="SW-106473_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_SQT.P1_0" Pin1InfoVect1LinkObjId="g_1742e90_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-410 3940,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1866930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3941,-574 3941,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20735@1" ObjectIDZND0="20731@0" Pin0InfoVect0LinkObjId="g_17e02b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106479_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3941,-574 3941,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1866b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-512 3941,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23033@0" ObjectIDZND0="20735@x" ObjectIDZND1="46566@x" Pin0InfoVect0LinkObjId="SW-106479_0" Pin0InfoVect1LinkObjId="SW-106473_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125793_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-512 3941,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_18672e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3941,-490 3941,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="46566@1" ObjectIDZND0="23033@x" ObjectIDZND1="20735@x" Pin0InfoVect0LinkObjId="SW-125793_0" Pin0InfoVect1LinkObjId="SW-106479_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106473_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3941,-490 3941,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_18767d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3941,-512 3941,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23033@x" ObjectIDND1="46566@x" ObjectIDZND0="20735@0" Pin0InfoVect0LinkObjId="SW-106479_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-125793_0" Pin1InfoVect1LinkObjId="SW-106473_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3941,-512 3941,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_18786a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4465,-510 4482,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23034@1" ObjectIDZND0="g_1878070@0" Pin0InfoVect0LinkObjId="g_1878070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4465,-510 4482,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1878890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-408 4304,-408 4304,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="43426@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1855190@0" Pin0InfoVect0LinkObjId="g_1855190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_SQT.P2_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-408 4304,-408 4304,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17d4750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-408 4404,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_1855190@0" ObjectIDZND0="43426@0" Pin0InfoVect0LinkObjId="SM-CX_SQT.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1855190_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-408 4404,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17d5ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-408 4491,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43426@x" ObjectIDND1="g_1855190@0" ObjectIDND2="20733@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_SQT.P2_0" Pin1InfoVect1LinkObjId="g_1855190_0" Pin1InfoVect2LinkObjId="SW-106474_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-408 4491,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17d61e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-408 4491,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="43426@x" ObjectIDND1="g_1855190@0" ObjectIDND2="20733@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_SQT.P2_0" Pin1InfoVect1LinkObjId="g_1855190_0" Pin1InfoVect2LinkObjId="SW-106474_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-408 4491,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17d63d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-337 4491,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1856160@0" Pin0InfoVect0LinkObjId="g_1856160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-337 4491,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17dfed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4680,-314 4680,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_18acab0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18acab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4680,-314 4680,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17e00c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4680,-377 4680,-408 4491,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="hydroGenerator" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="43426@x" ObjectIDZND2="g_1855190@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SM-CX_SQT.P2_0" Pin0InfoVect2LinkObjId="g_1855190_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4680,-377 4680,-408 4491,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17e02b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-572 4405,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20736@1" ObjectIDZND0="20731@0" Pin0InfoVect0LinkObjId="g_1866930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-572 4405,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17e04a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-510 4405,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23034@0" ObjectIDZND0="20736@x" ObjectIDZND1="20733@x" Pin0InfoVect0LinkObjId="SW-106480_0" Pin0InfoVect1LinkObjId="SW-106474_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125792_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-510 4405,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17e0690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-510 4404,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23034@x" ObjectIDND1="20733@x" ObjectIDZND0="20736@0" Pin0InfoVect0LinkObjId="SW-106480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-125792_0" Pin1InfoVect1LinkObjId="SW-106474_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-510 4404,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1831bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4892,-470 4909,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23035@1" ObjectIDZND0="g_1831440@0" Pin0InfoVect0LinkObjId="g_1831440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125791_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4892,-470 4909,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_18339d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-470 4832,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="23035@0" ObjectIDZND0="23032@x" ObjectIDZND1="g_17ffe80@0" Pin0InfoVect0LinkObjId="SW-125784_0" Pin0InfoVect1LinkObjId="g_17ffe80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125791_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-470 4832,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17ff1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4831,-536 4831,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23032@1" ObjectIDZND0="20731@0" Pin0InfoVect0LinkObjId="g_1866930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125784_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4831,-536 4831,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17ffaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4831,-428 4831,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_17ffe80@0" ObjectIDZND0="23035@x" ObjectIDZND1="23032@x" Pin0InfoVect0LinkObjId="SW-125791_0" Pin0InfoVect1LinkObjId="SW-125784_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17ffe80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4831,-428 4831,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17ffc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4831,-470 4831,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="23035@x" ObjectIDND1="g_17ffe80@0" ObjectIDZND0="23032@0" Pin0InfoVect0LinkObjId="SW-125784_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-125791_0" Pin1InfoVect1LinkObjId="g_17ffe80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4831,-470 4831,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1843750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-887 4017,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20738@1" ObjectIDZND0="g_1842fa0@0" Pin0InfoVect0LinkObjId="g_1842fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106482_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-887 4017,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1845530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-887 3940,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="20738@0" ObjectIDZND0="g_1845720@0" ObjectIDZND1="g_185f900@0" ObjectIDZND2="20725@x" Pin0InfoVect0LinkObjId="g_1845720_0" Pin0InfoVect1LinkObjId="g_185f900_0" Pin0InfoVect2LinkObjId="SW-106436_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106482_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-887 3940,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_185f520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3964,-861 3940,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1845720@0" ObjectIDZND0="20738@x" ObjectIDZND1="20737@x" ObjectIDZND2="g_185f900@0" Pin0InfoVect0LinkObjId="SW-106482_0" Pin0InfoVect1LinkObjId="SW-106481_0" Pin0InfoVect2LinkObjId="g_185f900_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1845720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3964,-861 3940,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_185f710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-834 4003,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="20725@x" ObjectIDND1="g_1845720@0" ObjectIDND2="20738@x" ObjectIDZND0="g_185f900@0" Pin0InfoVect0LinkObjId="g_185f900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106436_0" Pin1InfoVect1LinkObjId="g_1845720_0" Pin1InfoVect2LinkObjId="SW-106482_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-834 4003,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1860060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3939,-748 3837,-748 3837,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="earth" ObjectIDND0="20739@x" ObjectIDZND0="g_1860250@0" Pin0InfoVect0LinkObjId="g_1860250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3939,-748 3837,-748 3837,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17677b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-686 3940,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="20739@0" ObjectIDZND0="20734@1" Pin0InfoVect0LinkObjId="SW-106475_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-686 3940,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17679a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-629 3940,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20734@0" ObjectIDZND0="20731@0" Pin0InfoVect0LinkObjId="g_1866930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106475_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-629 3940,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1768140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-1035 4563,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-1035 4563,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1768330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-980 4563,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-980 4563,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1768670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-838 4563,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1779ef0@0" Pin0InfoVect0LinkObjId="g_1779ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-838 4563,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1768860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-764 4563,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1779ef0@1" ObjectIDZND0="g_177a8e0@1" Pin0InfoVect0LinkObjId="g_177a8e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1779ef0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-764 4563,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_177daa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-687 4542,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_177a8e0@0" ObjectIDND1="23036@x" ObjectIDZND0="23037@1" Pin0InfoVect0LinkObjId="SW-125786_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_177a8e0_0" Pin1InfoVect1LinkObjId="SW-125785_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-687 4542,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_177dcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4506,-687 4486,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23037@0" ObjectIDZND0="g_1768a50@0" Pin0InfoVect0LinkObjId="g_1768a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125786_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4506,-687 4486,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_177e6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-712 4563,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_177a8e0@0" ObjectIDZND0="23037@x" ObjectIDZND1="23036@x" Pin0InfoVect0LinkObjId="SW-125786_0" Pin0InfoVect1LinkObjId="SW-125785_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_177a8e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-712 4563,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17d9d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-687 4563,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="23037@x" ObjectIDND1="g_177a8e0@0" ObjectIDZND0="23036@1" Pin0InfoVect0LinkObjId="SW-125785_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-125786_0" Pin1InfoVect1LinkObjId="g_177a8e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-687 4563,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_17d9f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-628 4563,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23036@0" ObjectIDZND0="20731@0" Pin0InfoVect0LinkObjId="g_1866930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-125785_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-628 4563,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17da730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-1035 4728,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-1035 4728,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_17da950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-987 4728,-896 4919,-896 4919,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="load" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-987 4728,-896 4919,-896 4919,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1857160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-766 3940,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="20739@1" ObjectIDZND0="20725@0" Pin0InfoVect0LinkObjId="SW-106436_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-766 3940,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_18060e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-408 4404,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="43426@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="20733@0" Pin0InfoVect0LinkObjId="SW-106474_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_SQT.P2_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-408 4404,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1806340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-485 4404,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20733@1" ObjectIDZND0="23034@x" ObjectIDZND1="20736@x" Pin0InfoVect0LinkObjId="SW-125792_0" Pin0InfoVect1LinkObjId="SW-106480_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106474_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-485 4404,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1807fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-887 3940,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="20738@x" ObjectIDND1="20737@x" ObjectIDZND0="g_1845720@0" ObjectIDZND1="g_185f900@0" ObjectIDZND2="20725@x" Pin0InfoVect0LinkObjId="g_1845720_0" Pin0InfoVect1LinkObjId="g_185f900_0" Pin0InfoVect2LinkObjId="SW-106436_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106482_0" Pin1InfoVect1LinkObjId="SW-106481_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-887 3940,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_17c3ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-815 3940,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="20725@1" ObjectIDZND0="g_185f900@0" ObjectIDZND1="g_1845720@0" ObjectIDZND2="20738@x" Pin0InfoVect0LinkObjId="g_185f900_0" Pin0InfoVect1LinkObjId="g_1845720_0" Pin0InfoVect2LinkObjId="SW-106482_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-815 3940,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_17c4230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-887 3940,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20738@x" ObjectIDND1="g_1845720@0" ObjectIDND2="g_185f900@0" ObjectIDZND0="20737@0" Pin0InfoVect0LinkObjId="SW-106481_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106482_0" Pin1InfoVect1LinkObjId="g_1845720_0" Pin1InfoVect2LinkObjId="g_185f900_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-887 3940,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_17c4490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-861 3940,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_1845720@0" ObjectIDND1="20738@x" ObjectIDND2="20737@x" ObjectIDZND0="g_185f900@0" ObjectIDZND1="20725@x" Pin0InfoVect0LinkObjId="g_185f900_0" Pin0InfoVect1LinkObjId="SW-106436_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1845720_0" Pin1InfoVect1LinkObjId="SW-106482_0" Pin1InfoVect2LinkObjId="SW-106481_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-861 3940,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_185c1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3940,-938 3940,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" ObjectIDND0="20737@1" ObjectIDZND0="9195@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106481_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3940,-938 3940,-1051 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="20731" cx="3941" cy="-596" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20731" cx="4405" cy="-596" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20731" cx="4831" cy="-596" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20731" cx="3940" cy="-596" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20731" cx="4563" cy="-596" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4728" cy="-1035" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4563" cy="-1035" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-93908" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3397.000000 -1097.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19752" ObjectName="DYN-CX_SQT"/>
     <cge:Meas_Ref ObjectId="93908"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_181b8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3876.000000 -1129.000000) translate(0,15)">110kV一.二级联线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18ccda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -1006.000000) translate(0,15)">110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18ccda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -1006.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18ccda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -1006.000000) translate(0,51)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18ccda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -1006.000000) translate(0,69)">丘</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18ccda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -1006.000000) translate(0,87)">田</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18ccda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -1006.000000) translate(0,105)">T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18ccda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -1006.000000) translate(0,123)">接</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18ccda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -1006.000000) translate(0,141)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18ccb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -935.000000) translate(0,15)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_188c450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3972.000000 -911.000000) translate(0,15)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c3400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4056.000000 -846.000000) translate(0,15)">110kV线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_155ab50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3954.000000 -811.000000) translate(0,15)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_180ebd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3971.000000 -732.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_181d4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3956.000000 -656.000000) translate(0,15)">6012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1608cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4629.000000 -1065.000000) translate(0,15)">400V 厂用电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_181e270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4884.000000 -1063.000000) translate(0,15)">至施工用电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_181e5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4527.000000 -1002.000000) translate(0,15)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dd710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4692.000000 -1009.000000) translate(0,15)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dd8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4591.000000 -875.000000) translate(0,15)">直配电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dda70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4486.000000 -716.000000) translate(0,15)">41117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17ddc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4509.000000 -658.000000) translate(0,15)">4111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dde10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4760.000000 -626.000000) translate(0,15)">6.3kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_181d130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3890.000000 -562.000000) translate(0,15)">6312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dba20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -537.000000) translate(0,15)">63127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dbbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3970.000000 -371.000000) translate(0,15)">6131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dbd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4165.000000 -371.000000) translate(0,15)">6132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dbf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4351.000000 -563.000000) translate(0,15)">6322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dc0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4357.000000 -485.000000) translate(0,15)">632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dc290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4438.000000 -537.000000) translate(0,15)">63227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dc440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4775.000000 -537.000000) translate(0,15)">3601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dc5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4860.000000 -497.000000) translate(0,15)">36017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dc7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4839.000000 -320.000000) translate(0,15)">6kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dc980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.000000 -373.000000) translate(0,15)">6232</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1728b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4437.000000 -371.000000) translate(0,15)">6231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1728ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3795.000000 -184.000000) translate(0,15)">1号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1728ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3904.000000 -132.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17291d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4273.000000 -184.000000) translate(0,15)">2号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1729500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4374.000000 -132.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17296e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -582.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17c8100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -1061.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17c8100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -1061.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17c8100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -1061.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17c8100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -1061.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17c8100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -1061.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17c8100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -1061.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_17c8100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3122.000000 -1061.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_172b3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3270.000000 -1172.500000) translate(0,16)">三丘田电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_182bdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3239.000000 -225.000000) translate(0,15)">7813379</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_182c5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -733.000000) translate(0,15)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_182c5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -733.000000) translate(0,33)">SF10-63000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_182c5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -733.000000) translate(0,51)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_182c5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -733.000000) translate(0,69)">121+-2*2.5%/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_185a5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4017.000000 -133.000000) translate(0,15)">2号发电机参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_185a5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4017.000000 -133.000000) translate(0,33)">SF6000-10/2600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_185a5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4017.000000 -133.000000) translate(0,51)">Ie=687.3A,cos=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_185bf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -133.000000) translate(0,15)">2号发电机参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_185bf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -133.000000) translate(0,33)">SF6000-10/2600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_185bf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 -133.000000) translate(0,51)">Ie=687.3A,cos=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_185cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3895.000000 -485.000000) translate(0,15)">631</text>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-931 4569,-944 4556,-944 4563,-931 4563,-932 4563,-931 " stroke="rgb(127,127,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-931 4734,-944 4721,-944 4728,-931 4728,-932 4728,-931 " stroke="rgb(127,127,127)"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_SQT.CX_SQT_6ⅠM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-596 3784,-596 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20731" ObjectName="BS-CX_SQT.CX_SQT_6ⅠM"/>
    <cge:TPSR_Ref TObjectID="20731"/></metadata>
   <polyline fill="none" opacity="0" points="4900,-596 3784,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4522,-1035 4814,-1035 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4522,-1035 4814,-1035 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1512020" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4013.000000 -518.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1878070" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4477.000000 -516.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1831440" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4904.000000 -476.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1842fa0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4012.000000 -893.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1860250" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3827.000000 -712.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1768a50" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4491.000000 -693.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3226.000000 -1124.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116498" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3237.538462 -1019.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116498" ObjectName="CX_SQT:CX_SQT_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116499" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3236.538462 -978.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116499" ObjectName="CX_SQT:CX_SQT_ZJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3238" y="-1183"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3238" y="-1183"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3189" y="-1200"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3189" y="-1200"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1728930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.000000 101.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_181df50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3825.000000 86.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17c2510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3850.000000 71.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17c2680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 814.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17ddf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.000000 799.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17de160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 784.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_172a030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 646.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_172a240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 662.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_172aaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3711.000000 629.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17276f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 678.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4910.000000 -1043.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-106443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -674.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20731"/>
     <cge:Term_Ref ObjectID="28786"/>
    <cge:TPSR_Ref TObjectID="20731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-106444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -674.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20731"/>
     <cge:Term_Ref ObjectID="28786"/>
    <cge:TPSR_Ref TObjectID="20731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-106445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -674.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20731"/>
     <cge:Term_Ref ObjectID="28786"/>
    <cge:TPSR_Ref TObjectID="20731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-106446" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -674.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106446" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20731"/>
     <cge:Term_Ref ObjectID="28786"/>
    <cge:TPSR_Ref TObjectID="20731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-106458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 -101.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106458" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20732"/>
     <cge:Term_Ref ObjectID="28787"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-106459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 -101.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20732"/>
     <cge:Term_Ref ObjectID="28787"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-106455" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 -101.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20732"/>
     <cge:Term_Ref ObjectID="28787"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-106470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -96.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20733"/>
     <cge:Term_Ref ObjectID="28789"/>
    <cge:TPSR_Ref TObjectID="20733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-106471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -96.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20733"/>
     <cge:Term_Ref ObjectID="28789"/>
    <cge:TPSR_Ref TObjectID="20733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-106467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -96.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20733"/>
     <cge:Term_Ref ObjectID="28789"/>
    <cge:TPSR_Ref TObjectID="20733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-106433" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4085.000000 -813.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106433" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20725"/>
     <cge:Term_Ref ObjectID="28782"/>
    <cge:TPSR_Ref TObjectID="20725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-106434" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4085.000000 -813.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106434" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20725"/>
     <cge:Term_Ref ObjectID="28782"/>
    <cge:TPSR_Ref TObjectID="20725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-106430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4085.000000 -813.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106430" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20725"/>
     <cge:Term_Ref ObjectID="28782"/>
    <cge:TPSR_Ref TObjectID="20725"/></metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-106436">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.000000 -780.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20725" ObjectName="SW-CX_SQT.CX_SQT_101BK"/>
     <cge:Meas_Ref ObjectId="106436"/>
    <cge:TPSR_Ref TObjectID="20725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106473">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3932.000000 -455.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46566" ObjectName="SW-CX_SQT.CX_SQT_631BK"/>
     <cge:Meas_Ref ObjectId="106473"/>
    <cge:TPSR_Ref TObjectID="46566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106474">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4395.000000 -450.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20733" ObjectName="SW-CX_SQT.CX_SQT_632BK"/>
     <cge:Meas_Ref ObjectId="106474"/>
    <cge:TPSR_Ref TObjectID="20733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -972.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 -979.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3238" y="-1183"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3189" y="-1200"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_17e1d00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4209.000000 -257.000000)" xlink:href="#lightningRod:shape62"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18acab0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.000000 -216.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17ffe80">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4791.000000 -330.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1779ef0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4558.000000 -759.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_177a8e0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -707.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1845720">
    <use class="BV-110KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4018.500000 -854.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_185f900">
    <use class="BV-110KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4042.500000 -825.500000)" xlink:href="#lightningRod:shape81"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1742e90">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3828.000000 -195.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1855190">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4292.000000 -193.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1855520">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 -170.000000)" xlink:href="#lightningRod:shape127"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1856160">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4454.000000 -162.000000)" xlink:href="#lightningRod:shape127"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_SQT"/>
</svg>