<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-93" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3116 -1198 1934 957">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape38">
    <rect height="8" stroke-width="0.75" width="18" x="21" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="1" x2="1" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="4" x2="4" y1="10" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="7" x2="7" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="21" x2="7" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="46" x2="26" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="28" x2="26" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="26" x2="28" y1="7" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape17">
    <rect height="29" stroke-width="2" width="15" x="1" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="8" x2="11" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="9" x2="6" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.8" x1="8" x2="8" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="11" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="1" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="8" x2="8" y1="51" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape17_0">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape17_1">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="voltageTransformer:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="48" y1="4" y2="4"/>
    <circle cx="41" cy="55" fillStyle="0" r="25" stroke-width="0.520408"/>
    <circle cx="57" cy="26" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="50" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="48" x2="40" y1="66" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="32" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="22" x2="22" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="30" x2="22" y1="29" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="22" x2="14" y1="22" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="57" x2="57" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="73" x2="57" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="73" x2="57" y1="24" y2="33"/>
    <circle cx="25" cy="25" fillStyle="0" r="25" stroke-width="0.520408"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_137a940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_137b560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_137bd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_137c5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_137d620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_137e100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_137e840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_137f070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13800f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13800f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b9dd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b9dd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b9f690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b9f690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1ba0460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba1c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ba2850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ba3530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ba3c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba5280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba5d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba6540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ba6d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba7de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba8760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ba9250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ba9c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bab280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1babcd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1baced0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1badb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bbcb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bb5210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1bb6900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1bb08f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="967" width="1944" x="3111" y="-1203"/>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3669,-507 3664,-497 3674,-497 3669,-507 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3669,-519 3664,-529 3674,-529 3669,-519 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3895,-508 3890,-498 3900,-498 3895,-508 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3895,-520 3890,-530 3900,-530 3895,-520 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4134,-507 4129,-497 4139,-497 4134,-507 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4134,-519 4129,-529 4139,-529 4134,-519 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4343,-509 4338,-499 4348,-499 4343,-509 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4343,-521 4338,-531 4348,-531 4343,-521 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4608,-508 4603,-498 4613,-498 4608,-508 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4608,-520 4603,-530 4613,-530 4608,-520 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4888,-508 4883,-498 4893,-498 4888,-508 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4888,-520 4883,-530 4893,-530 4888,-520 " stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-87489">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4202.000000 -966.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18993" ObjectName="SW-LF_JZ.LF_JZ_311BK"/>
     <cge:Meas_Ref ObjectId="87489"/>
    <cge:TPSR_Ref TObjectID="18993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87501">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 -762.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18995" ObjectName="SW-LF_JZ.LF_JZ_421BK"/>
     <cge:Meas_Ref ObjectId="87501"/>
    <cge:TPSR_Ref TObjectID="18995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87595">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19009" ObjectName="SW-LF_JZ.LF_JZ_464BK"/>
     <cge:Meas_Ref ObjectId="87595"/>
    <cge:TPSR_Ref TObjectID="19009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87509">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3659.596639 -581.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18997" ObjectName="SW-LF_JZ.LF_JZ_461BK"/>
     <cge:Meas_Ref ObjectId="87509"/>
    <cge:TPSR_Ref TObjectID="18997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87653">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.333333 -581.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19017" ObjectName="SW-LF_JZ.LF_JZ_465BK"/>
     <cge:Meas_Ref ObjectId="87653"/>
    <cge:TPSR_Ref TObjectID="19017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87537">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.215686 -581.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19001" ObjectName="SW-LF_JZ.LF_JZ_462BK"/>
     <cge:Meas_Ref ObjectId="87537"/>
    <cge:TPSR_Ref TObjectID="19001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4879.333333 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19013" ObjectName="SW-LF_JZ.LF_JZ_466BK"/>
     <cge:Meas_Ref ObjectId="87624"/>
    <cge:TPSR_Ref TObjectID="19013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87566">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.526611 -580.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19005" ObjectName="SW-LF_JZ.LF_JZ_463BK"/>
     <cge:Meas_Ref ObjectId="87566"/>
    <cge:TPSR_Ref TObjectID="19005"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_275a230">
    <use class="BV-10KV" transform="matrix(0.500000 -0.000000 0.000000 -0.500000 3550.000000 -413.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291b6b0">
    <use class="BV-35KV" transform="matrix(0.500000 -0.000000 0.000000 -0.500000 4293.000000 -988.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SZ" endPointId="0" endStationName="LF_JZ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_JiuZhuang" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4211,-1125 4211,-1097 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19305" ObjectName="AC-35kV.LN_JiuZhuang"/>
    <cge:TPSR_Ref TObjectID="19305_SS-93"/></metadata>
   <polyline fill="none" opacity="0" points="4211,-1125 4211,-1097 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-LF_JZ.LF_JZ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="32598"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -820.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -820.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="18992" ObjectName="TF-LF_JZ.LF_JZ_1T"/>
    <cge:TPSR_Ref TObjectID="18992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -921.000000)" xlink:href="#transformer2:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -921.000000)" xlink:href="#transformer2:shape17_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4934.000000 -275.000000)" xlink:href="#transformer2:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4934.000000 -275.000000)" xlink:href="#transformer2:shape17_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_15c4d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4211,-1098 4211,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" ObjectIDND0="19305@1" ObjectIDZND0="18994@1" Pin0InfoVect0LinkObjId="SW-87490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4211,-1098 4211,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f63a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-826 4212,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="18992@0" ObjectIDZND0="18995@1" Pin0InfoVect0LinkObjId="SW-87501_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20db8b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-826 4212,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27e33d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-974 4212,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="18993@0" ObjectIDZND0="48335@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87489_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-974 4212,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24ad2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-770 4212,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18995@0" ObjectIDZND0="18996@1" Pin0InfoVect0LinkObjId="SW-87502_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87501_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-770 4212,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba46d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-690 4212,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18991@0" ObjectIDZND0="18996@0" Pin0InfoVect0LinkObjId="SW-87502_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219b2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-690 4212,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_144bcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4343,-690 4343,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18991@0" ObjectIDZND0="19010@1" Pin0InfoVect0LinkObjId="SW-87596_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219b2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4343,-690 4343,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_131a220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4343,-635 4343,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19010@0" ObjectIDZND0="19009@1" Pin0InfoVect0LinkObjId="SW-87595_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87596_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4343,-635 4343,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_274e050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4343,-590 4343,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19009@0" ObjectIDZND0="19011@1" Pin0InfoVect0LinkObjId="SW-87597_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87595_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4343,-590 4343,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2758f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-690 3669,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18991@0" ObjectIDZND0="18998@1" Pin0InfoVect0LinkObjId="SW-87510_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219b2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3669,-690 3669,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13070f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-634 3669,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18998@0" ObjectIDZND0="18997@1" Pin0InfoVect0LinkObjId="SW-87509_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3669,-634 3669,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2422af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-589 3669,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18997@0" ObjectIDZND0="18999@1" Pin0InfoVect0LinkObjId="SW-87511_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87509_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3669,-589 3669,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e2150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4608,-690 4608,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18991@0" ObjectIDZND0="19018@1" Pin0InfoVect0LinkObjId="SW-87654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219b2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4608,-690 4608,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1275600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4608,-634 4608,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19018@0" ObjectIDZND0="19017@1" Pin0InfoVect0LinkObjId="SW-87653_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4608,-634 4608,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_144a6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4608,-589 4608,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19017@0" ObjectIDZND0="19019@1" Pin0InfoVect0LinkObjId="SW-87655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87653_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4608,-589 4608,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20c5540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-690 3895,-670 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18991@0" ObjectIDZND0="19002@1" Pin0InfoVect0LinkObjId="SW-87538_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219b2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-690 3895,-670 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21c41c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-634 3895,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19002@0" ObjectIDZND0="19001@1" Pin0InfoVect0LinkObjId="SW-87537_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87538_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-634 3895,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e1800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-589 3895,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19001@0" ObjectIDZND0="19003@1" Pin0InfoVect0LinkObjId="SW-87539_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87537_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-589 3895,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20a3660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-690 4888,-671 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18991@0" ObjectIDZND0="19014@1" Pin0InfoVect0LinkObjId="SW-87625_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_219b2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-690 4888,-671 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26c0180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-635 4888,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19014@0" ObjectIDZND0="19013@1" Pin0InfoVect0LinkObjId="SW-87624_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87625_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-635 4888,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2187d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-590 4888,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19013@0" ObjectIDZND0="19015@1" Pin0InfoVect0LinkObjId="SW-87626_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87624_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-590 4888,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1391650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-634 4134,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19006@0" ObjectIDZND0="19005@1" Pin0InfoVect0LinkObjId="SW-87566_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87567_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-634 4134,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13f5310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-588 4134,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19005@0" ObjectIDZND0="19007@1" Pin0InfoVect0LinkObjId="SW-87568_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87566_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-588 4134,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_219b2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-670 4134,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19006@1" ObjectIDZND0="18991@0" Pin0InfoVect0LinkObjId="g_1384c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87567_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-670 4134,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_291b360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4119,-968 4119,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_275a230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_275a230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4119,-968 4119,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2916390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4119,-1042 4119,-1063 4210,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_275a230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4119,-1042 4119,-1063 4210,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e347f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-538 3669,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="18999@0" ObjectIDZND0="19000@1" Pin0InfoVect0LinkObjId="SW-87512_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87511_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3669,-538 3669,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12e0030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-538 3895,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="19003@0" ObjectIDZND0="19004@1" Pin0InfoVect0LinkObjId="SW-87540_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87539_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-538 3895,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27dcc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-537 4134,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="19007@0" ObjectIDZND0="19008@1" Pin0InfoVect0LinkObjId="SW-87569_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-537 4134,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1392630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4343,-539 4343,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="19011@0" ObjectIDZND0="19012@1" Pin0InfoVect0LinkObjId="SW-87598_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87597_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4343,-539 4343,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21552b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4608,-538 4608,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="19019@0" ObjectIDZND0="19020@1" Pin0InfoVect0LinkObjId="SW-87656_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4608,-538 4608,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1373110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-539 4888,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="19015@0" ObjectIDZND0="19016@1" Pin0InfoVect0LinkObjId="SW-87627_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87626_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-539 4888,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b2d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4211,-1016 4211,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="18994@0" ObjectIDZND0="18993@1" Pin0InfoVect0LinkObjId="SW-87489_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4211,-1016 4211,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a3ecd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4313,-1028 4313,-1075 4210,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" ObjectIDND0="g_291b6b0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_291b6b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4313,-1028 4313,-1075 4210,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_27d7a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-322 4947,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_275a230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_275a230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-322 4947,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e098c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-452 4888,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="19016@0" ObjectIDZND0="0@x" ObjectIDZND1="g_23ff630@0" ObjectIDZND2="g_131ac60@0" Pin0InfoVect0LinkObjId="g_275a230_0" Pin0InfoVect1LinkObjId="g_23ff630_0" Pin0InfoVect2LinkObjId="g_131ac60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87627_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-452 4888,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_283fbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-453 3570,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_275a230@0" ObjectIDZND0="g_291a0f0@0" Pin0InfoVect0LinkObjId="g_291a0f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_275a230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-453 3570,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12760a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3570,-517 3570,-545 3543,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_291a0f0@1" ObjectIDZND0="g_2924770@0" ObjectIDZND1="19022@x" Pin0InfoVect0LinkObjId="g_2924770_0" Pin0InfoVect1LinkObjId="SW-87691_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_291a0f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3570,-517 3570,-545 3543,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b8190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3543,-545 3509,-545 3509,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_291a0f0@0" ObjectIDND1="19022@x" ObjectIDZND0="g_2924770@0" Pin0InfoVect0LinkObjId="g_2924770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_291a0f0_0" Pin1InfoVect1LinkObjId="SW-87691_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3543,-545 3509,-545 3509,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e2a2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3543,-545 3543,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_291a0f0@0" ObjectIDND1="g_2924770@0" ObjectIDZND0="19022@0" Pin0InfoVect0LinkObjId="SW-87691_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_291a0f0_0" Pin1InfoVect1LinkObjId="g_2924770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3543,-545 3543,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1384c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3543,-651 3543,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19022@1" ObjectIDZND0="18991@0" Pin0InfoVect0LinkObjId="g_219b2e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87691_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3543,-651 3543,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e6770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-435 3694,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="19000@x" ObjectIDND1="34449@x" ObjectIDZND0="g_205a1a0@0" Pin0InfoVect0LinkObjId="g_205a1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87512_0" Pin1InfoVect1LinkObjId="EC-LF_JZ.461Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3669,-435 3694,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_214a520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-452 3669,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="19000@0" ObjectIDZND0="34449@x" ObjectIDZND1="g_205a1a0@0" Pin0InfoVect0LinkObjId="EC-LF_JZ.461Ld_0" Pin0InfoVect1LinkObjId="g_205a1a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3669,-452 3669,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20cf900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-435 3669,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="19000@x" ObjectIDND1="g_205a1a0@0" ObjectIDZND0="34449@0" Pin0InfoVect0LinkObjId="EC-LF_JZ.461Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87512_0" Pin1InfoVect1LinkObjId="g_205a1a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3669,-435 3669,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27da240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3896,-433 3921,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="19004@x" ObjectIDND1="34450@x" ObjectIDZND0="g_1e94470@0" Pin0InfoVect0LinkObjId="g_1e94470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87540_0" Pin1InfoVect1LinkObjId="EC-LF_JZ.462Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3896,-433 3921,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ba910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-451 3895,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="19004@0" ObjectIDZND0="g_1e94470@0" ObjectIDZND1="34450@x" Pin0InfoVect0LinkObjId="g_1e94470_0" Pin0InfoVect1LinkObjId="EC-LF_JZ.462Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-451 3895,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2504310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-433 3895,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1e94470@0" ObjectIDND1="19004@x" ObjectIDZND0="34450@0" Pin0InfoVect0LinkObjId="EC-LF_JZ.462Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e94470_0" Pin1InfoVect1LinkObjId="SW-87540_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-433 3895,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e1bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-435 4160,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="19008@x" ObjectIDND1="34451@x" ObjectIDZND0="g_21c9f20@0" Pin0InfoVect0LinkObjId="g_21c9f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87569_0" Pin1InfoVect1LinkObjId="EC-LF_JZ.463Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-435 4160,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2571290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-450 4134,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="19008@0" ObjectIDZND0="g_21c9f20@0" ObjectIDZND1="34451@x" Pin0InfoVect0LinkObjId="g_21c9f20_0" Pin0InfoVect1LinkObjId="EC-LF_JZ.463Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87569_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-450 4134,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_263ab40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-435 4134,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_21c9f20@0" ObjectIDND1="19008@x" ObjectIDZND0="34451@0" Pin0InfoVect0LinkObjId="EC-LF_JZ.463Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21c9f20_0" Pin1InfoVect1LinkObjId="SW-87569_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-435 4134,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ea650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4344,-435 4369,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="19012@x" ObjectIDND1="34452@x" ObjectIDZND0="g_217db90@0" Pin0InfoVect0LinkObjId="g_217db90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87598_0" Pin1InfoVect1LinkObjId="EC-LF_JZ.464Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4344,-435 4369,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27bc1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4343,-451 4343,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="19012@0" ObjectIDZND0="g_217db90@0" ObjectIDZND1="34452@x" Pin0InfoVect0LinkObjId="g_217db90_0" Pin0InfoVect1LinkObjId="EC-LF_JZ.464Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87598_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4343,-451 4343,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e22d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4343,-435 4343,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_217db90@0" ObjectIDND1="19012@x" ObjectIDZND0="34452@0" Pin0InfoVect0LinkObjId="EC-LF_JZ.464Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_217db90_0" Pin1InfoVect1LinkObjId="SW-87598_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4343,-435 4343,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_219f990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-419 4972,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="19016@x" ObjectIDND2="g_131ac60@0" ObjectIDZND0="g_23ff630@0" Pin0InfoVect0LinkObjId="g_23ff630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_275a230_0" Pin1InfoVect1LinkObjId="SW-87627_0" Pin1InfoVect2LinkObjId="g_131ac60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-419 4972,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2924f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-396 4947,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="0@0" ObjectIDZND0="19016@x" ObjectIDZND1="g_131ac60@0" ObjectIDZND2="34453@x" Pin0InfoVect0LinkObjId="SW-87627_0" Pin0InfoVect1LinkObjId="g_131ac60_0" Pin0InfoVect2LinkObjId="EC-LF_JZ.466Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_275a230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-396 4947,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e1f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4947,-419 4947,-442 4888,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="0@x" ObjectIDND1="g_23ff630@0" ObjectIDZND0="19016@x" ObjectIDZND1="g_131ac60@0" ObjectIDZND2="34453@x" Pin0InfoVect0LinkObjId="SW-87627_0" Pin0InfoVect1LinkObjId="g_131ac60_0" Pin0InfoVect2LinkObjId="EC-LF_JZ.466Ld_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_275a230_0" Pin1InfoVect1LinkObjId="g_23ff630_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4947,-419 4947,-442 4888,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2194bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4887,-426 4862,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="19016@x" ObjectIDND1="0@x" ObjectIDND2="g_23ff630@0" ObjectIDZND0="g_131ac60@0" Pin0InfoVect0LinkObjId="g_131ac60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-87627_0" Pin1InfoVect1LinkObjId="g_275a230_0" Pin1InfoVect2LinkObjId="g_23ff630_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4887,-426 4862,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_299c5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-442 4888,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="19016@x" ObjectIDND1="0@x" ObjectIDND2="g_23ff630@0" ObjectIDZND0="g_131ac60@0" ObjectIDZND1="34453@x" Pin0InfoVect0LinkObjId="g_131ac60_0" Pin0InfoVect1LinkObjId="EC-LF_JZ.466Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-87627_0" Pin1InfoVect1LinkObjId="g_275a230_0" Pin1InfoVect2LinkObjId="g_23ff630_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-442 4888,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_144a2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-426 4888,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_131ac60@0" ObjectIDND1="19016@x" ObjectIDND2="0@x" ObjectIDZND0="34453@0" Pin0InfoVect0LinkObjId="EC-LF_JZ.466Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_131ac60_0" Pin1InfoVect1LinkObjId="SW-87627_0" Pin1InfoVect2LinkObjId="g_275a230_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-426 4888,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14528d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4608,-451 4608,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="19020@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_275a230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4608,-451 4608,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_e82830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-948 4212,-931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="48335@0" ObjectIDZND0="48336@1" Pin0InfoVect0LinkObjId="SW-311526_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27e33d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-948 4212,-931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20db8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-920 4212,-907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="48336@0" ObjectIDZND0="18992@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-311526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-920 4212,-907 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="18991" cx="4343" cy="-690" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18991" cx="3669" cy="-690" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18991" cx="4608" cy="-690" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18991" cx="3895" cy="-690" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18991" cx="4888" cy="-690" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18991" cx="4134" cy="-690" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18991" cx="3543" cy="-690" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48335" cx="4212" cy="-948" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48335" cx="4212" cy="-948" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18991" cx="4212" cy="-690" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-52542" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3422.000000 -1093.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9252" ObjectName="DYN-LF_JZ"/>
     <cge:Meas_Ref ObjectId="52542"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2553ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,16)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2553ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2553ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,56)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2553ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,76)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2553ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,96)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2553ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2553ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,136)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2553ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,156)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_2553ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,176)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,16)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,76)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,96)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,136)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,156)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,196)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,216)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,256)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,276)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,296)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,316)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,336)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" graphid="g_24b8c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -664.000000) translate(0,356)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2188f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">旧庄变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ddb640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3422.000000 -716.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2923510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4226.000000 -963.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13de780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4225.000000 -791.000000) translate(0,12)">421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_135c250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4320.333333 -378.000000) translate(0,15)">新民线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1347c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4595.333333 -374.000000) translate(0,15)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19b6c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3867.333333 -375.000000) translate(0,15)">八屯线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d65110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4841.333333 -377.000000) translate(0,15)">六六七九线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2420400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4108.333333 -375.000000) translate(0,15)">街区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2437a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4222.000000 -739.000000) translate(0,12)">4211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e3a2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 -657.000000) translate(0,12)">4661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2ebc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4617.000000 -658.000000) translate(0,12)">4651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d0140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4353.000000 -662.000000) translate(0,12)">4641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20dbf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 -560.000000) translate(0,12)">4662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14d8680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4616.000000 -559.000000) translate(0,12)">4652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26f4600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4352.000000 -559.000000) translate(0,12)">4642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1385eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.000000 -657.000000) translate(0,12)">4631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_269a8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 -560.000000) translate(0,12)">4632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2405350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3906.000000 -655.000000) translate(0,12)">4621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12795d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3907.000000 -560.000000) translate(0,12)">4622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2182820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -656.000000) translate(0,12)">4611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d3690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3680.000000 -561.000000) translate(0,12)">4612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_245f0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3680.000000 -473.000000) translate(0,12)">4613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_26a28e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3255.000000 -330.000000) translate(0,16)">15758530128</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26bac20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4244.000000 -843.000000) translate(0,15)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e22fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3635.333333 -376.000000) translate(0,15)">塔石苴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12730b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4076.000000 -917.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1324260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4169.000000 -1148.000000) translate(0,15)">35kV旧庄线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b47f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -1029.000000) translate(0,15)">线路电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_270c210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4970.000000 -317.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26446d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3905.000000 -473.000000) translate(0,12)">4623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24419d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4140.000000 -472.000000) translate(0,12)">4633</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2454d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4349.000000 -472.000000) translate(0,12)">4643</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1405880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4616.000000 -474.000000) translate(0,12)">4653</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2637030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4894.000000 -477.000000) translate(0,12)">4663</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2412b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4219.000000 -1046.000000) translate(0,12)">3116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_218e220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3421.000000 -397.000000) translate(0,16)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e4180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3551.000000 -642.000000) translate(0,12)">4601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13bdf40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3616.000000 -608.000000) translate(0,12)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e00a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3844.000000 -610.000000) translate(0,12)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2193ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4080.000000 -607.000000) translate(0,12)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2669970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4287.000000 -610.000000) translate(0,12)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26eb180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4831.000000 -611.000000) translate(0,12)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293bc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3684.000000 -610.000000) translate(0,12)">461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_241a390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3911.000000 -610.000000) translate(0,12)">462</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1dc1510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4146.000000 -609.000000) translate(0,12)">463</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd4f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4357.000000 -611.000000) translate(0,12)">464</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_212ca00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4622.000000 -610.000000) translate(0,12)">465</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138afd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4900.000000 -611.000000) translate(0,12)">466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2409080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3161.000000 -774.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_24626b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3483.000000 -1150.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_21c16b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3481.000000 -1116.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2428420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.500000 -892.000000) translate(0,12)">档位（档）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13a3d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4256.000000 -878.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_27e3030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -291.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_27e3030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -291.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_25d86a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3255.000000 -301.500000) translate(0,16)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_25d86a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3255.000000 -301.500000) translate(0,36)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_25d86a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3255.000000 -301.500000) translate(0,56)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_25d8a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3503.500000 -1055.000000) translate(0,16)">AVC</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3487" y="-1066"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_JZ.LF_JZ_10M">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3482,-690 5001,-690 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18991" ObjectName="BS-LF_JZ.LF_JZ_10M"/>
    <cge:TPSR_Ref TObjectID="18991"/></metadata>
   <polyline fill="none" opacity="0" points="3482,-690 5001,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_JZ.XM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4203,-948 4219,-948 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48335" ObjectName="BS-LF_JZ.XM"/>
    <cge:TPSR_Ref TObjectID="48335"/></metadata>
   <polyline fill="none" opacity="0" points="4203,-948 4219,-948 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1ef5f10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 -1075.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291a0f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3563.000000 -468.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2924770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3501.000000 -427.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_205a1a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3689.000000 -429.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e94470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3916.000000 -427.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21c9f20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4155.000000 -429.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_217db90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 -429.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ff630">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4967.000000 -413.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_131ac60">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4868.000000 -420.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-219819" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4434.000000 -893.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219819" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18992"/>
     <cge:Term_Ref ObjectID="26391"/>
    <cge:TPSR_Ref TObjectID="18992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-87313" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -1008.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87313" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18993"/>
     <cge:Term_Ref ObjectID="26393"/>
    <cge:TPSR_Ref TObjectID="18993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87314" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -1008.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87314" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18993"/>
     <cge:Term_Ref ObjectID="26393"/>
    <cge:TPSR_Ref TObjectID="18993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87307" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -1008.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18993"/>
     <cge:Term_Ref ObjectID="26393"/>
    <cge:TPSR_Ref TObjectID="18993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-87326" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4332.000000 -801.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87326" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18995"/>
     <cge:Term_Ref ObjectID="26397"/>
    <cge:TPSR_Ref TObjectID="18995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87327" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4332.000000 -801.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87327" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18995"/>
     <cge:Term_Ref ObjectID="26397"/>
    <cge:TPSR_Ref TObjectID="18995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87320" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4332.000000 -801.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87320" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18995"/>
     <cge:Term_Ref ObjectID="26397"/>
    <cge:TPSR_Ref TObjectID="18995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-87341" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3660.000000 -349.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87341" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18997"/>
     <cge:Term_Ref ObjectID="26401"/>
    <cge:TPSR_Ref TObjectID="18997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87342" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3660.000000 -349.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87342" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18997"/>
     <cge:Term_Ref ObjectID="26401"/>
    <cge:TPSR_Ref TObjectID="18997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87335" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3660.000000 -349.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87335" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18997"/>
     <cge:Term_Ref ObjectID="26401"/>
    <cge:TPSR_Ref TObjectID="18997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-87354" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3887.000000 -349.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87354" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19001"/>
     <cge:Term_Ref ObjectID="26409"/>
    <cge:TPSR_Ref TObjectID="19001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87355" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3887.000000 -349.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87355" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19001"/>
     <cge:Term_Ref ObjectID="26409"/>
    <cge:TPSR_Ref TObjectID="19001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87348" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3887.000000 -349.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87348" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19001"/>
     <cge:Term_Ref ObjectID="26409"/>
    <cge:TPSR_Ref TObjectID="19001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-87367" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4125.000000 -348.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87367" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19005"/>
     <cge:Term_Ref ObjectID="26417"/>
    <cge:TPSR_Ref TObjectID="19005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87368" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4125.000000 -348.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87368" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19005"/>
     <cge:Term_Ref ObjectID="26417"/>
    <cge:TPSR_Ref TObjectID="19005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87361" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4125.000000 -348.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87361" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19005"/>
     <cge:Term_Ref ObjectID="26417"/>
    <cge:TPSR_Ref TObjectID="19005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-87380" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -350.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87380" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19009"/>
     <cge:Term_Ref ObjectID="26425"/>
    <cge:TPSR_Ref TObjectID="19009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87381" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -350.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87381" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19009"/>
     <cge:Term_Ref ObjectID="26425"/>
    <cge:TPSR_Ref TObjectID="19009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87374" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -350.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87374" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19009"/>
     <cge:Term_Ref ObjectID="26425"/>
    <cge:TPSR_Ref TObjectID="19009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-87405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4600.000000 -349.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19017"/>
     <cge:Term_Ref ObjectID="26441"/>
    <cge:TPSR_Ref TObjectID="19017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4600.000000 -349.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19017"/>
     <cge:Term_Ref ObjectID="26441"/>
    <cge:TPSR_Ref TObjectID="19017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87399" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4600.000000 -349.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19017"/>
     <cge:Term_Ref ObjectID="26441"/>
    <cge:TPSR_Ref TObjectID="19017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-87393" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -349.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19013"/>
     <cge:Term_Ref ObjectID="26433"/>
    <cge:TPSR_Ref TObjectID="19013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87394" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -349.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87394" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19013"/>
     <cge:Term_Ref ObjectID="26433"/>
    <cge:TPSR_Ref TObjectID="19013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87387" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -349.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19013"/>
     <cge:Term_Ref ObjectID="26433"/>
    <cge:TPSR_Ref TObjectID="19013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-87415" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -810.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87415" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18991"/>
     <cge:Term_Ref ObjectID="26390"/>
    <cge:TPSR_Ref TObjectID="18991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-87416" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -810.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87416" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18991"/>
     <cge:Term_Ref ObjectID="26390"/>
    <cge:TPSR_Ref TObjectID="18991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-87417" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -810.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87417" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18991"/>
     <cge:Term_Ref ObjectID="26390"/>
    <cge:TPSR_Ref TObjectID="18991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-87421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -810.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87421" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18991"/>
     <cge:Term_Ref ObjectID="26390"/>
    <cge:TPSR_Ref TObjectID="18991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-87418" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -810.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87418" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18991"/>
     <cge:Term_Ref ObjectID="26390"/>
    <cge:TPSR_Ref TObjectID="18991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-87419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -810.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87419" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18991"/>
     <cge:Term_Ref ObjectID="26390"/>
    <cge:TPSR_Ref TObjectID="18991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-87420" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -810.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87420" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18991"/>
     <cge:Term_Ref ObjectID="26390"/>
    <cge:TPSR_Ref TObjectID="18991"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
   <g href="35kV旧庄变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="73" x="4246" y="-879"/></g>
   <g href="35kV旧庄变10kV塔石苴线461间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="3680" y="-613"/></g>
   <g href="35kV旧庄变10kV八屯线462间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="3909" y="-612"/></g>
   <g href="35kV旧庄变10kV街区线463间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="17" qtmmishow="hidden" width="30" x="4144" y="-611"/></g>
   <g href="35kV旧庄变10kV新民线464间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="32" x="4353" y="-614"/></g>
   <g href="35kV旧庄变10kV备用线465间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4619" y="-611"/></g>
   <g href="35kV旧庄变10kV六六七九线466间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4897" y="-614"/></g>
   <g href="35kV旧庄变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="29" qtmmishow="hidden" width="82" x="3156" y="-780"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3472" y="-1158"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3471" y="-1122"/></g>
   <g href="AVC旧庄站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3486" y="-1067"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_299d2f0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4283.803279 976.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1318eb0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4274.000000 961.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_283f790" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4294.098361 945.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_2554c90" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4285.803279 802.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_2554e60" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4276.000000 787.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_2645d40" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4296.098361 771.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1343e30" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3614.803279 351.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1344050" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3605.000000 336.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_29211d0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3625.098361 320.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_13f5710" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3840.803279 352.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_13f5930" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3831.000000 337.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_283eb70" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 3851.098361 321.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_283ee60" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4077.803279 351.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_255b7c0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4068.000000 336.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_255b9c0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4088.098361 320.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_299f040" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4291.803279 352.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_299f2a0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4282.000000 337.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_299f540" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4302.098361 321.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_299f830" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4557.803279 351.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_291bf70" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4548.000000 336.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_291c140" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4568.098361 320.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_291ba50" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4817.803279 353.000000) translate(0,8)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_291bcb0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4808.000000 338.000000) translate(0,8)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_27e2ec0" transform="matrix(1.163934 -0.000000 0.000000 -1.533333 4828.098361 322.000000) translate(0,8)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_12dfd40" transform="matrix(1.649123 -0.000000 0.000000 -1.337838 3537.192982 780.033784) translate(0,9)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_27f4eb0" transform="matrix(1.649123 -0.000000 0.000000 -1.337838 3535.000000 766.216216) translate(0,9)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_27f5270" transform="matrix(1.649123 -0.000000 0.000000 -1.337838 3527.000000 736.216216) translate(0,9)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_29223e0" transform="matrix(1.649123 -0.000000 0.000000 -1.337838 3527.000000 720.216216) translate(0,9)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2922630" transform="matrix(1.649123 -0.000000 0.000000 -1.337838 3526.000000 750.216216) translate(0,9)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2912940" transform="matrix(1.649123 -0.000000 0.000000 -1.337838 3535.192982 794.851351) translate(0,9)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" graphid="g_2912b80" transform="matrix(1.649123 -0.000000 0.000000 -1.337838 3535.192982 810.331081) translate(0,9)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_JZ.463Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -384.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34451" ObjectName="EC-LF_JZ.463Ld"/>
    <cge:TPSR_Ref TObjectID="34451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_JZ.464Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -386.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34452" ObjectName="EC-LF_JZ.464Ld"/>
    <cge:TPSR_Ref TObjectID="34452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 -390.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_JZ.466Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4879.000000 -386.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34453" ObjectName="EC-LF_JZ.466Ld"/>
    <cge:TPSR_Ref TObjectID="34453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_JZ.462Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -386.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34450" ObjectName="EC-LF_JZ.462Ld"/>
    <cge:TPSR_Ref TObjectID="34450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_JZ.461Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 -390.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34449" ObjectName="EC-LF_JZ.461Ld"/>
    <cge:TPSR_Ref TObjectID="34449"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="73" x="4246" y="-879"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="73" x="4246" y="-879"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="3680" y="-613"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="3680" y="-613"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="3909" y="-612"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="3909" y="-612"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="17" qtmmishow="hidden" width="30" x="4144" y="-611"/>
    </a>
   <metadata/><rect fill="white" height="17" opacity="0" stroke="white" transform="" width="30" x="4144" y="-611"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="32" x="4353" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="32" x="4353" y="-614"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4619" y="-611"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4619" y="-611"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4897" y="-614"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4897" y="-614"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="29" qtmmishow="hidden" width="82" x="3156" y="-780"/>
    </a>
   <metadata/><rect fill="white" height="29" opacity="0" stroke="white" transform="" width="82" x="3156" y="-780"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3472" y="-1158"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3472" y="-1158"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3471" y="-1122"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3471" y="-1122"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3486" y="-1067"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3486" y="-1067"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-87490">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4202.000000 -1011.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18994" ObjectName="SW-LF_JZ.LF_JZ_3116SW"/>
     <cge:Meas_Ref ObjectId="87490"/>
    <cge:TPSR_Ref TObjectID="18994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87502">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 -713.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18996" ObjectName="SW-LF_JZ.LF_JZ_421SW"/>
     <cge:Meas_Ref ObjectId="87502"/>
    <cge:TPSR_Ref TObjectID="18996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87596">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -630.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19010" ObjectName="SW-LF_JZ.LF_JZ_4641SW"/>
     <cge:Meas_Ref ObjectId="87596"/>
    <cge:TPSR_Ref TObjectID="19010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87597">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -534.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19011" ObjectName="SW-LF_JZ.LF_JZ_4642SW"/>
     <cge:Meas_Ref ObjectId="87597"/>
    <cge:TPSR_Ref TObjectID="19011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87510">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 -629.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18998" ObjectName="SW-LF_JZ.LF_JZ_4611SW"/>
     <cge:Meas_Ref ObjectId="87510"/>
    <cge:TPSR_Ref TObjectID="18998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87511">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 -533.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18999" ObjectName="SW-LF_JZ.LF_JZ_4612SW"/>
     <cge:Meas_Ref ObjectId="87511"/>
    <cge:TPSR_Ref TObjectID="18999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87626">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4880.000000 -534.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19015" ObjectName="SW-LF_JZ.LF_JZ_4662SW"/>
     <cge:Meas_Ref ObjectId="87626"/>
    <cge:TPSR_Ref TObjectID="19015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87567">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -629.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19006" ObjectName="SW-LF_JZ.LF_JZ_4631SW"/>
     <cge:Meas_Ref ObjectId="87567"/>
    <cge:TPSR_Ref TObjectID="19006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87568">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -532.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19007" ObjectName="SW-LF_JZ.LF_JZ_4632SW"/>
     <cge:Meas_Ref ObjectId="87568"/>
    <cge:TPSR_Ref TObjectID="19007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87625">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4879.000000 -630.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19014" ObjectName="SW-LF_JZ.LF_JZ_4661SW"/>
     <cge:Meas_Ref ObjectId="87625"/>
    <cge:TPSR_Ref TObjectID="19014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87654">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 -629.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19018" ObjectName="SW-LF_JZ.LF_JZ_4651SW"/>
     <cge:Meas_Ref ObjectId="87654"/>
    <cge:TPSR_Ref TObjectID="19018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87655">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 -533.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19019" ObjectName="SW-LF_JZ.LF_JZ_4652SW"/>
     <cge:Meas_Ref ObjectId="87655"/>
    <cge:TPSR_Ref TObjectID="19019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87538">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -629.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19002" ObjectName="SW-LF_JZ.LF_JZ_4621SW"/>
     <cge:Meas_Ref ObjectId="87538"/>
    <cge:TPSR_Ref TObjectID="19002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87539">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -533.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19003" ObjectName="SW-LF_JZ.LF_JZ_4622SW"/>
     <cge:Meas_Ref ObjectId="87539"/>
    <cge:TPSR_Ref TObjectID="19003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.000000 -985.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87540">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -446.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19004" ObjectName="SW-LF_JZ.LF_JZ_4623SW"/>
     <cge:Meas_Ref ObjectId="87540"/>
    <cge:TPSR_Ref TObjectID="19004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87512">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 -447.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19000" ObjectName="SW-LF_JZ.LF_JZ_4613SW"/>
     <cge:Meas_Ref ObjectId="87512"/>
    <cge:TPSR_Ref TObjectID="19000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87627">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4879.000000 -447.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19016" ObjectName="SW-LF_JZ.LF_JZ_4663SW"/>
     <cge:Meas_Ref ObjectId="87627"/>
    <cge:TPSR_Ref TObjectID="19016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87656">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 -446.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19020" ObjectName="SW-LF_JZ.LF_JZ_4653SW"/>
     <cge:Meas_Ref ObjectId="87656"/>
    <cge:TPSR_Ref TObjectID="19020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87598">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -446.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19012" ObjectName="SW-LF_JZ.LF_JZ_4643SW"/>
     <cge:Meas_Ref ObjectId="87598"/>
    <cge:TPSR_Ref TObjectID="19012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87569">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -445.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19008" ObjectName="SW-LF_JZ.LF_JZ_4633SW"/>
     <cge:Meas_Ref ObjectId="87569"/>
    <cge:TPSR_Ref TObjectID="19008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 -339.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87691">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3534.000000 -610.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19022" ObjectName="SW-LF_JZ.LF_JZ_4601SW"/>
     <cge:Meas_Ref ObjectId="87691"/>
    <cge:TPSR_Ref TObjectID="19022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-311526">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.304348 4203.000000 -919.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48336" ObjectName="SW-LF_JZ.XB"/>
     <cge:Meas_Ref ObjectId="311526"/>
    <cge:TPSR_Ref TObjectID="48336"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-87313" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.290323 -0.000000 -0.000000 1.333333 3251.500000 -1025.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87313" ObjectName="LF_JZ:LF_JZ_311BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-87313" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.290323 -0.000000 -0.000000 1.333333 3250.500000 -987.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87313" ObjectName="LF_JZ:LF_JZ_311BK_P"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_JZ"/>
</svg>