<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-10" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-246 -5241 2789 1753">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape46">
    <circle cx="18" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="10" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <rect height="16" stroke-width="1" width="31" x="5" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="36" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape181">
    <polyline points="5,5 5,7 5,8 5,9 6,10 6,11 7,12 8,13 9,14 10,15 12,16 13,16 15,16 16,17 18,17 19,16 21,16 22,16 24,15 25,14 26,13 27,12 28,11 29,10 29,9 29,8 29,7 29,5 " stroke-width="0.402481"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape101">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="39" y2="44"/>
    <polyline DF8003:Layer="0" points="16,70 10,58 22,58 16,70 16,69 16,70 "/>
    <circle cx="16" cy="61" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="8" y2="8"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,39 40,39 40,8 " stroke-width="1"/>
    <circle cx="16" cy="39" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="39" y2="34"/>
   </symbol>
   <symbol id="nonConstantLoad:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="11" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="19" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="33" y2="33"/>
   </symbol>
   <symbol id="transformer2:shape44_0">
    <circle cx="26" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="27" y1="57" y2="27"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="26,57 2,57 1,57 " stroke-width="1"/>
    <polyline DF8003:Layer="0" points="27,14 21,27 34,27 27,14 27,15 27,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="87" y2="87"/>
    <polyline DF8003:Layer="0" points="25,87 21,78 31,78 25,87 "/>
   </symbol>
   <symbol id="transformer2:shape44_1">
    <circle cx="25" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="30" y1="57" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="21" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="57" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape17_0">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape17_1">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="voltageTransformer:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="58" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="58" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="38" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="51" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="59" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="37" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="70" y2="61"/>
    <ellipse cx="21" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="18" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape2">
    <circle cx="7" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="10" x2="15" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="32" x2="32" y1="35" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="10" x2="15" y1="26" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="15" x2="15" y1="24" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="14" x2="14" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="14" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="14" y1="17" y2="15"/>
    <ellipse cx="14" cy="15" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="25" cy="25" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="14" cy="25" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline points="26,27 42,27 42,6 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="39" x2="45" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="41" x2="43" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="38" x2="46" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="26" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="26" x2="26" y1="27" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="26" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="26" x2="26" y1="14" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="26" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="26" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="42" y1="15" y2="15"/>
    <ellipse cx="25" cy="15" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="32" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="39" x2="39" y1="35" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="50" x2="39" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="14" x2="14" y1="35" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="21" x2="21" y1="35" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="21" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="42" y2="42"/>
   </symbol>
   <symbol id="voltageTransformer:shape0">
    <circle cx="21" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f2eb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f34dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f36210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f371e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f383e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f38ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f39ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f3a5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f3ae40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f35630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f3b5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2f3bf60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f3f2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f3feb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f40860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f41170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f42960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f43660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f43f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f44910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f45af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f46470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f47550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f3daa0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f4cf40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f49270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2f4a650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2f4b430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2f58f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2f48940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1763" width="2799" x="-251" y="-5246"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="0" fill="none" points="1340,-4127 1354,-4118 1354,-4136 1340,-4128 1340,-4127 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="1311,-4127 1297,-4118 1297,-4136 1311,-4128 1311,-4127 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="228,-4162 214,-4162 221,-4150 228,-4162 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="228,-4138 214,-4138 221,-4150 228,-4138 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="308,-4092 294,-4092 301,-4104 308,-4092 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="0" fill="rgb(60,120,255)" points="304,-4107 298,-4107 301,-4117 304,-4107 " stroke="rgb(60,120,255)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2411.551724 -4506.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2304.551724 -4504.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2197.551724 -4501.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-25671">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1771.551724 -4506.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4093" ObjectName="SW-CX_XQ.CX_XQ_326BK"/>
     <cge:Meas_Ref ObjectId="25671"/>
    <cge:TPSR_Ref TObjectID="4093"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-25685">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1596.000000 -4658.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4098" ObjectName="SW-CX_XQ.CX_XQ_325BK"/>
     <cge:Meas_Ref ObjectId="25685"/>
    <cge:TPSR_Ref TObjectID="4098"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-133210">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1596.000000 -5074.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24366" ObjectName="SW-MD_AL.MD_AL_372BK"/>
     <cge:Meas_Ref ObjectId="133210"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-133190">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -5078.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24362" ObjectName="SW-MD_AL.MD_AL_371BK"/>
     <cge:Meas_Ref ObjectId="133190"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-26014">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -4654.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4182" ObjectName="SW-CX_XQ.CX_XQ_323BK"/>
     <cge:Meas_Ref ObjectId="26014"/>
    <cge:TPSR_Ref TObjectID="4182"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-132271">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 -5093.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24242" ObjectName="SW-MD_XJ.MD_XJ_361BK"/>
     <cge:Meas_Ref ObjectId="132271"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-25718">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 -4502.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4109" ObjectName="SW-CX_XQ.CX_XQ_322BK"/>
     <cge:Meas_Ref ObjectId="25718"/>
    <cge:TPSR_Ref TObjectID="4109"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-25700">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 -4499.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4104" ObjectName="SW-CX_XQ.CX_XQ_321BK"/>
     <cge:Meas_Ref ObjectId="25700"/>
    <cge:TPSR_Ref TObjectID="4104"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18056">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 995.000000 -4681.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2761" ObjectName="SW-CX_MD.CX_MD_384BK"/>
     <cge:Meas_Ref ObjectId="18056"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135206">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1097.000000 -3668.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24477" ObjectName="SW-MD_HP.MD_HP_391BK"/>
     <cge:Meas_Ref ObjectId="135206"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18870">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 745.674556 -4675.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2849" ObjectName="SW-CX_MD.CX_MD_386BK"/>
     <cge:Meas_Ref ObjectId="18870"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-136723">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 745.674556 -4216.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24710" ObjectName="SW-MD_LP.MD_LP_342BK"/>
     <cge:Meas_Ref ObjectId="136723"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-136704">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 573.000000 -4206.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24707" ObjectName="SW-MD_LP.MD_LP_341BK"/>
     <cge:Meas_Ref ObjectId="136704"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-17937">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 573.000000 -4680.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2748" ObjectName="SW-CX_MD.CX_MD_381BK"/>
     <cge:Meas_Ref ObjectId="17937"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-17997">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 -4685.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2756" ObjectName="SW-CX_MD.CX_MD_383BK"/>
     <cge:Meas_Ref ObjectId="17997"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135222">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 -3662.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24481" ObjectName="SW-MD_HP.MD_HP_392BK"/>
     <cge:Meas_Ref ObjectId="135222"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18584">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.000000 -4686.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2832" ObjectName="SW-CX_MD.CX_MD_385BK"/>
     <cge:Meas_Ref ObjectId="18584"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-17967">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 212.000000 -4687.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2752" ObjectName="SW-CX_MD.CX_MD_382BK"/>
     <cge:Meas_Ref ObjectId="17967"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18027">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -4880.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2760" ObjectName="SW-CX_MD.CX_MD_380BK"/>
     <cge:Meas_Ref ObjectId="18027"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-55893">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.000000 -4662.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10192" ObjectName="SW-CX_XQ.CX_XQ_312BK"/>
     <cge:Meas_Ref ObjectId="55893"/>
    <cge:TPSR_Ref TObjectID="10192"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-132299">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1065.000000 -5092.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24249" ObjectName="SW-MD_XJ.MD_XJ_362BK"/>
     <cge:Meas_Ref ObjectId="132299"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-106839">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 864.000000 -4683.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20867" ObjectName="SW-CX_MD.CX_MD_387BK"/>
     <cge:Meas_Ref ObjectId="106839"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-137072">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 863.000000 -4189.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24762" ObjectName="SW-CX_CC.CX_CC_371BK"/>
     <cge:Meas_Ref ObjectId="137072"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188717">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1277.000000 -3962.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28763" ObjectName="SW-MD_YD.MD_YD_382BK"/>
     <cge:Meas_Ref ObjectId="188717"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 -3963.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24561" ObjectName="SW-MD_YD.MD_YD_381BK"/>
     <cge:Meas_Ref ObjectId="135720"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-136203">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1562.000000 -4441.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24626" ObjectName="SW-MD_GY.MD_GY_341BK"/>
     <cge:Meas_Ref ObjectId="136203"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-218950">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 212.000000 -4005.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34067" ObjectName="SW-MD_SMC.MD_SMC_381BK"/>
     <cge:Meas_Ref ObjectId="218950"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-218993">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 242.000000 -3837.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34072" ObjectName="SW-MD_SMC.MD_SMC_382BK"/>
     <cge:Meas_Ref ObjectId="218993"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-220421">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 241.668750 -3544.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34422" ObjectName="SW-CX_FT.CX_FT_367BK"/>
     <cge:Meas_Ref ObjectId="220421"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="0" id="g_3cc4080">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2396.000000 -4182.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_221ff50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1091.000000 -4948.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_289b3c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1240.000000 -4952.000000)" xlink:href="#voltageTransformer:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b56400">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1294.000000 -4145.000000)" xlink:href="#voltageTransformer:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_375af40">
    <use class="BV-35KV" transform="matrix(0.000000 -0.998724 -1.588235 -0.000000 201.205882 -3848.535077)" xlink:href="#voltageTransformer:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_375e150">
    <use class="BV-35KV" transform="matrix(-1.071429 -0.000000 -0.000000 1.461538 193.000000 -3649.000000)" xlink:href="#voltageTransformer:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1224.000000 -4569.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1224.000000 -4569.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 999.000000 -4200.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 999.000000 -4200.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1797.000000 -4653.000000)" xlink:href="#transformer2:shape44_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1797.000000 -4653.000000)" xlink:href="#transformer2:shape44_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 162.000000 -4032.000000)" xlink:href="#transformer2:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 162.000000 -4032.000000)" xlink:href="#transformer2:shape17_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="NonConstantLoad_Layer">
   <g DF8003:Layer="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1815.000000 -4632.000000)" xlink:href="#nonConstantLoad:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_3b66fc0">
     <polyline DF8003:Layer="0" fill="none" points="2421,-4602 2421,-4590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2421,-4602 2421,-4590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_43cbfb0">
     <polyline DF8003:Layer="0" fill="none" points="2314,-4590 2314,-4602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2314,-4590 2314,-4602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3dd5290">
     <polyline DF8003:Layer="0" fill="none" points="2207,-4588 2207,-4602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2207,-4588 2207,-4602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f3db10">
     <polyline DF8003:Layer="0" fill="none" points="2421,-4363 2421,-4381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3dfdf80@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3dfdf80_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2421,-4363 2421,-4381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc8a80">
     <polyline DF8003:Layer="0" fill="none" points="2421,-4363 2449,-4363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3dfdf80@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3dfdf80_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2421,-4363 2449,-4363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3d88e80">
     <polyline DF8003:Layer="0" fill="none" points="2485,-4363 2501,-4363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_3ef3930@0" Pin0InfoVect0LinkObjId="g_3ef3930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2485,-4363 2501,-4363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3afcf70">
     <polyline DF8003:Layer="0" fill="none" points="2421,-4496 2421,-4514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2421,-4496 2421,-4514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e01070">
     <polyline DF8003:Layer="0" fill="none" points="2421,-4541 2421,-4554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2421,-4541 2421,-4554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_445a770">
     <polyline DF8003:Layer="0" fill="none" points="2314,-4488 2314,-4512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2314,-4488 2314,-4512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_374bb70">
     <polyline DF8003:Layer="0" fill="none" points="2314,-4539 2314,-4554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2314,-4539 2314,-4554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_44c62d0">
     <polyline DF8003:Layer="0" fill="none" points="2207,-4492 2207,-4509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2207,-4492 2207,-4509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f40200">
     <polyline DF8003:Layer="0" fill="none" points="2207,-4536 2207,-4552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2207,-4536 2207,-4552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2782e60">
     <polyline DF8003:Layer="0" fill="none" points="2421,-4312 2440,-4312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_3dfdf80@0" Pin0InfoVect0LinkObjId="g_3dfdf80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2421,-4312 2440,-4312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4551b50">
     <polyline DF8003:Layer="0" fill="none" points="2421,-4178 2421,-4312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3dfdf80@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3dfdf80_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2421,-4178 2421,-4312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2764bf0">
     <polyline DF8003:Layer="0" fill="none" points="2421,-4312 2421,-4363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3dfdf80@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3dfdf80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2421,-4312 2421,-4363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_45272e0">
     <polyline DF8003:Layer="0" fill="none" points="2421,-4417 2421,-4429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2421,-4417 2421,-4429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3760470">
     <polyline DF8003:Layer="0" fill="none" points="2421,-4429 2421,-4460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2421,-4429 2421,-4460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e38a60">
     <polyline DF8003:Layer="0" fill="none" points="2314,-4429 2314,-4452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2314,-4429 2314,-4452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3eb4b30">
     <polyline DF8003:Layer="0" fill="none" points="2207,-4429 2207,-4456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2207,-4429 2207,-4456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_45b3dc0">
     <polyline DF8003:Layer="0" fill="none" points="2421,-4429 2379,-4429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2421,-4429 2379,-4429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36b4bd0">
     <polyline DF8003:Layer="0" fill="none" points="2343,-4429 2314,-4429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2343,-4429 2314,-4429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e70ba0">
     <polyline DF8003:Layer="0" fill="none" points="2314,-4429 2272,-4429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2314,-4429 2272,-4429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_47e70a0">
     <polyline DF8003:Layer="0" fill="none" points="2236,-4429 2207,-4429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2236,-4429 2207,-4429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_452d620">
     <polyline DF8003:Layer="0" fill="none" points="2207,-4416 2207,-4429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2207,-4416 2207,-4429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_43cd030">
     <polyline DF8003:Layer="0" fill="none" points="2207,-4368 2207,-4380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3c60160@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c60160_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2207,-4368 2207,-4380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_373c9b0">
     <polyline DF8003:Layer="0" fill="none" points="2207,-4368 2224,-4368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3c60160@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c60160_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2207,-4368 2224,-4368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_48596b0">
     <polyline DF8003:Layer="0" fill="none" points="2260,-4368 2274,-4368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_28c4130@0" Pin0InfoVect0LinkObjId="g_28c4130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2260,-4368 2274,-4368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f2fc40">
     <polyline DF8003:Layer="0" fill="none" points="2315,-4312 2334,-4312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="4089@x" ObjectIDZND0="g_2870980@0" Pin0InfoVect0LinkObjId="g_2870980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-25666_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2315,-4312 2334,-4312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebc450">
     <polyline DF8003:Layer="0" fill="none" points="2314,-4312 2314,-4368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2870980@0" ObjectIDND1="4089@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2870980_0" Pin1InfoVect1LinkObjId="SW-25666_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2314,-4312 2314,-4368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37118f0">
     <polyline DF8003:Layer="0" fill="none" points="2313,-4368 2333,-4368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2870980@0" ObjectIDND1="4089@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2870980_0" Pin1InfoVect1LinkObjId="SW-25666_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2313,-4368 2333,-4368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_39d99f0">
     <polyline DF8003:Layer="0" fill="none" points="2369,-4368 2383,-4368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_3eea190@0" Pin0InfoVect0LinkObjId="g_3eea190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2369,-4368 2383,-4368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c40e30">
     <polyline DF8003:Layer="0" fill="none" points="1781,-4514 1781,-4497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4093@0" ObjectIDZND0="4089@1" Pin0InfoVect0LinkObjId="SW-25666_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1781,-4514 1781,-4497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f13d10">
     <polyline DF8003:Layer="0" fill="none" points="1781,-4605 1781,-4593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4050@0" ObjectIDZND0="4088@1" Pin0InfoVect0LinkObjId="SW-25665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f715e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1781,-4605 1781,-4593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45cabf0">
     <polyline DF8003:Layer="0" fill="none" points="1781,-4557 1781,-4541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4088@0" ObjectIDZND0="4093@1" Pin0InfoVect0LinkObjId="SW-25671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1781,-4557 1781,-4541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d9de30">
     <polyline DF8003:Layer="0" fill="none" points="1605,-4709 1605,-4693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4095@0" ObjectIDZND0="4098@1" Pin0InfoVect0LinkObjId="SW-25685_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-4709 1605,-4693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27701e0">
     <polyline DF8003:Layer="0" fill="none" points="1605,-4666 1605,-4654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4098@0" ObjectIDZND0="4094@1" Pin0InfoVect0LinkObjId="SW-25680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25685_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-4666 1605,-4654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f715e0">
     <polyline DF8003:Layer="0" fill="none" points="1605,-4618 1605,-4605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4094@0" ObjectIDZND0="4050@0" Pin0InfoVect0LinkObjId="g_27720b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-4618 1605,-4605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebfd70">
     <polyline DF8003:Layer="0" fill="none" points="1605,-5082 1605,-5065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24366@0" ObjectIDZND0="24369@1" Pin0InfoVect0LinkObjId="SW-133214_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-5082 1605,-5065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e6d810">
     <polyline DF8003:Layer="0" fill="none" points="1605,-5176 1605,-5161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24359@0" ObjectIDZND0="24367@1" Pin0InfoVect0LinkObjId="SW-133212_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39e0690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-5176 1605,-5161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39d7670">
     <polyline DF8003:Layer="0" fill="none" points="1605,-5125 1605,-5109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24367@0" ObjectIDZND0="24366@1" Pin0InfoVect0LinkObjId="SW-133210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133212_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-5125 1605,-5109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39e0690">
     <polyline DF8003:Layer="0" fill="none" points="1427,-5164 1427,-5176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24363@1" ObjectIDZND0="24359@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133192_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-5164 1427,-5176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4749b70">
     <polyline DF8003:Layer="0" fill="none" points="1427,-5113 1427,-5128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24362@1" ObjectIDZND0="24363@0" Pin0InfoVect0LinkObjId="SW-133192_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133190_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-5113 1427,-5128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44cc020">
     <polyline DF8003:Layer="0" fill="none" points="1427,-5069 1427,-5086 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24364@1" ObjectIDZND0="24362@0" Pin0InfoVect0LinkObjId="SW-133190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133193_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-5069 1427,-5086 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27829f0">
     <polyline DF8003:Layer="0" fill="none" points="1427,-5000 1446,-5000 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24365@x" ObjectIDND1="24364@x" ObjectIDND2="g_3ca2e30@0" ObjectIDZND0="g_22f07c0@0" Pin0InfoVect0LinkObjId="g_22f07c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-133194_0" Pin1InfoVect1LinkObjId="SW-133193_0" Pin1InfoVect2LinkObjId="g_3ca2e30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-5000 1446,-5000 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3766ac0">
     <polyline DF8003:Layer="0" fill="none" points="1427,-5033 1427,-5018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="24364@0" ObjectIDZND0="g_22f07c0@0" ObjectIDZND1="g_3ca2e30@0" ObjectIDZND2="24248@x" Pin0InfoVect0LinkObjId="g_22f07c0_0" Pin0InfoVect1LinkObjId="g_3ca2e30_0" Pin0InfoVect2LinkObjId="SW-132280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-5033 1427,-5018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_396bf00">
     <polyline DF8003:Layer="0" fill="none" points="1427,-5018 1427,-5000 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="24365@x" ObjectIDND1="24364@x" ObjectIDZND0="g_22f07c0@0" ObjectIDZND1="g_3ca2e30@0" ObjectIDZND2="24248@x" Pin0InfoVect0LinkObjId="g_22f07c0_0" Pin0InfoVect1LinkObjId="g_3ca2e30_0" Pin0InfoVect2LinkObjId="SW-132280_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-133194_0" Pin1InfoVect1LinkObjId="SW-133193_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-5018 1427,-5000 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e6d480">
     <polyline DF8003:Layer="0" fill="none" points="1427,-5018 1445,-5018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_22f07c0@0" ObjectIDND1="g_3ca2e30@0" ObjectIDND2="24248@x" ObjectIDZND0="24365@0" Pin0InfoVect0LinkObjId="SW-133194_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22f07c0_0" Pin1InfoVect1LinkObjId="g_3ca2e30_0" Pin1InfoVect2LinkObjId="SW-132280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-5018 1445,-5018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36ea1d0">
     <polyline DF8003:Layer="0" fill="none" points="1481,-5018 1495,-5018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24365@1" ObjectIDZND0="g_2871aa0@0" Pin0InfoVect0LinkObjId="g_2871aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133194_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1481,-5018 1495,-5018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dfa540">
     <polyline DF8003:Layer="0" fill="none" points="1427,-4704 1427,-4689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4185@0" ObjectIDZND0="4182@1" Pin0InfoVect0LinkObjId="SW-26014_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26024_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-4704 1427,-4689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224bab0">
     <polyline DF8003:Layer="0" fill="none" points="1427,-4662 1427,-4650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4182@0" ObjectIDZND0="4183@1" Pin0InfoVect0LinkObjId="SW-26022_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-4662 1427,-4650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22fa110">
     <polyline DF8003:Layer="0" fill="none" points="1427,-4614 1427,-4605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4183@0" ObjectIDZND0="4049@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26022_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-4614 1427,-4605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3debeb0">
     <polyline DF8003:Layer="0" fill="none" points="1605,-5001 1624,-5001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24369@x" ObjectIDND1="24371@x" ObjectIDND2="g_3ca45d0@0" ObjectIDZND0="g_38a06b0@0" Pin0InfoVect0LinkObjId="g_38a06b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-133214_0" Pin1InfoVect1LinkObjId="SW-133216_0" Pin1InfoVect2LinkObjId="g_3ca45d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-5001 1624,-5001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45b6890">
     <polyline DF8003:Layer="0" fill="none" points="1605,-5029 1605,-5018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24369@0" ObjectIDZND0="g_38a06b0@0" ObjectIDZND1="g_3ca45d0@0" ObjectIDZND2="g_3cc9c30@0" Pin0InfoVect0LinkObjId="g_38a06b0_0" Pin0InfoVect1LinkObjId="g_3ca45d0_0" Pin0InfoVect2LinkObjId="g_3cc9c30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133214_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-5029 1605,-5018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_372c610">
     <polyline DF8003:Layer="0" fill="none" points="1605,-5018 1605,-5001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24369@x" ObjectIDND1="24371@x" ObjectIDZND0="g_38a06b0@0" ObjectIDZND1="g_3ca45d0@0" ObjectIDZND2="g_3cc9c30@0" Pin0InfoVect0LinkObjId="g_38a06b0_0" Pin0InfoVect1LinkObjId="g_3ca45d0_0" Pin0InfoVect2LinkObjId="g_3cc9c30_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-133214_0" Pin1InfoVect1LinkObjId="SW-133216_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-5018 1605,-5001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f140a0">
     <polyline DF8003:Layer="0" fill="none" points="1226,-5089 1226,-5101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24244@1" ObjectIDZND0="24242@0" Pin0InfoVect0LinkObjId="SW-132271_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132275_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-5089 1226,-5101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45b79b0">
     <polyline DF8003:Layer="0" fill="none" points="1226,-5128 1226,-5140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24242@1" ObjectIDZND0="24243@0" Pin0InfoVect0LinkObjId="SW-132273_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-5128 1226,-5140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39ddb20">
     <polyline DF8003:Layer="0" fill="none" points="1226,-5040 1243,-5040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24245@x" ObjectIDND1="g_28b4120@0" ObjectIDND2="12195@x" ObjectIDZND0="24248@0" Pin0InfoVect0LinkObjId="SW-132280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132277_0" Pin1InfoVect1LinkObjId="g_28b4120_0" Pin1InfoVect2LinkObjId="SW-64573_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-5040 1243,-5040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4509870">
     <polyline DF8003:Layer="0" fill="none" points="1279,-5040 1293,-5040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24248@1" ObjectIDZND0="g_3d11d70@0" Pin0InfoVect0LinkObjId="g_3d11d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-5040 1293,-5040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3764110">
     <polyline DF8003:Layer="0" fill="none" points="1226,-5040 1226,-5053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24245@x" ObjectIDND1="g_28b4120@0" ObjectIDND2="12195@x" ObjectIDZND0="24244@0" Pin0InfoVect0LinkObjId="SW-132275_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132277_0" Pin1InfoVect1LinkObjId="g_28b4120_0" Pin1InfoVect2LinkObjId="SW-64573_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-5040 1226,-5053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4721a80">
     <polyline DF8003:Layer="0" fill="none" points="1226,-5022 1226,-5040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24245@x" ObjectIDND1="g_28b4120@0" ObjectIDND2="12195@x" ObjectIDZND0="24248@x" ObjectIDZND1="24244@x" Pin0InfoVect0LinkObjId="SW-132280_0" Pin0InfoVect1LinkObjId="SW-132275_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132277_0" Pin1InfoVect1LinkObjId="g_28b4120_0" Pin1InfoVect2LinkObjId="SW-64573_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-5022 1226,-5040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45b9360">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4605 1389,-4590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4049@0" ObjectIDZND0="4105@1" Pin0InfoVect0LinkObjId="SW-25713_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fa110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4605 1389,-4590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_286bfc0">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4554 1389,-4537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4105@0" ObjectIDZND0="4109@1" Pin0InfoVect0LinkObjId="SW-25718_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25713_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4554 1389,-4537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4508130">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4510 1389,-4499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4109@0" ObjectIDZND0="4106@1" Pin0InfoVect0LinkObjId="SW-25714_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25718_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4510 1389,-4499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44d8780">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4463 1389,-4451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4106@0" ObjectIDZND0="24567@x" ObjectIDZND1="24564@x" ObjectIDZND2="24562@x" Pin0InfoVect0LinkObjId="SW-135733_0" Pin0InfoVect1LinkObjId="SW-135724_0" Pin0InfoVect2LinkObjId="SW-135722_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25714_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4463 1389,-4451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b3bf0">
     <polyline DF8003:Layer="0" fill="none" points="1467,-4451 1467,-4438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_452e630@0" ObjectIDND1="4106@x" ObjectIDND2="24567@x" ObjectIDZND0="g_3ef3430@0" Pin0InfoVect0LinkObjId="g_3ef3430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_452e630_0" Pin1InfoVect1LinkObjId="SW-25714_0" Pin1InfoVect2LinkObjId="SW-135733_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1467,-4451 1467,-4438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_39cdfa0">
     <polyline DF8003:Layer="0" fill="none" points="2176,-4323 2176,-4343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3c60160@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c60160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2176,-4323 2176,-4343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_43ce1a0">
     <polyline DF8003:Layer="0" fill="none" points="2176,-4343 2207,-4343 2207,-4368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3c60160@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c60160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2176,-4343 2207,-4343 2207,-4368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_397a0a0">
     <polyline DF8003:Layer="0" fill="none" points="1499,-4434 1499,-4451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_452e630@0" ObjectIDZND0="24631@x" ObjectIDZND1="24629@x" ObjectIDZND2="4106@x" Pin0InfoVect0LinkObjId="SW-136209_0" Pin0InfoVect1LinkObjId="SW-136207_0" Pin0InfoVect2LinkObjId="SW-25714_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_452e630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1499,-4434 1499,-4451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_46e25d0">
     <polyline DF8003:Layer="0" fill="none" points="1505,-4451 1505,-4465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24629@x" ObjectIDND1="g_452e630@0" ObjectIDND2="4106@x" ObjectIDZND0="24631@0" Pin0InfoVect0LinkObjId="SW-136209_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-136207_0" Pin1InfoVect1LinkObjId="g_452e630_0" Pin1InfoVect2LinkObjId="SW-25714_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-4451 1505,-4465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b4730">
     <polyline DF8003:Layer="0" fill="none" points="1505,-4501 1505,-4515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="24631@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136209_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-4501 1505,-4515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_47c38f0">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4101 1434,-4101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_3b53e90@0" ObjectIDND1="g_3b556d0@0" ObjectIDND2="g_3b56400@0" ObjectIDZND0="24567@0" Pin0InfoVect0LinkObjId="SW-135733_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b53e90_0" Pin1InfoVect1LinkObjId="g_3b556d0_0" Pin1InfoVect2LinkObjId="g_3b56400_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4101 1434,-4101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4660e00">
     <polyline DF8003:Layer="0" fill="none" points="1494,-4100 1494,-4138 1508,-4138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="24567@x" ObjectIDND1="g_2758f10@0" ObjectIDZND0="24568@0" Pin0InfoVect0LinkObjId="SW-135734_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135733_0" Pin1InfoVect1LinkObjId="g_2758f10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-4100 1494,-4138 1508,-4138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3765240">
     <polyline DF8003:Layer="0" fill="none" points="1544,-4138 1561,-4138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24568@1" ObjectIDZND0="g_47c7f40@0" Pin0InfoVect0LinkObjId="g_47c7f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135734_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1544,-4138 1561,-4138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3763a50">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4065 1403,-4065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24562@x" ObjectIDND1="g_3b53e90@0" ObjectIDND2="g_3b556d0@0" ObjectIDZND0="24564@0" Pin0InfoVect0LinkObjId="SW-135724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135722_0" Pin1InfoVect1LinkObjId="g_3b53e90_0" Pin1InfoVect2LinkObjId="g_3b556d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4065 1403,-4065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_457e5d0">
     <polyline DF8003:Layer="0" fill="none" points="1439,-4065 1456,-4065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24564@1" ObjectIDZND0="g_3b751a0@0" Pin0InfoVect0LinkObjId="g_3b751a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1439,-4065 1456,-4065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e6bd20">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4065 1389,-4049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24564@x" ObjectIDND1="g_3b53e90@0" ObjectIDND2="g_3b556d0@0" ObjectIDZND0="24562@1" Pin0InfoVect0LinkObjId="SW-135722_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135724_0" Pin1InfoVect1LinkObjId="g_3b53e90_0" Pin1InfoVect2LinkObjId="g_3b556d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4065 1389,-4049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e6c600">
     <polyline DF8003:Layer="0" fill="none" points="1285,-4605 1285,-4585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4049@0" ObjectIDZND0="4099@1" Pin0InfoVect0LinkObjId="SW-25694_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fa110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-4605 1285,-4585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ea2da0">
     <polyline DF8003:Layer="0" fill="none" points="1285,-4549 1285,-4534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4099@0" ObjectIDZND0="4104@1" Pin0InfoVect0LinkObjId="SW-25700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25694_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-4549 1285,-4534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45be4e0">
     <polyline DF8003:Layer="0" fill="none" points="1285,-4486 1285,-4507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4100@1" ObjectIDZND0="4104@0" Pin0InfoVect0LinkObjId="SW-25700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25695_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-4486 1285,-4507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_49405e0">
     <polyline DF8003:Layer="0" fill="none" points="1211,-4410 1211,-4428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_44f3f10@0" ObjectIDZND0="0@x" ObjectIDZND1="4100@x" ObjectIDZND2="g_3e37870@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-25695_0" Pin0InfoVect2LinkObjId="g_3e37870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44f3f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1211,-4410 1211,-4428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_464b8f0">
     <polyline DF8003:Layer="0" fill="none" points="1211,-4428 1285,-4428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_44f3f10@0" ObjectIDND1="0@x" ObjectIDZND0="4100@x" ObjectIDZND1="g_3e37870@0" ObjectIDZND2="24480@x" Pin0InfoVect0LinkObjId="SW-25695_0" Pin0InfoVect1LinkObjId="g_3e37870_0" Pin0InfoVect2LinkObjId="SW-135210_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_44f3f10_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1211,-4428 1285,-4428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_47fdb20">
     <polyline DF8003:Layer="0" fill="none" points="1211,-4428 1211,-4452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_44f3f10@0" ObjectIDND1="4100@x" ObjectIDND2="g_3e37870@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_44f3f10_0" Pin1InfoVect1LinkObjId="SW-25695_0" Pin1InfoVect2LinkObjId="g_3e37870_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1211,-4428 1211,-4452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3738c20">
     <polyline DF8003:Layer="0" fill="none" points="1211,-4497 1211,-4522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1211,-4497 1211,-4522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e9a310">
     <polyline DF8003:Layer="0" fill="none" points="1004,-4770 1004,-4791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2762@1" ObjectIDZND0="2745@0" Pin0InfoVect0LinkObjId="g_286c200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18062_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1004,-4770 1004,-4791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_275c010">
     <polyline DF8003:Layer="0" fill="none" points="1004,-4716 1004,-4734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2761@1" ObjectIDZND0="2762@0" Pin0InfoVect0LinkObjId="SW-18062_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18056_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1004,-4716 1004,-4734 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2780060">
     <polyline DF8003:Layer="0" fill="none" points="1004,-4671 1004,-4689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2763@1" ObjectIDZND0="2761@0" Pin0InfoVect0LinkObjId="SW-18056_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18063_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1004,-4671 1004,-4689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_47a82a0">
     <polyline DF8003:Layer="0" fill="none" points="1054,-4572 1063,-4572 1063,-4542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2764@1" ObjectIDZND0="9691@0" Pin0InfoVect0LinkObjId="g_387fef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18064_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-4572 1063,-4572 1063,-4542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3764de0">
     <polyline DF8003:Layer="0" fill="none" points="986,-4336 1004,-4336 986,-4336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3e70720@0" ObjectIDND1="0@x" ObjectIDZND0="g_3e70720@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3e70720_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3e70720_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="986,-4336 1004,-4336 986,-4336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_276e880">
     <polyline DF8003:Layer="0" fill="none" points="986,-4336 986,-4353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3e70720@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_3e70720@0" Pin0InfoVect0LinkObjId="g_3e70720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e70720_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="986,-4336 986,-4353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ea0660">
     <polyline DF8003:Layer="0" fill="none" points="986,-4336 986,-4310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3e70720@0" ObjectIDND1="0@x" ObjectIDND2="g_3e70720@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e70720_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_3e70720_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="986,-4336 986,-4310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4592510">
     <polyline DF8003:Layer="0" fill="none" points="986,-4265 986,-4247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="986,-4265 986,-4247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a70e0">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4451 1389,-4127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4106@x" ObjectIDND1="g_452e630@0" ObjectIDND2="g_3ef3430@0" ObjectIDZND0="24567@x" ObjectIDZND1="24564@x" ObjectIDZND2="24562@x" Pin0InfoVect0LinkObjId="SW-135733_0" Pin0InfoVect1LinkObjId="SW-135724_0" Pin0InfoVect2LinkObjId="SW-135722_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-25714_0" Pin1InfoVect1LinkObjId="g_452e630_0" Pin1InfoVect2LinkObjId="g_3ef3430_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4451 1389,-4127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f41060">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4127 1389,-4101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3b53e90@0" ObjectIDND1="g_3b556d0@0" ObjectIDND2="g_3b56400@0" ObjectIDZND0="24567@x" ObjectIDZND1="24564@x" ObjectIDZND2="24562@x" Pin0InfoVect0LinkObjId="SW-135733_0" Pin0InfoVect1LinkObjId="SW-135724_0" Pin0InfoVect2LinkObjId="SW-135722_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b53e90_0" Pin1InfoVect1LinkObjId="g_3b556d0_0" Pin1InfoVect2LinkObjId="g_3b56400_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4127 1389,-4101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36eb880">
     <polyline DF8003:Layer="0" fill="none" points="1106,-3721 1106,-3703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24479@0" ObjectIDZND0="24477@1" Pin0InfoVect0LinkObjId="SW-135206_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135209_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1106,-3721 1106,-3703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_291c590">
     <polyline DF8003:Layer="0" fill="none" points="1106,-3676 1106,-3656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24477@0" ObjectIDZND0="24478@1" Pin0InfoVect0LinkObjId="SW-135208_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135206_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1106,-3676 1106,-3656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3760a20">
     <polyline DF8003:Layer="0" fill="none" points="1106,-3620 1106,-3596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24478@0" ObjectIDZND0="24473@0" Pin0InfoVect0LinkObjId="g_3d1fbd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135208_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1106,-3620 1106,-3596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b574a0">
     <polyline DF8003:Layer="0" fill="none" points="1122,-3813 1106,-3813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_46b7880@0" ObjectIDZND0="24480@x" ObjectIDZND1="24479@x" ObjectIDZND2="g_3e37870@0" Pin0InfoVect0LinkObjId="SW-135210_0" Pin0InfoVect1LinkObjId="SW-135209_0" Pin0InfoVect2LinkObjId="g_3e37870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_46b7880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1122,-3813 1106,-3813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c91ea0">
     <polyline DF8003:Layer="0" fill="none" points="1106,-3852 1125,-3852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24480@x" ObjectIDND1="24479@x" ObjectIDND2="g_46b7880@0" ObjectIDZND0="g_3e37870@0" Pin0InfoVect0LinkObjId="g_3e37870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135210_0" Pin1InfoVect1LinkObjId="SW-135209_0" Pin1InfoVect2LinkObjId="g_46b7880_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1106,-3852 1125,-3852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f25e60">
     <polyline DF8003:Layer="0" fill="none" points="1106,-3813 1106,-3770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3e37870@0" ObjectIDND1="2764@x" ObjectIDND2="2763@x" ObjectIDZND0="24480@x" ObjectIDZND1="24479@x" Pin0InfoVect0LinkObjId="SW-135210_0" Pin0InfoVect1LinkObjId="SW-135209_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e37870_0" Pin1InfoVect1LinkObjId="SW-18064_0" Pin1InfoVect2LinkObjId="SW-18063_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1106,-3813 1106,-3770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_47c7c60">
     <polyline DF8003:Layer="0" fill="none" points="1106,-3770 1106,-3757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3e37870@0" ObjectIDND1="2764@x" ObjectIDND2="2763@x" ObjectIDZND0="24479@1" Pin0InfoVect0LinkObjId="SW-135209_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e37870_0" Pin1InfoVect1LinkObjId="SW-18064_0" Pin1InfoVect2LinkObjId="SW-18063_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1106,-3770 1106,-3757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4755990">
     <polyline DF8003:Layer="0" fill="none" points="1106,-3770 1123,-3770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3e37870@0" ObjectIDND1="2764@x" ObjectIDND2="2763@x" ObjectIDZND0="24480@0" Pin0InfoVect0LinkObjId="SW-135210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e37870_0" Pin1InfoVect1LinkObjId="SW-18064_0" Pin1InfoVect2LinkObjId="SW-18063_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1106,-3770 1123,-3770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e8340">
     <polyline DF8003:Layer="0" fill="none" points="1159,-3770 1172,-3770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24480@1" ObjectIDZND0="g_2486430@0" Pin0InfoVect0LinkObjId="g_2486430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1159,-3770 1172,-3770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f3fd40">
     <polyline DF8003:Layer="0" fill="none" points="755,-4662 755,-4683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2847@1" ObjectIDZND0="2849@0" Pin0InfoVect0LinkObjId="SW-18870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18858_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-4662 755,-4683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ec6360">
     <polyline DF8003:Layer="0" fill="none" points="755,-4710 755,-4725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2849@1" ObjectIDZND0="2846@0" Pin0InfoVect0LinkObjId="SW-18857_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18870_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-4710 755,-4725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_286c200">
     <polyline DF8003:Layer="0" fill="none" points="755,-4761 755,-4791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2846@1" ObjectIDZND0="2745@0" Pin0InfoVect0LinkObjId="g_3e9a310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18857_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-4761 755,-4791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_387fef0">
     <polyline DF8003:Layer="0" fill="none" points="807,-4570 821,-4570 821,-4542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2848@1" ObjectIDZND0="9691@0" Pin0InfoVect0LinkObjId="g_47a82a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18859_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="807,-4570 821,-4570 821,-4542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44c6000">
     <polyline DF8003:Layer="0" fill="none" points="755,-4307 741,-4307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_36ebcc0@0" ObjectIDND1="2848@x" ObjectIDND2="2847@x" ObjectIDZND0="g_275a100@0" Pin0InfoVect0LinkObjId="g_275a100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36ebcc0_0" Pin1InfoVect1LinkObjId="SW-18859_0" Pin1InfoVect2LinkObjId="SW-18858_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-4307 741,-4307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3760690">
     <polyline DF8003:Layer="0" fill="none" points="752,-4383 738,-4383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_275a100@0" ObjectIDND1="24711@x" ObjectIDND2="2848@x" ObjectIDZND0="g_36ebcc0@0" Pin0InfoVect0LinkObjId="g_36ebcc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_275a100_0" Pin1InfoVect1LinkObjId="SW-136728_0" Pin1InfoVect2LinkObjId="SW-18859_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-4383 738,-4383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3717fc0">
     <polyline DF8003:Layer="0" fill="none" points="755,-4167 755,-4193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24703@0" ObjectIDZND0="24712@0" Pin0InfoVect0LinkObjId="SW-136728_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e35f30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-4167 755,-4193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28743c0">
     <polyline DF8003:Layer="0" fill="none" points="755,-4210 755,-4224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24712@1" ObjectIDZND0="24710@0" Pin0InfoVect0LinkObjId="SW-136723_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136728_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-4210 755,-4224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45be790">
     <polyline DF8003:Layer="0" fill="none" points="582,-4290 569,-4290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="24708@x" ObjectIDND1="g_3eceea0@0" ObjectIDND2="2751@x" ObjectIDZND0="g_45567f0@0" Pin0InfoVect0LinkObjId="g_45567f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-136709_0" Pin1InfoVect1LinkObjId="g_3eceea0_0" Pin1InfoVect2LinkObjId="SW-17945_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="582,-4290 569,-4290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e35f30">
     <polyline DF8003:Layer="0" fill="none" points="582,-4183 582,-4167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24709@0" ObjectIDZND0="24703@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136709_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="582,-4183 582,-4167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ecec40">
     <polyline DF8003:Layer="0" fill="none" points="582,-4290 582,-4274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_45567f0@0" ObjectIDND1="g_3eceea0@0" ObjectIDND2="2751@x" ObjectIDZND0="24708@0" Pin0InfoVect0LinkObjId="SW-136709_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_45567f0_0" Pin1InfoVect1LinkObjId="g_3eceea0_0" Pin1InfoVect2LinkObjId="SW-17945_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="582,-4290 582,-4274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f3d580">
     <polyline DF8003:Layer="0" fill="none" points="582,-4257 582,-4241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24708@1" ObjectIDZND0="24707@1" Pin0InfoVect0LinkObjId="SW-136704_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136709_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="582,-4257 582,-4241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2781d70">
     <polyline DF8003:Layer="0" fill="none" points="582,-4214 582,-4200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24707@0" ObjectIDZND0="24709@1" Pin0InfoVect0LinkObjId="SW-136709_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136704_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="582,-4214 582,-4200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4638df0">
     <polyline DF8003:Layer="0" fill="none" points="583,-4340 596,-4340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_45567f0@0" ObjectIDND1="24708@x" ObjectIDND2="2751@x" ObjectIDZND0="g_3eceea0@0" Pin0InfoVect0LinkObjId="g_3eceea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_45567f0_0" Pin1InfoVect1LinkObjId="SW-136709_0" Pin1InfoVect2LinkObjId="SW-17945_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="583,-4340 596,-4340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_372d590">
     <polyline DF8003:Layer="0" fill="none" points="582,-4340 582,-4290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3eceea0@0" ObjectIDND1="2751@x" ObjectIDND2="2750@x" ObjectIDZND0="g_45567f0@0" ObjectIDZND1="24708@x" Pin0InfoVect0LinkObjId="g_45567f0_0" Pin0InfoVect1LinkObjId="SW-136709_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3eceea0_0" Pin1InfoVect1LinkObjId="SW-17945_0" Pin1InfoVect2LinkObjId="SW-17944_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="582,-4340 582,-4290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_372dd70">
     <polyline DF8003:Layer="0" fill="none" points="582,-4688 582,-4667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2748@0" ObjectIDZND0="2750@1" Pin0InfoVect0LinkObjId="SW-17944_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17937_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="582,-4688 582,-4667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_372dfd0">
     <polyline DF8003:Layer="0" fill="none" points="582,-4792 582,-4766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2744@0" ObjectIDZND0="2749@1" Pin0InfoVect0LinkObjId="SW-17943_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d36f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="582,-4792 582,-4766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39d5e50">
     <polyline DF8003:Layer="0" fill="none" points="582,-4730 582,-4715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2749@0" ObjectIDZND0="2748@1" Pin0InfoVect0LinkObjId="SW-17937_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17943_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="582,-4730 582,-4715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27855a0">
     <polyline DF8003:Layer="0" fill="none" points="470,-4693 470,-4672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2756@0" ObjectIDZND0="2758@1" Pin0InfoVect0LinkObjId="SW-18004_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-4693 470,-4672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2785800">
     <polyline DF8003:Layer="0" fill="none" points="470,-4792 470,-4771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2744@0" ObjectIDZND0="2757@1" Pin0InfoVect0LinkObjId="SW-18003_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d36f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-4792 470,-4771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27845a0">
     <polyline DF8003:Layer="0" fill="none" points="470,-4735 470,-4720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2757@0" ObjectIDZND0="2756@1" Pin0InfoVect0LinkObjId="SW-17997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18003_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-4735 470,-4720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2781b30">
     <polyline DF8003:Layer="0" fill="none" points="470,-3818 465,-3818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2784800@0" ObjectIDZND0="g_3735c80@0" ObjectIDZND1="2759@x" ObjectIDZND2="2758@x" Pin0InfoVect0LinkObjId="g_3735c80_0" Pin0InfoVect1LinkObjId="SW-18005_0" Pin0InfoVect2LinkObjId="SW-18004_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2784800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="470,-3818 465,-3818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3735a20">
     <polyline DF8003:Layer="0" fill="none" points="470,-3868 489,-3868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2784800@0" ObjectIDND1="2759@x" ObjectIDND2="2758@x" ObjectIDZND0="g_3735c80@0" Pin0InfoVect0LinkObjId="g_3735c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2784800_0" Pin1InfoVect1LinkObjId="SW-18005_0" Pin1InfoVect2LinkObjId="SW-18004_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-3868 489,-3868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44d24b0">
     <polyline DF8003:Layer="0" fill="none" points="470,-3868 470,-3818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3735c80@0" ObjectIDND1="2759@x" ObjectIDND2="2758@x" ObjectIDZND0="g_2784800@0" Pin0InfoVect0LinkObjId="g_2784800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3735c80_0" Pin1InfoVect1LinkObjId="SW-18005_0" Pin1InfoVect2LinkObjId="SW-18004_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-3868 470,-3818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d26780">
     <polyline DF8003:Layer="0" fill="none" points="470,-3818 470,-3768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_3735c80@0" ObjectIDND1="2759@x" ObjectIDND2="2758@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3735c80_0" Pin1InfoVect1LinkObjId="SW-18005_0" Pin1InfoVect2LinkObjId="SW-18004_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="470,-3818 470,-3768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4592ce0">
     <polyline DF8003:Layer="0" fill="none" points="433,-3768 440,-3768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24484@0" ObjectIDZND0="g_4592f40@0" Pin0InfoVect0LinkObjId="g_4592f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135226_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="433,-3768 440,-3768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1f970">
     <polyline DF8003:Layer="0" fill="none" points="470,-3670 470,-3650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24481@0" ObjectIDZND0="24482@1" Pin0InfoVect0LinkObjId="SW-135224_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135222_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-3670 470,-3650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1fbd0">
     <polyline DF8003:Layer="0" fill="none" points="470,-3614 470,-3596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24482@0" ObjectIDZND0="24473@0" Pin0InfoVect0LinkObjId="g_3760a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135224_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-3614 470,-3596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1fe30">
     <polyline DF8003:Layer="0" fill="none" points="470,-3768 470,-3751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="24483@1" Pin0InfoVect0LinkObjId="SW-135225_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-3768 470,-3751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d9c960">
     <polyline DF8003:Layer="0" fill="none" points="470,-3715 470,-3697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24483@0" ObjectIDZND0="24481@1" Pin0InfoVect0LinkObjId="SW-135222_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135225_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-3715 470,-3697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d9cbc0">
     <polyline DF8003:Layer="0" fill="none" points="470,-4575 470,-3868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="2759@x" ObjectIDND1="2758@x" ObjectIDZND0="g_3735c80@0" ObjectIDZND1="g_2784800@0" Pin0InfoVect0LinkObjId="g_3735c80_0" Pin0InfoVect1LinkObjId="g_2784800_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18005_0" Pin1InfoVect1LinkObjId="SW-18004_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="470,-4575 470,-3868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce7750">
     <polyline DF8003:Layer="0" fill="none" points="521,-4574 531,-4574 531,-4542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2759@1" ObjectIDZND0="9691@0" Pin0InfoVect0LinkObjId="g_47a82a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="521,-4574 531,-4574 531,-4542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce79b0">
     <polyline DF8003:Layer="0" fill="none" points="582,-4573 582,-4340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="2751@x" ObjectIDND1="2750@x" ObjectIDZND0="g_3eceea0@0" ObjectIDZND1="g_45567f0@0" ObjectIDZND2="24708@x" Pin0InfoVect0LinkObjId="g_3eceea0_0" Pin0InfoVect1LinkObjId="g_45567f0_0" Pin0InfoVect2LinkObjId="SW-136709_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-17945_0" Pin1InfoVect1LinkObjId="SW-17944_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="582,-4573 582,-4340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d88150">
     <polyline DF8003:Layer="0" fill="none" points="641,-4573 649,-4573 649,-4542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2751@1" ObjectIDZND0="9691@0" Pin0InfoVect0LinkObjId="g_47a82a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17945_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-4573 649,-4573 649,-4542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d08f20">
     <polyline DF8003:Layer="0" fill="none" points="273,-4573 282,-4573 282,-4542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2755@1" ObjectIDZND0="9691@0" Pin0InfoVect0LinkObjId="g_47a82a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17975_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="273,-4573 282,-4573 282,-4542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_452d180">
     <polyline DF8003:Layer="0" fill="none" points="308,-4694 308,-4673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2832@0" ObjectIDZND0="2834@1" Pin0InfoVect0LinkObjId="SW-18591_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18584_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="308,-4694 308,-4673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1b0d0">
     <polyline DF8003:Layer="0" fill="none" points="308,-4637 308,-4542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2834@0" ObjectIDZND0="9691@0" Pin0InfoVect0LinkObjId="g_47a82a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="308,-4637 308,-4542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1b330">
     <polyline DF8003:Layer="0" fill="none" points="308,-4792 308,-4772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2744@0" ObjectIDZND0="2833@1" Pin0InfoVect0LinkObjId="SW-18590_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d36f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="308,-4792 308,-4772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1b590">
     <polyline DF8003:Layer="0" fill="none" points="308,-4736 308,-4721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2833@0" ObjectIDZND0="2832@1" Pin0InfoVect0LinkObjId="SW-18584_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="308,-4736 308,-4721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db5110">
     <polyline DF8003:Layer="0" fill="none" points="221,-4792 221,-4773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2744@0" ObjectIDZND0="2753@1" Pin0InfoVect0LinkObjId="SW-17973_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d36f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4792 221,-4773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db5370">
     <polyline DF8003:Layer="0" fill="none" points="221,-4737 221,-4722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2753@0" ObjectIDZND0="2752@1" Pin0InfoVect0LinkObjId="SW-17967_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17973_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4737 221,-4722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3db55d0">
     <polyline DF8003:Layer="0" fill="none" points="221,-4695 221,-4674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2752@0" ObjectIDZND0="2754@1" Pin0InfoVect0LinkObjId="SW-17974_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17967_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4695 221,-4674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d97aa0">
     <polyline DF8003:Layer="0" fill="none" points="237,-4573 221,-4573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="2755@0" ObjectIDZND0="2754@x" ObjectIDZND1="34066@x" ObjectIDZND2="34069@x" Pin0InfoVect0LinkObjId="SW-17974_0" Pin0InfoVect1LinkObjId="SW-134324_0" Pin0InfoVect2LinkObjId="SW-218948_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17975_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="237,-4573 221,-4573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d97d00">
     <polyline DF8003:Layer="0" fill="none" points="221,-4573 221,-4638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="2755@x" ObjectIDND1="34066@x" ObjectIDND2="34069@x" ObjectIDZND0="2754@0" Pin0InfoVect0LinkObjId="SW-17974_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-17975_0" Pin1InfoVect1LinkObjId="SW-134324_0" Pin1InfoVect2LinkObjId="SW-218948_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4573 221,-4638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c911f0">
     <polyline DF8003:Layer="0" fill="none" points="221,-3956 221,-3942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34068@0" ObjectIDZND0="24419@0" Pin0InfoVect0LinkObjId="g_3cb38f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-3956 221,-3942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d36f70">
     <polyline DF8003:Layer="0" fill="none" points="603,-4810 603,-4792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3022@0" ObjectIDZND0="2744@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18042_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="603,-4810 603,-4792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c82a80">
     <polyline DF8003:Layer="0" fill="none" points="692,-4791 692,-4813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2745@0" ObjectIDZND0="20854@0" Pin0InfoVect0LinkObjId="SW-106657_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e9a310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="692,-4791 692,-4813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca7560">
     <polyline DF8003:Layer="0" fill="none" points="692,-4849 692,-4890 663,-4890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20854@1" ObjectIDZND0="2760@0" Pin0InfoVect0LinkObjId="SW-18027_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106657_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="692,-4849 692,-4890 663,-4890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2772820">
     <polyline DF8003:Layer="0" fill="none" points="636,-4890 603,-4890 603,-4846 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2760@1" ObjectIDZND0="3022@1" Pin0InfoVect0LinkObjId="SW-18042_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18027_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-4890 603,-4890 603,-4846 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ccc840">
     <polyline DF8003:Layer="0" fill="none" points="1468,-4605 1468,-4623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4049@0" ObjectIDZND0="10193@0" Pin0InfoVect0LinkObjId="SW-55895_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22fa110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-4605 1468,-4623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cccaa0">
     <polyline DF8003:Layer="0" fill="none" points="1468,-4659 1468,-4672 1502,-4672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10193@1" ObjectIDZND0="10192@1" Pin0InfoVect0LinkObjId="SW-55893_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55895_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-4659 1468,-4672 1502,-4672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2771e50">
     <polyline DF8003:Layer="0" fill="none" points="1529,-4672 1559,-4672 1559,-4658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10192@0" ObjectIDZND0="10194@1" Pin0InfoVect0LinkObjId="SW-55896_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55893_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1529,-4672 1559,-4672 1559,-4658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27720b0">
     <polyline DF8003:Layer="0" fill="none" points="1559,-4622 1559,-4605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10194@0" ObjectIDZND0="4050@0" Pin0InfoVect0LinkObjId="g_3f715e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55896_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1559,-4622 1559,-4605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_42fabb0">
     <polyline DF8003:Layer="0" fill="none" points="2418,-4252 2418,-4262 2349,-4262 2349,-4242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3cc4080@0" ObjectIDZND0="g_3cc32f0@0" Pin0InfoVect0LinkObjId="g_3cc32f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cc4080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2418,-4252 2418,-4262 2349,-4262 2349,-4242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42fc380">
     <polyline DF8003:Layer="0" fill="none" points="1558,-4758 1551,-4758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="19704@0" ObjectIDZND0="g_42fbd30@1" Pin0InfoVect0LinkObjId="g_42fbd30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93023_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1558,-4758 1551,-4758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42fc5e0">
     <polyline DF8003:Layer="0" fill="none" points="1520,-4758 1508,-4758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_42fbd30@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42fbd30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1520,-4758 1508,-4758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ccfe50">
     <polyline DF8003:Layer="0" fill="none" points="1594,-4758 1605,-4758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="19704@1" ObjectIDZND0="4095@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3d15310@0" Pin0InfoVect0LinkObjId="SW-25681_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3d15310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93023_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1594,-4758 1605,-4758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc1ac0">
     <polyline DF8003:Layer="0" fill="none" points="1074,-5085 1074,-5100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24251@1" ObjectIDZND0="24249@0" Pin0InfoVect0LinkObjId="SW-132299_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132302_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-5085 1074,-5100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc1d20">
     <polyline DF8003:Layer="0" fill="none" points="1074,-5127 1074,-5139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24249@1" ObjectIDZND0="24250@0" Pin0InfoVect0LinkObjId="SW-132301_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-5127 1074,-5139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dab4a0">
     <polyline DF8003:Layer="0" fill="none" points="1074,-5036 1091,-5036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24251@x" ObjectIDND1="g_3cc1f80@0" ObjectIDND2="24252@x" ObjectIDZND0="24253@0" Pin0InfoVect0LinkObjId="SW-132304_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132302_0" Pin1InfoVect1LinkObjId="g_3cc1f80_0" Pin1InfoVect2LinkObjId="SW-132303_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-5036 1091,-5036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dab700">
     <polyline DF8003:Layer="0" fill="none" points="1127,-5036 1141,-5036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24253@1" ObjectIDZND0="g_3dab960@0" Pin0InfoVect0LinkObjId="g_3dab960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132304_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1127,-5036 1141,-5036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc9770">
     <polyline DF8003:Layer="0" fill="none" points="1074,-5036 1074,-5049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24253@x" ObjectIDND1="g_3cc1f80@0" ObjectIDND2="24252@x" ObjectIDZND0="24251@0" Pin0InfoVect0LinkObjId="SW-132302_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132304_0" Pin1InfoVect1LinkObjId="g_3cc1f80_0" Pin1InfoVect2LinkObjId="SW-132303_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-5036 1074,-5049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc99d0">
     <polyline DF8003:Layer="0" fill="none" points="1074,-5017 1074,-5036 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3cc1f80@0" ObjectIDND1="24252@x" ObjectIDND2="g_3cc9c30@0" ObjectIDZND0="24251@x" ObjectIDZND1="24253@x" Pin0InfoVect0LinkObjId="SW-132302_0" Pin0InfoVect1LinkObjId="SW-132304_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3cc1f80_0" Pin1InfoVect1LinkObjId="SW-132303_0" Pin1InfoVect2LinkObjId="g_3cc9c30_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-5017 1074,-5036 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ccb930">
     <polyline DF8003:Layer="0" fill="none" points="1605,-4914 1439,-4914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3ca45d0@0" ObjectIDND1="g_38a06b0@0" ObjectIDND2="24369@x" ObjectIDZND0="g_3cc9c30@0" Pin0InfoVect0LinkObjId="g_3cc9c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ca45d0_0" Pin1InfoVect1LinkObjId="g_38a06b0_0" Pin1InfoVect2LinkObjId="SW-133214_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-4914 1439,-4914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_221ddd0">
     <polyline DF8003:Layer="0" fill="none" points="1415,-4914 1074,-4914 1074,-5017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3cc9c30@1" ObjectIDZND0="24251@x" ObjectIDZND1="24253@x" ObjectIDZND2="g_3cc1f80@0" Pin0InfoVect0LinkObjId="SW-132302_0" Pin0InfoVect1LinkObjId="SW-132304_0" Pin0InfoVect2LinkObjId="g_3cc1f80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cc9c30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1415,-4914 1074,-4914 1074,-5017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_221e010">
     <polyline DF8003:Layer="0" fill="none" points="1074,-5175 1074,-5189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24250@1" ObjectIDZND0="24325@0" Pin0InfoVect0LinkObjId="g_221e820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132301_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-5175 1074,-5189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_221e820">
     <polyline DF8003:Layer="0" fill="none" points="1226,-5176 1226,-5189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24243@1" ObjectIDZND0="24325@0" Pin0InfoVect0LinkObjId="g_221e010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132273_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-5176 1226,-5189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_289aa40">
     <polyline DF8003:Layer="0" fill="none" points="1074,-5017 1098,-5017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="24251@x" ObjectIDND1="24253@x" ObjectIDND2="g_3cc9c30@0" ObjectIDZND0="g_3cc1f80@0" ObjectIDZND1="24252@x" Pin0InfoVect0LinkObjId="g_3cc1f80_0" Pin0InfoVect1LinkObjId="SW-132303_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132302_0" Pin1InfoVect1LinkObjId="SW-132304_0" Pin1InfoVect2LinkObjId="g_3cc9c30_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-5017 1098,-5017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_289aca0">
     <polyline DF8003:Layer="0" fill="none" points="1098,-5017 1108,-5017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="24251@x" ObjectIDND1="24253@x" ObjectIDND2="g_3cc9c30@0" ObjectIDZND0="g_3cc1f80@0" Pin0InfoVect0LinkObjId="g_3cc1f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132302_0" Pin1InfoVect1LinkObjId="SW-132304_0" Pin1InfoVect2LinkObjId="g_3cc9c30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1098,-5017 1108,-5017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_289af00">
     <polyline DF8003:Layer="0" fill="none" points="1098,-4968 1098,-4985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_221ff50@0" ObjectIDZND0="24252@0" Pin0InfoVect0LinkObjId="SW-132303_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_221ff50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1098,-4968 1098,-4985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_289b160">
     <polyline DF8003:Layer="0" fill="none" points="1098,-5012 1098,-5017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="24252@1" ObjectIDZND0="24251@x" ObjectIDZND1="24253@x" ObjectIDZND2="g_3cc9c30@0" Pin0InfoVect0LinkObjId="SW-132302_0" Pin0InfoVect1LinkObjId="SW-132304_0" Pin0InfoVect2LinkObjId="g_3cc9c30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132303_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1098,-5012 1098,-5017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc64c0">
     <polyline DF8003:Layer="0" fill="none" points="1247,-4972 1247,-4989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_289b3c0@0" ObjectIDZND0="24245@0" Pin0InfoVect0LinkObjId="SW-132277_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_289b3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-4972 1247,-4989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc6720">
     <polyline DF8003:Layer="0" fill="none" points="1247,-5016 1247,-5022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="24245@1" ObjectIDZND0="24248@x" ObjectIDZND1="24244@x" ObjectIDZND2="12195@x" Pin0InfoVect0LinkObjId="SW-132280_0" Pin0InfoVect1LinkObjId="SW-132275_0" Pin0InfoVect2LinkObjId="SW-64573_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-132277_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-5016 1247,-5022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc7210">
     <polyline DF8003:Layer="0" fill="none" points="1226,-5022 1247,-5022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24248@x" ObjectIDND1="24244@x" ObjectIDND2="12195@x" ObjectIDZND0="24245@x" ObjectIDZND1="g_28b4120@0" Pin0InfoVect0LinkObjId="SW-132277_0" Pin0InfoVect1LinkObjId="g_28b4120_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132280_0" Pin1InfoVect1LinkObjId="SW-132275_0" Pin1InfoVect2LinkObjId="SW-64573_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-5022 1247,-5022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc7470">
     <polyline DF8003:Layer="0" fill="none" points="1247,-5022 1254,-5022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="24245@x" ObjectIDND1="24248@x" ObjectIDND2="24244@x" ObjectIDZND0="g_28b4120@0" Pin0InfoVect0LinkObjId="g_28b4120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132277_0" Pin1InfoVect1LinkObjId="SW-132280_0" Pin1InfoVect2LinkObjId="SW-132275_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-5022 1254,-5022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cb4db0">
     <polyline DF8003:Layer="0" fill="none" points="1378,-4750 1371,-4750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="12195@0" ObjectIDZND0="g_3cb4760@1" Pin0InfoVect0LinkObjId="g_3cb4760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-64573_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1378,-4750 1371,-4750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cb5010">
     <polyline DF8003:Layer="0" fill="none" points="1340,-4750 1328,-4750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3cb4760@0" ObjectIDZND0="g_3d74bc0@0" Pin0InfoVect0LinkObjId="g_3d74bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cb4760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1340,-4750 1328,-4750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d749d0">
     <polyline DF8003:Layer="0" fill="none" points="1415,-4750 1427,-4750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="12195@1" ObjectIDZND0="4185@x" ObjectIDZND1="24248@x" ObjectIDZND2="24244@x" Pin0InfoVect0LinkObjId="SW-26024_0" Pin0InfoVect1LinkObjId="SW-132280_0" Pin0InfoVect2LinkObjId="SW-132275_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-64573_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1415,-4750 1427,-4750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d75910">
     <polyline DF8003:Layer="0" fill="none" points="1427,-4740 1427,-4750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4185@1" ObjectIDZND0="12195@x" ObjectIDZND1="24248@x" ObjectIDZND2="24244@x" Pin0InfoVect0LinkObjId="SW-64573_0" Pin0InfoVect1LinkObjId="SW-132280_0" Pin0InfoVect2LinkObjId="SW-132275_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26024_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-4740 1427,-4750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d75b70">
     <polyline DF8003:Layer="0" fill="none" points="1605,-4745 1605,-4758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="4095@1" ObjectIDZND0="19704@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3d15310@0" Pin0InfoVect0LinkObjId="SW-93023_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3d15310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-4745 1605,-4758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d75dd0">
     <polyline DF8003:Layer="0" fill="none" points="1605,-5018 1624,-5018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_38a06b0@0" ObjectIDND1="g_3ca45d0@0" ObjectIDND2="g_3cc9c30@0" ObjectIDZND0="24371@0" Pin0InfoVect0LinkObjId="SW-133216_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38a06b0_0" Pin1InfoVect1LinkObjId="g_3ca45d0_0" Pin1InfoVect2LinkObjId="g_3cc9c30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-5018 1624,-5018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d76030">
     <polyline DF8003:Layer="0" fill="none" points="1660,-5018 1669,-5018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24371@1" ObjectIDZND0="g_3cc76d0@0" Pin0InfoVect0LinkObjId="g_3cc76d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133216_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1660,-5018 1669,-5018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca3d40">
     <polyline DF8003:Layer="0" fill="none" points="1427,-4986 1411,-4986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_22f07c0@0" ObjectIDND1="24365@x" ObjectIDND2="24364@x" ObjectIDZND0="g_3ca2e30@0" Pin0InfoVect0LinkObjId="g_3ca2e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22f07c0_0" Pin1InfoVect1LinkObjId="SW-133194_0" Pin1InfoVect2LinkObjId="SW-133193_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-4986 1411,-4986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca5c40">
     <polyline DF8003:Layer="0" fill="none" points="1427,-5000 1427,-4986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_22f07c0@0" ObjectIDND1="24365@x" ObjectIDND2="24364@x" ObjectIDZND0="g_3ca2e30@0" ObjectIDZND1="24248@x" ObjectIDZND2="24244@x" Pin0InfoVect0LinkObjId="g_3ca2e30_0" Pin0InfoVect1LinkObjId="SW-132280_0" Pin0InfoVect2LinkObjId="SW-132275_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22f07c0_0" Pin1InfoVect1LinkObjId="SW-133194_0" Pin1InfoVect2LinkObjId="SW-133193_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-5000 1427,-4986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d93f50">
     <polyline DF8003:Layer="0" fill="none" points="1781,-4461 1781,-4249 2314,-4249 2314,-4312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="4089@0" ObjectIDZND0="g_2870980@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2870980_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1781,-4461 1781,-4249 2314,-4249 2314,-4312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d94a50">
     <polyline DF8003:Layer="0" fill="none" points="1106,-3813 1106,-3852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="24480@x" ObjectIDND1="24479@x" ObjectIDND2="g_46b7880@0" ObjectIDZND0="g_3e37870@0" ObjectIDZND1="2764@x" ObjectIDZND2="2763@x" Pin0InfoVect0LinkObjId="g_3e37870_0" Pin0InfoVect1LinkObjId="SW-18064_0" Pin0InfoVect2LinkObjId="SW-18063_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135210_0" Pin1InfoVect1LinkObjId="SW-135209_0" Pin1InfoVect2LinkObjId="g_46b7880_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1106,-3813 1106,-3852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d95580">
     <polyline DF8003:Layer="0" fill="none" points="755,-4383 755,-4569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_275a100@0" ObjectIDND1="24711@x" ObjectIDND2="g_36ebcc0@0" ObjectIDZND0="2848@x" ObjectIDZND1="2847@x" Pin0InfoVect0LinkObjId="SW-18859_0" Pin0InfoVect1LinkObjId="SW-18858_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_275a100_0" Pin1InfoVect1LinkObjId="SW-136728_0" Pin1InfoVect2LinkObjId="g_36ebcc0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="755,-4383 755,-4569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d957e0">
     <polyline DF8003:Layer="0" fill="none" points="755,-4307 755,-4383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_275a100@0" ObjectIDND1="24711@x" ObjectIDZND0="g_36ebcc0@0" ObjectIDZND1="2848@x" ObjectIDZND2="2847@x" Pin0InfoVect0LinkObjId="g_36ebcc0_0" Pin0InfoVect1LinkObjId="SW-18859_0" Pin0InfoVect2LinkObjId="SW-18858_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_275a100_0" Pin1InfoVect1LinkObjId="SW-136728_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="755,-4307 755,-4383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d8bd50">
     <polyline DF8003:Layer="0" fill="none" points="873,-4772 873,-4791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20868@1" ObjectIDZND0="2745@0" Pin0InfoVect0LinkObjId="g_3e9a310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106840_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,-4772 873,-4791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cd8230">
     <polyline DF8003:Layer="0" fill="none" points="873,-4718 873,-4736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20867@1" ObjectIDZND0="20868@0" Pin0InfoVect0LinkObjId="SW-106840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106839_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,-4718 873,-4736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cdac50">
     <polyline DF8003:Layer="0" fill="none" points="873,-4673 873,-4691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20869@1" ObjectIDZND0="20867@0" Pin0InfoVect0LinkObjId="SW-106839_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106841_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,-4673 873,-4691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d100d0">
     <polyline DF8003:Layer="0" fill="none" points="923,-4574 932,-4574 932,-4542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20870@1" ObjectIDZND0="9691@0" Pin0InfoVect0LinkObjId="g_47a82a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106842_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-4574 932,-4574 932,-4542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d09ed0">
     <polyline DF8003:Layer="0" fill="none" points="871,-4400 858,-4400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3d10970@0" ObjectIDND1="g_3cd1710@0" ObjectIDND2="20869@x" ObjectIDZND0="g_3d0a130@0" Pin0InfoVect0LinkObjId="g_3d0a130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d10970_0" Pin1InfoVect1LinkObjId="g_3cd1710_0" Pin1InfoVect2LinkObjId="SW-106841_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="871,-4400 858,-4400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d0bc70">
     <polyline DF8003:Layer="0" fill="none" points="873,-4575 873,-4400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20869@x" ObjectIDND1="20870@x" ObjectIDZND0="g_3d0a130@0" ObjectIDZND1="g_3d10970@0" ObjectIDZND2="g_3cd1710@0" Pin0InfoVect0LinkObjId="g_3d0a130_0" Pin0InfoVect1LinkObjId="g_3d10970_0" Pin0InfoVect2LinkObjId="g_3cd1710_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106841_0" Pin1InfoVect1LinkObjId="SW-106842_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="873,-4575 873,-4400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d0bed0">
     <polyline DF8003:Layer="0" fill="none" points="892,-4386 892,-4400 873,-4400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3d10970@0" ObjectIDZND0="g_3d0a130@0" ObjectIDZND1="g_3cd1710@0" ObjectIDZND2="20869@x" Pin0InfoVect0LinkObjId="g_3d0a130_0" Pin0InfoVect1LinkObjId="g_3cd1710_0" Pin0InfoVect2LinkObjId="SW-106841_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d10970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="892,-4386 892,-4400 873,-4400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1bfd0">
     <polyline DF8003:Layer="0" fill="none" points="873,-4320 860,-4320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3d1d1a0@0" ObjectIDND1="g_3cd1710@0" ObjectIDND2="24765@x" ObjectIDZND0="g_3d0c130@0" Pin0InfoVect0LinkObjId="g_3d0c130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d1d1a0_0" Pin1InfoVect1LinkObjId="g_3cd1710_0" Pin1InfoVect2LinkObjId="SW-137074_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,-4320 860,-4320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1cce0">
     <polyline DF8003:Layer="0" fill="none" points="873,-4400 873,-4387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3d0a130@0" ObjectIDND1="g_3d10970@0" ObjectIDND2="20869@x" ObjectIDZND0="g_3cd1710@0" Pin0InfoVect0LinkObjId="g_3cd1710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d0a130_0" Pin1InfoVect1LinkObjId="g_3d10970_0" Pin1InfoVect2LinkObjId="SW-106841_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,-4400 873,-4387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d1cf40">
     <polyline DF8003:Layer="0" fill="none" points="894,-4306 894,-4320 875,-4320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3d1d1a0@0" ObjectIDZND0="g_3d0c130@0" ObjectIDZND1="g_3cd1710@0" ObjectIDZND2="24765@x" Pin0InfoVect0LinkObjId="g_3d0c130_0" Pin0InfoVect1LinkObjId="g_3cd1710_0" Pin0InfoVect2LinkObjId="SW-137074_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d1d1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="894,-4306 894,-4320 875,-4320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d78430">
     <polyline DF8003:Layer="0" fill="none" points="872,-4244 872,-4224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24763@1" ObjectIDZND0="24762@1" Pin0InfoVect0LinkObjId="SW-137072_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-137073_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="872,-4244 872,-4224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d78690">
     <polyline DF8003:Layer="0" fill="none" points="872,-4197 872,-4169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24762@0" ObjectIDZND0="24764@1" Pin0InfoVect0LinkObjId="SW-137073_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-137072_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="872,-4197 872,-4169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cd2130">
     <polyline DF8003:Layer="0" fill="none" points="873,-4334 873,-4320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3cd1710@1" ObjectIDZND0="g_3d0c130@0" ObjectIDZND1="g_3d1d1a0@0" ObjectIDZND2="24765@x" Pin0InfoVect0LinkObjId="g_3d0c130_0" Pin0InfoVect1LinkObjId="g_3d1d1a0_0" Pin0InfoVect2LinkObjId="SW-137074_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cd1710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="873,-4334 873,-4320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3cd2390">
     <polyline DF8003:Layer="0" fill="none" points="832,-4260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="832,-4260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cd2e80">
     <polyline DF8003:Layer="0" fill="none" points="873,-4320 873,-4305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3d0c130@0" ObjectIDND1="g_3d1d1a0@0" ObjectIDND2="g_3cd1710@0" ObjectIDZND0="24765@x" ObjectIDZND1="24763@x" Pin0InfoVect0LinkObjId="SW-137074_0" Pin0InfoVect1LinkObjId="SW-137073_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d0c130_0" Pin1InfoVect1LinkObjId="g_3d1d1a0_0" Pin1InfoVect2LinkObjId="g_3cd1710_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="873,-4320 873,-4305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cd30e0">
     <polyline DF8003:Layer="0" fill="none" points="873,-4305 873,-4264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3d0c130@0" ObjectIDND1="g_3d1d1a0@0" ObjectIDND2="g_3cd1710@0" ObjectIDZND0="24763@0" Pin0InfoVect0LinkObjId="SW-137073_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d0c130_0" Pin1InfoVect1LinkObjId="g_3d1d1a0_0" Pin1InfoVect2LinkObjId="g_3cd1710_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,-4305 873,-4264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d66760">
     <polyline DF8003:Layer="0" fill="none" points="846,-4290 846,-4305 873,-4305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="24765@1" ObjectIDZND0="g_3d0c130@0" ObjectIDZND1="g_3d1d1a0@0" ObjectIDZND2="g_3cd1710@0" Pin0InfoVect0LinkObjId="g_3d0c130_0" Pin0InfoVect1LinkObjId="g_3d1d1a0_0" Pin0InfoVect2LinkObjId="g_3cd1710_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-137074_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="846,-4290 846,-4305 873,-4305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d67450">
     <polyline DF8003:Layer="0" fill="none" points="846,-4254 846,-4240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24765@0" ObjectIDZND0="g_3d669c0@0" Pin0InfoVect0LinkObjId="g_3d669c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-137074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="846,-4254 846,-4240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d676b0">
     <polyline DF8003:Layer="0" fill="none" points="873,-4151 873,-4133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24764@0" ObjectIDZND0="24789@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-137073_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,-4151 873,-4133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2896ec0">
     <polyline DF8003:Layer="0" fill="none" points="1557,-4501 1557,-4515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="24630@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136208_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1557,-4501 1557,-4515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2899670">
     <polyline DF8003:Layer="0" fill="none" points="1371,-4127 1371,-4165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="24567@x" ObjectIDND1="24564@x" ObjectIDND2="24562@x" ObjectIDZND0="g_3b53e90@0" Pin0InfoVect0LinkObjId="g_3b53e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135733_0" Pin1InfoVect1LinkObjId="SW-135724_0" Pin1InfoVect2LinkObjId="SW-135722_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-4127 1371,-4165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b539d0">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4127 1371,-4127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="24567@x" ObjectIDND1="24564@x" ObjectIDND2="24562@x" ObjectIDZND0="g_3b53e90@0" ObjectIDZND1="g_3b556d0@0" ObjectIDZND2="g_3b56400@0" Pin0InfoVect0LinkObjId="g_3b53e90_0" Pin0InfoVect1LinkObjId="g_3b556d0_0" Pin0InfoVect2LinkObjId="g_3b56400_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135733_0" Pin1InfoVect1LinkObjId="SW-135724_0" Pin1InfoVect2LinkObjId="SW-135722_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4127 1371,-4127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b53c30">
     <polyline DF8003:Layer="0" fill="none" points="1371,-4127 1286,-4127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="24567@x" ObjectIDND1="24564@x" ObjectIDND2="24562@x" ObjectIDZND0="g_3b556d0@0" ObjectIDZND1="g_3b56400@0" ObjectIDZND2="28766@x" Pin0InfoVect0LinkObjId="g_3b556d0_0" Pin0InfoVect1LinkObjId="g_3b56400_0" Pin0InfoVect2LinkObjId="SW-188720_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135733_0" Pin1InfoVect1LinkObjId="SW-135724_0" Pin1InfoVect2LinkObjId="SW-135722_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1371,-4127 1286,-4127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b55470">
     <polyline DF8003:Layer="0" fill="none" points="1286,-4127 1286,-4186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="24567@x" ObjectIDND1="24564@x" ObjectIDND2="24562@x" ObjectIDZND0="g_3b556d0@0" ObjectIDZND1="g_3b56400@0" Pin0InfoVect0LinkObjId="g_3b556d0_0" Pin0InfoVect1LinkObjId="g_3b56400_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135733_0" Pin1InfoVect1LinkObjId="SW-135724_0" Pin1InfoVect2LinkObjId="SW-135722_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-4127 1286,-4186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4300bb0">
     <polyline DF8003:Layer="0" fill="none" points="1257,-4186 1286,-4186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3b556d0@0" ObjectIDZND0="24567@x" ObjectIDZND1="24564@x" ObjectIDZND2="24562@x" Pin0InfoVect0LinkObjId="SW-135733_0" Pin0InfoVect1LinkObjId="SW-135724_0" Pin0InfoVect2LinkObjId="SW-135722_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b556d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1257,-4186 1286,-4186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4300e10">
     <polyline DF8003:Layer="0" fill="none" points="1286,-4186 1298,-4186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="24567@x" ObjectIDND1="24564@x" ObjectIDND2="24562@x" ObjectIDZND0="g_3b56400@0" Pin0InfoVect0LinkObjId="g_3b56400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135733_0" Pin1InfoVect1LinkObjId="SW-135724_0" Pin1InfoVect2LinkObjId="SW-135722_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-4186 1298,-4186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3731500">
     <polyline DF8003:Layer="0" fill="none" points="1286,-4127 1286,-4074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24567@x" ObjectIDND1="24564@x" ObjectIDND2="24562@x" ObjectIDZND0="28766@x" ObjectIDZND1="28764@x" Pin0InfoVect0LinkObjId="SW-188720_0" Pin0InfoVect1LinkObjId="SW-188718_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135733_0" Pin1InfoVect1LinkObjId="SW-135724_0" Pin1InfoVect2LinkObjId="SW-135722_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-4127 1286,-4074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3733c30">
     <polyline DF8003:Layer="0" fill="none" points="1286,-4074 1277,-4074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24567@x" ObjectIDND1="24564@x" ObjectIDND2="24562@x" ObjectIDZND0="28766@1" Pin0InfoVect0LinkObjId="SW-188720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135733_0" Pin1InfoVect1LinkObjId="SW-135724_0" Pin1InfoVect2LinkObjId="SW-135722_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-4074 1277,-4074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3733e90">
     <polyline DF8003:Layer="0" fill="none" points="1241,-4074 1231,-4074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28766@0" ObjectIDZND0="g_37340f0@0" Pin0InfoVect0LinkObjId="g_37340f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-4074 1231,-4074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2758340">
     <polyline DF8003:Layer="0" fill="none" points="1286,-4074 1286,-4054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24567@x" ObjectIDND1="24564@x" ObjectIDND2="24562@x" ObjectIDZND0="28764@1" Pin0InfoVect0LinkObjId="SW-188718_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-135733_0" Pin1InfoVect1LinkObjId="SW-135724_0" Pin1InfoVect2LinkObjId="SW-135722_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-4074 1286,-4054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c9df30">
     <polyline DF8003:Layer="0" fill="none" points="1286,-4018 1286,-3997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28764@0" ObjectIDZND0="28763@1" Pin0InfoVect0LinkObjId="SW-188717_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188718_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-4018 1286,-3997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca0990">
     <polyline DF8003:Layer="0" fill="none" points="1286,-3971 1286,-3959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="28765@1" Pin0InfoVect0LinkObjId="SW-188719_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-3971 1286,-3959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ca0bf0">
     <polyline DF8003:Layer="0" fill="none" points="1286,-3923 1286,-3910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28765@0" ObjectIDZND0="28762@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188719_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1286,-3923 1286,-3910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_287fd40">
     <polyline DF8003:Layer="0" fill="none" points="1389,-3900 1389,-3910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="24563@x" Pin0InfoVect0LinkObjId="SW-135723_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-3900 1389,-3910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d91e60">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4014 1389,-3998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24562@0" ObjectIDZND0="24561@1" Pin0InfoVect0LinkObjId="SW-135720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135722_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4014 1389,-3998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3caa810">
     <polyline DF8003:Layer="0" fill="none" points="1389,-3971 1389,-3959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24561@0" ObjectIDZND0="24563@1" Pin0InfoVect0LinkObjId="SW-135723_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-3971 1389,-3959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3caaa70">
     <polyline DF8003:Layer="0" fill="none" points="1389,-3923 1389,-3912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="24563@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135723_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-3923 1389,-3912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cab940">
     <polyline DF8003:Layer="0" fill="none" points="1557,-4451 1557,-4465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" ObjectIDND0="24629@x" ObjectIDND1="24626@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136207_0" Pin1InfoVect1LinkObjId="SW-136203_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1557,-4451 1557,-4465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c8a070">
     <polyline DF8003:Layer="0" fill="none" points="1505,-4451 1512,-4451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="24631@x" ObjectIDND1="g_452e630@0" ObjectIDND2="4106@x" ObjectIDZND0="24629@0" Pin0InfoVect0LinkObjId="SW-136207_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-136209_0" Pin1InfoVect1LinkObjId="g_452e630_0" Pin1InfoVect2LinkObjId="SW-25714_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-4451 1512,-4451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c8a2d0">
     <polyline DF8003:Layer="0" fill="none" points="1548,-4451 1557,-4451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24629@1" ObjectIDZND0="24626@x" Pin0InfoVect0LinkObjId="SW-136203_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136207_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1548,-4451 1557,-4451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c8cb50">
     <polyline DF8003:Layer="0" fill="none" points="1557,-4451 1571,-4451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24629@x" ObjectIDZND0="24626@1" Pin0InfoVect0LinkObjId="SW-136203_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136207_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1557,-4451 1571,-4451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cb9960">
     <polyline DF8003:Layer="0" fill="none" points="1657,-4451 1670,-4451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24627@1" ObjectIDZND0="24622@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136205_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1657,-4451 1670,-4451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b72d50">
     <polyline DF8003:Layer="0" fill="none" points="1611,-4500 1611,-4512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24628@1" ObjectIDZND0="g_3b72fb0@0" Pin0InfoVect0LinkObjId="g_3b72fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136206_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1611,-4500 1611,-4512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3da1810">
     <polyline DF8003:Layer="0" fill="none" points="1389,-3864 1389,-3842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-3864 1389,-3842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3da1a20">
     <polyline DF8003:Layer="0" fill="none" points="1389,-3842 1389,-3830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-3842 1389,-3830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3da1c50">
     <polyline DF8003:Layer="0" fill="none" points="1442,-3843 1465,-3843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-3843 1465,-3843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45d9830">
     <polyline DF8003:Layer="0" fill="none" points="2314,-4368 2314,-4380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2870980@0" ObjectIDND1="4089@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2870980_0" Pin1InfoVect1LinkObjId="SW-25666_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2314,-4368 2314,-4380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_45d9a90">
     <polyline DF8003:Layer="0" fill="none" points="2314,-4416 2314,-4429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2314,-4416 2314,-4429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45da960">
     <polyline DF8003:Layer="0" fill="none" points="1470,-4101 1494,-4101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="24567@1" ObjectIDZND0="24568@x" ObjectIDZND1="g_2758f10@0" Pin0InfoVect0LinkObjId="SW-135734_0" Pin0InfoVect1LinkObjId="g_2758f10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-135733_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1470,-4101 1494,-4101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45dab50">
     <polyline DF8003:Layer="0" fill="none" points="1494,-4101 1515,-4101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="24568@x" ObjectIDND1="24567@x" ObjectIDZND0="g_2758f10@0" Pin0InfoVect0LinkObjId="g_2758f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-135734_0" Pin1InfoVect1LinkObjId="SW-135733_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-4101 1515,-4101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45dad40">
     <polyline DF8003:Layer="0" fill="none" points="1591,-4451 1621,-4451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="24627@0" Pin0InfoVect0LinkObjId="SW-136205_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1591,-4451 1621,-4451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_45db9d0">
     <polyline DF8003:Layer="0" fill="none" points="1406,-3843 1389,-3843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,-3843 1389,-3843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_45dbc30">
     <polyline DF8003:Layer="0" fill="none" points="1389,-3843 1389,-3844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-3843 1389,-3844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45dbe90">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4101 1389,-4065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3b53e90@0" ObjectIDND1="g_3b556d0@0" ObjectIDND2="g_3b56400@0" ObjectIDZND0="24564@x" ObjectIDZND1="24562@x" Pin0InfoVect0LinkObjId="SW-135724_0" Pin0InfoVect1LinkObjId="SW-135722_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b53e90_0" Pin1InfoVect1LinkObjId="g_3b556d0_0" Pin1InfoVect2LinkObjId="g_3b56400_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4101 1389,-4065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d18f20">
     <polyline DF8003:Layer="0" fill="none" points="1605,-4758 1605,-4772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="19704@x" ObjectIDND1="4095@x" ObjectIDZND0="0@x" ObjectIDZND1="g_3d15310@0" ObjectIDZND2="g_3cc9c30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3d15310_0" Pin0InfoVect2LinkObjId="g_3cc9c30_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-93023_0" Pin1InfoVect1LinkObjId="SW-25681_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-4758 1605,-4772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d19180">
     <polyline DF8003:Layer="0" fill="none" points="1735,-4786 1735,-4772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3d15310@0" ObjectIDZND0="0@x" ObjectIDZND1="19704@x" ObjectIDZND2="4095@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-93023_0" Pin0InfoVect2LinkObjId="SW-25681_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d15310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1735,-4786 1735,-4772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d193e0">
     <polyline DF8003:Layer="0" fill="none" points="1750,-4772 1735,-4772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_3d15310@0" ObjectIDZND1="19704@x" ObjectIDZND2="4095@x" Pin0InfoVect0LinkObjId="g_3d15310_0" Pin0InfoVect1LinkObjId="SW-93023_0" Pin0InfoVect2LinkObjId="SW-25681_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1750,-4772 1735,-4772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d19640">
     <polyline DF8003:Layer="0" fill="none" points="1736,-4772 1605,-4772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_3d15310@0" ObjectIDZND0="19704@x" ObjectIDZND1="4095@x" ObjectIDZND2="g_3cc9c30@0" Pin0InfoVect0LinkObjId="SW-93023_0" Pin0InfoVect1LinkObjId="SW-25681_0" Pin0InfoVect2LinkObjId="g_3cc9c30_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3d15310_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1736,-4772 1605,-4772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_48d0020">
     <polyline DF8003:Layer="0" fill="none" points="1802,-4772 1823,-4772 1823,-4746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1802,-4772 1823,-4772 1823,-4746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_48d28d0">
     <polyline DF8003:Layer="0" fill="none" points="221,-4013 221,-3990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="34067@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4013 221,-3990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_48d5110">
     <polyline DF8003:Layer="0" fill="none" points="221,-4059 221,-4040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34069@0" ObjectIDZND0="34067@1" Pin0InfoVect0LinkObjId="SW-218950_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218948_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4059 221,-4040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_48d5370">
     <polyline DF8003:Layer="0" fill="none" points="221,-4117 221,-4095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34066@x" ObjectIDND1="2755@x" ObjectIDND2="2754@x" ObjectIDZND0="34069@1" Pin0InfoVect0LinkObjId="SW-218948_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-134324_0" Pin1InfoVect1LinkObjId="SW-17975_0" Pin1InfoVect2LinkObjId="SW-17974_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4117 221,-4095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cafc60">
     <polyline DF8003:Layer="0" fill="none" points="221,-4130 238,-4130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34069@x" ObjectIDND1="2755@x" ObjectIDND2="2754@x" ObjectIDZND0="34066@0" Pin0InfoVect0LinkObjId="SW-134324_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-218948_0" Pin1InfoVect1LinkObjId="SW-17975_0" Pin1InfoVect2LinkObjId="SW-17974_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4130 238,-4130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cafec0">
     <polyline DF8003:Layer="0" fill="none" points="274,-4130 290,-4130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34066@1" ObjectIDZND0="g_3cb0120@0" Pin0InfoVect0LinkObjId="g_3cb0120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134324_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="274,-4130 290,-4130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cb38f0">
     <polyline DF8003:Layer="0" fill="none" points="251,-3942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="24419@0" ObjectIDZND0="24419@0" Pin0InfoVect0LinkObjId="g_3c911f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c911f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cea4c0">
     <polyline DF8003:Layer="0" fill="none" points="251,-3884 251,-3872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34073@0" ObjectIDZND0="34072@1" Pin0InfoVect0LinkObjId="SW-218993_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218994_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3884 251,-3872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cecd20">
     <polyline DF8003:Layer="0" fill="none" points="251,-3845 251,-3833 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="34072@0" ObjectIDZND0="34074@1" Pin0InfoVect0LinkObjId="SW-218995_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218993_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3845 251,-3833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd6c20">
     <polyline DF8003:Layer="0" fill="none" points="251,-3588 251,-3579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34428@1" ObjectIDZND0="34422@1" Pin0InfoVect0LinkObjId="SW-220421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-220422_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3588 251,-3579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd6e80">
     <polyline DF8003:Layer="0" fill="none" points="251,-3552 251,-3543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="34422@0" ObjectIDZND0="34423@1" Pin0InfoVect0LinkObjId="SW-220422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-220421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3552 251,-3543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd70e0">
     <polyline DF8003:Layer="0" fill="none" points="251,-3526 251,-3513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34423@0" ObjectIDZND0="7274@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-220422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3526 251,-3513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd7560">
     <polyline DF8003:Layer="0" fill="none" points="251,-3920 251,-3942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34073@1" ObjectIDZND0="24419@0" Pin0InfoVect0LinkObjId="g_3c911f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218994_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3920 251,-3942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd8270">
     <polyline DF8003:Layer="0" fill="none" points="251,-3784 272,-3784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34074@x" ObjectIDND1="g_375e150@0" ObjectIDND2="34424@x" ObjectIDZND0="34071@0" Pin0InfoVect0LinkObjId="SW-218997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-218995_0" Pin1InfoVect1LinkObjId="g_375e150_0" Pin1InfoVect2LinkObjId="SW-220423_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3784 272,-3784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4302790">
     <polyline DF8003:Layer="0" fill="none" points="251,-3797 251,-3784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="34074@0" ObjectIDZND0="34071@x" ObjectIDZND1="g_375e150@0" ObjectIDZND2="34424@x" Pin0InfoVect0LinkObjId="SW-218997_0" Pin0InfoVect1LinkObjId="g_375e150_0" Pin0InfoVect2LinkObjId="SW-220423_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3797 251,-3784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4304c70">
     <polyline DF8003:Layer="0" fill="none" points="308,-3784 318,-3784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34071@1" ObjectIDZND0="g_4304ed0@0" Pin0InfoVect0LinkObjId="g_4304ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="308,-3784 318,-3784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4305e50">
     <polyline DF8003:Layer="0" fill="none" points="251,-3629 271,-3629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34428@x" ObjectIDND1="g_375e150@0" ObjectIDND2="34071@x" ObjectIDZND0="34424@0" Pin0InfoVect0LinkObjId="SW-220423_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-220422_0" Pin1InfoVect1LinkObjId="g_375e150_0" Pin1InfoVect2LinkObjId="SW-218997_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3629 271,-3629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43067c0">
     <polyline DF8003:Layer="0" fill="none" points="251,-3629 251,-3605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34424@x" ObjectIDND1="g_375e150@0" ObjectIDND2="34071@x" ObjectIDZND0="34428@0" Pin0InfoVect0LinkObjId="SW-220422_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-220423_0" Pin1InfoVect1LinkObjId="g_375e150_0" Pin1InfoVect2LinkObjId="SW-218997_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3629 251,-3605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4308e20">
     <polyline DF8003:Layer="0" fill="none" points="307,-3629 318,-3629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34424@1" ObjectIDZND0="g_4309080@0" Pin0InfoVect0LinkObjId="g_4309080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-220423_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="307,-3629 318,-3629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b48200">
     <polyline DF8003:Layer="0" fill="none" points="175,-4079 175,-4116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="34070@0" Pin0InfoVect0LinkObjId="SW-218949_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="175,-4079 175,-4116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b48c00">
     <polyline DF8003:Layer="0" fill="none" points="221,-4573 221,-4167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="2755@x" ObjectIDND1="2754@x" ObjectIDZND0="34066@x" ObjectIDZND1="34069@x" ObjectIDZND2="34070@x" Pin0InfoVect0LinkObjId="SW-134324_0" Pin0InfoVect1LinkObjId="SW-218948_0" Pin0InfoVect2LinkObjId="SW-218949_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-17975_0" Pin1InfoVect1LinkObjId="SW-17974_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4573 221,-4167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b4a3b0">
     <polyline DF8003:Layer="0" fill="none" points="301,-4078 301,-4109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="301,-4078 301,-4109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3759860">
     <polyline DF8003:Layer="0" fill="none" points="301,-4026 301,-3990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3b49000@0" ObjectIDZND0="34055@1" Pin0InfoVect0LinkObjId="SW-134407_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b49000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="301,-4026 301,-3990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3759ac0">
     <polyline DF8003:Layer="0" fill="none" points="301,-3954 301,-3942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34055@0" ObjectIDZND0="24419@0" Pin0InfoVect0LinkObjId="g_3c911f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134407_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="301,-3954 301,-3942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_375def0">
     <polyline DF8003:Layer="0" fill="none" points="191,-3822 191,-3854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="34075@1" ObjectIDZND0="g_375af40@0" Pin0InfoVect0LinkObjId="g_375af40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="191,-3822 191,-3854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_375e850">
     <polyline DF8003:Layer="0" fill="none" points="187,-3640 251,-3640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_375e150@0" ObjectIDZND0="34424@x" ObjectIDZND1="34428@x" ObjectIDZND2="34071@x" Pin0InfoVect0LinkObjId="SW-220423_0" Pin0InfoVect1LinkObjId="SW-220422_0" Pin0InfoVect2LinkObjId="SW-218997_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_375e150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="187,-3640 251,-3640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cef6c0">
     <polyline DF8003:Layer="0" fill="none" points="251,-3776 251,-3640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34071@x" ObjectIDND1="34074@x" ObjectIDND2="34075@x" ObjectIDZND0="g_375e150@0" ObjectIDZND1="34424@x" ObjectIDZND2="34428@x" Pin0InfoVect0LinkObjId="g_375e150_0" Pin0InfoVect1LinkObjId="SW-220423_0" Pin0InfoVect2LinkObjId="SW-220422_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-218997_0" Pin1InfoVect1LinkObjId="SW-218995_0" Pin1InfoVect2LinkObjId="SW-218996_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3776 251,-3640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cef920">
     <polyline DF8003:Layer="0" fill="none" points="251,-3640 251,-3628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_375e150@0" ObjectIDND1="34071@x" ObjectIDND2="34074@x" ObjectIDZND0="34424@x" ObjectIDZND1="34428@x" Pin0InfoVect0LinkObjId="SW-220423_0" Pin0InfoVect1LinkObjId="SW-220422_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_375e150_0" Pin1InfoVect1LinkObjId="SW-218997_0" Pin1InfoVect2LinkObjId="SW-218995_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3640 251,-3628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf0410">
     <polyline DF8003:Layer="0" fill="none" points="221,-4117 221,-4086 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="34069@x" ObjectIDND1="34066@x" ObjectIDND2="2755@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-218948_0" Pin1InfoVect1LinkObjId="SW-134324_0" Pin1InfoVect2LinkObjId="SW-17975_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4117 221,-4086 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf0f00">
     <polyline DF8003:Layer="0" fill="none" points="221,-4130 221,-4117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="34066@x" ObjectIDND1="2755@x" ObjectIDND2="2754@x" ObjectIDZND0="34069@x" Pin0InfoVect0LinkObjId="SW-218948_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-134324_0" Pin1InfoVect1LinkObjId="SW-17975_0" Pin1InfoVect2LinkObjId="SW-17974_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4130 221,-4117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3cf1160">
     <polyline DF8003:Layer="0" fill="none" points="2067,-4780 2067,-4343 2176,-4343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_3c60160@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3c60160_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2067,-4780 2067,-4343 2176,-4343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf1c50">
     <polyline DF8003:Layer="0" fill="none" points="1589,-4988 1605,-4988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3ca45d0@0" ObjectIDZND0="g_38a06b0@0" ObjectIDZND1="24369@x" ObjectIDZND2="24371@x" Pin0InfoVect0LinkObjId="g_38a06b0_0" Pin0InfoVect1LinkObjId="SW-133214_0" Pin0InfoVect2LinkObjId="SW-133216_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ca45d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-4988 1605,-4988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf1eb0">
     <polyline DF8003:Layer="0" fill="none" points="1605,-4988 1605,-5001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3ca45d0@0" ObjectIDND1="g_3cc9c30@0" ObjectIDND2="19704@x" ObjectIDZND0="g_38a06b0@0" ObjectIDZND1="24369@x" ObjectIDZND2="24371@x" Pin0InfoVect0LinkObjId="g_38a06b0_0" Pin0InfoVect1LinkObjId="SW-133214_0" Pin0InfoVect2LinkObjId="SW-133216_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ca45d0_0" Pin1InfoVect1LinkObjId="g_3cc9c30_0" Pin1InfoVect2LinkObjId="SW-93023_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-4988 1605,-5001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf29a0">
     <polyline DF8003:Layer="0" fill="none" points="1605,-4988 1605,-4914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3ca45d0@0" ObjectIDND1="g_38a06b0@0" ObjectIDND2="24369@x" ObjectIDZND0="g_3cc9c30@0" ObjectIDZND1="19704@x" ObjectIDZND2="4095@x" Pin0InfoVect0LinkObjId="g_3cc9c30_0" Pin0InfoVect1LinkObjId="SW-93023_0" Pin0InfoVect2LinkObjId="SW-25681_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ca45d0_0" Pin1InfoVect1LinkObjId="g_38a06b0_0" Pin1InfoVect2LinkObjId="SW-133214_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-4988 1605,-4914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf2c00">
     <polyline DF8003:Layer="0" fill="none" points="1226,-5022 1226,-4946 1427,-4946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="24248@x" ObjectIDND1="24244@x" ObjectIDND2="24245@x" ObjectIDZND0="12195@x" ObjectIDZND1="4185@x" ObjectIDZND2="g_3ca2e30@0" Pin0InfoVect0LinkObjId="SW-64573_0" Pin0InfoVect1LinkObjId="SW-26024_0" Pin0InfoVect2LinkObjId="g_3ca2e30_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132280_0" Pin1InfoVect1LinkObjId="SW-132275_0" Pin1InfoVect2LinkObjId="SW-132277_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1226,-5022 1226,-4946 1427,-4946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf36f0">
     <polyline DF8003:Layer="0" fill="none" points="1427,-4750 1427,-4946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="12195@x" ObjectIDND1="4185@x" ObjectIDZND0="24248@x" ObjectIDZND1="24244@x" ObjectIDZND2="24245@x" Pin0InfoVect0LinkObjId="SW-132280_0" Pin0InfoVect1LinkObjId="SW-132275_0" Pin0InfoVect2LinkObjId="SW-132277_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-64573_0" Pin1InfoVect1LinkObjId="SW-26024_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-4750 1427,-4946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf3950">
     <polyline DF8003:Layer="0" fill="none" points="1427,-4946 1427,-4986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="24248@x" ObjectIDND1="24244@x" ObjectIDND2="24245@x" ObjectIDZND0="g_3ca2e30@0" ObjectIDZND1="g_22f07c0@0" ObjectIDZND2="24365@x" Pin0InfoVect0LinkObjId="g_3ca2e30_0" Pin0InfoVect1LinkObjId="g_22f07c0_0" Pin0InfoVect2LinkObjId="SW-133194_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-132280_0" Pin1InfoVect1LinkObjId="SW-132275_0" Pin1InfoVect2LinkObjId="SW-132277_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-4946 1427,-4986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf4440">
     <polyline DF8003:Layer="0" fill="none" points="485,-4574 470,-4574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2759@0" ObjectIDZND0="2758@x" ObjectIDZND1="g_3735c80@0" ObjectIDZND2="g_2784800@0" Pin0InfoVect0LinkObjId="SW-18004_0" Pin0InfoVect1LinkObjId="g_3735c80_0" Pin0InfoVect2LinkObjId="g_2784800_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18005_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="485,-4574 470,-4574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf46a0">
     <polyline DF8003:Layer="0" fill="none" points="470,-4574 470,-4636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="2759@x" ObjectIDND1="g_3735c80@0" ObjectIDND2="g_2784800@0" ObjectIDZND0="2758@0" Pin0InfoVect0LinkObjId="SW-18004_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18005_0" Pin1InfoVect1LinkObjId="g_3735c80_0" Pin1InfoVect2LinkObjId="g_2784800_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="470,-4574 470,-4636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf4900">
     <polyline DF8003:Layer="0" fill="none" points="605,-4573 582,-4573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="2751@0" ObjectIDZND0="g_3eceea0@0" ObjectIDZND1="g_45567f0@0" ObjectIDZND2="24708@x" Pin0InfoVect0LinkObjId="g_3eceea0_0" Pin0InfoVect1LinkObjId="g_45567f0_0" Pin0InfoVect2LinkObjId="SW-136709_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17945_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="605,-4573 582,-4573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf4b60">
     <polyline DF8003:Layer="0" fill="none" points="582,-4573 582,-4631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="2751@x" ObjectIDND1="g_3eceea0@0" ObjectIDND2="g_45567f0@0" ObjectIDZND0="2750@0" Pin0InfoVect0LinkObjId="SW-17944_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-17945_0" Pin1InfoVect1LinkObjId="g_3eceea0_0" Pin1InfoVect2LinkObjId="g_45567f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="582,-4573 582,-4631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf5690">
     <polyline DF8003:Layer="0" fill="none" points="771,-4570 755,-4570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="2848@0" ObjectIDZND0="g_275a100@0" ObjectIDZND1="24711@x" ObjectIDZND2="g_36ebcc0@0" Pin0InfoVect0LinkObjId="g_275a100_0" Pin0InfoVect1LinkObjId="SW-136728_0" Pin0InfoVect2LinkObjId="g_36ebcc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18859_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="771,-4570 755,-4570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf58f0">
     <polyline DF8003:Layer="0" fill="none" points="755,-4570 755,-4626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_275a100@0" ObjectIDND1="24711@x" ObjectIDND2="g_36ebcc0@0" ObjectIDZND0="2847@0" Pin0InfoVect0LinkObjId="SW-18858_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_275a100_0" Pin1InfoVect1LinkObjId="SW-136728_0" Pin1InfoVect2LinkObjId="g_36ebcc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-4570 755,-4626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf63e0">
     <polyline DF8003:Layer="0" fill="none" points="873,-4637 873,-4574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20869@0" ObjectIDZND0="g_3d0a130@0" ObjectIDZND1="g_3d10970@0" ObjectIDZND2="g_3cd1710@0" Pin0InfoVect0LinkObjId="g_3d0a130_0" Pin0InfoVect1LinkObjId="g_3d10970_0" Pin0InfoVect2LinkObjId="g_3cd1710_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106841_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="873,-4637 873,-4574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf6640">
     <polyline DF8003:Layer="0" fill="none" points="873,-4574 887,-4574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3d0a130@0" ObjectIDND1="g_3d10970@0" ObjectIDND2="g_3cd1710@0" ObjectIDZND0="20870@0" Pin0InfoVect0LinkObjId="SW-106842_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3d0a130_0" Pin1InfoVect1LinkObjId="g_3d10970_0" Pin1InfoVect2LinkObjId="g_3cd1710_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="873,-4574 887,-4574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf7700">
     <polyline DF8003:Layer="0" fill="none" points="251,-3784 251,-3776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34071@x" ObjectIDND1="34074@x" ObjectIDZND0="g_375e150@0" ObjectIDZND1="34424@x" ObjectIDZND2="34428@x" Pin0InfoVect0LinkObjId="g_375e150_0" Pin0InfoVect1LinkObjId="SW-220423_0" Pin0InfoVect2LinkObjId="SW-220422_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-218997_0" Pin1InfoVect1LinkObjId="SW-218995_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3784 251,-3776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cf7960">
     <polyline DF8003:Layer="0" fill="none" points="251,-3776 191,-3776 191,-3786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_375e150@0" ObjectIDND1="34424@x" ObjectIDND2="34428@x" ObjectIDZND0="34075@0" Pin0InfoVect0LinkObjId="SW-218996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_375e150_0" Pin1InfoVect1LinkObjId="SW-220423_0" Pin1InfoVect2LinkObjId="SW-220422_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-3776 191,-3776 191,-3786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_430d360">
     <polyline DF8003:Layer="0" fill="none" points="221,-4130 221,-4167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34066@x" ObjectIDND1="34069@x" ObjectIDZND0="2755@x" ObjectIDZND1="2754@x" ObjectIDZND2="34070@x" Pin0InfoVect0LinkObjId="SW-17975_0" Pin0InfoVect1LinkObjId="SW-17974_0" Pin0InfoVect2LinkObjId="SW-218949_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134324_0" Pin1InfoVect1LinkObjId="SW-218948_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4130 221,-4167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_430d5c0">
     <polyline DF8003:Layer="0" fill="none" points="221,-4167 175,-4167 175,-4153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="2755@x" ObjectIDND1="2754@x" ObjectIDND2="34066@x" ObjectIDZND0="34070@1" Pin0InfoVect0LinkObjId="SW-218949_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-17975_0" Pin1InfoVect1LinkObjId="SW-17974_0" Pin1InfoVect2LinkObjId="SW-134324_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-4167 175,-4167 175,-4153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_430d820">
     <polyline DF8003:Layer="0" fill="none" points="1106,-3852 1106,-4336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3e37870@0" ObjectIDND1="24480@x" ObjectIDND2="24479@x" ObjectIDZND0="2764@x" ObjectIDZND1="2763@x" ObjectIDZND2="g_44f3f10@0" Pin0InfoVect0LinkObjId="SW-18064_0" Pin0InfoVect1LinkObjId="SW-18063_0" Pin0InfoVect2LinkObjId="g_44f3f10_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e37870_0" Pin1InfoVect1LinkObjId="SW-135210_0" Pin1InfoVect2LinkObjId="SW-135209_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1106,-3852 1106,-4336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_430fa50">
     <polyline DF8003:Layer="0" fill="none" points="1004,-4572 1004,-4336 1106,-4336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="2764@x" ObjectIDND1="2763@x" ObjectIDZND0="g_3e37870@0" ObjectIDZND1="24480@x" ObjectIDZND2="24479@x" Pin0InfoVect0LinkObjId="g_3e37870_0" Pin0InfoVect1LinkObjId="SW-135210_0" Pin0InfoVect2LinkObjId="SW-135209_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18064_0" Pin1InfoVect1LinkObjId="SW-18063_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1004,-4572 1004,-4336 1106,-4336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4312250">
     <polyline DF8003:Layer="0" fill="none" points="1018,-4572 1004,-4572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="2764@0" ObjectIDZND0="g_3e37870@0" ObjectIDZND1="24480@x" ObjectIDZND2="24479@x" Pin0InfoVect0LinkObjId="g_3e37870_0" Pin0InfoVect1LinkObjId="SW-135210_0" Pin0InfoVect2LinkObjId="SW-135209_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18064_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1018,-4572 1004,-4572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_43124b0">
     <polyline DF8003:Layer="0" fill="none" points="1004,-4572 1004,-4635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3e37870@0" ObjectIDND1="24480@x" ObjectIDND2="24479@x" ObjectIDZND0="2763@0" Pin0InfoVect0LinkObjId="SW-18063_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e37870_0" Pin1InfoVect1LinkObjId="SW-135210_0" Pin1InfoVect2LinkObjId="SW-135209_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1004,-4572 1004,-4635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b78b10">
     <polyline DF8003:Layer="0" fill="none" points="755,-4307 755,-4279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_275a100@0" ObjectIDND1="g_36ebcc0@0" ObjectIDND2="2848@x" ObjectIDZND0="24711@0" Pin0InfoVect0LinkObjId="SW-136728_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_275a100_0" Pin1InfoVect1LinkObjId="g_36ebcc0_0" Pin1InfoVect2LinkObjId="SW-18859_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-4307 755,-4279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b78d70">
     <polyline DF8003:Layer="0" fill="none" points="755,-4262 755,-4251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24711@1" ObjectIDZND0="24710@1" Pin0InfoVect0LinkObjId="SW-136723_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136728_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="755,-4262 755,-4251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b7f510">
     <polyline DF8003:Layer="0" fill="none" points="1605,-4772 1605,-4914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="19704@x" ObjectIDND1="4095@x" ObjectIDND2="0@x" ObjectIDZND0="g_3cc9c30@0" ObjectIDZND1="g_3ca45d0@0" ObjectIDZND2="g_38a06b0@0" Pin0InfoVect0LinkObjId="g_3cc9c30_0" Pin0InfoVect1LinkObjId="g_3ca45d0_0" Pin0InfoVect2LinkObjId="g_38a06b0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-93023_0" Pin1InfoVect1LinkObjId="SW-25681_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-4772 1605,-4914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b808e0">
     <polyline DF8003:Layer="0" fill="none" points="1611,-4464 1611,-4451 1598,-4451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24628@0" ObjectIDZND0="24626@0" Pin0InfoVect0LinkObjId="SW-136203_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-136206_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1611,-4464 1611,-4451 1598,-4451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b813d0">
     <polyline DF8003:Layer="0" fill="none" points="1505,-4451 1499,-4451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="24631@x" ObjectIDND1="24629@x" ObjectIDZND0="g_452e630@0" ObjectIDZND1="4106@x" ObjectIDZND2="24567@x" Pin0InfoVect0LinkObjId="g_452e630_0" Pin0InfoVect1LinkObjId="SW-25714_0" Pin0InfoVect2LinkObjId="SW-135733_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-136209_0" Pin1InfoVect1LinkObjId="SW-136207_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-4451 1499,-4451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d2cdd0">
     <polyline DF8003:Layer="0" fill="none" points="1499,-4451 1471,-4451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_452e630@0" ObjectIDZND0="4106@x" ObjectIDZND1="24567@x" ObjectIDZND2="24564@x" Pin0InfoVect0LinkObjId="SW-25714_0" Pin0InfoVect1LinkObjId="SW-135733_0" Pin0InfoVect2LinkObjId="SW-135724_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_452e630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1499,-4451 1471,-4451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d2d030">
     <polyline DF8003:Layer="0" fill="none" points="1389,-4451 1471,-4451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4106@x" ObjectIDND1="24567@x" ObjectIDND2="24564@x" ObjectIDZND0="g_452e630@0" ObjectIDZND1="g_3ef3430@0" Pin0InfoVect0LinkObjId="g_452e630_0" Pin0InfoVect1LinkObjId="g_3ef3430_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-25714_0" Pin1InfoVect1LinkObjId="SW-135733_0" Pin1InfoVect2LinkObjId="SW-135724_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-4451 1471,-4451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d2e370">
     <polyline DF8003:Layer="0" fill="none" points="1285,-4450 1285,-4428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="4100@0" ObjectIDZND0="g_44f3f10@0" ObjectIDZND1="0@x" ObjectIDZND2="g_3e37870@0" Pin0InfoVect0LinkObjId="g_44f3f10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3e37870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25695_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-4450 1285,-4428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d2e5d0">
     <polyline DF8003:Layer="0" fill="none" points="1285,-4428 1285,-4336 1106,-4336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_44f3f10@0" ObjectIDND1="0@x" ObjectIDND2="4100@x" ObjectIDZND0="g_3e37870@0" ObjectIDZND1="24480@x" ObjectIDZND2="24479@x" Pin0InfoVect0LinkObjId="g_3e37870_0" Pin0InfoVect1LinkObjId="SW-135210_0" Pin0InfoVect2LinkObjId="SW-135209_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_44f3f10_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-25695_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-4428 1285,-4336 1106,-4336 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="0" busDevId="0" cx="2421" cy="-4602" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="2314" cy="-4602" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="2207" cy="-4602" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24359" cx="1605" cy="-5176" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24359" cx="1427" cy="-5176" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24557" cx="1389" cy="-3910" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24325" cx="1074" cy="-5189" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24325" cx="1226" cy="-5189" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24789" cx="873" cy="-4133" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="28762" cx="1286" cy="-3910" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24622" cx="1670" cy="-4451" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="7274" cx="251" cy="-3513" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24419" cx="221" cy="-3942" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24419" cx="251" cy="-3942" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24419" cx="301" cy="-3942" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24419" cx="251" cy="-3942" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="2744" cx="470" cy="-4792" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="2744" cx="308" cy="-4792" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="2744" cx="221" cy="-4792" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="2744" cx="603" cy="-4792" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24473" cx="1106" cy="-3596" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24473" cx="470" cy="-3596" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="2745" cx="1004" cy="-4791" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="2745" cx="692" cy="-4791" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="2745" cx="873" cy="-4791" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="2744" cx="582" cy="-4792" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24703" cx="755" cy="-4167" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="24703" cx="582" cy="-4167" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9691" cx="1063" cy="-4542" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9691" cx="821" cy="-4542" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9691" cx="531" cy="-4542" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9691" cx="649" cy="-4542" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9691" cx="282" cy="-4542" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9691" cx="308" cy="-4542" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="9691" cx="932" cy="-4542" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="4049" cx="1427" cy="-4605" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="4049" cx="1389" cy="-4605" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="4049" cx="1285" cy="-4605" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="4049" cx="1468" cy="-4605" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="4050" cx="1781" cy="-4605" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="4050" cx="1605" cy="-4605" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="4050" cx="1559" cy="-4605" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 609.000000 -4602.000000) translate(0,17)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 537.000000 -4717.000000) translate(0,17)">381</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 278.000000 -4762.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 279.000000 -4668.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 420.000000 -4716.000000) translate(0,17)">383</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 438.000000 -4761.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 439.000000 -4667.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 261.000000 -4716.000000) translate(0,17)">385</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 555.000000 -4761.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 556.000000 -4667.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 -4602.000000) translate(0,17)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1019.000000 -4714.000000) translate(0,17)">384</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -4761.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 977.000000 -4667.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 321.000000 -4829.000000) translate(0,17)">110kV牟定变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 632.000000 -4917.000000) translate(0,17)">312</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 615.000000 -4838.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1027.000000 -4600.000000) translate(0,17)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 244.000000 -4600.000000) translate(0,17)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 172.000000 -4717.000000) translate(0,17)">382</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 189.000000 -4762.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 190.000000 -4668.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 299.000000 -3935.000000) translate(0,17)">飒马场变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -4162.000000) translate(0,17)">龙排变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 739.000000 -3592.000000) translate(0,17)">35kV红坡变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 -4544.000000) translate(0,17)">321</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -4581.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -4481.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1009.000000 -4510.000000) translate(0,17)">新</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1009.000000 -4510.000000) translate(0,38)">桥</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1009.000000 -4510.000000) translate(0,59)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1342.000000 -4544.000000) translate(0,17)">322</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -4590.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -4494.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1450.000000 -4590.000000) translate(0,17)">110kV新桥变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1382.000000 -4688.000000) translate(0,17)">323</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1407.000000 -4729.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -4641.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1615.000000 -4693.000000) translate(0,17)">325</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 -4743.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1613.000000 -4646.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1492.000000 -5208.000000) translate(0,17)">安乐变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1119.000000 -5225.000000) translate(0,17)">戌街变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1194.000000 -5094.000000) translate(0,17)">新</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1194.000000 -5094.000000) translate(0,38)">戌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1194.000000 -5094.000000) translate(0,59)">安</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1194.000000 -5094.000000) translate(0,80)">Ⅰ</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1194.000000 -5094.000000) translate(0,101)">回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1194.000000 -5094.000000) translate(0,122)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -4540.000000) translate(0,17)">326</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1748.000000 -4584.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1748.000000 -4490.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2223.000000 -4544.000000) translate(0,17)">388</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2250.000000 -4589.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.000000 -4496.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2332.000000 -4544.000000) translate(0,17)">387</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2360.000000 -4589.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2356.000000 -4497.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1831.000000 -4277.000000) translate(0,17)">甸心线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2285.000000 -4357.000000) translate(0,17)">3883D</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2393.000000 -4357.000000) translate(0,17)">3873D</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2117.000000 -4544.000000) translate(0,17)">389</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2135.000000 -4589.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2136.000000 -4495.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2178.000000 -4357.000000) translate(0,17)">3893D</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2109.000000 -4410.000000) translate(0,17)">3893</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2215.000000 -4408.000000) translate(0,17)">3883</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2325.000000 -4409.000000) translate(0,17)">3873</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2191.000000 -4461.000000) translate(0,17)">3800</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2296.000000 -4461.000000) translate(0,17)">3805</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2156.000000 -4638.000000) translate(0,17)">35kV甸心变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 -4570.000000) translate(0,17)">旁母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 774.000000 -4753.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -4658.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 705.000000 -4708.000000) translate(0,17)">386</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 780.000000 -4600.000000) translate(0,17)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2392.000000 -4293.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2158.000000 -4313.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2284.000000 -4293.000000) translate(0,17)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2772c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2772c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2772c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2772c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2772c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2772c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2772c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,59)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,101)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,143)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,164)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,185)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,206)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,227)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,248)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,269)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,290)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,311)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,332)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,353)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_27734e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3736d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 165.000000 -4818.000000) translate(0,17)">I段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3737200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1019.000000 -4817.000000) translate(0,17)">II段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3737440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -4631.000000) translate(0,17)">I段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3737680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1763.000000 -4628.000000) translate(0,17)">II段</text>
   <text DF8003:Layer="0" fill="rgb(38,38,38)" font-family="SimHei" font-size="20" graphid="g_3cd5090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -86.000000 -5212.000000) translate(0,16)">牟定片区35kV电网图</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3d69960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1501.000000 -4699.000000) translate(0,14)">312</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2772310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -4648.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c8d5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1564.000000 -4647.000000) translate(0,12)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42fb840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1486.000000 -4747.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cd00b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1558.000000 -4779.000000) translate(0,12)">3259</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3cca440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -5073.000000) translate(0,17)">新</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3cca440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -5073.000000) translate(0,38)">戌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3cca440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -5073.000000) translate(0,59)">安</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3cca440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -5073.000000) translate(0,80)">Ⅱ</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3cca440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -5073.000000) translate(0,101)">回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3cca440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -5073.000000) translate(0,122)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_221f030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -4980.000000) translate(0,12)">新</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_221f030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -4980.000000) translate(0,27)">戌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_221f030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -4980.000000) translate(0,42)">安</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_221f030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -4980.000000) translate(0,57)">Ⅱ</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_221f030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -4980.000000) translate(0,72)">回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_221f030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -4980.000000) translate(0,87)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb4270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.000000 -4739.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d744e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1384.000000 -4773.000000) translate(0,12)">3239</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_3d76290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1356.000000 -5008.000000) translate(0,8)">AB相TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_3ca3fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1534.000000 -5010.000000) translate(0,8)">AB相TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3d95a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -4763.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3d95f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 846.000000 -4669.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3d961c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 896.000000 -4602.000000) translate(0,17)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3d10330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 888.000000 -4716.000000) translate(0,17)">387</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3d67ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 798.000000 -4293.000000) translate(0,13)">37167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3d68510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 883.000000 -4220.000000) translate(0,17)">371</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d68750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 -4125.000000) translate(0,15)">长冲开关站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2896c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1344.000000 -4460.000000) translate(0,17)">N14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27585a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1402.000000 -4089.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2758a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1435.000000 -4127.000000) translate(0,12)">3819</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2758cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1506.000000 -4164.000000) translate(0,12)">38197</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b413c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1435.000000 -4127.000000) translate(0,12)">3819</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b418b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -4043.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b41af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1223.000000 -4068.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b41d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1299.000000 -3993.000000) translate(0,12)">382</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ca0e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -3948.000000) translate(0,12)">3822</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287cf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 -3904.000000) translate(0,12)">35kVⅡ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287d500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1423.000000 -3907.000000) translate(0,12)">35kVⅠ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287ffa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1393.000000 -3885.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d8fb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 -4039.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d920c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.000000 -3992.000000) translate(0,12)">381</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3caacd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 -3948.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c8a530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1513.000000 -4443.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c8cdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1573.000000 -4443.000000) translate(0,12)">341</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb9bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1624.000000 -4443.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cba0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1485.000000 -4543.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cba2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1538.000000 -4543.000000) translate(0,12)">34160</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cba530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1651.000000 -4491.000000) translate(0,12)">Ⅰ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b73a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1575.000000 -4542.000000) translate(0,12)">34117</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b75c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -4021.000000) translate(0,16)">新</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b75c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -4021.000000) translate(0,36)">余</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b75c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -4021.000000) translate(0,56)">古</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b75c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -4021.000000) translate(0,76)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b76780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -4020.000000) translate(0,16)">新余</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b76780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -4020.000000) translate(0,36)">古线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b76780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -4020.000000) translate(0,56)">Ⅱ回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b76780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -4020.000000) translate(0,76)">T线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b77120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1404.000000 -3837.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1309.000000 -3904.000000) translate(0,17)">余丁变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_45d9cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 702.000000 -4841.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45dc0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 175.000000 -3933.000000) translate(0,12)">I段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45dc5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 228.000000 -3981.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45dc7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 420.000000 -3616.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45dca20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 477.000000 -3639.000000) translate(0,12)">3921</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45dcc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 479.000000 -3691.000000) translate(0,12)">392</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45dcea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 477.000000 -3740.000000) translate(0,12)">3926</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45dd0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -3794.000000) translate(0,12)">39267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45dd320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1113.000000 -3645.000000) translate(0,12)">3911</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45dd560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1115.000000 -3697.000000) translate(0,12)">391</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45dd7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1113.000000 -3746.000000) translate(0,12)">3916</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45dd9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1119.000000 -3791.000000) translate(0,12)">39167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45ddc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.000000 -4188.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45dde60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 591.000000 -4235.000000) translate(0,12)">341</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45de0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 763.674556 -4245.000000) translate(0,12)">342</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45de2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1023.000000 -5184.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45de520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1081.000000 -5164.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45de760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -5121.000000) translate(0,12)">362</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6ba90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1081.000000 -5074.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6bc90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.000000 -5057.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6bed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -5006.000000) translate(0,12)">3629</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6c110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -5165.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6c350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -5122.000000) translate(0,12)">361</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6c590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -5078.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6c7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1243.000000 -5062.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6ca10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1253.000000 -5010.000000) translate(0,12)">3619</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6cc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -5153.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6ce90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1436.000000 -5107.000000) translate(0,12)">371</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6d0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -5058.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6d310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1446.000000 -5039.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6d550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1612.000000 -5150.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6d790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 -5103.000000) translate(0,12)">372</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6d9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1612.000000 -5054.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6dc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1623.000000 -5036.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d6de50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -5171.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3d6e090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -4894.000000) translate(0,13)">新</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3d6e090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -4894.000000) translate(0,29)">戌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3d6e090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -4894.000000) translate(0,45)">安</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3d6e090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -4894.000000) translate(0,61)">Ⅰ</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3d6e090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -4894.000000) translate(0,77)">回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3d6e090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -4894.000000) translate(0,93)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3d6f250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1743.000000 -4767.000000) translate(0,10)">35kV力石1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3d6f250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1743.000000 -4767.000000) translate(0,22)">号站用变支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3d703a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1765.000000 -4705.000000) translate(0,10)"> 35kV1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3d703a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1765.000000 -4705.000000) translate(0,22)">号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3d709b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1792.000000 -4667.000000) translate(0,10)">至0.4kV系统</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3d709b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1792.000000 -4667.000000) translate(0,22)">220kV力石变</text>
   <text DF8003:Layer="0" fill="rgb(0,0,0)" font-family="SimSun" font-size="25" graphid="g_48d0940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -5216.000000) translate(0,20)">配调返回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb0b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 230.000000 -4034.000000) translate(0,12)">381</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb1040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 228.000000 -4084.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb1280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 241.000000 -4152.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd77c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 260.000000 -3866.000000) translate(0,12)">382</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd7df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 258.000000 -3909.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fd8030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 259.000000 -3827.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4305960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 271.000000 -3808.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b43950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 217.000000 -3891.000000) translate(0,16)">凤</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b43950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 217.000000 -3891.000000) translate(0,36)">飒</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b43950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 217.000000 -3891.000000) translate(0,56)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b44560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 203.000000 -3509.000000) translate(0,17)">凤屯升压站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b47b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 137.000000 -4135.000000) translate(0,12)">3819</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3759d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 304.000000 -3979.000000) translate(0,12)">3841</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_375a210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 307.000000 -4117.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_375aa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 262.668750 -3574.000000) translate(0,12)">367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_375ad00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 268.000000 -3623.000000) translate(0,12)">36767</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_375b3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 198.000000 -3809.000000) translate(0,12)">3829</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 -4513.000000) translate(0,16)">龙</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 -4513.000000) translate(0,36)">排</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 -4513.000000) translate(0,56)">Ⅱ</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 -4513.000000) translate(0,76)">回</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 -4513.000000) translate(0,96)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 192.000000 -4150.000000) translate(0,16)">飒</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 192.000000 -4150.000000) translate(0,36)">马</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 192.000000 -4150.000000) translate(0,56)">场</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 192.000000 -4150.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 559.000000 -4510.000000) translate(0,16)">龙</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 559.000000 -4510.000000) translate(0,36)">排</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 559.000000 -4510.000000) translate(0,56)">Ⅰ</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 559.000000 -4510.000000) translate(0,76)">回</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 559.000000 -4510.000000) translate(0,96)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 437.000000 -3958.000000) translate(0,16)">红</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 437.000000 -3958.000000) translate(0,36)">坡</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 437.000000 -3958.000000) translate(0,56)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -3726.000000) translate(0,16)">红</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -3726.000000) translate(0,36)">坡</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -3726.000000) translate(0,56)">T</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -3726.000000) translate(0,76)">接</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -3726.000000) translate(0,96)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -4316.000000) translate(0,11)">2</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -4316.000000) translate(0,24)">号</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -4316.000000) translate(0,37)">站</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -4316.000000) translate(0,50)">用</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -4316.000000) translate(0,63)">变</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -4577.000000) translate(0,12)">2</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -4577.000000) translate(0,27)">号</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -4577.000000) translate(0,42)">站</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -4577.000000) translate(0,57)">用</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -4577.000000) translate(0,72)">变</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1402.000000 -4353.000000) translate(0,16)">新</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1402.000000 -4353.000000) translate(0,36)">余</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1402.000000 -4353.000000) translate(0,56)">古</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1402.000000 -4353.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 404.000000 -3842.000000) translate(0,6)">AB相TV</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 502.000000 -4309.000000) translate(0,6)">AB相TV</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 678.000000 -4323.000000) translate(0,6)">AB相TV</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1676.000000 -4477.000000) translate(0,16)">古</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1676.000000 -4477.000000) translate(0,36)">岩</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1676.000000 -4477.000000) translate(0,56)">变</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d32970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 -4510.000000) translate(0,16)">牟</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d32970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 -4510.000000) translate(0,36)">冲</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3d32970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 -4510.000000) translate(0,56)">线</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="0" fill="none" height="22" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="169" y="-4109"/>
   <rect DF8003:Layer="0" fill="none" height="22" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="295" y="-4019"/>
   <rect DF8003:Layer="0" fill="none" height="16" stroke="rgb(255,255,0)" stroke-width="1" width="9" x="187" y="-3846"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="0" id="BS-MD_XJ.MD_XJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1027,-5189 1275,-5189 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24325" ObjectName="BS-MD_XJ.MD_XJ_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1027,-5189 1275,-5189 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-MD_AL.MD_AL_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1387,-5176 1649,-5176 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24359" ObjectName="BS-MD_AL.MD_AL_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1387,-5176 1649,-5176 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_XQ.CX_XQ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1246,-4605 1495,-4605 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4049" ObjectName="BS-CX_XQ.CX_XQ_3IM"/>
    <cge:TPSR_Ref TObjectID="4049"/></metadata>
   <polyline fill="none" opacity="0" points="1246,-4605 1495,-4605 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_XQ.CX_XQ_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1533,-4605 1804,-4605 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4050" ObjectName="BS-CX_XQ.CX_XQ_3IIM"/>
    <cge:TPSR_Ref TObjectID="4050"/></metadata>
   <polyline fill="none" opacity="0" points="1533,-4605 1804,-4605 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="2099,-4602 2543,-4602 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2099,-4602 2543,-4602 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-MD_GY.MD_GY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1670,-4474 1670,-4420 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24622" ObjectName="BS-MD_GY.MD_GY_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1670,-4474 1670,-4420 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_MD.CX_MD_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="163,-4792 641,-4792 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2744" ObjectName="BS-CX_MD.CX_MD_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="163,-4792 641,-4792 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_MD.CX_MD_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="667,-4791 1089,-4791 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2745" ObjectName="BS-CX_MD.CX_MD_3IIM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="667,-4791 1089,-4791 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-MD_HP.MD_HP_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="420,-3596 1156,-3596 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24473" ObjectName="BS-MD_HP.MD_HP_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="420,-3596 1156,-3596 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_MD.CX_MD_3PM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="163,-4542 1105,-4542 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9691" ObjectName="BS-CX_MD.CX_MD_3PM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="163,-4542 1105,-4542 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-MD_LP.MD_LP_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="551,-4168 793,-4168 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24703" ObjectName="BS-MD_LP.MD_LP_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="551,-4168 793,-4168 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-MD_SMC.MD_SMC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="161,-3942 354,-3942 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24419" ObjectName="BS-MD_SMC.MD_SMC_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="161,-3942 354,-3942 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_CC.CX_CC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="845,-4133 904,-4133 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24789" ObjectName="BS-CX_CC.CX_CC_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="845,-4133 904,-4133 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-MD_YD.MD_YD_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1238,-3910 1334,-3910 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28762" ObjectName="BS-MD_YD.MD_YD_3IIM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1238,-3910 1334,-3910 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-MD_YD.MD_YD_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1350,-3910 1432,-3910 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24557" ObjectName="BS-MD_YD.MD_YD_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1350,-3910 1432,-3910 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_FT.CX_FT_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="173,-3513 334,-3513 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="7274" ObjectName="BS-CX_FT.CX_FT_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="173,-3513 334,-3513 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="0" id="g_3dfdf80">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2475.551724 -4300.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2870980">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2369.551724 -4300.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3c60160">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 2163.551724 -4287.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_22f07c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1442.000000 -4994.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_38a06b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1620.000000 -4995.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_28b4120">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1250.000000 -5016.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ef3430">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1460.000000 -4380.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_452e630">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 1509.000000 -4362.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_44f3f10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1204.000000 -4352.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e70720">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 979.000000 -4412.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_46b7880">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1194.000000 -3803.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3e37870">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1121.000000 -3846.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_275a100">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 668.674556 -4297.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_36ebcc0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 742.674556 -4377.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_45567f0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 497.000000 -4280.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3eceea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.000000 -4334.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2784800">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 398.000000 -3808.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3735c80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 485.000000 -3862.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3cc32f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2342.000000 -4184.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_42fae20">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1510.000000 -4751.000000)" xlink:href="#lightningRod:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_42fbd30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1515.000000 -4749.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3cc1f80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1104.000000 -5011.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3cc9c30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1410.000000 -4909.000000)" xlink:href="#lightningRod:shape181"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3cb4760">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.000000 -4741.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d74bc0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1332.000000 -4743.000000)" xlink:href="#lightningRod:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ca2e30">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1339.000000 -4976.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ca45d0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1517.000000 -4978.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d10970">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 898.500000 -4390.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d0a130">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 786.000000 -4390.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d0c130">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 788.000000 -4310.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d1d1a0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 900.500000 -4310.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3cd1710">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 878.000000 -4392.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b53e90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1364.000000 -4161.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b556d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1250.000000 -4132.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2758f10">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1551.000000 -4089.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d15310">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1728.000000 -4782.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b49000">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 285.000000 -4102.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="40" qtmmishow="hidden" width="226" x="-117" y="-5223"/>
    </a>
   <metadata/><rect fill="white" height="40" opacity="0" stroke="white" transform="" width="226" x="-117" y="-5223"/></g>
   <g DF8003:Layer="0" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-166" y="-5240"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-166" y="-5240"/></g>
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="39" qtmmishow="hidden" width="127" x="138" y="-5223"/>
    </a>
   <metadata/><rect fill="white" height="39" opacity="0" stroke="white" transform="" width="127" x="138" y="-5223"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(205,205,0)" stroke-width="1" x1="1499" x2="1499" y1="-4761" y2="-4754"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(205,205,0)" stroke-width="1" x1="1491" x2="1491" y1="-4761" y2="-4754"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1096" x2="1100" y1="-4952" y2="-4952"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1096" x2="1100" y1="-4962" y2="-4962"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1245" x2="1249" y1="-4957" y2="-4957"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1245" x2="1249" y1="-4967" y2="-4967"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(205,205,0)" stroke-width="1" x1="1321" x2="1321" y1="-4753" y2="-4746"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(205,205,0)" stroke-width="1" x1="1313" x2="1313" y1="-4753" y2="-4746"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(30,144,255)" stroke-dasharray="10 5 " stroke-width="1" x1="1389" x2="1389" y1="-3830" y2="-3809"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="1343" x2="1343" y1="-3833" y2="-3833"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(30,144,255)" stroke-width="1" x1="1375" x2="1375" y1="-3830" y2="-3830"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="301" x2="324" y1="-4092" y2="-4075"/>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="0" id="g_3eea190" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 2375.551724 -4359.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ef3930" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 2493.551724 -4354.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_28c4130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 2266.551724 -4359.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2871aa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1488.000000 -5009.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d11d70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1286.000000 -5031.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_47c7f40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1554.000000 -4129.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2486430" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1165.000000 -3761.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_4592f40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 408.000000 -3759.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3dab960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.428571 -0.000000 0.000000 -1.500000 1135.000000 -5027.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3cc76d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1665.000000 -5012.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3d669c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 1.454545 837.000000 -4247.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_37340f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1213.000000 -4068.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b72fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1605.000000 -4507.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b73f30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1551.000000 -4508.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b74740" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1499.000000 -4510.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b751a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1452.000000 -4059.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b77370" refnum="0">
    <use class="BV-0KV" transform="matrix(1.476190 -0.000000 0.000000 -1.500000 1457.000000 -3834.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3cb0120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 286.000000 -4124.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_4304ed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.000000 -3778.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_4309080" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.000000 -3623.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图.svg" style="fill-opacity:0"><rect height="40" qtmmishow="hidden" width="226" x="-117" y="-5223"/></g>
   <g href="cx_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-166" y="-5240"/></g>
   <g href="cx_配调_配网接线图35_牟定.svg" style="fill-opacity:0"><rect height="39" qtmmishow="hidden" width="127" x="138" y="-5223"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2411.551724 -4549.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2411.551724 -4455.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2304.551724 -4549.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2304.551724 -4447.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2197.551724 -4547.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2197.551724 -4451.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2411.551724 -4376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2443.551724 -4358.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2337.551724 -4424.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2230.551724 -4424.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2197.551724 -4375.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2218.551724 -4363.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2327.551724 -4363.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-25665">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1771.551724 -4552.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4088" ObjectName="SW-CX_XQ.CX_XQ_3262SW"/>
     <cge:Meas_Ref ObjectId="25665"/>
    <cge:TPSR_Ref TObjectID="4088"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-25666">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1771.551724 -4456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4089" ObjectName="SW-CX_XQ.CX_XQ_3266SW"/>
     <cge:Meas_Ref ObjectId="25666"/>
    <cge:TPSR_Ref TObjectID="4089"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-25680">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1596.000000 -4613.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4094" ObjectName="SW-CX_XQ.CX_XQ_3252SW"/>
     <cge:Meas_Ref ObjectId="25680"/>
    <cge:TPSR_Ref TObjectID="4094"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-25681">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1596.000000 -4704.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4095" ObjectName="SW-CX_XQ.CX_XQ_3256SW"/>
     <cge:Meas_Ref ObjectId="25681"/>
    <cge:TPSR_Ref TObjectID="4095"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-133212">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1596.000000 -5120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24367" ObjectName="SW-MD_AL.MD_AL_3721SW"/>
     <cge:Meas_Ref ObjectId="133212"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-133214">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1596.000000 -5024.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24369" ObjectName="SW-MD_AL.MD_AL_3726SW"/>
     <cge:Meas_Ref ObjectId="133214"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-133192">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -5123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24363" ObjectName="SW-MD_AL.MD_AL_3711SW"/>
     <cge:Meas_Ref ObjectId="133192"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-133193">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -5028.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24364" ObjectName="SW-MD_AL.MD_AL_3716SW"/>
     <cge:Meas_Ref ObjectId="133193"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-133194">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1440.000000 -5013.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24365" ObjectName="SW-MD_AL.MD_AL_37167SW"/>
     <cge:Meas_Ref ObjectId="133194"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-26024">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -4699.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4185" ObjectName="SW-CX_XQ.CX_XQ_3236SW"/>
     <cge:Meas_Ref ObjectId="26024"/>
    <cge:TPSR_Ref TObjectID="4185"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-26022">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -4609.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4183" ObjectName="SW-CX_XQ.CX_XQ_3231SW"/>
     <cge:Meas_Ref ObjectId="26022"/>
    <cge:TPSR_Ref TObjectID="4183"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-133216">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1619.000000 -5013.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24371" ObjectName="SW-MD_AL.MD_AL_37267SW"/>
     <cge:Meas_Ref ObjectId="133216"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-132273">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 -5135.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24243" ObjectName="SW-MD_XJ.MD_XJ_3611SW"/>
     <cge:Meas_Ref ObjectId="132273"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-132275">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1217.000000 -5048.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24244" ObjectName="SW-MD_XJ.MD_XJ_3616SW"/>
     <cge:Meas_Ref ObjectId="132275"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-132280">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1238.000000 -5035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24248" ObjectName="SW-MD_XJ.MD_XJ_36167SW"/>
     <cge:Meas_Ref ObjectId="132280"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-25713">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 -4549.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4105" ObjectName="SW-CX_XQ.CX_XQ_3221SW"/>
     <cge:Meas_Ref ObjectId="25713"/>
    <cge:TPSR_Ref TObjectID="4105"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-25714">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 -4458.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4106" ObjectName="SW-CX_XQ.CX_XQ_3226SW"/>
     <cge:Meas_Ref ObjectId="25714"/>
    <cge:TPSR_Ref TObjectID="4106"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-136209">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1496.000000 -4460.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24631" ObjectName="SW-MD_GY.MD_GY_34167SW"/>
     <cge:Meas_Ref ObjectId="136209"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135734">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1503.000000 -4133.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24568" ObjectName="SW-MD_YD.MD_YD_38197SW"/>
     <cge:Meas_Ref ObjectId="135734"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1398.000000 -4060.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24564" ObjectName="SW-MD_YD.MD_YD_38167SW"/>
     <cge:Meas_Ref ObjectId="135724"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-25694">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 -4544.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4099" ObjectName="SW-CX_XQ.CX_XQ_3211SW"/>
     <cge:Meas_Ref ObjectId="25694"/>
    <cge:TPSR_Ref TObjectID="4099"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-25695">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 -4445.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4100" ObjectName="SW-CX_XQ.CX_XQ_3216SW"/>
     <cge:Meas_Ref ObjectId="25695"/>
    <cge:TPSR_Ref TObjectID="4100"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1217.000000 -4502.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18062">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 995.000000 -4729.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2762" ObjectName="SW-CX_MD.CX_MD_3841SW"/>
     <cge:Meas_Ref ObjectId="18062"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18063">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 995.000000 -4630.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2763" ObjectName="SW-CX_MD.CX_MD_3842SW"/>
     <cge:Meas_Ref ObjectId="18063"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18064">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1013.000000 -4567.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2764" ObjectName="SW-CX_MD.CX_MD_3844SW"/>
     <cge:Meas_Ref ObjectId="18064"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 992.000000 -4260.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135209">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1097.000000 -3716.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24479" ObjectName="SW-MD_HP.MD_HP_3916SW"/>
     <cge:Meas_Ref ObjectId="135209"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135208">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1097.000000 -3615.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24478" ObjectName="SW-MD_HP.MD_HP_3911SW"/>
     <cge:Meas_Ref ObjectId="135208"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135210">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 -3765.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24480" ObjectName="SW-MD_HP.MD_HP_39167SW"/>
     <cge:Meas_Ref ObjectId="135210"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18857">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 745.674556 -4720.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2846" ObjectName="SW-CX_MD.CX_MD_3861SW"/>
     <cge:Meas_Ref ObjectId="18857"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18858">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 745.674556 -4621.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2847" ObjectName="SW-CX_MD.CX_MD_3862SW"/>
     <cge:Meas_Ref ObjectId="18858"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18859">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.674556 -4565.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2848" ObjectName="SW-CX_MD.CX_MD_3864SW"/>
     <cge:Meas_Ref ObjectId="18859"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-136728">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 744.674556 -4255.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24711" ObjectName="SW-MD_LP.MD_LP_342XC"/>
     <cge:Meas_Ref ObjectId="136728"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-136728">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 744.674556 -4186.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24712" ObjectName="SW-MD_LP.MD_LP_342XC1"/>
     <cge:Meas_Ref ObjectId="136728"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-136709">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.000000 -4250.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24708" ObjectName="SW-MD_LP.MD_LP_341XC"/>
     <cge:Meas_Ref ObjectId="136709"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-17943">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 573.000000 -4725.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2749" ObjectName="SW-CX_MD.CX_MD_3811SW"/>
     <cge:Meas_Ref ObjectId="17943"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-17944">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 573.000000 -4626.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2750" ObjectName="SW-CX_MD.CX_MD_3812SW"/>
     <cge:Meas_Ref ObjectId="17944"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18003">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 -4730.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2757" ObjectName="SW-CX_MD.CX_MD_3831SW"/>
     <cge:Meas_Ref ObjectId="18003"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 -4631.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2758" ObjectName="SW-CX_MD.CX_MD_3832SW"/>
     <cge:Meas_Ref ObjectId="18004"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135226">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 428.000000 -3763.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24484" ObjectName="SW-MD_HP.MD_HP_39267SW"/>
     <cge:Meas_Ref ObjectId="135226"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135225">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 -3710.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24483" ObjectName="SW-MD_HP.MD_HP_3926SW"/>
     <cge:Meas_Ref ObjectId="135225"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135224">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 461.000000 -3609.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24482" ObjectName="SW-MD_HP.MD_HP_3921SW"/>
     <cge:Meas_Ref ObjectId="135224"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18005">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 -4569.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2759" ObjectName="SW-CX_MD.CX_MD_3834SW"/>
     <cge:Meas_Ref ObjectId="18005"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-17945">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 600.000000 -4568.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2751" ObjectName="SW-CX_MD.CX_MD_3814SW"/>
     <cge:Meas_Ref ObjectId="17945"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-17975">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 232.000000 -4568.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2755" ObjectName="SW-CX_MD.CX_MD_3824SW"/>
     <cge:Meas_Ref ObjectId="17975"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18590">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.000000 -4731.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2833" ObjectName="SW-CX_MD.CX_MD_3851SW"/>
     <cge:Meas_Ref ObjectId="18590"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18591">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.000000 -4632.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2834" ObjectName="SW-CX_MD.CX_MD_3852SW"/>
     <cge:Meas_Ref ObjectId="18591"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-17974">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 212.000000 -4633.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2754" ObjectName="SW-CX_MD.CX_MD_3822SW"/>
     <cge:Meas_Ref ObjectId="17974"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-17973">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 212.000000 -4732.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2753" ObjectName="SW-CX_MD.CX_MD_3821SW"/>
     <cge:Meas_Ref ObjectId="17973"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-134323">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 212.000000 -3951.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34068" ObjectName="SW-MD_SMC.MD_SMC_3811SW"/>
     <cge:Meas_Ref ObjectId="134323"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-18042">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 594.000000 -4805.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3022" ObjectName="SW-CX_MD.CX_MD_3801SW"/>
     <cge:Meas_Ref ObjectId="18042"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-106657">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.000000 -4808.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20854" ObjectName="SW-CX_MD.CX_MD_3122SW"/>
     <cge:Meas_Ref ObjectId="106657"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-136709">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.000000 -4176.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24709" ObjectName="SW-MD_LP.MD_LP_341XC1"/>
     <cge:Meas_Ref ObjectId="136709"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-55895">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1459.000000 -4618.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10193" ObjectName="SW-CX_XQ.CX_XQ_3121SW"/>
     <cge:Meas_Ref ObjectId="55895"/>
    <cge:TPSR_Ref TObjectID="10193"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-55896">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1550.000000 -4617.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10194" ObjectName="SW-CX_XQ.CX_XQ_3122SW"/>
     <cge:Meas_Ref ObjectId="55896"/>
    <cge:TPSR_Ref TObjectID="10194"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-93023">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1553.000000 -4753.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19704" ObjectName="SW-CX_XQ.CX_XQ_3259SW"/>
     <cge:Meas_Ref ObjectId="93023"/>
    <cge:TPSR_Ref TObjectID="19704"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-132301">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1065.000000 -5134.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24250" ObjectName="SW-MD_XJ.MD_XJ_3621SW"/>
     <cge:Meas_Ref ObjectId="132301"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-132302">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1065.000000 -5044.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24251" ObjectName="SW-MD_XJ.MD_XJ_3626SW"/>
     <cge:Meas_Ref ObjectId="132302"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-132304">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1086.000000 -5031.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24253" ObjectName="SW-MD_XJ.MD_XJ_36267SW"/>
     <cge:Meas_Ref ObjectId="132304"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-132303">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.785714 -0.000000 0.000000 -0.760870 1091.000000 -4981.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24252" ObjectName="SW-MD_XJ.MD_XJ_3629SW"/>
     <cge:Meas_Ref ObjectId="132303"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-132277">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.785714 -0.000000 0.000000 -0.760870 1240.000000 -4985.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24245" ObjectName="SW-MD_XJ.MD_XJ_3619SW"/>
     <cge:Meas_Ref ObjectId="132277"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-64573">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.000000 -4745.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12195" ObjectName="SW-CX_XQ.CX_XQ_3239SW"/>
     <cge:Meas_Ref ObjectId="64573"/>
    <cge:TPSR_Ref TObjectID="12195"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-106840">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 864.000000 -4731.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20868" ObjectName="SW-CX_MD.CX_MD_3872SW"/>
     <cge:Meas_Ref ObjectId="106840"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-106841">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 864.000000 -4632.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20869" ObjectName="SW-CX_MD.CX_MD_3876SW"/>
     <cge:Meas_Ref ObjectId="106841"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-106842">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 882.000000 -4569.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20870" ObjectName="SW-CX_MD.CX_MD_3875SW"/>
     <cge:Meas_Ref ObjectId="106842"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-137073">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 863.000000 -4240.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24763" ObjectName="SW-CX_CC.CX_CC_371XC"/>
     <cge:Meas_Ref ObjectId="137073"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-137073">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 862.000000 -4145.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24764" ObjectName="SW-CX_CC.CX_CC_371XC1"/>
     <cge:Meas_Ref ObjectId="137073"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-137074">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 837.000000 -4249.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24765" ObjectName="SW-CX_CC.CX_CC_37167SW"/>
     <cge:Meas_Ref ObjectId="137074"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-136208">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1548.000000 -4460.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24630" ObjectName="SW-MD_GY.MD_GY_34160SW"/>
     <cge:Meas_Ref ObjectId="136208"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1236.000000 -4069.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28766" ObjectName="SW-MD_YD.MD_YD_38267SW"/>
     <cge:Meas_Ref ObjectId="188720"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188718">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1277.000000 -4013.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28764" ObjectName="SW-MD_YD.MD_YD_3826SW"/>
     <cge:Meas_Ref ObjectId="188718"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135733">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1428.000000 -4096.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24567" ObjectName="SW-MD_YD.MD_YD_3819SW"/>
     <cge:Meas_Ref ObjectId="135733"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1277.000000 -3918.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28765" ObjectName="SW-MD_YD.MD_YD_3822SW"/>
     <cge:Meas_Ref ObjectId="188719"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 -3859.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135722">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 -4009.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24562" ObjectName="SW-MD_YD.MD_YD_3816SW"/>
     <cge:Meas_Ref ObjectId="135722"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-135723">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 -3918.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24563" ObjectName="SW-MD_YD.MD_YD_3811SW"/>
     <cge:Meas_Ref ObjectId="135723"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-136207">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1507.000000 -4446.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24629" ObjectName="SW-MD_GY.MD_GY_3416SW"/>
     <cge:Meas_Ref ObjectId="136207"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-136205">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1616.000000 -4446.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24627" ObjectName="SW-MD_GY.MD_GY_3411SW"/>
     <cge:Meas_Ref ObjectId="136205"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-136206">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1602.000000 -4459.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24628" ObjectName="SW-MD_GY.MD_GY_34117SW"/>
     <cge:Meas_Ref ObjectId="136206"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1401.000000 -3838.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2304.551724 -4375.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1745.000000 -4767.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-218948">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 212.000000 -4054.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34069" ObjectName="SW-MD_SMC.MD_SMC_3816SW"/>
     <cge:Meas_Ref ObjectId="218948"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-134324">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 233.000000 -4125.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34066" ObjectName="SW-MD_SMC.MD_SMC_38167SW"/>
     <cge:Meas_Ref ObjectId="134324"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-218994">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 242.000000 -3879.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34073" ObjectName="SW-MD_SMC.MD_SMC_3821SW"/>
     <cge:Meas_Ref ObjectId="218994"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-218995">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 242.000000 -3792.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34074" ObjectName="SW-MD_SMC.MD_SMC_3826SW"/>
     <cge:Meas_Ref ObjectId="218995"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-220422">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 240.668750 -3519.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34423" ObjectName="SW-CX_FT.CX_FT_367XC"/>
     <cge:Meas_Ref ObjectId="220422"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-220422">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 240.668750 -3581.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34428" ObjectName="SW-CX_FT.CX_FT_367XC1"/>
     <cge:Meas_Ref ObjectId="220422"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-218997">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 267.000000 -3779.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34071" ObjectName="SW-MD_SMC.MD_SMC_38267SW"/>
     <cge:Meas_Ref ObjectId="218997"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-220423">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 266.000000 -3624.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34424" ObjectName="SW-CX_FT.CX_FT_36767SW"/>
     <cge:Meas_Ref ObjectId="220423"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-218949">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 166.000000 -4112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34070" ObjectName="SW-MD_SMC.MD_SMC_3819SW"/>
     <cge:Meas_Ref ObjectId="218949"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-134407">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 292.000000 -3949.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34055" ObjectName="SW-MD_SMC.MD_SMC_3841SW"/>
     <cge:Meas_Ref ObjectId="134407"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-218996">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 182.000000 -3781.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34075" ObjectName="SW-MD_SMC.MD_SMC_3829SW"/>
     <cge:Meas_Ref ObjectId="218996"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="0" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -117.000000 -5164.013514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="$AUDIT-BAD-LAYER:0.000000 0.000000" layer11="图层2:0.000000 0.000000" layer12="GDXT:0.000000 0.000000" layer13="Defpoints:0.000000 0.000000" layer14="yc:0.000000 0.000000" layer15="PUBLIC:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer4="$AUDIT-BAD-LAYER:0.000000 0.000000" layer5="图层2:0.000000 0.000000" layer6="GDXT:0.000000 0.000000" layer7="Defpoints:0.000000 0.000000" layer8="yc:0.000000 0.000000" layer9="0:0.000000 0.000000" layerN="16" moveAndZoomFlag="1"/>
</svg>