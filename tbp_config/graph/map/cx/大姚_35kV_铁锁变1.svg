<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-339" aopId="1311234" id="thSvg" product="E8000V2" version="1.0" viewBox="-686 -1477 1953 1468">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape25">
    <polyline arcFlag="1" points="26,105 24,105 22,104 21,104 19,103 18,102 16,101 15,99 14,97 14,96 13,94 13,92 13,90 14,88 14,87 15,85 16,84 18,82 19,81 21,80 22,80 24,79 26,79 28,79 30,80 31,80 33,81 34,82 36,84 37,85 38,87 38,88 39,90 39,92 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="39" x2="26" y1="92" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="60" y2="52"/>
    <polyline arcFlag="1" points="43,19 44,19 45,19 45,19 46,19 46,20 47,20 47,21 48,21 48,22 48,22 48,23 49,24 49,24 49,25 48,26 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,29 45,30 44,30 43,30 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,41 44,41 45,41 45,42 46,42 46,42 47,43 47,43 48,44 48,44 48,45 48,45 49,46 49,47 49,47 48,48 48,49 48,49 48,50 47,50 47,51 46,51 46,52 45,52 45,52 44,52 43,52 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="26" x2="26" y1="92" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="26" x2="43" y1="13" y2="13"/>
    <rect height="23" stroke-width="0.369608" width="12" x="20" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="7" x2="43" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="9" x2="9" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="42" x2="42" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="19" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="17" x2="33" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="18" x2="33" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="60" y2="35"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="29"/>
    <polyline arcFlag="1" points="43,30 44,30 45,30 45,30 46,31 46,31 47,31 47,32 48,32 48,33 48,34 48,34 49,35 49,36 49,36 48,37 48,38 48,38 48,39 47,39 47,40 46,40 46,40 45,41 45,41 44,41 43,41 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.1301" x1="6" x2="6" y1="17" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.1301" x1="0" x2="13" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.1301" x1="4" x2="9" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.06525" x1="5" x2="8" y1="2" y2="2"/>
    <polyline arcFlag="1" points="7,29 7,29 7,29 7,29 8,29 8,30 8,30 8,30 9,30 9,31 9,31 9,31 9,32 9,32 9,32 9,33 9,33 9,34 9,34 8,34 8,34 8,35 8,35 7,35 7,35 7,35 " stroke-width="0.323101"/>
    <polyline arcFlag="1" points="6,17 7,17 7,17 7,17 7,17 8,17 8,17 8,18 8,18 8,18 8,19 9,19 9,19 9,20 9,20 9,20 8,21 8,21 8,21 8,22 8,22 8,22 7,22 7,23 7,23 7,23 " stroke-width="0.323101"/>
    <polyline arcFlag="1" points="7,23 7,23 7,23 7,23 8,23 8,23 8,24 8,24 9,24 9,24 9,25 9,25 9,25 9,26 9,26 9,27 9,27 9,27 9,28 8,28 8,28 8,28 8,29 7,29 7,29 7,29 " stroke-width="0.323101"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.35042" x1="7" x2="7" y1="46" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
   </symbol>
   <symbol id="voltageTransformer:shape117">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="9" y2="24"/>
    <circle cx="59" cy="16" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="22" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="61" x2="62" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="58" x2="57" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="57" x2="62" y1="14" y2="14"/>
    <circle cx="48" cy="9" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="22" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="23" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="23" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="52" x2="48" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="48" x2="44" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="48" x2="48" y1="10" y2="5"/>
    <circle cx="35" cy="9" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="24" y2="24"/>
   </symbol>
   <symbol id="voltageTransformer:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="64" x2="64" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="61" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="57" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="9" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="45" y1="9" y2="9"/>
    <circle cx="31" cy="46" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="38" cy="40" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="31" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="23" x2="23" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.503497" x1="39" x2="22" y1="10" y2="10"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d7afa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26a3530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d7cf40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d7da90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d7ecc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d7f960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d80500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d80f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d7c0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d7c0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d842b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d842b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d861b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d861b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2d871c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d88e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d89aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d8a980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d8b260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d8ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d8d720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d8dfe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d8e7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d8f880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d90200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d90cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d916b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d92b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d936d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d94700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d95340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2da3b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d969c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2d97b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2d99140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1478" width="1963" x="-691" y="-1482"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-443" y="-1121"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-314416">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 257.241796 -931.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48587" ObjectName="SW-DY_TSB.DY_TSB_3011SW"/>
     <cge:Meas_Ref ObjectId="314416"/>
    <cge:TPSR_Ref TObjectID="48587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 417.761290 -1082.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48581" ObjectName="SW-DY_TSB.DY_TSB_3901SW"/>
     <cge:Meas_Ref ObjectId="314384"/>
    <cge:TPSR_Ref TObjectID="48581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314529">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 701.000000 -1039.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48596" ObjectName="SW-DY_TSB.DY_TSB_3601SW"/>
     <cge:Meas_Ref ObjectId="314529"/>
    <cge:TPSR_Ref TObjectID="48596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314320">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 94.000000 -1047.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48576" ObjectName="SW-DY_TSB.DY_TSB_3511SW"/>
     <cge:Meas_Ref ObjectId="314320"/>
    <cge:TPSR_Ref TObjectID="48576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314321">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 94.000000 -1206.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48577" ObjectName="SW-DY_TSB.DY_TSB_3516SW"/>
     <cge:Meas_Ref ObjectId="314321"/>
    <cge:TPSR_Ref TObjectID="48577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314602">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.761290 -247.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48612" ObjectName="SW-DY_TSB.DY_TSB_0636SW"/>
     <cge:Meas_Ref ObjectId="314602"/>
    <cge:TPSR_Ref TObjectID="48612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 349.000000 -1043.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48582" ObjectName="SW-DY_TSB.DY_TSB_39010SW"/>
     <cge:Meas_Ref ObjectId="314385"/>
    <cge:TPSR_Ref TObjectID="48582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314386">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 455.000000 -1147.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48583" ObjectName="SW-DY_TSB.DY_TSB_39017SW"/>
     <cge:Meas_Ref ObjectId="314386"/>
    <cge:TPSR_Ref TObjectID="48583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314323">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 21.000000 -1183.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48579" ObjectName="SW-DY_TSB.DY_TSB_35160SW"/>
     <cge:Meas_Ref ObjectId="314323"/>
    <cge:TPSR_Ref TObjectID="48579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314324">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 25.000000 -1108.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48580" ObjectName="SW-DY_TSB.DY_TSB_35117SW"/>
     <cge:Meas_Ref ObjectId="314324"/>
    <cge:TPSR_Ref TObjectID="48580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314322">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 24.000000 -1265.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48578" ObjectName="SW-DY_TSB.DY_TSB_35167SW"/>
     <cge:Meas_Ref ObjectId="314322"/>
    <cge:TPSR_Ref TObjectID="48578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314530">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 729.000000 -1110.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48597" ObjectName="SW-DY_TSB.DY_TSB_36017SW"/>
     <cge:Meas_Ref ObjectId="314530"/>
    <cge:TPSR_Ref TObjectID="48597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314409">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.000000 -1040.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48584" ObjectName="SW-DY_TSB.DY_TSB_3121SW"/>
     <cge:Meas_Ref ObjectId="314409"/>
    <cge:TPSR_Ref TObjectID="48584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314410">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 834.000000 -1099.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48585" ObjectName="SW-DY_TSB.DY_TSB_31217SW"/>
     <cge:Meas_Ref ObjectId="314410"/>
    <cge:TPSR_Ref TObjectID="48585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314417">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 189.000000 -905.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48588" ObjectName="SW-DY_TSB.DY_TSB_30117SW"/>
     <cge:Meas_Ref ObjectId="314417"/>
    <cge:TPSR_Ref TObjectID="48588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 256.000000 -599.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48591" ObjectName="SW-DY_TSB.DY_TSB_001XC1"/>
     <cge:Meas_Ref ObjectId="314431"/>
    <cge:TPSR_Ref TObjectID="48591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 256.000000 -522.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48590" ObjectName="SW-DY_TSB.DY_TSB_001XC"/>
     <cge:Meas_Ref ObjectId="314431"/>
    <cge:TPSR_Ref TObjectID="48590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314535">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -139.000000 -437.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48599" ObjectName="SW-DY_TSB.DY_TSB_061XC"/>
     <cge:Meas_Ref ObjectId="314535"/>
    <cge:TPSR_Ref TObjectID="48599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314535">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -139.000000 -360.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48600" ObjectName="SW-DY_TSB.DY_TSB_061XC1"/>
     <cge:Meas_Ref ObjectId="314535"/>
    <cge:TPSR_Ref TObjectID="48600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314536">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -203.000000 -340.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48601" ObjectName="SW-DY_TSB.DY_TSB_06167SW"/>
     <cge:Meas_Ref ObjectId="314536"/>
    <cge:TPSR_Ref TObjectID="48601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314600">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.000000 -440.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48609" ObjectName="SW-DY_TSB.DY_TSB_063XC"/>
     <cge:Meas_Ref ObjectId="314600"/>
    <cge:TPSR_Ref TObjectID="48609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314600">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.000000 -363.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48610" ObjectName="SW-DY_TSB.DY_TSB_063XC1"/>
     <cge:Meas_Ref ObjectId="314600"/>
    <cge:TPSR_Ref TObjectID="48610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314601">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 292.000000 -305.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48611" ObjectName="SW-DY_TSB.DY_TSB_06360SW"/>
     <cge:Meas_Ref ObjectId="314601"/>
    <cge:TPSR_Ref TObjectID="48611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314603">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 292.000000 -186.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48613" ObjectName="SW-DY_TSB.DY_TSB_06367SW"/>
     <cge:Meas_Ref ObjectId="314603"/>
    <cge:TPSR_Ref TObjectID="48613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314566">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 96.000000 -439.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48603" ObjectName="SW-DY_TSB.DY_TSB_062XC"/>
     <cge:Meas_Ref ObjectId="314566"/>
    <cge:TPSR_Ref TObjectID="48603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314566">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 96.000000 -362.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48604" ObjectName="SW-DY_TSB.DY_TSB_062XC1"/>
     <cge:Meas_Ref ObjectId="314566"/>
    <cge:TPSR_Ref TObjectID="48604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314567">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 62.000000 -304.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48605" ObjectName="SW-DY_TSB.DY_TSB_06260SW"/>
     <cge:Meas_Ref ObjectId="314567"/>
    <cge:TPSR_Ref TObjectID="48605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314568">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 97.000000 -205.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48606" ObjectName="SW-DY_TSB.DY_TSB_0626SW"/>
     <cge:Meas_Ref ObjectId="314568"/>
    <cge:TPSR_Ref TObjectID="48606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314569">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 44.000000 -208.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48607" ObjectName="SW-DY_TSB.DY_TSB_06267SW"/>
     <cge:Meas_Ref ObjectId="314569"/>
    <cge:TPSR_Ref TObjectID="48607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315141">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48650" ObjectName="SW-DY_TSB.DY_TSB_0030SW"/>
     <cge:Meas_Ref ObjectId="315141"/>
    <cge:TPSR_Ref TObjectID="48650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314774">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 520.761290 -245.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48630" ObjectName="SW-DY_TSB.DY_TSB_0646SW"/>
     <cge:Meas_Ref ObjectId="314774"/>
    <cge:TPSR_Ref TObjectID="48630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 520.000000 -438.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48627" ObjectName="SW-DY_TSB.DY_TSB_064XC"/>
     <cge:Meas_Ref ObjectId="314772"/>
    <cge:TPSR_Ref TObjectID="48627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 520.000000 -361.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48628" ObjectName="SW-DY_TSB.DY_TSB_064XC1"/>
     <cge:Meas_Ref ObjectId="314772"/>
    <cge:TPSR_Ref TObjectID="48628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 486.000000 -303.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48629" ObjectName="SW-DY_TSB.DY_TSB_06460SW"/>
     <cge:Meas_Ref ObjectId="314773"/>
    <cge:TPSR_Ref TObjectID="48629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314775">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 486.000000 -184.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48631" ObjectName="SW-DY_TSB.DY_TSB_06467SW"/>
     <cge:Meas_Ref ObjectId="314775"/>
    <cge:TPSR_Ref TObjectID="48631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314717">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.761290 -247.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48624" ObjectName="SW-DY_TSB.DY_TSB_0656SW"/>
     <cge:Meas_Ref ObjectId="314717"/>
    <cge:TPSR_Ref TObjectID="48624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314715">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -440.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48621" ObjectName="SW-DY_TSB.DY_TSB_065XC"/>
     <cge:Meas_Ref ObjectId="314715"/>
    <cge:TPSR_Ref TObjectID="48621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314715">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -363.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48622" ObjectName="SW-DY_TSB.DY_TSB_065XC1"/>
     <cge:Meas_Ref ObjectId="314715"/>
    <cge:TPSR_Ref TObjectID="48622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314716">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -305.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48623" ObjectName="SW-DY_TSB.DY_TSB_06560SW"/>
     <cge:Meas_Ref ObjectId="314716"/>
    <cge:TPSR_Ref TObjectID="48623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314718">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -186.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48625" ObjectName="SW-DY_TSB.DY_TSB_06567SW"/>
     <cge:Meas_Ref ObjectId="314718"/>
    <cge:TPSR_Ref TObjectID="48625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314659">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.761290 -247.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48618" ObjectName="SW-DY_TSB.DY_TSB_0666SW"/>
     <cge:Meas_Ref ObjectId="314659"/>
    <cge:TPSR_Ref TObjectID="48618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314657">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 -440.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48615" ObjectName="SW-DY_TSB.DY_TSB_066XC"/>
     <cge:Meas_Ref ObjectId="314657"/>
    <cge:TPSR_Ref TObjectID="48615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314657">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 -363.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48616" ObjectName="SW-DY_TSB.DY_TSB_066XC1"/>
     <cge:Meas_Ref ObjectId="314657"/>
    <cge:TPSR_Ref TObjectID="48616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314658">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 873.000000 -305.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48617" ObjectName="SW-DY_TSB.DY_TSB_06660SW"/>
     <cge:Meas_Ref ObjectId="314658"/>
    <cge:TPSR_Ref TObjectID="48617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314660">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 873.000000 -186.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48619" ObjectName="SW-DY_TSB.DY_TSB_06667SW"/>
     <cge:Meas_Ref ObjectId="314660"/>
    <cge:TPSR_Ref TObjectID="48619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314521">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 48.000000 -569.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48593" ObjectName="SW-DY_TSB.DY_TSB_0901XC1"/>
     <cge:Meas_Ref ObjectId="314521"/>
    <cge:TPSR_Ref TObjectID="48593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314521">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 48.000000 -502.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48592" ObjectName="SW-DY_TSB.DY_TSB_0901XC"/>
     <cge:Meas_Ref ObjectId="314521"/>
    <cge:TPSR_Ref TObjectID="48592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314525">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1026.000000 -566.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48595" ObjectName="SW-DY_TSB.DY_TSB_0121XC1"/>
     <cge:Meas_Ref ObjectId="314525"/>
    <cge:TPSR_Ref TObjectID="48595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314525">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1026.000000 -503.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48594" ObjectName="SW-DY_TSB.DY_TSB_0121XC"/>
     <cge:Meas_Ref ObjectId="314525"/>
    <cge:TPSR_Ref TObjectID="48594"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_TSB.DY_TSB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-88,-1008 971,-1008 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48640" ObjectName="BS-DY_TSB.DY_TSB_3IM"/>
    <cge:TPSR_Ref TObjectID="48640"/></metadata>
   <polyline fill="none" opacity="0" points="-88,-1008 971,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_TSB.DY_TSB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-264,-487 1101,-487 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48641" ObjectName="BS-DY_TSB.DY_TSB_9IM"/>
    <cge:TPSR_Ref TObjectID="48641"/></metadata>
   <polyline fill="none" opacity="0" points="-264,-487 1101,-487 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-DY_TSB.DY_ST_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 81.000000 -78.000000)" xlink:href="#capacitor:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48647" ObjectName="CB-DY_TSB.DY_ST_Cb1"/>
    <cge:TPSR_Ref TObjectID="48647"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-DY_TSB.DY_TSB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="47857"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 227.000000 -724.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 227.000000 -724.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="48632" ObjectName="TF-DY_TSB.DY_TSB_1T"/>
    <cge:TPSR_Ref TObjectID="48632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 695.000000 -1226.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 695.000000 -1226.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_269c650">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 261.000000 -634.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_266aa00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 180.000000 -695.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_255cda0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 -1189.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26453a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 701.000000 -1144.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_251cca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 178.000000 -735.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26aad30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.000000 -302.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26ab7f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 51.000000 -645.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2686df0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 40.000000 -1320.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2652c50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -107.000000 -292.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_254fb10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 370.000000 -302.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25f0a50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 370.000000 -183.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_263d0f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 96.000000 -301.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2576d80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 140.000000 -301.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a5bd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -146.000000 -226.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a6ad0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.603774 -197.000000 -86.000000)" xlink:href="#lightningRod:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_262ec60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -197.000000 -130.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e8c00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 520.000000 -300.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2621670">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 564.000000 -300.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_258b5b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 564.000000 -181.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c6710">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.000000 -302.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d0280">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 758.000000 -302.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b8910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 758.000000 -183.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257a180">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 -302.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2583ff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.000000 -302.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2523970">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.000000 -183.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_252e700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 -626.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -655.000000 -1176.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -523.000000 -958.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -523.000000 -914.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -524.000000 -1040.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -523.000000 -999.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-314896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -66.000000 -1111.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48640"/>
     <cge:Term_Ref ObjectID="47859"/>
    <cge:TPSR_Ref TObjectID="48640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-314897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -66.000000 -1111.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48640"/>
     <cge:Term_Ref ObjectID="47859"/>
    <cge:TPSR_Ref TObjectID="48640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-314898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -66.000000 -1111.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48640"/>
     <cge:Term_Ref ObjectID="47859"/>
    <cge:TPSR_Ref TObjectID="48640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-314902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -66.000000 -1111.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48640"/>
     <cge:Term_Ref ObjectID="47859"/>
    <cge:TPSR_Ref TObjectID="48640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-314899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -66.000000 -1111.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48640"/>
     <cge:Term_Ref ObjectID="47859"/>
    <cge:TPSR_Ref TObjectID="48640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-314928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -201.000000 -587.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48641"/>
     <cge:Term_Ref ObjectID="47860"/>
    <cge:TPSR_Ref TObjectID="48641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-314929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -201.000000 -587.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48641"/>
     <cge:Term_Ref ObjectID="47860"/>
    <cge:TPSR_Ref TObjectID="48641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-314930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -201.000000 -587.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48641"/>
     <cge:Term_Ref ObjectID="47860"/>
    <cge:TPSR_Ref TObjectID="48641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-314934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -201.000000 -587.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48641"/>
     <cge:Term_Ref ObjectID="47860"/>
    <cge:TPSR_Ref TObjectID="48641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-314931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -201.000000 -587.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48641"/>
     <cge:Term_Ref ObjectID="47860"/>
    <cge:TPSR_Ref TObjectID="48641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-314924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 240.000000 -1146.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48575"/>
     <cge:Term_Ref ObjectID="47741"/>
    <cge:TPSR_Ref TObjectID="48575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-314925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 240.000000 -1146.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48575"/>
     <cge:Term_Ref ObjectID="47741"/>
    <cge:TPSR_Ref TObjectID="48575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-314921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 240.000000 -1146.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48575"/>
     <cge:Term_Ref ObjectID="47741"/>
    <cge:TPSR_Ref TObjectID="48575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-314907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -956.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48586"/>
     <cge:Term_Ref ObjectID="47763"/>
    <cge:TPSR_Ref TObjectID="48586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-314908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -956.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48586"/>
     <cge:Term_Ref ObjectID="47763"/>
    <cge:TPSR_Ref TObjectID="48586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-314904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -956.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48586"/>
     <cge:Term_Ref ObjectID="47763"/>
    <cge:TPSR_Ref TObjectID="48586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-314914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.000000 -574.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48589"/>
     <cge:Term_Ref ObjectID="47769"/>
    <cge:TPSR_Ref TObjectID="48589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-314915" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.000000 -574.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48589"/>
     <cge:Term_Ref ObjectID="47769"/>
    <cge:TPSR_Ref TObjectID="48589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-314911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 412.000000 -574.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48589"/>
     <cge:Term_Ref ObjectID="47769"/>
    <cge:TPSR_Ref TObjectID="48589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-314939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -193.000000 -68.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314939" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48598"/>
     <cge:Term_Ref ObjectID="47787"/>
    <cge:TPSR_Ref TObjectID="48598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-314940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -193.000000 -68.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48598"/>
     <cge:Term_Ref ObjectID="47787"/>
    <cge:TPSR_Ref TObjectID="48598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-314936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -193.000000 -68.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48598"/>
     <cge:Term_Ref ObjectID="47787"/>
    <cge:TPSR_Ref TObjectID="48598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-314946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 49.000000 -63.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48602"/>
     <cge:Term_Ref ObjectID="47795"/>
    <cge:TPSR_Ref TObjectID="48602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-314947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 49.000000 -63.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48602"/>
     <cge:Term_Ref ObjectID="47795"/>
    <cge:TPSR_Ref TObjectID="48602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-314943" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 49.000000 -63.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48602"/>
     <cge:Term_Ref ObjectID="47795"/>
    <cge:TPSR_Ref TObjectID="48602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-314953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -63.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48608"/>
     <cge:Term_Ref ObjectID="47807"/>
    <cge:TPSR_Ref TObjectID="48608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-314954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -63.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48608"/>
     <cge:Term_Ref ObjectID="47807"/>
    <cge:TPSR_Ref TObjectID="48608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-314950" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -63.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314950" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48608"/>
     <cge:Term_Ref ObjectID="47807"/>
    <cge:TPSR_Ref TObjectID="48608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-314974" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 -63.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314974" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48626"/>
     <cge:Term_Ref ObjectID="47843"/>
    <cge:TPSR_Ref TObjectID="48626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-314975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 -63.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48626"/>
     <cge:Term_Ref ObjectID="47843"/>
    <cge:TPSR_Ref TObjectID="48626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-314971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 -63.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48626"/>
     <cge:Term_Ref ObjectID="47843"/>
    <cge:TPSR_Ref TObjectID="48626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-314967" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -63.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48620"/>
     <cge:Term_Ref ObjectID="47831"/>
    <cge:TPSR_Ref TObjectID="48620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-314968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -63.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48620"/>
     <cge:Term_Ref ObjectID="47831"/>
    <cge:TPSR_Ref TObjectID="48620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-314964" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.000000 -63.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314964" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48620"/>
     <cge:Term_Ref ObjectID="47831"/>
    <cge:TPSR_Ref TObjectID="48620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-314960" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.000000 -63.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314960" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48614"/>
     <cge:Term_Ref ObjectID="47819"/>
    <cge:TPSR_Ref TObjectID="48614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-314961" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.000000 -63.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314961" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48614"/>
     <cge:Term_Ref ObjectID="47819"/>
    <cge:TPSR_Ref TObjectID="48614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-314957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.000000 -63.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48614"/>
     <cge:Term_Ref ObjectID="47819"/>
    <cge:TPSR_Ref TObjectID="48614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-314918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 386.000000 -734.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48632"/>
     <cge:Term_Ref ObjectID="47858"/>
    <cge:TPSR_Ref TObjectID="48632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-314919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 386.000000 -734.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="314919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48632"/>
     <cge:Term_Ref ObjectID="47858"/>
    <cge:TPSR_Ref TObjectID="48632"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-420" y="-1235"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-420" y="-1235"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-420" y="-1270"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-420" y="-1270"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-443" y="-1122"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-443" y="-1122"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-575" y="-1255"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-575" y="-1255"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-624" y="-1272"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-624" y="-1272"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="113" y="-1162"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="113" y="-1162"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="306" y="-824"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="306" y="-824"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-120" y="-423"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-120" y="-423"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="116" y="-425"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="116" y="-425"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="346" y="-426"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="346" y="-426"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="540" y="-424"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="540" y="-424"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="734" y="-426"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="734" y="-426"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="927" y="-426"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="927" y="-426"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="57" x="-621" y="-759"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="57" x="-621" y="-759"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-420" y="-1235"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-420" y="-1270"/></g>
   <g href="AVC铁锁站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-443" y="-1122"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-575" y="-1255"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-624" y="-1272"/></g>
   <g href="35kV铁锁变35kV三铁线351开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="113" y="-1162"/></g>
   <g href="35kV铁锁变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="306" y="-824"/></g>
   <g href="35kV铁锁变10kV1号接地变061开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-120" y="-423"/></g>
   <g href="35kV铁锁变10kV1号电容器组062开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="116" y="-425"/></g>
   <g href="35kV铁锁变10kV永河线063开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="346" y="-426"/></g>
   <g href="35kV铁锁变10kV备用一064开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="540" y="-424"/></g>
   <g href="35kV铁锁变10kV金沙线065开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="734" y="-426"/></g>
   <g href="35kV铁锁变10kV自碑么线066开关间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="927" y="-426"/></g>
   <g href="35kV铁锁变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="57" x="-621" y="-759"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-314415">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 257.241796 -851.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48586" ObjectName="SW-DY_TSB.DY_TSB_301BK"/>
     <cge:Meas_Ref ObjectId="314415"/>
    <cge:TPSR_Ref TObjectID="48586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314430">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 257.241796 -556.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48589" ObjectName="SW-DY_TSB.DY_TSB_001BK"/>
     <cge:Meas_Ref ObjectId="314430"/>
    <cge:TPSR_Ref TObjectID="48589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314319">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 94.000000 -1133.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48575" ObjectName="SW-DY_TSB.DY_TSB_351BK"/>
     <cge:Meas_Ref ObjectId="314319"/>
    <cge:TPSR_Ref TObjectID="48575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314534">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -138.758204 -394.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48598" ObjectName="SW-DY_TSB.DY_TSB_061BK"/>
     <cge:Meas_Ref ObjectId="314534"/>
    <cge:TPSR_Ref TObjectID="48598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314599">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.241796 -397.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48608" ObjectName="SW-DY_TSB.DY_TSB_063BK"/>
     <cge:Meas_Ref ObjectId="314599"/>
    <cge:TPSR_Ref TObjectID="48608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314565">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 97.241796 -396.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48602" ObjectName="SW-DY_TSB.DY_TSB_062BK"/>
     <cge:Meas_Ref ObjectId="314565"/>
    <cge:TPSR_Ref TObjectID="48602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 521.241796 -395.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48626" ObjectName="SW-DY_TSB.DY_TSB_064BK"/>
     <cge:Meas_Ref ObjectId="314771"/>
    <cge:TPSR_Ref TObjectID="48626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314714">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 715.241796 -397.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48620" ObjectName="SW-DY_TSB.DY_TSB_065BK"/>
     <cge:Meas_Ref ObjectId="314714"/>
    <cge:TPSR_Ref TObjectID="48620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-314656">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 908.241796 -397.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48614" ObjectName="SW-DY_TSB.DY_TSB_066BK"/>
     <cge:Meas_Ref ObjectId="314656"/>
    <cge:TPSR_Ref TObjectID="48614"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ST" endPointId="0" endStationName="DY_TSB" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_SanTie" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="105,-1403 105,-1475 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48652" ObjectName="AC-35kV.LN_SanTie"/>
    <cge:TPSR_Ref TObjectID="48652_SS-339"/></metadata>
   <polyline fill="none" opacity="0" points="105,-1403 105,-1475 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_25a2fe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 71.000000 -253.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a3a70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 47.000000 -244.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="48640" cx="427" cy="-1008" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48640" cx="710" cy="-1008" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48640" cx="902" cy="-1008" fill="rgb(255,255,0)" lineStyle="1" r="4" stroke="rgb(255,255,0)" stroke-dasharray="10 5 " stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48640" cx="266" cy="-1008" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48641" cx="266" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48641" cx="336" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48641" cx="106" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48641" cx="-129" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48641" cx="530" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48641" cx="724" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48641" cx="917" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48641" cx="58" cy="-487" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48640" cx="103" cy="-1008" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b65090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b65090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b65090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b65090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b65090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b65090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b65090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b65090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1b65090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -1043.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26e10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -639.000000 -605.000000) translate(0,353)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26503a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -86.000000 -994.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f92e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -276.000000 -478.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2319bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.000000 -803.000000) translate(0,12)">SZ20-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2319bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.000000 -803.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2319bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.000000 -803.000000) translate(0,42)">Ynd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2319bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.000000 -803.000000) translate(0,57)">Uk=7.66%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_264c270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -409.000000 -1227.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_261b5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -409.000000 -1262.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d76b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -621.000000 -759.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2643e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -686.000000 -221.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26a5600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 350.000000 -1301.000000) translate(0,12)">35kVⅠ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26090a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 678.000000 -1352.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26abe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -162.000000 -174.000000) translate(0,12)">10kV1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ac190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 295.000000 -125.000000) translate(0,12)">10kV永河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_26aca10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -426.500000 -1110.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e9a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -123.000000) translate(0,12)">10kV备用一线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c7570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 683.000000 -125.000000) translate(0,12)">10kV金沙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_257afc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -125.000000) translate(0,12)">10kV自碑么线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252e110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -19.000000 -781.000000) translate(0,12)">10kVⅠ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2514740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -529.000000 -1244.500000) translate(0,16)">铁锁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25153d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 113.000000 -1162.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2515830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 111.000000 -1077.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2515a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 34.000000 -1141.000000) translate(0,12)">35117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2515cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 29.000000 -1216.000000) translate(0,12)">35160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2515ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 111.000000 -1236.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2516130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 32.000000 -1298.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2516370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 -1112.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25165b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 357.000000 -1076.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25167f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 463.000000 -1180.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2516a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 717.000000 -1069.000000) translate(0,12)">3601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2516c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 737.000000 -1143.000000) translate(0,12)">36017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2516eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 909.000000 -1070.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25170f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 842.000000 -1132.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2517330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 276.000000 -880.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2517570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 273.000000 -961.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25177b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 197.000000 -938.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25179f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 306.000000 -824.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2517e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 276.000000 -585.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2518090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -120.000000 -423.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25182d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -195.000000 -373.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2518510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 116.000000 -425.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2518750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 12.000000 -346.000000) translate(0,12)">06260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2518990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 113.000000 -235.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2518bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -14.000000 -245.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2518e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 346.000000 -426.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2519050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 249.000000 -340.000000) translate(0,12)">06360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2519290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -277.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25194d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 251.000000 -221.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2519710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 540.000000 -424.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2519af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -340.000000) translate(0,12)">06460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2519f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 440.000000 -221.000000) translate(0,12)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251a160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 537.000000 -275.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251a3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 734.000000 -426.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251a5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 629.000000 -345.000000) translate(0,12)">06560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25342e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 637.000000 -220.000000) translate(0,12)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2534520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 731.000000 -277.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2534760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -426.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25349a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.000000 -341.000000) translate(0,12)">06660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2534be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 828.000000 -222.000000) translate(0,12)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2534e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 924.000000 -277.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2535060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1053.000000 -552.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25352a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 78.000000 -547.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2539d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 141.000000 -117.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_253c480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -183.000000 -210.000000) translate(0,12)">0030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_253cc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 114.000000 -1464.000000) translate(0,12)">三铁线</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21f3d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 336.000000 734.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24841c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 306.500000 712.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_255d660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -125.000000 1109.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26b46b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -125.000000 1090.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26b4820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -133.000000 1039.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26b4990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -125.000000 1073.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26b4b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -119.000000 1056.000000) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26b4d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 351.000000 956.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26b4f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 339.500000 937.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26b50c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 365.000000 919.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26b5230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -264.000000 586.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264f590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -264.000000 567.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264f700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -272.000000 516.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264f870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -264.000000 550.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264f9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -258.000000 533.000000) translate(0,12)">U0(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2608550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 351.000000 575.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2608740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 339.500000 556.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26088f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 365.000000 538.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2608b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -253.000000 69.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2608d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -264.500000 50.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2608ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -239.000000 32.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ac3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 178.000000 1146.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ac5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 166.500000 1127.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ac780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 192.000000 1109.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_TSB.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.761290 -131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48644" ObjectName="EC-DY_TSB.063Ld"/>
    <cge:TPSR_Ref TObjectID="48644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_TSB.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 520.761290 -129.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48654" ObjectName="EC-DY_TSB.064Ld"/>
    <cge:TPSR_Ref TObjectID="48654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_TSB.065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 714.761290 -131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48646" ObjectName="EC-DY_TSB.065Ld"/>
    <cge:TPSR_Ref TObjectID="48646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_TSB.066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.761290 -131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48645" ObjectName="EC-DY_TSB.066Ld"/>
    <cge:TPSR_Ref TObjectID="48645"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-311533" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -341.500000 -1153.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48339" ObjectName="DYN-DY_TSB"/>
     <cge:Meas_Ref ObjectId="311533"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_26acc00">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 461.500000 -1270.500000)" xlink:href="#voltageTransformer:shape117"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26879e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 131.000000 -1379.000000)" xlink:href="#voltageTransformer:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_252f3f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 32.000000 -764.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2684320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="734,-1117 712,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="48597@0" ObjectIDZND0="g_26453a0@0" ObjectIDZND1="48596@x" Pin0InfoVect0LinkObjId="g_26453a0_0" Pin0InfoVect1LinkObjId="SW-314529_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="734,-1117 712,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_251c070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-702 266,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_266aa00@0" ObjectIDZND0="g_269c650@0" ObjectIDZND1="48632@x" Pin0InfoVect0LinkObjId="g_269c650_0" Pin0InfoVect1LinkObjId="g_251cab0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_266aa00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="237,-702 266,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_251c8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-692 266,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_269c650@0" ObjectIDZND0="g_266aa00@0" ObjectIDZND1="48632@x" Pin0InfoVect0LinkObjId="g_266aa00_0" Pin0InfoVect1LinkObjId="g_251cab0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_269c650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="266,-692 266,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_251cab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-702 266,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_266aa00@0" ObjectIDND1="g_269c650@0" ObjectIDZND0="48632@0" Pin0InfoVect0LinkObjId="g_2608270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_266aa00_0" Pin1InfoVect1LinkObjId="g_269c650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="266,-702 266,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2608270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="185,-789 185,-800 265,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_251cca0@0" ObjectIDZND0="48632@x" Pin0InfoVect0LinkObjId="g_251cab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_251cca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="185,-789 185,-800 265,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26ab600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-288 336,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48612@1" ObjectIDZND0="g_26aad30@0" Pin0InfoVect0LinkObjId="g_26aad30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314602_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-288 336,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2661060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-1193 382,-1184 429,-1184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_255cda0@0" ObjectIDZND0="g_26acc00@0" ObjectIDZND1="48583@x" ObjectIDZND2="48581@x" Pin0InfoVect0LinkObjId="g_26acc00_0" Pin0InfoVect1LinkObjId="SW-314386_0" Pin0InfoVect2LinkObjId="SW-314384_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_255cda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="382,-1193 382,-1184 429,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26618b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="427,-1237 427,-1184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_26acc00@0" ObjectIDZND0="g_255cda0@0" ObjectIDZND1="48583@x" ObjectIDZND2="48581@x" Pin0InfoVect0LinkObjId="g_255cda0_0" Pin0InfoVect1LinkObjId="SW-314386_0" Pin0InfoVect2LinkObjId="SW-314384_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26acc00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="427,-1237 427,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262a420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="400,-1050 427,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="48582@0" ObjectIDZND0="48640@0" ObjectIDZND1="48581@x" Pin0InfoVect0LinkObjId="g_25c37b0_0" Pin0InfoVect1LinkObjId="SW-314384_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="400,-1050 427,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262ad90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="427,-1008 427,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48640@0" ObjectIDZND0="48582@x" ObjectIDZND1="48581@x" Pin0InfoVect0LinkObjId="SW-314385_0" Pin0InfoVect1LinkObjId="SW-314384_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262a420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="427,-1008 427,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262afb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="427,-1050 427,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="48582@x" ObjectIDND1="48640@0" ObjectIDZND0="48581@0" Pin0InfoVect0LinkObjId="SW-314384_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-314385_0" Pin1InfoVect1LinkObjId="g_262a420_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="427,-1050 427,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25fae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="460,-1154 427,-1154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48583@0" ObjectIDZND0="48581@x" ObjectIDZND1="g_255cda0@0" ObjectIDZND2="g_26acc00@0" Pin0InfoVect0LinkObjId="SW-314384_0" Pin0InfoVect1LinkObjId="g_255cda0_0" Pin0InfoVect2LinkObjId="g_26acc00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="460,-1154 427,-1154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25fb780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="427,-1123 427,-1154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48581@1" ObjectIDZND0="48583@x" ObjectIDZND1="g_255cda0@0" ObjectIDZND2="g_26acc00@0" Pin0InfoVect0LinkObjId="SW-314386_0" Pin0InfoVect1LinkObjId="g_255cda0_0" Pin0InfoVect2LinkObjId="g_26acc00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="427,-1123 427,-1154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25fb9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="427,-1154 427,-1184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="48583@x" ObjectIDND1="48581@x" ObjectIDZND0="g_255cda0@0" ObjectIDZND1="g_26acc00@0" Pin0InfoVect0LinkObjId="g_255cda0_0" Pin0InfoVect1LinkObjId="g_26acc00_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-314386_0" Pin1InfoVect1LinkObjId="SW-314384_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="427,-1154 427,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c28e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="710,-1231 710,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_26453a0@0" Pin0InfoVect0LinkObjId="g_26453a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="710,-1231 710,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c3370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="710,-1149 710,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_26453a0@1" ObjectIDZND0="48597@x" ObjectIDZND1="48596@x" Pin0InfoVect0LinkObjId="SW-314530_0" Pin0InfoVect1LinkObjId="SW-314529_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26453a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="710,-1149 710,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c3590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="710,-1080 710,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="48596@1" ObjectIDZND0="48597@x" ObjectIDZND1="g_26453a0@0" Pin0InfoVect0LinkObjId="SW-314530_0" Pin0InfoVect1LinkObjId="g_26453a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314529_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="710,-1080 710,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c37b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="710,-1044 710,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48596@0" ObjectIDZND0="48640@0" Pin0InfoVect0LinkObjId="g_262a420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314529_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="710,-1044 710,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2640240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-1045 902,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48584@0" ObjectIDZND0="48640@0" Pin0InfoVect0LinkObjId="g_262a420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314409_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-1045 902,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2640a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="902,-1128 1132,-1128 1132,-1008 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="48585@x" ObjectIDND1="48584@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-314410_0" Pin1InfoVect1LinkObjId="SW-314409_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="902,-1128 1132,-1128 1132,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2636a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="885,-1106 902,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48585@0" ObjectIDZND0="48584@x" Pin0InfoVect0LinkObjId="SW-314409_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="885,-1106 902,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26374c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-1081 902,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48584@1" ObjectIDZND0="48585@x" Pin0InfoVect0LinkObjId="SW-314410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314409_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-1081 902,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26376e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-1106 902,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="48585@x" ObjectIDND1="48584@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-314410_0" Pin1InfoVect1LinkObjId="SW-314409_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="902,-1106 902,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2637900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-821 266,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="48632@1" ObjectIDZND0="48586@0" Pin0InfoVect0LinkObjId="SW-314415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_251cab0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="266,-821 266,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2638850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="240,-912 266,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="48588@0" ObjectIDZND0="48586@x" ObjectIDZND1="48587@x" Pin0InfoVect0LinkObjId="SW-314415_0" Pin0InfoVect1LinkObjId="SW-314416_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314417_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="240,-912 266,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2639320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-886 266,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48586@1" ObjectIDZND0="48588@x" ObjectIDZND1="48587@x" Pin0InfoVect0LinkObjId="SW-314417_0" Pin0InfoVect1LinkObjId="SW-314416_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="266,-886 266,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2639580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-912 266,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="48588@x" ObjectIDND1="48586@x" ObjectIDZND0="48587@0" Pin0InfoVect0LinkObjId="SW-314416_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-314417_0" Pin1InfoVect1LinkObjId="SW-314415_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="266,-912 266,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2639fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-591 266,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48589@1" ObjectIDZND0="48591@1" Pin0InfoVect0LinkObjId="SW-314431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="266,-591 266,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2593e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-623 266,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48591@0" ObjectIDZND0="g_269c650@1" Pin0InfoVect0LinkObjId="g_269c650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="266,-623 266,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2594070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-546 266,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48590@1" ObjectIDZND0="48589@0" Pin0InfoVect0LinkObjId="SW-314430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314431_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="266,-546 266,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25942d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-972 266,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48587@1" ObjectIDZND0="48640@0" Pin0InfoVect0LinkObjId="g_262a420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="266,-972 266,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2594ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="266,-529 266,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48590@0" ObjectIDZND0="48641@0" Pin0InfoVect0LinkObjId="g_263ce90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="266,-529 266,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2652790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-129,-384 -129,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48600@1" ObjectIDZND0="48598@0" Pin0InfoVect0LinkObjId="SW-314534_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314535_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-129,-384 -129,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26529f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-129,-429 -129,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48598@1" ObjectIDZND0="48599@1" Pin0InfoVect0LinkObjId="SW-314535_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314534_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-129,-429 -129,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d4230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-152,-347 -129,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48601@0" ObjectIDZND0="48600@x" ObjectIDZND1="g_2652c50@0" ObjectIDZND2="g_25a5bd0@0" Pin0InfoVect0LinkObjId="SW-314535_0" Pin0InfoVect1LinkObjId="g_2652c50_0" Pin0InfoVect2LinkObjId="g_25a5bd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314536_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-152,-347 -129,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d4f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-129,-259 -129,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_25a5bd0@0" ObjectIDZND0="48601@x" ObjectIDZND1="48600@x" ObjectIDZND2="g_2652c50@0" Pin0InfoVect0LinkObjId="SW-314536_0" Pin0InfoVect1LinkObjId="SW-314535_0" Pin0InfoVect2LinkObjId="g_2652c50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25a5bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-129,-259 -129,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d51a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-129,-347 -129,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="48601@x" ObjectIDND1="g_2652c50@0" ObjectIDND2="g_25a5bd0@0" ObjectIDZND0="48600@0" Pin0InfoVect0LinkObjId="SW-314535_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-314536_0" Pin1InfoVect1LinkObjId="g_2652c50_0" Pin1InfoVect2LinkObjId="g_25a5bd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-129,-347 -129,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d5400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-100,-346 -129,-346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2652c50@0" ObjectIDZND0="48601@x" ObjectIDZND1="48600@x" ObjectIDZND2="g_25a5bd0@0" Pin0InfoVect0LinkObjId="SW-314536_0" Pin0InfoVect1LinkObjId="SW-314535_0" Pin0InfoVect2LinkObjId="g_25a5bd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2652c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-100,-346 -129,-346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_254f660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-387 336,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48610@1" ObjectIDZND0="48608@0" Pin0InfoVect0LinkObjId="SW-314599_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314600_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-387 336,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_254f8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-432 336,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48608@1" ObjectIDZND0="48609@1" Pin0InfoVect0LinkObjId="SW-314600_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314599_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-432 336,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25ef620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="301,-356 336,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48611@0" ObjectIDZND0="g_26aad30@0" ObjectIDZND1="48610@x" ObjectIDZND2="g_254fb10@0" Pin0InfoVect0LinkObjId="g_26aad30_0" Pin0InfoVect1LinkObjId="SW-314600_0" Pin0InfoVect2LinkObjId="g_254fb10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="301,-356 336,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25f0330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-346 336,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_26aad30@1" ObjectIDZND0="48611@x" ObjectIDZND1="48610@x" ObjectIDZND2="g_254fb10@0" Pin0InfoVect0LinkObjId="SW-314601_0" Pin0InfoVect1LinkObjId="SW-314600_0" Pin0InfoVect2LinkObjId="g_254fb10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26aad30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="336,-346 336,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25f0590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-356 336,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="48611@x" ObjectIDND1="g_26aad30@0" ObjectIDND2="g_254fb10@0" ObjectIDZND0="48610@0" Pin0InfoVect0LinkObjId="SW-314600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-314601_0" Pin1InfoVect1LinkObjId="g_26aad30_0" Pin1InfoVect2LinkObjId="g_254fb10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-356 336,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25f07f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="377,-356 336,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_254fb10@0" ObjectIDZND0="48611@x" ObjectIDZND1="g_26aad30@0" ObjectIDZND2="48610@x" Pin0InfoVect0LinkObjId="SW-314601_0" Pin0InfoVect1LinkObjId="g_26aad30_0" Pin0InfoVect2LinkObjId="SW-314600_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_254fb10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="377,-356 336,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_263ba60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="377,-237 336,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_25f0a50@0" ObjectIDZND0="48612@x" ObjectIDZND1="48613@x" ObjectIDZND2="48644@x" Pin0InfoVect0LinkObjId="SW-314602_0" Pin0InfoVect1LinkObjId="SW-314603_0" Pin0InfoVect2LinkObjId="EC-DY_TSB.063Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25f0a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="377,-237 336,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_263c770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-158 336,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="48644@0" ObjectIDZND0="g_25f0a50@0" ObjectIDZND1="48612@x" ObjectIDZND2="48613@x" Pin0InfoVect0LinkObjId="g_25f0a50_0" Pin0InfoVect1LinkObjId="SW-314602_0" Pin0InfoVect2LinkObjId="SW-314603_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-DY_TSB.063Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="336,-158 336,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_263c9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-237 336,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_25f0a50@0" ObjectIDND1="48613@x" ObjectIDND2="48644@x" ObjectIDZND0="48612@0" Pin0InfoVect0LinkObjId="SW-314602_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25f0a50_0" Pin1InfoVect1LinkObjId="SW-314603_0" Pin1InfoVect2LinkObjId="EC-DY_TSB.063Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-237 336,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_263cc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="301,-237 336,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="48613@0" ObjectIDZND0="g_25f0a50@0" ObjectIDZND1="48612@x" ObjectIDZND2="48644@x" Pin0InfoVect0LinkObjId="g_25f0a50_0" Pin0InfoVect1LinkObjId="SW-314602_0" Pin0InfoVect2LinkObjId="EC-DY_TSB.063Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314603_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="301,-237 336,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_263ce90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-464 336,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48609@0" ObjectIDZND0="48641@0" Pin0InfoVect0LinkObjId="g_2594ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-464 336,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25768c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="106,-386 106,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48604@1" ObjectIDZND0="48602@0" Pin0InfoVect0LinkObjId="SW-314565_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314566_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="106,-386 106,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2576b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="106,-431 106,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48602@1" ObjectIDZND0="48603@1" Pin0InfoVect0LinkObjId="SW-314566_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314565_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="106,-431 106,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2625ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="71,-355 106,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48605@0" ObjectIDZND0="g_263d0f0@0" ObjectIDZND1="g_2576d80@0" ObjectIDZND2="48604@x" Pin0InfoVect0LinkObjId="g_263d0f0_0" Pin0InfoVect1LinkObjId="g_2576d80_0" Pin0InfoVect2LinkObjId="SW-314566_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314567_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="71,-355 106,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2625e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="106,-345 106,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_263d0f0@1" ObjectIDZND0="48605@x" ObjectIDZND1="g_2576d80@0" ObjectIDZND2="48604@x" Pin0InfoVect0LinkObjId="SW-314567_0" Pin0InfoVect1LinkObjId="g_2576d80_0" Pin0InfoVect2LinkObjId="SW-314566_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_263d0f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="106,-345 106,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2626060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="106,-355 106,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_263d0f0@0" ObjectIDND1="48605@x" ObjectIDND2="g_2576d80@0" ObjectIDZND0="48604@0" Pin0InfoVect0LinkObjId="SW-314566_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_263d0f0_0" Pin1InfoVect1LinkObjId="SW-314567_0" Pin1InfoVect2LinkObjId="g_2576d80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="106,-355 106,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26262c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="147,-355 106,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2576d80@0" ObjectIDZND0="g_263d0f0@0" ObjectIDZND1="48605@x" ObjectIDZND2="48604@x" Pin0InfoVect0LinkObjId="g_263d0f0_0" Pin0InfoVect1LinkObjId="SW-314567_0" Pin0InfoVect2LinkObjId="SW-314566_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2576d80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="147,-355 106,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2626520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="106,-463 106,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48603@0" ObjectIDZND0="48641@0" Pin0InfoVect0LinkObjId="g_2594ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314566_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="106,-463 106,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a2d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="53,-213 53,-73 107,-73 107,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" ObjectIDZND0="48647@0" Pin0InfoVect0LinkObjId="CB-DY_TSB.DY_ST_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="53,-213 53,-73 107,-73 107,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a4500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="77,-258 77,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25a2fe0@0" ObjectIDZND0="48607@1" Pin0InfoVect0LinkObjId="SW-314569_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25a2fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="77,-258 77,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a4760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="107,-203 77,-203 77,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="48606@x" ObjectIDND1="48647@x" ObjectIDZND0="48607@0" Pin0InfoVect0LinkObjId="SW-314569_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-314568_0" Pin1InfoVect1LinkObjId="CB-DY_TSB.DY_ST_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="107,-203 77,-203 77,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a49c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="107,-214 107,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="48606@0" ObjectIDZND0="48607@x" ObjectIDZND1="48647@x" Pin0InfoVect0LinkObjId="SW-314569_0" Pin0InfoVect1LinkObjId="CB-DY_TSB.DY_ST_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="107,-214 107,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a4c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="107,-203 107,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="48607@x" ObjectIDND1="48606@x" ObjectIDZND0="48647@1" Pin0InfoVect0LinkObjId="CB-DY_TSB.DY_ST_Cb1_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-314569_0" Pin1InfoVect1LinkObjId="SW-314568_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="107,-203 107,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a4e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="106,-246 106,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48606@1" ObjectIDZND0="g_263d0f0@0" Pin0InfoVect0LinkObjId="g_263d0f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314568_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="106,-246 106,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a5970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-129,-461 -129,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48599@0" ObjectIDZND0="48641@0" Pin0InfoVect0LinkObjId="g_2594ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314535_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-129,-461 -129,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_262f890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-190,-135 -190,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_262ec60@1" ObjectIDZND0="g_25a6ad0@0" Pin0InfoVect0LinkObjId="g_25a6ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262ec60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-190,-135 -190,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_262faf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-129,-243 -190,-243 -190,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="48650@1" Pin0InfoVect0LinkObjId="SW-315141_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-129,-243 -190,-243 -190,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2632550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-190,-169 -190,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_262ec60@0" ObjectIDZND0="48650@0" Pin0InfoVect0LinkObjId="SW-315141_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262ec60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-190,-169 -190,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e9820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,-286 530,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48630@1" ObjectIDZND0="g_25e8c00@0" Pin0InfoVect0LinkObjId="g_25e8c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314774_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="530,-286 530,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26211b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,-385 530,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48628@1" ObjectIDZND0="48626@0" Pin0InfoVect0LinkObjId="SW-314771_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="530,-385 530,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2621410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,-430 530,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48626@1" ObjectIDZND0="48627@1" Pin0InfoVect0LinkObjId="SW-314772_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314771_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="530,-430 530,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258ac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="495,-354 530,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48629@0" ObjectIDZND0="g_25e8c00@0" ObjectIDZND1="g_2621670@0" ObjectIDZND2="48628@x" Pin0InfoVect0LinkObjId="g_25e8c00_0" Pin0InfoVect1LinkObjId="g_2621670_0" Pin0InfoVect2LinkObjId="SW-314772_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="495,-354 530,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258ae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,-344 530,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_25e8c00@1" ObjectIDZND0="48629@x" ObjectIDZND1="g_2621670@0" ObjectIDZND2="48628@x" Pin0InfoVect0LinkObjId="SW-314773_0" Pin0InfoVect1LinkObjId="g_2621670_0" Pin0InfoVect2LinkObjId="SW-314772_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e8c00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="530,-344 530,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258b0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,-354 530,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_25e8c00@0" ObjectIDND1="48629@x" ObjectIDND2="g_2621670@0" ObjectIDZND0="48628@0" Pin0InfoVect0LinkObjId="SW-314772_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25e8c00_0" Pin1InfoVect1LinkObjId="SW-314773_0" Pin1InfoVect2LinkObjId="g_2621670_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="530,-354 530,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258b350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-354 530,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2621670@0" ObjectIDZND0="g_25e8c00@0" ObjectIDZND1="48629@x" ObjectIDZND2="48628@x" Pin0InfoVect0LinkObjId="g_25e8c00_0" Pin0InfoVect1LinkObjId="SW-314773_0" Pin0InfoVect2LinkObjId="SW-314772_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2621670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="571,-354 530,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258f5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-235 530,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_258b5b0@0" ObjectIDZND0="48631@x" ObjectIDZND1="48630@x" ObjectIDZND2="48654@x" Pin0InfoVect0LinkObjId="SW-314775_0" Pin0InfoVect1LinkObjId="SW-314774_0" Pin0InfoVect2LinkObjId="EC-DY_TSB.064Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_258b5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="571,-235 530,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258f830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,-156 530,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="48654@0" ObjectIDZND0="g_258b5b0@0" ObjectIDZND1="48631@x" ObjectIDZND2="48630@x" Pin0InfoVect0LinkObjId="g_258b5b0_0" Pin0InfoVect1LinkObjId="SW-314775_0" Pin0InfoVect2LinkObjId="SW-314774_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-DY_TSB.064Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="530,-156 530,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258fa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,-235 530,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_258b5b0@0" ObjectIDND1="48631@x" ObjectIDND2="48654@x" ObjectIDZND0="48630@0" Pin0InfoVect0LinkObjId="SW-314774_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_258b5b0_0" Pin1InfoVect1LinkObjId="SW-314775_0" Pin1InfoVect2LinkObjId="EC-DY_TSB.064Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="530,-235 530,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258fcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="495,-235 530,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="48631@0" ObjectIDZND0="g_258b5b0@0" ObjectIDZND1="48630@x" ObjectIDZND2="48654@x" Pin0InfoVect0LinkObjId="g_258b5b0_0" Pin0InfoVect1LinkObjId="SW-314774_0" Pin0InfoVect2LinkObjId="EC-DY_TSB.064Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314775_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="495,-235 530,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258ff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="530,-462 530,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48627@0" ObjectIDZND0="48641@0" Pin0InfoVect0LinkObjId="g_2594ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="530,-462 530,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c7310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-288 724,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48624@1" ObjectIDZND0="g_25c6710@0" Pin0InfoVect0LinkObjId="g_25c6710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314717_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-288 724,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25cfdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-387 724,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48622@1" ObjectIDZND0="48620@0" Pin0InfoVect0LinkObjId="SW-314714_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314715_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-387 724,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d0020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-432 724,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48620@1" ObjectIDZND0="48621@1" Pin0InfoVect0LinkObjId="SW-314715_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314714_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-432 724,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b7f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="689,-356 724,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48623@0" ObjectIDZND0="g_25c6710@0" ObjectIDZND1="g_25d0280@0" ObjectIDZND2="48622@x" Pin0InfoVect0LinkObjId="g_25c6710_0" Pin0InfoVect1LinkObjId="g_25d0280_0" Pin0InfoVect2LinkObjId="SW-314715_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314716_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="689,-356 724,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b81f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-346 724,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_25c6710@1" ObjectIDZND0="48623@x" ObjectIDZND1="g_25d0280@0" ObjectIDZND2="48622@x" Pin0InfoVect0LinkObjId="SW-314716_0" Pin0InfoVect1LinkObjId="g_25d0280_0" Pin0InfoVect2LinkObjId="SW-314715_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25c6710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="724,-346 724,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b8450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-356 724,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_25c6710@0" ObjectIDND1="48623@x" ObjectIDND2="g_25d0280@0" ObjectIDZND0="48622@0" Pin0InfoVect0LinkObjId="SW-314715_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25c6710_0" Pin1InfoVect1LinkObjId="SW-314716_0" Pin1InfoVect2LinkObjId="g_25d0280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-356 724,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b86b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="765,-356 724,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_25d0280@0" ObjectIDZND0="g_25c6710@0" ObjectIDZND1="48623@x" ObjectIDZND2="48622@x" Pin0InfoVect0LinkObjId="g_25c6710_0" Pin0InfoVect1LinkObjId="SW-314716_0" Pin0InfoVect2LinkObjId="SW-314715_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25d0280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="765,-356 724,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25bc930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="765,-237 724,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_25b8910@0" ObjectIDZND0="48625@x" ObjectIDZND1="48624@x" ObjectIDZND2="48646@x" Pin0InfoVect0LinkObjId="SW-314718_0" Pin0InfoVect1LinkObjId="SW-314717_0" Pin0InfoVect2LinkObjId="EC-DY_TSB.065Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25b8910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="765,-237 724,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25bcb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-158 724,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="48646@0" ObjectIDZND0="g_25b8910@0" ObjectIDZND1="48625@x" ObjectIDZND2="48624@x" Pin0InfoVect0LinkObjId="g_25b8910_0" Pin0InfoVect1LinkObjId="SW-314718_0" Pin0InfoVect2LinkObjId="SW-314717_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-DY_TSB.065Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="724,-158 724,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25bcdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-237 724,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_25b8910@0" ObjectIDND1="48625@x" ObjectIDND2="48646@x" ObjectIDZND0="48624@0" Pin0InfoVect0LinkObjId="SW-314717_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25b8910_0" Pin1InfoVect1LinkObjId="SW-314718_0" Pin1InfoVect2LinkObjId="EC-DY_TSB.065Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-237 724,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25bd050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="689,-237 724,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="48625@0" ObjectIDZND0="g_25b8910@0" ObjectIDZND1="48624@x" ObjectIDZND2="48646@x" Pin0InfoVect0LinkObjId="g_25b8910_0" Pin0InfoVect1LinkObjId="SW-314717_0" Pin0InfoVect2LinkObjId="EC-DY_TSB.065Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314718_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="689,-237 724,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25bd2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-464 724,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48621@0" ObjectIDZND0="48641@0" Pin0InfoVect0LinkObjId="g_2594ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314715_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-464 724,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257ad60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,-288 917,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48618@1" ObjectIDZND0="g_257a180@0" Pin0InfoVect0LinkObjId="g_257a180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314659_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="917,-288 917,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2583b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,-387 917,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48616@1" ObjectIDZND0="48614@0" Pin0InfoVect0LinkObjId="SW-314656_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314657_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="917,-387 917,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2583d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,-432 917,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48614@1" ObjectIDZND0="48615@1" Pin0InfoVect0LinkObjId="SW-314657_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314656_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="917,-432 917,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2522ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="882,-356 917,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48617@0" ObjectIDZND0="g_257a180@0" ObjectIDZND1="g_2583ff0@0" ObjectIDZND2="48616@x" Pin0InfoVect0LinkObjId="g_257a180_0" Pin0InfoVect1LinkObjId="g_2583ff0_0" Pin0InfoVect2LinkObjId="SW-314657_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314658_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="882,-356 917,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2523250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,-346 917,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_257a180@1" ObjectIDZND0="48617@x" ObjectIDZND1="g_2583ff0@0" ObjectIDZND2="48616@x" Pin0InfoVect0LinkObjId="SW-314658_0" Pin0InfoVect1LinkObjId="g_2583ff0_0" Pin0InfoVect2LinkObjId="SW-314657_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_257a180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="917,-346 917,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25234b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,-356 917,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_257a180@0" ObjectIDND1="48617@x" ObjectIDND2="g_2583ff0@0" ObjectIDZND0="48616@0" Pin0InfoVect0LinkObjId="SW-314657_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_257a180_0" Pin1InfoVect1LinkObjId="SW-314658_0" Pin1InfoVect2LinkObjId="g_2583ff0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="917,-356 917,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2523710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="958,-356 917,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2583ff0@0" ObjectIDZND0="g_257a180@0" ObjectIDZND1="48617@x" ObjectIDZND2="48616@x" Pin0InfoVect0LinkObjId="g_257a180_0" Pin0InfoVect1LinkObjId="SW-314658_0" Pin0InfoVect2LinkObjId="SW-314657_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2583ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="958,-356 917,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2527710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="958,-237 917,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2523970@0" ObjectIDZND0="48619@x" ObjectIDZND1="48618@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-314660_0" Pin0InfoVect1LinkObjId="SW-314659_0" Pin0InfoVect2LinkObjId="TF-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2523970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="958,-237 917,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2527970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,-237 917,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2523970@0" ObjectIDND1="48619@x" ObjectIDND2="0@x" ObjectIDZND0="48618@0" Pin0InfoVect0LinkObjId="SW-314659_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2523970_0" Pin1InfoVect1LinkObjId="SW-314660_0" Pin1InfoVect2LinkObjId="TF-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="917,-237 917,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2527bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="882,-237 917,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="48619@0" ObjectIDZND0="g_2523970@0" ObjectIDZND1="48618@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2523970_0" Pin0InfoVect1LinkObjId="SW-314659_0" Pin0InfoVect2LinkObjId="TF-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="882,-237 917,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2527e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,-464 917,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48615@0" ObjectIDZND0="48641@0" Pin0InfoVect0LinkObjId="g_2594ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314657_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="917,-464 917,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_252f1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11,-630 11,-621 58,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_252e700@0" ObjectIDZND0="g_26ab7f0@0" ObjectIDZND1="48593@x" Pin0InfoVect0LinkObjId="g_26ab7f0_0" Pin0InfoVect1LinkObjId="SW-314521_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_252e700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="11,-630 11,-621 58,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2531d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="58,-694 57,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_26ab7f0@1" ObjectIDZND0="g_252f3f0@0" Pin0InfoVect0LinkObjId="g_252f3f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26ab7f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="58,-694 57,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2532860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="58,-621 58,-650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_252e700@0" ObjectIDND1="48593@x" ObjectIDZND0="g_26ab7f0@0" Pin0InfoVect0LinkObjId="g_26ab7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_252e700_0" Pin1InfoVect1LinkObjId="SW-314521_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="58,-621 58,-650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2509930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="58,-593 58,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48593@0" ObjectIDZND0="g_252e700@0" ObjectIDZND1="g_26ab7f0@0" Pin0InfoVect0LinkObjId="g_252e700_0" Pin0InfoVect1LinkObjId="g_26ab7f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314521_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="58,-593 58,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2509b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="58,-487 58,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="48641@0" ObjectIDZND0="48592@0" Pin0InfoVect0LinkObjId="SW-314521_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2594ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="58,-487 58,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2509df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="58,-526 58,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48592@1" ObjectIDZND0="48593@1" Pin0InfoVect0LinkObjId="SW-314521_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314521_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="58,-526 58,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250a050">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="1036,-611 1266,-611 1266,-491 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="48595@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314525_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1036,-611 1266,-611 1266,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2513030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1036,-510 1036,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48594@0" ObjectIDZND0="48641@0" Pin0InfoVect0LinkObjId="g_2594ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314525_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1036,-510 1036,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2513290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1036,-611 1036,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="48595@0" Pin0InfoVect0LinkObjId="SW-314525_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1036,-611 1036,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25134f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1036,-573 1036,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48595@1" ObjectIDZND0="48594@1" Pin0InfoVect0LinkObjId="SW-314525_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314525_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1036,-573 1036,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_253b280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1027,-166 917,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@2" ObjectIDZND0="48645@x" ObjectIDZND1="g_2523970@0" ObjectIDZND2="48619@x" Pin0InfoVect0LinkObjId="EC-DY_TSB.066Ld_0" Pin0InfoVect1LinkObjId="g_2523970_0" Pin0InfoVect2LinkObjId="SW-314660_0" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1027,-166 917,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_253bd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,-158 917,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48645@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2523970@0" ObjectIDZND2="48619@x" Pin0InfoVect0LinkObjId="TF-0_0" Pin0InfoVect1LinkObjId="g_2523970_0" Pin0InfoVect2LinkObjId="SW-314660_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-DY_TSB.066Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="917,-158 917,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_253bff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,-166 917,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="48645@x" ObjectIDZND0="g_2523970@0" ObjectIDZND1="48619@x" ObjectIDZND2="48618@x" Pin0InfoVect0LinkObjId="g_2523970_0" Pin0InfoVect1LinkObjId="SW-314660_0" Pin0InfoVect2LinkObjId="SW-314659_0" Pin0Num="2" Pin1InfoVect0LinkObjId="TF-0_0" Pin1InfoVect1LinkObjId="EC-DY_TSB.066Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="917,-166 917,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_253df80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="103,-1008 103,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="48640@0" ObjectIDZND0="48576@0" Pin0InfoVect0LinkObjId="SW-314320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262a420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="103,-1008 103,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_253e170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="135,-1369 103,-1369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_26879e0@0" ObjectIDZND0="48652@1" ObjectIDZND1="g_2686df0@0" ObjectIDZND2="48578@x" Pin0InfoVect0LinkObjId="g_253ec20_1" Pin0InfoVect1LinkObjId="g_2686df0_0" Pin0InfoVect2LinkObjId="SW-314322_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26879e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="135,-1369 103,-1369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_253ec20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="103,-1369 103,-1405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_26879e0@0" ObjectIDND1="g_2686df0@0" ObjectIDND2="48578@x" ObjectIDZND0="48652@1" Pin0InfoVect0LinkObjId="g_253e170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26879e0_0" Pin1InfoVect1LinkObjId="g_2686df0_0" Pin1InfoVect2LinkObjId="SW-314322_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="103,-1369 103,-1405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_253ee80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="47,-1324 103,-1324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_2686df0@0" ObjectIDZND0="g_26879e0@0" ObjectIDZND1="48652@1" ObjectIDZND2="48578@x" Pin0InfoVect0LinkObjId="g_26879e0_0" Pin0InfoVect1LinkObjId="g_253e170_1" Pin0InfoVect2LinkObjId="SW-314322_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2686df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="47,-1324 103,-1324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_253f970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="103,-1324 103,-1369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" EndDevType1="powerLine" ObjectIDND0="g_2686df0@0" ObjectIDND1="48578@x" ObjectIDND2="48577@x" ObjectIDZND0="g_26879e0@0" ObjectIDZND1="48652@1" Pin0InfoVect0LinkObjId="g_26879e0_0" Pin0InfoVect1LinkObjId="g_253e170_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2686df0_0" Pin1InfoVect1LinkObjId="SW-314322_0" Pin1InfoVect2LinkObjId="SW-314321_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="103,-1324 103,-1369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_253fbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="75,-1272 103,-1272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48578@0" ObjectIDZND0="48577@x" ObjectIDZND1="g_2686df0@0" ObjectIDZND2="g_26879e0@0" Pin0InfoVect0LinkObjId="SW-314321_0" Pin0InfoVect1LinkObjId="g_2686df0_0" Pin0InfoVect2LinkObjId="g_26879e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="75,-1272 103,-1272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25406c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="103,-1247 103,-1272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48577@1" ObjectIDZND0="48578@x" ObjectIDZND1="g_2686df0@0" ObjectIDZND2="g_26879e0@0" Pin0InfoVect0LinkObjId="SW-314322_0" Pin0InfoVect1LinkObjId="g_2686df0_0" Pin0InfoVect2LinkObjId="g_26879e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314321_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="103,-1247 103,-1272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2540920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="103,-1272 103,-1324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="48578@x" ObjectIDND1="48577@x" ObjectIDZND0="g_2686df0@0" ObjectIDZND1="g_26879e0@0" ObjectIDZND2="48652@1" Pin0InfoVect0LinkObjId="g_2686df0_0" Pin0InfoVect1LinkObjId="g_26879e0_0" Pin0InfoVect2LinkObjId="g_253e170_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-314322_0" Pin1InfoVect1LinkObjId="SW-314321_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="103,-1272 103,-1324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2540b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="72,-1190 103,-1190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="48579@0" ObjectIDZND0="48575@x" ObjectIDZND1="48577@x" Pin0InfoVect0LinkObjId="SW-314319_0" Pin0InfoVect1LinkObjId="SW-314321_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="72,-1190 103,-1190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2541670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="103,-1168 103,-1190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48575@1" ObjectIDZND0="48579@x" ObjectIDZND1="48577@x" Pin0InfoVect0LinkObjId="SW-314323_0" Pin0InfoVect1LinkObjId="SW-314321_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314319_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="103,-1168 103,-1190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25418d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="103,-1190 103,-1211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="48579@x" ObjectIDND1="48575@x" ObjectIDZND0="48577@0" Pin0InfoVect0LinkObjId="SW-314321_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-314323_0" Pin1InfoVect1LinkObjId="SW-314319_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="103,-1190 103,-1211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2541b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="76,-1115 103,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48580@0" ObjectIDZND0="48576@x" ObjectIDZND1="48575@x" Pin0InfoVect0LinkObjId="SW-314320_0" Pin0InfoVect1LinkObjId="SW-314319_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="76,-1115 103,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2542620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="103,-1088 103,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48576@1" ObjectIDZND0="48580@x" ObjectIDZND1="48575@x" Pin0InfoVect0LinkObjId="SW-314324_0" Pin0InfoVect1LinkObjId="SW-314319_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-314320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="103,-1088 103,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2542880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="103,-1115 103,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="48580@x" ObjectIDND1="48576@x" ObjectIDZND0="48575@0" Pin0InfoVect0LinkObjId="SW-314319_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-314324_0" Pin1InfoVect1LinkObjId="SW-314320_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="103,-1115 103,-1141 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_TSB"/>
</svg>