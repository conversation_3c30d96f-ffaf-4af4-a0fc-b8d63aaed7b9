<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-117" aopId="786686" id="thSvg" viewBox="3115 -1198 1874 1272">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape169">
    <circle cx="26" cy="57" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="20" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="20" y1="19" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="20" x2="20" y1="28" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="55" y1="42" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="70" x2="62" y1="49" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="62" y1="33" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="18" y1="62" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="25" y1="69" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="25" y1="53" y2="62"/>
    <circle cx="26" cy="25" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <circle cx="56" cy="41" fillStyle="0" r="25" stroke-width="0.520408"/>
   </symbol>
   <symbol id="lightningRod:shape165">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="96" y2="104"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.299051" x1="44" x2="44" y1="63" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.468987" x1="7" x2="44" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="63" y2="33"/>
    <polyline fill="none" points="25,96 27,96 29,95 30,95 32,94 33,93 35,92 36,90 37,88 37,87 38,85 38,83 38,81 37,79 37,78 36,76 35,75 33,73 32,72 30,71 29,71 27,70 25,70 23,70 21,71 20,71 18,72 17,73 15,75 14,76 13,78 13,79 12,81 12,83 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="26" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="24" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="17" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="52" x2="52" y1="52" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="26" x2="26" y1="16" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="42" x2="42" y1="2" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="9" x2="9" y1="2" y2="6"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="32"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <polyline arcFlag="1" fill="none" points="43,42 44,42 45,42 45,42 46,43 46,43 47,44 47,44 48,45 48,45 48,46 48,47 49,48 49,49 49,49 48,50 48,51 48,52 48,52 47,53 47,53 46,54 46,54 45,55 45,55 44,55 43,55 " stroke-width="1"/>
    <polyline arcFlag="1" fill="none" points="43,29 44,29 45,29 45,29 46,30 46,30 47,31 47,31 48,32 48,32 48,33 48,34 49,35 49,36 49,36 48,37 48,38 48,39 48,39 47,40 47,40 46,41 46,41 45,42 45,42 44,42 43,42 " stroke-width="1"/>
    <polyline arcFlag="1" fill="none" points="43,17 44,17 45,17 45,17 46,18 46,18 47,19 47,19 48,20 48,20 48,21 48,22 49,23 49,24 49,24 48,25 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,30 45,30 44,30 43,30 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape197">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="15,57 40,57 40,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="14,14 20,27 7,27 14,14 14,15 14,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="42" y2="0"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="55" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
   </symbol>
   <symbol id="lightningRod:shape198">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="87"/>
    <rect height="4" stroke-width="1" width="19" x="10" y="117"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="9,130 31,108 31,94 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="28" y1="136" y2="136"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="145" y2="137"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="55" y2="51"/>
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="31,57 6,57 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="51" y2="26"/>
    <circle cx="30" cy="79" fillStyle="0" r="15" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="18" x2="1" y1="33" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.675" x1="18" x2="18" y1="27" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.845585" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="21" x2="1" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="19" x2="28" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="19" x2="19" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="-8" x2="1" y1="29" y2="29"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="16" x2="-1" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="17" x2="25" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="16" x2="16" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="-7" x2="-2" y1="27" y2="27"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="21" x2="1" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="19" x2="28" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="19" x2="19" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="-8" x2="1" y1="27" y2="27"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="15" x2="15" y1="23" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="14" x2="16" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="15" x2="15" y1="50" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="15" y1="49" y2="32"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="15" x2="15" y1="52" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="15" x2="15" y1="23" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="15" x2="15" y1="50" y2="59"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="15" x2="15" y1="23" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="14" x2="16" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="15" x2="15" y1="50" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="15" y1="49" y2="32"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="15" x2="15" y1="52" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="15" x2="15" y1="23" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="15" x2="15" y1="50" y2="59"/>
   </symbol>
   <symbol id="voltageTransformer:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="11" y2="9"/>
    <circle cx="15" cy="19" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="24" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="11" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="9" y2="7"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b2090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b2970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21b32b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21b4500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21b57f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21b6490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b7030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b7a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b82a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b8c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21b9870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21ba080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21ba660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bb000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bb880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21bbfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bd760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21be460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21bed20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21bf710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c08f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c1270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c1d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21c70e0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_21c7e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21c3a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_21c4f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21d44c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_21c9ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1282" width="1884" x="3110" y="-1203"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-596"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1197"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3985.000000 -768.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66149">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4470.000000 -603.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12486" ObjectName="SW-CX_DLB.CX_DLB_3716SW"/>
     <cge:Meas_Ref ObjectId="66149"/>
    <cge:TPSR_Ref TObjectID="12486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66148">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4470.000000 -465.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12485" ObjectName="SW-CX_DLB.CX_DLB_3711SW"/>
     <cge:Meas_Ref ObjectId="66148"/>
    <cge:TPSR_Ref TObjectID="12485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66158">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3698.000000 -373.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12490" ObjectName="SW-CX_DLB.CX_DLB_3721SW"/>
     <cge:Meas_Ref ObjectId="66158"/>
    <cge:TPSR_Ref TObjectID="12490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66159">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3698.000000 -235.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12491" ObjectName="SW-CX_DLB.CX_DLB_3726SW"/>
     <cge:Meas_Ref ObjectId="66159"/>
    <cge:TPSR_Ref TObjectID="12491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66166">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -372.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12493" ObjectName="SW-CX_DLB.CX_DLB_3731SW"/>
     <cge:Meas_Ref ObjectId="66166"/>
    <cge:TPSR_Ref TObjectID="12493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66167">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -234.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12494" ObjectName="SW-CX_DLB.CX_DLB_3736SW"/>
     <cge:Meas_Ref ObjectId="66167"/>
    <cge:TPSR_Ref TObjectID="12494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66174">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4154.000000 -372.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12496" ObjectName="SW-CX_DLB.CX_DLB_3741SW"/>
     <cge:Meas_Ref ObjectId="66174"/>
    <cge:TPSR_Ref TObjectID="12496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66175">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4154.000000 -234.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12497" ObjectName="SW-CX_DLB.CX_DLB_3746SW"/>
     <cge:Meas_Ref ObjectId="66175"/>
    <cge:TPSR_Ref TObjectID="12497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66182">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4377.000000 -366.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12499" ObjectName="SW-CX_DLB.CX_DLB_3751SW"/>
     <cge:Meas_Ref ObjectId="66182"/>
    <cge:TPSR_Ref TObjectID="12499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66183">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4377.000000 -233.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12500" ObjectName="SW-CX_DLB.CX_DLB_3756SW"/>
     <cge:Meas_Ref ObjectId="66183"/>
    <cge:TPSR_Ref TObjectID="12500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66186">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4606.000000 -371.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12502" ObjectName="SW-CX_DLB.CX_DLB_3761SW"/>
     <cge:Meas_Ref ObjectId="66186"/>
    <cge:TPSR_Ref TObjectID="12502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66187">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4606.000000 -233.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12503" ObjectName="SW-CX_DLB.CX_DLB_3766SW"/>
     <cge:Meas_Ref ObjectId="66187"/>
    <cge:TPSR_Ref TObjectID="12503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66191">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4833.000000 -374.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12505" ObjectName="SW-CX_DLB.CX_DLB_3771SW"/>
     <cge:Meas_Ref ObjectId="66191"/>
    <cge:TPSR_Ref TObjectID="12505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66192">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4833.000000 -262.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12506" ObjectName="SW-CX_DLB.CX_DLB_3776SW"/>
     <cge:Meas_Ref ObjectId="66192"/>
    <cge:TPSR_Ref TObjectID="12506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66150">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -463.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12487" ObjectName="SW-CX_DLB.CX_DLB_3901SW"/>
     <cge:Meas_Ref ObjectId="66150"/>
    <cge:TPSR_Ref TObjectID="12487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66151">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4792.000000 -526.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12488" ObjectName="SW-CX_DLB.CX_DLB_39017SW"/>
     <cge:Meas_Ref ObjectId="66151"/>
    <cge:TPSR_Ref TObjectID="12488"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_XJH.CX_XJH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-1149 3713,-933 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2280" ObjectName="BS-CX_XJH.CX_XJH_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3713,-1149 3713,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-1149 4871,-933 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4871,-1149 4871,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3947,-899 3947,-683 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3947,-899 3947,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DLB.CX_DLB_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3628,-452 4918,-452 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="12507" ObjectName="BS-CX_DLB.CX_DLB_3M"/>
    <cge:TPSR_Ref TObjectID="12507"/></metadata>
   <polyline fill="none" opacity="0" points="3628,-452 4918,-452 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_16e2050">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -830.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_243bd30">
    <use class="BV-35KV" transform="matrix(1.500000 -0.000000 0.000000 -1.465438 4478.000000 -726.838710)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_272f480">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4532.000000 -689.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_253e8b0">
    <use class="BV-35KV" transform="matrix(1.500000 -0.000000 0.000000 -1.465438 3706.000000 -133.838710)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_b4057c0">
    <use class="BV-35KV" transform="matrix(1.500000 -0.000000 0.000000 -1.465438 3932.000000 -133.838710)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2955ff0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 -231.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2336430">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.000000 -230.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f2fd0">
    <use class="BV-35KV" transform="matrix(1.500000 -0.000000 0.000000 -1.465438 4162.000000 -133.838710)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2614e20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 -230.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18365b0">
    <use class="BV-35KV" transform="matrix(1.500000 -0.000000 0.000000 -1.465438 4385.000000 -132.838710)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d27e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -229.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b9ef10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -25.000000)" xlink:href="#lightningRod:shape169"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1701d70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3913.000000 -25.000000)" xlink:href="#lightningRod:shape169"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21dc760">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -25.000000)" xlink:href="#lightningRod:shape169"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d77fa0">
    <use class="BV-35KV" transform="matrix(1.500000 -0.000000 0.000000 -1.465438 4614.000000 -132.838710)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cbfaf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4596.000000 -24.000000)" xlink:href="#lightningRod:shape165"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a88f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4377.000000 -26.000000)" xlink:href="#lightningRod:shape197"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2613c20">
    <use class="BV-35KV" transform="matrix(1.500000 -0.000000 0.000000 -1.465438 4841.000000 -183.838710)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18898b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -26.000000)" xlink:href="#lightningRod:shape198"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2416020">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4713.500000 -566.500000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b09b70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 -580.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3233.000000 -1115.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-66101" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4611.000000 -632.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12484"/>
     <cge:Term_Ref ObjectID="17233"/>
    <cge:TPSR_Ref TObjectID="12484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-66102" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4611.000000 -632.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12484"/>
     <cge:Term_Ref ObjectID="17233"/>
    <cge:TPSR_Ref TObjectID="12484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-66098" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4611.000000 -632.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12484"/>
     <cge:Term_Ref ObjectID="17233"/>
    <cge:TPSR_Ref TObjectID="12484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-66094" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4611.000000 -632.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12484"/>
     <cge:Term_Ref ObjectID="17233"/>
    <cge:TPSR_Ref TObjectID="12484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-66095" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4611.000000 -632.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12484"/>
     <cge:Term_Ref ObjectID="17233"/>
    <cge:TPSR_Ref TObjectID="12484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-66096" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4611.000000 -632.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66096" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12484"/>
     <cge:Term_Ref ObjectID="17233"/>
    <cge:TPSR_Ref TObjectID="12484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-66097" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4611.000000 -632.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12484"/>
     <cge:Term_Ref ObjectID="17233"/>
    <cge:TPSR_Ref TObjectID="12484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-66107" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3823.000000 -366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66107" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12489"/>
     <cge:Term_Ref ObjectID="17243"/>
    <cge:TPSR_Ref TObjectID="12489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-66108" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3823.000000 -366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66108" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12489"/>
     <cge:Term_Ref ObjectID="17243"/>
    <cge:TPSR_Ref TObjectID="12489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-66104" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3823.000000 -366.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66104" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12489"/>
     <cge:Term_Ref ObjectID="17243"/>
    <cge:TPSR_Ref TObjectID="12489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-66113" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4047.000000 -366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12492"/>
     <cge:Term_Ref ObjectID="17249"/>
    <cge:TPSR_Ref TObjectID="12492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-66114" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4047.000000 -366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66114" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12492"/>
     <cge:Term_Ref ObjectID="17249"/>
    <cge:TPSR_Ref TObjectID="12492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-66110" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4047.000000 -366.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12492"/>
     <cge:Term_Ref ObjectID="17249"/>
    <cge:TPSR_Ref TObjectID="12492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-66119" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4285.000000 -366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12495"/>
     <cge:Term_Ref ObjectID="17255"/>
    <cge:TPSR_Ref TObjectID="12495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-66120" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4285.000000 -366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12495"/>
     <cge:Term_Ref ObjectID="17255"/>
    <cge:TPSR_Ref TObjectID="12495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-66116" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4285.000000 -366.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66116" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12495"/>
     <cge:Term_Ref ObjectID="17255"/>
    <cge:TPSR_Ref TObjectID="12495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-66125" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4506.000000 -366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66125" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12498"/>
     <cge:Term_Ref ObjectID="17261"/>
    <cge:TPSR_Ref TObjectID="12498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-66126" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4506.000000 -366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12498"/>
     <cge:Term_Ref ObjectID="17261"/>
    <cge:TPSR_Ref TObjectID="12498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-66122" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4506.000000 -366.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12498"/>
     <cge:Term_Ref ObjectID="17261"/>
    <cge:TPSR_Ref TObjectID="12498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-66135" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4733.000000 -366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12501"/>
     <cge:Term_Ref ObjectID="17267"/>
    <cge:TPSR_Ref TObjectID="12501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-66136" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4733.000000 -366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12501"/>
     <cge:Term_Ref ObjectID="17267"/>
    <cge:TPSR_Ref TObjectID="12501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-66132" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4733.000000 -366.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12501"/>
     <cge:Term_Ref ObjectID="17267"/>
    <cge:TPSR_Ref TObjectID="12501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-66142" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4958.000000 -366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12504"/>
     <cge:Term_Ref ObjectID="17273"/>
    <cge:TPSR_Ref TObjectID="12504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-66143" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4958.000000 -366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12504"/>
     <cge:Term_Ref ObjectID="17273"/>
    <cge:TPSR_Ref TObjectID="12504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-66139" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4958.000000 -366.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66139" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="12504"/>
     <cge:Term_Ref ObjectID="17273"/>
    <cge:TPSR_Ref TObjectID="12504"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3245" y="-1174"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3245" y="-1174"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3196" y="-1191"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3196" y="-1191"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3245" y="-1174"/></g>
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3196" y="-1191"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-13244">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -1039.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2013" ObjectName="SW-CX_XJH.CX_XJH_343BK"/>
     <cge:Meas_Ref ObjectId="13244"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 -1039.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66145">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4476.000000 -554.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12484" ObjectName="SW-CX_DLB.CX_DLB_371BK"/>
     <cge:Meas_Ref ObjectId="66145"/>
    <cge:TPSR_Ref TObjectID="12484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66152">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -324.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12489" ObjectName="SW-CX_DLB.CX_DLB_372BK"/>
     <cge:Meas_Ref ObjectId="66152"/>
    <cge:TPSR_Ref TObjectID="12489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66160">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3930.000000 -323.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12492" ObjectName="SW-CX_DLB.CX_DLB_373BK"/>
     <cge:Meas_Ref ObjectId="66160"/>
    <cge:TPSR_Ref TObjectID="12492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66168">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4160.000000 -323.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12495" ObjectName="SW-CX_DLB.CX_DLB_374BK"/>
     <cge:Meas_Ref ObjectId="66168"/>
    <cge:TPSR_Ref TObjectID="12495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66176">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4383.000000 -322.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12498" ObjectName="SW-CX_DLB.CX_DLB_375BK"/>
     <cge:Meas_Ref ObjectId="66176"/>
    <cge:TPSR_Ref TObjectID="12498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66184">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 -326.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12501" ObjectName="SW-CX_DLB.CX_DLB_376BK"/>
     <cge:Meas_Ref ObjectId="66184"/>
    <cge:TPSR_Ref TObjectID="12501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66188">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4839.000000 -326.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12504" ObjectName="SW-CX_DLB.CX_DLB_377BK"/>
     <cge:Meas_Ref ObjectId="66188"/>
    <cge:TPSR_Ref TObjectID="12504"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1df0870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4838.000000 -543.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="2280" cx="3713" cy="-1049" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4871" cy="-1049" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3947" cy="-794" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12507" cx="3713" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12507" cx="3939" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12507" cx="4169" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12507" cx="4392" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12507" cx="4485" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12507" cx="4621" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12507" cx="4848" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="12507" cx="4759" cy="-452" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22dca60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3137.000000 -576.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23902c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1055.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23902c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1055.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23902c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1055.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23902c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1055.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23902c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1055.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23902c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1055.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23902c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3129.000000 -1055.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_18489c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3284.000000 -1163.500000) translate(0,16)">东林变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ae0350" transform="matrix(1.000000 0.000000 0.000000 1.000000 3654.000000 -1133.000000) translate(0,23)">220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ae0350" transform="matrix(1.000000 0.000000 0.000000 1.000000 3654.000000 -1133.000000) translate(0,51)"> kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ae0350" transform="matrix(1.000000 0.000000 0.000000 1.000000 3654.000000 -1133.000000) translate(0,79)"> 谢</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ae0350" transform="matrix(1.000000 0.000000 0.000000 1.000000 3654.000000 -1133.000000) translate(0,107)"> 家</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ae0350" transform="matrix(1.000000 0.000000 0.000000 1.000000 3654.000000 -1133.000000) translate(0,135)"> 河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ae0350" transform="matrix(1.000000 0.000000 0.000000 1.000000 3654.000000 -1133.000000) translate(0,163)"> 变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ef5500" transform="matrix(1.000000 0.000000 0.000000 1.000000 4885.000000 -1133.000000) translate(0,23)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ef5500" transform="matrix(1.000000 0.000000 0.000000 1.000000 4885.000000 -1133.000000) translate(0,51)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ef5500" transform="matrix(1.000000 0.000000 0.000000 1.000000 4885.000000 -1133.000000) translate(0,79)">机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ef5500" transform="matrix(1.000000 0.000000 0.000000 1.000000 4885.000000 -1133.000000) translate(0,107)">械</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ef5500" transform="matrix(1.000000 0.000000 0.000000 1.000000 4885.000000 -1133.000000) translate(0,135)">园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ef5500" transform="matrix(1.000000 0.000000 0.000000 1.000000 4885.000000 -1133.000000) translate(0,163)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_1ef5500" transform="matrix(1.000000 0.000000 0.000000 1.000000 4885.000000 -1133.000000) translate(0,191)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ad23f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4799.000000 -1143.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_275db90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3911.000000 -901.000000) translate(0,20)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_275db90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3911.000000 -901.000000) translate(0,45)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_275db90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3911.000000 -901.000000) translate(0,70)">滇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_275db90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3911.000000 -901.000000) translate(0,95)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_275db90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3911.000000 -901.000000) translate(0,120)">铁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_275db90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3911.000000 -901.000000) translate(0,145)">合</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_275db90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3911.000000 -901.000000) translate(0,170)">金</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_275db90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3911.000000 -901.000000) translate(0,195)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_275db90" transform="matrix(1.000000 0.000000 0.000000 1.000000 3911.000000 -901.000000) translate(0,220)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1d6d920" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -769.000000) translate(0,20)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1d6d920" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -769.000000) translate(0,45)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1d6d920" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -769.000000) translate(0,70)">带</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1d6d920" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -769.000000) translate(0,95)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1d6d920" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -769.000000) translate(0,120)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1d6d920" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -769.000000) translate(0,145)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1d6d920" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -769.000000) translate(0,170)">支</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_1d6d920" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -769.000000) translate(0,195)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_25556a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4495.000000 -1041.000000) translate(0,20)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_25556a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4495.000000 -1041.000000) translate(0,45)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_25556a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4495.000000 -1041.000000) translate(0,70)">东</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_25556a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4495.000000 -1041.000000) translate(0,95)">林</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_25556a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4495.000000 -1041.000000) translate(0,120)">支</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_25556a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4495.000000 -1041.000000) translate(0,145)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_18bd070" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -1041.000000) translate(0,20)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_18bd070" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -1041.000000) translate(0,45)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_18bd070" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -1041.000000) translate(0,70)">滇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_18bd070" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -1041.000000) translate(0,95)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_18bd070" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -1041.000000) translate(0,120)">铁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_18bd070" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -1041.000000) translate(0,145)">合</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_18bd070" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -1041.000000) translate(0,170)">金</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_18bd070" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -1041.000000) translate(0,195)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_18bd070" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -1041.000000) translate(0,220)">支</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_18bd070" transform="matrix(1.000000 0.000000 0.000000 1.000000 4109.000000 -1041.000000) translate(0,245)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_23b7860" transform="matrix(1.000000 0.000000 0.000000 1.000000 3664.500000 -21.000000) translate(0,23)">1号冶炼变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_21a5d70" transform="matrix(1.000000 0.000000 0.000000 1.000000 3890.500000 -21.000000) translate(0,23)">2号冶炼变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_2652ad0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4120.000000 -21.000000) translate(0,23)">3号冶炼变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_2532360" transform="matrix(1.000000 0.000000 0.000000 1.000000 4337.000000 -21.000000) translate(0,23)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_24bd790" transform="matrix(1.000000 0.000000 0.000000 1.000000 4546.000000 -21.000000) translate(0,23)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" graphid="g_19e5280" transform="matrix(1.000000 0.000000 0.000000 1.000000 4777.000000 -21.000000) translate(0,23)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2080420" transform="matrix(1.000000 0.000000 0.000000 1.000000 3718.000000 -1149.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b80700" transform="matrix(1.000000 0.000000 0.000000 1.000000 3817.000000 -1083.000000) translate(0,15)">343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_2570c10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4221.000000 -1087.000000) translate(0,20)">35kV谢园T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2748940" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1083.000000) translate(0,15)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2203ba0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3975.000000 -827.000000) translate(0,15)">3513</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28d9940" transform="matrix(1.000000 0.000000 0.000000 1.000000 4064.000000 -1039.000000) translate(0,15)">N1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_208b860" transform="matrix(1.000000 0.000000 0.000000 1.000000 4444.000000 -1039.000000) translate(0,15)">N17</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_18d04a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4703.000000 -735.000000) translate(0,18)"> 35k母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_18d04a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4703.000000 -735.000000) translate(0,40)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e2caa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3642.000000 20.000000) translate(0,15)">ZSSP-2000kVA/35/1kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e2caa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3642.000000 20.000000) translate(0,33)">2000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e2caa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3642.000000 20.000000) translate(0,51)">35±5%/1kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e596a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3868.000000 20.000000) translate(0,15)">ZSSP-2000kVA/35/1kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e596a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3868.000000 20.000000) translate(0,33)">2000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e596a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3868.000000 20.000000) translate(0,51)">35±5%/1kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bb1b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4098.000000 20.000000) translate(0,15)">ZSSP-2000kVA/35/1kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bb1b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4098.000000 20.000000) translate(0,33)">2000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bb1b40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4098.000000 20.000000) translate(0,51)">35±5%/1kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21e72e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4319.000000 20.000000) translate(0,15)">S11-1600kVA/35/0.4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21e72e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4319.000000 20.000000) translate(0,33)">1600kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21e72e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4319.000000 20.000000) translate(0,51)">35±5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eb08a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4768.000000 20.000000) translate(0,15)">S11-315kVA/35/0.4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eb08a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4768.000000 20.000000) translate(0,33)">315kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1eb08a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4768.000000 20.000000) translate(0,51)">35±5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23fd5b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4494.000000 -583.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e4a7f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4492.000000 -513.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c7e50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4492.000000 -651.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d6efe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4766.000000 -511.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e500b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4781.000000 -577.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_270d4b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3722.000000 -353.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce38c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3720.000000 -421.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce2d60" transform="matrix(1.000000 0.000000 0.000000 1.000000 3720.000000 -283.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cbcda0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3948.000000 -352.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce27b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3946.000000 -420.000000) translate(0,12)">3731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a9dc20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3946.000000 -282.000000) translate(0,12)">3736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_253e510" transform="matrix(1.000000 0.000000 0.000000 1.000000 4178.000000 -352.000000) translate(0,12)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2ead0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4176.000000 -420.000000) translate(0,12)">3741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2539490" transform="matrix(1.000000 0.000000 0.000000 1.000000 4176.000000 -282.000000) translate(0,12)">3746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20af270" transform="matrix(1.000000 0.000000 0.000000 1.000000 4401.000000 -351.000000) translate(0,12)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2619180" transform="matrix(1.000000 0.000000 0.000000 1.000000 4399.000000 -281.000000) translate(0,12)">3756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e2bb50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4399.000000 -414.000000) translate(0,12)">3751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e18800" transform="matrix(1.000000 0.000000 0.000000 1.000000 4630.000000 -351.000000) translate(0,12)">376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_253cda0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4628.000000 -281.000000) translate(0,12)">3766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22dc810" transform="matrix(1.000000 0.000000 0.000000 1.000000 4628.000000 -419.000000) translate(0,12)">3761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac8be0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -351.000000) translate(0,12)">377</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2026f40" transform="matrix(1.000000 0.000000 0.000000 1.000000 4855.000000 -422.000000) translate(0,12)">3771</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ad0840" transform="matrix(1.000000 0.000000 0.000000 1.000000 4855.000000 -313.000000) translate(0,12)">3776</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b9830" transform="matrix(1.000000 0.000000 0.000000 1.000000 3628.000000 -472.000000) translate(0,12)">35kV母线</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3927" x2="3957" y1="549" y2="517"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3957" x2="3927" y1="549" y2="517"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="3927" x2="3957" y1="549" y2="517"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2212c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3769.000000 366.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2559f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 351.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2604ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3783.000000 336.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_253d760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.000000 366.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7d380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3982.000000 351.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2556b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 336.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_185d070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4230.000000 366.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1881280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 351.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e02b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 336.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_274a5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 366.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22958d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 351.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eab040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4463.000000 336.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_274b720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4679.000000 366.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_274b150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4668.000000 351.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2295ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4693.000000 336.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e5fa80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.000000 366.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b24a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4893.000000 351.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26100b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4918.000000 336.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21e1a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4553.000000 631.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e552c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4542.000000 616.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2296a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 601.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23f9920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 586.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_238bf00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 572.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16cb5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 557.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1acb8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 542.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2573770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3816,-1049 3713,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="2013@1" ObjectIDZND0="2280@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-13244_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3816,-1049 3713,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2027140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4760,-1049 4871,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4760,-1049 4871,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23b3970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-1049 4098,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="2013@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_16e2050@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_16e2050_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-13244_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-1049 4098,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2431c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-794 3947,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-794 3947,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f0fd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4485,-589 4485,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="12484@1" ObjectIDZND0="12486@0" Pin0InfoVect0LinkObjId="SW-66149_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66145_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4485,-589 4485,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d08480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4485,-562 4485,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="12484@0" ObjectIDZND0="12485@1" Pin0InfoVect0LinkObjId="SW-66148_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66145_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4485,-562 4485,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b3b340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-332 3713,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="12489@0" ObjectIDZND0="12491@1" Pin0InfoVect0LinkObjId="SW-66159_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66152_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-332 3713,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f7af50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-452 3713,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12507@0" ObjectIDZND0="12490@1" Pin0InfoVect0LinkObjId="SW-66158_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1886030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-452 3713,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2687f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-396 3713,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="12490@0" ObjectIDZND0="12489@1" Pin0InfoVect0LinkObjId="SW-66152_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66158_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-396 3713,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21f4ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4098,-1049 4098,-794 4069,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="2013@x" ObjectIDND1="0@x" ObjectIDND2="g_16e2050@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-13244_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_16e2050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4098,-1049 4098,-794 4069,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e99cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4069,-794 4011,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="2013@x" ObjectIDND1="0@x" ObjectIDND2="g_16e2050@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-13244_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_16e2050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4069,-794 4011,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2451e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4069,-794 4069,-533 3942,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" BeginDevType2="lightningRod" ObjectIDND0="2013@x" ObjectIDND1="0@x" ObjectIDND2="g_16e2050@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-13244_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_16e2050_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4069,-794 4069,-533 3942,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22781f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3939,-331 3939,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="12492@0" ObjectIDZND0="12494@1" Pin0InfoVect0LinkObjId="SW-66167_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3939,-331 3939,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_240a140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3939,-452 3939,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12507@0" ObjectIDZND0="12493@1" Pin0InfoVect0LinkObjId="SW-66166_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1886030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3939,-452 3939,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24e9320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3939,-395 3939,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="12493@0" ObjectIDZND0="12492@1" Pin0InfoVect0LinkObjId="SW-66160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66166_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3939,-395 3939,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_188afd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3939,-106 3939,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1701d70@0" ObjectIDZND0="g_b4057c0@0" Pin0InfoVect0LinkObjId="g_b4057c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1701d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3939,-106 3939,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18ab350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-106 3713,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1b9ef10@0" ObjectIDZND0="g_253e8b0@0" Pin0InfoVect0LinkObjId="g_253e8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b9ef10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-106 3713,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29aca20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-239 3744,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_253e8b0@0" ObjectIDND1="12491@x" ObjectIDZND0="g_2955ff0@0" Pin0InfoVect0LinkObjId="g_2955ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_253e8b0_0" Pin1InfoVect1LinkObjId="SW-66159_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-239 3744,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a7c1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-219 3713,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_253e8b0@1" ObjectIDZND0="12491@x" ObjectIDZND1="g_2955ff0@0" Pin0InfoVect0LinkObjId="SW-66159_0" Pin0InfoVect1LinkObjId="g_2955ff0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_253e8b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-219 3713,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ddbc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-239 3713,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_253e8b0@0" ObjectIDND1="g_2955ff0@0" ObjectIDZND0="12491@0" Pin0InfoVect0LinkObjId="SW-66159_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_253e8b0_0" Pin1InfoVect1LinkObjId="g_2955ff0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-239 3713,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_b3f7280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3939,-238 3970,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_b4057c0@0" ObjectIDND1="12494@x" ObjectIDZND0="g_2336430@0" Pin0InfoVect0LinkObjId="g_2336430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_b4057c0_0" Pin1InfoVect1LinkObjId="SW-66167_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3939,-238 3970,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1840f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3939,-219 3939,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_b4057c0@1" ObjectIDZND0="12494@x" ObjectIDZND1="g_2336430@0" Pin0InfoVect0LinkObjId="SW-66167_0" Pin0InfoVect1LinkObjId="g_2336430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_b4057c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3939,-219 3939,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b08e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3939,-238 3939,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_b4057c0@0" ObjectIDND1="g_2336430@0" ObjectIDZND0="12494@0" Pin0InfoVect0LinkObjId="SW-66167_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_b4057c0_0" Pin1InfoVect1LinkObjId="g_2336430_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3939,-238 3939,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21f4520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-331 4169,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="12495@0" ObjectIDZND0="12497@1" Pin0InfoVect0LinkObjId="SW-66175_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-331 4169,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1da71f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-452 4169,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12507@0" ObjectIDZND0="12496@1" Pin0InfoVect0LinkObjId="SW-66174_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1886030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-452 4169,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21e3540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-395 4169,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="12496@0" ObjectIDZND0="12495@1" Pin0InfoVect0LinkObjId="SW-66168_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66174_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-395 4169,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ad60e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-106 4169,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_21dc760@0" ObjectIDZND0="g_22f2fd0@0" Pin0InfoVect0LinkObjId="g_22f2fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21dc760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-106 4169,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2955a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-238 4200,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_22f2fd0@0" ObjectIDND1="12497@x" ObjectIDZND0="g_2614e20@0" Pin0InfoVect0LinkObjId="g_2614e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22f2fd0_0" Pin1InfoVect1LinkObjId="SW-66175_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-238 4200,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251f080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-219 4169,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_22f2fd0@1" ObjectIDZND0="g_2614e20@0" ObjectIDZND1="12497@x" Pin0InfoVect0LinkObjId="g_2614e20_0" Pin0InfoVect1LinkObjId="SW-66175_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f2fd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-219 4169,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24bc4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-238 4169,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2614e20@0" ObjectIDND1="g_22f2fd0@0" ObjectIDZND0="12497@0" Pin0InfoVect0LinkObjId="SW-66175_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2614e20_0" Pin1InfoVect1LinkObjId="g_22f2fd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-238 4169,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18854f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4392,-330 4392,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="12498@0" ObjectIDZND0="12500@1" Pin0InfoVect0LinkObjId="SW-66183_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66176_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4392,-330 4392,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a4f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4392,-452 4392,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12507@0" ObjectIDZND0="12499@1" Pin0InfoVect0LinkObjId="SW-66182_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1886030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4392,-452 4392,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24205b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4392,-389 4392,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="12499@0" ObjectIDZND0="12498@1" Pin0InfoVect0LinkObjId="SW-66176_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4392,-389 4392,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18a1320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4392,-119 4392,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_21a88f0@0" ObjectIDZND0="g_18365b0@0" Pin0InfoVect0LinkObjId="g_18365b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21a88f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4392,-119 4392,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2073070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4392,-237 4423,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_18365b0@0" ObjectIDND1="12500@x" ObjectIDZND0="g_25d27e0@0" Pin0InfoVect0LinkObjId="g_25d27e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18365b0_0" Pin1InfoVect1LinkObjId="SW-66183_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4392,-237 4423,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16c6a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4392,-218 4392,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_18365b0@1" ObjectIDZND0="g_25d27e0@0" ObjectIDZND1="12500@x" Pin0InfoVect0LinkObjId="g_25d27e0_0" Pin0InfoVect1LinkObjId="SW-66183_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18365b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4392,-218 4392,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1eeffc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4392,-237 4392,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_18365b0@0" ObjectIDND1="g_25d27e0@0" ObjectIDZND0="12500@0" Pin0InfoVect0LinkObjId="SW-66183_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18365b0_0" Pin1InfoVect1LinkObjId="g_25d27e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4392,-237 4392,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1886030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4485,-488 4485,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="12485@0" ObjectIDZND0="12507@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66148_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4485,-488 4485,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d329a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-452 4621,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12507@0" ObjectIDZND0="12502@1" Pin0InfoVect0LinkObjId="SW-66186_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1886030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-452 4621,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_257beb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-128 4621,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1cbfaf0@0" ObjectIDZND0="g_1d77fa0@0" Pin0InfoVect0LinkObjId="g_1d77fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cbfaf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-128 4621,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2022630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-218 4621,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d77fa0@1" ObjectIDZND0="12503@0" Pin0InfoVect0LinkObjId="SW-66187_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d77fa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-218 4621,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_299d5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a4b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-334 4848,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="12504@0" ObjectIDZND0="12506@1" Pin0InfoVect0LinkObjId="SW-66192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66188_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-334 4848,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ab6fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-452 4848,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12507@0" ObjectIDZND0="12505@1" Pin0InfoVect0LinkObjId="SW-66191_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1886030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-452 4848,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ddd460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-397 4848,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="12505@0" ObjectIDZND0="12504@1" Pin0InfoVect0LinkObjId="SW-66188_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66191_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-397 4848,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d05b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-171 4848,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_18898b0@0" ObjectIDZND0="g_2613c20@0" Pin0InfoVect0LinkObjId="g_2613c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18898b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-171 4848,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1dbd8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-285 4848,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="12506@0" ObjectIDZND0="g_2613c20@1" Pin0InfoVect0LinkObjId="g_2613c20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-285 4848,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15c8590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-452 4759,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="12507@0" ObjectIDZND0="12487@0" Pin0InfoVect0LinkObjId="SW-66150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1886030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-452 4759,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c3dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-521 4759,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="12487@1" ObjectIDZND0="g_2416020@0" ObjectIDZND1="g_1b09b70@0" ObjectIDZND2="12488@x" Pin0InfoVect0LinkObjId="g_2416020_0" Pin0InfoVect1LinkObjId="g_1b09b70_0" Pin0InfoVect2LinkObjId="SW-66151_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66150_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-521 4759,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_240f080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-552 4705,-552 4705,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="12487@x" ObjectIDND1="g_1b09b70@0" ObjectIDND2="12488@x" ObjectIDZND0="g_2416020@0" Pin0InfoVect0LinkObjId="g_2416020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-66150_0" Pin1InfoVect1LinkObjId="g_1b09b70_0" Pin1InfoVect2LinkObjId="SW-66151_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-552 4705,-552 4705,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2948600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-552 4759,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="12487@x" ObjectIDND1="g_2416020@0" ObjectIDND2="12488@x" ObjectIDZND0="g_1b09b70@0" Pin0InfoVect0LinkObjId="g_1b09b70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-66150_0" Pin1InfoVect1LinkObjId="g_2416020_0" Pin1InfoVect2LinkObjId="SW-66151_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-552 4759,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22d3190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-616 4759,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1b09b70@1" ObjectIDZND0="g_185ccc0@0" Pin0InfoVect0LinkObjId="g_185ccc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b09b70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-616 4759,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2e0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4759,-552 4783,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="12487@x" ObjectIDND1="g_2416020@0" ObjectIDND2="g_1b09b70@0" ObjectIDZND0="12488@0" Pin0InfoVect0LinkObjId="SW-66151_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-66150_0" Pin1InfoVect1LinkObjId="g_2416020_0" Pin1InfoVect2LinkObjId="g_1b09b70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4759,-552 4783,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac2f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4818,-552 4843,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="12488@1" ObjectIDZND0="g_1df0870@0" Pin0InfoVect0LinkObjId="g_1df0870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66151_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4818,-552 4843,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2292e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4098,-1049 4485,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2013@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_16e2050@0" ObjectIDZND2="g_243bd30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_16e2050_0" Pin0InfoVect2LinkObjId="g_243bd30_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-13244_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4098,-1049 4485,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d4f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4485,-1049 4733,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="2013@x" ObjectIDND1="0@x" ObjectIDND2="g_16e2050@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-13244_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_16e2050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4485,-1049 4733,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2683cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4535,-838 4485,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_16e2050@0" ObjectIDZND0="g_243bd30@0" ObjectIDZND1="2013@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_243bd30_0" Pin0InfoVect1LinkObjId="SW-13244_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16e2050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4535,-838 4485,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2968bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4485,-812 4485,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_243bd30@1" ObjectIDZND0="g_16e2050@0" ObjectIDZND1="2013@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_16e2050_0" Pin0InfoVect1LinkObjId="SW-13244_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_243bd30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4485,-812 4485,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206b9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4485,-838 4485,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_16e2050@0" ObjectIDND1="g_243bd30@0" ObjectIDZND0="2013@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-13244_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16e2050_0" Pin1InfoVect1LinkObjId="g_243bd30_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4485,-838 4485,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff8110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4536,-697 4485,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_272f480@0" ObjectIDZND0="g_243bd30@0" ObjectIDZND1="12486@x" Pin0InfoVect0LinkObjId="g_243bd30_0" Pin0InfoVect1LinkObjId="SW-66149_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_272f480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4536,-697 4485,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25fa690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4485,-734 4485,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_243bd30@0" ObjectIDZND0="g_272f480@0" ObjectIDZND1="12486@x" Pin0InfoVect0LinkObjId="g_272f480_0" Pin0InfoVect1LinkObjId="SW-66149_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_243bd30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4485,-734 4485,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2968e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4485,-697 4485,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_272f480@0" ObjectIDND1="g_243bd30@0" ObjectIDZND0="12486@1" Pin0InfoVect0LinkObjId="SW-66149_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_272f480_0" Pin1InfoVect1LinkObjId="g_243bd30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4485,-697 4485,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d13c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-291 4621,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="12503@1" ObjectIDZND0="12501@0" Pin0InfoVect0LinkObjId="SW-66184_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66187_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-291 4621,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206c960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-361 4621,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="12501@1" ObjectIDZND0="12502@0" Pin0InfoVect0LinkObjId="SW-66186_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66184_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-361 4621,-394 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_185ccc0">
    <use class="BV-35KV" transform="matrix(1.903226 -0.000000 0.000000 -2.346154 4729.000000 -623.000000)" xlink:href="#voltageTransformer:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-65761" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3417.000000 -1083.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12219" ObjectName="DYN-CX_DLB"/>
     <cge:Meas_Ref ObjectId="65761"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>