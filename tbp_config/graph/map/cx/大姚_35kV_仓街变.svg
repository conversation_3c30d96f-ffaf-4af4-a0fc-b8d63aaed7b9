<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-230" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-751 -1298 2170 1274">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape137">
    <ellipse cx="18" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="25" x2="28" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="28" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="28" x2="28" y1="17" y2="19"/>
    <ellipse cx="28" cy="16" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="14" x2="20" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="14" y1="19" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="17" x2="20" y1="19" y2="22"/>
    <ellipse cx="17" cy="21" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="13" y2="16"/>
    <ellipse cx="8" cy="14" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="15" x2="18" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="18" x2="18" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="18" y1="6" y2="8"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape21_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape35_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="51" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="16,14 10,26 22,26 16,14 16,15 16,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="51"/>
   </symbol>
   <symbol id="transformer2:shape35_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="81" y2="76"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.37687"/>
    <polyline points="58,100 64,100 " stroke-width="1.37687"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.37687"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape85_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="14" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="14" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="30" y2="30"/>
   </symbol>
   <symbol id="transformer2:shape85_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
   </symbol>
   <symbol id="voltageTransformer:shape33">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="6" y2="6"/>
   </symbol>
   <symbol id="voltageTransformer:shape54">
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="32" y2="23"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a147c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_22a88a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22a44b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a16c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28c7bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28c87a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28c9230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_28c9c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_22eabc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_22eabc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28cd2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28cd2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28cefa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28cefa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_28cfff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d1950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28d24f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28d3360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28d3ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d5330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d5b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d6180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28d6ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d7d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d8700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28d91f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28d9bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28db1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28dbc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_28dce20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28dda90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28e3fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28e4dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_28df430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_28e0960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1284" width="2180" x="-756" y="-1303"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1417" x2="1417" y1="-585" y2="-568"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="24" stroke="rgb(238,238,0)" stroke-width="1" width="11" x="353" y="-1068"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="24" stroke="rgb(0,238,0)" stroke-width="1" width="11" x="-213" y="-654"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-432" y="-1184"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-159981">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 49.971879 -949.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26552" ObjectName="SW-DY_CJ.DY_CJ_3211SW"/>
     <cge:Meas_Ref ObjectId="159981"/>
    <cge:TPSR_Ref TObjectID="26552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159982">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 49.971879 -1076.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26553" ObjectName="SW-DY_CJ.DY_CJ_3216SW"/>
     <cge:Meas_Ref ObjectId="159982"/>
    <cge:TPSR_Ref TObjectID="26553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-159983">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -12.180663 -1137.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26554" ObjectName="SW-DY_CJ.DY_CJ_3219SW"/>
     <cge:Meas_Ref ObjectId="159983"/>
    <cge:TPSR_Ref TObjectID="26554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 927.971879 -955.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26557" ObjectName="SW-DY_CJ.DY_CJ_3221SW"/>
     <cge:Meas_Ref ObjectId="160004"/>
    <cge:TPSR_Ref TObjectID="26557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160003">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 927.971879 -1137.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26556" ObjectName="SW-DY_CJ.DY_CJ_3226SW"/>
     <cge:Meas_Ref ObjectId="160003"/>
    <cge:TPSR_Ref TObjectID="26556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160005">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.021739 994.000000 -961.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26558" ObjectName="SW-DY_CJ.DY_CJ_32217SW"/>
     <cge:Meas_Ref ObjectId="160005"/>
    <cge:TPSR_Ref TObjectID="26558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 49.241796 -857.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26564" ObjectName="SW-DY_CJ.DY_CJ_3011SW"/>
     <cge:Meas_Ref ObjectId="160066"/>
    <cge:TPSR_Ref TObjectID="26564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160074">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 -653.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26567" ObjectName="SW-DY_CJ.DY_CJ_0016SW"/>
     <cge:Meas_Ref ObjectId="160074"/>
    <cge:TPSR_Ref TObjectID="26567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160177">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -85.000000 -273.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26577" ObjectName="SW-DY_CJ.DY_CJ_0216SW"/>
     <cge:Meas_Ref ObjectId="160177"/>
    <cge:TPSR_Ref TObjectID="26577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160176">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -85.000000 -419.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26576" ObjectName="SW-DY_CJ.DY_CJ_0211SW"/>
     <cge:Meas_Ref ObjectId="160176"/>
    <cge:TPSR_Ref TObjectID="26576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160057">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 308.971879 -1034.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26560" ObjectName="SW-DY_CJ.DY_CJ_39017SW"/>
     <cge:Meas_Ref ObjectId="160057"/>
    <cge:TPSR_Ref TObjectID="26560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160056">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 349.819337 -950.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26559" ObjectName="SW-DY_CJ.DY_CJ_3901SW"/>
     <cge:Meas_Ref ObjectId="160056"/>
    <cge:TPSR_Ref TObjectID="26559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 640.971879 -1012.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 851.819337 -1120.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160073">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 47.241796 -551.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26566" ObjectName="SW-DY_CJ.DY_CJ_0011SW"/>
     <cge:Meas_Ref ObjectId="160073"/>
    <cge:TPSR_Ref TObjectID="26566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160075">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -17.847458 -597.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26568" ObjectName="SW-DY_CJ.DY_CJ_00110SW"/>
     <cge:Meas_Ref ObjectId="160075"/>
    <cge:TPSR_Ref TObjectID="26568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160117">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 968.241796 -860.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26570" ObjectName="SW-DY_CJ.DY_CJ_3021SW"/>
     <cge:Meas_Ref ObjectId="160117"/>
    <cge:TPSR_Ref TObjectID="26570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160126">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 940.000000 -615.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26573" ObjectName="SW-DY_CJ.DY_CJ_0026SW"/>
     <cge:Meas_Ref ObjectId="160126"/>
    <cge:TPSR_Ref TObjectID="26573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160125">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 967.241796 -513.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26572" ObjectName="SW-DY_CJ.DY_CJ_0021SW"/>
     <cge:Meas_Ref ObjectId="160125"/>
    <cge:TPSR_Ref TObjectID="26572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160127">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.152542 -559.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26574" ObjectName="SW-DY_CJ.DY_CJ_00210SW"/>
     <cge:Meas_Ref ObjectId="160127"/>
    <cge:TPSR_Ref TObjectID="26574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160178">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -106.847458 -382.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26578" ObjectName="SW-DY_CJ.DY_CJ_02117SW"/>
     <cge:Meas_Ref ObjectId="160178"/>
    <cge:TPSR_Ref TObjectID="26578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160061">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -258.028121 -624.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26562" ObjectName="SW-DY_CJ.DY_CJ_09017SW"/>
     <cge:Meas_Ref ObjectId="160061"/>
    <cge:TPSR_Ref TObjectID="26562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160060">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -244.000000 -540.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26561" ObjectName="SW-DY_CJ.DY_CJ_0901SW"/>
     <cge:Meas_Ref ObjectId="160060"/>
    <cge:TPSR_Ref TObjectID="26561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160195">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 124.000000 -272.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26581" ObjectName="SW-DY_CJ.DY_CJ_0226SW"/>
     <cge:Meas_Ref ObjectId="160195"/>
    <cge:TPSR_Ref TObjectID="26581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160194">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 124.000000 -418.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26580" ObjectName="SW-DY_CJ.DY_CJ_0221SW"/>
     <cge:Meas_Ref ObjectId="160194"/>
    <cge:TPSR_Ref TObjectID="26580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160196">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 102.152542 -381.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26582" ObjectName="SW-DY_CJ.DY_CJ_02217SW"/>
     <cge:Meas_Ref ObjectId="160196"/>
    <cge:TPSR_Ref TObjectID="26582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 -226.028121 -270.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160267">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 987.000000 -271.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26597" ObjectName="SW-DY_CJ.DY_CJ_0266SW"/>
     <cge:Meas_Ref ObjectId="160267"/>
    <cge:TPSR_Ref TObjectID="26597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160266">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 987.000000 -413.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26596" ObjectName="SW-DY_CJ.DY_CJ_0261SW"/>
     <cge:Meas_Ref ObjectId="160266"/>
    <cge:TPSR_Ref TObjectID="26596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160268">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 965.152542 -380.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26598" ObjectName="SW-DY_CJ.DY_CJ_02617SW"/>
     <cge:Meas_Ref ObjectId="160268"/>
    <cge:TPSR_Ref TObjectID="26598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160213">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 332.000000 -269.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26585" ObjectName="SW-DY_CJ.DY_CJ_0236SW"/>
     <cge:Meas_Ref ObjectId="160213"/>
    <cge:TPSR_Ref TObjectID="26585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160212">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 332.000000 -415.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26584" ObjectName="SW-DY_CJ.DY_CJ_0231SW"/>
     <cge:Meas_Ref ObjectId="160212"/>
    <cge:TPSR_Ref TObjectID="26584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160214">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.152542 -378.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26586" ObjectName="SW-DY_CJ.DY_CJ_02317SW"/>
     <cge:Meas_Ref ObjectId="160214"/>
    <cge:TPSR_Ref TObjectID="26586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160231">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 542.000000 -272.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26589" ObjectName="SW-DY_CJ.DY_CJ_0246SW"/>
     <cge:Meas_Ref ObjectId="160231"/>
    <cge:TPSR_Ref TObjectID="26589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160230">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 542.000000 -418.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26588" ObjectName="SW-DY_CJ.DY_CJ_0241SW"/>
     <cge:Meas_Ref ObjectId="160230"/>
    <cge:TPSR_Ref TObjectID="26588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160232">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 520.152542 -381.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26590" ObjectName="SW-DY_CJ.DY_CJ_02417SW"/>
     <cge:Meas_Ref ObjectId="160232"/>
    <cge:TPSR_Ref TObjectID="26590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160249">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 -275.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26593" ObjectName="SW-DY_CJ.DY_CJ_0256SW"/>
     <cge:Meas_Ref ObjectId="160249"/>
    <cge:TPSR_Ref TObjectID="26593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160248">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 -421.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26592" ObjectName="SW-DY_CJ.DY_CJ_0251SW"/>
     <cge:Meas_Ref ObjectId="160248"/>
    <cge:TPSR_Ref TObjectID="26592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160250">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.152542 -384.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26594" ObjectName="SW-DY_CJ.DY_CJ_02517SW"/>
     <cge:Meas_Ref ObjectId="160250"/>
    <cge:TPSR_Ref TObjectID="26594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160285">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.000000 -270.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26601" ObjectName="SW-DY_CJ.DY_CJ_0276SW"/>
     <cge:Meas_Ref ObjectId="160285"/>
    <cge:TPSR_Ref TObjectID="26601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160284">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.000000 -416.363636)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26600" ObjectName="SW-DY_CJ.DY_CJ_0271SW"/>
     <cge:Meas_Ref ObjectId="160284"/>
    <cge:TPSR_Ref TObjectID="26600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160286">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1177.152542 -379.636364)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26602" ObjectName="SW-DY_CJ.DY_CJ_02717SW"/>
     <cge:Meas_Ref ObjectId="160286"/>
    <cge:TPSR_Ref TObjectID="26602"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_CJ.DY_CJ_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-327,-916 1360,-916 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26548" ObjectName="BS-DY_CJ.DY_CJ_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="26548"/></metadata>
   <polyline fill="none" opacity="0" points="-327,-916 1360,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_CJ.DY_CJ_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1345,-494 -334,-494 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26549" ObjectName="BS-DY_CJ.DY_CJ_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="26549"/></metadata>
   <polyline fill="none" opacity="0" points="1345,-494 -334,-494 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_CJ.022Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 151.096774 -165.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34100" ObjectName="EC-DY_CJ.022Ld"/>
    <cge:TPSR_Ref TObjectID="34100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_CJ.021Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -57.903226 -159.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42381" ObjectName="EC-DY_CJ.021Ld"/>
    <cge:TPSR_Ref TObjectID="42381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_CJ.026Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1014.096774 -164.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34104" ObjectName="EC-DY_CJ.026Ld"/>
    <cge:TPSR_Ref TObjectID="34104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_CJ.023Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 359.096774 -162.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34101" ObjectName="EC-DY_CJ.023Ld"/>
    <cge:TPSR_Ref TObjectID="34101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_CJ.024Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 569.096774 -165.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34102" ObjectName="EC-DY_CJ.024Ld"/>
    <cge:TPSR_Ref TObjectID="34102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_CJ.025Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 792.096774 -168.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34103" ObjectName="EC-DY_CJ.025Ld"/>
    <cge:TPSR_Ref TObjectID="34103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_CJ.027Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.096774 -163.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34105" ObjectName="EC-DY_CJ.027Ld"/>
    <cge:TPSR_Ref TObjectID="34105"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_299b0f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.021739 997.000000 -932.347826)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_318cb20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 312.152542 -1081.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f462d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -19.000000 -550.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ef95c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 901.000000 -512.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e0bfe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -130.847458 -382.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_317add0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -254.847458 -675.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3444cf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 78.152542 -381.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31b9bd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 941.152542 -380.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3267f50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 286.152542 -378.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dc3550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 496.152542 -381.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31e2840" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 719.152542 -384.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33044c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.152542 -379.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2f3f9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="59,-954 59,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26552@0" ObjectIDZND0="26548@0" Pin0InfoVect0LinkObjId="g_2d58e20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159981_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="59,-954 59,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f2be90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="59,-1022 59,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26551@0" ObjectIDZND0="26552@1" Pin0InfoVect0LinkObjId="SW-159981_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159979_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="59,-1022 59,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f1a1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="59,-1081 59,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26553@0" ObjectIDZND0="26551@1" Pin0InfoVect0LinkObjId="SW-159979_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159982_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="59,-1081 59,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e5d630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="59,-1117 59,-1193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="26553@1" ObjectIDZND0="26554@x" ObjectIDZND1="34313@1" Pin0InfoVect0LinkObjId="SW-159983_0" Pin0InfoVect1LinkObjId="g_2e5d890_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159982_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="59,-1117 59,-1193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e5d890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="59,-1193 59,-1240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="26553@x" ObjectIDND1="26554@x" ObjectIDZND0="34313@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-159982_0" Pin1InfoVect1LinkObjId="SW-159983_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="59,-1193 59,-1240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d83660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-3,-1178 -3,-1193 59,-1193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="26554@1" ObjectIDZND0="26553@x" ObjectIDZND1="34313@1" Pin0InfoVect0LinkObjId="SW-159982_0" Pin0InfoVect1LinkObjId="g_2e5d890_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159983_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-3,-1178 -3,-1193 59,-1193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d57e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-31,-1116 -31,-1134 -3,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_2d84670@0" ObjectIDZND0="26554@x" ObjectIDZND1="g_2d838c0@0" Pin0InfoVect0LinkObjId="SW-159983_0" Pin0InfoVect1LinkObjId="g_2d838c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d84670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-31,-1116 -31,-1134 -3,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d580a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-3,-1142 -3,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="26554@0" ObjectIDZND0="g_2d84670@0" ObjectIDZND1="g_2d838c0@0" Pin0InfoVect0LinkObjId="g_2d84670_0" Pin0InfoVect1LinkObjId="g_2d838c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-159983_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-3,-1142 -3,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d58300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-3,-1134 -3,-1123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2d84670@0" ObjectIDND1="26554@x" ObjectIDZND0="g_2d838c0@0" Pin0InfoVect0LinkObjId="g_2d838c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d84670_0" Pin1InfoVect1LinkObjId="SW-159983_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-3,-1134 -3,-1123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d58e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-960 937,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26557@0" ObjectIDZND0="26548@0" Pin0InfoVect0LinkObjId="g_2f3f9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="937,-960 937,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_299ae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1003,-966 1003,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26558@1" ObjectIDZND0="g_299b0f0@0" Pin0InfoVect0LinkObjId="g_299b0f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1003,-966 1003,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dddcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="58,-862 58,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26564@0" ObjectIDZND0="26563@1" Pin0InfoVect0LinkObjId="SW-160064_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160066_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="58,-862 58,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dddf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="56,-659 56,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26567@1" ObjectIDZND0="26565@1" Pin0InfoVect0LinkObjId="SW-160071_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160074_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="56,-659 56,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e51c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="57,-727 56,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="26603@0" ObjectIDZND0="26567@0" Pin0InfoVect0LinkObjId="SW-160074_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e51eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="57,-727 56,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e51eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="58,-810 58,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="26563@0" ObjectIDZND0="26603@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160064_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="58,-810 58,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e520e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-49,-336 -49,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26575@0" ObjectIDZND0="26577@0" Pin0InfoVect0LinkObjId="SW-160177_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160174_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-49,-336 -49,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b083f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-49,-363 -49,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26575@1" ObjectIDZND0="26576@x" ObjectIDZND1="26578@x" Pin0InfoVect0LinkObjId="SW-160176_0" Pin0InfoVect1LinkObjId="SW-160178_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160174_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-49,-363 -49,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b08630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-49,-388 -49,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26575@x" ObjectIDND1="26578@x" ObjectIDZND0="26576@1" Pin0InfoVect0LinkObjId="SW-160176_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160174_0" Pin1InfoVect1LinkObjId="SW-160178_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-49,-388 -49,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8eee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="318,-1075 318,-1086 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26560@1" ObjectIDZND0="g_318cb20@0" Pin0InfoVect0LinkObjId="g_318cb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160057_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="318,-1075 318,-1086 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8f140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,-1018 398,-1018 398,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26559@x" ObjectIDND1="26560@x" ObjectIDND2="g_3028a60@0" ObjectIDZND0="g_2f8f860@0" Pin0InfoVect0LinkObjId="g_2f8f860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-160056_0" Pin1InfoVect1LinkObjId="SW-160057_0" Pin1InfoVect2LinkObjId="g_3028a60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,-1018 398,-1018 398,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8f3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,-1081 359,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3028a60@0" ObjectIDZND0="g_2f8f860@0" ObjectIDZND1="26559@x" ObjectIDZND2="26560@x" Pin0InfoVect0LinkObjId="g_2f8f860_0" Pin0InfoVect1LinkObjId="SW-160056_0" Pin0InfoVect2LinkObjId="SW-160057_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3028a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="359,-1081 359,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8f600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,-1018 359,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2f8f860@0" ObjectIDND1="26560@x" ObjectIDND2="g_3028a60@0" ObjectIDZND0="26559@1" Pin0InfoVect0LinkObjId="SW-160056_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2f8f860_0" Pin1InfoVect1LinkObjId="SW-160057_0" Pin1InfoVect2LinkObjId="g_3028a60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,-1018 359,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e9a900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="318,-1039 318,-1018 359,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="26560@0" ObjectIDZND0="g_2f8f860@0" ObjectIDZND1="26559@x" ObjectIDZND2="g_3028a60@0" Pin0InfoVect0LinkObjId="g_2f8f860_0" Pin0InfoVect1LinkObjId="SW-160056_0" Pin0InfoVect2LinkObjId="g_3028a60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160057_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="318,-1039 318,-1018 359,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e9ab60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,-955 359,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26559@0" ObjectIDZND0="26548@0" Pin0InfoVect0LinkObjId="g_2f3f9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160056_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="359,-955 359,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2de6f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="646,-916 646,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26548@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f3f9d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="646,-916 646,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2de8f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="646,-1007 646,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="646,-1007 646,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2de9d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="857,-1098 857,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_2de9170@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2de9170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="857,-1098 857,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e25680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-1178 937,-1196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="26556@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2d713d0@0" ObjectIDZND2="38065@1" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2d713d0_0" Pin0InfoVect2LinkObjId="g_2e258e0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160003_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="937,-1178 937,-1196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e258e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-1196 937,-1237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="26556@x" ObjectIDND1="0@x" ObjectIDND2="g_2d713d0@0" ObjectIDZND0="38065@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-160003_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2d713d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="937,-1196 937,-1237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e25b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-1196 857,-1196 857,-1170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="26556@x" ObjectIDND1="g_2d713d0@0" ObjectIDND2="38065@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-160003_0" Pin1InfoVect1LinkObjId="g_2d713d0_0" Pin1InfoVect2LinkObjId="g_2e258e0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="937,-1196 857,-1196 857,-1170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e25da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1003,-1003 1003,-1019 937,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26558@0" ObjectIDZND0="26555@x" ObjectIDZND1="26557@x" Pin0InfoVect0LinkObjId="SW-160001_0" Pin0InfoVect1LinkObjId="SW-160004_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160005_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1003,-1003 1003,-1019 937,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e26000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-1048 937,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26555@0" ObjectIDZND0="26558@x" ObjectIDZND1="26557@x" Pin0InfoVect0LinkObjId="SW-160005_0" Pin0InfoVect1LinkObjId="SW-160004_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160001_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="937,-1048 937,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e26260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-1019 937,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26558@x" ObjectIDND1="26555@x" ObjectIDZND0="26557@1" Pin0InfoVect0LinkObjId="SW-160004_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160005_0" Pin1InfoVect1LinkObjId="SW-160001_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="937,-1019 937,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e264c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="58,-898 59,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26564@1" ObjectIDZND0="26548@0" Pin0InfoVect0LinkObjId="g_2f3f9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="58,-898 59,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e913d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="56,-556 56,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26566@0" ObjectIDZND0="26549@0" Pin0InfoVect0LinkObjId="g_2f1b590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160073_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="56,-556 56,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e91630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="56,-603 23,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26565@x" ObjectIDND1="26566@x" ObjectIDZND0="26568@1" Pin0InfoVect0LinkObjId="SW-160075_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160071_0" Pin1InfoVect1LinkObjId="SW-160073_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="56,-603 23,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eefd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="56,-612 56,-603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26565@0" ObjectIDZND0="26566@x" ObjectIDZND1="26568@x" Pin0InfoVect0LinkObjId="SW-160073_0" Pin0InfoVect1LinkObjId="SW-160075_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160071_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="56,-612 56,-603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2eeff80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="56,-603 56,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26565@x" ObjectIDND1="26568@x" ObjectIDZND0="26566@1" Pin0InfoVect0LinkObjId="SW-160073_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160071_0" Pin1InfoVect1LinkObjId="SW-160075_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="56,-603 56,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e18d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-13,-603 -13,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26568@0" ObjectIDZND0="g_2f462d0@0" Pin0InfoVect0LinkObjId="g_2f462d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160075_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-13,-603 -13,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e18f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-13,-580 -13,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26568@x" ObjectIDZND0="g_2f462d0@0" Pin0InfoVect0LinkObjId="g_2f462d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160075_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-13,-580 -13,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e191e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,-580 39,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="earth" ObjectIDND0="26568@x" ObjectIDND1="g_2f462d0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160075_0" Pin1InfoVect1LinkObjId="g_2f462d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="39,-580 39,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e19cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,-573 39,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="earth" ObjectIDZND0="26568@x" ObjectIDZND1="g_2f462d0@0" Pin0InfoVect0LinkObjId="SW-160075_0" Pin0InfoVect1LinkObjId="g_2f462d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="39,-573 39,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e19f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,-580 -13,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="earth" ObjectIDZND0="26568@x" ObjectIDZND1="g_2f462d0@0" Pin0InfoVect0LinkObjId="SW-160075_0" Pin0InfoVect1LinkObjId="g_2f462d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="39,-580 -13,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d74fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-621 976,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26573@1" ObjectIDZND0="26571@1" Pin0InfoVect0LinkObjId="SW-160123_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160126_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-621 976,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e8aea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-565 943,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26571@x" ObjectIDND1="26572@x" ObjectIDZND0="26574@1" Pin0InfoVect0LinkObjId="SW-160127_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160123_0" Pin1InfoVect1LinkObjId="SW-160125_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-565 943,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e8b100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-574 976,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26571@0" ObjectIDZND0="26574@x" ObjectIDZND1="26572@x" Pin0InfoVect0LinkObjId="SW-160127_0" Pin0InfoVect1LinkObjId="SW-160125_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160123_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="976,-574 976,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e8b360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-565 976,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26574@x" ObjectIDND1="26571@x" ObjectIDZND0="26572@1" Pin0InfoVect0LinkObjId="SW-160125_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160127_0" Pin1InfoVect1LinkObjId="SW-160123_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-565 976,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e46570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-565 907,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26574@0" ObjectIDZND0="g_2ef95c0@0" Pin0InfoVect0LinkObjId="g_2ef95c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="907,-565 907,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e467d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="907,-542 907,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26574@x" ObjectIDZND0="g_2ef95c0@0" Pin0InfoVect0LinkObjId="g_2ef95c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="907,-542 907,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e46a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="959,-542 959,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="earth" ObjectIDND0="26574@x" ObjectIDND1="g_2ef95c0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160127_0" Pin1InfoVect1LinkObjId="g_2ef95c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="959,-542 959,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e46c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="959,-535 959,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="earth" ObjectIDZND0="26574@x" ObjectIDZND1="g_2ef95c0@0" Pin0InfoVect0LinkObjId="SW-160127_0" Pin0InfoVect1LinkObjId="g_2ef95c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="959,-535 959,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e46ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="959,-542 907,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="earth" ObjectIDZND0="26574@x" ObjectIDZND1="g_2ef95c0@0" Pin0InfoVect0LinkObjId="SW-160127_0" Pin0InfoVect1LinkObjId="g_2ef95c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="959,-542 907,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e0e6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="977,-865 977,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26570@0" ObjectIDZND0="26569@1" Pin0InfoVect0LinkObjId="SW-160115_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160117_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="977,-865 977,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e0e900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-665 976,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2e0d950@1" ObjectIDZND0="26573@0" Pin0InfoVect0LinkObjId="SW-160126_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e0d950_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-665 976,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f1b590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-49,-460 -49,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26576@0" ObjectIDZND0="26549@0" Pin0InfoVect0LinkObjId="g_2e913d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160176_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-49,-460 -49,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f1b7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-66,-388 -49,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26578@1" ObjectIDZND0="26575@x" ObjectIDZND1="26576@x" Pin0InfoVect0LinkObjId="SW-160174_0" Pin0InfoVect1LinkObjId="SW-160176_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160178_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-66,-388 -49,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e0ca70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-102,-388 -113,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26578@0" ObjectIDZND0="g_2e0bfe0@0" Pin0InfoVect0LinkObjId="g_2e0bfe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160178_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-102,-388 -113,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fc8980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-208,-545 -208,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26561@1" ObjectIDZND0="26549@0" Pin0InfoVect0LinkObjId="g_2e913d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-208,-545 -208,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dc7380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="160,-335 160,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26579@0" ObjectIDZND0="26581@0" Pin0InfoVect0LinkObjId="SW-160195_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="160,-335 160,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3199510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="160,-362 160,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26579@1" ObjectIDZND0="26580@x" ObjectIDZND1="26582@x" Pin0InfoVect0LinkObjId="SW-160194_0" Pin0InfoVect1LinkObjId="SW-160196_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160192_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="160,-362 160,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3199770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="160,-387 160,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26579@x" ObjectIDND1="26582@x" ObjectIDZND0="26580@1" Pin0InfoVect0LinkObjId="SW-160194_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160192_0" Pin1InfoVect1LinkObjId="SW-160196_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="160,-387 160,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32e5850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="160,-459 160,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26580@0" ObjectIDZND0="26549@0" Pin0InfoVect0LinkObjId="g_2e913d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160194_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="160,-459 160,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32e5ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="143,-387 160,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26582@1" ObjectIDZND0="26579@x" ObjectIDZND1="26580@x" Pin0InfoVect0LinkObjId="SW-160192_0" Pin0InfoVect1LinkObjId="SW-160194_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160196_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="143,-387 160,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3445780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="107,-387 96,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26582@0" ObjectIDZND0="g_3444cf0@0" Pin0InfoVect0LinkObjId="g_3444cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160196_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="107,-387 96,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3446270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-16,-259 -49,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_2b07740@0" ObjectIDZND0="26577@x" ObjectIDZND1="42381@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-160177_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.021Ld_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b07740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-16,-259 -49,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_254c1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-49,-278 -49,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="26577@1" ObjectIDZND0="g_2b07740@0" ObjectIDZND1="42381@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2b07740_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.021Ld_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160177_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-49,-278 -49,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33fb1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-219,-265 -219,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-219,-265 -219,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fbba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-49,-202 -221,-202 -221,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="42381@x" ObjectIDND1="g_2b07740@0" ObjectIDND2="26577@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-DY_CJ.021Ld_0" Pin1InfoVect1LinkObjId="g_2b07740_0" Pin1InfoVect2LinkObjId="SW-160177_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-49,-202 -221,-202 -221,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_301a160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-267 160,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_301b0e0@0" ObjectIDZND0="26581@x" ObjectIDZND1="34100@x" Pin0InfoVect0LinkObjId="SW-160195_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.022Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_301b0e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="193,-267 160,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_301ac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="160,-277 160,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26581@1" ObjectIDZND0="g_301b0e0@0" ObjectIDZND1="34100@x" Pin0InfoVect0LinkObjId="g_301b0e0_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.022Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160195_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="160,-277 160,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_301ae80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="160,-267 160,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_301b0e0@0" ObjectIDND1="26581@x" ObjectIDZND0="34100@0" Pin0InfoVect0LinkObjId="EC-DY_CJ.022Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_301b0e0_0" Pin1InfoVect1LinkObjId="SW-160195_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="160,-267 160,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_301be10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-334 1023,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26595@0" ObjectIDZND0="26597@0" Pin0InfoVect0LinkObjId="SW-160267_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160264_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-334 1023,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41116b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-361 1023,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26595@1" ObjectIDZND0="26596@x" ObjectIDZND1="26598@x" Pin0InfoVect0LinkObjId="SW-160266_0" Pin0InfoVect1LinkObjId="SW-160268_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160264_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-361 1023,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4111910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-386 1023,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26595@x" ObjectIDND1="26598@x" ObjectIDZND0="26596@1" Pin0InfoVect0LinkObjId="SW-160266_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160264_0" Pin1InfoVect1LinkObjId="SW-160268_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-386 1023,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ba9c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-454 1023,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26596@0" ObjectIDZND0="26549@0" Pin0InfoVect0LinkObjId="g_2e913d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160266_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-454 1023,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ba9e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1006,-386 1023,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26598@1" ObjectIDZND0="26595@x" ObjectIDZND1="26596@x" Pin0InfoVect0LinkObjId="SW-160264_0" Pin0InfoVect1LinkObjId="SW-160266_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160268_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1006,-386 1023,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ba660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="970,-386 959,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26598@0" ObjectIDZND0="g_31b9bd0@0" Pin0InfoVect0LinkObjId="g_31b9bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160268_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="970,-386 959,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31ba8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-266 1023,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_31bafe0@0" ObjectIDZND0="26597@x" ObjectIDZND1="34104@x" Pin0InfoVect0LinkObjId="SW-160267_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.026Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31bafe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-266 1023,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31bab20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-276 1023,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26597@1" ObjectIDZND0="g_31bafe0@0" ObjectIDZND1="34104@x" Pin0InfoVect0LinkObjId="g_31bafe0_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.026Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160267_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-276 1023,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31bad80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1023,-266 1023,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_31bafe0@0" ObjectIDND1="26597@x" ObjectIDZND0="34104@0" Pin0InfoVect0LinkObjId="EC-DY_CJ.026Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_31bafe0_0" Pin1InfoVect1LinkObjId="SW-160267_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1023,-266 1023,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_346bb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="368,-332 368,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26583@0" ObjectIDZND0="26585@0" Pin0InfoVect0LinkObjId="SW-160213_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="368,-332 368,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_298e5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="368,-359 368,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26583@1" ObjectIDZND0="26584@x" ObjectIDZND1="26586@x" Pin0InfoVect0LinkObjId="SW-160212_0" Pin0InfoVect1LinkObjId="SW-160214_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="368,-359 368,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_298e800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="368,-384 368,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26583@x" ObjectIDND1="26586@x" ObjectIDZND0="26584@1" Pin0InfoVect0LinkObjId="SW-160212_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160210_0" Pin1InfoVect1LinkObjId="SW-160214_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="368,-384 368,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342dce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="368,-456 368,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26584@0" ObjectIDZND0="26549@0" Pin0InfoVect0LinkObjId="g_2e913d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160212_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="368,-456 368,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_342df40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="351,-384 368,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26586@1" ObjectIDZND0="26583@x" ObjectIDZND1="26584@x" Pin0InfoVect0LinkObjId="SW-160210_0" Pin0InfoVect1LinkObjId="SW-160212_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160214_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="351,-384 368,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32689e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-384 304,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26586@0" ObjectIDZND0="g_3267f50@0" Pin0InfoVect0LinkObjId="g_3267f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160214_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,-384 304,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3268c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-264 368,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3269360@0" ObjectIDZND0="26585@x" ObjectIDZND1="34101@x" Pin0InfoVect0LinkObjId="SW-160213_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.023Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3269360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="401,-264 368,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3268ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="368,-274 368,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26585@1" ObjectIDZND0="g_3269360@0" ObjectIDZND1="34101@x" Pin0InfoVect0LinkObjId="g_3269360_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.023Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160213_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="368,-274 368,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3269100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="368,-264 368,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3269360@0" ObjectIDND1="26585@x" ObjectIDZND0="34101@0" Pin0InfoVect0LinkObjId="EC-DY_CJ.023Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3269360_0" Pin1InfoVect1LinkObjId="SW-160213_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="368,-264 368,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3283730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-335 578,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26587@0" ObjectIDZND0="26589@0" Pin0InfoVect0LinkObjId="SW-160231_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160228_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-335 578,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_327d0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-362 578,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26587@1" ObjectIDZND0="26588@x" ObjectIDZND1="26590@x" Pin0InfoVect0LinkObjId="SW-160230_0" Pin0InfoVect1LinkObjId="SW-160232_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160228_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="578,-362 578,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_327d330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-387 578,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26587@x" ObjectIDND1="26590@x" ObjectIDZND0="26588@1" Pin0InfoVect0LinkObjId="SW-160230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160228_0" Pin1InfoVect1LinkObjId="SW-160232_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-387 578,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e8220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-459 578,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26588@0" ObjectIDZND0="26549@0" Pin0InfoVect0LinkObjId="g_2e913d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-459 578,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e8480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,-387 578,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26590@1" ObjectIDZND0="26588@x" ObjectIDZND1="26587@x" Pin0InfoVect0LinkObjId="SW-160230_0" Pin0InfoVect1LinkObjId="SW-160228_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160232_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="561,-387 578,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dc3fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="525,-387 514,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26590@0" ObjectIDZND0="g_2dc3550@0" Pin0InfoVect0LinkObjId="g_2dc3550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160232_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="525,-387 514,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dc4240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="611,-267 578,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2dc4960@0" ObjectIDZND0="26589@x" ObjectIDZND1="34102@x" Pin0InfoVect0LinkObjId="SW-160231_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.024Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dc4960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="611,-267 578,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dc44a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-277 578,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26589@1" ObjectIDZND0="g_2dc4960@0" ObjectIDZND1="34102@x" Pin0InfoVect0LinkObjId="g_2dc4960_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.024Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160231_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="578,-277 578,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dc4700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-267 578,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="26589@x" ObjectIDND1="g_2dc4960@0" ObjectIDZND0="34102@0" Pin0InfoVect0LinkObjId="EC-DY_CJ.024Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160231_0" Pin1InfoVect1LinkObjId="g_2dc4960_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-267 578,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25117f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-338 801,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26591@0" ObjectIDZND0="26593@0" Pin0InfoVect0LinkObjId="SW-160249_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160246_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-338 801,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31f1490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-365 801,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26591@1" ObjectIDZND0="26592@x" ObjectIDZND1="26594@x" Pin0InfoVect0LinkObjId="SW-160248_0" Pin0InfoVect1LinkObjId="SW-160250_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160246_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="801,-365 801,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31f16d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-390 801,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26591@x" ObjectIDND1="26594@x" ObjectIDZND0="26592@1" Pin0InfoVect0LinkObjId="SW-160248_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160246_0" Pin1InfoVect1LinkObjId="SW-160250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-390 801,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3367330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-462 801,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26592@0" ObjectIDZND0="26549@0" Pin0InfoVect0LinkObjId="g_2e913d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160248_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-462 801,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3367590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-390 801,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26594@1" ObjectIDZND0="26591@x" ObjectIDZND1="26592@x" Pin0InfoVect0LinkObjId="SW-160246_0" Pin0InfoVect1LinkObjId="SW-160248_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="784,-390 801,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e32d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="748,-390 737,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26594@0" ObjectIDZND0="g_31e2840@0" Pin0InfoVect0LinkObjId="g_31e2840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="748,-390 737,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e3530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,-270 801,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_30c9b10@0" ObjectIDZND0="26593@x" ObjectIDZND1="34103@x" Pin0InfoVect0LinkObjId="SW-160249_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.025Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30c9b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="834,-270 801,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e3790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-280 801,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26593@1" ObjectIDZND0="g_30c9b10@0" ObjectIDZND1="34103@x" Pin0InfoVect0LinkObjId="g_30c9b10_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.025Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="801,-280 801,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31e39f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,-270 801,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_30c9b10@0" ObjectIDND1="26593@x" ObjectIDZND0="34103@0" Pin0InfoVect0LinkObjId="EC-DY_CJ.025Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30c9b10_0" Pin1InfoVect1LinkObjId="SW-160249_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="801,-270 801,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30cc4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1235,-333 1235,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26599@0" ObjectIDZND0="26601@0" Pin0InfoVect0LinkObjId="SW-160285_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160282_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1235,-333 1235,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3384c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1235,-360 1235,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26599@1" ObjectIDZND0="26600@x" ObjectIDZND1="26602@x" Pin0InfoVect0LinkObjId="SW-160284_0" Pin0InfoVect1LinkObjId="SW-160286_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1235,-360 1235,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3384ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1235,-385 1235,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26599@x" ObjectIDND1="26602@x" ObjectIDZND0="26600@1" Pin0InfoVect0LinkObjId="SW-160284_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160282_0" Pin1InfoVect1LinkObjId="SW-160286_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1235,-385 1235,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31cd350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1235,-457 1235,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26600@0" ObjectIDZND0="26549@0" Pin0InfoVect0LinkObjId="g_2e913d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160284_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1235,-457 1235,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31cd5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1218,-385 1235,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26602@1" ObjectIDZND0="26600@x" ObjectIDZND1="26599@x" Pin0InfoVect0LinkObjId="SW-160284_0" Pin0InfoVect1LinkObjId="SW-160282_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1218,-385 1235,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3304f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1182,-385 1171,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26602@0" ObjectIDZND0="g_33044c0@0" Pin0InfoVect0LinkObjId="g_33044c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160286_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1182,-385 1171,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33051b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1268,-265 1235,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3e18340@0" ObjectIDZND0="26601@x" ObjectIDZND1="34105@x" Pin0InfoVect0LinkObjId="SW-160285_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.027Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e18340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1268,-265 1235,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3305410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1235,-275 1235,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26601@1" ObjectIDZND0="g_3e18340@0" ObjectIDZND1="34105@x" Pin0InfoVect0LinkObjId="g_3e18340_0" Pin0InfoVect1LinkObjId="EC-DY_CJ.027Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160285_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1235,-275 1235,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3305670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1235,-265 1235,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="26601@x" ObjectIDND1="g_3e18340@0" ObjectIDZND0="34105@0" Pin0InfoVect0LinkObjId="EC-DY_CJ.027Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160285_0" Pin1InfoVect1LinkObjId="g_3e18340_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1235,-265 1235,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e1a5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="977,-901 977,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26570@1" ObjectIDZND0="26548@0" Pin0InfoVect0LinkObjId="g_2f3f9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160117_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="977,-901 977,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e1ae00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-518 976,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26572@0" ObjectIDZND0="26549@0" Pin0InfoVect0LinkObjId="g_2e913d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160125_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-518 976,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32e1460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="977,-822 977,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="26569@0" ObjectIDZND0="26604@1" Pin0InfoVect0LinkObjId="g_32e16c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160115_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="977,-822 977,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32e16c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-718 977,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2e0d950@0" ObjectIDZND0="26604@0" Pin0InfoVect0LinkObjId="g_32e1460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e0d950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-718 977,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3023ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-249,-680 -249,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_317add0@0" ObjectIDZND0="26562@1" Pin0InfoVect0LinkObjId="SW-160061_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_317add0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-249,-680 -249,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3023ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-208,-611 -249,-611 -249,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="26561@x" ObjectIDND1="g_32e92a0@0" ObjectIDZND0="26562@0" Pin0InfoVect0LinkObjId="SW-160061_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160060_0" Pin1InfoVect1LinkObjId="g_32e92a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-208,-611 -249,-611 -249,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3024670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-208,-664 -208,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_32e92a0@0" ObjectIDZND0="26562@x" ObjectIDZND1="26561@x" Pin0InfoVect0LinkObjId="SW-160061_0" Pin0InfoVect1LinkObjId="SW-160060_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32e92a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-208,-664 -208,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30248d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-208,-611 -208,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="26562@x" ObjectIDND1="g_32e92a0@0" ObjectIDZND0="26561@0" Pin0InfoVect0LinkObjId="SW-160060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-160061_0" Pin1InfoVect1LinkObjId="g_32e92a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-208,-611 -208,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32e7cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="937,-1075 937,-1142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26555@1" ObjectIDZND0="26556@0" Pin0InfoVect0LinkObjId="SW-160003_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-160001_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="937,-1075 937,-1142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32e7f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-1171 988,-1196 937,-1196 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_2d713d0@0" ObjectIDZND0="26556@x" ObjectIDZND1="0@x" ObjectIDZND2="38065@1" Pin0InfoVect0LinkObjId="SW-160003_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2e258e0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d713d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="988,-1171 988,-1196 937,-1196 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32e8c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-49,-186 -49,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="42381@0" ObjectIDZND0="g_2b07740@0" ObjectIDZND1="26577@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2b07740_0" Pin0InfoVect1LinkObjId="SW-160177_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-DY_CJ.021Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-49,-186 -49,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32e8ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-49,-259 -49,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2b07740@0" ObjectIDND1="26577@x" ObjectIDZND0="42381@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="EC-DY_CJ.021Ld_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b07740_0" Pin1InfoVect1LinkObjId="SW-160177_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-49,-259 -49,-202 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="26548" cx="59" cy="-916" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26548" cx="937" cy="-916" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26548" cx="359" cy="-916" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26549" cx="-49" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26549" cx="578" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26549" cx="160" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26549" cx="368" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26549" cx="801" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26549" cx="1023" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26549" cx="1235" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26548" cx="977" cy="-916" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26549" cx="976" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26549" cx="-208" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26548" cx="646" cy="-916" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26549" cx="56" cy="-494" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26548" cx="59" cy="-916" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153547" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -483.500000 -1159.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26037" ObjectName="DYN-DY_CJ"/>
     <cge:Meas_Ref ObjectId="153547"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f3f710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 595.096525 -1151.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d58560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 31.000000 -1298.000000) translate(0,15)">大仓南线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d72100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -1290.000000) translate(0,15)">中仓线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f31cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 316.000000 -1136.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2e26bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -559.000000 -1243.500000) translate(0,16)">仓街变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8ba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8ba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8ba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,59)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8ba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8ba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,101)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8ba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8ba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,143)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8ba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8ba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,185)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8ba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8ba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -1102.000000) translate(0,227)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d8bd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -708.000000 -664.000000) translate(0,374)">联系方式：0878-6148332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_317b860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -725.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_254c420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -272.903475 -409.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32e1920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -323.000000 -946.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32e1e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 524.000000 -522.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e2050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 74.000000 -1042.000000) translate(0,12)">321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e2290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 66.000000 -1107.000000) translate(0,12)">3216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e24d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 66.000000 -979.000000) translate(0,12)">3211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e2710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5.000000 -1169.000000) translate(0,12)">3219</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e2950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 370.000000 -978.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e2b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 267.000000 -1065.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e2dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -1070.000000) translate(0,12)">322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e3010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 945.000000 -986.000000) translate(0,12)">3221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e3250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 887.000000 -1167.000000) translate(0,12)">3226</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e3490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1013.000000 -989.000000) translate(0,12)">32217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e36d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 73.000000 -830.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e3910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 70.000000 -885.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e3b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 65.000000 -633.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e3d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 68.000000 -681.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e3fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 67.000000 -581.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e4210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -12.000000 -629.000000) translate(0,12)">00110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e4450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 994.000000 -843.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32e4690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 987.000000 -889.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349a9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 995.000000 -594.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349ac10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -642.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349ae50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 -538.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349b090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 909.000000 -592.000000) translate(0,12)">00210</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349b2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -201.000000 -570.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349b510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -303.000000 -656.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349b750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -34.000000 -359.000000) translate(0,12)">021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349b990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -38.000000 -449.000000) translate(0,12)">0211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349bbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -37.000000 -300.000000) translate(0,12)">0216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349be10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -104.000000 -414.000000) translate(0,12)">02117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349c050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -353.000000) translate(0,12)">022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349c290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 171.000000 -302.000000) translate(0,12)">0226</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349c4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 172.000000 -449.000000) translate(0,12)">0221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349c710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 108.000000 -414.000000) translate(0,12)">02217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349c950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 384.000000 -352.000000) translate(0,12)">023</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349cb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 -297.000000) translate(0,12)">0236</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349cdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 -445.000000) translate(0,12)">0231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349d010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -410.000000) translate(0,12)">02317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349d250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 594.000000 -354.000000) translate(0,12)">024</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349d490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -303.000000) translate(0,12)">0246</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349d6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 -447.000000) translate(0,12)">0241</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349d910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 522.000000 -412.000000) translate(0,12)">02417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_349db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 816.000000 -359.000000) translate(0,12)">025</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31eb4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 812.000000 -301.000000) translate(0,12)">0256</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31eb710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 812.000000 -449.000000) translate(0,12)">0251</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31eb950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 748.000000 -416.000000) translate(0,12)">02517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ebb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1035.000000 -355.000000) translate(0,12)">026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ebdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1033.000000 -299.000000) translate(0,12)">0266</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ec010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1034.000000 -447.000000) translate(0,12)">0261</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ec250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -412.000000) translate(0,12)">02617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ec490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1249.000000 -354.000000) translate(0,12)">027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ec6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -297.000000) translate(0,12)">0276</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ec910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1244.000000 -446.000000) translate(0,12)">0271</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ecb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 -410.000000) translate(0,12)">02717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbf420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -97.500000 -738.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3024b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99.000000 -737.000000) translate(0,15)">S7-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3024b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99.000000 -737.000000) translate(0,33)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3024b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99.000000 -737.000000) translate(0,51)">6.8%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3025f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1018.000000 -756.000000) translate(0,15)">SZ9-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3025f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1018.000000 -756.000000) translate(0,33)">35±3*2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3025f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1018.000000 -756.000000) translate(0,51)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3025f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1018.000000 -756.000000) translate(0,69)">7.14%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3027970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -700.000000 -835.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_30281f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -443.000000 -1226.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3028770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -443.000000 -1261.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3329090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 138.000000 -157.000000) translate(0,15)">仓钟线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3329850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 -153.000000) translate(0,15)">仓将线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3329db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 559.000000 -154.000000) translate(0,15)">惠丰线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31ac360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 780.000000 -158.000000) translate(0,15)">仓海线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31ac8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 994.000000 -157.000000) translate(0,15)">仓连线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_31ace20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1206.000000 -159.000000) translate(0,15)">仓槐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31ad460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -751.000000 -264.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_31ad750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -601.000000 -274.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_31ad750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -601.000000 -274.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e1bb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 879.000000 -813.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e1d1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -55.000000 -775.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3e1d910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -415.500000 -1173.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e1ddd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -92.000000 -154.000000) translate(0,15)">南仓联络线</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="-49,-225 -54,-215 -44,-215 -49,-225 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="-49,-237 -54,-247 -44,-247 -49,-237 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="160,-245 155,-255 165,-255 160,-245 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="160,-233 155,-223 165,-223 160,-233 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="578,-225 573,-215 583,-215 578,-225 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="578,-237 573,-247 583,-247 578,-237 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="367,-240 362,-250 372,-250 367,-240 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="367,-228 362,-218 372,-218 367,-228 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="801,-229 796,-219 806,-219 801,-229 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="801,-241 796,-251 806,-251 801,-241 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1023,-228 1018,-218 1028,-218 1023,-228 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1023,-240 1018,-250 1028,-250 1023,-240 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1235,-228 1230,-218 1240,-218 1235,-228 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1235,-240 1230,-250 1240,-250 1235,-240 " stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2d838c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -11.000000 -1057.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2de9170">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 848.000000 -1067.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 630.152542 -1126.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 630.152542 -1126.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 -234.847458 -383.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 -234.847458 -383.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_CJ.DY_CJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="37666"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.726285 -0.000000 0.000000 -0.754372 949.000000 -737.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.726285 -0.000000 0.000000 -0.754372 949.000000 -737.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26604" ObjectName="TF-DY_CJ.DY_CJ_2T"/>
    <cge:TPSR_Ref TObjectID="26604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_CJ.DY_CJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="37662"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.740000 -0.000000 0.000000 -0.755556 39.000000 -723.000000)" xlink:href="#transformer2:shape85_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.740000 -0.000000 0.000000 -0.755556 39.000000 -723.000000)" xlink:href="#transformer2:shape85_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26603" ObjectName="TF-DY_CJ.DY_CJ_1T"/>
    <cge:TPSR_Ref TObjectID="26603"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -657.000000 -1196.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-159886" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -616.000000 -1104.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159886" ObjectName="DY_CJ:DY_CJ_3ⅠM_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-160315" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -593.000000 -975.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160315" ObjectName="DY_CJ:DY_CJ_YGZJ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-160316" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -593.000000 -934.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160316" ObjectName="DY_CJ:DY_CJ_WGZJ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-160315" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -595.000000 -1060.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160315" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-160315" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -592.000000 -1017.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160315" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-605" y="-1254"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-605" y="-1254"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-654" y="-1271"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-654" y="-1271"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="74" y="-1042"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="74" y="-1042"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="952" y="-1070"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="952" y="-1070"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="176" y="-353"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="176" y="-353"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="384" y="-352"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="384" y="-352"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="594" y="-354"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="594" y="-354"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="816" y="-359"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="816" y="-359"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1035" y="-355"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1035" y="-355"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1249" y="-354"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1249" y="-354"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="-34" y="-359"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="-34" y="-359"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="79" x="-701" y="-835"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="79" x="-701" y="-835"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-454" y="-1234"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-454" y="-1234"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-454" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-454" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="879" y="-813"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="879" y="-813"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="-55" y="-775"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="-55" y="-775"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-432" y="-1185"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-432" y="-1185"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ece80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -82.000000 91.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ed100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -82.000000 74.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ed340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -82.000000 56.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ed580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -78.000000 39.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ed7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -96.000000 128.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31eda00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -107.500000 109.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31edd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1273.000000 1013.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31edf70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 1066.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ee1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.000000 978.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ee3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.000000 960.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ee630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.000000 943.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f7030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 1047.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f7270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.000000 996.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f74b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 1030.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f77e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 868.000000 785.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f7a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 838.500000 754.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f7d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.000000 590.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f7fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1255.000000 643.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f81f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1247.000000 555.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f8430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1247.000000 537.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f8670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1263.000000 520.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f88b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1255.000000 624.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f8af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1247.000000 573.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f8d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1255.000000 607.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a75c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -50.000000 995.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a7850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -50.000000 978.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a7a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -50.000000 960.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a7cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -46.000000 943.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a7f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -64.000000 1032.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a8150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -75.500000 1013.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a8480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 838.000000 993.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a8700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 838.000000 976.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a8940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 838.000000 958.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a8b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 941.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a8dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 824.000000 1030.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a9000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 812.500000 1011.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a9330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 138.000000 840.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a95b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 138.000000 823.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a97f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 138.000000 805.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a9a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 142.000000 788.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28a9c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 124.000000 877.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3946560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 112.500000 858.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39468a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 138.000000 604.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3946b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 138.000000 587.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3946d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 138.000000 569.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3946fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 142.000000 552.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39471e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 124.000000 641.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3947420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 112.500000 622.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3947750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1053.000000 866.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39479d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1053.000000 849.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3947c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1053.000000 831.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3947e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1057.000000 814.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3948090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1039.000000 903.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39482d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1027.500000 884.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3948600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1058.000000 610.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3948880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1058.000000 593.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3948ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1058.000000 575.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3948d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1062.000000 558.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3948f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1044.000000 647.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3949180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1032.500000 628.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39494b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 132.000000 95.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3949730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 132.000000 78.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3949970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 132.000000 60.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297d930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 136.000000 43.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297db70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 118.000000 132.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297ddb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 106.500000 113.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297e0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 334.000000 93.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297e360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 334.000000 76.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297e5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 334.000000 58.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297e7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 338.000000 41.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297ea20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 320.000000 130.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297ec60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 308.500000 111.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297ef90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 94.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297f210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 77.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297f450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 59.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297f690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 560.000000 42.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297f8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 542.000000 131.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297fb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 530.500000 112.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297fe40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 779.000000 96.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29800c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 779.000000 79.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2980300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 779.000000 61.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2980540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 783.000000 44.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2980780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 133.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29809c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 753.500000 114.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2980cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1002.000000 95.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbcbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1002.000000 78.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbce30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1002.000000 60.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbd070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1006.000000 43.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbd2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 988.000000 132.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbd4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 976.500000 113.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbd820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1218.000000 95.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbdaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1218.000000 78.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbdce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1218.000000 60.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbdf20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.000000 43.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbe160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1204.000000 132.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dbe3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1192.500000 113.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_CJ" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dacangnanTcj" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="59,-1239 59,-1270 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34313" ObjectName="AC-35kV.LN_dacangnanTcj"/>
    <cge:TPSR_Ref TObjectID="34313_SS-230"/></metadata>
   <polyline fill="none" opacity="0" points="59,-1239 59,-1270 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_CJ" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_zhongcangTcj" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="937,-1234 937,-1266 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38065" ObjectName="AC-35kV.LN_zhongcangTcj"/>
    <cge:TPSR_Ref TObjectID="38065_SS-230"/></metadata>
   <polyline fill="none" opacity="0" points="937,-1234 937,-1266 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -36.000000 -132.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26575"/>
     <cge:Term_Ref ObjectID="37604"/>
    <cge:TPSR_Ref TObjectID="26575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -36.000000 -132.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26575"/>
     <cge:Term_Ref ObjectID="37604"/>
    <cge:TPSR_Ref TObjectID="26575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -36.000000 -132.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26575"/>
     <cge:Term_Ref ObjectID="37604"/>
    <cge:TPSR_Ref TObjectID="26575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -36.000000 -132.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26575"/>
     <cge:Term_Ref ObjectID="37604"/>
    <cge:TPSR_Ref TObjectID="26575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159923" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -36.000000 -132.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159923" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26575"/>
     <cge:Term_Ref ObjectID="37604"/>
    <cge:TPSR_Ref TObjectID="26575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -36.000000 -132.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26575"/>
     <cge:Term_Ref ObjectID="37604"/>
    <cge:TPSR_Ref TObjectID="26575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -136.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26579"/>
     <cge:Term_Ref ObjectID="37612"/>
    <cge:TPSR_Ref TObjectID="26579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -136.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26579"/>
     <cge:Term_Ref ObjectID="37612"/>
    <cge:TPSR_Ref TObjectID="26579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -136.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26579"/>
     <cge:Term_Ref ObjectID="37612"/>
    <cge:TPSR_Ref TObjectID="26579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -136.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26579"/>
     <cge:Term_Ref ObjectID="37612"/>
    <cge:TPSR_Ref TObjectID="26579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -136.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26579"/>
     <cge:Term_Ref ObjectID="37612"/>
    <cge:TPSR_Ref TObjectID="26579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -136.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26579"/>
     <cge:Term_Ref ObjectID="37612"/>
    <cge:TPSR_Ref TObjectID="26579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 381.000000 -134.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26583"/>
     <cge:Term_Ref ObjectID="37620"/>
    <cge:TPSR_Ref TObjectID="26583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159937" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 381.000000 -134.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159937" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26583"/>
     <cge:Term_Ref ObjectID="37620"/>
    <cge:TPSR_Ref TObjectID="26583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 381.000000 -134.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26583"/>
     <cge:Term_Ref ObjectID="37620"/>
    <cge:TPSR_Ref TObjectID="26583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 381.000000 -134.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26583"/>
     <cge:Term_Ref ObjectID="37620"/>
    <cge:TPSR_Ref TObjectID="26583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 381.000000 -134.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26583"/>
     <cge:Term_Ref ObjectID="37620"/>
    <cge:TPSR_Ref TObjectID="26583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159938" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 381.000000 -134.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159938" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26583"/>
     <cge:Term_Ref ObjectID="37620"/>
    <cge:TPSR_Ref TObjectID="26583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159942" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 -136.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159942" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26587"/>
     <cge:Term_Ref ObjectID="37628"/>
    <cge:TPSR_Ref TObjectID="26587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159943" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 -136.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26587"/>
     <cge:Term_Ref ObjectID="37628"/>
    <cge:TPSR_Ref TObjectID="26587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 -136.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159939" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26587"/>
     <cge:Term_Ref ObjectID="37628"/>
    <cge:TPSR_Ref TObjectID="26587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 -136.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26587"/>
     <cge:Term_Ref ObjectID="37628"/>
    <cge:TPSR_Ref TObjectID="26587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 -136.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26587"/>
     <cge:Term_Ref ObjectID="37628"/>
    <cge:TPSR_Ref TObjectID="26587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159944" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 -136.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26587"/>
     <cge:Term_Ref ObjectID="37628"/>
    <cge:TPSR_Ref TObjectID="26587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159948" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -135.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26591"/>
     <cge:Term_Ref ObjectID="37636"/>
    <cge:TPSR_Ref TObjectID="26591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159949" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -135.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159949" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26591"/>
     <cge:Term_Ref ObjectID="37636"/>
    <cge:TPSR_Ref TObjectID="26591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -135.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26591"/>
     <cge:Term_Ref ObjectID="37636"/>
    <cge:TPSR_Ref TObjectID="26591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -135.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26591"/>
     <cge:Term_Ref ObjectID="37636"/>
    <cge:TPSR_Ref TObjectID="26591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -135.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26591"/>
     <cge:Term_Ref ObjectID="37636"/>
    <cge:TPSR_Ref TObjectID="26591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159950" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -135.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159950" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26591"/>
     <cge:Term_Ref ObjectID="37636"/>
    <cge:TPSR_Ref TObjectID="26591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -135.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26595"/>
     <cge:Term_Ref ObjectID="37644"/>
    <cge:TPSR_Ref TObjectID="26595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159955" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -135.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159955" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26595"/>
     <cge:Term_Ref ObjectID="37644"/>
    <cge:TPSR_Ref TObjectID="26595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159951" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -135.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159951" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26595"/>
     <cge:Term_Ref ObjectID="37644"/>
    <cge:TPSR_Ref TObjectID="26595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -135.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26595"/>
     <cge:Term_Ref ObjectID="37644"/>
    <cge:TPSR_Ref TObjectID="26595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -135.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26595"/>
     <cge:Term_Ref ObjectID="37644"/>
    <cge:TPSR_Ref TObjectID="26595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -135.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26595"/>
     <cge:Term_Ref ObjectID="37644"/>
    <cge:TPSR_Ref TObjectID="26595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159960" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -137.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159960" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26599"/>
     <cge:Term_Ref ObjectID="37652"/>
    <cge:TPSR_Ref TObjectID="26599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159961" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -137.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159961" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26599"/>
     <cge:Term_Ref ObjectID="37652"/>
    <cge:TPSR_Ref TObjectID="26599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -137.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26599"/>
     <cge:Term_Ref ObjectID="37652"/>
    <cge:TPSR_Ref TObjectID="26599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -137.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26599"/>
     <cge:Term_Ref ObjectID="37652"/>
    <cge:TPSR_Ref TObjectID="26599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -137.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26599"/>
     <cge:Term_Ref ObjectID="37652"/>
    <cge:TPSR_Ref TObjectID="26599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159962" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -137.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159962" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26599"/>
     <cge:Term_Ref ObjectID="37652"/>
    <cge:TPSR_Ref TObjectID="26599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -648.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26571"/>
     <cge:Term_Ref ObjectID="37596"/>
    <cge:TPSR_Ref TObjectID="26571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -648.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26571"/>
     <cge:Term_Ref ObjectID="37596"/>
    <cge:TPSR_Ref TObjectID="26571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -648.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26571"/>
     <cge:Term_Ref ObjectID="37596"/>
    <cge:TPSR_Ref TObjectID="26571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159915" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -648.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26571"/>
     <cge:Term_Ref ObjectID="37596"/>
    <cge:TPSR_Ref TObjectID="26571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -648.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26571"/>
     <cge:Term_Ref ObjectID="37596"/>
    <cge:TPSR_Ref TObjectID="26571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -648.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26571"/>
     <cge:Term_Ref ObjectID="37596"/>
    <cge:TPSR_Ref TObjectID="26571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -900.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26569"/>
     <cge:Term_Ref ObjectID="37592"/>
    <cge:TPSR_Ref TObjectID="26569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -900.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26569"/>
     <cge:Term_Ref ObjectID="37592"/>
    <cge:TPSR_Ref TObjectID="26569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -900.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26569"/>
     <cge:Term_Ref ObjectID="37592"/>
    <cge:TPSR_Ref TObjectID="26569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -900.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26569"/>
     <cge:Term_Ref ObjectID="37592"/>
    <cge:TPSR_Ref TObjectID="26569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159910" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -900.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26569"/>
     <cge:Term_Ref ObjectID="37592"/>
    <cge:TPSR_Ref TObjectID="26569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -900.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26569"/>
     <cge:Term_Ref ObjectID="37592"/>
    <cge:TPSR_Ref TObjectID="26569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -643.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26565"/>
     <cge:Term_Ref ObjectID="37584"/>
    <cge:TPSR_Ref TObjectID="26565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -643.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26565"/>
     <cge:Term_Ref ObjectID="37584"/>
    <cge:TPSR_Ref TObjectID="26565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -643.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26565"/>
     <cge:Term_Ref ObjectID="37584"/>
    <cge:TPSR_Ref TObjectID="26565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -643.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26565"/>
     <cge:Term_Ref ObjectID="37584"/>
    <cge:TPSR_Ref TObjectID="26565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159903" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -643.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26565"/>
     <cge:Term_Ref ObjectID="37584"/>
    <cge:TPSR_Ref TObjectID="26565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159906" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -643.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159906" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26565"/>
     <cge:Term_Ref ObjectID="37584"/>
    <cge:TPSR_Ref TObjectID="26565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 184.000000 -880.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26563"/>
     <cge:Term_Ref ObjectID="37580"/>
    <cge:TPSR_Ref TObjectID="26563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 184.000000 -880.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26563"/>
     <cge:Term_Ref ObjectID="37580"/>
    <cge:TPSR_Ref TObjectID="26563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 184.000000 -880.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26563"/>
     <cge:Term_Ref ObjectID="37580"/>
    <cge:TPSR_Ref TObjectID="26563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 184.000000 -880.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26563"/>
     <cge:Term_Ref ObjectID="37580"/>
    <cge:TPSR_Ref TObjectID="26563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 184.000000 -880.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26563"/>
     <cge:Term_Ref ObjectID="37580"/>
    <cge:TPSR_Ref TObjectID="26563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 184.000000 -880.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26563"/>
     <cge:Term_Ref ObjectID="37580"/>
    <cge:TPSR_Ref TObjectID="26563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159870" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -1.000000 -1033.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159870" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26551"/>
     <cge:Term_Ref ObjectID="37556"/>
    <cge:TPSR_Ref TObjectID="26551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -1.000000 -1033.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26551"/>
     <cge:Term_Ref ObjectID="37556"/>
    <cge:TPSR_Ref TObjectID="26551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159867" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -1.000000 -1033.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159867" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26551"/>
     <cge:Term_Ref ObjectID="37556"/>
    <cge:TPSR_Ref TObjectID="26551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159868" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -1.000000 -1033.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159868" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26551"/>
     <cge:Term_Ref ObjectID="37556"/>
    <cge:TPSR_Ref TObjectID="26551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159869" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -1.000000 -1033.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26551"/>
     <cge:Term_Ref ObjectID="37556"/>
    <cge:TPSR_Ref TObjectID="26551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159872" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -1.000000 -1033.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26551"/>
     <cge:Term_Ref ObjectID="37556"/>
    <cge:TPSR_Ref TObjectID="26551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-159876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -1031.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26555"/>
     <cge:Term_Ref ObjectID="37564"/>
    <cge:TPSR_Ref TObjectID="26555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-159877" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -1031.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26555"/>
     <cge:Term_Ref ObjectID="37564"/>
    <cge:TPSR_Ref TObjectID="26555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-159873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -1031.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26555"/>
     <cge:Term_Ref ObjectID="37564"/>
    <cge:TPSR_Ref TObjectID="26555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-159874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -1031.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26555"/>
     <cge:Term_Ref ObjectID="37564"/>
    <cge:TPSR_Ref TObjectID="26555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-159875" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -1031.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26555"/>
     <cge:Term_Ref ObjectID="37564"/>
    <cge:TPSR_Ref TObjectID="26555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-159878" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -1031.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26555"/>
     <cge:Term_Ref ObjectID="37564"/>
    <cge:TPSR_Ref TObjectID="26555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-159879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -1067.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26548"/>
     <cge:Term_Ref ObjectID="37552"/>
    <cge:TPSR_Ref TObjectID="26548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-159880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -1067.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26548"/>
     <cge:Term_Ref ObjectID="37552"/>
    <cge:TPSR_Ref TObjectID="26548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-159881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -1067.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26548"/>
     <cge:Term_Ref ObjectID="37552"/>
    <cge:TPSR_Ref TObjectID="26548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-159885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -1067.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26548"/>
     <cge:Term_Ref ObjectID="37552"/>
    <cge:TPSR_Ref TObjectID="26548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-159882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -1067.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26548"/>
     <cge:Term_Ref ObjectID="37552"/>
    <cge:TPSR_Ref TObjectID="26548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-159883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -1067.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26548"/>
     <cge:Term_Ref ObjectID="37552"/>
    <cge:TPSR_Ref TObjectID="26548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-159884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -1067.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26548"/>
     <cge:Term_Ref ObjectID="37552"/>
    <cge:TPSR_Ref TObjectID="26548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-159886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -1067.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26548"/>
     <cge:Term_Ref ObjectID="37552"/>
    <cge:TPSR_Ref TObjectID="26548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-159887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -646.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26549"/>
     <cge:Term_Ref ObjectID="37553"/>
    <cge:TPSR_Ref TObjectID="26549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-159888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -646.000000) translate(0,33)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26549"/>
     <cge:Term_Ref ObjectID="37553"/>
    <cge:TPSR_Ref TObjectID="26549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-159889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -646.000000) translate(0,51)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26549"/>
     <cge:Term_Ref ObjectID="37553"/>
    <cge:TPSR_Ref TObjectID="26549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-159893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -646.000000) translate(0,69)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26549"/>
     <cge:Term_Ref ObjectID="37553"/>
    <cge:TPSR_Ref TObjectID="26549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-159890" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -646.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26549"/>
     <cge:Term_Ref ObjectID="37553"/>
    <cge:TPSR_Ref TObjectID="26549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-159891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -646.000000) translate(0,105)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26549"/>
     <cge:Term_Ref ObjectID="37553"/>
    <cge:TPSR_Ref TObjectID="26549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-159892" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -646.000000) translate(0,123)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26549"/>
     <cge:Term_Ref ObjectID="37553"/>
    <cge:TPSR_Ref TObjectID="26549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-159894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1314.000000 -646.000000) translate(0,141)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26549"/>
     <cge:Term_Ref ObjectID="37553"/>
    <cge:TPSR_Ref TObjectID="26549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-160317" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.000000 -785.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="160317" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26604"/>
     <cge:Term_Ref ObjectID="37667"/>
    <cge:TPSR_Ref TObjectID="26604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-159920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 909.000000 -758.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26604"/>
     <cge:Term_Ref ObjectID="37664"/>
    <cge:TPSR_Ref TObjectID="26604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-159907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -29.000000 -740.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="159907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26603"/>
     <cge:Term_Ref ObjectID="37663"/>
    <cge:TPSR_Ref TObjectID="26603"/></metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-159979">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 50.000000 -1014.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26551" ObjectName="SW-DY_CJ.DY_CJ_321BK"/>
     <cge:Meas_Ref ObjectId="159979"/>
    <cge:TPSR_Ref TObjectID="26551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160001">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 928.000000 -1040.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26555" ObjectName="SW-DY_CJ.DY_CJ_322BK"/>
     <cge:Meas_Ref ObjectId="160001"/>
    <cge:TPSR_Ref TObjectID="26555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160064">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 49.241796 -802.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26563" ObjectName="SW-DY_CJ.DY_CJ_301BK"/>
     <cge:Meas_Ref ObjectId="160064"/>
    <cge:TPSR_Ref TObjectID="26563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160071">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 47.089254 -604.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26565" ObjectName="SW-DY_CJ.DY_CJ_001BK"/>
     <cge:Meas_Ref ObjectId="160071"/>
    <cge:TPSR_Ref TObjectID="26565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160174">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -58.910746 -327.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26575" ObjectName="SW-DY_CJ.DY_CJ_021BK"/>
     <cge:Meas_Ref ObjectId="160174"/>
    <cge:TPSR_Ref TObjectID="26575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160115">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 968.241796 -814.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26569" ObjectName="SW-DY_CJ.DY_CJ_302BK"/>
     <cge:Meas_Ref ObjectId="160115"/>
    <cge:TPSR_Ref TObjectID="26569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160123">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 967.089254 -566.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26571" ObjectName="SW-DY_CJ.DY_CJ_002BK"/>
     <cge:Meas_Ref ObjectId="160123"/>
    <cge:TPSR_Ref TObjectID="26571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 151.089254 -326.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26579" ObjectName="SW-DY_CJ.DY_CJ_022BK"/>
     <cge:Meas_Ref ObjectId="160192"/>
    <cge:TPSR_Ref TObjectID="26579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160264">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1014.089254 -325.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26595" ObjectName="SW-DY_CJ.DY_CJ_026BK"/>
     <cge:Meas_Ref ObjectId="160264"/>
    <cge:TPSR_Ref TObjectID="26595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160210">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 359.089254 -323.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26583" ObjectName="SW-DY_CJ.DY_CJ_023BK"/>
     <cge:Meas_Ref ObjectId="160210"/>
    <cge:TPSR_Ref TObjectID="26583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160228">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 569.089254 -326.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26587" ObjectName="SW-DY_CJ.DY_CJ_024BK"/>
     <cge:Meas_Ref ObjectId="160228"/>
    <cge:TPSR_Ref TObjectID="26587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160246">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 792.089254 -329.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26591" ObjectName="SW-DY_CJ.DY_CJ_025BK"/>
     <cge:Meas_Ref ObjectId="160246"/>
    <cge:TPSR_Ref TObjectID="26591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-160282">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.089254 -324.636364)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26599" ObjectName="SW-DY_CJ.DY_CJ_027BK"/>
     <cge:Meas_Ref ObjectId="160282"/>
    <cge:TPSR_Ref TObjectID="26599"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-605" y="-1254"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-654" y="-1271"/></g>
   <g href="35kV仓街变DY_CJ_321间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="74" y="-1042"/></g>
   <g href="35kV仓街变DY_CJ_322间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="952" y="-1070"/></g>
   <g href="35kV仓街变DY_CJ_022间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="176" y="-353"/></g>
   <g href="35kV仓街变DY_CJ_023间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="384" y="-352"/></g>
   <g href="35kV仓街变DY_CJ_024间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="594" y="-354"/></g>
   <g href="35kV仓街变DY_CJ_025间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="816" y="-359"/></g>
   <g href="35kV仓街变DY_CJ_026间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1035" y="-355"/></g>
   <g href="35kV仓街变DY_CJ_027间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1249" y="-354"/></g>
   <g href="35kV仓街变DY_CJ_021间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="-34" y="-359"/></g>
   <g href="35kV仓街变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="79" x="-701" y="-835"/></g>
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-454" y="-1234"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-454" y="-1269"/></g>
   <g href="35kV仓街变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="879" y="-813"/></g>
   <g href="35kV仓街变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="-55" y="-775"/></g>
   <g href="AVC仓街站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-432" y="-1185"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2d84670">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -38.112970 -1062.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d713d0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 980.887030 -1117.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b07740">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -22.835532 -205.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f8f860">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 391.000000 -1027.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e0d950">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.000000 -660.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_301b0e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 186.164468 -213.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31bafe0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1049.164468 -212.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3269360">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 394.164468 -210.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dc4960">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 604.164468 -213.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30c9b10">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 827.164468 -216.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e18340">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1261.164468 -211.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3028a60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 341.000000 -1079.000000)" xlink:href="#lightningRod:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32e92a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -227.000000 -662.000000)" xlink:href="#lightningRod:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_CJ"/>
</svg>