<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-223" aopId="3936770" id="thSvg" product="E8000V2" version="1.0" viewBox="-738 -1271 2012 1157">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape186">
    <circle cx="15" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="41" x2="39" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="43" x2="37" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="34" x2="46" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="19" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="57" y2="53"/>
    <circle cx="15" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="43" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,16 21,28 9,28 15,16 15,16 15,16 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,59 40,59 40,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="53" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="10" y1="87" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="18" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="14" y1="81" y2="87"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape65">
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="46" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="18" y2="20"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="5" x2="9" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="5" x2="9" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="9" x2="9" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="41" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="38"/>
    <rect height="13" stroke-width="1" width="7" x="32" y="17"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="27,8 35,8 35,18 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="38" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="40" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
   </symbol>
   <symbol id="voltageTransformer:shape140">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="66" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="24" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="18" y1="8" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="24" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="18" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="22" y2="18"/>
    <circle cx="22" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="22" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="78" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="69" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="45" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="67" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="59" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="46" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="66" y2="53"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34cbdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34cc770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34cd120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34cde00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34cf000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34cfc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d0470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d0e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d16f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d20d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d20d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d3ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d3ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_34d4eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d6bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d7780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d8070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34d89b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34da0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34da890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34daf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34db9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34dcb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34dd500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34ddff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34e32c0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34e3f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34dfcd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34e1150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34e1bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_34e5380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_34e67e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1167" width="2022" x="-743" y="-1276"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-170310">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 160.333333 -1019.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27187" ObjectName="SW-YM_JY.YM_JY_361BK"/>
     <cge:Meas_Ref ObjectId="170310"/>
    <cge:TPSR_Ref TObjectID="27187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170366">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 503.534917 -805.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27193" ObjectName="SW-YM_JY.YM_JY_301BK"/>
     <cge:Meas_Ref ObjectId="170366"/>
    <cge:TPSR_Ref TObjectID="27193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170374">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 503.534917 -630.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27195" ObjectName="SW-YM_JY.YM_JY_001BK"/>
     <cge:Meas_Ref ObjectId="170374"/>
    <cge:TPSR_Ref TObjectID="27195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170501">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -165.303937 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27197" ObjectName="SW-YM_JY.YM_JY_061BK"/>
     <cge:Meas_Ref ObjectId="170501"/>
    <cge:TPSR_Ref TObjectID="27197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170553">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 58.029396 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27200" ObjectName="SW-YM_JY.YM_JY_062BK"/>
     <cge:Meas_Ref ObjectId="170553"/>
    <cge:TPSR_Ref TObjectID="27200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170605">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 280.362729 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27203" ObjectName="SW-YM_JY.YM_JY_063BK"/>
     <cge:Meas_Ref ObjectId="170605"/>
    <cge:TPSR_Ref TObjectID="27203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170657">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 500.696063 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27206" ObjectName="SW-YM_JY.YM_JY_064BK"/>
     <cge:Meas_Ref ObjectId="170657"/>
    <cge:TPSR_Ref TObjectID="27206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170709">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 723.029396 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27209" ObjectName="SW-YM_JY.YM_JY_065BK"/>
     <cge:Meas_Ref ObjectId="170709"/>
    <cge:TPSR_Ref TObjectID="27209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170761">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 945.362729 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27212" ObjectName="SW-YM_JY.YM_JY_066BK"/>
     <cge:Meas_Ref ObjectId="170761"/>
    <cge:TPSR_Ref TObjectID="27212"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2daec80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 844.000000 -1129.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39d12e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 18.568754 -731.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_43e37e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 197.000000 -1113.000000)" xlink:href="#voltageTransformer:shape140"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YM_JB" endPointId="0" endStationName="YM_JY" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_bianyi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="169,-1219 169,-1246 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38092" ObjectName="AC-35kV.LN_bianyi"/>
    <cge:TPSR_Ref TObjectID="38092_SS-223"/></metadata>
   <polyline fill="none" opacity="0" points="169,-1219 169,-1246 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YM_JY.YM_JY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="38454"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 474.534917 -694.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 474.534917 -694.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="27214" ObjectName="TF-YM_JY.YM_JY_1T"/>
    <cge:TPSR_Ref TObjectID="27214"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_44ee7d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 857.000000 -1057.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a3da30">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 818.067821 -967.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a3dd90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 164.000000 -866.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_435e460">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 154.000000 -744.000000)" xlink:href="#lightningRod:shape186"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_435f7f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 31.568754 -662.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_381a200">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -8.363424 -572.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a451d0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -137.839766 -229.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b3f720">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 84.493567 -229.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_438e840">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 305.826901 -229.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a94060">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 527.160234 -229.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a44e10">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 749.493567 -229.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_48fb1f0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 970.826901 -229.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4e3e510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1175.827934 -467.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_475f150">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1166.827934 -347.000000)" xlink:href="#lightningRod:shape186"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3646310">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 93.000000 -1195.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-170232" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -588.000000 -1016.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170232" ObjectName="YM_JY:YM_JY_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-170233" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -589.000000 -973.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170233" ObjectName="YM_JY:YM_JY_301BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-170232" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -1099.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170232" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-170232" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -590.000000 -1058.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170232" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170246" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -164.000000 -160.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27197"/>
     <cge:Term_Ref ObjectID="38418"/>
    <cge:TPSR_Ref TObjectID="27197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -164.000000 -160.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27197"/>
     <cge:Term_Ref ObjectID="38418"/>
    <cge:TPSR_Ref TObjectID="27197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170243" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -164.000000 -160.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170243" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27197"/>
     <cge:Term_Ref ObjectID="38418"/>
    <cge:TPSR_Ref TObjectID="27197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170252" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 56.000000 -160.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170252" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27200"/>
     <cge:Term_Ref ObjectID="38424"/>
    <cge:TPSR_Ref TObjectID="27200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170253" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 56.000000 -160.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170253" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27200"/>
     <cge:Term_Ref ObjectID="38424"/>
    <cge:TPSR_Ref TObjectID="27200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170249" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 56.000000 -160.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27200"/>
     <cge:Term_Ref ObjectID="38424"/>
    <cge:TPSR_Ref TObjectID="27200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170258" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 271.000000 -162.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170258" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27203"/>
     <cge:Term_Ref ObjectID="38430"/>
    <cge:TPSR_Ref TObjectID="27203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170259" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 271.000000 -162.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27203"/>
     <cge:Term_Ref ObjectID="38430"/>
    <cge:TPSR_Ref TObjectID="27203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 271.000000 -162.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27203"/>
     <cge:Term_Ref ObjectID="38430"/>
    <cge:TPSR_Ref TObjectID="27203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170264" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -162.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27206"/>
     <cge:Term_Ref ObjectID="38436"/>
    <cge:TPSR_Ref TObjectID="27206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170265" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -162.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170265" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27206"/>
     <cge:Term_Ref ObjectID="38436"/>
    <cge:TPSR_Ref TObjectID="27206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170261" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -162.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170261" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27206"/>
     <cge:Term_Ref ObjectID="38436"/>
    <cge:TPSR_Ref TObjectID="27206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170270" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 716.000000 -162.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170270" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27209"/>
     <cge:Term_Ref ObjectID="38442"/>
    <cge:TPSR_Ref TObjectID="27209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170271" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 716.000000 -162.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27209"/>
     <cge:Term_Ref ObjectID="38442"/>
    <cge:TPSR_Ref TObjectID="27209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170267" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 716.000000 -162.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170267" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27209"/>
     <cge:Term_Ref ObjectID="38442"/>
    <cge:TPSR_Ref TObjectID="27209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170276" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 941.000000 -159.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27212"/>
     <cge:Term_Ref ObjectID="38448"/>
    <cge:TPSR_Ref TObjectID="27212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170277" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 941.000000 -159.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27212"/>
     <cge:Term_Ref ObjectID="38448"/>
    <cge:TPSR_Ref TObjectID="27212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170273" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 941.000000 -159.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170273" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27212"/>
     <cge:Term_Ref ObjectID="38448"/>
    <cge:TPSR_Ref TObjectID="27212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170238" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 638.000000 -673.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27195"/>
     <cge:Term_Ref ObjectID="38414"/>
    <cge:TPSR_Ref TObjectID="27195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170239" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 638.000000 -673.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27195"/>
     <cge:Term_Ref ObjectID="38414"/>
    <cge:TPSR_Ref TObjectID="27195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170235" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 638.000000 -673.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27195"/>
     <cge:Term_Ref ObjectID="38414"/>
    <cge:TPSR_Ref TObjectID="27195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-170240" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 638.000000 -673.000000) translate(0,57)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27195"/>
     <cge:Term_Ref ObjectID="38414"/>
    <cge:TPSR_Ref TObjectID="27195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170232" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 -847.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27193"/>
     <cge:Term_Ref ObjectID="38410"/>
    <cge:TPSR_Ref TObjectID="27193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170233" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 -847.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27193"/>
     <cge:Term_Ref ObjectID="38410"/>
    <cge:TPSR_Ref TObjectID="27193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170229" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 -847.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170229" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27193"/>
     <cge:Term_Ref ObjectID="38410"/>
    <cge:TPSR_Ref TObjectID="27193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-170234" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 -847.000000) translate(0,57)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27193"/>
     <cge:Term_Ref ObjectID="38410"/>
    <cge:TPSR_Ref TObjectID="27193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-170209" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 285.000000 -1062.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27187"/>
     <cge:Term_Ref ObjectID="38398"/>
    <cge:TPSR_Ref TObjectID="27187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-170210" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 285.000000 -1062.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170210" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27187"/>
     <cge:Term_Ref ObjectID="38398"/>
    <cge:TPSR_Ref TObjectID="27187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-170206" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 285.000000 -1062.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170206" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27187"/>
     <cge:Term_Ref ObjectID="38398"/>
    <cge:TPSR_Ref TObjectID="27187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-170213" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -34.000000 -1035.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170213" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27184"/>
     <cge:Term_Ref ObjectID="38394"/>
    <cge:TPSR_Ref TObjectID="27184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-170214" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -34.000000 -1035.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170214" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27184"/>
     <cge:Term_Ref ObjectID="38394"/>
    <cge:TPSR_Ref TObjectID="27184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-170215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -34.000000 -1035.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27184"/>
     <cge:Term_Ref ObjectID="38394"/>
    <cge:TPSR_Ref TObjectID="27184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-170216" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -34.000000 -1035.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27184"/>
     <cge:Term_Ref ObjectID="38394"/>
    <cge:TPSR_Ref TObjectID="27184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-170220" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -34.000000 -1035.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170220" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27184"/>
     <cge:Term_Ref ObjectID="38394"/>
    <cge:TPSR_Ref TObjectID="27184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-170221" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -222.000000 -690.000000) translate(0,12)">170221.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170221" ObjectName="YM_JY.YM_JY_9IM:F"/>
     <cge:PSR_Ref ObjectID="27185"/>
     <cge:Term_Ref ObjectID="38395"/>
    <cge:TPSR_Ref TObjectID="27185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-170222" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -222.000000 -690.000000) translate(0,27)">170222.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170222" ObjectName="YM_JY.YM_JY_9IM:F"/>
     <cge:PSR_Ref ObjectID="27185"/>
     <cge:Term_Ref ObjectID="38395"/>
    <cge:TPSR_Ref TObjectID="27185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-170223" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -222.000000 -690.000000) translate(0,42)">170223.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170223" ObjectName="YM_JY.YM_JY_9IM:F"/>
     <cge:PSR_Ref ObjectID="27185"/>
     <cge:Term_Ref ObjectID="38395"/>
    <cge:TPSR_Ref TObjectID="27185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-170227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -222.000000 -690.000000) translate(0,57)">170227.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170227" ObjectName="YM_JY.YM_JY_9IM:F"/>
     <cge:PSR_Ref ObjectID="27185"/>
     <cge:Term_Ref ObjectID="38395"/>
    <cge:TPSR_Ref TObjectID="27185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-170224" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -222.000000 -690.000000) translate(0,72)">170224.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170224" ObjectName="YM_JY.YM_JY_9IM:F"/>
     <cge:PSR_Ref ObjectID="27185"/>
     <cge:Term_Ref ObjectID="38395"/>
    <cge:TPSR_Ref TObjectID="27185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-170225" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -222.000000 -690.000000) translate(0,87)">170225.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170225" ObjectName="YM_JY.YM_JY_9IM:F"/>
     <cge:PSR_Ref ObjectID="27185"/>
     <cge:Term_Ref ObjectID="38395"/>
    <cge:TPSR_Ref TObjectID="27185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-170226" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -222.000000 -690.000000) translate(0,102)">170226.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170226" ObjectName="YM_JY.YM_JY_9IM:F"/>
     <cge:PSR_Ref ObjectID="27185"/>
     <cge:Term_Ref ObjectID="38395"/>
    <cge:TPSR_Ref TObjectID="27185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-170228" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -222.000000 -690.000000) translate(0,117)">170228.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170228" ObjectName="YM_JY.YM_JY_9IM:F"/>
     <cge:PSR_Ref ObjectID="27185"/>
     <cge:Term_Ref ObjectID="38395"/>
    <cge:TPSR_Ref TObjectID="27185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-170242" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 700.000000 -753.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170242" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27214"/>
     <cge:Term_Ref ObjectID="38452"/>
    <cge:TPSR_Ref TObjectID="27214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-170241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 700.000000 -753.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="170241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27214"/>
     <cge:Term_Ref ObjectID="38452"/>
    <cge:TPSR_Ref TObjectID="27214"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-620" y="-1249"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-620" y="-1249"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="179" y="-1048"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="179" y="-1048"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="549" y="-746"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="549" y="-746"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-147" y="-416"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-147" y="-416"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="76" y="-416"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="76" y="-416"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="299" y="-416"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="299" y="-416"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="519" y="-416"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="519" y="-416"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="741" y="-416"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="741" y="-416"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="964" y="-416"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="964" y="-416"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-366" y="-1211"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-366" y="-1211"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-366" y="-1250"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-366" y="-1250"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="17" qtmmishow="hidden" width="59" x="-705" y="-882"/>
    </a>
   <metadata/><rect fill="white" height="17" opacity="0" stroke="white" transform="" width="59" x="-705" y="-882"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-238,-1242 -241,-1245 -241,-1188 -238,-1191 -238,-1242" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-238,-1242 -241,-1245 -178,-1245 -181,-1242 -238,-1242" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="-238,-1191 -241,-1188 -178,-1188 -181,-1191 -238,-1191" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="-181,-1242 -178,-1245 -178,-1188 -181,-1191 -181,-1242" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="51" stroke="rgb(255,255,255)" width="57" x="-238" y="-1242"/>
     <rect fill="none" height="51" qtmmishow="hidden" stroke="rgb(0,0,0)" width="57" x="-238" y="-1242"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-620" y="-1249"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="35kV姜驿变35kV边驿线间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="179" y="-1048"/></g>
   <g href="35kV姜驿变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="549" y="-746"/></g>
   <g href="35kV姜驿变10kV阿洒姑线061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-147" y="-416"/></g>
   <g href="35kV姜驿变10kV贡茶线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="76" y="-416"/></g>
   <g href="35kV姜驿变10kV金河线063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="299" y="-416"/></g>
   <g href="35kV姜驿变10kV红花箐线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="519" y="-416"/></g>
   <g href="35kV姜驿变10kV姜中线065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="741" y="-416"/></g>
   <g href="35kV姜驿变10kV大地线066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="964" y="-416"/></g>
   <g href="cx_配调_配网接线图35_元谋.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-366" y="-1211"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-366" y="-1250"/></g>
   <g href="35kV姜驿变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="17" qtmmishow="hidden" width="59" x="-705" y="-882"/></g>
   <g href="AVC姜驿站.svg" style="fill-opacity:0"><rect height="51" qtmmishow="hidden" stroke="rgb(0,0,0)" width="57" x="-238" y="-1242"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-170312">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 160.333333 -961.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27188" ObjectName="SW-YM_JY.YM_JY_3611SW"/>
     <cge:Meas_Ref ObjectId="170312"/>
    <cge:TPSR_Ref TObjectID="27188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170313">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 160.333333 -1072.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27189" ObjectName="SW-YM_JY.YM_JY_3616SW"/>
     <cge:Meas_Ref ObjectId="170313"/>
    <cge:TPSR_Ref TObjectID="27189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170314">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 104.819337 -1128.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27190" ObjectName="SW-YM_JY.YM_JY_36167SW"/>
     <cge:Meas_Ref ObjectId="170314"/>
    <cge:TPSR_Ref TObjectID="27190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170362">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.333333 -965.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27191" ObjectName="SW-YM_JY.YM_JY_3901SW"/>
     <cge:Meas_Ref ObjectId="170362"/>
    <cge:TPSR_Ref TObjectID="27191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170368">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 503.534917 -866.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27194" ObjectName="SW-YM_JY.YM_JY_3011SW"/>
     <cge:Meas_Ref ObjectId="170368"/>
    <cge:TPSR_Ref TObjectID="27194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170376">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 503.534917 -570.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27196" ObjectName="SW-YM_JY.YM_JY_0011SW"/>
     <cge:Meas_Ref ObjectId="170376"/>
    <cge:TPSR_Ref TObjectID="27196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170365">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 28.902088 -570.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27192" ObjectName="SW-YM_JY.YM_JY_0901SW"/>
     <cge:Meas_Ref ObjectId="170365"/>
    <cge:TPSR_Ref TObjectID="27192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170503">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -163.754918 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27198" ObjectName="SW-YM_JY.YM_JY_0611SW"/>
     <cge:Meas_Ref ObjectId="170503"/>
    <cge:TPSR_Ref TObjectID="27198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170504">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -163.754918 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27199" ObjectName="SW-YM_JY.YM_JY_0616SW"/>
     <cge:Meas_Ref ObjectId="170504"/>
    <cge:TPSR_Ref TObjectID="27199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170555">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 57.578416 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27201" ObjectName="SW-YM_JY.YM_JY_0621SW"/>
     <cge:Meas_Ref ObjectId="170555"/>
    <cge:TPSR_Ref TObjectID="27201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170556">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 57.578416 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27202" ObjectName="SW-YM_JY.YM_JY_0626SW"/>
     <cge:Meas_Ref ObjectId="170556"/>
    <cge:TPSR_Ref TObjectID="27202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170607">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.911749 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27204" ObjectName="SW-YM_JY.YM_JY_0631SW"/>
     <cge:Meas_Ref ObjectId="170607"/>
    <cge:TPSR_Ref TObjectID="27204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170608">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.911749 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27205" ObjectName="SW-YM_JY.YM_JY_0636SW"/>
     <cge:Meas_Ref ObjectId="170608"/>
    <cge:TPSR_Ref TObjectID="27205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170659">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 501.245082 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27207" ObjectName="SW-YM_JY.YM_JY_0641SW"/>
     <cge:Meas_Ref ObjectId="170659"/>
    <cge:TPSR_Ref TObjectID="27207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170660">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 501.245082 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27208" ObjectName="SW-YM_JY.YM_JY_0646SW"/>
     <cge:Meas_Ref ObjectId="170660"/>
    <cge:TPSR_Ref TObjectID="27208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170711">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 722.578416 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27210" ObjectName="SW-YM_JY.YM_JY_0651SW"/>
     <cge:Meas_Ref ObjectId="170711"/>
    <cge:TPSR_Ref TObjectID="27210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170712">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 722.578416 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27211" ObjectName="SW-YM_JY.YM_JY_0656SW"/>
     <cge:Meas_Ref ObjectId="170712"/>
    <cge:TPSR_Ref TObjectID="27211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170763">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 944.911749 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27213" ObjectName="SW-YM_JY.YM_JY_0661SW"/>
     <cge:Meas_Ref ObjectId="170763"/>
    <cge:TPSR_Ref TObjectID="27213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-170847">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 944.911749 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27217" ObjectName="SW-YM_JY.YM_JY_0666SW"/>
     <cge:Meas_Ref ObjectId="170847"/>
    <cge:TPSR_Ref TObjectID="27217"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YM_JY.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -163.837711 -208.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34178" ObjectName="EC-YM_JY.061Ld"/>
    <cge:TPSR_Ref TObjectID="34178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_JY.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 58.495622 -208.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34179" ObjectName="EC-YM_JY.062Ld"/>
    <cge:TPSR_Ref TObjectID="34179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_JY.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.828956 -208.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34180" ObjectName="EC-YM_JY.063Ld"/>
    <cge:TPSR_Ref TObjectID="34180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_JY.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 501.162289 -208.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34181" ObjectName="EC-YM_JY.064Ld"/>
    <cge:TPSR_Ref TObjectID="34181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_JY.065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 723.495622 -208.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34182" ObjectName="EC-YM_JY.065Ld"/>
    <cge:TPSR_Ref TObjectID="34182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_JY.066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 944.828956 -208.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34183" ObjectName="EC-YM_JY.066Ld"/>
    <cge:TPSR_Ref TObjectID="34183"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3ff9850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="169,-1002 169,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27188@1" ObjectIDZND0="27187@0" Pin0InfoVect0LinkObjId="SW-170310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170312_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="169,-1002 169,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ff110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="169,-1054 169,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27187@1" ObjectIDZND0="27189@0" Pin0InfoVect0LinkObjId="SW-170313_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="169,-1054 169,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4075640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="169,-1133 169,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27190@x" ObjectIDND1="g_43e37e0@0" ObjectIDND2="g_3646310@0" ObjectIDZND0="27189@1" Pin0InfoVect0LinkObjId="SW-170313_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-170314_0" Pin1InfoVect1LinkObjId="g_43e37e0_0" Pin1InfoVect2LinkObjId="g_3646310_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="169,-1133 169,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d20790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="169,-966 169,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27188@0" ObjectIDZND0="27184@0" Pin0InfoVect0LinkObjId="g_33996f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170312_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="169,-966 169,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_435e0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="864,-944 864,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27184@0" ObjectIDZND0="27191@0" Pin0InfoVect0LinkObjId="SW-170362_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d20790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="864,-944 864,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dca390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="864,-1106 864,-1131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_44ee7d0@1" ObjectIDZND0="g_2daec80@0" Pin0InfoVect0LinkObjId="g_2daec80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44ee7d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="864,-1106 864,-1131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4745300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="825,-1021 825,-1037 865,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_3a3da30@0" ObjectIDZND0="27191@x" ObjectIDZND1="g_44ee7d0@0" Pin0InfoVect0LinkObjId="SW-170362_0" Pin0InfoVect1LinkObjId="g_44ee7d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a3da30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="825,-1021 825,-1037 865,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3faf820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="864,-1006 864,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="27191@1" ObjectIDZND0="g_3a3da30@0" ObjectIDZND1="g_44ee7d0@0" Pin0InfoVect0LinkObjId="g_3a3da30_0" Pin0InfoVect1LinkObjId="g_44ee7d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170362_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="864,-1006 864,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ffd3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="864,-1037 864,-1062 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3a3da30@0" ObjectIDND1="27191@x" ObjectIDZND0="g_44ee7d0@0" Pin0InfoVect0LinkObjId="g_44ee7d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3a3da30_0" Pin1InfoVect1LinkObjId="SW-170362_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="864,-1037 864,-1062 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33996f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="169,-916 169,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_3a3dd90@1" ObjectIDZND0="27184@0" Pin0InfoVect0LinkObjId="g_3d20790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a3dd90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="169,-916 169,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4872650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="169,-871 169,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3a3dd90@0" ObjectIDZND0="g_435e460@1" Pin0InfoVect0LinkObjId="g_435e460_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a3dd90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="169,-871 169,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30cd070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="513,-813 513,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="27193@0" ObjectIDZND0="27214@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170366_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="513,-813 513,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d27780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="513,-699 513,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="27214@1" ObjectIDZND0="27195@1" Pin0InfoVect0LinkObjId="SW-170374_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30cd070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="513,-699 513,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a5e3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="513,-638 513,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27195@0" ObjectIDZND0="27196@1" Pin0InfoVect0LinkObjId="SW-170376_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170374_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="513,-638 513,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_437c3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="513,-575 513,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27196@0" ObjectIDZND0="27185@0" Pin0InfoVect0LinkObjId="g_4a56cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="513,-575 513,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3936990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="38,-546 38,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27185@0" ObjectIDZND0="27192@0" Pin0InfoVect0LinkObjId="SW-170365_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_437c3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="38,-546 38,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44ef1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="38,-711 38,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_435f7f0@1" ObjectIDZND0="g_39d12e0@0" Pin0InfoVect0LinkObjId="g_39d12e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_435f7f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="38,-711 38,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3553060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1,-626 -1,-642 38,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_381a200@0" ObjectIDZND0="27192@x" ObjectIDZND1="g_435f7f0@0" Pin0InfoVect0LinkObjId="SW-170365_0" Pin0InfoVect1LinkObjId="g_435f7f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_381a200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-1,-626 -1,-642 38,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407ad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="38,-611 38,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="27192@1" ObjectIDZND0="g_381a200@0" ObjectIDZND1="g_435f7f0@0" Pin0InfoVect0LinkObjId="g_381a200_0" Pin0InfoVect1LinkObjId="g_435f7f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170365_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="38,-611 38,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35f0b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="38,-642 38,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_381a200@0" ObjectIDND1="27192@x" ObjectIDZND0="g_435f7f0@0" Pin0InfoVect0LinkObjId="g_435f7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_381a200_0" Pin1InfoVect1LinkObjId="SW-170365_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="38,-642 38,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3aad9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-155,-546 -155,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27185@0" ObjectIDZND0="27198@1" Pin0InfoVect0LinkObjId="SW-170503_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_437c3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-155,-546 -155,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35d7080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-155,-461 -155,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27198@0" ObjectIDZND0="27197@1" Pin0InfoVect0LinkObjId="SW-170501_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170503_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-155,-461 -155,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3648910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-155,-395 -155,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27197@0" ObjectIDZND0="27199@1" Pin0InfoVect0LinkObjId="SW-170504_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170501_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-155,-395 -155,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3098ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-155,-283 -131,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34178@x" ObjectIDND1="27199@x" ObjectIDZND0="g_3a451d0@0" Pin0InfoVect0LinkObjId="g_3a451d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YM_JY.061Ld_0" Pin1InfoVect1LinkObjId="SW-170504_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-155,-283 -131,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2da16f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-155,-283 -155,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3a451d0@0" ObjectIDND1="27199@x" ObjectIDZND0="34178@0" Pin0InfoVect0LinkObjId="EC-YM_JY.061Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3a451d0_0" Pin1InfoVect1LinkObjId="SW-170504_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-155,-283 -155,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d7eed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-155,-321 -155,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="27199@0" ObjectIDZND0="34178@x" ObjectIDZND1="g_3a451d0@0" Pin0InfoVect0LinkObjId="EC-YM_JY.061Ld_0" Pin0InfoVect1LinkObjId="g_3a451d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170504_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-155,-321 -155,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_422f870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="67,-546 67,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27185@0" ObjectIDZND0="27201@1" Pin0InfoVect0LinkObjId="SW-170555_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_437c3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="67,-546 67,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a4eb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="67,-461 67,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27201@0" ObjectIDZND0="27200@1" Pin0InfoVect0LinkObjId="SW-170553_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170555_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="67,-461 67,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44f3bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="67,-395 67,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27200@0" ObjectIDZND0="27202@1" Pin0InfoVect0LinkObjId="SW-170556_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170553_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="67,-395 67,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3803590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="67,-283 91,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34179@x" ObjectIDND1="27202@x" ObjectIDZND0="g_3b3f720@0" Pin0InfoVect0LinkObjId="g_3b3f720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YM_JY.062Ld_0" Pin1InfoVect1LinkObjId="SW-170556_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="67,-283 91,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d879e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="67,-283 67,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3b3f720@0" ObjectIDND1="27202@x" ObjectIDZND0="34179@0" Pin0InfoVect0LinkObjId="EC-YM_JY.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3b3f720_0" Pin1InfoVect1LinkObjId="SW-170556_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="67,-283 67,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c12180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="67,-321 67,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="27202@0" ObjectIDZND0="34179@x" ObjectIDZND1="g_3b3f720@0" Pin0InfoVect0LinkObjId="EC-YM_JY.062Ld_0" Pin0InfoVect1LinkObjId="g_3b3f720_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170556_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="67,-321 67,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_382de90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-546 289,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27185@0" ObjectIDZND0="27204@1" Pin0InfoVect0LinkObjId="SW-170607_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_437c3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-546 289,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d19780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-461 289,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27204@0" ObjectIDZND0="27203@1" Pin0InfoVect0LinkObjId="SW-170605_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-461 289,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3751a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-395 289,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27203@0" ObjectIDZND0="27205@1" Pin0InfoVect0LinkObjId="SW-170608_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170605_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-395 289,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_435f310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-283 313,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34180@x" ObjectIDND1="27205@x" ObjectIDZND0="g_438e840@0" Pin0InfoVect0LinkObjId="g_438e840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YM_JY.063Ld_0" Pin1InfoVect1LinkObjId="SW-170608_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-283 313,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_46e6100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-283 289,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_438e840@0" ObjectIDND1="27205@x" ObjectIDZND0="34180@0" Pin0InfoVect0LinkObjId="EC-YM_JY.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_438e840_0" Pin1InfoVect1LinkObjId="SW-170608_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="289,-283 289,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_49d7870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="289,-321 289,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="27205@0" ObjectIDZND0="34180@x" ObjectIDZND1="g_438e840@0" Pin0InfoVect0LinkObjId="EC-YM_JY.063Ld_0" Pin0InfoVect1LinkObjId="g_438e840_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170608_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="289,-321 289,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3958520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="510,-546 510,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27185@0" ObjectIDZND0="27207@1" Pin0InfoVect0LinkObjId="SW-170659_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_437c3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="510,-546 510,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_362a890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="510,-461 510,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27207@0" ObjectIDZND0="27206@1" Pin0InfoVect0LinkObjId="SW-170657_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="510,-461 510,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36d9160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="510,-395 510,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27206@0" ObjectIDZND0="27208@1" Pin0InfoVect0LinkObjId="SW-170660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170657_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="510,-395 510,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35930e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="512,-283 534,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34181@x" ObjectIDND1="27208@x" ObjectIDZND0="g_2a94060@0" Pin0InfoVect0LinkObjId="g_2a94060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YM_JY.064Ld_0" Pin1InfoVect1LinkObjId="SW-170660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="512,-283 534,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b65d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="510,-283 510,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2a94060@0" ObjectIDND1="27208@x" ObjectIDZND0="34181@0" Pin0InfoVect0LinkObjId="EC-YM_JY.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a94060_0" Pin1InfoVect1LinkObjId="SW-170660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="510,-283 510,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3417b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="510,-321 510,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="27208@0" ObjectIDZND0="34181@x" ObjectIDZND1="g_2a94060@0" Pin0InfoVect0LinkObjId="EC-YM_JY.064Ld_0" Pin0InfoVect1LinkObjId="g_2a94060_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="510,-321 510,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_362f2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="732,-546 732,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27185@0" ObjectIDZND0="27210@1" Pin0InfoVect0LinkObjId="SW-170711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_437c3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="732,-546 732,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b42800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="732,-461 732,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27210@0" ObjectIDZND0="27209@1" Pin0InfoVect0LinkObjId="SW-170709_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="732,-461 732,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_480f780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="732,-395 732,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27209@0" ObjectIDZND0="27211@1" Pin0InfoVect0LinkObjId="SW-170712_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170709_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="732,-395 732,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_487ed70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="732,-283 756,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34182@x" ObjectIDND1="27211@x" ObjectIDZND0="g_3a44e10@0" Pin0InfoVect0LinkObjId="g_3a44e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YM_JY.065Ld_0" Pin1InfoVect1LinkObjId="SW-170712_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="732,-283 756,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_487eb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="732,-283 732,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3a44e10@0" ObjectIDND1="27211@x" ObjectIDZND0="34182@0" Pin0InfoVect0LinkObjId="EC-YM_JY.065Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3a44e10_0" Pin1InfoVect1LinkObjId="SW-170712_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="732,-283 732,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d2f0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="732,-321 732,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="27211@0" ObjectIDZND0="34182@x" ObjectIDZND1="g_3a44e10@0" Pin0InfoVect0LinkObjId="EC-YM_JY.065Ld_0" Pin0InfoVect1LinkObjId="g_3a44e10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170712_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="732,-321 732,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d1ee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="954,-546 954,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27185@0" ObjectIDZND0="27213@1" Pin0InfoVect0LinkObjId="SW-170763_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_437c3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="954,-546 954,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d7ec90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="954,-461 954,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27213@0" ObjectIDZND0="27212@1" Pin0InfoVect0LinkObjId="SW-170761_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="954,-461 954,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_435f530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="954,-395 954,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27212@0" ObjectIDZND0="27217@1" Pin0InfoVect0LinkObjId="SW-170847_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="954,-395 954,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3861c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="954,-283 978,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34183@x" ObjectIDND1="27217@x" ObjectIDZND0="g_48fb1f0@0" Pin0InfoVect0LinkObjId="g_48fb1f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YM_JY.066Ld_0" Pin1InfoVect1LinkObjId="SW-170847_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="954,-283 978,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38619f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="954,-283 954,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_48fb1f0@0" ObjectIDND1="27217@x" ObjectIDZND0="34183@0" Pin0InfoVect0LinkObjId="EC-YM_JY.066Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_48fb1f0_0" Pin1InfoVect1LinkObjId="SW-170847_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="954,-283 954,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3860920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="954,-321 954,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="27217@0" ObjectIDZND0="34183@x" ObjectIDZND1="g_48fb1f0@0" Pin0InfoVect0LinkObjId="EC-YM_JY.066Ld_0" Pin0InfoVect1LinkObjId="g_48fb1f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170847_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="954,-321 954,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4674bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1181,-472 1181,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_4e3e510@0" ObjectIDZND0="g_475f150@1" Pin0InfoVect0LinkObjId="g_475f150_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4e3e510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1181,-472 1181,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4a56cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1181,-517 1181,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_4e3e510@1" ObjectIDZND0="27185@0" Pin0InfoVect0LinkObjId="g_437c3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4e3e510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1181,-517 1181,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3658e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="169,-1202 169,-1219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_43e37e0@0" ObjectIDND1="27189@x" ObjectIDND2="27190@x" ObjectIDZND0="38092@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_43e37e0_0" Pin1InfoVect1LinkObjId="SW-170313_0" Pin1InfoVect2LinkObjId="SW-170314_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="169,-1202 169,-1219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b511e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="169,-1133 146,-1133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="27189@x" ObjectIDND1="g_43e37e0@0" ObjectIDND2="g_3646310@0" ObjectIDZND0="27190@0" Pin0InfoVect0LinkObjId="SW-170314_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-170313_0" Pin1InfoVect1LinkObjId="g_43e37e0_0" Pin1InfoVect2LinkObjId="g_3646310_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="169,-1133 146,-1133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a44a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="110,-1133 94,-1133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27190@1" ObjectIDZND0="g_43ca0c0@0" Pin0InfoVect0LinkObjId="g_43ca0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170314_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="110,-1133 94,-1133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_373caa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="169,-1202 169,-1133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_43e37e0@0" ObjectIDND1="g_3646310@0" ObjectIDND2="38092@1" ObjectIDZND0="27189@x" ObjectIDZND1="27190@x" Pin0InfoVect0LinkObjId="SW-170313_0" Pin0InfoVect1LinkObjId="SW-170314_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_43e37e0_0" Pin1InfoVect1LinkObjId="g_3646310_0" Pin1InfoVect2LinkObjId="g_3658e60_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="169,-1202 169,-1133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4060130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="169,-1202 219,-1202 219,-1191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="27189@x" ObjectIDND1="27190@x" ObjectIDND2="g_3646310@0" ObjectIDZND0="g_43e37e0@0" Pin0InfoVect0LinkObjId="g_43e37e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-170313_0" Pin1InfoVect1LinkObjId="SW-170314_0" Pin1InfoVect2LinkObjId="g_3646310_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="169,-1202 219,-1202 219,-1191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_47f8060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="169,-1202 150,-1202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_43e37e0@0" ObjectIDND1="27189@x" ObjectIDND2="27190@x" ObjectIDZND0="g_3646310@0" Pin0InfoVect0LinkObjId="g_3646310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_43e37e0_0" Pin1InfoVect1LinkObjId="SW-170313_0" Pin1InfoVect2LinkObjId="SW-170314_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="169,-1202 150,-1202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4f79260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="513,-840 513,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27193@1" ObjectIDZND0="27194@0" Pin0InfoVect0LinkObjId="SW-170368_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170366_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="513,-840 513,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4911680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="513,-907 513,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27194@1" ObjectIDZND0="27184@0" Pin0InfoVect0LinkObjId="g_3d20790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170368_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="513,-907 513,-944 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153540" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -425.000000 -1165.000000)" xlink:href="#dynamicPoint:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26030" ObjectName="DYN-YM_JY"/>
     <cge:Meas_Ref ObjectId="153540"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_488e270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -364.000000 159.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c109a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -375.000000 144.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_340e7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -350.000000 129.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d8ef60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 576.000000 673.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_464ad90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 565.000000 658.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9cd70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 590.000000 643.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_482ebb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 225.000000 1062.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c10f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 214.000000 1047.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39a5640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 239.000000 1032.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a591e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 564.000000 847.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43ae130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 553.000000 832.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd7cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 817.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3583d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 619.000000 740.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346d9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 619.000000 755.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bb5450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -103.000000 994.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b20710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -87.000000 978.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30cd810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -95.000000 1009.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339bfe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -95.000000 1024.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_370ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -95.000000 1038.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_43ca0c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 76.000000 -1127.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_391fef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -567.000000 -1239.500000) translate(0,16)">姜驿变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_439aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_439aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_439aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_439aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_439aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_439aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_439aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_439aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_439aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_475fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c0f9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1155.000000 -192.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35bd4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -293.000000 -538.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bd2350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -6.000000 -807.000000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aaed80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 143.000000 -743.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b58d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -1192.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4364330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 127.000000 -1271.000000) translate(0,12)">35kV边驿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4391f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -148.000000 -486.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35f71d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -148.000000 -346.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3751870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -147.000000 -416.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43806b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 961.000000 -486.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0f2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -416.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a949e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 74.000000 -486.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30c90c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 74.000000 -346.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_482f3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 76.000000 -416.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_488fd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 517.000000 -486.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37ffd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 517.000000 -346.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b21220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 519.000000 -416.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38085c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 739.000000 -486.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43e3a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 739.000000 -346.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ac620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 741.000000 -416.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc1910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 296.000000 -486.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c1c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 296.000000 -346.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b82640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 299.000000 -416.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37aebe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 961.000000 -346.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3652e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -897.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3792960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 522.000000 -834.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b5a6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 522.000000 -659.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4792dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -600.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d05a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -746.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d1c530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -991.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_49e0d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 179.000000 -1048.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bd7170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.000000 -1102.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43640c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 115.000000 -1154.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4718010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 871.000000 -995.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42b8580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45.000000 -600.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_364e3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -40.000000 -932.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_48ed750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 359.000000 -768.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_48ed750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 359.000000 -768.000000) translate(0,27)">SZ11-2500/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_48ed750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 359.000000 -768.000000) translate(0,42)">35000/10500</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_48ed750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 359.000000 -768.000000) translate(0,57)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3a588f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -355.000000 -1204.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2db32b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -355.000000 -1241.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3687890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -704.000000 -881.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39230b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 199.000000 -901.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37fb9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -198.000000 -193.000000) translate(0,12)">10kV阿洒姑线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_438eee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 33.000000 -195.000000) translate(0,12)">10kV贡茶线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b53a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 255.000000 -197.000000) translate(0,12)">10kV金河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a45560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -199.000000) translate(0,12)">10kV红花箐线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a675c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 695.000000 -201.000000) translate(0,12)">10kV姜中线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34204d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 913.000000 -201.000000) translate(0,12)">10kV大地线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2aa3e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -738.000000 -258.500000) translate(0,17)">元谋巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_4855b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -592.000000 -268.000000) translate(0,16)">18787879021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_4855b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -592.000000 -268.000000) translate(0,36)">13908784331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_36d4880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -592.000000 -303.000000) translate(0,16)">8358118</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3c157e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -232.000000 -1225.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3630f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -279.000000 -659.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_422e630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -279.000000 -674.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ba3390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -279.000000 -690.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_366b510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -273.000000 -642.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3783f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -267.000000 -582.000000) translate(0,12)">F(Hz)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38515f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -287.000000 -627.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b69b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -287.000000 -611.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_377ccb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -287.000000 -596.000000) translate(0,12)">Uca(kV):</text>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YM_JY.YM_JY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-43,-944 1075,-944 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27184" ObjectName="BS-YM_JY.YM_JY_3IM"/>
    <cge:TPSR_Ref TObjectID="27184"/></metadata>
   <polyline fill="none" opacity="0" points="-43,-944 1075,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_JY.YM_JY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-248,-546 1274,-546 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27185" ObjectName="BS-YM_JY.YM_JY_9IM"/>
    <cge:TPSR_Ref TObjectID="27185"/></metadata>
   <polyline fill="none" opacity="0" points="-248,-546 1274,-546 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="27184" cx="169" cy="-944" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27184" cx="864" cy="-944" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27184" cx="513" cy="-944" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27185" cx="513" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27185" cx="38" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27185" cx="-155" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27185" cx="67" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27185" cx="289" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27185" cx="510" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27185" cx="732" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27185" cx="954" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27185" cx="1181" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="27184" cx="169" cy="-944" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YM_JY"/>
</svg>