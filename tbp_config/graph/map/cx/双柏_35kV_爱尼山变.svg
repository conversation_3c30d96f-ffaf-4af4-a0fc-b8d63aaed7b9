<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-161" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-262 -1188 2651 1400">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape201">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.485753" x1="3" x2="15" y1="27" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="15" x2="9" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.282524" x1="6" x2="6" y1="90" y2="96"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.532624" x1="5" x2="8" y1="100" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="3" x2="10" y1="98" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="0" x2="13" y1="96" y2="96"/>
    <polyline arcFlag="1" points="6,79 7,79 7,79 8,79 9,79 9,78 10,78 11,77 11,77 11,76 12,75 12,75 12,74 12,73 12,72 12,72 11,71 11,70 11,70 10,69 9,69 9,68 8,68 7,68 7,68 6,68 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,68 7,68 7,68 8,68 9,68 9,67 10,67 11,66 11,66 11,65 12,64 12,64 12,63 12,62 12,61 12,61 11,60 11,59 11,59 10,58 9,58 9,57 8,57 7,57 7,57 6,57 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,57 7,57 7,57 8,57 9,57 9,56 10,56 11,55 11,55 11,54 12,53 12,53 12,52 12,51 12,50 12,50 11,49 11,48 11,48 10,47 9,47 9,46 8,46 7,46 7,46 6,46 " stroke-width="0.815047"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="6" x2="6" y1="4" y2="13"/>
    <polyline arcFlag="1" points="6,46 7,46 7,46 8,46 9,46 9,45 10,45 11,44 11,44 11,43 12,42 12,42 12,41 12,40 12,39 12,39 11,38 11,37 11,37 10,36 9,36 9,35 8,35 7,35 7,35 6,35 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,35 7,35 7,35 8,35 9,35 9,34 10,34 11,33 11,33 11,32 12,31 12,31 12,30 12,29 12,28 12,28 11,27 11,26 11,26 10,25 9,25 9,24 8,24 7,24 7,24 6,24 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,90 7,90 7,90 8,90 9,90 9,89 10,89 11,88 11,88 11,87 12,86 12,86 12,85 12,84 12,83 12,83 11,82 11,81 11,81 10,80 9,80 9,79 8,79 7,79 7,79 6,79 " stroke-width="0.815047"/>
    <polyline arcFlag="1" points="6,24 7,24 7,24 8,24 9,24 9,23 10,23 11,22 11,22 11,21 12,20 12,20 12,19 12,18 12,17 12,17 11,16 11,15 11,15 10,14 9,14 9,13 8,13 7,13 7,13 6,13 " stroke-width="0.815047"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.71892" x1="15" x2="15" y1="8" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape94_0">
    <circle cx="13" cy="62" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="13" y1="38" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="40" x2="33" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="46" x2="28" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="39" x2="35" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="42" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="31" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="9" y1="0" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="13" y1="7" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="12,70 8,61 18,61 12,70 "/>
   </symbol>
   <symbol id="transformer2:shape94_1">
    <ellipse cx="13" cy="43" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="46" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="42" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="38" y2="42"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="58" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="58" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="38" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="51" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="59" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="37" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="70" y2="61"/>
    <ellipse cx="21" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="18" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape144">
    <circle cx="17" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="0" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="3" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="2" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="5" x2="10" y1="37" y2="37"/>
    <polyline points="5,7 5,37 " stroke-width="1"/>
    <circle cx="17" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="39" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="15" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="30" x2="30" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="34" x2="30" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="34" x2="30" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="27" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="33" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="41" x2="38" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="30" x2="27" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="39" y2="36"/>
    <circle cx="30" cy="23" r="7.5" stroke-width="0.804311"/>
    <circle cx="29" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="22" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="14" y1="25" y2="25"/>
    <circle cx="40" cy="30" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_297ec40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_297fda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2980750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2981420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2982680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29832a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2983d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_29847c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e187c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e187c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2987a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2987a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29892b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29892b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_298a2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_298bf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_298cba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_298da80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_298e360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_298fb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2990640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2990dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2991580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2992660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2992fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2993ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2994490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2995930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29964a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29974c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2998110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a6420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a6ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_299a510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_299b9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1410" width="2661" x="-267" y="-1193"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cafbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 236.000000 880.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae9960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 236.000000 866.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae9b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 236.000000 852.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae9d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 242.000000 837.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae9f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.000000 822.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aea1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 776.000000 -60.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aea3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 -75.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aea5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.000000 -90.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aea810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 202.000000 520.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aeaa20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 202.000000 506.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aeac00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 202.000000 492.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aeade0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 208.000000 477.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aeafc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 194.000000 462.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aeb290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 694.000000 874.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aeb470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.000000 859.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aeb650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 708.000000 844.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aeb920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1477.000000 882.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aebb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.000000 867.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aebce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1491.000000 852.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aebfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 973.000000 692.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aec190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.000000 677.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aec370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 987.000000 662.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aec730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 997.000000 521.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aec920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 986.000000 506.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aecb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1011.000000 491.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b83680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -166.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b83aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 -181.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b83ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 576.000000 -196.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b84010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 934.000000 -59.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b84270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 923.000000 -74.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b844b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 948.000000 -89.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b847e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 -60.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b84a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1144.000000 -75.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b84c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1169.000000 -90.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b84fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 -61.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b85210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1420.000000 -76.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b85450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1445.000000 -91.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b85780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1693.000000 -61.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b859e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1682.000000 -76.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b85c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1707.000000 -91.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bc9990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 353.000000 -156.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bc9ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 342.000000 -171.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bca130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 367.000000 -186.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="64" stroke="rgb(0,255,0)" stroke-width="1" width="37" x="610" y="11"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(0,255,0)" stroke-width="1" width="34" x="653" y="40"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(0,255,0)" stroke-width="1" width="10" x="658" y="43"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="92" stroke="rgb(0,255,0)" stroke-width="1" width="20" x="558" y="8"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="1" width="6" x="566" y="49"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(0,255,0)" stroke-width="0.5" width="32" x="653" y="-92"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-28" y="-971"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-193388">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 650.000000 -941.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29405" ObjectName="SW-SB_ANS.SB_ANS_39167SW"/>
     <cge:Meas_Ref ObjectId="193388"/>
    <cge:TPSR_Ref TObjectID="29405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111112">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1249.000000 -708.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21387" ObjectName="SW-SB_ANS.SB_ANS_3901SW"/>
     <cge:Meas_Ref ObjectId="111112"/>
    <cge:TPSR_Ref TObjectID="21387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111024">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 914.000000 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21385" ObjectName="SW-SB_ANS.SB_ANS_0011SW"/>
     <cge:Meas_Ref ObjectId="111024"/>
    <cge:TPSR_Ref TObjectID="21385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111082">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 700.000000 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21386" ObjectName="SW-SB_ANS.SB_ANS_0901SW"/>
     <cge:Meas_Ref ObjectId="111082"/>
    <cge:TPSR_Ref TObjectID="21386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111145">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.000000 -338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21391" ObjectName="SW-SB_ANS.SB_ANS_0421SW"/>
     <cge:Meas_Ref ObjectId="111145"/>
    <cge:TPSR_Ref TObjectID="21391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111146">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21392" ObjectName="SW-SB_ANS.SB_ANS_0426SW"/>
     <cge:Meas_Ref ObjectId="111146"/>
    <cge:TPSR_Ref TObjectID="21392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111199">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1478.000000 -334.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21397" ObjectName="SW-SB_ANS.SB_ANS_0441SW"/>
     <cge:Meas_Ref ObjectId="111199"/>
    <cge:TPSR_Ref TObjectID="21397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111200">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1478.000000 -176.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21398" ObjectName="SW-SB_ANS.SB_ANS_0446SW"/>
     <cge:Meas_Ref ObjectId="111200"/>
    <cge:TPSR_Ref TObjectID="21398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111226">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1733.000000 -336.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21400" ObjectName="SW-SB_ANS.SB_ANS_0451SW"/>
     <cge:Meas_Ref ObjectId="111226"/>
    <cge:TPSR_Ref TObjectID="21400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111227">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1733.000000 -178.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21401" ObjectName="SW-SB_ANS.SB_ANS_0456SW"/>
     <cge:Meas_Ref ObjectId="111227"/>
    <cge:TPSR_Ref TObjectID="21401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1962.000000 -334.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111172">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 -337.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21394" ObjectName="SW-SB_ANS.SB_ANS_0431SW"/>
     <cge:Meas_Ref ObjectId="111172"/>
    <cge:TPSR_Ref TObjectID="21394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111173">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 -179.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21395" ObjectName="SW-SB_ANS.SB_ANS_0436SW"/>
     <cge:Meas_Ref ObjectId="111173"/>
    <cge:TPSR_Ref TObjectID="21395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110927">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 629.000000 -879.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21379" ObjectName="SW-SB_ANS.SB_ANS_3916SW"/>
     <cge:Meas_Ref ObjectId="110927"/>
    <cge:TPSR_Ref TObjectID="21379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110926">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 629.000000 -780.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21378" ObjectName="SW-SB_ANS.SB_ANS_3911SW"/>
     <cge:Meas_Ref ObjectId="110926"/>
    <cge:TPSR_Ref TObjectID="21378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1417.000000 -887.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29404" ObjectName="SW-SB_ANS.SB_ANS_3926SW"/>
     <cge:Meas_Ref ObjectId="193385"/>
    <cge:TPSR_Ref TObjectID="29404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1417.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29403" ObjectName="SW-SB_ANS.SB_ANS_3921SW"/>
     <cge:Meas_Ref ObjectId="193384"/>
    <cge:TPSR_Ref TObjectID="29403"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193387">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 914.000000 -695.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29407" ObjectName="SW-SB_ANS.SB_ANS_3011SW"/>
     <cge:Meas_Ref ObjectId="193387"/>
    <cge:TPSR_Ref TObjectID="29407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 519.000000 -989.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193386">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1359.000000 -905.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29406" ObjectName="SW-SB_ANS.SB_ANS_39267SW"/>
     <cge:Meas_Ref ObjectId="193386"/>
    <cge:TPSR_Ref TObjectID="29406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.000000 -263.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111118">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 827.000000 -338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21388" ObjectName="SW-SB_ANS.SB_ANS_0411SW"/>
     <cge:Meas_Ref ObjectId="111118"/>
    <cge:TPSR_Ref TObjectID="21388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111119">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 827.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21389" ObjectName="SW-SB_ANS.SB_ANS_0416SW"/>
     <cge:Meas_Ref ObjectId="111119"/>
    <cge:TPSR_Ref TObjectID="21389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231299">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 560.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38561" ObjectName="SW-SB_ANS.SB_ANS_0461SW"/>
     <cge:Meas_Ref ObjectId="231299"/>
    <cge:TPSR_Ref TObjectID="38561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231300">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 560.000000 -234.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38562" ObjectName="SW-SB_ANS.SB_ANS_0466SW"/>
     <cge:Meas_Ref ObjectId="231300"/>
    <cge:TPSR_Ref TObjectID="38562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231328">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 609.000000 -20.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38571" ObjectName="SW-SB_ANS.SB_ANS_0030SW"/>
     <cge:Meas_Ref ObjectId="231328"/>
    <cge:TPSR_Ref TObjectID="38571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111253">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 400.000000 -341.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21403" ObjectName="SW-SB_ANS.SB_ANS_0471SW"/>
     <cge:Meas_Ref ObjectId="111253"/>
    <cge:TPSR_Ref TObjectID="21403"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111254">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 400.000000 -183.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21404" ObjectName="SW-SB_ANS.SB_ANS_0473SW"/>
     <cge:Meas_Ref ObjectId="111254"/>
    <cge:TPSR_Ref TObjectID="21404"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-249762">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 400.000000 -49.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41825" ObjectName="SW-SB_ANS.SB_ANS_0476SW"/>
     <cge:Meas_Ref ObjectId="249762"/>
    <cge:TPSR_Ref TObjectID="41825"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-SB_ANS.SB_ANS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="287,-767 1814,-767 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21375" ObjectName="BS-SB_ANS.SB_ANS_3IM"/>
    <cge:TPSR_Ref TObjectID="21375"/></metadata>
   <polyline fill="none" opacity="0" points="287,-767 1814,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_ANS.SB_ANS_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-417 2389,-417 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21376" ObjectName="BS-SB_ANS.SB_ANS_9IM"/>
    <cge:TPSR_Ref TObjectID="21376"/></metadata>
   <polyline fill="none" opacity="0" points="288,-417 2389,-417 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-SB_ANS.041Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 -12.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34332" ObjectName="EC-SB_ANS.041Ld"/>
    <cge:TPSR_Ref TObjectID="34332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_ANS.042Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 994.000000 -12.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34333" ObjectName="EC-SB_ANS.042Ld"/>
    <cge:TPSR_Ref TObjectID="34333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_ANS.044Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1482.000000 -8.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34335" ObjectName="EC-SB_ANS.044Ld"/>
    <cge:TPSR_Ref TObjectID="34335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_ANS.045Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1737.000000 -10.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34336" ObjectName="EC-SB_ANS.045Ld"/>
    <cge:TPSR_Ref TObjectID="34336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_ANS.043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1225.000000 -11.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34334" ObjectName="EC-SB_ANS.043Ld"/>
    <cge:TPSR_Ref TObjectID="34334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_ANS.047Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 404.000000 56.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42918" ObjectName="EC-SB_ANS.047Ld"/>
    <cge:TPSR_Ref TObjectID="42918"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c1e7a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 712.000000 -940.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bc18b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1362.000000 -877.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1ca1c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-767 1258,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21375@0" ObjectIDZND0="21387@1" Pin0InfoVect0LinkObjId="SW-111112_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-767 1258,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca1e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-696 1209,-696 1209,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21387@x" ObjectIDND1="g_1b3eb60@0" ObjectIDZND0="g_1ca2880@0" Pin0InfoVect0LinkObjId="g_1ca2880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-111112_0" Pin1InfoVect1LinkObjId="g_1b3eb60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-696 1209,-696 1209,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca2690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-713 1258,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21387@0" ObjectIDZND0="g_1ca2880@0" ObjectIDZND1="g_1b3eb60@0" Pin0InfoVect0LinkObjId="g_1ca2880_0" Pin0InfoVect1LinkObjId="g_1b3eb60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-713 1258,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c56140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-544 923,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="21407@0" ObjectIDZND0="21384@1" Pin0InfoVect0LinkObjId="SW-111023_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bdb090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-544 923,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c56330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-487 923,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21384@0" ObjectIDZND0="21385@1" Pin0InfoVect0LinkObjId="SW-111024_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111023_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-487 923,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bbc050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-336 676,-336 676,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21386@x" ObjectIDND1="0@x" ObjectIDZND0="g_1c56520@0" Pin0InfoVect0LinkObjId="g_1c56520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-111082_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-336 676,-336 676,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bbc8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-354 709,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21386@0" ObjectIDZND0="g_1c56520@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1c56520_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111082_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="709,-354 709,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bbd3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,-139 800,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21389@x" ObjectIDND1="g_1c20000@0" ObjectIDZND0="g_1bbcc00@0" Pin0InfoVect0LinkObjId="g_1bbcc00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-111119_0" Pin1InfoVect1LinkObjId="g_1c20000_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="836,-139 800,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bbdc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,-185 836,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21389@0" ObjectIDZND0="g_1bbcc00@0" ObjectIDZND1="g_1c20000@0" Pin0InfoVect0LinkObjId="g_1bbcc00_0" Pin0InfoVect1LinkObjId="g_1c20000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111119_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="836,-185 836,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4e3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-343 999,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21391@0" ObjectIDZND0="21393@1" Pin0InfoVect0LinkObjId="SW-111147_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111145_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,-343 999,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4e5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-261 999,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21393@0" ObjectIDZND0="21392@1" Pin0InfoVect0LinkObjId="SW-111146_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111147_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,-261 999,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4f170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-139 963,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21392@x" ObjectIDND1="34333@x" ObjectIDZND0="g_1c4e7a0@0" Pin0InfoVect0LinkObjId="g_1c4e7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-111146_0" Pin1InfoVect1LinkObjId="EC-SB_ANS.042Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,-139 963,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4f360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-185 999,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21392@0" ObjectIDZND0="g_1c4e7a0@0" ObjectIDZND1="34333@x" Pin0InfoVect0LinkObjId="g_1c4e7a0_0" Pin0InfoVect1LinkObjId="EC-SB_ANS.042Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111146_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="999,-185 999,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c4f550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-139 999,-33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1c4e7a0@0" ObjectIDND1="21392@x" ObjectIDZND0="34333@0" Pin0InfoVect0LinkObjId="EC-SB_ANS.042Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c4e7a0_0" Pin1InfoVect1LinkObjId="SW-111146_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,-139 999,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be8ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,-339 1487,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21397@0" ObjectIDZND0="21399@1" Pin0InfoVect0LinkObjId="SW-111201_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111199_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1487,-339 1487,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1be8cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,-256 1487,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21399@0" ObjectIDZND0="21398@1" Pin0InfoVect0LinkObjId="SW-111200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111201_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1487,-256 1487,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c8c160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,-135 1451,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21398@x" ObjectIDND1="34335@x" ObjectIDZND0="g_1be8ec0@0" Pin0InfoVect0LinkObjId="g_1be8ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-111200_0" Pin1InfoVect1LinkObjId="EC-SB_ANS.044Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1487,-135 1451,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c8c350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,-181 1487,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21398@0" ObjectIDZND0="g_1be8ec0@0" ObjectIDZND1="34335@x" Pin0InfoVect0LinkObjId="g_1be8ec0_0" Pin0InfoVect1LinkObjId="EC-SB_ANS.044Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1487,-181 1487,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c8c540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,-135 1487,-29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1be8ec0@0" ObjectIDND1="21398@x" ObjectIDZND0="34335@0" Pin0InfoVect0LinkObjId="EC-SB_ANS.044Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1be8ec0_0" Pin1InfoVect1LinkObjId="SW-111200_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1487,-135 1487,-29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bb7900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-341 1742,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21400@0" ObjectIDZND0="21402@1" Pin0InfoVect0LinkObjId="SW-111228_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111226_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-341 1742,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bb7b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-259 1742,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21402@0" ObjectIDZND0="21401@1" Pin0InfoVect0LinkObjId="SW-111227_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111228_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-259 1742,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cacfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-124 1718,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21401@x" ObjectIDND1="34336@x" ObjectIDZND0="g_1bb7d40@0" Pin0InfoVect0LinkObjId="g_1bb7d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-111227_0" Pin1InfoVect1LinkObjId="EC-SB_ANS.045Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-124 1718,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cad940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-183 1742,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21401@0" ObjectIDZND0="g_1bb7d40@0" ObjectIDZND1="34336@x" Pin0InfoVect0LinkObjId="g_1bb7d40_0" Pin0InfoVect1LinkObjId="EC-SB_ANS.045Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111227_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-183 1742,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1cadb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-124 1742,-31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1bb7d40@0" ObjectIDND1="21401@x" ObjectIDZND0="34336@0" Pin0InfoVect0LinkObjId="EC-SB_ANS.045Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bb7d40_0" Pin1InfoVect1LinkObjId="SW-111227_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-124 1742,-31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cadd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1969,-339 1969,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1969,-339 1969,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c96010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-342 1230,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21394@0" ObjectIDZND0="21396@1" Pin0InfoVect0LinkObjId="SW-111174_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111172_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-342 1230,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c96230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-259 1230,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21396@0" ObjectIDZND0="21395@1" Pin0InfoVect0LinkObjId="SW-111173_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111174_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-259 1230,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ba1460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-138 1194,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21395@x" ObjectIDND1="34334@x" ObjectIDZND0="g_1ba08b0@0" Pin0InfoVect0LinkObjId="g_1ba08b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-111173_0" Pin1InfoVect1LinkObjId="EC-SB_ANS.043Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-138 1194,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ba31e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-767 638,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21375@0" ObjectIDZND0="21378@0" Pin0InfoVect0LinkObjId="SW-110926_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-767 638,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ba4260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-920 638,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21379@1" ObjectIDZND0="29405@x" ObjectIDZND1="g_1c0b5d0@0" ObjectIDZND2="g_1c0c4e0@0" Pin0InfoVect0LinkObjId="SW-193388_0" Pin0InfoVect1LinkObjId="g_1c0b5d0_0" Pin0InfoVect2LinkObjId="g_1c0c4e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110927_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="638,-920 638,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ba44c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-946 655,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="21379@x" ObjectIDND1="g_1c0b5d0@0" ObjectIDND2="g_1c0c4e0@0" ObjectIDZND0="29405@0" Pin0InfoVect0LinkObjId="SW-193388_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-110927_0" Pin1InfoVect1LinkObjId="g_1c0b5d0_0" Pin1InfoVect2LinkObjId="g_1c0c4e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-946 655,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ba4720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="691,-946 716,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29405@1" ObjectIDZND0="g_1c1e7a0@0" Pin0InfoVect0LinkObjId="g_1c1e7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="691,-946 716,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b45730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-767 1426,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21375@0" ObjectIDZND0="29403@0" Pin0InfoVect0LinkObjId="SW-193384_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-767 1426,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b45990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-829 1426,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29403@1" ObjectIDZND0="21382@0" Pin0InfoVect0LinkObjId="SW-110956_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-829 1426,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b45bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-872 1426,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21382@1" ObjectIDZND0="29404@0" Pin0InfoVect0LinkObjId="SW-193385_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110956_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-872 1426,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bdabd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-767 923,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21375@0" ObjectIDZND0="29407@1" Pin0InfoVect0LinkObjId="SW-193387_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-767 923,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bdae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-700 923,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29407@0" ObjectIDZND0="21383@1" Pin0InfoVect0LinkObjId="SW-110998_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-700 923,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bdb090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-653 923,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="21383@0" ObjectIDZND0="21407@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110998_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-653 923,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c5f5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-1048 1426,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_1c0ef30@0" ObjectIDND1="g_1c1ef40@0" ObjectIDND2="g_1c0d100@0" ObjectIDZND0="37762@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c0ef30_0" Pin1InfoVect1LinkObjId="g_1c1ef40_0" Pin1InfoVect2LinkObjId="g_1c0d100_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-1048 1426,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c614b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-1109 709,-1109 709,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="voltageTransformer" ObjectIDZND0="g_1c60170@0" Pin0InfoVect0LinkObjId="g_1c60170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-1109 709,-1109 709,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c61710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-1109 598,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1c61970@0" Pin0InfoVect0LinkObjId="g_1c61970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-1109 598,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b4cba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="639,-1055 524,-1055 524,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" ObjectIDND0="g_1c0c4e0@0" ObjectIDND1="37763@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c0c4e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="639,-1055 524,-1055 524,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b4ce00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-994 524,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,-994 524,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0aa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-993 592,-993 592,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21379@x" ObjectIDND1="29405@x" ObjectIDND2="g_1c0c4e0@0" ObjectIDZND0="g_1c0b5d0@0" Pin0InfoVect0LinkObjId="g_1c0b5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-110927_0" Pin1InfoVect1LinkObjId="SW-193388_0" Pin1InfoVect2LinkObjId="g_1c0c4e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-993 592,-993 592,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0b3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-946 638,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21379@x" ObjectIDND1="29405@x" ObjectIDZND0="g_1c0b5d0@0" ObjectIDZND1="g_1c0c4e0@0" Pin0InfoVect0LinkObjId="g_1c0b5d0_0" Pin0InfoVect1LinkObjId="g_1c0c4e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110927_0" Pin1InfoVect1LinkObjId="SW-193388_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="638,-946 638,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0dd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-993 638,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21379@x" ObjectIDND1="29405@x" ObjectIDND2="g_1c0b5d0@0" ObjectIDZND0="g_1c0c4e0@0" Pin0InfoVect0LinkObjId="g_1c0c4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-110927_0" Pin1InfoVect1LinkObjId="SW-193388_0" Pin1InfoVect2LinkObjId="g_1c0b5d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-993 638,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0df80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-1048 1480,-1048 1480,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="voltageTransformer" ObjectIDND0="g_1c0ef30@0" ObjectIDND1="g_1c0d100@0" ObjectIDND2="37762@1" ObjectIDZND0="g_1c1ef40@0" Pin0InfoVect0LinkObjId="g_1c1ef40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c0ef30_0" Pin1InfoVect1LinkObjId="g_1c0d100_0" Pin1InfoVect2LinkObjId="g_1c5f5e0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-1048 1480,-1048 1480,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0ea70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-928 1426,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="29404@1" ObjectIDZND0="g_1c0d100@0" ObjectIDZND1="29406@x" Pin0InfoVect0LinkObjId="g_1c0d100_0" Pin0InfoVect1LinkObjId="SW-193386_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193385_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-928 1426,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0ecd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-1048 1404,-1048 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_1c1ef40@0" ObjectIDND1="g_1c0d100@0" ObjectIDND2="37762@1" ObjectIDZND0="g_1c0ef30@0" Pin0InfoVect0LinkObjId="g_1c0ef30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c1ef40_0" Pin1InfoVect1LinkObjId="g_1c0d100_0" Pin1InfoVect2LinkObjId="g_1c5f5e0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-1048 1404,-1048 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0fce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-961 1426,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="29404@x" ObjectIDND1="29406@x" ObjectIDZND0="g_1c0d100@0" Pin0InfoVect0LinkObjId="g_1c0d100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193385_0" Pin1InfoVect1LinkObjId="SW-193386_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-961 1426,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c0ff40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-1022 1426,-1048 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="g_1c0d100@1" ObjectIDZND0="g_1c0ef30@0" ObjectIDZND1="g_1c1ef40@0" ObjectIDZND2="37762@1" Pin0InfoVect0LinkObjId="g_1c0ef30_0" Pin0InfoVect1LinkObjId="g_1c1ef40_0" Pin0InfoVect2LinkObjId="g_1c5f5e0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c0d100_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-1022 1426,-1048 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc2300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-961 1368,-961 1368,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="29404@x" ObjectIDND1="g_1c0d100@0" ObjectIDZND0="29406@1" Pin0InfoVect0LinkObjId="SW-193386_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193385_0" Pin1InfoVect1LinkObjId="g_1c0d100_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-961 1368,-961 1368,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bc2560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1368,-910 1368,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29406@0" ObjectIDZND0="g_1bc18b0@0" Pin0InfoVect0LinkObjId="g_1bc18b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1368,-910 1368,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b3f3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-696 1258,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21387@x" ObjectIDND1="g_1ca2880@0" ObjectIDZND0="g_1b3eb60@0" Pin0InfoVect0LinkObjId="g_1b3eb60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-111112_0" Pin1InfoVect1LinkObjId="g_1ca2880_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-696 1258,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b3f640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-650 1258,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1b3eb60@1" ObjectIDZND0="g_1bc27c0@0" Pin0InfoVect0LinkObjId="g_1bc27c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b3eb60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-650 1258,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bd3d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-336 709,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1c56520@0" ObjectIDND1="21386@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c56520_0" Pin1InfoVect1LinkObjId="SW-111082_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-336 709,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bd3f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-268 709,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_1b42720@0" Pin0InfoVect0LinkObjId="g_1b42720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-268 709,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aaab00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,-343 836,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21388@0" ObjectIDZND0="21390@1" Pin0InfoVect0LinkObjId="SW-111120_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111118_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="836,-343 836,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aaad60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,-261 836,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21390@0" ObjectIDZND0="21389@1" Pin0InfoVect0LinkObjId="SW-111119_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="836,-261 836,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c2bb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="569,-321 569,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38560@1" ObjectIDZND0="38561@0" Pin0InfoVect0LinkObjId="SW-231299_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231301_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="569,-321 569,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c2bdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="569,-275 569,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38562@1" ObjectIDZND0="38560@0" Pin0InfoVect0LinkObjId="SW-231301_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="569,-275 569,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c2dc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="569,-213 533,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38562@x" ObjectIDND1="g_1bd41d0@0" ObjectIDZND0="g_1c2cf10@0" Pin0InfoVect0LinkObjId="g_1c2cf10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231300_0" Pin1InfoVect1LinkObjId="g_1bd41d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="569,-213 533,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c1f8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="569,-213 569,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1c2cf10@0" ObjectIDND1="g_1bd41d0@0" ObjectIDZND0="38562@0" Pin0InfoVect0LinkObjId="SW-231300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c2cf10_0" Pin1InfoVect1LinkObjId="g_1bd41d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="569,-213 569,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c1fb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="569,-100 569,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1c2c010@0" ObjectIDZND0="g_1bd41d0@1" Pin0InfoVect0LinkObjId="g_1bd41d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c2c010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="569,-100 569,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c1fda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="569,-184 569,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1bd41d0@0" ObjectIDZND0="g_1c2cf10@0" ObjectIDZND1="38562@x" Pin0InfoVect0LinkObjId="g_1c2cf10_0" Pin0InfoVect1LinkObjId="SW-231300_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bd41d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="569,-184 569,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c25ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,-139 836,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1bbcc00@0" ObjectIDND1="21389@x" ObjectIDZND0="g_1c20000@0" Pin0InfoVect0LinkObjId="g_1c20000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bbcc00_0" Pin1InfoVect1LinkObjId="SW-111119_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="836,-139 836,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c25e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,-65 836,-33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1c20000@1" ObjectIDZND0="34332@0" Pin0InfoVect0LinkObjId="EC-SB_ANS.041Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c20000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="836,-65 836,-33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6c7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-821 638,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21378@1" ObjectIDZND0="21381@0" Pin0InfoVect0LinkObjId="SW-110929_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110926_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-821 638,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6ca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-866 638,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21381@1" ObjectIDZND0="21379@0" Pin0InfoVect0LinkObjId="SW-110927_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110929_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-866 638,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6db10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-1128 638,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="37763@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1c0c4e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1c0c4e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="638,-1128 638,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b6dd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="638,-1055 638,-1044 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="37763@1" ObjectIDZND0="g_1c0c4e0@1" Pin0InfoVect0LinkObjId="g_1c0c4e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="638,-1055 638,-1044 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b8a050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-184 1230,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21395@0" ObjectIDZND0="g_1ba08b0@0" ObjectIDZND1="34334@x" Pin0InfoVect0LinkObjId="g_1ba08b0_0" Pin0InfoVect1LinkObjId="EC-SB_ANS.043Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111173_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-184 1230,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b8a240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-138 1230,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1ba08b0@0" ObjectIDND1="21395@x" ObjectIDZND0="34334@0" Pin0InfoVect0LinkObjId="EC-SB_ANS.043Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ba08b0_0" Pin1InfoVect1LinkObjId="SW-111173_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-138 1230,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b60df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-416 409,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21376@0" ObjectIDZND0="21403@1" Pin0InfoVect0LinkObjId="SW-111253_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bca820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,-416 409,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b63120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-346 409,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21403@0" ObjectIDZND0="21405@1" Pin0InfoVect0LinkObjId="SW-111255_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111253_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,-346 409,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b63380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-263 409,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21405@0" ObjectIDZND0="21404@1" Pin0InfoVect0LinkObjId="SW-111254_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111255_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,-263 409,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc76e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-176 445,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21404@x" ObjectIDND1="g_1bc8430@0" ObjectIDZND0="g_1bc6990@0" Pin0InfoVect0LinkObjId="g_1bc6990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-111254_0" Pin1InfoVect1LinkObjId="g_1bc8430_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,-176 445,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc81d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-188 409,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21404@0" ObjectIDZND0="g_1bc6990@0" ObjectIDZND1="g_1bc8430@0" Pin0InfoVect0LinkObjId="g_1bc6990_0" Pin0InfoVect1LinkObjId="g_1bc8430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111254_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="409,-188 409,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc9180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-176 409,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1bc6990@0" ObjectIDND1="21404@x" ObjectIDZND0="g_1bc8430@0" Pin0InfoVect0LinkObjId="g_1bc8430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1bc6990_0" Pin1InfoVect1LinkObjId="SW-111254_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,-176 409,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc93e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-104 409,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1bc8430@1" ObjectIDZND0="41825@1" Pin0InfoVect0LinkObjId="SW-249762_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc8430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,-104 409,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bc9640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,35 409,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="42918@0" ObjectIDZND0="41825@0" Pin0InfoVect0LinkObjId="SW-249762_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-SB_ANS.047Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="409,35 409,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bca820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="569,-387 569,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38561@1" ObjectIDZND0="21376@0" Pin0InfoVect0LinkObjId="g_1bcaa10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="569,-387 569,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcaa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="709,-390 709,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21386@1" ObjectIDZND0="21376@0" Pin0InfoVect0LinkObjId="g_1bca820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111082_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="709,-390 709,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcac00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="836,-379 836,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21388@1" ObjectIDZND0="21376@0" Pin0InfoVect0LinkObjId="g_1bca820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111118_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="836,-379 836,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcadf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-432 923,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21385@0" ObjectIDZND0="21376@0" Pin0InfoVect0LinkObjId="g_1bca820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111024_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-432 923,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcb020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,-379 999,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21391@1" ObjectIDZND0="21376@0" Pin0InfoVect0LinkObjId="g_1bca820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111145_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,-379 999,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcb250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1230,-378 1230,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21394@1" ObjectIDZND0="21376@0" Pin0InfoVect0LinkObjId="g_1bca820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111172_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-378 1230,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcb480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,-375 1487,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21397@1" ObjectIDZND0="21376@0" Pin0InfoVect0LinkObjId="g_1bca820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111199_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1487,-375 1487,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcb6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1742,-377 1742,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21400@1" ObjectIDZND0="21376@0" Pin0InfoVect0LinkObjId="g_1bca820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-111226_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1742,-377 1742,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bcb8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1969,-391 1969,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="21376@0" Pin0InfoVect0LinkObjId="g_1bca820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1969,-391 1969,-416 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="21375" cx="1258" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21375" cx="638" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21375" cx="1426" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21375" cx="923" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21376" cx="409" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21376" cx="569" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21376" cx="709" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21376" cx="836" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21376" cx="923" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21376" cx="999" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21376" cx="1230" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21376" cx="1487" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21376" cx="1742" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21376" cx="1969" cy="-416" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-109187" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 -982.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21233" ObjectName="DYN-SB_ANS"/>
     <cge:Meas_Ref ObjectId="109187"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c7de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -923.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c7de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -923.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c7de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -923.000000) translate(0,59)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c7de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -923.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c7de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -923.000000) translate(0,101)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c7de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -923.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c7de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -923.000000) translate(0,143)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c7de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -923.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c7de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -923.000000) translate(0,185)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c7de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -923.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c7de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -240.000000 -923.000000) translate(0,227)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a01fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 813.000000 10.000000) translate(0,15)">独田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cc8e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 10.000000) translate(0,15)">旧哨线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c1c530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1200.000000 10.000000) translate(0,15)">大麦地线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_194eab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1463.000000 10.000000) translate(0,15)">六合线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1953fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1711.000000 10.000000) translate(0,15)">乡政府线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1c530f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1c88b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -114.000000 -1049.500000) translate(0,16)">爱尼山变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1c727e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -1187.000000) translate(0,18)">35kV爱尼山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bf4580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 287.000000 -790.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bf4830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 252.000000 -440.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1c1edd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -1166.000000) translate(0,18)">35kV爱田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1c4ce60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1497.000000 -997.000000) translate(0,18)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1c54d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1227.000000 -583.000000) translate(0,18)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bbca90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 664.000000 -182.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cabe70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1942.000000 -194.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cabe70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1942.000000 -194.000000) translate(0,33)">S11-30kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1cacbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -135.000000 -113.000000) translate(0,17)">7814081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cadf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 -738.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1aec550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1045.000000 -595.000000) translate(0,12)">档位(档):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1ba4980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.000000 -1024.000000) translate(0,18)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bdb2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 -909.000000) translate(0,12)">3916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bdb900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 -810.000000) translate(0,12)">3911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bdbbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 664.000000 -972.000000) translate(0,12)">39167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bdbe30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1442.000000 -860.000000) translate(0,12)">392</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bdc070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -917.000000) translate(0,12)">3926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bdc2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1433.000000 -818.000000) translate(0,12)">3921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bdc860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 932.000000 -674.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bdca30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 930.000000 -725.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bdcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 713.000000 -611.000000) translate(0,15)">1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bdcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 713.000000 -611.000000) translate(0,33)">SZ11-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bdcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 713.000000 -611.000000) translate(0,51)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1bdcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 713.000000 -611.000000) translate(0,69)">Yd11，Ud%=6.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1c15f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 68.000000 -1063.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1c16ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 67.000000 -1022.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1c1d6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -658.000000) translate(0,24)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1b4d060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.000000 -998.000000) translate(0,18)">1号站用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1b4d060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 427.000000 -998.000000) translate(0,40)">变50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1b41a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2019.000000 -313.000000) translate(0,18)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1b41a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2019.000000 -313.000000) translate(0,40)">S13-50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c31a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 584.000000 103.000000) translate(0,15)">1号接地变及</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c31a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 584.000000 103.000000) translate(0,33)">消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c26060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 716.000000 -379.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c26690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -282.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c26bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -368.000000) translate(0,12)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c26e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -210.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b67c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1008.000000 -282.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b67ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1006.000000 -368.000000) translate(0,12)">0421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b680e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1006.000000 -210.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b68320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1239.000000 -280.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b68560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1237.000000 -367.000000) translate(0,12)">0431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b687a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1237.000000 -209.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b689e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1496.000000 -278.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b68c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1494.000000 -364.000000) translate(0,12)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b68e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1494.000000 -206.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b690a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1751.000000 -280.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b692e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1749.000000 -366.000000) translate(0,12)">0451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b69520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1749.000000 -208.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b69760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 932.000000 -508.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b699a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 930.000000 -457.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b69be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1375.000000 -935.000000) translate(0,12)">39267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b6cc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 647.000000 -860.000000) translate(0,12)">391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1b6e050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -11.500000 -960.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b6ef10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 578.000000 -315.000000) translate(0,12)">046</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b6f420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 -376.000000) translate(0,12)">0461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b6f660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 -264.000000) translate(0,12)">0466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b6f8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 625.000000 -50.000000) translate(0,12)">0030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b85e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -262.000000 -74.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b85e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -262.000000 -74.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b882d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -135.000000 -84.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b882d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -135.000000 -84.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1b882d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -135.000000 -84.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_1b89090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 965.000000 -596.000000) translate(0,13)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b8a450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 399.000000 72.000000) translate(0,15)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b8a450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 399.000000 72.000000) translate(0,33)">箐</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b8a450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 399.000000 72.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b64210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -371.000000) translate(0,12)">0471</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b64700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -213.000000) translate(0,12)">0473</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b64940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 418.000000 -284.000000) translate(0,12)">047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b670d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -79.000000) translate(0,12)">0476</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="630" x2="630" y1="23" y2="39"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.637931" x1="630" x2="630" y1="46" y2="62"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="639" x2="663" y1="62" y2="62"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="639" x2="658" y1="47" y2="47"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="639" x2="644" y1="39" y2="39"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="639" x2="644" y1="24" y2="24"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="663" x2="663" y1="67" y2="62"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="663" x2="680" y1="68" y2="68"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="668" x2="680" y1="47" y2="47"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="573" x2="565" y1="109" y2="109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.394631" x1="571" x2="567" y1="111" y2="111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.339777" x1="569" x2="568" y1="112" y2="112"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.533936" x1="569" x2="569" y1="96" y2="108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="680" x2="680" y1="55" y2="47"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="684" x2="680" y1="58" y2="58"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="684" x2="680" y1="58" y2="55"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="680" x2="680" y1="59" y2="68"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="648" x2="570" y1="-84" y2="-84"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="648" x2="655" y1="-84" y2="-84"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.533936" x1="698" x2="698" y1="-88" y2="-80"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.686489" x1="702" x2="702" y1="-87" y2="-82"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.339777" x1="705" x2="705" y1="-85" y2="-83"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.533936" x1="683" x2="698" y1="-84" y2="-84"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="618" x2="618" y1="-84" y2="-60"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="618" x2="618" y1="-25" y2="21"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="569" x2="569" y1="-7" y2="14"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="618" x2="540" y1="-7" y2="-7"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="569" x2="569" y1="13" y2="22"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="568" x2="570" y1="22" y2="22"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="569" x2="561" y1="40" y2="23"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="569" x2="569" y1="40" y2="49"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="569" x2="569" y1="65" y2="74"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="658" x2="678" y1="-66" y2="-100"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="683" x2="655" y1="-84" y2="-84"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="678" x2="683" y1="-100" y2="-100"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="659" x2="653" y1="-66" y2="-66"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c1ef40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1458.000000 -964.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c60170">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 687.000000 -1026.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bc27c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1241.000000 -584.000000)" xlink:href="#voltageTransformer:shape144"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b42720">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 684.000000 -193.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.000000 -904.000000)" xlink:href="#transformer2:shape94_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.000000 -904.000000)" xlink:href="#transformer2:shape94_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1956.000000 -225.000000)" xlink:href="#transformer2:shape94_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1956.000000 -225.000000)" xlink:href="#transformer2:shape94_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_ANS.SB_ANS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="29925"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 892.000000 -541.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 892.000000 -541.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21407" ObjectName="TF-SB_ANS.SB_ANS_1T"/>
    <cge:TPSR_Ref TObjectID="21407"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -161.000000 -1001.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-110781" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -124.000000 -797.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110781" ObjectName="SB_ANS:SB_ANS_311BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-110782" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -123.000000 -757.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110782" ObjectName="SB_ANS:SB_ANS_311BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-110781" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -126.000000 -879.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110781" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-110781" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -124.000000 -840.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110781" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="-150" y="-1060"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="-150" y="-1060"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-198" y="-1077"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="57" y="-1071"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="57" y="-1071"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="59" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="59" y="-1029"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="30" qtmmishow="hidden" width="117" x="-236" y="-658"/>
    </a>
   <metadata/><rect fill="white" height="30" opacity="0" stroke="white" transform="" width="117" x="-236" y="-658"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="845" y="-282"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="845" y="-282"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1008" y="-282"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1008" y="-282"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1239" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1239" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1496" y="-278"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1496" y="-278"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1751" y="-280"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1751" y="-280"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1439" y="-863"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1439" y="-863"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="647" y="-860"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="647" y="-860"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-28" y="-972"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-28" y="-972"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="578" y="-315"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="578" y="-315"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="16" qtmmishow="hidden" width="56" x="965" y="-596"/>
    </a>
   <metadata/><rect fill="white" height="16" opacity="0" stroke="white" transform="" width="56" x="965" y="-596"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="418" y="-284"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="418" y="-284"/></g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="639,31 638,31 638,31 637,31 637,31 636,31 636,30 636,30 636,30 635,29 635,29 635,28 635,28 635,27 635,27 635,26 635,26 636,25 636,25 636,24 636,24 637,24 637,24 638,23 638,23 639,23 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="639,39 638,39 638,39 637,39 637,39 636,38 636,38 636,38 636,37 635,37 635,36 635,36 635,35 635,35 635,34 635,34 635,33 636,33 636,33 636,32 636,32 637,32 637,31 638,31 638,31 639,31 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="639,55 638,55 638,54 637,54 637,54 636,54 636,53 636,53 636,53 635,52 635,52 635,51 635,51 635,50 635,50 635,49 635,49 636,48 636,48 636,48 636,47 637,47 637,47 638,47 638,46 639,46 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="639,62 638,62 638,62 637,62 637,62 636,61 636,61 636,61 636,60 635,60 635,59 635,59 635,58 635,58 635,57 635,57 635,56 636,56 636,56 636,55 636,55 637,55 637,54 638,54 638,54 639,54 " stroke="rgb(50,205,50)" stroke-width="0.527575"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="569,85 568,85 568,85 567,85 566,85 566,84 565,84 564,83 564,83 564,82 563,81 563,81 563,80 563,79 563,78 563,78 564,77 564,76 564,76 565,75 566,75 566,74 567,74 568,74 568,74 569,74 " stroke="rgb(0,255,0)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="569,96 568,96 568,96 567,96 566,96 566,95 565,95 564,94 564,94 564,93 563,92 563,92 563,91 563,90 563,89 563,89 564,88 564,87 564,87 565,86 566,86 566,85 567,85 568,85 568,85 569,85 " stroke="rgb(0,255,0)" stroke-width="0.171589"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="SB_ANS" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_ainishan_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="638,-1127 638,-1168 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37763" ObjectName="AC-35kV.LN_ainishan_line"/>
    <cge:TPSR_Ref TObjectID="37763_SS-161"/></metadata>
   <polyline fill="none" opacity="0" points="638,-1127 638,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_ANS" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_aitianA_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1427,-1101 1427,-1142 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37762" ObjectName="AC-35kV.LN_aitianA_line"/>
    <cge:TPSR_Ref TObjectID="37762_SS-161"/></metadata>
   <polyline fill="none" opacity="0" points="1427,-1101 1427,-1142 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110794" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -521.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110794" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21384"/>
     <cge:Term_Ref ObjectID="29877"/>
    <cge:TPSR_Ref TObjectID="21384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110795" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -521.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110795" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21384"/>
     <cge:Term_Ref ObjectID="29877"/>
    <cge:TPSR_Ref TObjectID="21384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110787" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -521.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21384"/>
     <cge:Term_Ref ObjectID="29877"/>
    <cge:TPSR_Ref TObjectID="21384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110824" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 838.000000 60.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110824" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21390"/>
     <cge:Term_Ref ObjectID="29889"/>
    <cge:TPSR_Ref TObjectID="21390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110825" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 838.000000 60.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110825" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21390"/>
     <cge:Term_Ref ObjectID="29889"/>
    <cge:TPSR_Ref TObjectID="21390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110817" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 838.000000 60.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110817" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21390"/>
     <cge:Term_Ref ObjectID="29889"/>
    <cge:TPSR_Ref TObjectID="21390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110838" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 60.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110838" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21393"/>
     <cge:Term_Ref ObjectID="29895"/>
    <cge:TPSR_Ref TObjectID="21393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110839" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 60.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110839" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21393"/>
     <cge:Term_Ref ObjectID="29895"/>
    <cge:TPSR_Ref TObjectID="21393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110831" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 60.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110831" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21393"/>
     <cge:Term_Ref ObjectID="29895"/>
    <cge:TPSR_Ref TObjectID="21393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110866" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 58.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21399"/>
     <cge:Term_Ref ObjectID="29907"/>
    <cge:TPSR_Ref TObjectID="21399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110867" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 58.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110867" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21399"/>
     <cge:Term_Ref ObjectID="29907"/>
    <cge:TPSR_Ref TObjectID="21399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110859" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 58.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110859" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21399"/>
     <cge:Term_Ref ObjectID="29907"/>
    <cge:TPSR_Ref TObjectID="21399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1760.000000 60.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21402"/>
     <cge:Term_Ref ObjectID="29913"/>
    <cge:TPSR_Ref TObjectID="21402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1760.000000 60.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21402"/>
     <cge:Term_Ref ObjectID="29913"/>
    <cge:TPSR_Ref TObjectID="21402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1760.000000 60.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21402"/>
     <cge:Term_Ref ObjectID="29913"/>
    <cge:TPSR_Ref TObjectID="21402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-110798" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 294.000000 -881.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21375"/>
     <cge:Term_Ref ObjectID="29861"/>
    <cge:TPSR_Ref TObjectID="21375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-110799" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 294.000000 -881.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110799" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21375"/>
     <cge:Term_Ref ObjectID="29861"/>
    <cge:TPSR_Ref TObjectID="21375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-110800" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 294.000000 -881.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110800" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21375"/>
     <cge:Term_Ref ObjectID="29861"/>
    <cge:TPSR_Ref TObjectID="21375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-110807" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 294.000000 -881.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110807" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21375"/>
     <cge:Term_Ref ObjectID="29861"/>
    <cge:TPSR_Ref TObjectID="21375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-110804" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 294.000000 -881.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110804" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21375"/>
     <cge:Term_Ref ObjectID="29861"/>
    <cge:TPSR_Ref TObjectID="21375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-110801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 260.000000 -523.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21376"/>
     <cge:Term_Ref ObjectID="29862"/>
    <cge:TPSR_Ref TObjectID="21376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-110802" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 260.000000 -523.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21376"/>
     <cge:Term_Ref ObjectID="29862"/>
    <cge:TPSR_Ref TObjectID="21376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-110803" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 260.000000 -523.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110803" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21376"/>
     <cge:Term_Ref ObjectID="29862"/>
    <cge:TPSR_Ref TObjectID="21376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-110811" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 260.000000 -523.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110811" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21376"/>
     <cge:Term_Ref ObjectID="29862"/>
    <cge:TPSR_Ref TObjectID="21376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-110808" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 260.000000 -523.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110808" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21376"/>
     <cge:Term_Ref ObjectID="29862"/>
    <cge:TPSR_Ref TObjectID="21376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-110797" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1113.000000 -596.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110797" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21407"/>
     <cge:Term_Ref ObjectID="29923"/>
    <cge:TPSR_Ref TObjectID="21407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110767" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -882.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110767" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21382"/>
     <cge:Term_Ref ObjectID="29873"/>
    <cge:TPSR_Ref TObjectID="21382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110768" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -882.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110768" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21382"/>
     <cge:Term_Ref ObjectID="29873"/>
    <cge:TPSR_Ref TObjectID="21382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110760" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -882.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110760" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21382"/>
     <cge:Term_Ref ObjectID="29873"/>
    <cge:TPSR_Ref TObjectID="21382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 -690.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21383"/>
     <cge:Term_Ref ObjectID="29875"/>
    <cge:TPSR_Ref TObjectID="21383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 -690.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21383"/>
     <cge:Term_Ref ObjectID="29875"/>
    <cge:TPSR_Ref TObjectID="21383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 -690.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21383"/>
     <cge:Term_Ref ObjectID="29875"/>
    <cge:TPSR_Ref TObjectID="21383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110753" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -875.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21381"/>
     <cge:Term_Ref ObjectID="29871"/>
    <cge:TPSR_Ref TObjectID="21381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110754" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -875.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21381"/>
     <cge:Term_Ref ObjectID="29871"/>
    <cge:TPSR_Ref TObjectID="21381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110746" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -875.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110746" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21381"/>
     <cge:Term_Ref ObjectID="29871"/>
    <cge:TPSR_Ref TObjectID="21381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-231350" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 616.000000 167.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231350" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38560"/>
     <cge:Term_Ref ObjectID="57784"/>
    <cge:TPSR_Ref TObjectID="38560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-231351" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 616.000000 167.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231351" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38560"/>
     <cge:Term_Ref ObjectID="57784"/>
    <cge:TPSR_Ref TObjectID="38560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-231347" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 616.000000 167.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="231347" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38560"/>
     <cge:Term_Ref ObjectID="57784"/>
    <cge:TPSR_Ref TObjectID="38560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110852" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 60.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21396"/>
     <cge:Term_Ref ObjectID="29901"/>
    <cge:TPSR_Ref TObjectID="21396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110853" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 60.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110853" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21396"/>
     <cge:Term_Ref ObjectID="29901"/>
    <cge:TPSR_Ref TObjectID="21396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110845" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 60.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110845" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21396"/>
     <cge:Term_Ref ObjectID="29901"/>
    <cge:TPSR_Ref TObjectID="21396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 413.000000 156.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21405"/>
     <cge:Term_Ref ObjectID="29919"/>
    <cge:TPSR_Ref TObjectID="21405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 413.000000 156.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21405"/>
     <cge:Term_Ref ObjectID="29919"/>
    <cge:TPSR_Ref TObjectID="21405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 413.000000 156.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21405"/>
     <cge:Term_Ref ObjectID="29919"/>
    <cge:TPSR_Ref TObjectID="21405"/></metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-111023">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 914.000000 -479.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21384" ObjectName="SW-SB_ANS.SB_ANS_001BK"/>
     <cge:Meas_Ref ObjectId="111023"/>
    <cge:TPSR_Ref TObjectID="21384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111147">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.000000 -253.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21393" ObjectName="SW-SB_ANS.SB_ANS_042BK"/>
     <cge:Meas_Ref ObjectId="111147"/>
    <cge:TPSR_Ref TObjectID="21393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111201">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1478.000000 -248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21399" ObjectName="SW-SB_ANS.SB_ANS_044BK"/>
     <cge:Meas_Ref ObjectId="111201"/>
    <cge:TPSR_Ref TObjectID="21399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111228">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1733.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21402" ObjectName="SW-SB_ANS.SB_ANS_045BK"/>
     <cge:Meas_Ref ObjectId="111228"/>
    <cge:TPSR_Ref TObjectID="21402"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111174">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1221.000000 -251.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21396" ObjectName="SW-SB_ANS.SB_ANS_043BK"/>
     <cge:Meas_Ref ObjectId="111174"/>
    <cge:TPSR_Ref TObjectID="21396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110956">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1417.000000 -837.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21382" ObjectName="SW-SB_ANS.SB_ANS_392BK"/>
     <cge:Meas_Ref ObjectId="110956"/>
    <cge:TPSR_Ref TObjectID="21382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110998">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 914.000000 -645.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21383" ObjectName="SW-SB_ANS.SB_ANS_301BK"/>
     <cge:Meas_Ref ObjectId="110998"/>
    <cge:TPSR_Ref TObjectID="21383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111120">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 827.000000 -253.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21390" ObjectName="SW-SB_ANS.SB_ANS_041BK"/>
     <cge:Meas_Ref ObjectId="111120"/>
    <cge:TPSR_Ref TObjectID="21390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 560.000000 -286.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38560" ObjectName="SW-SB_ANS.SB_ANS_046BK"/>
     <cge:Meas_Ref ObjectId="231301"/>
    <cge:TPSR_Ref TObjectID="38560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110929">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 629.000000 -831.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21381" ObjectName="SW-SB_ANS.SB_ANS_391BK"/>
     <cge:Meas_Ref ObjectId="110929"/>
    <cge:TPSR_Ref TObjectID="21381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-111255">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 400.000000 -255.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21405" ObjectName="SW-SB_ANS.SB_ANS_047BK"/>
     <cge:Meas_Ref ObjectId="111255"/>
    <cge:TPSR_Ref TObjectID="21405"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="-150" y="-1060"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="57" y="-1071"/></g>
   <g href="cx_配调_配网接线图35_双柏.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="59" y="-1029"/></g>
   <g href="35kV爱尼山变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="30" qtmmishow="hidden" width="117" x="-236" y="-658"/></g>
   <g href="35kV爱尼山变10kV独田线041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="845" y="-282"/></g>
   <g href="35kV爱尼山变10kV旧哨线042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1008" y="-282"/></g>
   <g href="35kV爱尼山变10kV大麦地线043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1239" y="-280"/></g>
   <g href="35kV爱尼山变10kV六合线044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1496" y="-278"/></g>
   <g href="35kV爱尼山变10kV乡政府线045间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1751" y="-280"/></g>
   <g href="35kV爱尼山变35kV爱田线392间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1439" y="-863"/></g>
   <g href="35kV爱尼山变35kV爱尼山线391间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="647" y="-860"/></g>
   <g href="AVC爱尼山站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-28" y="-972"/></g>
   <g href="35kV爱尼山变10kV1号接地变及消弧线圈046间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="578" y="-315"/></g>
   <g href="35kV爱尼山变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="16" qtmmishow="hidden" width="56" x="965" y="-596"/></g>
   <g href="35kV爱尼山变10kV大箐线047间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="418" y="-284"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1ca2880">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1202.000000 -627.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c56520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 669.000000 -257.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bbcc00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 793.000000 -81.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c4e7a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 956.000000 -81.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1be8ec0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1444.000000 -77.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bb7d40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1711.000000 -66.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ba08b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1187.000000 -80.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c61970">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 541.000000 -1102.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c0b5d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 585.000000 -929.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c0c4e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 628.000000 -1000.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c0d100">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1416.000000 -978.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c0ef30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1347.000000 -1041.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b3eb60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1249.000000 -645.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bd41d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.648489 -126.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c2f9a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 0.626634 612.000000 20.000000)" xlink:href="#lightningRod:shape201"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c2c010">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 552.000000 -67.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c2cf10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 526.000000 -155.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c20000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 830.648489 -60.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bc6990">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 452.000000 -118.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bc8430">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 403.648489 -99.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="SB_ANS"/>
</svg>