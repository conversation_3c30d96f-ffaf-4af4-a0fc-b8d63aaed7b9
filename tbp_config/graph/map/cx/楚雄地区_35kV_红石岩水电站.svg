<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-141" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1200 1680 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape89">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="47" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="38" x2="20" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="20" x2="20" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="13" x2="13" y1="48" y2="31"/>
    <circle cx="34" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="24" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="4" x2="13" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape48">
    <polyline points="27,17 52,17 52,9 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="49" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="51" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="58" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="49" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="51" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="58" y1="9" y2="9"/>
    <polyline points="27,37 52,37 52,29 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="27" y1="40" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="27" y1="40" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="13" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="32" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="13" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="27" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="27" y1="21" y2="18"/>
    <circle cx="27" cy="19" r="11" stroke-width="1"/>
    <ellipse cx="12" cy="28" rx="11.5" ry="11" stroke-width="1"/>
    <circle cx="27" cy="36" r="11" stroke-width="1"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape6_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="38" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="38" y2="13"/>
   </symbol>
   <symbol id="switch2:shape6_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="13" y2="14"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="11" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape43_0">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
   </symbol>
   <symbol id="transformer2:shape43_1">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape46_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="61" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="31" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 40,62 40,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="5"/>
    <circle cx="16" cy="62" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape46_1">
    <circle cx="16" cy="84" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,93 10,81 22,81 16,93 16,92 16,93 "/>
   </symbol>
   <symbol id="voltageTransformer:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="48" y1="4" y2="4"/>
    <circle cx="41" cy="55" fillStyle="0" r="25" stroke-width="0.520408"/>
    <circle cx="57" cy="26" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="50" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="48" x2="40" y1="66" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="32" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="22" x2="22" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="30" x2="22" y1="29" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="22" x2="14" y1="22" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="57" x2="57" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="73" x2="57" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="73" x2="57" y1="24" y2="33"/>
    <circle cx="25" cy="25" fillStyle="0" r="25" stroke-width="0.520408"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_25cae90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25cbf30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_25cc920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_25cd5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_25ce7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_25cf490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25d0030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_25d0b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1cd17f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1cd17f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25d4400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25d4400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25d6160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25d6160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_25d7180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25d8d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_25d9970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_25da730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_25db070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25dc730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25dd330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25ddbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_25de390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25df470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25dfdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25e08e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_25e12a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_25e26f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_25e3210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_25e41f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_25e4eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_25f36b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25e64b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_25e76e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_25e8cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1690" x="3112" y="-1205"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(127,127,127)" stroke-width="0.416667" width="14" x="3599" y="-143"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(127,127,127)" stroke-width="0.416667" width="14" x="4222" y="-783"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(127,127,127)" stroke-width="0.416667" width="14" x="4464" y="-785"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(127,127,127)" stroke-width="0.416667" width="14" x="3823" y="-143"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(127,127,127)" stroke-width="0.416667" width="14" x="3789" y="-359"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(127,127,127)" stroke-width="0.416667" width="14" x="4019" y="-144"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(127,127,127)" stroke-width="0.416667" width="14" x="4243" y="-144"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(127,127,127)" stroke-width="0.416667" width="14" x="4425" y="-143"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-106513">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3735.000000 -954.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20749" ObjectName="SW-CX_HSY.CX_HSY_3011SW"/>
     <cge:Meas_Ref ObjectId="106513"/>
    <cge:TPSR_Ref TObjectID="20749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106515">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 -949.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20751" ObjectName="SW-CX_HSY.CX_HSY_3901SW"/>
     <cge:Meas_Ref ObjectId="106515"/>
    <cge:TPSR_Ref TObjectID="20751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106607">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3734.000000 -567.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20800" ObjectName="SW-CX_HSY.CX_HSY_6011SW"/>
     <cge:Meas_Ref ObjectId="106607"/>
    <cge:TPSR_Ref TObjectID="20800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106516">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 -1008.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20752" ObjectName="SW-CX_HSY.CX_HSY_39010SW"/>
     <cge:Meas_Ref ObjectId="106516"/>
    <cge:TPSR_Ref TObjectID="20752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106517">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 -928.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20753" ObjectName="SW-CX_HSY.CX_HSY_39017SW"/>
     <cge:Meas_Ref ObjectId="106517"/>
    <cge:TPSR_Ref TObjectID="20753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106584">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3526.000000 -382.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20777" ObjectName="SW-CX_HSY.CX_HSY_6111SW"/>
     <cge:Meas_Ref ObjectId="106584"/>
    <cge:TPSR_Ref TObjectID="20777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106586">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3597.000000 -164.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20779" ObjectName="SW-CX_HSY.CX_HSY_6101SW"/>
     <cge:Meas_Ref ObjectId="106586"/>
    <cge:TPSR_Ref TObjectID="20779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106514">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3671.000000 -929.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20750" ObjectName="SW-CX_HSY.CX_HSY_30117SW"/>
     <cge:Meas_Ref ObjectId="106514"/>
    <cge:TPSR_Ref TObjectID="20750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106525">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 -956.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20761" ObjectName="SW-CX_HSY.CX_HSY_3611SW"/>
     <cge:Meas_Ref ObjectId="106525"/>
    <cge:TPSR_Ref TObjectID="20761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106527">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 -930.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20763" ObjectName="SW-CX_HSY.CX_HSY_36117SW"/>
     <cge:Meas_Ref ObjectId="106527"/>
    <cge:TPSR_Ref TObjectID="20763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106524">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 -816.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20760" ObjectName="SW-CX_HSY.CX_HSY_3616SW"/>
     <cge:Meas_Ref ObjectId="106524"/>
    <cge:TPSR_Ref TObjectID="20760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106526">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 -790.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20762" ObjectName="SW-CX_HSY.CX_HSY_36167SW"/>
     <cge:Meas_Ref ObjectId="106526"/>
    <cge:TPSR_Ref TObjectID="20762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106522">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4170.000000 -792.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20758" ObjectName="SW-CX_HSY.CX_HSY_3619SW"/>
     <cge:Meas_Ref ObjectId="106522"/>
    <cge:TPSR_Ref TObjectID="20758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106523">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4220.000000 -805.000000)" xlink:href="#switch2:shape6_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20759" ObjectName="SW-CX_HSY.CX_HSY_36197SW"/>
     <cge:Meas_Ref ObjectId="106523"/>
    <cge:TPSR_Ref TObjectID="20759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106528">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4392.000000 -958.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20764" ObjectName="SW-CX_HSY.CX_HSY_3621SW"/>
     <cge:Meas_Ref ObjectId="106528"/>
    <cge:TPSR_Ref TObjectID="20764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106531">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4329.000000 -932.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20767" ObjectName="SW-CX_HSY.CX_HSY_36217SW"/>
     <cge:Meas_Ref ObjectId="106531"/>
    <cge:TPSR_Ref TObjectID="20767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106529">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4392.000000 -818.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20765" ObjectName="SW-CX_HSY.CX_HSY_3626SW"/>
     <cge:Meas_Ref ObjectId="106529"/>
    <cge:TPSR_Ref TObjectID="20765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106530">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -792.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20766" ObjectName="SW-CX_HSY.CX_HSY_36267SW"/>
     <cge:Meas_Ref ObjectId="106530"/>
    <cge:TPSR_Ref TObjectID="20766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106520">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 -794.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20756" ObjectName="SW-CX_HSY.CX_HSY_3629SW"/>
     <cge:Meas_Ref ObjectId="106520"/>
    <cge:TPSR_Ref TObjectID="20756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106521">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4462.000000 -807.000000)" xlink:href="#switch2:shape6_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20757" ObjectName="SW-CX_HSY.CX_HSY_36297SW"/>
     <cge:Meas_Ref ObjectId="106521"/>
    <cge:TPSR_Ref TObjectID="20757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106590">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 -164.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20783" ObjectName="SW-CX_HSY.CX_HSY_6301SW"/>
     <cge:Meas_Ref ObjectId="106590"/>
    <cge:TPSR_Ref TObjectID="20783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106587">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3634.000000 -70.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20780" ObjectName="SW-CX_HSY.CX_HSY_61017SW"/>
     <cge:Meas_Ref ObjectId="106587"/>
    <cge:TPSR_Ref TObjectID="20780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106591">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3773.000000 -68.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20784" ObjectName="SW-CX_HSY.CX_HSY_63017SW"/>
     <cge:Meas_Ref ObjectId="106591"/>
    <cge:TPSR_Ref TObjectID="20784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106588">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.000000 -166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20781" ObjectName="SW-CX_HSY.CX_HSY_6201SW"/>
     <cge:Meas_Ref ObjectId="106588"/>
    <cge:TPSR_Ref TObjectID="20781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106589">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.000000 -70.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20782" ObjectName="SW-CX_HSY.CX_HSY_62017SW"/>
     <cge:Meas_Ref ObjectId="106589"/>
    <cge:TPSR_Ref TObjectID="20782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106610">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3787.000000 -381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20803" ObjectName="SW-CX_HSY.CX_HSY_6901SW"/>
     <cge:Meas_Ref ObjectId="106610"/>
    <cge:TPSR_Ref TObjectID="20803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106611">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -304.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20804" ObjectName="SW-CX_HSY.CX_HSY_69017SW"/>
     <cge:Meas_Ref ObjectId="106611"/>
    <cge:TPSR_Ref TObjectID="20804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106585">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3584.000000 -291.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20778" ObjectName="SW-CX_HSY.CX_HSY_61117SW"/>
     <cge:Meas_Ref ObjectId="106585"/>
    <cge:TPSR_Ref TObjectID="20778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106593">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -383.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20786" ObjectName="SW-CX_HSY.CX_HSY_6121SW"/>
     <cge:Meas_Ref ObjectId="106593"/>
    <cge:TPSR_Ref TObjectID="20786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106594">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4004.000000 -292.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20787" ObjectName="SW-CX_HSY.CX_HSY_61217SW"/>
     <cge:Meas_Ref ObjectId="106594"/>
    <cge:TPSR_Ref TObjectID="20787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106595">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 -167.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20788" ObjectName="SW-CX_HSY.CX_HSY_6102SW"/>
     <cge:Meas_Ref ObjectId="106595"/>
    <cge:TPSR_Ref TObjectID="20788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106599">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -167.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20792" ObjectName="SW-CX_HSY.CX_HSY_6302SW"/>
     <cge:Meas_Ref ObjectId="106599"/>
    <cge:TPSR_Ref TObjectID="20792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106596">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 -71.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20789" ObjectName="SW-CX_HSY.CX_HSY_61027SW"/>
     <cge:Meas_Ref ObjectId="106596"/>
    <cge:TPSR_Ref TObjectID="20789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106600">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 -73.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20793" ObjectName="SW-CX_HSY.CX_HSY_63027SW"/>
     <cge:Meas_Ref ObjectId="106600"/>
    <cge:TPSR_Ref TObjectID="20793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106597">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4241.000000 -166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20790" ObjectName="SW-CX_HSY.CX_HSY_6202SW"/>
     <cge:Meas_Ref ObjectId="106597"/>
    <cge:TPSR_Ref TObjectID="20790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106598">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4278.000000 -71.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20791" ObjectName="SW-CX_HSY.CX_HSY_62027SW"/>
     <cge:Meas_Ref ObjectId="106598"/>
    <cge:TPSR_Ref TObjectID="20791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106605">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 -381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20798" ObjectName="SW-CX_HSY.CX_HSY_6621SW"/>
     <cge:Meas_Ref ObjectId="106605"/>
    <cge:TPSR_Ref TObjectID="20798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106606">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.000000 -290.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20799" ObjectName="SW-CX_HSY.CX_HSY_66217SW"/>
     <cge:Meas_Ref ObjectId="106606"/>
    <cge:TPSR_Ref TObjectID="20799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106608">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -460.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20801" ObjectName="SW-CX_HSY.CX_HSY_60117SW"/>
     <cge:Meas_Ref ObjectId="106608"/>
    <cge:TPSR_Ref TObjectID="20801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106602">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 -382.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20795" ObjectName="SW-CX_HSY.CX_HSY_6611SW"/>
     <cge:Meas_Ref ObjectId="106602"/>
    <cge:TPSR_Ref TObjectID="20795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106603">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -291.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20796" ObjectName="SW-CX_HSY.CX_HSY_66117SW"/>
     <cge:Meas_Ref ObjectId="106603"/>
    <cge:TPSR_Ref TObjectID="20796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4712.000000 -966.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_HSY.CX_HSY_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3469,-440 4671,-440 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20775" ObjectName="BS-CX_HSY.CX_HSY_6IM"/>
    <cge:TPSR_Ref TObjectID="20775"/></metadata>
   <polyline fill="none" opacity="0" points="3469,-440 4671,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HSY.CX_HSY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3495,-1055 4797,-1055 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="20744" ObjectName="BS-CX_HSY.CX_HSY_3IM"/>
    <cge:TPSR_Ref TObjectID="20744"/></metadata>
   <polyline fill="none" opacity="0" points="3495,-1055 4797,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HSY.CX_HSY_cz1">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4510,-66 4579,-66 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46575" ObjectName="BS-CX_HSY.CX_HSY_cz1"/>
    <cge:TPSR_Ref TObjectID="46575"/></metadata>
   <polyline fill="none" opacity="0" points="4510,-66 4579,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HSY.CX_HSY_cz2">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-65 4733,-65 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46576" ObjectName="BS-CX_HSY.CX_HSY_cz2"/>
    <cge:TPSR_Ref TObjectID="46576"/></metadata>
   <polyline fill="none" opacity="0" points="4672,-65 4733,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HSY.CX_HSY_cz2">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4681,-67 4681,-67 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="46576" ObjectName="BS-CX_HSY.CX_HSY_cz2"/>
    <cge:TPSR_Ref TObjectID="46576"/></metadata>
   <polyline fill="none" opacity="0" points="4681,-67 4681,-67 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_HSY.662Load">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 -246.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46572" ObjectName="EC-CX_HSY.662Load"/>
    <cge:TPSR_Ref TObjectID="46572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HSY.661Load">
    <use class="BKBV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4728.000000 -171.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46571" ObjectName="EC-CX_HSY.661Load"/>
    <cge:TPSR_Ref TObjectID="46571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HSY.362Load">
    <use class="BKBV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4395.000000 -601.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46573" ObjectName="EC-CX_HSY.362Load"/>
    <cge:TPSR_Ref TObjectID="46573"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1caa400" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4009.000000 -908.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1cbdcc0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3697.000000 -642.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c88900">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3927.000000 -836.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c892d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -874.000000)" xlink:href="#lightningRod:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c12fa0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -53.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bdca80">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 -297.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ba1240">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3769.000000 -270.000000)" xlink:href="#lightningRod:shape48"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c67d60">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3495.000000 -231.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bc62c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 -54.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ca0380">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3915.000000 -232.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b4ddd0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -230.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1be00d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4354.000000 -83.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c56b90">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4506.000000 -231.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116500" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3245.538462 -1014.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116500" ObjectName="CX_HSY:CX_HSY_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116501" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3243.538462 -974.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116501" ObjectName="CX_HSY:CX_HSY_ZJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-106494" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3563.000000 -1126.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20744"/>
     <cge:Term_Ref ObjectID="28805"/>
    <cge:TPSR_Ref TObjectID="20744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-106495" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3563.000000 -1126.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20744"/>
     <cge:Term_Ref ObjectID="28805"/>
    <cge:TPSR_Ref TObjectID="20744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-106496" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3563.000000 -1126.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20744"/>
     <cge:Term_Ref ObjectID="28805"/>
    <cge:TPSR_Ref TObjectID="20744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-106497" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3563.000000 -1126.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106497" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20744"/>
     <cge:Term_Ref ObjectID="28805"/>
    <cge:TPSR_Ref TObjectID="20744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-106491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -909.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20748"/>
     <cge:Term_Ref ObjectID="28812"/>
    <cge:TPSR_Ref TObjectID="20748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-106492" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -909.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106492" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20748"/>
     <cge:Term_Ref ObjectID="28812"/>
    <cge:TPSR_Ref TObjectID="20748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-106488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -909.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20748"/>
     <cge:Term_Ref ObjectID="28812"/>
    <cge:TPSR_Ref TObjectID="20748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-106551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3491.000000 -63.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20776"/>
     <cge:Term_Ref ObjectID="28859"/>
    <cge:TPSR_Ref TObjectID="20776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-106552" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3491.000000 -63.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20776"/>
     <cge:Term_Ref ObjectID="28859"/>
    <cge:TPSR_Ref TObjectID="20776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-106548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3491.000000 -63.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20776"/>
     <cge:Term_Ref ObjectID="28859"/>
    <cge:TPSR_Ref TObjectID="20776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-106564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -62.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20785"/>
     <cge:Term_Ref ObjectID="28877"/>
    <cge:TPSR_Ref TObjectID="20785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-106565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -62.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20785"/>
     <cge:Term_Ref ObjectID="28877"/>
    <cge:TPSR_Ref TObjectID="20785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-106561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3948.000000 -62.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20785"/>
     <cge:Term_Ref ObjectID="28877"/>
    <cge:TPSR_Ref TObjectID="20785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-106502" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -930.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106502" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20754"/>
     <cge:Term_Ref ObjectID="28824"/>
    <cge:TPSR_Ref TObjectID="20754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-106503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -930.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20754"/>
     <cge:Term_Ref ObjectID="28824"/>
    <cge:TPSR_Ref TObjectID="20754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-106499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -930.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20754"/>
     <cge:Term_Ref ObjectID="28824"/>
    <cge:TPSR_Ref TObjectID="20754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-106568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -511.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20775"/>
     <cge:Term_Ref ObjectID="28858"/>
    <cge:TPSR_Ref TObjectID="20775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-106569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -511.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20775"/>
     <cge:Term_Ref ObjectID="28858"/>
    <cge:TPSR_Ref TObjectID="20775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-106570" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -511.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20775"/>
     <cge:Term_Ref ObjectID="28858"/>
    <cge:TPSR_Ref TObjectID="20775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-106571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -511.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="106571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="20775"/>
     <cge:Term_Ref ObjectID="28858"/>
    <cge:TPSR_Ref TObjectID="20775"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="142" x="3246" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="142" x="3246" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="142" x="3246" y="-1177"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-106512">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3735.000000 -875.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20748" ObjectName="SW-CX_HSY.CX_HSY_301BK"/>
     <cge:Meas_Ref ObjectId="106512"/>
    <cge:TPSR_Ref TObjectID="20748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106609">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3734.000000 -650.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20802" ObjectName="SW-CX_HSY.CX_HSY_601BK"/>
     <cge:Meas_Ref ObjectId="106609"/>
    <cge:TPSR_Ref TObjectID="20802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106583">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3526.000000 -314.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20776" ObjectName="SW-CX_HSY.CX_HSY_611BK"/>
     <cge:Meas_Ref ObjectId="106583"/>
    <cge:TPSR_Ref TObjectID="20776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106518">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 -876.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20754" ObjectName="SW-CX_HSY.CX_HSY_361BK"/>
     <cge:Meas_Ref ObjectId="106518"/>
    <cge:TPSR_Ref TObjectID="20754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106519">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4392.000000 -878.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20755" ObjectName="SW-CX_HSY.CX_HSY_362BK"/>
     <cge:Meas_Ref ObjectId="106519"/>
    <cge:TPSR_Ref TObjectID="20755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106592">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20785" ObjectName="SW-CX_HSY.CX_HSY_612BK"/>
     <cge:Meas_Ref ObjectId="106592"/>
    <cge:TPSR_Ref TObjectID="20785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106604">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20797" ObjectName="SW-CX_HSY.CX_HSY_662BK"/>
     <cge:Meas_Ref ObjectId="106604"/>
    <cge:TPSR_Ref TObjectID="20797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106601">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 -314.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20794" ObjectName="SW-CX_HSY.CX_HSY_661BK"/>
     <cge:Meas_Ref ObjectId="106601"/>
    <cge:TPSR_Ref TObjectID="20794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106511">
    <use class="BV-4KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4608.000000 -87.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20747" ObjectName="SW-CX_HSY.CX_HSY_403BK"/>
     <cge:Meas_Ref ObjectId="106511"/>
    <cge:TPSR_Ref TObjectID="20747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-106510">
    <use class="BV-4KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4710.000000 -91.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20746" ObjectName="SW-CX_HSY.CX_HSY_402BK"/>
     <cge:Meas_Ref ObjectId="106510"/>
    <cge:TPSR_Ref TObjectID="20746"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_HSY" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_wantamahongThs" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4160,-618 4160,-729 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37858" ObjectName="AC-35kV.LN_wantamahongThs"/>
    <cge:TPSR_Ref TObjectID="37858_SS-141"/></metadata>
   <polyline fill="none" opacity="0" points="4160,-618 4160,-729 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_HSY.P1">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3511.000000 -109.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43415" ObjectName="SM-CX_HSY.P1"/>
    <cge:TPSR_Ref TObjectID="43415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HSY.P2">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.000000 -110.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43416" ObjectName="SM-CX_HSY.P2"/>
    <cge:TPSR_Ref TObjectID="43416"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="20775" cx="3743" cy="-439" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20744" cx="3934" cy="-1055" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20744" cx="4717" cy="-1055" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20744" cx="3744" cy="-1055" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20775" cx="3535" cy="-440" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20744" cx="4160" cy="-1055" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20744" cx="4402" cy="-1055" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20775" cx="3955" cy="-440" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20775" cx="4395" cy="-440" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20775" cx="4546" cy="-440" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="20775" cx="3796" cy="-440" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46575" cx="4546" cy="-66" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46576" cx="4694" cy="-65" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46576" cx="4719" cy="-65" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="46575" cx="4567" cy="-66" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca4c30" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3473.000000 -465.000000) translate(0,12)">6.3kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1832ca0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3498.000000 -1047.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2db80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3500.000000 -96.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1989fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca3600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca3600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca3600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca3600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca3600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca3600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ca3600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1977810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3272.000000 -1166.500000) translate(0,16)">红石岩电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1664780" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3898.000000 -830.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c2d4e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3768.000000 -261.000000) translate(0,12)">6kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce37b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3731.000000 -126.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1beabd0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4125.000000 -605.000000) translate(0,12)">35kV万他马红线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca9a40" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4362.000000 -594.000000) translate(0,12)">35kV逸红线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd7060" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3920.000000 -97.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c3fd80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4151.000000 -127.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c0fba0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4410.000000 -254.000000) translate(0,12)">43B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be83c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4533.000000 -56.000000) translate(0,12)">厂用1段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1beeba0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4682.000000 -53.000000) translate(0,12)">厂用2段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf10c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4375.000000 -53.000000) translate(0,12)">近区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf1bb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4736.000000 -940.000000) translate(0,12)">42B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6bcf0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3259.000000 -224.000000) translate(0,12)">6861166</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -904.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6ca50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3751.000000 -984.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6ccd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -962.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6d010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3752.000000 -679.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6d470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -597.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6d6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3811.000000 -495.000000) translate(0,12)">60117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6d8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.000000 -979.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6db30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3870.000000 -1041.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6dd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -961.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6dfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -905.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6e1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4095.000000 -963.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6e430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4166.000000 -986.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6e670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4166.000000 -846.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6e8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4094.000000 -823.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6eaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -823.000000) translate(0,12)">3619</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6ed30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4238.000000 -840.000000) translate(0,12)">36197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6ef70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -907.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6f1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4408.000000 -988.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6f3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -965.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6f630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4408.000000 -848.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6f870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4335.000000 -825.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6fab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4418.000000 -825.000000) translate(0,12)">3629</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6fcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4480.000000 -842.000000) translate(0,12)">36297</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6ff30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3544.000000 -343.000000) translate(0,12)">611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c70170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3542.000000 -412.000000) translate(0,12)">6111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c703b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3602.000000 -326.000000) translate(0,12)">61117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3803.000000 -411.000000) translate(0,12)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c70830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -339.000000) translate(0,12)">69017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c70a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3964.000000 -344.000000) translate(0,12)">612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c70cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3962.000000 -413.000000) translate(0,12)">6121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c70ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -327.000000) translate(0,12)">61217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c71130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -197.000000) translate(0,12)">6102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c71370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4141.000000 -197.000000) translate(0,12)">6302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c715b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4211.000000 -108.000000) translate(0,12)">63027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bce860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4257.000000 -196.000000) translate(0,12)">6202</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bceaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4296.000000 -106.000000) translate(0,12)">62027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcece0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4072.000000 -106.000000) translate(0,12)">61027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcef20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3613.000000 -194.000000) translate(0,12)">6101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcf160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3652.000000 -105.000000) translate(0,12)">61017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcf3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3721.000000 -194.000000) translate(0,12)">6301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcf5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -103.000000) translate(0,12)">63017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcf820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3837.000000 -196.000000) translate(0,12)">6201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcfa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3876.000000 -105.000000) translate(0,12)">62017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcfca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4404.000000 -342.000000) translate(0,12)">662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bcfee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -411.000000) translate(0,12)">6621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd0120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4462.000000 -325.000000) translate(0,12)">66217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd0360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4555.000000 -343.000000) translate(0,12)">661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd05a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4553.000000 -412.000000) translate(0,12)">6611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd07e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.000000 -326.000000) translate(0,12)">66117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd0a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4555.000000 -141.000000) translate(0,12)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd0c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4618.000000 -121.000000) translate(0,12)">403</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd0ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4728.000000 -120.000000) translate(0,12)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b68bb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3450.000000 -757.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b68bb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3450.000000 -757.000000) translate(0,27)">SFS9-10000-38.5/6.3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b68bb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3450.000000 -757.000000) translate(0,42)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b6b110" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4094.000000 -341.000000) translate(0,12)">1,2号发电机参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b6b110" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4094.000000 -341.000000) translate(0,27)">SFW4000-8/1730</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b6b110" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4094.000000 -341.000000) translate(0,42)">Pe=4000MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b6b110" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4094.000000 -341.000000) translate(0,57)">Ue=6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b6b110" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4094.000000 -341.000000) translate(0,72)">Ie=485A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b6b110" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4094.000000 -341.000000) translate(0,87)">cos=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b6cad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3793.000000 -805.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_249abb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4510.000000 -86.000000) translate(0,12)">厂站I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b78770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4577.000000 -223.000000) translate(0,12)">35kV41b</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_250ced0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4601.000000 -46.000000) translate(0,12)">厂站2段</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd11d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3506.000000 1097.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd2090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3506.000000 1113.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd25e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3495.000000 1080.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd2b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3506.000000 1129.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd2eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3585.000000 910.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd3a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3574.000000 895.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bd42b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3599.000000 880.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b637d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3426.000000 64.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b639c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3415.000000 49.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b63bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3440.000000 34.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b64b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 930.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b64d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 915.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b64f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4224.000000 900.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b652c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3558.000000 484.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b65530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3558.000000 500.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b65770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3547.000000 467.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b659b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3558.000000 516.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_HSY.CX_HSY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28856"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3719.000000 -749.000000)" xlink:href="#transformer2:shape43_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3719.000000 -749.000000)" xlink:href="#transformer2:shape43_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="20769" ObjectName="TF-CX_HSY.CX_HSY_1T"/>
    <cge:TPSR_Ref TObjectID="20769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3701.000000 -99.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3701.000000 -99.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 -100.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 -100.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4383.000000 -188.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4383.000000 -188.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4701.000000 -856.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4701.000000 -856.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.811111 4525.000000 -184.000000)" xlink:href="#transformer2:shape43_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.811111 4525.000000 -184.000000)" xlink:href="#transformer2:shape43_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-88593" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3388.000000 -1091.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19101" ObjectName="DYN-CX_HSY"/>
     <cge:Meas_Ref ObjectId="88593"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c0aa00">
    <use class="BV-35KV" transform="matrix(0.378049 -0.000000 0.000000 -0.468864 4214.000000 -704.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ca6e40">
    <use class="BV-35KV" transform="matrix(0.378049 -0.000000 0.000000 -0.468864 4456.000000 -706.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c39690">
    <use class="BV-6KV" transform="matrix(0.378049 -0.000000 0.000000 -0.421978 3591.000000 -68.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c3ad70">
    <use class="BV-6KV" transform="matrix(0.378049 -0.000000 0.000000 -0.421978 3815.000000 -68.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c9ec10">
    <use class="BV-6KV" transform="matrix(0.378049 -0.000000 0.000000 -0.421978 4011.000000 -69.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b97980">
    <use class="BV-6KV" transform="matrix(0.378049 -0.000000 0.000000 -0.421978 4235.000000 -69.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c11850">
    <use class="BV-0KV" transform="matrix(0.378049 -0.000000 0.000000 -0.421978 4417.000000 -68.000000)" xlink:href="#voltageTransformer:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1c9c750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-995 3744,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20749@1" ObjectIDZND0="20744@0" Pin0InfoVect0LinkObjId="g_1cff4c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106513_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-995 3744,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bab830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-883 3744,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="20748@0" ObjectIDZND0="20769@0" Pin0InfoVect0LinkObjId="g_1cbe580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-883 3744,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1cbdad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-658 3743,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20802@0" ObjectIDZND0="20800@1" Pin0InfoVect0LinkObjId="SW-106607_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106609_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-658 3743,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1cbe580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3704,-700 3704,-721 3743,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="g_1cbdcc0@0" ObjectIDZND0="20769@x" ObjectIDZND1="20802@x" Pin0InfoVect0LinkObjId="g_1bab830_0" Pin0InfoVect1LinkObjId="SW-106609_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1cbdcc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3704,-700 3704,-721 3743,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1cbe770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-685 3743,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="20802@1" ObjectIDZND0="20769@x" ObjectIDZND1="g_1cbdcc0@0" Pin0InfoVect0LinkObjId="g_1bab830_0" Pin0InfoVect1LinkObjId="g_1cbdcc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106609_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-685 3743,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c51ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-721 3743,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="20802@x" ObjectIDND1="g_1cbdcc0@0" ObjectIDZND0="20769@1" Pin0InfoVect0LinkObjId="g_1bab830_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106609_0" Pin1InfoVect1LinkObjId="g_1cbdcc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-721 3743,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cff4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3913,-1015 3934,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="20752@0" ObjectIDZND0="20744@0" ObjectIDZND1="20751@x" Pin0InfoVect0LinkObjId="g_1c9c750_0" Pin0InfoVect1LinkObjId="SW-106515_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106516_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3913,-1015 3934,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cff6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3934,-990 3934,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="20751@1" ObjectIDZND0="20752@x" ObjectIDZND1="20744@0" Pin0InfoVect0LinkObjId="SW-106516_0" Pin0InfoVect1LinkObjId="g_1c9c750_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106515_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3934,-990 3934,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cff8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3934,-1015 3934,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="20752@x" ObjectIDND1="20751@x" ObjectIDZND0="20744@0" Pin0InfoVect0LinkObjId="g_1c9c750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106516_0" Pin1InfoVect1LinkObjId="SW-106515_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3934,-1015 3934,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c88520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-935 3933,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="20753@0" ObjectIDZND0="g_1c892d0@0" ObjectIDZND1="g_1c88900@0" ObjectIDZND2="20751@x" Pin0InfoVect0LinkObjId="g_1c892d0_0" Pin0InfoVect1LinkObjId="g_1c88900_0" Pin0InfoVect2LinkObjId="SW-106515_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106517_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-935 3933,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c88710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3934,-935 3934,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="20753@x" ObjectIDND1="g_1c892d0@0" ObjectIDND2="g_1c88900@0" ObjectIDZND0="20751@0" Pin0InfoVect0LinkObjId="SW-106515_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106517_0" Pin1InfoVect1LinkObjId="g_1c892d0_0" Pin1InfoVect2LinkObjId="g_1c88900_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3934,-935 3934,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca9c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-914 3934,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1c892d0@0" ObjectIDZND0="20753@x" ObjectIDZND1="20751@x" ObjectIDZND2="g_1c88900@0" Pin0InfoVect0LinkObjId="SW-106517_0" Pin0InfoVect1LinkObjId="SW-106515_0" Pin0InfoVect2LinkObjId="g_1c88900_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c892d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-914 3934,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca9e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3934,-935 3934,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="20753@x" ObjectIDND1="20751@x" ObjectIDZND0="g_1c892d0@0" ObjectIDZND1="g_1c88900@0" Pin0InfoVect0LinkObjId="g_1c892d0_0" Pin0InfoVect1LinkObjId="g_1c88900_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106517_0" Pin1InfoVect1LinkObjId="SW-106515_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3934,-935 3934,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1caa020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3934,-914 3934,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1c892d0@0" ObjectIDND1="20753@x" ObjectIDND2="20751@x" ObjectIDZND0="g_1c88900@0" Pin0InfoVect0LinkObjId="g_1c88900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c892d0_0" Pin1InfoVect1LinkObjId="SW-106517_0" Pin1InfoVect2LinkObjId="SW-106515_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3934,-914 3934,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1caa210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-914 4014,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_1c892d0@1" ObjectIDZND0="g_1caa400@0" Pin0InfoVect0LinkObjId="g_1caa400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c892d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-914 4014,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bce080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3535,-423 3535,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20777@1" ObjectIDZND0="20775@0" Pin0InfoVect0LinkObjId="g_1c39430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106584_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3535,-423 3535,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1b989c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3535,-221 3535,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="g_1c67d60@0" ObjectIDND1="20776@x" ObjectIDND2="20783@x" ObjectIDZND0="43415@0" Pin0InfoVect0LinkObjId="SM-CX_HSY.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c67d60_0" Pin1InfoVect1LinkObjId="SW-106583_0" Pin1InfoVect2LinkObjId="SW-106590_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3535,-221 3535,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1b995c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3535,-305 3535,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1c67d60@0" ObjectIDND1="20776@x" ObjectIDZND0="43415@x" ObjectIDZND1="20783@x" ObjectIDZND2="20781@x" Pin0InfoVect0LinkObjId="SM-CX_HSY.P1_0" Pin0InfoVect1LinkObjId="SW-106590_0" Pin0InfoVect2LinkObjId="SW-106588_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c67d60_0" Pin1InfoVect1LinkObjId="SW-106583_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3535,-305 3535,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ce56f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3722,-936 3743,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20750@0" ObjectIDZND0="20748@x" ObjectIDZND1="20749@x" Pin0InfoVect0LinkObjId="SW-106512_0" Pin0InfoVect1LinkObjId="SW-106513_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106514_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3722,-936 3743,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ce58e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-910 3744,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20748@1" ObjectIDZND0="20750@x" ObjectIDZND1="20749@x" Pin0InfoVect0LinkObjId="SW-106514_0" Pin0InfoVect1LinkObjId="SW-106513_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106512_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-910 3744,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ce5ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-936 3744,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20748@x" ObjectIDND1="20750@x" ObjectIDZND0="20749@0" Pin0InfoVect0LinkObjId="SW-106513_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106512_0" Pin1InfoVect1LinkObjId="SW-106514_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-936 3744,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c7f010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-996 4160,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20761@1" ObjectIDZND0="20744@0" Pin0InfoVect0LinkObjId="g_1c9c750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106525_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-996 4160,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1babb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4138,-937 4159,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20763@0" ObjectIDZND0="20754@x" ObjectIDZND1="20761@x" Pin0InfoVect0LinkObjId="SW-106518_0" Pin0InfoVect1LinkObjId="SW-106525_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106527_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4138,-937 4159,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1babd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-937 4159,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20763@x" ObjectIDND1="20754@x" ObjectIDZND0="20761@0" Pin0InfoVect0LinkObjId="SW-106525_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106527_0" Pin1InfoVect1LinkObjId="SW-106518_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-937 4159,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1babf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-911 4159,-937 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20754@1" ObjectIDZND0="20763@x" ObjectIDZND1="20761@x" Pin0InfoVect0LinkObjId="SW-106527_0" Pin0InfoVect1LinkObjId="SW-106525_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106518_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-911 4159,-937 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1badda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-857 4159,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20760@1" ObjectIDZND0="20754@0" Pin0InfoVect0LinkObjId="SW-106518_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106524_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-857 4159,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c641b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-797 4158,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="20762@0" ObjectIDZND0="20758@x" ObjectIDZND1="20760@x" ObjectIDZND2="37858@1" Pin0InfoVect0LinkObjId="SW-106522_0" Pin0InfoVect1LinkObjId="SW-106524_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-797 4158,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c643a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-729 4159,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37858@1" ObjectIDZND0="20758@x" ObjectIDZND1="20762@x" ObjectIDZND2="20760@x" Pin0InfoVect0LinkObjId="SW-106522_0" Pin0InfoVect1LinkObjId="SW-106526_0" Pin0InfoVect2LinkObjId="SW-106524_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-729 4159,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c645c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-797 4159,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="20758@x" ObjectIDND1="20762@x" ObjectIDND2="37858@1" ObjectIDZND0="20760@0" Pin0InfoVect0LinkObjId="SW-106524_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106522_0" Pin1InfoVect1LinkObjId="SW-106526_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-797 4159,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c440d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-797 4175,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="20762@x" ObjectIDND1="20760@x" ObjectIDND2="37858@1" ObjectIDZND0="20758@0" Pin0InfoVect0LinkObjId="SW-106522_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106526_0" Pin1InfoVect1LinkObjId="SW-106524_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-797 4175,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c442f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4211,-797 4229,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="20758@1" ObjectIDZND0="20759@x" ObjectIDZND1="g_1c0aa00@0" Pin0InfoVect0LinkObjId="SW-106523_0" Pin0InfoVect1LinkObjId="g_1c0aa00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106522_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4211,-797 4229,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c44690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4229,-810 4229,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="20759@0" ObjectIDZND0="20758@x" ObjectIDZND1="g_1c0aa00@0" Pin0InfoVect0LinkObjId="SW-106522_0" Pin0InfoVect1LinkObjId="g_1c0aa00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106523_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4229,-810 4229,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c448b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4229,-797 4229,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="20759@x" ObjectIDND1="20758@x" ObjectIDZND0="g_1c0aa00@0" Pin0InfoVect0LinkObjId="g_1c0aa00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106523_0" Pin1InfoVect1LinkObjId="SW-106522_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4229,-797 4229,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bee400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-998 4402,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20764@1" ObjectIDZND0="20744@0" Pin0InfoVect0LinkObjId="g_1c9c750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106528_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-998 4402,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c60d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4380,-939 4401,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20767@0" ObjectIDZND0="20755@x" ObjectIDZND1="20764@x" Pin0InfoVect0LinkObjId="SW-106519_0" Pin0InfoVect1LinkObjId="SW-106528_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106531_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4380,-939 4401,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c60fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-939 4401,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20767@x" ObjectIDND1="20755@x" ObjectIDZND0="20764@0" Pin0InfoVect0LinkObjId="SW-106528_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106531_0" Pin1InfoVect1LinkObjId="SW-106519_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-939 4401,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c611d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-913 4401,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20755@1" ObjectIDZND0="20767@x" ObjectIDZND1="20764@x" Pin0InfoVect0LinkObjId="SW-106531_0" Pin0InfoVect1LinkObjId="SW-106528_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106519_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-913 4401,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c63610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-859 4401,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20765@1" ObjectIDZND0="20755@0" Pin0InfoVect0LinkObjId="SW-106519_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106529_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-859 4401,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bf74b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4379,-799 4400,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="20766@0" ObjectIDZND0="20756@x" ObjectIDZND1="20765@x" ObjectIDZND2="46573@x" Pin0InfoVect0LinkObjId="SW-106520_0" Pin0InfoVect1LinkObjId="SW-106529_0" Pin0InfoVect2LinkObjId="EC-CX_HSY.362Load_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4379,-799 4400,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c8e8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-628 4400,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="46573@0" ObjectIDZND0="20756@x" ObjectIDZND1="20766@x" ObjectIDZND2="20765@x" Pin0InfoVect0LinkObjId="SW-106520_0" Pin0InfoVect1LinkObjId="SW-106530_0" Pin0InfoVect2LinkObjId="SW-106529_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_HSY.362Load_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-628 4400,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c8eae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-799 4401,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="20756@x" ObjectIDND1="20766@x" ObjectIDND2="46573@x" ObjectIDZND0="20765@0" Pin0InfoVect0LinkObjId="SW-106529_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106520_0" Pin1InfoVect1LinkObjId="SW-106530_0" Pin1InfoVect2LinkObjId="EC-CX_HSY.362Load_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-799 4401,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c90c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-799 4417,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="20766@x" ObjectIDND1="20765@x" ObjectIDND2="46573@x" ObjectIDZND0="20756@0" Pin0InfoVect0LinkObjId="SW-106520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106530_0" Pin1InfoVect1LinkObjId="SW-106529_0" Pin1InfoVect2LinkObjId="EC-CX_HSY.362Load_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-799 4417,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c90eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-799 4471,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="20756@1" ObjectIDZND0="20757@x" ObjectIDZND1="g_1ca6e40@0" Pin0InfoVect0LinkObjId="SW-106521_0" Pin0InfoVect1LinkObjId="g_1ca6e40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106520_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-799 4471,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c91250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4471,-812 4471,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="20757@0" ObjectIDZND0="20756@x" ObjectIDZND1="g_1ca6e40@0" Pin0InfoVect0LinkObjId="SW-106520_0" Pin0InfoVect1LinkObjId="g_1ca6e40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106521_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4471,-812 4471,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bae3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4471,-799 4471,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="20757@x" ObjectIDND1="20756@x" ObjectIDZND0="g_1ca6e40@0" Pin0InfoVect0LinkObjId="g_1ca6e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106521_0" Pin1InfoVect1LinkObjId="SW-106520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4471,-799 4471,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c13920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-39 3714,-58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1c12fa0@0" Pin0InfoVect0LinkObjId="g_1c12fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-39 3714,-58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c13b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-92 3714,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1c12fa0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c12fa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-92 3714,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c145d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3606,-170 3606,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="20779@0" ObjectIDZND0="20780@x" ObjectIDZND1="g_1c39690@0" Pin0InfoVect0LinkObjId="SW-106587_0" Pin0InfoVect1LinkObjId="g_1c39690_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106586_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3606,-170 3606,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c147f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3606,-157 3606,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="20780@x" ObjectIDND1="20779@x" ObjectIDZND0="g_1c39690@0" Pin0InfoVect0LinkObjId="g_1c39690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106587_0" Pin1InfoVect1LinkObjId="SW-106586_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3606,-157 3606,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c14a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3643,-121 3643,-157 3606,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="20780@0" ObjectIDZND0="g_1c39690@0" ObjectIDZND1="20779@x" Pin0InfoVect0LinkObjId="g_1c39690_0" Pin0InfoVect1LinkObjId="SW-106586_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106587_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3643,-121 3643,-157 3606,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c5ac00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3782,-123 3782,-159 3715,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="20784@0" ObjectIDZND0="0@x" ObjectIDZND1="20783@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-106590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3782,-123 3782,-159 3715,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c5b1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-146 3714,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="20784@x" ObjectIDZND1="20783@x" Pin0InfoVect0LinkObjId="SW-106591_0" Pin0InfoVect1LinkObjId="SW-106590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-146 3714,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c5b430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-155 3714,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="20784@x" ObjectIDND1="0@x" ObjectIDZND0="20783@0" Pin0InfoVect0LinkObjId="SW-106590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106591_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-155 3714,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c5d5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-170 3830,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="20781@0" ObjectIDZND0="20782@x" ObjectIDZND1="g_1c3ad70@0" Pin0InfoVect0LinkObjId="SW-106589_0" Pin0InfoVect1LinkObjId="g_1c3ad70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106588_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3830,-170 3830,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c5d850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-157 3830,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="20782@x" ObjectIDND1="20781@x" ObjectIDZND0="g_1c3ad70@0" Pin0InfoVect0LinkObjId="g_1c3ad70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106589_0" Pin1InfoVect1LinkObjId="SW-106588_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3830,-157 3830,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c5dab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3867,-121 3867,-157 3830,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="20782@0" ObjectIDZND0="g_1c3ad70@0" ObjectIDZND1="20781@x" Pin0InfoVect0LinkObjId="g_1c3ad70_0" Pin0InfoVect1LinkObjId="SW-106588_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106589_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3867,-121 3867,-157 3830,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c38280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3796,-386 3796,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="20803@0" ObjectIDZND0="20804@x" ObjectIDZND1="g_1ba1240@0" ObjectIDZND2="g_1bdca80@0" Pin0InfoVect0LinkObjId="SW-106611_0" Pin0InfoVect1LinkObjId="g_1ba1240_0" Pin0InfoVect2LinkObjId="g_1bdca80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3796,-386 3796,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c384e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3796,-373 3796,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="20804@x" ObjectIDND1="g_1bdca80@0" ObjectIDND2="20803@x" ObjectIDZND0="g_1ba1240@0" Pin0InfoVect0LinkObjId="g_1ba1240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106611_0" Pin1InfoVect1LinkObjId="g_1bdca80_0" Pin1InfoVect2LinkObjId="SW-106610_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3796,-373 3796,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c38740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-355 3861,-373 3796,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1bdca80@0" ObjectIDZND0="20804@x" ObjectIDZND1="g_1ba1240@0" ObjectIDZND2="20803@x" Pin0InfoVect0LinkObjId="SW-106611_0" Pin0InfoVect1LinkObjId="g_1ba1240_0" Pin0InfoVect2LinkObjId="SW-106610_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bdca80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-355 3861,-373 3796,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c39430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3796,-422 3796,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20803@1" ObjectIDZND0="20775@0" Pin0InfoVect0LinkObjId="g_1bce080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3796,-422 3796,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bdc820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-355 3729,-373 3796,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="20804@0" ObjectIDZND0="g_1ba1240@0" ObjectIDZND1="g_1bdca80@0" ObjectIDZND2="20803@x" Pin0InfoVect0LinkObjId="g_1ba1240_0" Pin0InfoVect1LinkObjId="g_1bdca80_0" Pin0InfoVect2LinkObjId="SW-106610_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106611_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-355 3729,-373 3796,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c68ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3593,-342 3593,-376 3534,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20778@0" ObjectIDZND0="20776@x" ObjectIDZND1="20777@x" Pin0InfoVect0LinkObjId="SW-106583_0" Pin0InfoVect1LinkObjId="SW-106584_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106585_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3593,-342 3593,-376 3534,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bdb080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3535,-349 3535,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20776@1" ObjectIDZND0="20778@x" ObjectIDZND1="20777@x" Pin0InfoVect0LinkObjId="SW-106585_0" Pin0InfoVect1LinkObjId="SW-106584_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106583_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3535,-349 3535,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bdb2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3535,-376 3535,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20778@x" ObjectIDND1="20776@x" ObjectIDZND0="20777@0" Pin0InfoVect0LinkObjId="SW-106584_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106585_0" Pin1InfoVect1LinkObjId="SW-106583_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3535,-376 3535,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c94cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3502,-289 3502,-306 3535,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1c67d60@0" ObjectIDZND0="43415@x" ObjectIDZND1="20783@x" ObjectIDZND2="20781@x" Pin0InfoVect0LinkObjId="SM-CX_HSY.P1_0" Pin0InfoVect1LinkObjId="SW-106590_0" Pin0InfoVect2LinkObjId="SW-106588_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c67d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3502,-289 3502,-306 3535,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c94f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3535,-306 3535,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="43415@x" ObjectIDND1="20783@x" ObjectIDND2="20781@x" ObjectIDZND0="20776@0" Pin0InfoVect0LinkObjId="SW-106583_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_HSY.P1_0" Pin1InfoVect1LinkObjId="SW-106590_0" Pin1InfoVect2LinkObjId="SW-106588_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3535,-306 3535,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bf9bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-424 3955,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20786@1" ObjectIDZND0="20775@0" Pin0InfoVect0LinkObjId="g_1bce080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106593_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-424 3955,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bf9e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-343 4013,-377 3954,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20787@0" ObjectIDZND0="20785@x" ObjectIDZND1="20786@x" Pin0InfoVect0LinkObjId="SW-106592_0" Pin0InfoVect1LinkObjId="SW-106593_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106594_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4013,-343 4013,-377 3954,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bd66e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-350 3955,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20785@1" ObjectIDZND0="20787@x" ObjectIDZND1="20786@x" Pin0InfoVect0LinkObjId="SW-106594_0" Pin0InfoVect1LinkObjId="SW-106593_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106592_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-350 3955,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bd6940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-377 3955,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="20787@x" ObjectIDND1="20785@x" ObjectIDZND0="20786@0" Pin0InfoVect0LinkObjId="SW-106593_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106594_0" Pin1InfoVect1LinkObjId="SW-106592_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-377 3955,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bd6ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3922,-290 3922,-307 3955,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="hydroGenerator" EndDevType2="switch" ObjectIDND0="g_1ca0380@0" ObjectIDZND0="20785@x" ObjectIDZND1="43416@x" ObjectIDZND2="20792@x" Pin0InfoVect0LinkObjId="SW-106592_0" Pin0InfoVect1LinkObjId="SM-CX_HSY.P2_0" Pin0InfoVect2LinkObjId="SW-106599_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ca0380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3922,-290 3922,-307 3955,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bd6e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-307 3955,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="hydroGenerator" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_1ca0380@0" ObjectIDND1="43416@x" ObjectIDND2="20792@x" ObjectIDZND0="20785@0" Pin0InfoVect0LinkObjId="SW-106592_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ca0380_0" Pin1InfoVect1LinkObjId="SM-CX_HSY.P2_0" Pin1InfoVect2LinkObjId="SW-106599_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-307 3955,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c3d090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-222 3955,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="g_1ca0380@0" ObjectIDND1="20785@x" ObjectIDND2="20792@x" ObjectIDZND0="43416@0" Pin0InfoVect0LinkObjId="SM-CX_HSY.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1ca0380_0" Pin1InfoVect1LinkObjId="SW-106592_0" Pin1InfoVect2LinkObjId="SW-106599_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-222 3955,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c3e500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-306 3955,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="hydroGenerator" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1ca0380@0" ObjectIDND1="20785@x" ObjectIDZND0="43416@x" ObjectIDZND1="20792@x" ObjectIDZND2="20790@x" Pin0InfoVect0LinkObjId="SM-CX_HSY.P2_0" Pin0InfoVect1LinkObjId="SW-106599_0" Pin0InfoVect2LinkObjId="SW-106597_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1ca0380_0" Pin1InfoVect1LinkObjId="SW-106592_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-306 3955,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c3e760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4026,-207 4026,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="20788@1" ObjectIDZND0="43416@x" ObjectIDZND1="g_1ca0380@0" ObjectIDZND2="20785@x" Pin0InfoVect0LinkObjId="SM-CX_HSY.P2_0" Pin0InfoVect1LinkObjId="g_1ca0380_0" Pin0InfoVect2LinkObjId="SW-106592_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106595_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4026,-207 4026,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c3e9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4026,-222 3955,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="20792@x" ObjectIDND1="20790@x" ObjectIDND2="20788@x" ObjectIDZND0="43416@x" ObjectIDZND1="g_1ca0380@0" ObjectIDZND2="20785@x" Pin0InfoVect0LinkObjId="SM-CX_HSY.P2_0" Pin0InfoVect1LinkObjId="g_1ca0380_0" Pin0InfoVect2LinkObjId="SW-106592_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106599_0" Pin1InfoVect1LinkObjId="SW-106597_0" Pin1InfoVect2LinkObjId="SW-106595_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4026,-222 3955,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bc6c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-40 4134,-59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1bc62c0@0" Pin0InfoVect0LinkObjId="g_1bc62c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-40 4134,-59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bc6e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-93 4134,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1bc62c0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bc62c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-93 4134,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bc70d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4026,-222 4134,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43416@x" ObjectIDND1="g_1ca0380@0" ObjectIDND2="20785@x" ObjectIDZND0="20792@x" ObjectIDZND1="20790@x" Pin0InfoVect0LinkObjId="SW-106599_0" Pin0InfoVect1LinkObjId="SW-106597_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_HSY.P2_0" Pin1InfoVect1LinkObjId="g_1ca0380_0" Pin1InfoVect2LinkObjId="SW-106592_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4026,-222 4134,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bc7330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-222 4134,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="43416@x" ObjectIDND1="g_1ca0380@0" ObjectIDND2="20785@x" ObjectIDZND0="20792@1" Pin0InfoVect0LinkObjId="SW-106599_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_HSY.P2_0" Pin1InfoVect1LinkObjId="g_1ca0380_0" Pin1InfoVect2LinkObjId="SW-106592_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-222 4134,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bc7590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4026,-171 4026,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="20788@0" ObjectIDZND0="g_1c9ec10@0" ObjectIDZND1="20789@x" Pin0InfoVect0LinkObjId="g_1c9ec10_0" Pin0InfoVect1LinkObjId="SW-106596_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106595_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4026,-171 4026,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bc77f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4026,-158 4026,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="20789@x" ObjectIDND1="20788@x" ObjectIDZND0="g_1c9ec10@0" Pin0InfoVect0LinkObjId="g_1c9ec10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106596_0" Pin1InfoVect1LinkObjId="SW-106595_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4026,-158 4026,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bc7a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-122 4063,-158 4026,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="20789@0" ObjectIDZND0="g_1c9ec10@0" ObjectIDZND1="20788@x" Pin0InfoVect0LinkObjId="g_1c9ec10_0" Pin0InfoVect1LinkObjId="SW-106595_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106596_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-122 4063,-158 4026,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c9e750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-147 4134,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="20793@x" ObjectIDZND1="20792@x" Pin0InfoVect0LinkObjId="SW-106600_0" Pin0InfoVect1LinkObjId="SW-106599_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-147 4134,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c9e9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4134,-156 4134,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="20793@x" ObjectIDZND0="20792@0" Pin0InfoVect0LinkObjId="SW-106599_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-106600_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4134,-156 4134,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c40310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4202,-124 4202,-160 4135,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="20793@0" ObjectIDZND0="0@x" ObjectIDZND1="20792@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-106599_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4202,-124 4202,-160 4135,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c49fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4250,-171 4250,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="20790@0" ObjectIDZND0="g_1b97980@0" ObjectIDZND1="20791@x" Pin0InfoVect0LinkObjId="g_1b97980_0" Pin0InfoVect1LinkObjId="SW-106598_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106597_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4250,-171 4250,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c4a220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4250,-158 4250,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="20791@x" ObjectIDND1="20790@x" ObjectIDZND0="g_1b97980@0" Pin0InfoVect0LinkObjId="g_1b97980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106598_0" Pin1InfoVect1LinkObjId="SW-106597_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4250,-158 4250,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c4a480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4287,-122 4287,-158 4250,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="20791@0" ObjectIDZND0="g_1b97980@0" ObjectIDZND1="20790@x" Pin0InfoVect0LinkObjId="g_1b97980_0" Pin0InfoVect1LinkObjId="SW-106597_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106598_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4287,-122 4287,-158 4250,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1b97720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4250,-207 4250,-222 4134,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="20790@1" ObjectIDZND0="43416@x" ObjectIDZND1="g_1ca0380@0" ObjectIDZND2="20785@x" Pin0InfoVect0LinkObjId="SM-CX_HSY.P2_0" Pin0InfoVect1LinkObjId="g_1ca0380_0" Pin0InfoVect2LinkObjId="SW-106592_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106597_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4250,-207 4250,-222 4134,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1cb3bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-422 4395,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20798@1" ObjectIDZND0="20775@0" Pin0InfoVect0LinkObjId="g_1bce080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106605_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-422 4395,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1cb3e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-341 4453,-375 4394,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20799@0" ObjectIDZND0="20797@x" ObjectIDZND1="20798@x" Pin0InfoVect0LinkObjId="SW-106604_0" Pin0InfoVect1LinkObjId="SW-106605_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-341 4453,-375 4394,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1b4d910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-348 4395,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20797@1" ObjectIDZND0="20799@x" ObjectIDZND1="20798@x" Pin0InfoVect0LinkObjId="SW-106606_0" Pin0InfoVect1LinkObjId="SW-106605_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-348 4395,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1b4db70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-375 4395,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20797@x" ObjectIDND1="20799@x" ObjectIDZND0="20798@0" Pin0InfoVect0LinkObjId="SW-106605_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106604_0" Pin1InfoVect1LinkObjId="SW-106606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-375 4395,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1b4f350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-511 3802,-545 3743,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="20801@0" ObjectIDZND0="20775@0" ObjectIDZND1="20800@x" Pin0InfoVect0LinkObjId="g_1bce080_0" Pin0InfoVect1LinkObjId="SW-106607_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106608_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-511 3802,-545 3743,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c352b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-572 3743,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="20800@0" ObjectIDZND0="20801@x" ObjectIDZND1="20775@0" Pin0InfoVect0LinkObjId="SW-106608_0" Pin0InfoVect1LinkObjId="g_1bce080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-572 3743,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c35510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-545 3743,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="20801@x" ObjectIDND1="20800@x" ObjectIDZND0="20775@0" Pin0InfoVect0LinkObjId="g_1bce080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106608_0" Pin1InfoVect1LinkObjId="SW-106607_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-545 3743,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c35770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3606,-221 3714,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43415@x" ObjectIDND1="g_1c67d60@0" ObjectIDND2="20776@x" ObjectIDZND0="20783@x" ObjectIDZND1="20781@x" Pin0InfoVect0LinkObjId="SW-106590_0" Pin0InfoVect1LinkObjId="SW-106588_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_HSY.P1_0" Pin1InfoVect1LinkObjId="g_1c67d60_0" Pin1InfoVect2LinkObjId="SW-106583_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3606,-221 3714,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c36260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3606,-206 3606,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="hydroGenerator" ObjectIDND0="20779@1" ObjectIDZND0="20783@x" ObjectIDZND1="20781@x" ObjectIDZND2="43415@x" Pin0InfoVect0LinkObjId="SW-106590_0" Pin0InfoVect1LinkObjId="SW-106588_0" Pin0InfoVect2LinkObjId="SM-CX_HSY.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106586_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3606,-206 3606,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c364c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3606,-221 3535,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="20783@x" ObjectIDND1="20781@x" ObjectIDND2="20779@x" ObjectIDZND0="43415@x" ObjectIDZND1="g_1c67d60@0" ObjectIDZND2="20776@x" Pin0InfoVect0LinkObjId="SM-CX_HSY.P1_0" Pin0InfoVect1LinkObjId="g_1c67d60_0" Pin0InfoVect2LinkObjId="SW-106583_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-106590_0" Pin1InfoVect1LinkObjId="SW-106588_0" Pin1InfoVect2LinkObjId="SW-106586_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3606,-221 3535,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c36fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3830,-206 3830,-221 3714,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="20781@1" ObjectIDZND0="43415@x" ObjectIDZND1="g_1c67d60@0" ObjectIDZND2="20776@x" Pin0InfoVect0LinkObjId="SM-CX_HSY.P1_0" Pin0InfoVect1LinkObjId="g_1c67d60_0" Pin0InfoVect2LinkObjId="SW-106583_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106588_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3830,-206 3830,-221 3714,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c37210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-221 3714,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="43415@x" ObjectIDND1="g_1c67d60@0" ObjectIDND2="20776@x" ObjectIDZND0="20783@1" Pin0InfoVect0LinkObjId="SW-106590_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_HSY.P1_0" Pin1InfoVect1LinkObjId="g_1c67d60_0" Pin1InfoVect2LinkObjId="SW-106583_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-221 3714,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c0e2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-321 4395,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="20797@0" ObjectIDZND0="g_1b4ddd0@0" Pin0InfoVect0LinkObjId="g_1b4ddd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-321 4395,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c0e520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-305 4362,-305 4362,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="20797@x" ObjectIDZND0="g_1b4ddd0@0" Pin0InfoVect0LinkObjId="g_1b4ddd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-305 4362,-305 4362,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c0f940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-305 4395,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" ObjectIDND0="20797@x" ObjectIDND1="g_1b4ddd0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106604_0" Pin1InfoVect1LinkObjId="g_1b4ddd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-305 4395,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c11230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-193 4395,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1c11850@0" ObjectIDZND1="g_1be00d0@0" Pin0InfoVect0LinkObjId="g_1c11850_0" Pin0InfoVect1LinkObjId="g_1be00d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-193 4395,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1c11450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-158 4395,-91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="transformer2" ObjectIDND0="g_1c11850@0" ObjectIDND1="g_1be00d0@0" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c11850_0" Pin1InfoVect1LinkObjId="g_1be00d0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-158 4395,-91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bdfc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-101 4432,-158 4395,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_1c11850@0" ObjectIDZND0="g_1be00d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1be00d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c11850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-101 4432,-158 4395,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bdfe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-158 4361,-158 4361,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_1c11850@0" ObjectIDND1="0@x" ObjectIDZND0="g_1be00d0@0" Pin0InfoVect0LinkObjId="g_1be00d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c11850_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-158 4361,-158 4361,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c1e6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-423 4546,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20795@1" ObjectIDZND0="20775@0" Pin0InfoVect0LinkObjId="g_1bce080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106602_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-423 4546,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c1e930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-342 4604,-376 4545,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="20796@0" ObjectIDZND0="20794@x" ObjectIDZND1="20795@x" Pin0InfoVect0LinkObjId="SW-106601_0" Pin0InfoVect1LinkObjId="SW-106602_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106603_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-342 4604,-376 4545,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c566d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-349 4546,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20794@1" ObjectIDZND0="20796@x" ObjectIDZND1="20795@x" Pin0InfoVect0LinkObjId="SW-106603_0" Pin0InfoVect1LinkObjId="SW-106602_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106601_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-349 4546,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c56930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-376 4546,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20794@x" ObjectIDND1="20796@x" ObjectIDZND0="20795@0" Pin0InfoVect0LinkObjId="SW-106602_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-106601_0" Pin1InfoVect1LinkObjId="SW-106603_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-376 4546,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1b41e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-120 4546,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="46575@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-120 4546,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b44160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4567,-66 4566,-97 4617,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="46575@0" ObjectIDZND0="20747@1" Pin0InfoVect0LinkObjId="SW-106511_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b41e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4567,-66 4566,-97 4617,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be3350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4644,-97 4694,-97 4694,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="20747@0" ObjectIDZND0="46576@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106511_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4644,-97 4694,-97 4694,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1be7f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-955 4717,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-955 4717,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1be8160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-1016 4717,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="20744@0" Pin0InfoVect0LinkObjId="g_1c9c750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-1016 4717,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1bf0c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4719,-65 4719,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="46576@0" ObjectIDZND0="20746@0" Pin0InfoVect0LinkObjId="SW-106510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1be3350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4719,-65 4719,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d7c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4710,-167 4717,-175 4717,-183 4717,-239 4717,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4710,-167 4717,-175 4717,-183 4717,-239 4717,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25064c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4719,-126 4719,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="load" ObjectIDND0="20746@1" ObjectIDZND0="46571@0" Pin0InfoVect0LinkObjId="EC-CX_HSY.661Load_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4719,-126 4719,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2448b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-289 4513,-306 4537,-312 4547,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="g_1c56b90@0" ObjectIDZND0="20794@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-106601_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c56b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-289 4513,-306 4537,-312 4547,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1b69e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-321 4546,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="20794@0" ObjectIDZND0="g_1c56b90@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1c56b90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-106601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-321 4546,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1d37f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4546,-295 4546,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="breaker" ObjectIDND0="g_1c56b90@0" ObjectIDND1="20794@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1c56b90_0" Pin1InfoVect1LinkObjId="SW-106601_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4546,-295 4546,-147 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_HSY"/>
</svg>